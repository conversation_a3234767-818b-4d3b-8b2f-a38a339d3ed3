<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://dubbo.apache.org/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
	http://dubbo.apache.org/schema/dubbo http://dubbo.apache.org/schema/dubbo/dubbo.xsd" >

    <!--<dubbo:service group="${tsie.security.service-group}" ref="security-redisService" interface="com.tsieframework.cloud.security.serviceapi.system.api.RedisService" timeout="100000" />-->
    <!--<dubbo:service group="${tsie.security.service-group}" ref="security-securityService" interface="com.tsieframework.cloud.security.serviceapi.system.api.SecurityService" timeout="100000" />-->
    <!--<dubbo:service group="${tsie.security.service-group}" ref="security-registerService" interface="com.tsieframework.cloud.security.serviceapi.register.api.RegisterService" timeout="100000" />-->
    <!--<dubbo:service group="${tsie.security.service-group}" ref="security-ukeyService" interface="com.tsieframework.cloud.security.serviceapi.system.api.UKeyService" timeout="100000"/>-->
    <!--<dubbo:service group="${tsie.security.service-group}" ref="security-businessLogService" interface="com.tsieframework.cloud.security.serviceapi.businesslog.api.BusinessLogService" timeout="100000"/>-->

    <!--<dubbo:service group="${tsie.security.service-group}" ref="security-domainHibernateService" interface="com.tsieframework.core.base.service.hibernate.CommonService" timeout="100000"/>-->

</beans>