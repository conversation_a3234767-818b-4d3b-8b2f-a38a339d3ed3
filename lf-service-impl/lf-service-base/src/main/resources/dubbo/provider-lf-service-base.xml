<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns:dubbo="http://dubbo.apache.org/schema/dubbo"
  xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
	http://dubbo.apache.org/schema/dubbo http://dubbo.apache.org/schema/dubbo/dubbo.xsd">


  <!--预测-->
  <dubbo:reference group="forecastable" check="false" id="forecastable"
    interface="com.tsintergy.lf.serviceapi.algorithm.api.CustomizationForecastService" timeout="50000"/>
  <dubbo:reference group="typhoonForecast" check="false" id="typhoonForecast"
    interface="com.tsintergy.lf.serviceapi.algorithm.api.CustomizationForecastService" timeout="50000"/>
  <dubbo:reference group="analysisForecastService" check="false" id="analysisForecastService"
    interface="com.tsintergy.lf.serviceapi.algorithm.api.CustomizationForecastService" timeout="50000"/>
  <dubbo:reference group="shortForecastService" check="false" id="shortForecastService"
    interface="com.tsintergy.lf.serviceapi.algorithm.api.CustomizationForecastService" timeout="50000"/>
  <dubbo:reference group="holidayForecastService" check="false" id="holidayForecastService"
    interface="com.tsintergy.lf.serviceapi.algorithm.api.CustomizationForecastService" timeout="50000"/>
  <dubbo:reference group="preProcessService" check="false" id="preProcessService"
    interface="com.tsintergy.lf.serviceapi.algorithm.api.CustomizationForecastService" timeout="50000"/>
  <dubbo:reference group="sensitivityForecastService" check="false" id="sensitivityForecastService"
    interface="com.tsintergy.lf.serviceapi.algorithm.api.CustomizationForecastService" timeout="50000"/>
  <dubbo:reference group="similarDayForecast" check="false" id="similarDayForecast"
    interface="com.tsintergy.lf.serviceapi.algorithm.api.CustomizationForecastService" timeout="50000"/>
  <dubbo:reference group="forecastDataService" check="false" id="forecastDataService"
    interface="com.tsintergy.lf.serviceapi.base.forecast.api.ForecastDataService" timeout="50000"/>

  <dubbo:service ref="lf-manualForecastService"
    interface="com.tsintergy.lf.serviceapi.base.forecast.api.ManualForecastService" timeout="50000"/>
  <dubbo:service ref="lf-autoForecastService"
    interface="com.tsintergy.lf.serviceapi.base.forecast.api.AutoForecastService" timeout="50000"/>
  <dubbo:service ref="lf-forecastInfoService"
    interface="com.tsintergy.lf.serviceapi.base.forecast.api.ForecastInfoService" timeout="50000"/>
  <dubbo:service ref="lf-forecastLoadService"
    interface="com.tsintergy.lf.serviceapi.base.forecast.api.ForecastLoadService" timeout="50000"/>
  <dubbo:service ref="lf-forecastResultStatService"
    interface="com.tsintergy.lf.serviceapi.base.forecast.api.ForecastResultStatService" timeout="50000"/>
  <dubbo:service ref="lf-forecastService" interface="com.tsintergy.lf.serviceapi.base.forecast.api.ForecastService"
    timeout="50000"/>
  <dubbo:service ref="lf-similarDayService" interface="com.tsintergy.lf.serviceapi.base.forecast.api.SimilarDayService"
    timeout="50000"/>
  <dubbo:service ref="lf-thresholdCityFcService"
    interface="com.tsintergy.lf.serviceapi.base.forecast.api.ThresholdCityFcService" timeout="50000"/>
  <dubbo:service ref="lf-loadDecomposeCityWeekService"
    interface="com.tsintergy.lf.serviceapi.base.load.api.LoadDecomposeCityWeekService" timeout="50000"/>
  <dubbo:service ref="lf-loadDecomposeCityWeekStabilityService"
    interface="com.tsintergy.lf.serviceapi.base.load.api.LoadDecomposeCityWeekStabilityService"
    timeout="50000"/>


  <dubbo:service ref="lf-algorithmCacheService"
    interface="com.tsieframework.core.component.cache.service.DataCacheService" timeout="50000"/>
  <dubbo:service ref="lf-caliberCacheService"
    interface="com.tsieframework.core.component.cache.service.DataCacheService" timeout="50000"/>
  <dubbo:service ref="lf-cityCacheService"
    interface="com.tsieframework.core.component.cache.service.DataCacheService" timeout="50000"/>
  <dubbo:service ref="lf-settingSystemCacheService"
    interface="com.tsieframework.core.component.cache.service.DataCacheService" timeout="50000"/>

  <!--基础信息-->
  <dubbo:service ref="lf-algorithmService" interface="com.tsintergy.lf.serviceapi.base.forecast.api.AlgorithmService"
    timeout="50000"/>
  <dubbo:service ref="lf-caliberService" interface="com.tsintergy.lf.serviceapi.base.base.api.CaliberService"
    timeout="50000"/>
  <dubbo:service ref="lf-cityService" interface="com.tsintergy.lf.serviceapi.base.base.api.CityService" timeout="50000"/>
  <dubbo:service ref="lf-holidayService" interface="com.tsintergy.lf.serviceapi.base.base.api.HolidayService"
    timeout="50000"/>

  <!--节假日-->
  <dubbo:service ref="lf-holidayAnalysisService"
    interface="com.tsintergy.lf.serviceapi.base.holiday.api.HolidayAnalysisService" timeout="50000"/>
  <dubbo:service ref="lf-holidayModifyService"
    interface="com.tsintergy.lf.serviceapi.base.holiday.api.HolidayModifyService" timeout="50000"/>
  <dubbo:service ref="lf-holidayFeatureService"
    interface="com.tsintergy.lf.serviceapi.base.load.api.HolidayFeatureService" timeout="50000"/>


  <!--设置-->
  <dubbo:service ref="lf-userService" interface="com.tsintergy.lf.serviceapi.base.security.api.UserService"
    timeout="50000"/>
  <dubbo:service ref="lf-settingSystemService"
    interface="com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService" timeout="50000"/>
  <dubbo:service ref="lf-settingReportService"
    interface="com.tsintergy.lf.serviceapi.base.check.api.SettingReportService" timeout="50000"/>
  <dubbo:service ref="lf-checkService" interface="com.tsintergy.lf.serviceapi.base.check.api.CheckService"
    timeout="50000"/>
  <dubbo:service ref="lf-settingCheckService" interface="com.tsintergy.lf.serviceapi.base.check.api.SettingCheckService"
    timeout="50000"/>


  <!--后评估-->
  <dubbo:service ref="lf-deviationAnalyzeCityDayFcService"
    interface="com.tsintergy.lf.serviceapi.base.analyze.api.DeviationAnalyzeCityDayFcService"
    timeout="50000"/>
  <dubbo:service ref="lf-precisionWeatherService"
    interface="com.tsintergy.lf.serviceapi.base.datamanage.api.PrecisionWeatherService"
    timeout="50000"/>
  <dubbo:service ref="lf-accuracyLoadCityFcService"
    interface="com.tsintergy.lf.serviceapi.base.evalucation.api.AccuracyLoadCityFcService"
    timeout="50000"/>
  <dubbo:service ref="lf-deviationLoadCityFcService"
    interface="com.tsintergy.lf.serviceapi.base.evalucation.api.DeviationLoadCityFcService"
    timeout="50000"/>
  <dubbo:service ref="lf-dispersionLoadCityFcService"
    interface="com.tsintergy.lf.serviceapi.base.evalucation.api.DispersionLoadCityFcService"
    timeout="50000"/>
  <dubbo:service ref="lf-evalucationService"
    interface="com.tsintergy.lf.serviceapi.base.evalucation.api.EvalucationService" timeout="50000"/>
  <dubbo:service ref="lf-passLoadCityFcService"
    interface="com.tsintergy.lf.serviceapi.base.evalucation.api.PassLoadCityFcService" timeout="50000"/>
  <dubbo:service ref="lf-statisticsCityDayFcService"
    interface="com.tsintergy.lf.serviceapi.base.evalucation.api.StatisticsCityDayFcService"
    timeout="50000"/>


  <!--日志-->
  <dubbo:service ref="lf-tsieOperateLogService"
    interface="com.tsintergy.lf.serviceapi.base.audit.api.TsieOperateLogService" timeout="50000"/>
  <dubbo:service ref="lf-dataCheckInfoService"
    interface="com.tsintergy.lf.serviceapi.base.datamanage.api.DataCheckInfoService" timeout="50000"/>
  <dubbo:service ref="lf-taskInfoService" interface="com.tsintergy.lf.serviceapi.base.load.api.TaskInfoService"
    timeout="50000"/>


  <!--负荷-->
  <dubbo:service ref="lf-loadAccuracyCityMonthFcService"
    interface="com.tsintergy.lf.serviceapi.base.load.api.LoadAccuracyCityMonthFcService"
    timeout="50000"/>
  <dubbo:service ref="lf-loadBatchAccuracyService"
    interface="com.tsintergy.lf.serviceapi.base.load.api.LoadBatchAccuracyService" timeout="50000"/>
  <dubbo:service ref="lf-loadCityFcShortService"
    interface="com.tsintergy.lf.serviceapi.base.load.api.LoadCityFcShortService" timeout="50000"/>
  <dubbo:service ref="lf-loadCityHisClctService"
    interface="com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisClctService" timeout="50000"/>
  <dubbo:service ref="lf-loadCityHisService" interface="com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService"
    timeout="50000"/>
  <dubbo:service ref="lf-loadCompareService" interface="com.tsintergy.lf.serviceapi.base.load.api.LoadCompareService"
    timeout="50000"/>
  <dubbo:service ref="lf-loadCityFcBatchService"
    interface="com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcBatchService" timeout="50000"/>
  <dubbo:service ref="lf-loadCityFcService" interface="com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcService"
    timeout="50000"/>
  <dubbo:service ref="lf-loadCityFcTempService"
    interface="com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcTempService" timeout="50000"/>
  <dubbo:service ref="lf-autoGenerationClctService"
    interface="com.tsintergy.lf.serviceapi.base.datamanage.api.AutoGenerationClctService"
    timeout="50000"/>


  <!--负荷特性-->
  <dubbo:service ref="lf-loadFeatureCityDayFcService"
    interface="com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityDayFcService" timeout="50000"/>
  <dubbo:service ref="lf-loadFeatureCityDayFcShortService"
    interface="com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityDayFcShortService"
    timeout="50000"/>
  <dubbo:service ref="lf-loadFeatureCityDayHisService"
    interface="com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityDayHisService" timeout="50000"/>
  <dubbo:service ref="lf-loadFeatureCityMonthFcService"
    interface="com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityMonthFcService" timeout="50000"/>
  <dubbo:service ref="lf-loadFeatureCityMonthHisService"
    interface="com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityMonthHisService"
    timeout="50000"/>
  <dubbo:service ref="lf-loadFeatureCityQuarterHisService"
    interface="com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityQuarterHisService"
    timeout="50000"/>
  <dubbo:service ref="lf-loadFeatureCityWeekFcService"
    interface="com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityWeekFcService" timeout="50000"/>
  <dubbo:service ref="lf-loadFeatureCityWeekHisService"
    interface="com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityWeekHisService" timeout="50000"/>
  <dubbo:service ref="lf-loadFeatureCityYearHisService"
    interface="com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityYearHisService" timeout="50000"/>
  <dubbo:service ref="lf-loadFeatureStatService"
    interface="com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureStatService" timeout="50000"/>

  <dubbo:service ref="lf-otherLoadService" interface="com.tsintergy.lf.serviceapi.base.otherload.api.OtherLoadService"
    timeout="50000"/>
  <dubbo:service ref="lf-rollingLoadService" interface="com.tsintergy.lf.serviceapi.base.load.api.RollingLoadService"
    timeout="50000"/>


  <!--填报-->
  <dubbo:service ref="lf-reportLoadHisMonthEnergyService"
    interface="com.tsintergy.lf.serviceapi.base.load.api.ReportLoadHisMonthEnergyService"
    timeout="50000"/>
  <dubbo:service ref="lf-reportAccuracyDayService"
    interface="com.tsintergy.lf.serviceapi.base.report.api.ReportAccuracyDayService" timeout="50000"/>
  <dubbo:service ref="lf-reportAccuracyMonthService"
    interface="com.tsintergy.lf.serviceapi.base.report.api.ReportAccuracyMonthService" timeout="50000"/>
  <dubbo:service ref="lf-reportAccuracySynthesizeMonthService"
    interface="com.tsintergy.lf.serviceapi.base.report.api.ReportAccuracySynthesizeMonthService" timeout="50000"/>
  <dubbo:service ref="lf-reportAccuracyWeekService"
    interface="com.tsintergy.lf.serviceapi.base.report.api.ReportAccuracyWeekService" timeout="50000"/>
  <dubbo:service ref="lf-reportInfoLogService"
    interface="com.tsintergy.lf.serviceapi.base.report.api.ReportInfoLogService" timeout="50000"/>
  <dubbo:service ref="lf-reportLoadDayService"
    interface="com.tsintergy.lf.serviceapi.base.report.api.ReportLoadDayService" timeout="50000"/>
  <dubbo:service ref="lf-reportLoadHolidayService"
    interface="com.tsintergy.lf.serviceapi.base.report.api.ReportLoadHolidayService" timeout="50000"/>
  <dubbo:service ref="lf-reportLoadMonthService"
    interface="com.tsintergy.lf.serviceapi.base.report.api.ReportLoadMonthService" timeout="50000"/>
  <dubbo:service ref="lf-reportLoadPeakService"
    interface="com.tsintergy.lf.serviceapi.base.report.api.ReportLoadPeakService" timeout="50000"/>
  <dubbo:service ref="lf-reportLoadQuarterService"
    interface="com.tsintergy.lf.serviceapi.base.report.api.ReportLoadQuarterService" timeout="50000"/>
  <dubbo:service ref="lf-reportLoadWeekService"
    interface="com.tsintergy.lf.serviceapi.base.report.api.ReportLoadWeekService" timeout="50000"/>
  <dubbo:service ref="lf-reportLoadYearService"
    interface="com.tsintergy.lf.serviceapi.base.report.api.ReportLoadYearService" timeout="50000"/>


  <!--统计-->
  <dubbo:service ref="lf-statisticsAccuracyWeatherCityDayHisService"
    interface="com.tsintergy.lf.serviceapi.base.weather.api.StatisticsAccuracyWeatherCityDayHisService"
    timeout="50000"/>
  <dubbo:service ref="lf-statisticsAccuracyWeatherCityMonthHisService"
    interface="com.tsintergy.lf.serviceapi.base.weather.api.StatisticsAccuracyWeatherCityMonthHisService"
    timeout="50000"/>
  <dubbo:service ref="lf-statisticsAccuracyWeatherCityYearHisService"
    interface="com.tsintergy.lf.serviceapi.base.weather.api.StatisticsAccuracyWeatherCityYearHisService"
    timeout="50000"/>
  <dubbo:service ref="lf-statisticsSynthesizeWeatherCityDayFcService"
    interface="com.tsintergy.lf.serviceapi.base.weather.api.StatisticsSynthesizeWeatherCityDayFcService"
    timeout="50000"/>
  <dubbo:service ref="lf-statisticsSynthesizeWeatherCityDayHisService"
    interface="com.tsintergy.lf.serviceapi.base.weather.api.StatisticsSynthesizeWeatherCityDayHisService"
    timeout="50000"/>
  <dubbo:service ref="lf-statisticsWeatherCityDayFcStatService"
    interface="com.tsintergy.lf.serviceapi.base.evalucation.api.StatisticsWeatherCityDayFcStatService"
    timeout="50000"/>


  <!--气象-->
  <dubbo:service ref="lf-weatherTyphoonDefinitionService"
    interface="com.tsintergy.lf.serviceapi.base.base.api.WeatherTyphoonDefinitionService"
    timeout="50000"/>
  <dubbo:service ref="lf-weatherCityFcModifyService"
    interface="com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcModifyService" timeout="50000"/>
  <dubbo:service ref="lf-weatherCityFcService"
    interface="com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcService" timeout="50000"/>
  <dubbo:service ref="lf-weatherCityHisService"
    interface="com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityHisService" timeout="50000"/>
   <dubbo:service ref="lf-weatherSourceFcService"
    interface="com.tsintergy.lf.serviceapi.base.weather.api.WeatherSourceFcService" timeout="50000"/>
  <dubbo:service ref="lf-weatherFeatureCityDayFcService"
    interface="com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityDayFcService"
    timeout="50000"/>
  <dubbo:service ref="lf-weatherFeatureCityDayHisService"
    interface="com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityDayHisService"
    timeout="50000"/>
  <dubbo:service ref="lf-weatherFeatureCityMonthHisService"
    interface="com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityMonthHisService"
    timeout="50000"/>
  <dubbo:service ref="lf-weatherFeatureCityQuarterHisService"
    interface="com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityQuarterHisService"
    timeout="50000"/>
  <dubbo:service ref="lf-weatherFeatureStatService"
    interface="com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureStatService" timeout="50000"/>
  <dubbo:service ref="lf-weatherPredictionAccuracyService"
    interface="com.tsintergy.lf.serviceapi.base.weather.api.WeatherPredictionAccuracyService"
    timeout="50000"/>
  <dubbo:service ref="lf-weatherImpactAnalysisService"
    interface="com.tsintergy.lf.serviceapi.base.weather.api.WeatherImpactAnalysisService"
    timeout="50000"/>
  <dubbo:service ref="lf-weatherTyphoonRateService"
    interface="com.tsintergy.lf.serviceapi.base.weather.api.WeatherTyphoonRateService" timeout="50000"/>
  <dubbo:service ref="lf-extremeWeatherService"
    interface="com.tsintergy.lf.serviceapi.base.typhoon.api.ExtremeWeatherService" timeout="50000"/>
  <dubbo:service ref="lf-typhoonAnalysisConfigService"
    interface="com.tsintergy.lf.serviceapi.base.typhoon.api.TyphoonAnalysisConfigService"
    timeout="50000"/>
  <dubbo:service ref="lf-typhoonArchiveService"
    interface="com.tsintergy.lf.serviceapi.base.typhoon.api.TyphoonArchiveService" timeout="50000"/>
  <dubbo:service ref="lf-typhoonFcService" interface="com.tsintergy.lf.serviceapi.base.typhoon.api.TyphoonFcService"
    timeout="50000"/>
  <dubbo:service ref="lf-typhoonHisService" interface="com.tsintergy.lf.serviceapi.base.typhoon.api.TyphoonHisService"
    timeout="50000"/>
  <dubbo:service ref="lf-typhoonStatService" interface="com.tsintergy.lf.serviceapi.base.typhoon.api.TyphoonStatService"
    timeout="50000"/>


  <dubbo:service ref="lf-reportAccuracySynthesizeCumulativeService" interface="com.tsintergy.lf.serviceapi.base.report.api.ReportAccuracySynthesizeCumulativeService"
    timeout="50000"/>
  <dubbo:service ref="lf-reportAllAccuracyService" interface="com.tsintergy.lf.serviceapi.base.report.api.ReportAllAccuracyService"
    timeout="50000"/>
  <dubbo:service ref="lf-statisticsAccuracyLoadCityYearHisService" interface="com.tsintergy.lf.serviceapi.base.evalucation.api.StatisticsAccuracyLoadCityYearHisService"
    timeout="50000"/>

  <dubbo:service ref="lf-statisticsAccuracyLoadCityMonthHisService" interface="com.tsintergy.lf.serviceapi.base.evalucation.api.StatisticsAccuracyLoadCityMonthHisService"
    timeout="50000"/>

  <dubbo:service ref="lf-weatherCityFcLoadForecastService" interface="com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcLoadForecastService"
    timeout="50000"/>
  <dubbo:service ref="lf-loadResultService" interface="com.tsintergy.lf.serviceapi.base.load.api.LoadResultService"
    timeout="50000"/>
  <dubbo:service ref="lf-peakAndTroughAssessmentService" interface="com.tsintergy.lf.serviceapi.base.load.api.PeakAndTroughAssessmentService"
    timeout="50000"/>


</beans>
