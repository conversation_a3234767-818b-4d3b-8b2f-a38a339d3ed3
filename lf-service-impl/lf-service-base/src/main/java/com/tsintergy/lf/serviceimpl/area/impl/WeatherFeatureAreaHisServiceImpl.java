/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.area.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsieframework.core.base.service.BaseFacadeServiceImpl;
import com.tsieframework.util.compatible.BeanUtils;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.area.api.WeatherAreaHisService;
import com.tsintergy.lf.serviceapi.base.area.api.WeatherFeatureAreaHisService;
import com.tsintergy.lf.serviceapi.base.area.pojo.WeatherAreaHisDO;
import com.tsintergy.lf.serviceapi.base.area.pojo.WeatherFeatureAreaDayFcDO;
import com.tsintergy.lf.serviceapi.base.area.pojo.WeatherFeatureAreaDayHisDO;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherNewEnum;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureStatService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.BaseWeatherFeatureDayFcDO;
import com.tsintergy.lf.serviceimpl.area.dao.WeatherFeatureAreaDayHisDAO;
import java.sql.Timestamp;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/8/9 11:57
 * @Version: 1.0.0
 */
@Service("weatherFeatureAreaHisService")
public class WeatherFeatureAreaHisServiceImpl extends BaseFacadeServiceImpl implements WeatherFeatureAreaHisService {

    @Autowired
    private WeatherFeatureAreaDayHisDAO weatherFeatureAreaDayHisDAO;

    @Autowired
    private WeatherAreaHisService weatherAreaHisService;

    @Autowired
    private WeatherFeatureStatService weatherFeatureStatService;
    @Override
    public void doSaveOrUpdate(WeatherFeatureAreaDayHisDO weatherFeatureAreaDayHisDO) {
        List<WeatherFeatureAreaDayHisDO> areaWeatherFeature = this
            .findAreaWeatherFeature(weatherFeatureAreaDayHisDO.getAreaId(), weatherFeatureAreaDayHisDO.getDate(),
                weatherFeatureAreaDayHisDO.getDate());
        if (CollectionUtils.isEmpty(areaWeatherFeature)){
            weatherFeatureAreaDayHisDO.setCreatetime(new Timestamp(System.currentTimeMillis()));
            weatherFeatureAreaDayHisDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            weatherFeatureAreaDayHisDAO.save(weatherFeatureAreaDayHisDO);
        }else{
            WeatherFeatureAreaDayHisDO oldDo = areaWeatherFeature.get(0);
            weatherFeatureAreaDayHisDO.setId(oldDo.getId());
            weatherFeatureAreaDayHisDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            weatherFeatureAreaDayHisDAO.saveOrUpdateByTemplate(weatherFeatureAreaDayHisDO);
        }
    }

    @Override
    public List<WeatherFeatureAreaDayHisDO> findAreaWeatherFeature(String areaId, Date startDate, Date endDate) {
        List<WeatherFeatureAreaDayHisDO> list = weatherFeatureAreaDayHisDAO
            .findAll(JpaWrappers.<WeatherFeatureAreaDayHisDO>lambdaQuery()
                .ge(startDate != null, WeatherFeatureAreaDayHisDO::getDate, startDate)
                .le(endDate != null, WeatherFeatureAreaDayHisDO::getDate, endDate)
                .eq(areaId != null, WeatherFeatureAreaDayHisDO::getAreaId, areaId)
            );
        return list;
    }

    @Override
    public void statAreaWeatherHisFeature(Date startDate, Date endDate, String areaId) throws Exception {
        List<Date> dateList = DateUtil.getListBetweenDay(startDate, endDate);
        Map<String, WeatherAreaHisDO> collect = weatherAreaHisService
            .findWeatherCityHisDOs(null, null, startDate, endDate)
            .stream().collect(Collectors.toMap(t -> {
                return t.getAreaId() + t.getType() + DateUtil.getDateToStr(t.getDate());
            }, t -> t));

        Map<String,WeatherFeatureAreaDayHisDO> resultMap = new HashMap<>();
        for (Date date : dateList) {
            for (WeatherNewEnum value : WeatherNewEnum.values()) {
                String weatherKey = areaId + value.getType() + DateUtil.getDateToStr(date);
                String featureKey = areaId + DateUtil.getDateToStr(date);
                WeatherAreaHisDO weatherAreaHisDO = collect
                    .get(weatherKey);
                if (weatherAreaHisDO != null) {

                    WeatherFeatureAreaDayHisDO mapValue = resultMap
                        .get(featureKey);
                    if (mapValue==null){
                        mapValue = new WeatherFeatureAreaDayHisDO();
                        mapValue.setAreaId(areaId);
                    }

                    BaseWeatherFeatureDayFcDO baseWeatherFeatureDayFcDO = weatherFeatureStatService
                        .doStatWeatherFeature(weatherAreaHisDO, date, value.value());
                    BeanUtils.copyPropertiesNotNull(mapValue,baseWeatherFeatureDayFcDO);
                    resultMap.put(featureKey,mapValue);
                }
            }
        }


        updateAreaWeatherFeature(startDate,endDate,areaId,resultMap.values().stream().collect(Collectors.toList()));
    }

    private void updateAreaWeatherFeature(Date startDate, Date endDate, String areaId,List<WeatherFeatureAreaDayHisDO> weatherFeatureAreaDayHisDOS) {
        if (CollectionUtils.isEmpty(weatherFeatureAreaDayHisDOS)){
            return;
        }
        weatherFeatureAreaDayHisDAO.delete(JpaWrappers.<WeatherFeatureAreaDayHisDO>lambdaQuery()
            .eq(areaId!=null,WeatherFeatureAreaDayHisDO::getAreaId,areaId)
            .le(endDate!=null,WeatherFeatureAreaDayHisDO::getDate,endDate)
            .ge(startDate!=null,WeatherFeatureAreaDayHisDO::getDate,startDate));
        weatherFeatureAreaDayHisDAO.saveOrUpdateBatch(weatherFeatureAreaDayHisDOS);
    }
}