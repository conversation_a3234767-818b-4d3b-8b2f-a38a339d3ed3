/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.weather.dao;

import com.tsieframework.core.base.dao.hibernate.BaseAbstractDAO;
import com.tsieframework.core.base.dao.hibernate.BaseDAO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherStationFcBasicDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherStationHisBasicDO;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Component;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/5/20 15:41
 * @Version: 1.0.0
 */
@Component
public class WeatherStationFcBasicDAO extends BaseAbstractDAO<WeatherStationFcBasicDO> {


    public List<WeatherStationFcBasicDO> getWeatherStationHisBasicDO(String cityId,String stationId, Date startDate,Date endDate,Integer type){
        DBQueryParam param = DBQueryParamBuilder.create().build();
        DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (null != startDate) {
            param.getQueryConditions().put("_dnl_date", new java.sql.Date(startDate.getTime()));
        }
        if (null != endDate) {
            param.getQueryConditions().put("_dnm_date", new java.sql.Date(endDate.getTime()));
        }
        if (null != type) {
            param.getQueryConditions().put("_ne_type", type);
        }
        if (null != cityId) {
            param.getQueryConditions().put("_ne_cityId", cityId);
        }
        if (null != stationId) {
            param.getQueryConditions().put("_ne_stationId", stationId);
        }
        param.setOrderby("date");
        param.setDesc("0");
        List<WeatherStationFcBasicDO> datas = this.query(param).getDatas();
        return datas;
    }
}