package com.tsintergy.lf.serviceimpl.bgd.impl;

import com.alibaba.nacos.common.utils.MapUtils;
import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsintergy.aif.algorithm.serviceapi.base.constants.Constants;
import com.tsintergy.lf.serviceapi.base.bgd.api.IndustryBaseInitService;
import com.tsintergy.lf.serviceapi.base.bgd.api.IndustryFeatureService;
import com.tsintergy.lf.serviceapi.base.bgd.api.LoadFeatureEveryIndustryDayHisService;
import com.tsintergy.lf.serviceapi.base.bgd.api.LoadFeatureIndustryDayHisService;
import com.tsintergy.lf.serviceapi.base.bgd.dto.*;
import com.tsintergy.lf.serviceapi.base.bgd.enums.IndustrialImportFirstTypeEnum;
import com.tsintergy.lf.serviceapi.base.bgd.enums.IndustrialImportTypeEnum;
import com.tsintergy.lf.serviceapi.base.bgd.enums.IndustrialTypeEnum;
import com.tsintergy.lf.serviceapi.base.bgd.pojo.IndustryBaseInitDO;
import com.tsintergy.lf.serviceapi.base.bgd.pojo.LoadFeatureEveryIndustryDayHisServiceDO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.Map.Entry;
import java.util.stream.Collectors;

/**
 * Description: TODO <br>
 *
 * <AUTHOR>
 * @create 2023-02-16
 * @since 1.0.0
 */
@Service
public class IndustryLoadFeatureServiceImpl extends BaseServiceImpl implements IndustryFeatureService {

    @Autowired
    LoadFeatureIndustryDayHisService loadFeatureIndustryDayHisService;

    @Autowired
    LoadFeatureEveryIndustryDayHisService loadFeatureEveryIndustryDayHisService;

    @Autowired
    IndustryBaseInitService industryBaseInitService;

    @Override
    public List<IndustryEnergyValueDTO> queryTotalEnergy(String cityId, Date startDate, Date endDate) {
        List<IndustryEnergyValueDTO> industryElecHisDTOList =  new ArrayList<>();
        List<IndustryLoadHisDTO> industryElecHisDTOLists = new ArrayList<>();

        for (IndustrialTypeEnum value : IndustrialTypeEnum.values()) {
            if (value.getType().equals("0")) {
                continue;
            }
            IndustryLoadHisDTO industryLoadHisDTO = new IndustryLoadHisDTO();
            List<String> idList = new ArrayList<>();
            List<IndustryBaseInitDO> industryBaseList = industryBaseInitService.getIndustryBaseInitServiceListByName(
                value.getIndustrialName());
            if (!CollectionUtils.isEmpty(industryBaseList)) {
                for (IndustryBaseInitDO industryBaseInitDO : industryBaseList) {
                    // 查出产业信息
                    List<IndustryBaseInitDO> industryBaseInitList = industryBaseInitService.getIndustryBaseInitServiceListById(
                        industryBaseInitDO.getId());
                    if (!CollectionUtils.isEmpty(industryBaseInitList)) {
                        for (IndustryBaseInitDO baseInitDO : industryBaseInitList) {
                            idList.add(baseInitDO.getId());
                        }
                    }
                }
            }
            if (CollectionUtils.isEmpty(idList)) {
                continue;
            }
            // 查出所有产业行业的负荷
            Map<Date, List<LoadFeatureEveryIndustryDayHisServiceDO>> collect = loadFeatureEveryIndustryDayHisService.getLoadFeatureEveryIndustryDayHisServiceLists(
                cityId, startDate, endDate, idList).stream().filter(t -> t.getEnergy() != null).collect(
                Collectors.groupingBy(t -> t.getDate()));
            List<IndustryValueDTO> loadList = new ArrayList<>();
            for (Date date : collect.keySet()) {
                IndustryValueDTO industryValueDTO = new IndustryValueDTO();
                BigDecimal reduceEnergy = collect.get(date).stream().filter(t -> t.getEnergy() != null)
                    .map(LoadFeatureEveryIndustryDayHisServiceDO::getEnergy)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                industryValueDTO.setValue(reduceEnergy);
                industryValueDTO.setDate(date);
                loadList.add(industryValueDTO);
            }
            industryLoadHisDTO.setName(value.getIndustrialName());
            industryLoadHisDTO.setLoadList(loadList);
            industryElecHisDTOLists.add(industryLoadHisDTO);
        }
        for (IndustryLoadHisDTO elecHisDTOList : industryElecHisDTOLists) {
            IndustryEnergyValueDTO industryEnergyValueDTO = new IndustryEnergyValueDTO();
            industryEnergyValueDTO.setName(elecHisDTOList.getName());
            BigDecimal reduce = elecHisDTOList.getLoadList().stream().filter(t -> t.getValue() != null)
                .map(IndustryValueDTO::getValue)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            industryEnergyValueDTO.setValue(reduce);
            industryElecHisDTOList.add(industryEnergyValueDTO);
        }
        return industryElecHisDTOList;
    }

    @Override
    public List<IndustryLoadHisDTO> queryLoadIndustry(String cityId, Date startDate, Date endDate) {
        List<IndustryLoadHisDTO> industryElecHisDTOList = new ArrayList<>();
        List<IndustryLoadHisDTO> industryElecHisDTOLists = new ArrayList<>();
        for (IndustrialTypeEnum value : IndustrialTypeEnum.values()) {
            if (value.getType().equals("0")) {
                continue;
            }
            IndustryLoadHisDTO industryLoadHisDTO = new IndustryLoadHisDTO();
            List<String> idList = new ArrayList<>();
            List<IndustryBaseInitDO> industryBaseList = industryBaseInitService.getIndustryBaseInitServiceListByName(
                value.getIndustrialName());
            if (!CollectionUtils.isEmpty(industryBaseList)) {
                for (IndustryBaseInitDO industryBaseInitDO : industryBaseList) {
                    // 查出产业信息
                    List<IndustryBaseInitDO> industryBaseInitList = industryBaseInitService.getIndustryBaseInitServiceListById(
                        industryBaseInitDO.getId());
                    if (!CollectionUtils.isEmpty(industryBaseInitList)) {
                        for (IndustryBaseInitDO baseInitDO : industryBaseInitList) {
                            idList.add(baseInitDO.getId());
                        }
                    }
                }
            }
            if (CollectionUtils.isEmpty(idList)) {
                continue;
            }
            // 查出所有产业行业的负荷
            Map<Date, List<LoadFeatureEveryIndustryDayHisServiceDO>> collect = loadFeatureEveryIndustryDayHisService.getLoadFeatureEveryIndustryDayHisServiceLists(
                cityId, startDate, endDate, idList).stream().filter(t -> t.getEnergy() != null).collect(
                Collectors.groupingBy(t -> t.getDate()));

            List<IndustryValueDTO> loadList = new ArrayList<>();
            for (Date date : collect.keySet()) {
                IndustryValueDTO industryValueDTO = new IndustryValueDTO();
                BigDecimal reduceLoad = collect.get(date).stream().filter(t -> t.getMaxLoad() != null)
                    .map(LoadFeatureEveryIndustryDayHisServiceDO::getMaxLoad)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                industryValueDTO.setValue(reduceLoad);
                industryValueDTO.setDate(date);

                loadList.add(industryValueDTO);
            }
            industryLoadHisDTO.setName(value.getIndustrialName());
            industryLoadHisDTO.setLoadList(loadList);
            industryElecHisDTOList.add(industryLoadHisDTO);
        }
        List<IndustryValueDTO> collect = new ArrayList<>();
        for (IndustryLoadHisDTO industryLoadHisDTO : industryElecHisDTOList) {
            IndustryLoadHisDTO industryLoadHisDTO1 = new IndustryLoadHisDTO();
            industryLoadHisDTO1.setName(industryLoadHisDTO.getName());
            collect = industryLoadHisDTO.getLoadList().stream()
                .sorted(Comparator.comparing(IndustryValueDTO::getDate)).collect(Collectors.toList());
            industryLoadHisDTO1.setLoadList(collect);
            industryElecHisDTOLists.add(industryLoadHisDTO1);
        }
        return industryElecHisDTOLists;
    }

    @Override
    public List<IndustryLoadHisDTO> queryEnergyIndustry(String cityId, Date startDate, Date endDate) {
        List<IndustryLoadHisDTO> industryElecHisDTOList = new ArrayList<>();
        List<IndustryLoadHisDTO> industryElecHisDTOLists = new ArrayList<>();
        for (IndustrialTypeEnum value : IndustrialTypeEnum.values()) {
            if (value.getType().equals("0")) {
                continue;
            }
            IndustryLoadHisDTO industryLoadHisDTO = new IndustryLoadHisDTO();
            List<String> idList = new ArrayList<>();
            List<IndustryBaseInitDO> industryBaseList = industryBaseInitService.getIndustryBaseInitServiceListByName(
                value.getIndustrialName());
            if (!CollectionUtils.isEmpty(industryBaseList)) {
                for (IndustryBaseInitDO industryBaseInitDO : industryBaseList) {
                    // 查出产业信息
                    List<IndustryBaseInitDO> industryBaseInitList = industryBaseInitService.getIndustryBaseInitServiceListById(
                        industryBaseInitDO.getId());
                    if (!CollectionUtils.isEmpty(industryBaseInitList)) {
                        for (IndustryBaseInitDO baseInitDO : industryBaseInitList) {
                            idList.add(baseInitDO.getId());
                        }
                    }
                }
            }
            if (CollectionUtils.isEmpty(idList)) {
                continue;
            }
            // 查出所有产业行业的负荷
            Map<Date, List<LoadFeatureEveryIndustryDayHisServiceDO>> collect = loadFeatureEveryIndustryDayHisService.getLoadFeatureEveryIndustryDayHisServiceLists(
                cityId, startDate, endDate, idList).stream().filter(t -> t.getEnergy() != null).collect(
                Collectors.groupingBy(t -> t.getDate()));
            List<IndustryValueDTO> loadList = new ArrayList<>();
            for (Date date : collect.keySet()) {
                IndustryValueDTO industryValueDTO = new IndustryValueDTO();
                BigDecimal reduceEnergy = collect.get(date).stream().filter(t -> t.getEnergy() != null)
                    .map(LoadFeatureEveryIndustryDayHisServiceDO::getEnergy)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                industryValueDTO.setValue(reduceEnergy.divide(new BigDecimal(10)));
                industryValueDTO.setDate(date);
                loadList.add(industryValueDTO);
            }
            industryLoadHisDTO.setName(value.getIndustrialName());
            industryLoadHisDTO.setLoadList(loadList);
            industryElecHisDTOList.add(industryLoadHisDTO);
        }
        List<IndustryValueDTO> collect = new ArrayList<>();
        for (IndustryLoadHisDTO industryLoadHisDTO : industryElecHisDTOList) {
            IndustryLoadHisDTO industryLoadHisDTO1 = new IndustryLoadHisDTO();
            industryLoadHisDTO1.setName(industryLoadHisDTO.getName());
            collect = industryLoadHisDTO.getLoadList().stream()
                .sorted(Comparator.comparing(IndustryValueDTO::getDate)).collect(Collectors.toList());
            industryLoadHisDTO1.setLoadList(collect);
            industryElecHisDTOLists.add(industryLoadHisDTO1);
        }
        return industryElecHisDTOLists;
    }

    @Override
    public List<IndustryAllTypeLoadDTO> queryAllLoadIndustry(String cityId, Date startDate, Date endDate) {
        List<IndustryAllTypeLoadDTO> industryAllTypeLoadDTOList = new ArrayList<>();
        // 先查出所有产业行业的负荷
        Map<String, List<LoadFeatureEveryIndustryDayHisServiceDO>> collect = loadFeatureEveryIndustryDayHisService.getLoadFeatureEveryIndustryDayHisServiceList(
            cityId, startDate, endDate).stream().collect(
            Collectors.groupingBy(t -> t.getEveryIndustryId() + Constants.SEPARATOR_PUNCTUATION + t.getCityId()));

        List<IndustryBaseInitDO> industryBaseInitServiceList = industryBaseInitService.getIndustryBaseInitServiceListByTypes();

        if(!CollectionUtils.isEmpty(industryBaseInitServiceList)) {
            for (IndustryBaseInitDO industryBaseInitDO : industryBaseInitServiceList) {
                IndustryAllTypeLoadDTO industryAllTypeLoadDTO = new IndustryAllTypeLoadDTO();
                industryAllTypeLoadDTO.setId(industryBaseInitDO.getId());
                industryAllTypeLoadDTO.setName(industryBaseInitDO.getName());
                industryAllTypeLoadDTO.setType(industryBaseInitDO.getType());
                industryAllTypeLoadDTO.setLevel(industryBaseInitDO.getLevel());
                industryAllTypeLoadDTO.setParentId(industryBaseInitDO.getParentId());

                for (String s : collect.keySet()) {
                    String idType = industryBaseInitDO.getId() +
                        Constants.SEPARATOR_PUNCTUATION + cityId;
                    if (s.equals(idType)) {
                        IndustryAllValueDTO industryAllValueDTO = new IndustryAllValueDTO();
                        // 早峰
                        Optional<LoadFeatureEveryIndustryDayHisServiceDO> maxMorningPeak = collect.get(s).stream()
                            .filter(t -> t.getMorningPeak() != null)
                            .max(Comparator.comparing(LoadFeatureEveryIndustryDayHisServiceDO::getMorningPeak));
                        if (maxMorningPeak.isPresent()) {
                            LoadFeatureEveryIndustryDayHisServiceDO maxMorningPeakLoad = maxMorningPeak.get();
                            industryAllValueDTO.setMorningLoad(maxMorningPeakLoad.getMorningPeak());
                        }
                        // 晚峰
                        Optional<LoadFeatureEveryIndustryDayHisServiceDO> maxEveningPeak = collect.get(s).stream()
                            .filter(t -> t.getEveningPeak() != null)
                            .max(Comparator.comparing(LoadFeatureEveryIndustryDayHisServiceDO::getEveningPeak));
                        if (maxEveningPeak.isPresent()) {
                            LoadFeatureEveryIndustryDayHisServiceDO maxEveningPeakLoad = maxEveningPeak.get();
                            industryAllValueDTO.setEveningLoad(maxEveningPeakLoad.getEveningPeak());
                        }
                        // 平段
                        Optional<LoadFeatureEveryIndustryDayHisServiceDO> maxNoontimePeak = collect.get(s).stream()
                            .filter(t -> t.getNoontimePeak() != null)
                            .max(Comparator.comparing(LoadFeatureEveryIndustryDayHisServiceDO::getNoontimePeak));
                        if (maxNoontimePeak.isPresent()) {
                            LoadFeatureEveryIndustryDayHisServiceDO maxNoontimePeakLoad = maxNoontimePeak.get();
                            industryAllValueDTO.setNoonTimeLoad(maxNoontimePeakLoad.getNoontimePeak());
                        }
                        // 低谷
                        Optional<LoadFeatureEveryIndustryDayHisServiceDO> maxTrough = collect.get(s).stream()
                            .filter(t -> t.getTroughMax() != null)
                            .max(Comparator.comparing(LoadFeatureEveryIndustryDayHisServiceDO::getTroughMax));
                        if (maxTrough.isPresent()) {
                            LoadFeatureEveryIndustryDayHisServiceDO maxTroughLoad = maxTrough.get();
                            industryAllValueDTO.setThoughLoad(maxTroughLoad.getTroughMax());
                        }
                        industryAllTypeLoadDTO.setIndustryAllValueDTO(industryAllValueDTO);
                    }
                }
                industryAllTypeLoadDTOList.add(industryAllTypeLoadDTO);
            }
        }
        // TODO 递归拿到所有产业和行业的信息
        if (!CollectionUtils.isEmpty(industryAllTypeLoadDTOList)) {
            industryAllTypeLoadDTOList.stream().filter(t -> t.getParentId() != "0").map(menu -> {
                List<IndustryAllTypeLoadDTO> children = getChildren(menu, industryAllTypeLoadDTOList);
                menu.setChild(children);
                return menu;
            }).collect(Collectors.toList());
        }
        // TODO 取前四个对应的产业
        List<IndustryAllTypeLoadDTO> result = new ArrayList<>();
        for (IndustryAllTypeLoadDTO results : industryAllTypeLoadDTOList) {
            for (IndustrialTypeEnum industrialTypeEnum : IndustrialTypeEnum.values()){
                if (industrialTypeEnum.getIndustrialName().equals(results.getName())) {
                    result.add(results);
                }
            }
        }
        return result;
    }

    @Override
    public List<IndustryAllTypeLoadDTO> queryAllEnergyIndustry(String cityId, Date startDate, Date endDate) {
        List<IndustryAllTypeLoadDTO> industryAllTypeLoadDTOList = new ArrayList<>();
        // 先查出所有产业行业的负荷
        Map<String, List<LoadFeatureEveryIndustryDayHisServiceDO>> collect = loadFeatureEveryIndustryDayHisService.getLoadFeatureEveryIndustryDayHisServiceList(
            cityId, startDate, endDate).stream().collect(
            Collectors.groupingBy(t -> t.getEveryIndustryId() + Constants.SEPARATOR_PUNCTUATION + t.getCityId()));

        List<IndustryBaseInitDO> industryBaseInitServiceList = industryBaseInitService.getIndustryBaseInitServiceListByTypes();

        if(!CollectionUtils.isEmpty(industryBaseInitServiceList)) {
            for (IndustryBaseInitDO industryBaseInitDO : industryBaseInitServiceList) {
                IndustryAllTypeLoadDTO industryAllTypeLoadDTO = new IndustryAllTypeLoadDTO();
                industryAllTypeLoadDTO.setId(industryBaseInitDO.getId());
                industryAllTypeLoadDTO.setName(industryBaseInitDO.getName());
                industryAllTypeLoadDTO.setType(industryBaseInitDO.getType());
                industryAllTypeLoadDTO.setLevel(industryBaseInitDO.getLevel());
                industryAllTypeLoadDTO.setParentId(industryBaseInitDO.getParentId());

                for (String s : collect.keySet()) {
                    String idType = industryBaseInitDO.getId() +
                        Constants.SEPARATOR_PUNCTUATION + cityId;
                    if (s.equals(idType)) {
                        IndustryAllValueDTO industryAllValueDTO = new IndustryAllValueDTO();
                        // 电量
                        BigDecimal reduce = collect.get(s).stream()
                            .filter(t -> t.getEnergy() != null)
                            .map(LoadFeatureEveryIndustryDayHisServiceDO::getEnergy)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                        industryAllValueDTO.setEnergy(reduce);
                        industryAllTypeLoadDTO.setIndustryAllValueDTO(industryAllValueDTO);
                    }
                }
                industryAllTypeLoadDTOList.add(industryAllTypeLoadDTO);
            }
        }
        // TODO 递归拿到所有产业和行业的信息
        if (!CollectionUtils.isEmpty(industryAllTypeLoadDTOList)) {
            industryAllTypeLoadDTOList.stream().filter(t -> t.getParentId() != "0").map(menu -> {
                List<IndustryAllTypeLoadDTO> children = getChildren(menu, industryAllTypeLoadDTOList);
                menu.setChild(children);
                return menu;
            }).collect(Collectors.toList());
        }
        // TODO 取前四个对应的产业
        List<IndustryAllTypeLoadDTO> result = new ArrayList<>();
        for (IndustryAllTypeLoadDTO results : industryAllTypeLoadDTOList) {
            for (IndustrialTypeEnum industrialTypeEnum : IndustrialTypeEnum.values()){
                if (industrialTypeEnum.getIndustrialName().equals(results.getName())) {
                    result.add(results);
                }
            }
        }
        return result;
    }

    @Override
    public List<IndustryAllTypeLoadDTO> queryAllLoadIndustryDetails(String cityId, Date startDate, Date endDate,
        String industryType) {
        List<IndustryAllTypeLoadDTO> industryAllTypeLoadDTOList = new ArrayList<>();
        // 先查出所有产业行业的负荷
        Map<String, List<LoadFeatureEveryIndustryDayHisServiceDO>> collect = loadFeatureEveryIndustryDayHisService.getLoadFeatureEveryIndustryDayHisServiceList(
            cityId, startDate, endDate).stream().collect(
            Collectors.groupingBy(t -> t.getEveryIndustryId() + Constants.SEPARATOR_PUNCTUATION + t.getCityId()));

        List<IndustryBaseInitDO> industryBaseInitServiceList = industryBaseInitService.getIndustryBaseInitServiceListByTypes();

        if(!CollectionUtils.isEmpty(industryBaseInitServiceList)) {
            for (IndustryBaseInitDO industryBaseInitDO : industryBaseInitServiceList) {
                IndustryAllTypeLoadDTO industryAllTypeLoadDTO = new IndustryAllTypeLoadDTO();
                industryAllTypeLoadDTO.setId(industryBaseInitDO.getId());
                industryAllTypeLoadDTO.setName(industryBaseInitDO.getName());
                industryAllTypeLoadDTO.setType(industryBaseInitDO.getType());
                industryAllTypeLoadDTO.setLevel(industryBaseInitDO.getLevel());
                industryAllTypeLoadDTO.setParentId(industryBaseInitDO.getParentId());

                for (String s : collect.keySet()) {
                    String idType = industryBaseInitDO.getId() +
                        Constants.SEPARATOR_PUNCTUATION + cityId;
                    if (s.equals(idType)) {
                        IndustryAllValueDTO industryAllValueDTO = new IndustryAllValueDTO();
                        // 早峰
                        Optional<LoadFeatureEveryIndustryDayHisServiceDO> maxMorningPeak = collect.get(s).stream()
                            .filter(t -> t.getMorningPeak() != null)
                            .max(Comparator.comparing(LoadFeatureEveryIndustryDayHisServiceDO::getMorningPeak));
                        if (maxMorningPeak.isPresent()) {
                            LoadFeatureEveryIndustryDayHisServiceDO maxMorningPeakLoad = maxMorningPeak.get();
                            industryAllValueDTO.setMorningLoad(maxMorningPeakLoad.getMorningPeak());
                        }
                        // 晚峰
                        Optional<LoadFeatureEveryIndustryDayHisServiceDO> maxEveningPeak = collect.get(s).stream()
                            .filter(t -> t.getEveningPeak() != null)
                            .max(Comparator.comparing(LoadFeatureEveryIndustryDayHisServiceDO::getEveningPeak));
                        if (maxEveningPeak.isPresent()) {
                            LoadFeatureEveryIndustryDayHisServiceDO maxEveningPeakLoad = maxEveningPeak.get();
                            industryAllValueDTO.setEveningLoad(maxEveningPeakLoad.getEveningPeak());
                        }
                        // 平段
                        Optional<LoadFeatureEveryIndustryDayHisServiceDO> maxNoontimePeak = collect.get(s).stream()
                            .filter(t -> t.getNoontimePeak() != null)
                            .max(Comparator.comparing(LoadFeatureEveryIndustryDayHisServiceDO::getNoontimePeak));
                        if (maxNoontimePeak.isPresent()) {
                            LoadFeatureEveryIndustryDayHisServiceDO maxNoontimePeakLoad = maxNoontimePeak.get();
                            industryAllValueDTO.setNoonTimeLoad(maxNoontimePeakLoad.getNoontimePeak());
                        }
                        // 低谷
                        Optional<LoadFeatureEveryIndustryDayHisServiceDO> maxTrough = collect.get(s).stream()
                            .filter(t -> t.getTroughMax() != null)
                            .max(Comparator.comparing(LoadFeatureEveryIndustryDayHisServiceDO::getTroughMax));
                        if (maxTrough.isPresent()) {
                            LoadFeatureEveryIndustryDayHisServiceDO maxTroughLoad = maxTrough.get();
                            industryAllValueDTO.setThoughLoad(maxTroughLoad.getTroughMax());
                        }
                        // 电量
                        Optional<LoadFeatureEveryIndustryDayHisServiceDO> maxEnergy = collect.get(s).stream()
                            .filter(t -> t.getEnergy() != null)
                            .max(Comparator.comparing(LoadFeatureEveryIndustryDayHisServiceDO::getEnergy));
                        if (maxEnergy.isPresent()) {
                            LoadFeatureEveryIndustryDayHisServiceDO maxEnergyLoad = maxEnergy.get();
                            industryAllValueDTO.setEnergy(maxEnergyLoad.getEnergy());
                        }
                        industryAllTypeLoadDTO.setIndustryAllValueDTO(industryAllValueDTO);
                    }
                }
                industryAllTypeLoadDTOList.add(industryAllTypeLoadDTO);
            }
        }
        // TODO 递归拿到所有产业和行业的信息
        if (!CollectionUtils.isEmpty(industryAllTypeLoadDTOList)) {
            industryAllTypeLoadDTOList.stream().filter(t -> t.getParentId() != "0").map(menu -> {
                List<IndustryAllTypeLoadDTO> children = getChildren(menu, industryAllTypeLoadDTOList);
                menu.setChild(children);
                return menu;
            }).collect(Collectors.toList());
        }
        // TODO 取前四个对应的产业
        List<IndustryAllTypeLoadDTO> result = new ArrayList<>();
        for (IndustryAllTypeLoadDTO results : industryAllTypeLoadDTOList) {
            for (IndustrialTypeEnum industrialTypeEnum : IndustrialTypeEnum.values()){
                if (industrialTypeEnum.getIndustrialName().equals(results.getName())) {
                    result.add(results);
                }
            }
        }
        // 增加重点行业返回
        List<String> industryList = new ArrayList<>();
        for (IndustrialImportFirstTypeEnum value : IndustrialImportFirstTypeEnum.values()) {
            industryList.add(value.getIndustryId());
        }
        List<LoadFeatureEveryIndustryDayHisServiceDO> loadFeatureEveryIndustryLists = loadFeatureEveryIndustryDayHisService.getLoadFeatureEveryIndustryDayHisServiceLists(
            cityId, startDate, endDate,
            industryList);

        IndustryAllTypeLoadDTO industryAllTypeLoadDTO1 = new IndustryAllTypeLoadDTO();
        IndustryAllTypeLoadDTO industryAllTypeLoadDTO2 = new IndustryAllTypeLoadDTO();

        List<IndustryAllTypeLoadDTO> industryAllTypeLoadDTOS1 = new ArrayList<>();
        List<IndustryAllTypeLoadDTO> industryAllTypeLoadDTOS2 = new ArrayList<>();
        for (IndustrialImportFirstTypeEnum value : IndustrialImportFirstTypeEnum.values()) {
            // 高耗能下的固定行业
            IndustryAllTypeLoadDTO industryAllTypeLoadDTO3 = new IndustryAllTypeLoadDTO();
            IndustryAllValueDTO industryAllValueDTO = new IndustryAllValueDTO();
            // 早峰
            Optional<LoadFeatureEveryIndustryDayHisServiceDO> maxMorningPeak = loadFeatureEveryIndustryLists.stream()
                .filter(t -> t.getMorningPeak() != null && value.getIndustryId().equals(t.getEveryIndustryId()))
                .max(Comparator.comparing(LoadFeatureEveryIndustryDayHisServiceDO::getMorningPeak));
            if (maxMorningPeak.isPresent()) {
                LoadFeatureEveryIndustryDayHisServiceDO maxMorningPeakLoad = maxMorningPeak.get();
                industryAllValueDTO.setMorningLoad(maxMorningPeakLoad.getMorningPeak());
            }
            // 晚峰
            Optional<LoadFeatureEveryIndustryDayHisServiceDO> maxEveningPeak = loadFeatureEveryIndustryLists.stream()
                .filter(t -> t.getEveningPeak() != null && value.getIndustryId().equals(t.getEveryIndustryId()))
                .max(Comparator.comparing(LoadFeatureEveryIndustryDayHisServiceDO::getEveningPeak));
            if (maxEveningPeak.isPresent()) {
                LoadFeatureEveryIndustryDayHisServiceDO maxEveningPeakLoad = maxEveningPeak.get();
                industryAllValueDTO.setEveningLoad(maxEveningPeakLoad.getEveningPeak());
            }
            // 平段
            Optional<LoadFeatureEveryIndustryDayHisServiceDO> maxNoontimePeak = loadFeatureEveryIndustryLists.stream()
                .filter(t -> t.getNoontimePeak() != null && value.getIndustryId().equals(t.getEveryIndustryId()))
                .max(Comparator.comparing(LoadFeatureEveryIndustryDayHisServiceDO::getNoontimePeak));
            if (maxNoontimePeak.isPresent()) {
                LoadFeatureEveryIndustryDayHisServiceDO maxNoontimePeakLoad = maxNoontimePeak.get();
                industryAllValueDTO.setNoonTimeLoad(maxNoontimePeakLoad.getNoontimePeak());
            }
            // 低谷
            Optional<LoadFeatureEveryIndustryDayHisServiceDO> maxTrough = loadFeatureEveryIndustryLists.stream()
                .filter(t -> t.getTroughMax() != null && value.getIndustryId().equals(t.getEveryIndustryId()))
                .max(Comparator.comparing(LoadFeatureEveryIndustryDayHisServiceDO::getTroughMax));
            if (maxTrough.isPresent()) {
                LoadFeatureEveryIndustryDayHisServiceDO maxTroughLoad = maxTrough.get();
                industryAllValueDTO.setThoughLoad(maxTroughLoad.getTroughMax());
            }
            Optional<LoadFeatureEveryIndustryDayHisServiceDO> energy = loadFeatureEveryIndustryLists.stream()
                .filter(t -> t.getEnergy() != null && value.getIndustryId().equals(t.getEveryIndustryId()))
                .max(Comparator.comparing(LoadFeatureEveryIndustryDayHisServiceDO::getTroughMax));
            if (energy.isPresent()) {
                LoadFeatureEveryIndustryDayHisServiceDO energyMax = energy.get();
                industryAllValueDTO.setEnergy(energyMax.getEnergy());
            }

            industryAllTypeLoadDTO3.setName(value.getIndustryName());
            industryAllTypeLoadDTO3.setIndustryAllValueDTO(industryAllValueDTO);

            industryAllTypeLoadDTOS2.add(industryAllTypeLoadDTO3);
        }
        // 重点行业下的高耗能
        industryAllTypeLoadDTO2.setChild(industryAllTypeLoadDTOS2);
        industryAllTypeLoadDTO2.setName(IndustrialImportTypeEnum.FIRST.getIndustryName());
        industryAllTypeLoadDTOS1.add(industryAllTypeLoadDTO2);
        // 重点行业
        industryAllTypeLoadDTO1.setChild(industryAllTypeLoadDTOS1);
        industryAllTypeLoadDTO1.setName("重点行业");
        result.add(industryAllTypeLoadDTO1);
        return result;
    }

    @Override
    public List<IndustrialImportTypeDTO> queryAllLoadDateIndustryDetails(String cityId, Date startDate, Date endDate,
        String industryType) {
        List<IndustrialImportTypeDTO> industrialImportTypeDTOS = new ArrayList<>();
        List<String> typeList = new ArrayList<>();
        List<String> industryList = new ArrayList<>();
        for (IndustrialImportTypeEnum value : IndustrialImportTypeEnum.values()) {
            if ("0".equals(value.getIndustryId())) {continue;}
            typeList.add(value.getIndustryId());
        }
        for (IndustrialImportFirstTypeEnum firstValue : IndustrialImportFirstTypeEnum.values()) {
            industryList.add(firstValue.getIndustryId());
        }
        List<LoadFeatureEveryIndustryDayHisServiceDO> loadFeatureEveryIndustryList = loadFeatureEveryIndustryDayHisService.getLoadFeatureEveryIndustryDayHisServiceTypeList(
            cityId, startDate, endDate, typeList);

        Map<Date, List<LoadFeatureEveryIndustryDayHisServiceDO>> collect = loadFeatureEveryIndustryDayHisService.getLoadFeatureEveryIndustryDayHisServiceLists(
            cityId, startDate, endDate, industryList).stream().collect(
            Collectors.groupingBy(t -> t.getDate()));

        for (IndustrialImportTypeEnum value : IndustrialImportTypeEnum.values()) {
            IndustrialImportTypeDTO industrialImportTypeDTO = new IndustrialImportTypeDTO();
            List<IndustryImportValueDTO> industryImportValueDTOS = new ArrayList<>();
            List<IndustryImportValueDTO> sortValueDTOS = new ArrayList<>();
            List<IndustryImportValueDTO> sortsValueDTOS = new ArrayList<>();
            if ("0".equals(value.getIndustryId())) {
                // 高耗能需要单独计算（目前先将四个子高耗能行业值相加）
                if (MapUtils.isNotEmpty(collect)){
                    for (Entry<Date, List<LoadFeatureEveryIndustryDayHisServiceDO>> dateListEntry : collect.entrySet()) {
                        IndustryImportValueDTO industryImportValueDTO = new IndustryImportValueDTO();
                        industryImportValueDTO.setDate(dateListEntry.getKey());
                        // 早峰
                        BigDecimal reduce = dateListEntry.getValue()
                            .stream().filter(t -> t.getMorningPeak() != null)
                            .map(LoadFeatureEveryIndustryDayHisServiceDO::getMorningPeak)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                        industryImportValueDTO.setMorningLoad(reduce);
                        // 晚峰
                        BigDecimal reduce1 = dateListEntry.getValue()
                            .stream().filter(t -> t.getEveningPeak() != null)
                            .map(LoadFeatureEveryIndustryDayHisServiceDO::getEveningPeak)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                        industryImportValueDTO.setEveningLoad(reduce1);
                        // 低谷
                        BigDecimal reduce2 = dateListEntry.getValue()
                            .stream().filter(t -> t.getTroughMax() != null)
                            .map(LoadFeatureEveryIndustryDayHisServiceDO::getTroughMax)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                        industryImportValueDTO.setThoughLoad(reduce2);
                        // 电量
                        BigDecimal reduce3 = dateListEntry.getValue()
                            .stream().filter(t -> t.getEnergy() != null)
                            .map(LoadFeatureEveryIndustryDayHisServiceDO::getEnergy)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                        industryImportValueDTO.setEnergy(reduce3);
                        industryImportValueDTOS.add(industryImportValueDTO);
                    }
                }
                industrialImportTypeDTO.setName(value.getIndustryName());
                sortValueDTOS = industryImportValueDTOS.stream()
                    .sorted(Comparator.comparing(IndustryImportValueDTO::getDate)).collect(Collectors.toList());
            }
            industrialImportTypeDTO.setName(value.getIndustryName());
            if (CollectionUtils.isNotEmpty(loadFeatureEveryIndustryList)) {
                for (LoadFeatureEveryIndustryDayHisServiceDO loadFeatureEveryIndustryDO : loadFeatureEveryIndustryList) {
                    if (value.getIndustryId().equals(loadFeatureEveryIndustryDO.getType())) {
                        IndustryImportValueDTO industryImportValueDTO = new IndustryImportValueDTO();
                        industryImportValueDTO.setDate(loadFeatureEveryIndustryDO.getDate());
                        industryImportValueDTO.setMorningLoad(loadFeatureEveryIndustryDO.getMorningPeak());
                        industryImportValueDTO.setEveningLoad(loadFeatureEveryIndustryDO.getEveningPeak());
                        industryImportValueDTO.setThoughLoad(loadFeatureEveryIndustryDO.getTroughMax());
                        industryImportValueDTO.setEnergy(loadFeatureEveryIndustryDO.getEnergy());
                        sortValueDTOS.add(industryImportValueDTO);
                    }
                }
             /*   sortValueDTOS = industryImportValueDTOS.stream()
                    .sorted(Comparator.comparing(IndustryImportValueDTO::getDate)).collect(Collectors.toList());*/
            }
            sortsValueDTOS = sortValueDTOS.stream()
                .sorted(Comparator.comparing(IndustryImportValueDTO::getDate)).collect(Collectors.toList());
            industrialImportTypeDTO.setDateList(sortsValueDTOS);
            industrialImportTypeDTOS.add(industrialImportTypeDTO);
        }
        return industrialImportTypeDTOS;
    }

    private List<IndustryAllTypeLoadDTO> getChildren(IndustryAllTypeLoadDTO menu, List<IndustryAllTypeLoadDTO> list) {
        List<IndustryAllTypeLoadDTO> collect = list.stream()
            .filter(o -> menu.getId().equals(o.getParentId()))
            .map(cat-> {
                List<IndustryAllTypeLoadDTO> children = getChildren(cat, list);
                cat.setChild(children);
                return cat;
            })
            .collect(Collectors.toList());
        return collect;
    }

    private List<IndustryAllTypeDateLoadDTO> getChildren(IndustryAllTypeDateLoadDTO menu, List<IndustryAllTypeDateLoadDTO> list) {
        List<IndustryAllTypeDateLoadDTO> collect = list.stream()
            .filter(o -> menu.getId().equals(o.getParentId()))
            .map(cat-> {
                List<IndustryAllTypeDateLoadDTO> children = getChildren(cat, list);
                cat.setChild(children);
                return cat;
            })
            .collect(Collectors.toList());
        return collect;
    }
}

