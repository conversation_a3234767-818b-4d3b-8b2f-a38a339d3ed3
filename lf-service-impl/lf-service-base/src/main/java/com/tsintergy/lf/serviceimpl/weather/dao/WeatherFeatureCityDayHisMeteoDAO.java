package com.tsintergy.lf.serviceimpl.weather.dao;

import com.tsieframework.core.base.dao.DataPackage;
import com.tsieframework.core.base.dao.hibernate.*;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.util.compatible.BeanUtils;
import com.tsieframework.util.compatible.StringUtils;
import com.tsintergy.lf.core.enums.ConditionEnum;
import com.tsintergy.lf.core.enums.WeatherScreenEnum;
import com.tsintergy.lf.serviceapi.base.forecast.dto.SearchDTO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayHisMeteoDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version $Id: WeatherFeatureCityDayHisMeteoDAO.java, v 0.1 2018-01-31 11:00:29 tao Exp $$
 */

@Component
@Slf4j
public class WeatherFeatureCityDayHisMeteoDAO extends BaseAbstractDAO<WeatherFeatureCityDayHisMeteoDO> {

    public List<WeatherFeatureCityDayHisMeteoDO> findWeatherFeatureCityHisVOs(String cityId, Date date) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("1");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (StringUtils.isNotBlank(cityId)) {
            param.getQueryConditions().put("_se_cityId", cityId);
        }
        if (null != date) {
            param.getQueryConditions().put("_de_date", new java.sql.Date(date.getTime()));
        }
        param.setOrderby("date");
        param.setDesc("0");
        List<WeatherFeatureCityDayHisMeteoDO> weatherFeatureCityDayHisMeteoVOS = this.query(param).getDatas();
        return weatherFeatureCityDayHisMeteoVOS;
    }


    /**
     * 获取气象特性
     *
     * @param cityId 城市ID
     * @param date 日期
     */
    public WeatherFeatureCityDayHisMeteoDO getWeatherFeatureCityDayHisMeteoDO(String cityId, Date date) throws Exception {

        if (cityId == null) {
            throw new BusinessException("T706", "城市ID不可为空");
        }

        if (date == null) {
            throw new BusinessException("T706", "日期不可为空");
        }
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        param.getQueryConditions().put("_de_date", new java.sql.Date(date.getTime()));
        param.getQueryConditions().put("_ne_cityId", cityId);
        List<WeatherFeatureCityDayHisMeteoDO> weatherFeatureCityDayHisMeteoVOs = this.query(param).getDatas();
        if (weatherFeatureCityDayHisMeteoVOs.size() > 0) {
            return weatherFeatureCityDayHisMeteoVOs.get(0);
        }
        return null;
    }

    /**
     * 保存或更新
     */
    public WeatherFeatureCityDayHisMeteoDO doSaveOrUpdateWeatherFeatureCityDayHisMeteoDO(
        WeatherFeatureCityDayHisMeteoDO weatherFeatureCityDayHisMeteoVO) throws Exception {
        WeatherFeatureCityDayHisMeteoDO oldVO = getWeatherFeatureCityDayHisMeteoDO(weatherFeatureCityDayHisMeteoVO.getCityId(),
            weatherFeatureCityDayHisMeteoVO.getDate());
        if (oldVO != null) {
            String id = oldVO.getId();
            BeanUtils.copyPropertiesNotNull(oldVO, weatherFeatureCityDayHisMeteoVO);
            oldVO.setId(id);
            return (WeatherFeatureCityDayHisMeteoDO) this.updateAndFlush(oldVO);
        } else {
            return (WeatherFeatureCityDayHisMeteoDO) this.createAndFlush(weatherFeatureCityDayHisMeteoVO);
        }
    }

    /**
     * 保存或更新
     */
    public List<WeatherFeatureCityDayHisMeteoDO> doSaveOrUpdateWeatherFeatureCityDayHisMeteoDOs(
        List<WeatherFeatureCityDayHisMeteoDO> weatherFeatureCityDayHisMeteoVOs) throws Exception {
        List<WeatherFeatureCityDayHisMeteoDO> vos = new ArrayList<WeatherFeatureCityDayHisMeteoDO>();
        for (WeatherFeatureCityDayHisMeteoDO weatherFeatureCityDayHisMeteoVO : weatherFeatureCityDayHisMeteoVOs) {
            try {
                vos.add(this.doSaveOrUpdateWeatherFeatureCityDayHisMeteoDO(weatherFeatureCityDayHisMeteoVO));
            } catch (Exception e) {
                log.error("保存日气象特性出错了", e);
            }
        }
        return vos;
    }

    public List<WeatherFeatureCityDayHisMeteoDO> getWeatherFeatureCityDayHisMeteoDOs(String cityId, Date startDate, Date endDate)
        throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (null != startDate) {
            param.getQueryConditions().put("_dnl_date", new java.sql.Date(startDate.getTime()));
        }
        if (null != endDate) {
            param.getQueryConditions().put("_dnm_date", new java.sql.Date(endDate.getTime()));
        }
        if (null != cityId) {
            param.getQueryConditions().put("_se_cityId", cityId);
        }
        param.setOrderby("date");
        param.setDesc("0");
        return this.query(param).getDatas();
    }


    public List<WeatherFeatureCityDayHisMeteoDO> listWeatherFeatureCityDayHisMeteoDO(List<String> cityIds, Date startDate,
        Date endDate) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (!CollectionUtils.isEmpty(cityIds)) {
            param.getQueryConditions().put("_sin_cityId", cityIds);
        }
        if (null != startDate) {
            param.getQueryConditions().put("_dnl_date", new java.sql.Date(startDate.getTime()));
        }
        if (null != endDate) {
            param.getQueryConditions().put("_dnm_date", new java.sql.Date(endDate.getTime()));
        }
        param.setOrderby("date");
        param.setDesc("0");
        List<WeatherFeatureCityDayHisMeteoDO> weatherFeatureCityDayHisMeteoVOs = this.query(param).getDatas();
        return weatherFeatureCityDayHisMeteoVOs;
    }


    public List<WeatherFeatureCityDayHisMeteoDO> listWeatherFeatureCityDayHisMeteoDO(List<Date> dates, String cityId)
        throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (null != dates) {
            List<java.sql.Date> sqlDates = new ArrayList<java.sql.Date>();
            for (Date date : dates) {
                sqlDates.add(new java.sql.Date(date.getTime()));
            }
            param.getQueryConditions().put("_din_date", sqlDates);
        }
        if (null != cityId) {
            param.getQueryConditions().put("_se_cityId", cityId);
        }
        param.setOrderby("date");
        param.setDesc("0");
        List<WeatherFeatureCityDayHisMeteoDO> weatherFeatureCityDayHisMeteoVOs = this.query(param).getDatas();
        return weatherFeatureCityDayHisMeteoVOs;
    }


    /**
     * 多条件筛选气相特征日统计数据
     *
     * @param screenData 筛选对象
     * @return 实体对象
     */

    public List<WeatherFeatureCityDayHisMeteoDO> findWeatherFeature(String cityId, Date startDate, Date endDate,
        List<SearchDTO> screenData) throws Exception {
        DBQueryParamBuilder param = DBQueryParamBuilder.create().queryDataOnly();
        if (cityId != null) {
            param.where(QueryOp.StringEqualTo, "cityId", cityId);
        }
        if (startDate != null) {
            param.where(QueryOp.DateNoLessThan, "date", new java.sql.Date(startDate.getTime()));
        }
        if (endDate != null) {
            param.where(QueryOp.DateNoMoreThan, "date", new java.sql.Date(endDate.getTime()));
        }
        if (!CollectionUtils.isEmpty(screenData) || screenData != null) {
            for (SearchDTO data : screenData) {
                if (ConditionEnum.BETWEEN_THAN.getType().equals(data.getCondition())) {
                    String[] split = data.getValue().split("-");
                    param.where(QueryOp.NumberNoLessThan, WeatherScreenEnum.getSqlName(data.getType()),
                        new BigDecimal(split[0]));
                    param.where(QueryOp.NumberNoMoreThan, WeatherScreenEnum.getSqlName(data.getType()),
                        new BigDecimal(split[1]));
                } else {
                    param.where(ConditionEnum.getSqlName(data.getCondition()),
                        WeatherScreenEnum.getSqlName(data.getType()),
                        new BigDecimal(data.getValue()));
                }
            }
        }
        DataPackage query = query(param.build());
        return query.getDatas();
    }

}