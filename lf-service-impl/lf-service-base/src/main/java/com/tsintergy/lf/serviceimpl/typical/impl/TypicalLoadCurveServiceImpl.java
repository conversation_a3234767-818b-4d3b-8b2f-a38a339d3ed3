package com.tsintergy.lf.serviceimpl.typical.impl;

import static com.tsintergy.lf.serviceapi.base.typical.enums.CurveLoadCompareEnum.MAX_ENERGY_DAY;
import static com.tsintergy.lf.serviceapi.base.typical.enums.CurveLoadCompareEnum.MAX_LOAD_DAY;
import static com.tsintergy.lf.serviceapi.base.typical.enums.CurveLoadCompareEnum.MAX_PEAK_DAY;
import static com.tsintergy.lf.serviceapi.base.typical.enums.CurveLoadCompareEnum.MIN_LOAD_DAY;
import static com.tsintergy.lf.serviceapi.base.typical.enums.CurveLoadCompareEnum.REST_DAY;
import static com.tsintergy.lf.serviceapi.base.typical.enums.CurveLoadCompareEnum.SATURDAY;
import static com.tsintergy.lf.serviceapi.base.typical.enums.CurveLoadCompareEnum.SUNDAY;
import static com.tsintergy.lf.serviceapi.base.typical.enums.CurveLoadCompareEnum.WORKING_DAY;

import com.alibaba.nacos.common.utils.MapUtils;
import com.tsieframework.core.base.math.BigDecimalFunctions;
import com.tsintergy.aif.algorithm.serviceapi.base.util.DateUtil;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.enums.TenDaysTypeEnum;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.serviceapi.base.base.api.HolidayService;
import com.tsintergy.lf.serviceapi.base.bgd.api.LoadFeatureCityTenDaysHisService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityDayHisService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityDayHisDO;
import com.tsintergy.lf.serviceapi.base.typical.api.TypicalLoadCurveService;
import com.tsintergy.lf.serviceapi.base.typical.dto.Curve96Load;
import com.tsintergy.lf.serviceapi.base.typical.enums.CurveLoadCompareEnum;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2023-09-18
 * @since 1.0.0
 */
@Service("typicalLoadCurveService")
public class TypicalLoadCurveServiceImpl implements TypicalLoadCurveService {

    @Autowired
    private LoadCityHisService loadCityHisService;

    @Autowired
    private LoadFeatureCityDayHisService loadFeatureCityDayHisService;

    @Autowired
    private HolidayService holidayService;

    @Autowired
    private LoadFeatureCityTenDaysHisService loadFeatureCityTenDaysHisService;

    @Override
    public List<Curve96Load> findWeekCurveCompare(String cityId, String dateStr)
        throws Exception {
        List<Curve96Load> result = new ArrayList<>();
        String year = dateStr.split("-")[0];
        String week = dateStr.split("-")[1];
        Date yearFirst = DateUtil.getYearFirst(Integer.valueOf(year));
        // 周一
        Date weekStart = DateUtil.getMondayByDate(yearFirst);
        // 周末
        Date weekEnd = DateUtils.addDays(weekStart, +6);
        if (Integer.parseInt(week) > 1) {
            weekStart = DateUtils.addDays(weekStart, (Integer.parseInt(week) - 1) * 7);
            weekEnd = DateUtils.addDays(weekStart, +6);
        }
        // 不排除节假日
        List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisDOS = loadFeatureCityDayHisService.findLoadFeatureCityDayHisDOS(
            cityId, weekStart, weekEnd, getCaliberId(cityId));
        List<LoadCityHisDO> loadCityHisDOS = loadCityHisService.findLoadCityHisDOS(cityId, weekStart, weekEnd,
            getCaliberId(cityId));
        Map<String, List<LoadCityHisDO>> collect = loadCityHisDOS.stream()
            .collect(Collectors.groupingBy(t -> DateUtil.getStrDate(t.getDate(), "yyyy-MM-dd")));
        // 排除节假日
        List<LoadCityHisDO> loadCityHisDOS1 = new ArrayList<>();
        // 工作日
        List<LoadCityHisDO> loadCityHisDOS2 = new ArrayList<>();
        // 周六
        List<LoadCityHisDO> loadCityHisDOS3 = new ArrayList<>();
        // 周日
        List<LoadCityHisDO> loadCityHisDOS4 = new ArrayList<>();
        List<Date> holiday = holidayService.findHoliday(weekStart, weekEnd);
        if (!CollectionUtils.isEmpty(loadCityHisDOS)) {
            for (LoadCityHisDO loadCityHisDO : loadCityHisDOS) {
                if (!holiday.contains(loadCityHisDO.getDate())) {
                    loadCityHisDOS1.add(loadCityHisDO);
                }
            }
        }
        for (CurveLoadCompareEnum value : CurveLoadCompareEnum.values()) {
            Calendar cal = Calendar.getInstance();
            BigDecimal bigDecimal = null;
            List<BigDecimal> bigDecimals = new ArrayList<>();
            Date date = null;
            if (value.equals(REST_DAY)) {
                continue;
            }
            Curve96Load curve96Load = new Curve96Load();
            switch (value) {
                case WORKING_DAY:
                    // 工作日
                    for (LoadCityHisDO loadCityHisDO : loadCityHisDOS1) {
                        if (com.tsintergy.lf.core.util.DateUtil.isWorkingDay(
                            loadCityHisDO.getDate().getTime())) {
                            loadCityHisDOS2.add(loadCityHisDO);
                        }
                    }
                    if (!CollectionUtils.isEmpty(loadCityHisDOS2)) {
                        List<BigDecimal> zeroList = ColumnUtil.getZeroOrNullList(96, BigDecimal.ZERO);
                        for (LoadCityHisDO loadCityHisDO : loadCityHisDOS2) {
                            zeroList = BigDecimalFunctions.listAdd(zeroList, loadCityHisDO.getloadList());
                        }
                        List<BigDecimal> bigDecimals1 = BigDecimalFunctions.listDivideValue(zeroList, new BigDecimal(loadCityHisDOS2.size()));
                        curve96Load.setBigDecimals(bigDecimals1);
                    }
                    curve96Load.setDateStr(WORKING_DAY.getTypeName() + "（" + dateStr + "周）" );
                    result.add(curve96Load);
                    break;
                case SATURDAY:
                    // 周六
                    for (LoadCityHisDO loadCityHisDO : loadCityHisDOS1) {
                        cal.setTime(loadCityHisDO.getDate());
                        if (cal.get(Calendar.DAY_OF_WEEK) == Calendar.SATURDAY) {
                            loadCityHisDOS3.add(loadCityHisDO);
                        }
                    }
                    if (!CollectionUtils.isEmpty(loadCityHisDOS3)) {
                        List<BigDecimal> zeroList = ColumnUtil.getZeroOrNullList(96, BigDecimal.ZERO);
                        for (LoadCityHisDO loadCityHisDO : loadCityHisDOS3) {
                            zeroList = BigDecimalFunctions.listAdd(zeroList, loadCityHisDO.getloadList());
                        }
                        List<BigDecimal> bigDecimals1 = BigDecimalFunctions.listDivideValue(zeroList, new BigDecimal(loadCityHisDOS3.size()));
                        curve96Load.setBigDecimals(bigDecimals1);
                    }
                    curve96Load.setDateStr(SATURDAY.getTypeName() + "（" + dateStr + "周）" );
                    result.add(curve96Load);
                    break;
                case SUNDAY:
                    // 周日
                    for (LoadCityHisDO loadCityHisDO : loadCityHisDOS1) {
                        cal.setTime(loadCityHisDO.getDate());
                        if (cal.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY) {
                            loadCityHisDOS4.add(loadCityHisDO);
                        }
                    }
                    if (!CollectionUtils.isEmpty(loadCityHisDOS4)) {
                        List<BigDecimal> zeroList = ColumnUtil.getZeroOrNullList(96, BigDecimal.ZERO);
                        for (LoadCityHisDO loadCityHisDO : loadCityHisDOS4) {
                            zeroList = BigDecimalFunctions.listAdd(zeroList, loadCityHisDO.getloadList());
                        }
                        List<BigDecimal> bigDecimals1 = BigDecimalFunctions.listDivideValue(zeroList, new BigDecimal(loadCityHisDOS4.size()));
                        curve96Load.setBigDecimals(bigDecimals1);
                    }
                    curve96Load.setDateStr(SUNDAY.getTypeName() + "（" + dateStr + "周）" );
                    result.add(curve96Load);
                    break;
                case MAX_LOAD_DAY:
                    // 最大负荷日
                    if (!CollectionUtils.isEmpty(loadFeatureCityDayHisDOS)) {
                        bigDecimal = loadFeatureCityDayHisDOS.stream()
                            .max(Comparator.comparing(LoadFeatureCityDayHisDO::getMaxLoad)).get().getMaxLoad();
                        for (LoadFeatureCityDayHisDO loadFeatureCityDayHisDO : loadFeatureCityDayHisDOS) {
                            if (loadFeatureCityDayHisDO.getMaxLoad().equals(bigDecimal)) {
                                date = loadFeatureCityDayHisDO.getDate();
                            }
                        }
                    }
                    if (MapUtils.isNotEmpty(collect)) {
                        List<LoadCityHisDO> loadCityHisDOS5 = collect.get(DateUtil.getStrDate(date, "yyyy-MM-dd"));
                        if (!CollectionUtils.isEmpty(loadCityHisDOS5)) {
                            bigDecimals = loadCityHisDOS5.get(0).getloadList();
                        }
                    }
                    curve96Load.setDateStr(MAX_LOAD_DAY.getTypeName() + "（" + DateUtil.getStrDate(date, "yyyy-MM-dd") + "）" );
                    curve96Load.setBigDecimals(bigDecimals);
                    result.add(curve96Load);
                    break;
                case MIN_LOAD_DAY:
                    // 最小负荷日
                    if (!CollectionUtils.isEmpty(loadFeatureCityDayHisDOS)) {
                        bigDecimal = loadFeatureCityDayHisDOS.stream()
                            .min(Comparator.comparing(LoadFeatureCityDayHisDO::getMinLoad)).get().getMinLoad();
                        for (LoadFeatureCityDayHisDO loadFeatureCityDayHisDO : loadFeatureCityDayHisDOS) {
                            if (loadFeatureCityDayHisDO.getMinLoad().equals(bigDecimal)) {
                                date = loadFeatureCityDayHisDO.getDate();
                            }
                        }
                    }
                    if (MapUtils.isNotEmpty(collect)) {
                        List<LoadCityHisDO> loadCityHisDOS5 = collect.get(DateUtil.getStrDate(date, "yyyy-MM-dd"));
                        if (!CollectionUtils.isEmpty(loadCityHisDOS5)) {
                            bigDecimals = loadCityHisDOS5.get(0).getloadList();
                        }
                    }
                    curve96Load.setDateStr(MIN_LOAD_DAY.getTypeName() + "（" + DateUtil.getStrDate(date, "yyyy-MM-dd") + "）" );
                    curve96Load.setBigDecimals(bigDecimals);
                    result.add(curve96Load);
                    break;
                case MAX_ENERGY_DAY:
                    // 最大电量日
                    if (!CollectionUtils.isEmpty(loadFeatureCityDayHisDOS)) {
                        bigDecimal = loadFeatureCityDayHisDOS.stream()
                            .max(Comparator.comparing(LoadFeatureCityDayHisDO::getEnergy)).get().getEnergy();
                        for (LoadFeatureCityDayHisDO loadFeatureCityDayHisDO : loadFeatureCityDayHisDOS) {
                            if (loadFeatureCityDayHisDO.getEnergy().equals(bigDecimal)) {
                                date = loadFeatureCityDayHisDO.getDate();
                            }
                        }
                    }
                    if (MapUtils.isNotEmpty(collect)) {
                        List<LoadCityHisDO> loadCityHisDOS5 = collect.get(DateUtil.getStrDate(date, "yyyy-MM-dd"));
                        if (!CollectionUtils.isEmpty(loadCityHisDOS5)) {
                            bigDecimals = loadCityHisDOS5.get(0).getloadList();
                        }
                    }
                    curve96Load.setDateStr(MAX_ENERGY_DAY.getTypeName() + "（" + DateUtil.getStrDate(date, "yyyy-MM-dd") + "）");
                    curve96Load.setBigDecimals(bigDecimals);
                    result.add(curve96Load);
                    break;
                case MAX_PEAK_DAY:
                    // 最大峰谷差日
                    if (!CollectionUtils.isEmpty(loadFeatureCityDayHisDOS)) {
                        bigDecimal = loadFeatureCityDayHisDOS.stream()
                            .max(Comparator.comparing(LoadFeatureCityDayHisDO::getDifferent)).get().getDifferent();
                        for (LoadFeatureCityDayHisDO loadFeatureCityDayHisDO : loadFeatureCityDayHisDOS) {
                            if (loadFeatureCityDayHisDO.getDifferent().equals(bigDecimal)) {
                                date = loadFeatureCityDayHisDO.getDate();
                            }
                        }
                    }
                    if (MapUtils.isNotEmpty(collect)) {
                        List<LoadCityHisDO> loadCityHisDOS5 = collect.get(DateUtil.getStrDate(date, "yyyy-MM-dd"));
                        if (!CollectionUtils.isEmpty(loadCityHisDOS5)) {
                            bigDecimals = loadCityHisDOS5.get(0).getloadList();
                        }
                    }
                    curve96Load.setDateStr(MAX_PEAK_DAY.getTypeName() + "（" + DateUtil.getStrDate(date, "yyyy-MM-dd") + "）");
                    curve96Load.setBigDecimals(bigDecimals);
                    result.add(curve96Load);
                    break;
                default:
                    break;
            }
        }
        return result;
    }

    @Override
    public List<Curve96Load> findYearCurveCompare(String cityId, String dateStr, Integer type) throws Exception {
        List<Curve96Load> result = new ArrayList<>();
        List<Date> list = new ArrayList<>();
        List<Date> list1 = new ArrayList<>();
        List<String> list2 = new ArrayList<>();
        String year = dateStr.split("-")[0];
        Integer integer = Integer.valueOf(year);
        String week = dateStr.split("-")[1];
        for (Integer i = 0; i < Integer.valueOf(year); integer--) {
            if (integer == 2017) {
                break;
            }
            list2.add(String.valueOf(integer));
            Date yearFirst = DateUtil.getYearFirst(integer);
            // 周一
            Date weekStart = DateUtil.getMondayByDate(yearFirst);
            // 周末
            Date weekEnd = DateUtils.addDays(weekStart, +6);
            if (Integer.parseInt(week) > 1) {
                weekStart = DateUtils.addDays(weekStart, (Integer.parseInt(week) - 1) * 7);
                weekEnd = DateUtils.addDays(weekStart, +6);
            }
            List<Date> listBetweenDay = DateUtil.getListBetweenDay(weekStart, weekEnd);
            List<Date> holiday = holidayService.findHoliday(weekStart, weekEnd);
            list.addAll(listBetweenDay);
            list1.addAll(holiday);
        }
        // 不排除节假日
        List<LoadCityHisDO> loadCityHisList = loadCityHisService.findLoadCityDOsByCityIdInDates(cityId, list,
            getCaliberId(cityId));
        Map<String, List<LoadCityHisDO>> collect1 = loadCityHisList.stream()
            .collect(Collectors.groupingBy(t -> DateUtil.getStrDate(t.getDate(), "yyyy-MM-dd").substring(0, 4)));
        List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisDOS = loadFeatureCityDayHisService.listLoadFeature(cityId,
            getCaliberId(cityId), list);
        Map<String, List<LoadFeatureCityDayHisDO>> collect2 = loadFeatureCityDayHisDOS.stream()
            .collect(Collectors.groupingBy(t -> DateUtil.getStrDate(t.getDate(), "yyyy-MM-dd").substring(0, 4)));
        for (String yearStr : list2) {
            // 排除节假日
            List<LoadCityHisDO> loadCityHisDOS1 = new ArrayList<>();
            // 工作日
            List<LoadCityHisDO> loadCityHisDOS2 = new ArrayList<>();
            // 周六
            List<LoadCityHisDO> loadCityHisDOS3 = new ArrayList<>();
            // 周日
            List<LoadCityHisDO> loadCityHisDOS4 = new ArrayList<>();
            if (!CollectionUtils.isEmpty(collect1.get(yearStr))) {
                for (LoadCityHisDO loadCityHisDO : collect1.get(yearStr)) {
                    if (!list1.contains(loadCityHisDO.getDate())) {
                        loadCityHisDOS1.add(loadCityHisDO);
                    }
                }
            }
            // 去年需要关注
            Date dates = DateUtil.getDate(Integer.parseInt(yearStr) - 1 + "-12-20", "yyyy-MM-dd");
            if (!CollectionUtils.isEmpty(collect1.get(String.valueOf(Integer.parseInt(yearStr) - 1)))) {
                List<LoadCityHisDO> loadCityHisDOS = collect1.get(String.valueOf(Integer.parseInt(yearStr) - 1));
                for (LoadCityHisDO loadCityHisDO : loadCityHisDOS) {
                    if (!list1.contains(loadCityHisDO.getDate()) && loadCityHisDO.getDate().compareTo(dates) > 0) {
                        loadCityHisDOS1.add(loadCityHisDO);
                    }
                }
            }
            List<LoadCityHisDO> loadCityHisDOS = collect1.get(yearStr);
            Map<String, List<LoadCityHisDO>> collect = new HashMap<>();
            if (!CollectionUtils.isEmpty(loadCityHisDOS)) {
                collect = loadCityHisDOS.stream()
                    .collect(Collectors.groupingBy(t -> DateUtil.getStrDate(t.getDate(), "yyyy-MM-dd")));
            }

            List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisDOS1 = collect2.get(yearStr);
            for (CurveLoadCompareEnum value : CurveLoadCompareEnum.values()) {
                Calendar cal = Calendar.getInstance();
                BigDecimal bigDecimal = null;
                List<BigDecimal> bigDecimals = new ArrayList<>();
                Date date = null;
                if (value.equals(REST_DAY) || !value.getSort().equals(type)) {
                    continue;
                }
                Curve96Load curve96Load = new Curve96Load();
                switch (value) {
                    case WORKING_DAY:
                        // 工作日
                        for (LoadCityHisDO loadCityHisDO : loadCityHisDOS1) {
                            if (com.tsintergy.lf.core.util.DateUtil.isWorkingDay(
                                loadCityHisDO.getDate().getTime())) {
                                loadCityHisDOS2.add(loadCityHisDO);
                            }
                        }
                        if (!CollectionUtils.isEmpty(loadCityHisDOS2)) {
                            List<BigDecimal> zeroList = ColumnUtil.getZeroOrNullList(96, BigDecimal.ZERO);
                            for (LoadCityHisDO loadCityHisDO : loadCityHisDOS2) {
                                zeroList = BigDecimalFunctions.listAdd(zeroList, loadCityHisDO.getloadList());
                            }
                            List<BigDecimal> bigDecimals1 = BigDecimalFunctions.listDivideValue(zeroList, new BigDecimal(loadCityHisDOS2.size()));
                            curve96Load.setBigDecimals(bigDecimals1);
                        }
                        curve96Load.setDateStr(WORKING_DAY.getTypeName() + "（" + yearStr + "-" +  week + "周）" );
                        result.add(curve96Load);
                        break;
                    case SATURDAY:
                        // 周六
                        for (LoadCityHisDO loadCityHisDO : loadCityHisDOS1) {
                            cal.setTime(loadCityHisDO.getDate());
                            if (cal.get(Calendar.DAY_OF_WEEK) == Calendar.SATURDAY) {
                                loadCityHisDOS3.add(loadCityHisDO);
                            }
                        }
                        if (!CollectionUtils.isEmpty(loadCityHisDOS3)) {
                            List<BigDecimal> zeroList = ColumnUtil.getZeroOrNullList(96, BigDecimal.ZERO);
                            for (LoadCityHisDO loadCityHisDO : loadCityHisDOS3) {
                                zeroList = BigDecimalFunctions.listAdd(zeroList, loadCityHisDO.getloadList());
                            }
                            List<BigDecimal> bigDecimals1 = BigDecimalFunctions.listDivideValue(zeroList, new BigDecimal(loadCityHisDOS3.size()));
                            curve96Load.setBigDecimals(bigDecimals1);
                        }
                        curve96Load.setDateStr(SATURDAY.getTypeName() + "（" + yearStr + "-" +  week + "周）" );
                        result.add(curve96Load);
                        break;
                    case SUNDAY:
                        // 周日
                        for (LoadCityHisDO loadCityHisDO : loadCityHisDOS1) {
                            cal.setTime(loadCityHisDO.getDate());
                            if (cal.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY) {
                                loadCityHisDOS4.add(loadCityHisDO);
                            }
                        }
                        if (!CollectionUtils.isEmpty(loadCityHisDOS4)) {
                            List<BigDecimal> zeroList = ColumnUtil.getZeroOrNullList(96, BigDecimal.ZERO);
                            for (LoadCityHisDO loadCityHisDO : loadCityHisDOS4) {
                                zeroList = BigDecimalFunctions.listAdd(zeroList, loadCityHisDO.getloadList());
                            }
                            List<BigDecimal> bigDecimals1 = BigDecimalFunctions.listDivideValue(zeroList, new BigDecimal(loadCityHisDOS4.size()));
                            curve96Load.setBigDecimals(bigDecimals1);
                        }
                        curve96Load.setDateStr(SUNDAY.getTypeName() + "（" + yearStr + "-" +  week + "周）" );
                        result.add(curve96Load);
                        break;
                    case MAX_LOAD_DAY:
                        // 最大负荷日
                        if (!CollectionUtils.isEmpty(loadFeatureCityDayHisDOS1)) {
                            bigDecimal = loadFeatureCityDayHisDOS1.stream()
                                .max(Comparator.comparing(LoadFeatureCityDayHisDO::getMaxLoad)).get().getMaxLoad();
                            for (LoadFeatureCityDayHisDO loadFeatureCityDayHisDO : loadFeatureCityDayHisDOS1) {
                                if (loadFeatureCityDayHisDO.getMaxLoad().equals(bigDecimal)) {
                                    date = loadFeatureCityDayHisDO.getDate();
                                }
                            }
                        }
                        if (MapUtils.isNotEmpty(collect)) {
                            List<LoadCityHisDO> loadCityHisDOS5 = collect.get(DateUtil.getStrDate(date, "yyyy-MM-dd"));
                            if (!CollectionUtils.isEmpty(loadCityHisDOS5)) {
                                bigDecimals = loadCityHisDOS5.get(0).getloadList();
                            }
                        }
                        curve96Load.setDateStr(MAX_LOAD_DAY.getTypeName() + "（" + DateUtil.getStrDate(date, "yyyy-MM-dd") + "）" );
                        curve96Load.setBigDecimals(bigDecimals);
                        result.add(curve96Load);
                        break;
                    case MIN_LOAD_DAY:
                        // 最小负荷日
                        if (!CollectionUtils.isEmpty(loadFeatureCityDayHisDOS1)) {
                            bigDecimal = loadFeatureCityDayHisDOS1.stream()
                                .min(Comparator.comparing(LoadFeatureCityDayHisDO::getMinLoad)).get().getMinLoad();
                            for (LoadFeatureCityDayHisDO loadFeatureCityDayHisDO : loadFeatureCityDayHisDOS1) {
                                if (loadFeatureCityDayHisDO.getMinLoad().equals(bigDecimal)) {
                                    date = loadFeatureCityDayHisDO.getDate();
                                }
                            }
                        }
                        if (MapUtils.isNotEmpty(collect)) {
                            List<LoadCityHisDO> loadCityHisDOS5 = collect.get(DateUtil.getStrDate(date, "yyyy-MM-dd"));
                            if (!CollectionUtils.isEmpty(loadCityHisDOS5)) {
                                bigDecimals = loadCityHisDOS5.get(0).getloadList();
                            }
                        }
                        curve96Load.setDateStr(MIN_LOAD_DAY.getTypeName() + "（" + DateUtil.getStrDate(date, "yyyy-MM-dd") + "）" );
                        curve96Load.setBigDecimals(bigDecimals);
                        result.add(curve96Load);
                        break;
                    case MAX_ENERGY_DAY:
                        // 最大电量日
                        if (!CollectionUtils.isEmpty(loadFeatureCityDayHisDOS1)) {
                            bigDecimal = loadFeatureCityDayHisDOS1.stream()
                                .max(Comparator.comparing(LoadFeatureCityDayHisDO::getEnergy)).get().getEnergy();
                            for (LoadFeatureCityDayHisDO loadFeatureCityDayHisDO : loadFeatureCityDayHisDOS1) {
                                if (loadFeatureCityDayHisDO.getEnergy().equals(bigDecimal)) {
                                    date = loadFeatureCityDayHisDO.getDate();
                                }
                            }
                        }
                        if (MapUtils.isNotEmpty(collect)) {
                            List<LoadCityHisDO> loadCityHisDOS5 = collect.get(DateUtil.getStrDate(date, "yyyy-MM-dd"));
                            if (!CollectionUtils.isEmpty(loadCityHisDOS5)) {
                                bigDecimals = loadCityHisDOS5.get(0).getloadList();
                            }
                        }
                        curve96Load.setDateStr(MAX_ENERGY_DAY.getTypeName() + "（" + DateUtil.getStrDate(date, "yyyy-MM-dd") + "）");
                        curve96Load.setBigDecimals(bigDecimals);
                        result.add(curve96Load);
                        break;
                    case MAX_PEAK_DAY:
                        // 最大峰谷差日
                        if (!CollectionUtils.isEmpty(loadFeatureCityDayHisDOS1)) {
                            bigDecimal = loadFeatureCityDayHisDOS1.stream()
                                .max(Comparator.comparing(LoadFeatureCityDayHisDO::getDifferent)).get().getDifferent();
                            for (LoadFeatureCityDayHisDO loadFeatureCityDayHisDO : loadFeatureCityDayHisDOS1) {
                                if (loadFeatureCityDayHisDO.getDifferent().equals(bigDecimal)) {
                                    date = loadFeatureCityDayHisDO.getDate();
                                }
                            }
                        }
                        if (MapUtils.isNotEmpty(collect)) {
                            List<LoadCityHisDO> loadCityHisDOS5 = collect.get(DateUtil.getStrDate(date, "yyyy-MM-dd"));
                            if (!CollectionUtils.isEmpty(loadCityHisDOS5)) {
                                bigDecimals = loadCityHisDOS5.get(0).getloadList();
                            }
                        }
                        curve96Load.setDateStr(MAX_PEAK_DAY.getTypeName() + "（" + DateUtil.getStrDate(date, "yyyy-MM-dd") + "）");
                        curve96Load.setBigDecimals(bigDecimals);
                        result.add(curve96Load);
                        break;
                    default:
                        break;
                }
            }
        }
        return result;
    }

    @Override
    public List<Curve96Load> findTenDaysCurveCompare(String cityId, String dateStr, Integer type) throws Exception {
        List<Curve96Load> result = new ArrayList<>();
        List<Date> list1 = new ArrayList<>();
        String year = dateStr.split("-")[0];
        String month = dateStr.split("-")[1];
        Date firstDay = DateUtil.getFirstDay(Integer.parseInt(year), Integer.parseInt(month));
        Date lastDay = DateUtil.getLastDay(Integer.parseInt(year), Integer.parseInt(month));
        // 包含节假日
        List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisVOS = loadFeatureCityDayHisService.findLoadFeatureCityDayHisVOS(
            cityId, firstDay, lastDay, getCaliberId(cityId));
        List<LoadCityHisDO> loadCityHisDOS = loadCityHisService.getLoadCityHisDOS(cityId, getCaliberId(cityId),
            firstDay, lastDay);
        List<Date> holiday = holidayService.findHoliday(firstDay, lastDay);
        list1.addAll(holiday);
        for (CurveLoadCompareEnum value : CurveLoadCompareEnum.values()) {
            if (value.equals(REST_DAY) || !value.getSort().equals(type)) {
                continue;
            }
            for (TenDaysTypeEnum tenDaysTypeEnum : TenDaysTypeEnum.values()) {
                // 不包含节假日（工作日）
                List<LoadCityHisDO> loadCityHisDOS2 = new ArrayList<>();
                // 不包含节假日（周六）
                List<LoadCityHisDO> loadCityHisDOS3 = new ArrayList<>();
                // 不包含节假日（周日）
                List<LoadCityHisDO> loadCityHisDOS4 = new ArrayList<>();
                Calendar cal = Calendar.getInstance();
                BigDecimal bigDecimal = null;
                Date date = null;
                List<BigDecimal> bigDecimals = new ArrayList<>();
                List<LoadCityHisDO> loadCityHis = new ArrayList<>();
                List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisDOS1 = new ArrayList<>();
                Curve96Load curve96Load = new Curve96Load();
                curve96Load.setDateStr(dateStr + tenDaysTypeEnum.getName());
                if (!CollectionUtils.isEmpty(loadCityHisDOS) && !CollectionUtils.isEmpty(loadFeatureCityDayHisVOS)) {
                    if (tenDaysTypeEnum.getName().equals("上旬")) {
                        for (int i = 0; i < 10 && i < loadCityHisDOS.size() && i < loadFeatureCityDayHisVOS.size(); i++) {
                            loadCityHis.add(loadCityHisDOS.get(i));
                            loadFeatureCityDayHisDOS1.add(loadFeatureCityDayHisVOS.get(i));
                        }
                    } else if (tenDaysTypeEnum.getName().equals("中旬")) {
                        for (int i = 10; i < 20 && i < loadCityHisDOS.size() && i < loadFeatureCityDayHisVOS.size(); i++) {
                            loadCityHis.add(loadCityHisDOS.get(i));
                            loadFeatureCityDayHisDOS1.add(loadFeatureCityDayHisVOS.get(i));
                        }
                    } else {
                        for (int i = 20; i < loadCityHisDOS.size() && i < loadFeatureCityDayHisVOS.size(); i++) {
                            loadCityHis.add(loadCityHisDOS.get(i));
                            loadFeatureCityDayHisDOS1.add(loadFeatureCityDayHisVOS.get(i));
                        }
                    }
                }

                List<LoadCityHisDO> loadCityHisDOS1 = new ArrayList<>();

                if (!CollectionUtils.isEmpty(loadCityHis)) {
                    for (LoadCityHisDO loadCityHisDO : loadCityHis) {
                        if (!list1.contains(loadCityHisDO.getDate())) {
                            loadCityHisDOS1.add(loadCityHisDO);
                        }
                    }
                }

                Map<String, List<LoadCityHisDO>> collect = loadCityHis.stream()
                    .collect(Collectors.groupingBy(t -> DateUtil.getStrDate(t.getDate(), "yyyy-MM-dd")));

                switch (value) {
                    case WORKING_DAY:
                        // 工作日
                        for (LoadCityHisDO loadCityHisDO : loadCityHisDOS1) {
                            if (com.tsintergy.lf.core.util.DateUtil.isWorkingDay(
                                loadCityHisDO.getDate().getTime())) {
                                loadCityHisDOS2.add(loadCityHisDO);
                            }
                        }
                        if (!CollectionUtils.isEmpty(loadCityHisDOS2)) {
                            List<BigDecimal> zeroList = ColumnUtil.getZeroOrNullList(96, BigDecimal.ZERO);
                            for (LoadCityHisDO loadCityHisDO : loadCityHisDOS2) {
                                zeroList = BigDecimalFunctions.listAdd(zeroList, loadCityHisDO.getloadList());
                            }
                            List<BigDecimal> bigDecimals1 = BigDecimalFunctions.listDivideValue(zeroList, new BigDecimal(loadCityHisDOS2.size()));
                            curve96Load.setBigDecimals(bigDecimals1);
                        }
                        result.add(curve96Load);
                        break;
                    case SATURDAY:
                        // 周六
                        for (LoadCityHisDO loadCityHisDO : loadCityHisDOS1) {
                            cal.setTime(loadCityHisDO.getDate());
                            if (cal.get(Calendar.DAY_OF_WEEK) == Calendar.SATURDAY) {
                                loadCityHisDOS3.add(loadCityHisDO);
                            }
                        }
                        if (!CollectionUtils.isEmpty(loadCityHisDOS3)) {
                            List<BigDecimal> zeroList = ColumnUtil.getZeroOrNullList(96, BigDecimal.ZERO);
                            for (LoadCityHisDO loadCityHisDO : loadCityHisDOS3) {
                                zeroList = BigDecimalFunctions.listAdd(zeroList, loadCityHisDO.getloadList());
                            }
                            List<BigDecimal> bigDecimals1 = BigDecimalFunctions.listDivideValue(zeroList, new BigDecimal(loadCityHisDOS3.size()));
                            curve96Load.setBigDecimals(bigDecimals1);
                        }
                        result.add(curve96Load);
                        break;
                    case SUNDAY:
                        // 周日
                        for (LoadCityHisDO loadCityHisDO : loadCityHisDOS1) {
                            cal.setTime(loadCityHisDO.getDate());
                            if (cal.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY) {
                                loadCityHisDOS4.add(loadCityHisDO);
                            }
                        }
                        if (!CollectionUtils.isEmpty(loadCityHisDOS4)) {
                            List<BigDecimal> zeroList = ColumnUtil.getZeroOrNullList(96, BigDecimal.ZERO);
                            for (LoadCityHisDO loadCityHisDO : loadCityHisDOS4) {
                                zeroList = BigDecimalFunctions.listAdd(zeroList, loadCityHisDO.getloadList());
                            }
                            List<BigDecimal> bigDecimals1 = BigDecimalFunctions.listDivideValue(zeroList, new BigDecimal(loadCityHisDOS4.size()));
                            curve96Load.setBigDecimals(bigDecimals1);
                        }
                        result.add(curve96Load);
                        break;
                    case MAX_LOAD_DAY:
                        // 最大负荷日
                        if (!CollectionUtils.isEmpty(loadFeatureCityDayHisDOS1)) {
                            bigDecimal = loadFeatureCityDayHisDOS1.stream()
                                .max(Comparator.comparing(LoadFeatureCityDayHisDO::getMaxLoad)).get().getMaxLoad();
                            for (LoadFeatureCityDayHisDO loadFeatureCityDayHisDO : loadFeatureCityDayHisDOS1) {
                                if (loadFeatureCityDayHisDO.getMaxLoad().equals(bigDecimal)) {
                                    date = loadFeatureCityDayHisDO.getDate();
                                }
                            }
                        }
                        if (MapUtils.isNotEmpty(collect)) {
                            List<LoadCityHisDO> loadCityHisDOS5 = collect.get(DateUtil.getStrDate(date, "yyyy-MM-dd"));
                            if (!CollectionUtils.isEmpty(loadCityHisDOS5)) {
                                bigDecimals = loadCityHisDOS5.get(0).getloadList();
                            }
                        }
                        curve96Load.setDateStr(MAX_LOAD_DAY.getTypeName() + "（" + DateUtil.getStrDate(date, "yyyy-MM-dd") + "）" );
                        curve96Load.setBigDecimals(bigDecimals);
                        result.add(curve96Load);
                        break;
                    case MIN_LOAD_DAY:
                        // 最小负荷日
                        if (!CollectionUtils.isEmpty(loadFeatureCityDayHisDOS1)) {
                            bigDecimal = loadFeatureCityDayHisDOS1.stream()
                                .min(Comparator.comparing(LoadFeatureCityDayHisDO::getMinLoad)).get().getMinLoad();
                            for (LoadFeatureCityDayHisDO loadFeatureCityDayHisDO : loadFeatureCityDayHisDOS1) {
                                if (loadFeatureCityDayHisDO.getMinLoad().equals(bigDecimal)) {
                                    date = loadFeatureCityDayHisDO.getDate();
                                }
                            }
                        }
                        if (MapUtils.isNotEmpty(collect)) {
                            List<LoadCityHisDO> loadCityHisDOS5 = collect.get(DateUtil.getStrDate(date, "yyyy-MM-dd"));
                            if (!CollectionUtils.isEmpty(loadCityHisDOS5)) {
                                bigDecimals = loadCityHisDOS5.get(0).getloadList();
                            }
                        }
                        curve96Load.setDateStr(MIN_LOAD_DAY.getTypeName() + "（" + DateUtil.getStrDate(date, "yyyy-MM-dd") + "）" );
                        curve96Load.setBigDecimals(bigDecimals);
                        result.add(curve96Load);
                        break;
                    case MAX_ENERGY_DAY:
                        // 最大电量日
                        if (!CollectionUtils.isEmpty(loadFeatureCityDayHisDOS1)) {
                            bigDecimal = loadFeatureCityDayHisDOS1.stream()
                                .max(Comparator.comparing(LoadFeatureCityDayHisDO::getEnergy)).get().getEnergy();
                            for (LoadFeatureCityDayHisDO loadFeatureCityDayHisDO : loadFeatureCityDayHisDOS1) {
                                if (loadFeatureCityDayHisDO.getEnergy().equals(bigDecimal)) {
                                    date = loadFeatureCityDayHisDO.getDate();
                                }
                            }
                        }
                        if (MapUtils.isNotEmpty(collect)) {
                            List<LoadCityHisDO> loadCityHisDOS5 = collect.get(DateUtil.getStrDate(date, "yyyy-MM-dd"));
                            if (!CollectionUtils.isEmpty(loadCityHisDOS5)) {
                                bigDecimals = loadCityHisDOS5.get(0).getloadList();
                            }
                        }
                        curve96Load.setDateStr(MAX_ENERGY_DAY.getTypeName() + "（" + DateUtil.getStrDate(date, "yyyy-MM-dd") + "）");
                        curve96Load.setBigDecimals(bigDecimals);
                        result.add(curve96Load);
                        break;
                    case MAX_PEAK_DAY:
                        // 最大峰谷差日
                        if (!CollectionUtils.isEmpty(loadFeatureCityDayHisDOS1)) {
                            bigDecimal = loadFeatureCityDayHisDOS1.stream()
                                .max(Comparator.comparing(LoadFeatureCityDayHisDO::getDifferent)).get().getDifferent();
                            for (LoadFeatureCityDayHisDO loadFeatureCityDayHisDO : loadFeatureCityDayHisDOS1) {
                                if (loadFeatureCityDayHisDO.getDifferent().equals(bigDecimal)) {
                                    date = loadFeatureCityDayHisDO.getDate();
                                }
                            }
                        }
                        if (MapUtils.isNotEmpty(collect)) {
                            List<LoadCityHisDO> loadCityHisDOS5 = collect.get(DateUtil.getStrDate(date, "yyyy-MM-dd"));
                            if (!CollectionUtils.isEmpty(loadCityHisDOS5)) {
                                bigDecimals = loadCityHisDOS5.get(0).getloadList();
                            }
                        }
                        curve96Load.setDateStr(MAX_PEAK_DAY.getTypeName() + "（" + DateUtil.getStrDate(date, "yyyy-MM-dd") + "）");
                        curve96Load.setBigDecimals(bigDecimals);
                        result.add(curve96Load);
                        break;
                    default:
                        break;
                }
            }
        }
        return result;
    }

    @Override
    public List<Curve96Load> findTenDaysYearCurveCompare(String cityId, String dateStr, Integer type,
        Integer tenDaysType) throws Exception {
        List<Curve96Load> result = new ArrayList<>();
        List<Date> list = new ArrayList<>();
        List<Date> list1 = new ArrayList<>();
        List<String> list2 = new ArrayList<>();
        String year = dateStr.split("-")[0];
        String month = dateStr.split("-")[1];
        Integer integer = Integer.valueOf(year);
        for (Integer i = 0; i < Integer.valueOf(year); integer--) {
            if (integer == 2017) {
                break;
            }
            list2.add(String.valueOf(integer));
            Date firstDay = DateUtil.getFirstDay(integer, Integer.parseInt(month));
            Date lastDay = DateUtil.getLastDay(integer, Integer.parseInt(month));
            List<Date> listBetweenDay = DateUtil.getListBetweenDay(firstDay, lastDay);
            List<Date> holiday = holidayService.findHoliday(firstDay, lastDay);
            list.addAll(listBetweenDay);
            list1.addAll(holiday);
        }
        List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisDOS = loadFeatureCityDayHisService.listLoadFeature(cityId,
            getCaliberId(cityId), list);
        Map<String, List<LoadFeatureCityDayHisDO>> collect = loadFeatureCityDayHisDOS.stream()
            .collect(Collectors.groupingBy(t -> DateUtil.getStrDate(t.getDate(), "yyyy-MM-dd").substring(0, 4)));
        List<LoadCityHisDO> loadCityDOsByCityIdInDates = loadCityHisService.findLoadCityDOsByCityIdInDates(cityId, list,
            getCaliberId(cityId));
        Map<String, List<LoadCityHisDO>> collect1 = loadCityDOsByCityIdInDates.stream()
            .collect(Collectors.groupingBy(t -> DateUtil.getStrDate(t.getDate(), "yyyy-MM-dd").substring(0, 4)));
        for (String yearStr : list2) {
            List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisDOSS = collect.get(yearStr);
            List<LoadCityHisDO> loadCityHisDOS = collect1.get(yearStr);

            List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisVOS = new ArrayList<>();
            // 排除节假日
            List<LoadCityHisDO> loadCityHisDOS1 = new ArrayList<>();
            // 工作日
            List<LoadCityHisDO> loadCityHisDOS2 = new ArrayList<>();
            // 周六
            List<LoadCityHisDO> loadCityHisDOS3 = new ArrayList<>();
            // 周日
            List<LoadCityHisDO> loadCityHisDOS4 = new ArrayList<>();
            for (CurveLoadCompareEnum value : CurveLoadCompareEnum.values()) {
                if (value.equals(REST_DAY) || !value.getSort().equals(type) ) {
                    continue;
                }
                for (TenDaysTypeEnum tenDaysTypeEnum : TenDaysTypeEnum.values()) {
                    if (!Integer.valueOf(tenDaysTypeEnum.getType()).equals(tenDaysType)) {
                        continue;
                    }
                    Calendar cal = Calendar.getInstance();
                    BigDecimal bigDecimal = null;
                    Date date = null;
                    List<BigDecimal> bigDecimals = new ArrayList<>();
                    List<LoadCityHisDO> loadCityHis = new ArrayList<>();
                    List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisDOS1 = new ArrayList<>();
                    Curve96Load curve96Load = new Curve96Load();
                    curve96Load.setDateStr(dateStr + tenDaysTypeEnum.getName());
                    if (!CollectionUtils.isEmpty(loadCityHisDOS) && !CollectionUtils.isEmpty(loadFeatureCityDayHisDOSS)) {
                        if (tenDaysTypeEnum.getName().equals("上旬")) {
                            for (int i = 0; i < 10 && i < loadCityHisDOS.size() && i < loadFeatureCityDayHisDOSS.size(); i++) {
                                loadCityHis.add(loadCityHisDOS.get(i));
                                loadFeatureCityDayHisDOS1.add(loadFeatureCityDayHisDOSS.get(i));
                            }
                        } else if (tenDaysTypeEnum.getName().equals("中旬")) {
                            for (int i = 10; i < 20 && i < loadCityHisDOS.size() && i < loadFeatureCityDayHisDOSS.size(); i++) {
                                loadCityHis.add(loadCityHisDOS.get(i));
                                loadFeatureCityDayHisDOS1.add(loadFeatureCityDayHisDOSS.get(i));
                            }
                        } else {
                            for (int i = 20; i < loadCityHisDOS.size() && i < loadFeatureCityDayHisDOSS.size(); i++) {
                                loadCityHis.add(loadCityHisDOS.get(i));
                                loadFeatureCityDayHisDOS1.add(loadFeatureCityDayHisDOSS.get(i));
                            }
                        }
                    }

                    if (!CollectionUtils.isEmpty(loadCityHis)) {
                        for (LoadCityHisDO loadCityHisDO : loadCityHis) {
                            if (!list1.contains(loadCityHisDO.getDate())) {
                                loadCityHisDOS1.add(loadCityHisDO);
                            }
                        }
                    }
                    if (!CollectionUtils.isEmpty(loadFeatureCityDayHisDOS1)) {
                        for (LoadFeatureCityDayHisDO featureCityDayHisDOSS : loadFeatureCityDayHisDOS1) {
                            if (!list1.contains(featureCityDayHisDOSS.getDate())) {
                                loadFeatureCityDayHisVOS.add(featureCityDayHisDOSS);
                            }
                        }
                    }
                    Map<String, List<LoadCityHisDO>> collectF = new HashMap<>();
                    if (!CollectionUtils.isEmpty(loadCityHisDOS)) {
                        collectF = loadCityHis.stream()
                            .collect(Collectors.groupingBy(t -> DateUtil.getStrDate(t.getDate(), "yyyy-MM-dd")));
                    }
                    switch (value) {
                        case WORKING_DAY:
                            // 工作日
                            for (LoadCityHisDO loadCityHisDO : loadCityHisDOS1) {
                                if (com.tsintergy.lf.core.util.DateUtil.isWorkingDay(
                                    loadCityHisDO.getDate().getTime())) {
                                    loadCityHisDOS2.add(loadCityHisDO);
                                }
                            }
                            if (!CollectionUtils.isEmpty(loadCityHisDOS2)) {
                                List<BigDecimal> zeroList = ColumnUtil.getZeroOrNullList(96, BigDecimal.ZERO);
                                for (LoadCityHisDO loadCityHisDO : loadCityHisDOS2) {
                                    zeroList = BigDecimalFunctions.listAdd(zeroList, loadCityHisDO.getloadList());
                                }
                                List<BigDecimal> bigDecimals1 = BigDecimalFunctions.listDivideValue(zeroList, new BigDecimal(loadCityHisDOS2.size()));
                                curve96Load.setBigDecimals(bigDecimals1);
                            }
                            curve96Load.setDateStr(yearStr + "-" + month + tenDaysTypeEnum.getName());
                            result.add(curve96Load);
                            break;
                        case SATURDAY:
                            // 周六
                            for (LoadCityHisDO loadCityHisDO : loadCityHisDOS1) {
                                cal.setTime(loadCityHisDO.getDate());
                                if (cal.get(Calendar.DAY_OF_WEEK) == Calendar.SATURDAY) {
                                    loadCityHisDOS3.add(loadCityHisDO);
                                }
                            }
                            if (!CollectionUtils.isEmpty(loadCityHisDOS3)) {
                                List<BigDecimal> zeroList = ColumnUtil.getZeroOrNullList(96, BigDecimal.ZERO);
                                for (LoadCityHisDO loadCityHisDO : loadCityHisDOS3) {
                                    zeroList = BigDecimalFunctions.listAdd(zeroList, loadCityHisDO.getloadList());
                                }
                                List<BigDecimal> bigDecimals1 = BigDecimalFunctions.listDivideValue(zeroList, new BigDecimal(loadCityHisDOS3.size()));
                                curve96Load.setBigDecimals(bigDecimals1);
                            }
                            curve96Load.setDateStr(yearStr + "-" + month + tenDaysTypeEnum.getName());
                            result.add(curve96Load);
                            break;
                        case SUNDAY:
                            // 周日
                            for (LoadCityHisDO loadCityHisDO : loadCityHisDOS1) {
                                cal.setTime(loadCityHisDO.getDate());
                                if (cal.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY) {
                                    loadCityHisDOS4.add(loadCityHisDO);
                                }
                            }
                            if (!CollectionUtils.isEmpty(loadCityHisDOS4)) {
                                List<BigDecimal> zeroList = ColumnUtil.getZeroOrNullList(96, BigDecimal.ZERO);
                                for (LoadCityHisDO loadCityHisDO : loadCityHisDOS4) {
                                    zeroList = BigDecimalFunctions.listAdd(zeroList, loadCityHisDO.getloadList());
                                }
                                List<BigDecimal> bigDecimals1 = BigDecimalFunctions.listDivideValue(zeroList, new BigDecimal(loadCityHisDOS4.size()));
                                curve96Load.setBigDecimals(bigDecimals1);
                            }
                            curve96Load.setDateStr(yearStr + "-" + month + tenDaysTypeEnum.getName());
                            result.add(curve96Load);
                            break;
                        case MAX_LOAD_DAY:
                            // 最大负荷日
                            if (!CollectionUtils.isEmpty(loadFeatureCityDayHisDOS1)) {
                                bigDecimal = loadFeatureCityDayHisDOS1.stream()
                                    .max(Comparator.comparing(LoadFeatureCityDayHisDO::getMaxLoad)).get().getMaxLoad();
                                for (LoadFeatureCityDayHisDO loadFeatureCityDayHisDO : loadFeatureCityDayHisDOS1) {
                                    if (loadFeatureCityDayHisDO.getMaxLoad().equals(bigDecimal)) {
                                        date = loadFeatureCityDayHisDO.getDate();
                                    }
                                }
                            }
                            if (MapUtils.isNotEmpty(collectF)) {
                                List<LoadCityHisDO> loadCityHisDOS5 = collectF.get(DateUtil.getStrDate(date, "yyyy-MM-dd"));
                                if (!CollectionUtils.isEmpty(loadCityHisDOS5)) {
                                    bigDecimals = loadCityHisDOS5.get(0).getloadList();
                                }
                            }
                            curve96Load.setDateStr(MAX_LOAD_DAY.getTypeName() + "（" + DateUtil.getStrDate(date, "yyyy-MM-dd") + "）" );
                            curve96Load.setBigDecimals(bigDecimals);
                            result.add(curve96Load);
                            break;
                        case MIN_LOAD_DAY:
                            // 最小负荷日
                            if (!CollectionUtils.isEmpty(loadFeatureCityDayHisDOS1)) {
                                bigDecimal = loadFeatureCityDayHisDOS1.stream()
                                    .min(Comparator.comparing(LoadFeatureCityDayHisDO::getMinLoad)).get().getMinLoad();
                                for (LoadFeatureCityDayHisDO loadFeatureCityDayHisDO : loadFeatureCityDayHisDOS1) {
                                    if (loadFeatureCityDayHisDO.getMinLoad().equals(bigDecimal)) {
                                        date = loadFeatureCityDayHisDO.getDate();
                                    }
                                }
                            }
                            if (MapUtils.isNotEmpty(collectF)) {
                                List<LoadCityHisDO> loadCityHisDOS5 = collectF.get(DateUtil.getStrDate(date, "yyyy-MM-dd"));
                                if (!CollectionUtils.isEmpty(loadCityHisDOS5)) {
                                    bigDecimals = loadCityHisDOS5.get(0).getloadList();
                                }
                            }
                            curve96Load.setDateStr(MIN_LOAD_DAY.getTypeName() + "（" + DateUtil.getStrDate(date, "yyyy-MM-dd") + "）" );
                            curve96Load.setBigDecimals(bigDecimals);
                            result.add(curve96Load);
                            break;
                        case MAX_ENERGY_DAY:
                            // 最大电量日
                            if (!CollectionUtils.isEmpty(loadFeatureCityDayHisDOS1)) {
                                bigDecimal = loadFeatureCityDayHisDOS1.stream()
                                    .max(Comparator.comparing(LoadFeatureCityDayHisDO::getEnergy)).get().getEnergy();
                                for (LoadFeatureCityDayHisDO loadFeatureCityDayHisDO : loadFeatureCityDayHisDOS1) {
                                    if (loadFeatureCityDayHisDO.getEnergy().equals(bigDecimal)) {
                                        date = loadFeatureCityDayHisDO.getDate();
                                    }
                                }
                            }
                            if (MapUtils.isNotEmpty(collectF)) {
                                List<LoadCityHisDO> loadCityHisDOS5 = collectF.get(DateUtil.getStrDate(date, "yyyy-MM-dd"));
                                if (!CollectionUtils.isEmpty(loadCityHisDOS5)) {
                                    bigDecimals = loadCityHisDOS5.get(0).getloadList();
                                }
                            }
                            curve96Load.setDateStr(MAX_ENERGY_DAY.getTypeName() + "（" + DateUtil.getStrDate(date, "yyyy-MM-dd") + "）");
                            curve96Load.setBigDecimals(bigDecimals);
                            result.add(curve96Load);
                            break;
                        case MAX_PEAK_DAY:
                            // 最大峰谷差日
                            if (!CollectionUtils.isEmpty(loadFeatureCityDayHisDOS1)) {
                                bigDecimal = loadFeatureCityDayHisDOS1.stream()
                                    .max(Comparator.comparing(LoadFeatureCityDayHisDO::getDifferent)).get().getDifferent();
                                for (LoadFeatureCityDayHisDO loadFeatureCityDayHisDO : loadFeatureCityDayHisDOS1) {
                                    if (loadFeatureCityDayHisDO.getDifferent().equals(bigDecimal)) {
                                        date = loadFeatureCityDayHisDO.getDate();
                                    }
                                }
                            }
                            if (MapUtils.isNotEmpty(collectF)) {
                                List<LoadCityHisDO> loadCityHisDOS5 = collectF.get(DateUtil.getStrDate(date, "yyyy-MM-dd"));
                                if (!CollectionUtils.isEmpty(loadCityHisDOS5)) {
                                    bigDecimals = loadCityHisDOS5.get(0).getloadList();
                                }
                            }
                            curve96Load.setDateStr(MAX_PEAK_DAY.getTypeName() + "（" + DateUtil.getStrDate(date, "yyyy-MM-dd") + "）");
                            curve96Load.setBigDecimals(bigDecimals);
                            result.add(curve96Load);
                            break;
                        default:
                            break;
                    }
                }
            }
        }
        return result;
    }

    @Override
    public List<Curve96Load> findMonthCurveCompare(String cityId, String dateStart, String dateEnd, Integer type)
        throws Exception {
        List<Curve96Load> result = new ArrayList<>();
        List<String> targetYearList = com.tsintergy.lf.core.util.DateUtil.getTargetYearList(dateStart, dateEnd);
        List<Date> list1 = new ArrayList<>();
        List<Date> list = new ArrayList<>();
        for (String yearMonth : targetYearList) {
            String year = yearMonth.split("-")[0];
            String month = yearMonth.split("-")[1];
            String firstDayOfMonth = DateUtil.getFirstDayOfMonth(year, month);
            String lastDayOfMonth = DateUtil.getLastDayOfMonth(year, month);
            Date yearFirst = DateUtil.getDateFromString(firstDayOfMonth, "yyyy-MM-dd");
            Date yearLast = DateUtil.getDateFromString(lastDayOfMonth, "yyyy-MM-dd");
            List<Date> listBetweenDay = DateUtil.getListBetweenDay(yearFirst, yearLast);
            List<Date> holiday = holidayService.findHoliday(yearFirst, yearLast);
            list.addAll(listBetweenDay);
            list1.addAll(holiday);
        }
        List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisDOS = loadFeatureCityDayHisService.listLoadFeature(cityId,
            getCaliberId(cityId), list);
        Map<String, List<LoadFeatureCityDayHisDO>> collect = loadFeatureCityDayHisDOS.stream()
            .collect(Collectors.groupingBy(t -> DateUtil.getStrDate(t.getDate(), "yyyy-MM-dd").substring(0, 7)));
        List<LoadCityHisDO> loadCityDOsByCityIdInDates = loadCityHisService.findLoadCityDOsByCityIdInDates(cityId, list,
            getCaliberId(cityId));
        Map<String, List<LoadCityHisDO>> collect1 = loadCityDOsByCityIdInDates.stream()
            .collect(Collectors.groupingBy(t -> DateUtil.getStrDate(t.getDate(), "yyyy-MM-dd").substring(0, 7)));
        for (String yearMonth : targetYearList) {
            // 不区分
            List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisDOS1 = collect.get(yearMonth);
            List<LoadCityHisDO> loadCityHisDOS = collect1.get(yearMonth);

            for (CurveLoadCompareEnum value : CurveLoadCompareEnum.values()) {
                if (value.equals(REST_DAY) || !value.getSort().equals(type)) {
                    continue;
                }
                List<BigDecimal> bigDecimals = new ArrayList<>();
                Curve96Load curve96Load = new Curve96Load();
                Calendar cal = Calendar.getInstance();
                BigDecimal bigDecimal = null;
                Date date = null;
                // 工作日
                List<LoadCityHisDO> loadCityHisDOS2 = new ArrayList<>();
                // 周六
                List<LoadCityHisDO> loadCityHisDOS3 = new ArrayList<>();
                // 周日
                List<LoadCityHisDO> loadCityHisDOS4 = new ArrayList<>();
                // 排除节假日
                List<LoadCityHisDO> loadCityHisDOS1 = new ArrayList<>();
                if (!CollectionUtils.isEmpty(loadCityHisDOS)) {
                    for (LoadCityHisDO loadCityHisDO : loadCityHisDOS) {
                        if (!list1.contains(loadCityHisDO.getDate())) {
                            loadCityHisDOS1.add(loadCityHisDO);
                        }
                    }
                }

                Map<String, List<LoadCityHisDO>> collectF = new HashMap<>();
                if (!CollectionUtils.isEmpty(loadCityHisDOS)) {
                    collectF = loadCityHisDOS.stream()
                        .collect(Collectors.groupingBy(t -> DateUtil.getStrDate(t.getDate(), "yyyy-MM-dd")));
                }
                switch (value) {
                    case WORKING_DAY:
                        // 工作日
                        for (LoadCityHisDO loadCityHisDO : loadCityHisDOS1) {
                            if (com.tsintergy.lf.core.util.DateUtil.isWorkingDay(
                                loadCityHisDO.getDate().getTime())) {
                                loadCityHisDOS2.add(loadCityHisDO);
                            }
                        }
                        if (!CollectionUtils.isEmpty(loadCityHisDOS2)) {
                            List<BigDecimal> zeroList = ColumnUtil.getZeroOrNullList(96, BigDecimal.ZERO);
                            for (LoadCityHisDO loadCityHisDO : loadCityHisDOS2) {
                                zeroList = BigDecimalFunctions.listAdd(zeroList, loadCityHisDO.getloadList());
                            }
                            List<BigDecimal> bigDecimals1 = BigDecimalFunctions.listDivideValue(zeroList, new BigDecimal(loadCityHisDOS2.size()));
                            curve96Load.setBigDecimals(bigDecimals1);
                        }
                        curve96Load.setDateStr(yearMonth);
                        result.add(curve96Load);
                        break;
                    case SATURDAY:
                        // 周六
                        for (LoadCityHisDO loadCityHisDO : loadCityHisDOS1) {
                            cal.setTime(loadCityHisDO.getDate());
                            if (cal.get(Calendar.DAY_OF_WEEK) == Calendar.SATURDAY) {
                                loadCityHisDOS3.add(loadCityHisDO);
                            }
                        }
                        if (!CollectionUtils.isEmpty(loadCityHisDOS3)) {
                            List<BigDecimal> zeroList = ColumnUtil.getZeroOrNullList(96, BigDecimal.ZERO);
                            for (LoadCityHisDO loadCityHisDO : loadCityHisDOS3) {
                                zeroList = BigDecimalFunctions.listAdd(zeroList, loadCityHisDO.getloadList());
                            }
                            List<BigDecimal> bigDecimals1 = BigDecimalFunctions.listDivideValue(zeroList, new BigDecimal(loadCityHisDOS3.size()));
                            curve96Load.setBigDecimals(bigDecimals1);
                        }
                        curve96Load.setDateStr(yearMonth);
                        result.add(curve96Load);
                        break;
                    case SUNDAY:
                        // 周日
                        for (LoadCityHisDO loadCityHisDO : loadCityHisDOS1) {
                            cal.setTime(loadCityHisDO.getDate());
                            if (cal.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY) {
                                loadCityHisDOS4.add(loadCityHisDO);
                            }
                        }
                        if (!CollectionUtils.isEmpty(loadCityHisDOS4)) {
                            List<BigDecimal> zeroList = ColumnUtil.getZeroOrNullList(96, BigDecimal.ZERO);
                            for (LoadCityHisDO loadCityHisDO : loadCityHisDOS4) {
                                zeroList = BigDecimalFunctions.listAdd(zeroList, loadCityHisDO.getloadList());
                            }
                            List<BigDecimal> bigDecimals1 = BigDecimalFunctions.listDivideValue(zeroList, new BigDecimal(loadCityHisDOS4.size()));
                            curve96Load.setBigDecimals(bigDecimals1);
                        }
                        curve96Load.setDateStr(yearMonth);
                        result.add(curve96Load);
                        break;
                    case MAX_LOAD_DAY:
                        // 最大负荷日
                        if (!CollectionUtils.isEmpty(loadFeatureCityDayHisDOS1)) {
                            bigDecimal = loadFeatureCityDayHisDOS1.stream()
                                .max(Comparator.comparing(LoadFeatureCityDayHisDO::getMaxLoad)).get().getMaxLoad();
                            for (LoadFeatureCityDayHisDO loadFeatureCityDayHisDO : loadFeatureCityDayHisDOS1) {
                                if (loadFeatureCityDayHisDO.getMaxLoad().equals(bigDecimal)) {
                                    date = loadFeatureCityDayHisDO.getDate();
                                }
                            }
                        }
                        if (MapUtils.isNotEmpty(collectF)) {
                            List<LoadCityHisDO> loadCityHisDOS5 = collectF.get(DateUtil.getStrDate(date, "yyyy-MM-dd"));
                            if (!CollectionUtils.isEmpty(loadCityHisDOS5)) {
                                bigDecimals = loadCityHisDOS5.get(0).getloadList();
                            }
                        }
                        curve96Load.setDateStr(MAX_LOAD_DAY.getTypeName() + "（" + DateUtil.getStrDate(date, "yyyy-MM-dd") + "）" );
                        curve96Load.setBigDecimals(bigDecimals);
                        result.add(curve96Load);
                        break;
                    case MIN_LOAD_DAY:
                        // 最小负荷日
                        if (!CollectionUtils.isEmpty(loadFeatureCityDayHisDOS1)) {
                            bigDecimal = loadFeatureCityDayHisDOS1.stream()
                                .min(Comparator.comparing(LoadFeatureCityDayHisDO::getMinLoad)).get().getMinLoad();
                            for (LoadFeatureCityDayHisDO loadFeatureCityDayHisDO : loadFeatureCityDayHisDOS1) {
                                if (loadFeatureCityDayHisDO.getMinLoad().equals(bigDecimal)) {
                                    date = loadFeatureCityDayHisDO.getDate();
                                }
                            }
                        }
                        if (MapUtils.isNotEmpty(collectF)) {
                            List<LoadCityHisDO> loadCityHisDOS5 = collectF.get(DateUtil.getStrDate(date, "yyyy-MM-dd"));
                            if (!CollectionUtils.isEmpty(loadCityHisDOS5)) {
                                bigDecimals = loadCityHisDOS5.get(0).getloadList();
                            }
                        }
                        curve96Load.setDateStr(MIN_LOAD_DAY.getTypeName() + "（" + DateUtil.getStrDate(date, "yyyy-MM-dd") + "）" );
                        curve96Load.setBigDecimals(bigDecimals);
                        result.add(curve96Load);
                        break;
                    case MAX_ENERGY_DAY:
                        // 最大电量日
                        if (!CollectionUtils.isEmpty(loadFeatureCityDayHisDOS1)) {
                            bigDecimal = loadFeatureCityDayHisDOS1.stream()
                                .max(Comparator.comparing(LoadFeatureCityDayHisDO::getEnergy)).get().getEnergy();
                            for (LoadFeatureCityDayHisDO loadFeatureCityDayHisDO : loadFeatureCityDayHisDOS1) {
                                if (loadFeatureCityDayHisDO.getEnergy().equals(bigDecimal)) {
                                    date = loadFeatureCityDayHisDO.getDate();
                                }
                            }
                        }
                        if (MapUtils.isNotEmpty(collectF)) {
                            List<LoadCityHisDO> loadCityHisDOS5 = collectF.get(DateUtil.getStrDate(date, "yyyy-MM-dd"));
                            if (!CollectionUtils.isEmpty(loadCityHisDOS5)) {
                                bigDecimals = loadCityHisDOS5.get(0).getloadList();
                            }
                        }
                        curve96Load.setDateStr(MAX_ENERGY_DAY.getTypeName() + "（" + DateUtil.getStrDate(date, "yyyy-MM-dd") + "）");
                        curve96Load.setBigDecimals(bigDecimals);
                        result.add(curve96Load);
                        break;
                    case MAX_PEAK_DAY:
                        // 最大峰谷差日
                        if (!CollectionUtils.isEmpty(loadFeatureCityDayHisDOS1)) {
                            bigDecimal = loadFeatureCityDayHisDOS1.stream()
                                .max(Comparator.comparing(LoadFeatureCityDayHisDO::getDifferent)).get().getDifferent();
                            for (LoadFeatureCityDayHisDO loadFeatureCityDayHisDO : loadFeatureCityDayHisDOS1) {
                                if (loadFeatureCityDayHisDO.getDifferent().equals(bigDecimal)) {
                                    date = loadFeatureCityDayHisDO.getDate();
                                }
                            }
                        }
                        if (MapUtils.isNotEmpty(collectF)) {
                            List<LoadCityHisDO> loadCityHisDOS5 = collectF.get(DateUtil.getStrDate(date, "yyyy-MM-dd"));
                            if (!CollectionUtils.isEmpty(loadCityHisDOS5)) {
                                bigDecimals = loadCityHisDOS5.get(0).getloadList();
                            }
                        }
                        curve96Load.setDateStr(MAX_PEAK_DAY.getTypeName() + "（" + DateUtil.getStrDate(date, "yyyy-MM-dd") + "）");
                        curve96Load.setBigDecimals(bigDecimals);
                        result.add(curve96Load);
                        break;
                    default:
                        break;
                }
            }
        }
        return result;
    }

    @Override
    public List<Curve96Load> findMonthYearCurveCompare(String cityId, String dateStr, Integer type) throws Exception {
        List<Curve96Load> result = new ArrayList<>();
        String year = dateStr.split("-")[0];
        String month = dateStr.split("-")[1];
        List<Date> list1 = new ArrayList<>();
        List<Date> list = new ArrayList<>();
        List<String> list2 = new ArrayList<>();
        Integer integer = Integer.valueOf(year);
        for (Integer i = 0; i < Integer.valueOf(year); integer--) {
            if (integer == 2017) {
                break;
            }
            list2.add(integer + "-" + month);
        }
        for (String yearMonth : list2) {
            String year1 = yearMonth.split("-")[0];
            String month1 = yearMonth.split("-")[1];
            String firstDayOfMonth = DateUtil.getFirstDayOfMonth(year1, month1);
            String lastDayOfMonth = DateUtil.getLastDayOfMonth(year1, month1);
            Date yearFirst = DateUtil.getDateFromString(firstDayOfMonth, "yyyy-MM-dd");
            Date yearLast = DateUtil.getDateFromString(lastDayOfMonth, "yyyy-MM-dd");
            List<Date> listBetweenDay = DateUtil.getListBetweenDay(yearFirst, yearLast);
            List<Date> holiday = holidayService.findHoliday(yearFirst, yearLast);
            list.addAll(listBetweenDay);
            list1.addAll(holiday);
        }
        List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisDOS = loadFeatureCityDayHisService.listLoadFeature(cityId,
            getCaliberId(cityId), list);
        Map<String, List<LoadFeatureCityDayHisDO>> collect = loadFeatureCityDayHisDOS.stream()
            .collect(Collectors.groupingBy(t -> DateUtil.getStrDate(t.getDate(), "yyyy-MM-dd").substring(0, 7)));
        List<LoadCityHisDO> loadCityDOsByCityIdInDates = loadCityHisService.findLoadCityDOsByCityIdInDates(cityId, list,
            getCaliberId(cityId));
        Map<String, List<LoadCityHisDO>> collect1 = loadCityDOsByCityIdInDates.stream()
            .collect(Collectors.groupingBy(t -> DateUtil.getStrDate(t.getDate(), "yyyy-MM-dd").substring(0, 7)));
        for (String yearMonth : list2) {
            // 不区分
            List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisDOS1 = collect.get(yearMonth);
            List<LoadCityHisDO> loadCityHisDOS = collect1.get(yearMonth);

            for (CurveLoadCompareEnum value : CurveLoadCompareEnum.values()) {
                if (value.equals(REST_DAY) || !value.getSort().equals(type)) {
                    continue;
                }
                List<BigDecimal> bigDecimals = new ArrayList<>();
                Curve96Load curve96Load = new Curve96Load();
                Calendar cal = Calendar.getInstance();
                BigDecimal bigDecimal = null;
                Date date = null;
                // 工作日
                List<LoadCityHisDO> loadCityHisDOS2 = new ArrayList<>();
                // 周六
                List<LoadCityHisDO> loadCityHisDOS3 = new ArrayList<>();
                // 周日
                List<LoadCityHisDO> loadCityHisDOS4 = new ArrayList<>();
                // 排除节假日
                List<LoadCityHisDO> loadCityHisDOS1 = new ArrayList<>();
                if (!CollectionUtils.isEmpty(loadCityHisDOS)) {
                    for (LoadCityHisDO loadCityHisDO : loadCityHisDOS) {
                        if (!list1.contains(loadCityHisDO.getDate())) {
                            loadCityHisDOS1.add(loadCityHisDO);
                        }
                    }
                }
                Map<String, List<LoadCityHisDO>> collectF = new HashMap<>();
                if (!CollectionUtils.isEmpty(loadCityHisDOS)) {
                    collectF = loadCityHisDOS.stream()
                        .collect(Collectors.groupingBy(t -> DateUtil.getStrDate(t.getDate(), "yyyy-MM-dd")));
                }
                switch (value) {
                    case WORKING_DAY:
                        // 工作日
                        for (LoadCityHisDO loadCityHisDO : loadCityHisDOS1) {
                            if (com.tsintergy.lf.core.util.DateUtil.isWorkingDay(
                                loadCityHisDO.getDate().getTime())) {
                                loadCityHisDOS2.add(loadCityHisDO);
                            }
                        }
                        if (!CollectionUtils.isEmpty(loadCityHisDOS2)) {
                            List<BigDecimal> zeroList = ColumnUtil.getZeroOrNullList(96, BigDecimal.ZERO);
                            for (LoadCityHisDO loadCityHisDO : loadCityHisDOS2) {
                                zeroList = BigDecimalFunctions.listAdd(zeroList, loadCityHisDO.getloadList());
                            }
                            List<BigDecimal> bigDecimals1 = BigDecimalFunctions.listDivideValue(zeroList, new BigDecimal(loadCityHisDOS2.size()));
                            curve96Load.setBigDecimals(bigDecimals1);
                        }
                        curve96Load.setDateStr(yearMonth);
                        result.add(curve96Load);
                        break;
                    case SATURDAY:
                        // 周六
                        for (LoadCityHisDO loadCityHisDO : loadCityHisDOS1) {
                            cal.setTime(loadCityHisDO.getDate());
                            if (cal.get(Calendar.DAY_OF_WEEK) == Calendar.SATURDAY) {
                                loadCityHisDOS3.add(loadCityHisDO);
                            }
                        }
                        if (!CollectionUtils.isEmpty(loadCityHisDOS3)) {
                            List<BigDecimal> zeroList = ColumnUtil.getZeroOrNullList(96, BigDecimal.ZERO);
                            for (LoadCityHisDO loadCityHisDO : loadCityHisDOS3) {
                                zeroList = BigDecimalFunctions.listAdd(zeroList, loadCityHisDO.getloadList());
                            }
                            List<BigDecimal> bigDecimals1 = BigDecimalFunctions.listDivideValue(zeroList, new BigDecimal(loadCityHisDOS3.size()));
                            curve96Load.setBigDecimals(bigDecimals1);
                        }
                        curve96Load.setDateStr(yearMonth);
                        result.add(curve96Load);
                        break;
                    case SUNDAY:
                        // 周日
                        for (LoadCityHisDO loadCityHisDO : loadCityHisDOS1) {
                            cal.setTime(loadCityHisDO.getDate());
                            if (cal.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY) {
                                loadCityHisDOS4.add(loadCityHisDO);
                            }
                        }
                        if (!CollectionUtils.isEmpty(loadCityHisDOS4)) {
                            List<BigDecimal> zeroList = ColumnUtil.getZeroOrNullList(96, BigDecimal.ZERO);
                            for (LoadCityHisDO loadCityHisDO : loadCityHisDOS4) {
                                zeroList = BigDecimalFunctions.listAdd(zeroList, loadCityHisDO.getloadList());
                            }
                            List<BigDecimal> bigDecimals1 = BigDecimalFunctions.listDivideValue(zeroList, new BigDecimal(loadCityHisDOS4.size()));
                            curve96Load.setBigDecimals(bigDecimals1);
                        }
                        curve96Load.setDateStr(yearMonth);
                        result.add(curve96Load);
                        break;
                    case MAX_LOAD_DAY:
                        // 最大负荷日
                        if (!CollectionUtils.isEmpty(loadFeatureCityDayHisDOS1)) {
                            bigDecimal = loadFeatureCityDayHisDOS1.stream()
                                .max(Comparator.comparing(LoadFeatureCityDayHisDO::getMaxLoad)).get().getMaxLoad();
                            for (LoadFeatureCityDayHisDO loadFeatureCityDayHisDO : loadFeatureCityDayHisDOS1) {
                                if (loadFeatureCityDayHisDO.getMaxLoad().equals(bigDecimal)) {
                                    date = loadFeatureCityDayHisDO.getDate();
                                }
                            }
                        }
                        if (MapUtils.isNotEmpty(collectF)) {
                            List<LoadCityHisDO> loadCityHisDOS5 = collectF.get(DateUtil.getStrDate(date, "yyyy-MM-dd"));
                            if (!CollectionUtils.isEmpty(loadCityHisDOS5)) {
                                bigDecimals = loadCityHisDOS5.get(0).getloadList();
                            }
                        }
                        curve96Load.setDateStr(DateUtil.getStrDate(date, "yyyy-MM-dd"));
                        curve96Load.setBigDecimals(bigDecimals);
                        result.add(curve96Load);
                        break;
                    case MIN_LOAD_DAY:
                        // 最小负荷日
                        if (!CollectionUtils.isEmpty(loadFeatureCityDayHisDOS1)) {
                            bigDecimal = loadFeatureCityDayHisDOS1.stream()
                                .min(Comparator.comparing(LoadFeatureCityDayHisDO::getMinLoad)).get().getMinLoad();
                            for (LoadFeatureCityDayHisDO loadFeatureCityDayHisDO : loadFeatureCityDayHisDOS1) {
                                if (loadFeatureCityDayHisDO.getMinLoad().equals(bigDecimal)) {
                                    date = loadFeatureCityDayHisDO.getDate();
                                }
                            }
                        }
                        if (MapUtils.isNotEmpty(collectF)) {
                            List<LoadCityHisDO> loadCityHisDOS5 = collectF.get(DateUtil.getStrDate(date, "yyyy-MM-dd"));
                            if (!CollectionUtils.isEmpty(loadCityHisDOS5)) {
                                bigDecimals = loadCityHisDOS5.get(0).getloadList();
                            }
                        }
                        curve96Load.setDateStr(DateUtil.getStrDate(date, "yyyy-MM-dd"));
                        curve96Load.setBigDecimals(bigDecimals);
                        result.add(curve96Load);
                        break;
                    case MAX_ENERGY_DAY:
                        // 最大电量日
                        if (!CollectionUtils.isEmpty(loadFeatureCityDayHisDOS1)) {
                            bigDecimal = loadFeatureCityDayHisDOS1.stream()
                                .max(Comparator.comparing(LoadFeatureCityDayHisDO::getEnergy)).get().getEnergy();
                            for (LoadFeatureCityDayHisDO loadFeatureCityDayHisDO : loadFeatureCityDayHisDOS1) {
                                if (loadFeatureCityDayHisDO.getEnergy().equals(bigDecimal)) {
                                    date = loadFeatureCityDayHisDO.getDate();
                                }
                            }
                        }
                        if (MapUtils.isNotEmpty(collectF)) {
                            List<LoadCityHisDO> loadCityHisDOS5 = collectF.get(DateUtil.getStrDate(date, "yyyy-MM-dd"));
                            if (!CollectionUtils.isEmpty(loadCityHisDOS5)) {
                                bigDecimals = loadCityHisDOS5.get(0).getloadList();
                            }
                        }
                        curve96Load.setDateStr(DateUtil.getStrDate(date, "yyyy-MM-dd"));
                        curve96Load.setBigDecimals(bigDecimals);
                        result.add(curve96Load);
                        break;
                    case MAX_PEAK_DAY:
                        // 最大峰谷差日
                        if (!CollectionUtils.isEmpty(loadFeatureCityDayHisDOS1)) {
                            bigDecimal = loadFeatureCityDayHisDOS1.stream()
                                .max(Comparator.comparing(LoadFeatureCityDayHisDO::getDifferent)).get().getDifferent();
                            for (LoadFeatureCityDayHisDO loadFeatureCityDayHisDO : loadFeatureCityDayHisDOS1) {
                                if (loadFeatureCityDayHisDO.getDifferent().equals(bigDecimal)) {
                                    date = loadFeatureCityDayHisDO.getDate();
                                }
                            }
                        }
                        if (MapUtils.isNotEmpty(collectF)) {
                            List<LoadCityHisDO> loadCityHisDOS5 = collectF.get(DateUtil.getStrDate(date, "yyyy-MM-dd"));
                            if (!CollectionUtils.isEmpty(loadCityHisDOS5)) {
                                bigDecimals = loadCityHisDOS5.get(0).getloadList();
                            }
                        }
                        curve96Load.setDateStr(DateUtil.getStrDate(date, "yyyy-MM-dd"));
                        curve96Load.setBigDecimals(bigDecimals);
                        result.add(curve96Load);
                        break;
                    default:
                        break;
                }
            }
        }
        return result;
    }

    @Override
    public List<Curve96Load> findSeasoCurveCompare(String cityId, String dateStr, Integer type) throws Exception {
        List<Curve96Load> result = new ArrayList<>();
        List<Date> list1 = new ArrayList<>();
        List<Date> list = new ArrayList<>();
        List<String> list3 = Arrays.asList("春季", "夏季", "秋季", "冬季");
        // 春季
        List<String> spring = Arrays.asList(dateStr + "-03", dateStr + "-04", dateStr + "-05");
        // 夏季
        List<String> summer = Arrays.asList(dateStr + "-06", dateStr + "-07", dateStr + "-08");
        // 秋节
        List<String> autumn = Arrays.asList(dateStr + "-09", dateStr + "-10", dateStr + "-11");
        // 冬季
        List<String> winter = Arrays.asList(dateStr + "-12", Integer.parseInt(dateStr) + 1 + "-01",
            Integer.parseInt(dateStr) + 1 + "-02");
        List<String> allList = new ArrayList<>();
        Map<String, List<String>> map = new HashMap<>();
        allList.addAll(spring);
        allList.addAll(summer);
        allList.addAll(autumn);
        allList.addAll(winter);
        map.put("春季", spring);
        map.put("冬季", winter);
        map.put("夏季", summer);
        map.put("秋季", autumn);
        for (String yearMonth : allList) {
            String year1 = yearMonth.split("-")[0];
            String month1 = yearMonth.split("-")[1];
            String firstDayOfMonth = DateUtil.getFirstDayOfMonth(year1, month1);
            String lastDayOfMonth = DateUtil.getLastDayOfMonth(year1, month1);
            Date yearFirst = DateUtil.getDateFromString(firstDayOfMonth, "yyyy-MM-dd");
            Date yearLast = DateUtil.getDateFromString(lastDayOfMonth, "yyyy-MM-dd");
            List<Date> listBetweenDay = DateUtil.getListBetweenDay(yearFirst, yearLast);
            List<Date> holiday = holidayService.findHoliday(yearFirst, yearLast);
            list.addAll(listBetweenDay);
            list1.addAll(holiday);
        }

        List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisDOS = loadFeatureCityDayHisService.listLoadFeature(cityId,
            getCaliberId(cityId), list);
        Map<String, List<LoadFeatureCityDayHisDO>> collect = loadFeatureCityDayHisDOS.stream()
            .collect(Collectors.groupingBy(t -> DateUtil.getStrDate(t.getDate(), "yyyy-MM-dd").substring(0, 7)));
        List<LoadCityHisDO> loadCityDOsByCityIdInDates = loadCityHisService.findLoadCityDOsByCityIdInDates(cityId, list,
            getCaliberId(cityId));
        Map<String, List<LoadCityHisDO>> collect1 = loadCityDOsByCityIdInDates.stream()
            .collect(Collectors.groupingBy(t -> DateUtil.getStrDate(t.getDate(), "yyyy-MM-dd").substring(0, 7)));

        for (String seasonName : list3) {
            List<String> list2 = map.get(seasonName);
            // 不区分
            List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisDOS1 = new ArrayList<>();
            List<LoadCityHisDO> loadCityHisDOS = new ArrayList<>();

            for (String yearMonth : list2) {
                List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisDOS2 = collect.get(yearMonth);
                List<LoadCityHisDO> loadCityHisDOS1 = collect1.get(yearMonth);
                if (!CollectionUtils.isEmpty(loadFeatureCityDayHisDOS2)) {
                    loadFeatureCityDayHisDOS1.addAll(loadFeatureCityDayHisDOS2);
                }
                if (!CollectionUtils.isEmpty(loadCityHisDOS1)) {
                    loadCityHisDOS.addAll(loadCityHisDOS1);
                }
            }

            for (CurveLoadCompareEnum value : CurveLoadCompareEnum.values()) {
                if (value.equals(SATURDAY) || value.equals(SUNDAY) || !value.getSort().equals(type)) {
                    continue;
                }

                List<BigDecimal> bigDecimals = new ArrayList<>();
                Curve96Load curve96Load = new Curve96Load();
                BigDecimal bigDecimal = null;
                Date date = null;
                // 工作日
                List<LoadCityHisDO> loadCityHisDOS2 = new ArrayList<>();
                // 休息日
                List<LoadCityHisDO> loadCityHisDOS3 = new ArrayList<>();
                // 排除节假日
                List<LoadCityHisDO> loadCityHisDOS1 = new ArrayList<>();
                if (!CollectionUtils.isEmpty(loadCityHisDOS)) {
                    for (LoadCityHisDO loadCityHisDO : loadCityHisDOS) {
                        if (!list1.contains(loadCityHisDO.getDate())) {
                            loadCityHisDOS1.add(loadCityHisDO);
                        }
                    }
                }

                Map<String, List<LoadCityHisDO>> collectF = new HashMap<>();
                if (!CollectionUtils.isEmpty(loadCityHisDOS)) {
                    collectF = loadCityHisDOS.stream()
                        .collect(Collectors.groupingBy(t -> DateUtil.getStrDate(t.getDate(), "yyyy-MM-dd")));
                }

                switch (value) {
                    case WORKING_DAY:
                        // 工作日
                        for (LoadCityHisDO loadCityHisDO : loadCityHisDOS1) {
                            if (com.tsintergy.lf.core.util.DateUtil.isWorkingDay(
                                loadCityHisDO.getDate().getTime())) {
                                loadCityHisDOS2.add(loadCityHisDO);
                            }
                        }
                        if (!CollectionUtils.isEmpty(loadCityHisDOS2)) {
                            List<BigDecimal> zeroList = ColumnUtil.getZeroOrNullList(96, BigDecimal.ZERO);
                            for (LoadCityHisDO loadCityHisDO : loadCityHisDOS2) {
                                zeroList = BigDecimalFunctions.listAdd(zeroList, loadCityHisDO.getloadList());
                            }
                            List<BigDecimal> bigDecimals1 = BigDecimalFunctions.listDivideValue(zeroList, new BigDecimal(loadCityHisDOS2.size()));
                            curve96Load.setBigDecimals(bigDecimals1);
                        }
                        curve96Load.setDateStr(dateStr + seasonName);
                        result.add(curve96Load);
                        break;
                    case REST_DAY:
                        // 休息日
                        for (LoadCityHisDO loadCityHisDO : loadCityHisDOS1) {
                            if (!com.tsintergy.lf.core.util.DateUtil.isWorkingDay(
                                loadCityHisDO.getDate().getTime())) {
                                loadCityHisDOS3.add(loadCityHisDO);
                            }
                        }
                        if (!CollectionUtils.isEmpty(loadCityHisDOS3)) {
                            List<BigDecimal> zeroList = ColumnUtil.getZeroOrNullList(96, BigDecimal.ZERO);
                            for (LoadCityHisDO loadCityHisDO : loadCityHisDOS3) {
                                zeroList = BigDecimalFunctions.listAdd(zeroList, loadCityHisDO.getloadList());
                            }
                            List<BigDecimal> bigDecimals1 = BigDecimalFunctions.listDivideValue(zeroList, new BigDecimal(loadCityHisDOS3.size()));
                            curve96Load.setBigDecimals(bigDecimals1);
                        }
                        curve96Load.setDateStr(dateStr + seasonName);
                        result.add(curve96Load);
                        break;
                    case MAX_LOAD_DAY:
                        // 最大负荷日
                        if (!CollectionUtils.isEmpty(loadFeatureCityDayHisDOS1)) {
                            bigDecimal = loadFeatureCityDayHisDOS1.stream()
                                .max(Comparator.comparing(LoadFeatureCityDayHisDO::getMaxLoad)).get().getMaxLoad();
                            for (LoadFeatureCityDayHisDO loadFeatureCityDayHisDO : loadFeatureCityDayHisDOS1) {
                                if (loadFeatureCityDayHisDO.getMaxLoad().equals(bigDecimal)) {
                                    date = loadFeatureCityDayHisDO.getDate();
                                }
                            }
                        }
                        if (MapUtils.isNotEmpty(collectF)) {
                            List<LoadCityHisDO> loadCityHisDOS5 = collectF.get(DateUtil.getStrDate(date, "yyyy-MM-dd"));
                            if (!CollectionUtils.isEmpty(loadCityHisDOS5)) {
                                bigDecimals = loadCityHisDOS5.get(0).getloadList();
                            }
                        }
                        curve96Load.setDateStr(DateUtil.getStrDate(date, "yyyy-MM-dd") + seasonName);
                        curve96Load.setBigDecimals(bigDecimals);
                        result.add(curve96Load);
                        break;
                    case MIN_LOAD_DAY:
                        // 最小负荷日
                        if (!CollectionUtils.isEmpty(loadFeatureCityDayHisDOS1)) {
                            bigDecimal = loadFeatureCityDayHisDOS1.stream()
                                .min(Comparator.comparing(LoadFeatureCityDayHisDO::getMinLoad)).get().getMinLoad();
                            for (LoadFeatureCityDayHisDO loadFeatureCityDayHisDO : loadFeatureCityDayHisDOS1) {
                                if (loadFeatureCityDayHisDO.getMinLoad().equals(bigDecimal)) {
                                    date = loadFeatureCityDayHisDO.getDate();
                                }
                            }
                        }
                        if (MapUtils.isNotEmpty(collectF)) {
                            List<LoadCityHisDO> loadCityHisDOS5 = collectF.get(DateUtil.getStrDate(date, "yyyy-MM-dd"));
                            if (!CollectionUtils.isEmpty(loadCityHisDOS5)) {
                                bigDecimals = loadCityHisDOS5.get(0).getloadList();
                            }
                        }
                        curve96Load.setDateStr(DateUtil.getStrDate(date, "yyyy-MM-dd") + seasonName);
                        curve96Load.setBigDecimals(bigDecimals);
                        result.add(curve96Load);
                        break;
                    case MAX_ENERGY_DAY:
                        // 最大电量日
                        if (!CollectionUtils.isEmpty(loadFeatureCityDayHisDOS1)) {
                            bigDecimal = loadFeatureCityDayHisDOS1.stream()
                                .max(Comparator.comparing(LoadFeatureCityDayHisDO::getEnergy)).get().getEnergy();
                            for (LoadFeatureCityDayHisDO loadFeatureCityDayHisDO : loadFeatureCityDayHisDOS1) {
                                if (loadFeatureCityDayHisDO.getEnergy().equals(bigDecimal)) {
                                    date = loadFeatureCityDayHisDO.getDate();
                                }
                            }
                        }
                        if (MapUtils.isNotEmpty(collectF)) {
                            List<LoadCityHisDO> loadCityHisDOS5 = collectF.get(DateUtil.getStrDate(date, "yyyy-MM-dd"));
                            if (!CollectionUtils.isEmpty(loadCityHisDOS5)) {
                                bigDecimals = loadCityHisDOS5.get(0).getloadList();
                            }
                        }
                        curve96Load.setDateStr(DateUtil.getStrDate(date, "yyyy-MM-dd") + seasonName);
                        curve96Load.setBigDecimals(bigDecimals);
                        result.add(curve96Load);
                        break;
                    case MAX_PEAK_DAY:
                        // 最大峰谷差日
                        if (!CollectionUtils.isEmpty(loadFeatureCityDayHisDOS1)) {
                            bigDecimal = loadFeatureCityDayHisDOS1.stream()
                                .max(Comparator.comparing(LoadFeatureCityDayHisDO::getDifferent)).get().getDifferent();
                            for (LoadFeatureCityDayHisDO loadFeatureCityDayHisDO : loadFeatureCityDayHisDOS1) {
                                if (loadFeatureCityDayHisDO.getDifferent().equals(bigDecimal)) {
                                    date = loadFeatureCityDayHisDO.getDate();
                                }
                            }
                        }
                        if (MapUtils.isNotEmpty(collectF)) {
                            List<LoadCityHisDO> loadCityHisDOS5 = collectF.get(DateUtil.getStrDate(date, "yyyy-MM-dd"));
                            if (!CollectionUtils.isEmpty(loadCityHisDOS5)) {
                                bigDecimals = loadCityHisDOS5.get(0).getloadList();
                            }
                        }
                        curve96Load.setDateStr(DateUtil.getStrDate(date, "yyyy-MM-dd") + seasonName);
                        curve96Load.setBigDecimals(bigDecimals);
                        result.add(curve96Load);
                        break;
                    default:
                        break;
                }

            }
        }
        return result;
    }

    @Override
    public List<Curve96Load> findSeasonYearCurveCompare(String cityId, String dateStr, Integer type, Integer seasonType)
        throws Exception {
        List<Curve96Load> result = new ArrayList<>();
        List<Date> list1 = new ArrayList<>();
        List<Date> list = new ArrayList<>();
        List<String> seasonAllList = new ArrayList<>();
        Map<String, List<String>> map = new HashMap<>();
        Integer integer = Integer.valueOf(dateStr);
        for (Integer i = 0; i < Integer.valueOf(dateStr); integer--) {
            List<String> seasonList = new ArrayList<>();
            if (integer == 2017) {
                break;
            }
            if (seasonType == 1) {
                // 春季
                seasonList = Arrays.asList(integer + "-03", integer + "-04", integer + "-05");
                map.put(integer + "春季", seasonList);
            } else if (seasonType == 2) {
                // 夏季
                seasonList = Arrays.asList(integer + "-06", integer + "-07", integer + "-08");
                map.put(integer + "夏季", seasonList);
            } else if (seasonType == 3) {
                // 秋节
                seasonList = Arrays.asList(integer + "-09", integer + "-10", integer + "-11");
                map.put(integer + "秋季", seasonList);
            } else {
                // 冬季
                seasonList = Arrays.asList(integer + "-12", integer + 1 + "-01",
                    integer + 1 + "-02");
                map.put(integer + "冬季", seasonList);
            }
            seasonAllList.addAll(seasonList);
        }

        for (String yearMonth : seasonAllList) {
            String year1 = yearMonth.split("-")[0];
            String month1 = yearMonth.split("-")[1];
            String firstDayOfMonth = DateUtil.getFirstDayOfMonth(year1, month1);
            String lastDayOfMonth = DateUtil.getLastDayOfMonth(year1, month1);
            Date yearFirst = DateUtil.getDateFromString(firstDayOfMonth, "yyyy-MM-dd");
            Date yearLast = DateUtil.getDateFromString(lastDayOfMonth, "yyyy-MM-dd");
            List<Date> listBetweenDay = DateUtil.getListBetweenDay(yearFirst, yearLast);
            List<Date> holiday = holidayService.findHoliday(yearFirst, yearLast);
            list.addAll(listBetweenDay);
            list1.addAll(holiday);
        }

        List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisDOS = loadFeatureCityDayHisService.listLoadFeature(cityId,
            getCaliberId(cityId), list);
        Map<String, List<LoadFeatureCityDayHisDO>> collect = loadFeatureCityDayHisDOS.stream()
            .collect(Collectors.groupingBy(t -> DateUtil.getStrDate(t.getDate(), "yyyy-MM-dd").substring(0, 7)));
        List<LoadCityHisDO> loadCityDOsByCityIdInDates = loadCityHisService.findLoadCityDOsByCityIdInDates(cityId, list,
            getCaliberId(cityId));
        Map<String, List<LoadCityHisDO>> collect1 = loadCityDOsByCityIdInDates.stream()
            .collect(Collectors.groupingBy(t -> DateUtil.getStrDate(t.getDate(), "yyyy-MM-dd").substring(0, 7)));

        for (String seasonName : map.keySet()) {
            List<String> list2 = map.get(seasonName);
            // 不区分
            List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisDOS1 = new ArrayList<>();
            List<LoadCityHisDO> loadCityHisDOS = new ArrayList<>();

            for (String yearMonth : list2) {
                List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisDOS2 = collect.get(yearMonth);
                List<LoadCityHisDO> loadCityHisDOS1 = collect1.get(yearMonth);
                if (!CollectionUtils.isEmpty(loadFeatureCityDayHisDOS2)) {
                    loadFeatureCityDayHisDOS1.addAll(loadFeatureCityDayHisDOS2);
                }
                if (!CollectionUtils.isEmpty(loadCityHisDOS1)) {
                    loadCityHisDOS.addAll(loadCityHisDOS1);
                }
            }
            for (CurveLoadCompareEnum value : CurveLoadCompareEnum.values()) {
                if (value.equals(SATURDAY) || value.equals(SUNDAY) || !value.getSort().equals(type)) {
                    continue;
                }

                List<BigDecimal> bigDecimals = new ArrayList<>();
                Curve96Load curve96Load = new Curve96Load();
                BigDecimal bigDecimal = null;
                Date date = null;
                // 工作日
                List<LoadCityHisDO> loadCityHisDOS2 = new ArrayList<>();
                // 休息日
                List<LoadCityHisDO> loadCityHisDOS3 = new ArrayList<>();
                // 排除节假日
                List<LoadCityHisDO> loadCityHisDOS1 = new ArrayList<>();
                if (!CollectionUtils.isEmpty(loadCityHisDOS)) {
                    for (LoadCityHisDO loadCityHisDO : loadCityHisDOS) {
                        if (!list1.contains(loadCityHisDO.getDate())) {
                            loadCityHisDOS1.add(loadCityHisDO);
                        }
                    }
                }

                Map<String, List<LoadCityHisDO>> collectF = new HashMap<>();
                if (!CollectionUtils.isEmpty(loadCityHisDOS)) {
                    collectF = loadCityHisDOS.stream()
                        .collect(Collectors.groupingBy(t -> DateUtil.getStrDate(t.getDate(), "yyyy-MM-dd")));
                }

                switch (value) {
                    case WORKING_DAY:
                        // 工作日
                        for (LoadCityHisDO loadCityHisDO : loadCityHisDOS1) {
                            if (com.tsintergy.lf.core.util.DateUtil.isWorkingDay(
                                loadCityHisDO.getDate().getTime())) {
                                loadCityHisDOS2.add(loadCityHisDO);
                            }
                        }
                        if (!CollectionUtils.isEmpty(loadCityHisDOS2)) {
                            List<BigDecimal> zeroList = ColumnUtil.getZeroOrNullList(96, BigDecimal.ZERO);
                            for (LoadCityHisDO loadCityHisDO : loadCityHisDOS2) {
                                zeroList = BigDecimalFunctions.listAdd(zeroList, loadCityHisDO.getloadList());
                            }
                            List<BigDecimal> bigDecimals1 = BigDecimalFunctions.listDivideValue(zeroList, new BigDecimal(loadCityHisDOS2.size()));
                            curve96Load.setBigDecimals(bigDecimals1);
                        }
                        curve96Load.setDateStr(seasonName);
                        result.add(curve96Load);
                        break;
                    case REST_DAY:
                        // 休息日
                        for (LoadCityHisDO loadCityHisDO : loadCityHisDOS1) {
                            if (!com.tsintergy.lf.core.util.DateUtil.isWorkingDay(
                                loadCityHisDO.getDate().getTime())) {
                                loadCityHisDOS3.add(loadCityHisDO);
                            }
                        }
                        if (!CollectionUtils.isEmpty(loadCityHisDOS3)) {
                            List<BigDecimal> zeroList = ColumnUtil.getZeroOrNullList(96, BigDecimal.ZERO);
                            for (LoadCityHisDO loadCityHisDO : loadCityHisDOS3) {
                                zeroList = BigDecimalFunctions.listAdd(zeroList, loadCityHisDO.getloadList());
                            }
                            List<BigDecimal> bigDecimals1 = BigDecimalFunctions.listDivideValue(zeroList, new BigDecimal(loadCityHisDOS3.size()));
                            curve96Load.setBigDecimals(bigDecimals1);
                        }
                        curve96Load.setDateStr(seasonName);
                        result.add(curve96Load);
                        break;
                    case MAX_LOAD_DAY:
                        // 最大负荷日
                        if (!CollectionUtils.isEmpty(loadFeatureCityDayHisDOS1)) {
                            bigDecimal = loadFeatureCityDayHisDOS1.stream()
                                .max(Comparator.comparing(LoadFeatureCityDayHisDO::getMaxLoad)).get().getMaxLoad();
                            for (LoadFeatureCityDayHisDO loadFeatureCityDayHisDO : loadFeatureCityDayHisDOS1) {
                                if (loadFeatureCityDayHisDO.getMaxLoad().equals(bigDecimal)) {
                                    date = loadFeatureCityDayHisDO.getDate();
                                }
                            }
                        }
                        if (MapUtils.isNotEmpty(collectF)) {
                            List<LoadCityHisDO> loadCityHisDOS5 = collectF.get(DateUtil.getStrDate(date, "yyyy-MM-dd"));
                            if (!CollectionUtils.isEmpty(loadCityHisDOS5)) {
                                bigDecimals = loadCityHisDOS5.get(0).getloadList();
                            }
                        }
                        curve96Load.setDateStr(DateUtil.getStrDate(date, "yyyy-MM-dd"));
                        curve96Load.setBigDecimals(bigDecimals);
                        result.add(curve96Load);
                        break;
                    case MIN_LOAD_DAY:
                        // 最小负荷日
                        if (!CollectionUtils.isEmpty(loadFeatureCityDayHisDOS1)) {
                            bigDecimal = loadFeatureCityDayHisDOS1.stream()
                                .min(Comparator.comparing(LoadFeatureCityDayHisDO::getMinLoad)).get().getMinLoad();
                            for (LoadFeatureCityDayHisDO loadFeatureCityDayHisDO : loadFeatureCityDayHisDOS1) {
                                if (loadFeatureCityDayHisDO.getMinLoad().equals(bigDecimal)) {
                                    date = loadFeatureCityDayHisDO.getDate();
                                }
                            }
                        }
                        if (MapUtils.isNotEmpty(collectF)) {
                            List<LoadCityHisDO> loadCityHisDOS5 = collectF.get(DateUtil.getStrDate(date, "yyyy-MM-dd"));
                            if (!CollectionUtils.isEmpty(loadCityHisDOS5)) {
                                bigDecimals = loadCityHisDOS5.get(0).getloadList();
                            }
                        }
                        curve96Load.setDateStr(DateUtil.getStrDate(date, "yyyy-MM-dd"));
                        curve96Load.setBigDecimals(bigDecimals);
                        result.add(curve96Load);
                        break;
                    case MAX_ENERGY_DAY:
                        // 最大电量日
                        if (!CollectionUtils.isEmpty(loadFeatureCityDayHisDOS1)) {
                            bigDecimal = loadFeatureCityDayHisDOS1.stream()
                                .max(Comparator.comparing(LoadFeatureCityDayHisDO::getEnergy)).get().getEnergy();
                            for (LoadFeatureCityDayHisDO loadFeatureCityDayHisDO : loadFeatureCityDayHisDOS1) {
                                if (loadFeatureCityDayHisDO.getEnergy().equals(bigDecimal)) {
                                    date = loadFeatureCityDayHisDO.getDate();
                                }
                            }
                        }
                        if (MapUtils.isNotEmpty(collectF)) {
                            List<LoadCityHisDO> loadCityHisDOS5 = collectF.get(DateUtil.getStrDate(date, "yyyy-MM-dd"));
                            if (!CollectionUtils.isEmpty(loadCityHisDOS5)) {
                                bigDecimals = loadCityHisDOS5.get(0).getloadList();
                            }
                        }
                        curve96Load.setDateStr(DateUtil.getStrDate(date, "yyyy-MM-dd"));
                        curve96Load.setBigDecimals(bigDecimals);
                        result.add(curve96Load);
                        break;
                    case MAX_PEAK_DAY:
                        // 最大峰谷差日
                        if (!CollectionUtils.isEmpty(loadFeatureCityDayHisDOS1)) {
                            bigDecimal = loadFeatureCityDayHisDOS1.stream()
                                .max(Comparator.comparing(LoadFeatureCityDayHisDO::getDifferent)).get().getDifferent();
                            for (LoadFeatureCityDayHisDO loadFeatureCityDayHisDO : loadFeatureCityDayHisDOS1) {
                                if (loadFeatureCityDayHisDO.getDifferent().equals(bigDecimal)) {
                                    date = loadFeatureCityDayHisDO.getDate();
                                }
                            }
                        }
                        if (MapUtils.isNotEmpty(collectF)) {
                            List<LoadCityHisDO> loadCityHisDOS5 = collectF.get(DateUtil.getStrDate(date, "yyyy-MM-dd"));
                            if (!CollectionUtils.isEmpty(loadCityHisDOS5)) {
                                bigDecimals = loadCityHisDOS5.get(0).getloadList();
                            }
                        }
                        curve96Load.setDateStr(DateUtil.getStrDate(date, "yyyy-MM-dd"));
                        curve96Load.setBigDecimals(bigDecimals);
                        result.add(curve96Load);
                        break;
                    default:
                        break;
                }

            }
        }
        List<Curve96Load> collect2 = result.stream().sorted(Comparator.comparing(Curve96Load::getDateStr).reversed())
            .collect(Collectors.toList());
        return collect2;
    }

    @Override
    public List<Curve96Load> findAllYearCurveCompare(String cityId, String dateStr, Integer type) throws Exception {
        List<Curve96Load> result = new ArrayList<>();
        List<Date> list1 = new ArrayList<>();
        List<Date> list = new ArrayList<>();
        List<String> list2 = new ArrayList<>();
        Integer integer = Integer.valueOf(dateStr);
        for (Integer i = 0; i < Integer.valueOf(dateStr); integer--) {
            if (integer == 2017) {
                break;
            }
            list2.add(String.valueOf(integer));
            Date yearFirst = DateUtil.getYearFirst(integer);
            Date yearLast = DateUtil.getYearLast(integer);
            List<Date> listBetweenDay = DateUtil.getListBetweenDay(yearFirst, yearLast);
            List<Date> holiday = holidayService.findHoliday(yearFirst, yearLast);
            list.addAll(listBetweenDay);
            list1.addAll(holiday);
        }
        List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisDOS = loadFeatureCityDayHisService.listLoadFeature(cityId,
            getCaliberId(cityId), list);
        Map<String, List<LoadFeatureCityDayHisDO>> collect = loadFeatureCityDayHisDOS.stream()
            .collect(Collectors.groupingBy(t -> DateUtil.getStrDate(t.getDate(), "yyyy-MM-dd").substring(0, 4)));
        List<LoadCityHisDO> loadCityDOsByCityIdInDates = loadCityHisService.findLoadCityDOsByCityIdInDates(cityId, list,
            getCaliberId(cityId));
        Map<String, List<LoadCityHisDO>> collect1 = loadCityDOsByCityIdInDates.stream()
            .collect(Collectors.groupingBy(t -> DateUtil.getStrDate(t.getDate(), "yyyy-MM-dd").substring(0, 4)));

        for (String yearStr : list2) {
            // 不区分
            List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisDOS1 = collect.get(yearStr);
            List<LoadCityHisDO> loadCityHisDOS = collect1.get(yearStr);

            for (CurveLoadCompareEnum value : CurveLoadCompareEnum.values()) {
                if (value.equals(SATURDAY) || value.equals(SUNDAY) ||!value.getSort().equals(type)) {
                    continue;
                }
                List<BigDecimal> bigDecimals = new ArrayList<>();
                Curve96Load curve96Load = new Curve96Load();
                BigDecimal bigDecimal = null;
                Date date = null;
                // 工作日
                List<LoadCityHisDO> loadCityHisDOS2 = new ArrayList<>();
                // 休息日
                List<LoadCityHisDO> loadCityHisDOS3 = new ArrayList<>();
                // 排除节假日
                List<LoadCityHisDO> loadCityHisDOS1 = new ArrayList<>();
                if (!CollectionUtils.isEmpty(loadCityHisDOS)) {
                    for (LoadCityHisDO loadCityHisDO : loadCityHisDOS) {
                        if (!list1.contains(loadCityHisDO.getDate())) {
                            loadCityHisDOS1.add(loadCityHisDO);
                        }
                    }
                }

                Map<String, List<LoadCityHisDO>> collectF = new HashMap<>();
                if (!CollectionUtils.isEmpty(loadCityHisDOS)) {
                    collectF = loadCityHisDOS.stream()
                        .collect(Collectors.groupingBy(t -> DateUtil.getStrDate(t.getDate(), "yyyy-MM-dd")));
                }

                switch (value) {
                    case WORKING_DAY:
                        // 工作日
                        for (LoadCityHisDO loadCityHisDO : loadCityHisDOS1) {
                            if (com.tsintergy.lf.core.util.DateUtil.isWorkingDay(
                                loadCityHisDO.getDate().getTime())) {
                                loadCityHisDOS2.add(loadCityHisDO);
                            }
                        }
                        if (!CollectionUtils.isEmpty(loadCityHisDOS2)) {
                            List<BigDecimal> zeroList = ColumnUtil.getZeroOrNullList(96, BigDecimal.ZERO);
                            for (LoadCityHisDO loadCityHisDO : loadCityHisDOS2) {
                                zeroList = BigDecimalFunctions.listAdd(zeroList, loadCityHisDO.getloadList());
                            }
                            List<BigDecimal> bigDecimals1 = BigDecimalFunctions.listDivideValue(zeroList, new BigDecimal(loadCityHisDOS2.size()));
                            curve96Load.setBigDecimals(bigDecimals1);
                        }
                        curve96Load.setDateStr(yearStr);
                        result.add(curve96Load);
                        break;
                    case REST_DAY:
                        // 休息日
                        for (LoadCityHisDO loadCityHisDO : loadCityHisDOS1) {
                            if (!com.tsintergy.lf.core.util.DateUtil.isWorkingDay(
                                loadCityHisDO.getDate().getTime())) {
                                loadCityHisDOS3.add(loadCityHisDO);
                            }
                        }
                        if (!CollectionUtils.isEmpty(loadCityHisDOS3)) {
                            List<BigDecimal> zeroList = ColumnUtil.getZeroOrNullList(96, BigDecimal.ZERO);
                            for (LoadCityHisDO loadCityHisDO : loadCityHisDOS3) {
                                zeroList = BigDecimalFunctions.listAdd(zeroList, loadCityHisDO.getloadList());
                            }
                            List<BigDecimal> bigDecimals1 = BigDecimalFunctions.listDivideValue(zeroList, new BigDecimal(loadCityHisDOS3.size()));
                            curve96Load.setBigDecimals(bigDecimals1);
                        }
                        curve96Load.setDateStr(yearStr);
                        result.add(curve96Load);
                        break;
                    case MAX_LOAD_DAY:
                        // 最大负荷日
                        if (!CollectionUtils.isEmpty(loadFeatureCityDayHisDOS1)) {
                            bigDecimal = loadFeatureCityDayHisDOS1.stream()
                                .max(Comparator.comparing(LoadFeatureCityDayHisDO::getMaxLoad)).get().getMaxLoad();
                            for (LoadFeatureCityDayHisDO loadFeatureCityDayHisDO : loadFeatureCityDayHisDOS1) {
                                if (loadFeatureCityDayHisDO.getMaxLoad().equals(bigDecimal)) {
                                    date = loadFeatureCityDayHisDO.getDate();
                                }
                            }
                        }
                        if (MapUtils.isNotEmpty(collectF)) {
                            List<LoadCityHisDO> loadCityHisDOS5 = collectF.get(DateUtil.getStrDate(date, "yyyy-MM-dd"));
                            if (!CollectionUtils.isEmpty(loadCityHisDOS5)) {
                                bigDecimals = loadCityHisDOS5.get(0).getloadList();
                            }
                        }
                        curve96Load.setDateStr(DateUtil.getStrDate(date, "yyyy-MM-dd"));
                        curve96Load.setBigDecimals(bigDecimals);
                        result.add(curve96Load);
                        break;
                    case MIN_LOAD_DAY:
                        // 最小负荷日
                        if (!CollectionUtils.isEmpty(loadFeatureCityDayHisDOS1)) {
                            bigDecimal = loadFeatureCityDayHisDOS1.stream()
                                .min(Comparator.comparing(LoadFeatureCityDayHisDO::getMinLoad)).get().getMinLoad();
                            for (LoadFeatureCityDayHisDO loadFeatureCityDayHisDO : loadFeatureCityDayHisDOS1) {
                                if (loadFeatureCityDayHisDO.getMinLoad().equals(bigDecimal)) {
                                    date = loadFeatureCityDayHisDO.getDate();
                                }
                            }
                        }
                        if (MapUtils.isNotEmpty(collectF)) {
                            List<LoadCityHisDO> loadCityHisDOS5 = collectF.get(DateUtil.getStrDate(date, "yyyy-MM-dd"));
                            if (!CollectionUtils.isEmpty(loadCityHisDOS5)) {
                                bigDecimals = loadCityHisDOS5.get(0).getloadList();
                            }
                        }
                        curve96Load.setDateStr(DateUtil.getStrDate(date, "yyyy-MM-dd"));
                        curve96Load.setBigDecimals(bigDecimals);
                        result.add(curve96Load);
                        break;
                    case MAX_ENERGY_DAY:
                        // 最大电量日
                        if (!CollectionUtils.isEmpty(loadFeatureCityDayHisDOS1)) {
                            bigDecimal = loadFeatureCityDayHisDOS1.stream()
                                .max(Comparator.comparing(LoadFeatureCityDayHisDO::getEnergy)).get().getEnergy();
                            for (LoadFeatureCityDayHisDO loadFeatureCityDayHisDO : loadFeatureCityDayHisDOS1) {
                                if (loadFeatureCityDayHisDO.getEnergy().equals(bigDecimal)) {
                                    date = loadFeatureCityDayHisDO.getDate();
                                }
                            }
                        }
                        if (MapUtils.isNotEmpty(collectF)) {
                            List<LoadCityHisDO> loadCityHisDOS5 = collectF.get(DateUtil.getStrDate(date, "yyyy-MM-dd"));
                            if (!CollectionUtils.isEmpty(loadCityHisDOS5)) {
                                bigDecimals = loadCityHisDOS5.get(0).getloadList();
                            }
                        }
                        curve96Load.setDateStr(DateUtil.getStrDate(date, "yyyy-MM-dd"));
                        curve96Load.setBigDecimals(bigDecimals);
                        result.add(curve96Load);
                        break;
                    case MAX_PEAK_DAY:
                        // 最大峰谷差日
                        if (!CollectionUtils.isEmpty(loadFeatureCityDayHisDOS1)) {
                            bigDecimal = loadFeatureCityDayHisDOS1.stream()
                                .max(Comparator.comparing(LoadFeatureCityDayHisDO::getDifferent)).get().getDifferent();
                            for (LoadFeatureCityDayHisDO loadFeatureCityDayHisDO : loadFeatureCityDayHisDOS1) {
                                if (loadFeatureCityDayHisDO.getDifferent().equals(bigDecimal)) {
                                    date = loadFeatureCityDayHisDO.getDate();
                                }
                            }
                        }
                        if (MapUtils.isNotEmpty(collectF)) {
                            List<LoadCityHisDO> loadCityHisDOS5 = collectF.get(DateUtil.getStrDate(date, "yyyy-MM-dd"));
                            if (!CollectionUtils.isEmpty(loadCityHisDOS5)) {
                                bigDecimals = loadCityHisDOS5.get(0).getloadList();
                            }
                        }
                        curve96Load.setDateStr(DateUtil.getStrDate(date, "yyyy-MM-dd"));
                        curve96Load.setBigDecimals(bigDecimals);
                        result.add(curve96Load);
                        break;
                    default:
                        break;
                }
            }
        }
        return result;
    }

    private String getCaliberId(String cityId){
        if (Constants.PROVINCE_TYPE == Integer.parseInt(cityId)){
            return Constants.CALIBER_ID_BG_QW;
        }else{
            return Constants.CALIBER_ID_BG_DS;
        }
    }
}
