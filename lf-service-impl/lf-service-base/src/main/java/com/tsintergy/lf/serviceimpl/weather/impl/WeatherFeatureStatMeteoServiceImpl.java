package com.tsintergy.lf.serviceimpl.weather.impl;


import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.util.compatible.BeanUtils;
import com.tsieframework.util.compatible.BigDecimalUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherEnum;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityHisMeteoService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureStatMeteoService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisMeteoDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayHisMeteoDO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherFeatureCityDayHisMeteoDAO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * 气象特性统计接口实现 User:taojingui Date:18-2-23 Time:下午12:04
 */
@Service("weatherFeatureStatMeteoService")
@Slf4j
public class WeatherFeatureStatMeteoServiceImpl extends BaseServiceImpl implements WeatherFeatureStatMeteoService {


    @Autowired
    WeatherCityHisMeteoService weatherCityHisMeteoService;

    @Autowired
    WeatherFeatureCityDayHisMeteoDAO weatherFeatureCityDayHisDAO;

    @Override
    public List<WeatherFeatureCityDayHisMeteoDO> doStatWeatherFeatureCityDay(List<String> cityIds, Date startDate,
                                                                             Date endDate)
            throws Exception {
        try {
            List<WeatherCityHisMeteoDO> weatherCityHisVOs = weatherCityHisMeteoService
                    .getWeatherCityHisDOS(cityIds, null, startDate, endDate);
            List<WeatherFeatureCityDayHisMeteoDO> weatherFeatureCityDayHisVOs = this
                    .statisticsDayFeature(weatherCityHisVOs);
            return weatherFeatureCityDayHisDAO.doSaveOrUpdateWeatherFeatureCityDayHisMeteoDOs(weatherFeatureCityDayHisVOs);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("", "统计日气象特性出错了", e);
        }
    }


    public List<WeatherFeatureCityDayHisMeteoDO> statisticsDayFeature(List<WeatherCityHisMeteoDO> weatherCityHisVOs) {
        List<WeatherFeatureCityDayHisMeteoDO> weatherFeatureCityDayHisVOS = new ArrayList<WeatherFeatureCityDayHisMeteoDO>();
        if (weatherCityHisVOs != null) {
            for (WeatherCityHisMeteoDO weatherCityHisVO : weatherCityHisVOs) {
                try {
                    //统计气象特性
                    WeatherFeatureCityDayHisMeteoDO weatherFeatureCityDayHisVO = statisticsDayFeature(weatherCityHisVO);
                    weatherFeatureCityDayHisVOS.add(weatherFeatureCityDayHisVO);
                } catch (Exception e) {
                    log.error(e.toString());
                }
            }

            // 合并气象特性
            Map<String, WeatherFeatureCityDayHisMeteoDO> weatherFeatureCityDayHisVOMap = new HashMap<>();
            for (WeatherFeatureCityDayHisMeteoDO weatherFeatureCityDayHisVO : weatherFeatureCityDayHisVOS) {
                try {
                    String key =
                            weatherFeatureCityDayHisVO.getCityId() + "-" + weatherFeatureCityDayHisVO.getDate().getTime();
                    if (weatherFeatureCityDayHisVOMap.get(key) == null) {
                        weatherFeatureCityDayHisVOMap.put(key, weatherFeatureCityDayHisVO);
                    } else {
                        BeanUtils
                                .copyPropertiesNotNull(weatherFeatureCityDayHisVOMap.get(key), weatherFeatureCityDayHisVO);
                    }
                } catch (Exception e) {
                    log.warn(e.toString());
                }
            }

            weatherFeatureCityDayHisVOS = new ArrayList<>(weatherFeatureCityDayHisVOMap.values());
        }
        return weatherFeatureCityDayHisVOS;
    }

    /**
     * 统计气象特性
     *
     * @param weatherCityHisVO 气象数据
     */
    public WeatherFeatureCityDayHisMeteoDO statisticsDayFeature(WeatherCityHisMeteoDO weatherCityHisVO) throws Exception {
        List<BigDecimal> weatherList = BasePeriodUtils
                .toList(weatherCityHisVO, Constants.WEATHER_CURVE_POINT_NUM, Constants.WEATHER_CURVE_START_WITH_ZERO);
        Map<String, BigDecimal> maxMixAvg = BasePeriodUtils.getMaxMinAvg(weatherList, 4);
        WeatherFeatureCityDayHisMeteoDO weatherFeatureCityDayHisVO = new WeatherFeatureCityDayHisMeteoDO();
        weatherFeatureCityDayHisVO.setCityId(weatherCityHisVO.getCityId());
        weatherFeatureCityDayHisVO.setDate(weatherCityHisVO.getDate());
        //set近三日降雨量 todo wangchen 暂不处理，目前处理方式较慢
        //weatherFeatureCityDayHisVO.setRecentlyRainfall(countRecentlyRainfall(weatherCityHisVO));
        if (WeatherEnum.HUMIDITY.value().equals(weatherCityHisVO.getType())) {
            weatherFeatureCityDayHisVO.setHighestHumidity(maxMixAvg.get("max"));
            weatherFeatureCityDayHisVO.setAveHumidity(maxMixAvg.get("avg"));
            weatherFeatureCityDayHisVO.setLowestHumidity(maxMixAvg.get("min"));
        } else if (WeatherEnum.TEMPERATURE.value().equals(weatherCityHisVO.getType())) {
            // 统计最高温度发生时刻
            Map<String, BigDecimal> temperatureMap = BasePeriodUtils.toMap(weatherCityHisVO, Constants.LOAD_CURVE_POINT_NUM,
                    Constants.LOAD_CURVE_START_WITH_ZERO);
            for (String column : temperatureMap.keySet()) {
                BigDecimal temperature = temperatureMap.get(column);
                if (null != temperature) {
                    column = column.substring(1);
                    if (temperature.compareTo(maxMixAvg.get("max")) == 0) {
                        weatherFeatureCityDayHisVO.setHighestTemperatureTime(new StringBuffer(column).insert(2, ":").toString());
                    }
                }
            }
            weatherFeatureCityDayHisVO.setHighestTemperature(maxMixAvg.get("max"));
            weatherFeatureCityDayHisVO.setAveTemperature(maxMixAvg.get("avg"));
            weatherFeatureCityDayHisVO.setLowestTemperature(maxMixAvg.get("min"));

        } else if (WeatherEnum.RAINFALL.value().equals(weatherCityHisVO.getType())) {
            List<BigDecimal> bigDecimals = BasePeriodUtils
                    .toList(weatherCityHisVO, Constants.WEATHER_CURVE_POINT_NUM_24, true);
            weatherFeatureCityDayHisVO.setRainfall(BigDecimalUtils.addAllValue(bigDecimals));
        } else if (WeatherEnum.WINDSPEED.value().equals(weatherCityHisVO.getType())) {
            weatherFeatureCityDayHisVO.setMaxWinds(maxMixAvg.get("max"));
            weatherFeatureCityDayHisVO.setAveWinds(maxMixAvg.get("avg"));
            weatherFeatureCityDayHisVO.setMinWinds(maxMixAvg.get("min"));
        } else if (WeatherEnum.EFFECTIVE_TEMPERATURE.value().equals(weatherCityHisVO.getType())) {
            weatherFeatureCityDayHisVO.setHighestEffectiveTemperature(maxMixAvg.get("max"));
            weatherFeatureCityDayHisVO.setLowestEffectiveTemperature(maxMixAvg.get("min"));
            weatherFeatureCityDayHisVO.setAveEffectiveTemperature(maxMixAvg.get("avg"));
        }

        return weatherFeatureCityDayHisVO;
    }

}
