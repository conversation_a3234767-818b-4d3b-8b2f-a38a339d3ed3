/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司
 * Author:  wangchen
 * Date:  2019/3/11 15:01
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceimpl.load.dao;

import com.tsieframework.core.base.dao.hibernate.BaseAbstractDAO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.dao.hibernate.QueryOp;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityFcClctDO;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2019/3/11 
 * @since 1.0.0
 */
@Slf4j
public class LoadCityFcClctDAO extends BaseAbstractDAO<LoadCityFcClctDO> {


    public void doInsertOrUpdateBatch(List<LoadCityFcClctDO> loadCityFcClctVOList) {
        if (CollectionUtils.isNotEmpty(loadCityFcClctVOList)) {
            loadCityFcClctVOList.forEach(loadCityFcClctDO -> {
                try {
                    this.doInsertOrUpdate(loadCityFcClctDO);
                } catch (Exception e) {
                    log.error("保存采集预测负荷异常。。。", e);
                }
            });
        }
    }

    public void doInsertOrUpdate(LoadCityFcClctDO loadCityFcClctVO) throws Exception {
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create();
        if (loadCityFcClctVO.getCaliberId() != null) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo,"caliberId",loadCityFcClctVO.getCaliberId());
        }
        if (loadCityFcClctVO.getCityId() != null) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo,"cityId",loadCityFcClctVO.getCityId());
        }
        if (loadCityFcClctVO.getDate() != null) {
            dbQueryParamBuilder.where(QueryOp.DateEqualTo,"date",loadCityFcClctVO.getDate());
        }
        DBQueryParam dbQueryParam = dbQueryParamBuilder.build();
        List<LoadCityFcClctDO> loadCityFcClctVOS = this.query(dbQueryParam).getDatas();
        this.getSession().flush();
        this.getSession().clear();
        if (loadCityFcClctVOS != null && loadCityFcClctVOS.size() > 0) {
            loadCityFcClctVO.setId(loadCityFcClctVOS.get(0).getId());
            this.update(loadCityFcClctVO);
            return;
        }
        this.createAndFlush(loadCityFcClctVO);
    }
}