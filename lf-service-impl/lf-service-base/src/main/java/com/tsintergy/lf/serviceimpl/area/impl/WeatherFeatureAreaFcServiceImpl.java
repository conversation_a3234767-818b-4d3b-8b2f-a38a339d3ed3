/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.area.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsieframework.core.base.service.BaseFacadeServiceImpl;
import com.tsieframework.util.compatible.BeanUtils;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.area.api.WeatherAreaFcService;
import com.tsintergy.lf.serviceapi.base.area.api.WeatherAreaHisService;
import com.tsintergy.lf.serviceapi.base.area.api.WeatherFeatureAreaFcService;
import com.tsintergy.lf.serviceapi.base.area.pojo.WeatherAreaFcDO;
import com.tsintergy.lf.serviceapi.base.area.pojo.WeatherAreaHisDO;
import com.tsintergy.lf.serviceapi.base.area.pojo.WeatherFeatureAreaDayFcDO;
import com.tsintergy.lf.serviceapi.base.area.pojo.WeatherFeatureAreaDayHisDO;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherNewEnum;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureStatService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.BaseWeatherFeatureDayFcDO;
import com.tsintergy.lf.serviceimpl.area.dao.WeatherFeatureAreaDayFcDAO;
import java.sql.Timestamp;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/8/9 11:57
 * @Version: 1.0.0
 */
@Service("weatherFeatureAreaFcService")
public class WeatherFeatureAreaFcServiceImpl extends BaseFacadeServiceImpl implements WeatherFeatureAreaFcService {



    @Autowired
    private WeatherAreaFcService weatherAreaFcService;
    @Autowired
    private WeatherFeatureAreaDayFcDAO weatherFeatureAreaDayFcDAO;

    @Autowired
    WeatherFeatureStatService weatherFeatureStatService;
    @Override
    public void doSaveOrUpdate(WeatherFeatureAreaDayFcDO weatherFeatureAreaDayFcDO) {
        List<WeatherFeatureAreaDayFcDO> areaWeatherFeature = this
            .findAreaWeatherFeature(weatherFeatureAreaDayFcDO.getAreaId(), weatherFeatureAreaDayFcDO.getDate(),
                weatherFeatureAreaDayFcDO.getDate());
        if (CollectionUtils.isEmpty(areaWeatherFeature)){
            weatherFeatureAreaDayFcDO.setCreatetime(new Timestamp(System.currentTimeMillis()));
            weatherFeatureAreaDayFcDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            weatherFeatureAreaDayFcDAO.save(weatherFeatureAreaDayFcDO);
        }else{
            WeatherFeatureAreaDayFcDO oldDo = areaWeatherFeature.get(0);
            weatherFeatureAreaDayFcDO.setId(oldDo.getId());
            weatherFeatureAreaDayFcDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            weatherFeatureAreaDayFcDAO.saveOrUpdateByTemplate(weatherFeatureAreaDayFcDO);
        }


    }

    @Override
    public List<WeatherFeatureAreaDayFcDO> findAreaWeatherFeature(String areaId, Date startDate, Date endDate) {
        List<WeatherFeatureAreaDayFcDO> list = weatherFeatureAreaDayFcDAO
            .findAll(JpaWrappers.<WeatherFeatureAreaDayFcDO>lambdaQuery()
                .ge(startDate != null, WeatherFeatureAreaDayFcDO::getDate, startDate)
                .le(endDate != null, WeatherFeatureAreaDayFcDO::getDate, endDate)
                .eq(areaId != null, WeatherFeatureAreaDayFcDO::getAreaId, areaId)
            );
        return list;
    }

    @Override
    public void statAreaWeatherFcFeature(Date startDate, Date endDate, String areaId) throws Exception {
        Map<String, WeatherAreaFcDO> collect = weatherAreaFcService.findWeatherCityFcDOs(null, null, startDate, endDate)
            .stream().collect(Collectors.toMap(t -> {
                return t.getAreaId() + t.getType() + DateUtil.getDateToStr(t.getDate());
            }, t -> t));
        List<Date> dateList = DateUtil.getListBetweenDay(startDate,endDate);

        Map<String,WeatherFeatureAreaDayFcDO> resultMap = new HashMap<>();
        for (Date date : dateList) {
            for (WeatherNewEnum value : WeatherNewEnum.values()) {
                String weatherKey = areaId + value.getType() + DateUtil.getDateToStr(date);
                String featureKey = areaId + DateUtil.getDateToStr(date);

                WeatherAreaFcDO weatherAreaFcDO = collect
                    .get(weatherKey);
                if (weatherAreaFcDO!=null){

                    WeatherFeatureAreaDayFcDO mapValue = resultMap.get(featureKey);
                    if (mapValue == null){
                        mapValue = new WeatherFeatureAreaDayFcDO();
                        mapValue.setAreaId(areaId);
                    }
                    BaseWeatherFeatureDayFcDO baseWeatherFeatureDayFcDO = weatherFeatureStatService
                        .doStatWeatherFeature(weatherAreaFcDO, date, value.value());
                    BeanUtils.copyPropertiesNotNull(mapValue,baseWeatherFeatureDayFcDO);
                    resultMap.put(featureKey,mapValue);
                }
            }
        }

        updateAreaWeatherFeature(startDate,endDate,areaId,resultMap.values().stream().collect(Collectors.toList()));

    }

    private void updateAreaWeatherFeature(Date startDate, Date endDate, String areaId,List<WeatherFeatureAreaDayFcDO> weatherFeatureAreaDayFcDOS) {
        if (CollectionUtils.isEmpty(weatherFeatureAreaDayFcDOS)){
            return;
        }

        weatherFeatureAreaDayFcDAO.delete(JpaWrappers.<WeatherFeatureAreaDayFcDO>lambdaQuery()
            .eq(areaId!=null,WeatherFeatureAreaDayFcDO::getAreaId,areaId)
            .le(endDate!=null,WeatherFeatureAreaDayFcDO::getDate,endDate)
            .ge(startDate!=null,WeatherFeatureAreaDayFcDO::getDate,startDate));
        weatherFeatureAreaDayFcDAO.saveOrUpdateBatch(weatherFeatureAreaDayFcDOS);

    }
}