/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司 Author:  wangchen Date:  2019/5/15 20:07 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceimpl.evalucation.impl;

import com.tsieframework.core.base.dao.DataPackage;
import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.LoadCalUtil;
import com.tsintergy.lf.serviceapi.base.check.pojo.SettingReportDO;
import com.tsintergy.lf.serviceapi.base.evalucation.api.StatisticsWeatherCityDayFcStatService;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.StatisticsWeatherCityDayFcStatDO;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityHisService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisDO;
import com.tsintergy.lf.serviceimpl.check.dao.SettingReportDAO;
import com.tsintergy.lf.serviceimpl.evalucation.dao.StatisticsWeatherCityDayFcStatDAO;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2019/5/15
 * @since 1.0.0
 */

@Service("statisticsWeatherCityDayFcStatService")
public class StatisticsWeatherCityDayFcStatServiceImpl extends BaseServiceImpl implements StatisticsWeatherCityDayFcStatService {

    private static final Logger logger = LogManager.getLogger(StatisticsCityDayFcServiceImpl.class);

    @Autowired
    private WeatherCityHisService weatherCityHisService;

    @Autowired
    private WeatherCityFcService weatherCityFcService;

    @Autowired
    private StatisticsWeatherCityDayFcStatDAO statisticsWeatherCityDayFcStatDAO;

    @Autowired
    private SettingReportDAO settingReportDAO;

    @Override
    public DataPackage findStatisticsWeatherCityDayFcStat(String cityId, Integer weatherType, Date startDate,
                                                          Date endDate) {

        try {
            return statisticsWeatherCityDayFcStatDAO.findStatisticsWeatherCityDayFcStat(cityId, weatherType, startDate, endDate, null, null);
        } catch (Exception e) {
            logger.error("查询气象准确率统计异常...", e);
            throw TsieExceptionUtils.newBusinessException("01C20180003");
        }
    }


    private StatisticsWeatherCityDayFcStatDO getStatisticsWeatherCityDayFcStatDO(String cityId, Integer type, Date date)
            throws Exception {
        DataPackage dataPackage = statisticsWeatherCityDayFcStatDAO
                .findStatisticsWeatherCityDayFcStat(cityId, type, date, date, null, null);
        if (dataPackage != null) {
            List<StatisticsWeatherCityDayFcStatDO> weatherCityDayFcStatVOS = dataPackage.getDatas();
            if (!CollectionUtils.isEmpty(weatherCityDayFcStatVOS)) {
                return weatherCityDayFcStatVOS.get(0);
            }
        }
        return null;
    }


    /**
     * 功能描述: <br>
     * 统计气象预测准确率
     *
     * @return
     * <AUTHOR>
     * @since 1.0.0
     */
    @Override
    public void doStatWeatherAccuracy(String cityId, Date startDate, Date endDate) throws Exception {
        List<WeatherCityHisDO> weatherCityHisClctDOList = weatherCityHisService
                .findWeatherCityHisDOs(cityId, null, startDate, endDate);
        List<WeatherCityFcDO> weatherCityFcClctDOS = weatherCityFcService
                .findWeatherCityFcDOs(cityId, null, startDate, endDate);
        if (CollectionUtils.isEmpty(weatherCityFcClctDOS) || CollectionUtils.isEmpty(weatherCityHisClctDOList)
                || weatherCityFcClctDOS.size() < 1 || weatherCityHisClctDOList.size() < 1) {
            logger.error("统计气象的准确率失败，历史气象or预测气象无数据");
            return;
        }
        List<SettingReportDO> reportDOs = settingReportDAO.getSettingReportDOs(null);
        List<StatisticsWeatherCityDayFcStatDO> weatherDayFcStatVOs = statistics(weatherCityHisClctDOList,
                weatherCityFcClctDOS, reportDOs);
        this.doSaveOrUpdateStatisticsWeatherCityDayFcStatDOs(weatherDayFcStatVOs);
    }


    private List<StatisticsWeatherCityDayFcStatDO> statistics(List<WeatherCityHisDO> WeatherCityHisDOS,
                                                              List<WeatherCityFcDO> WeatherCityFcDOS, List<SettingReportDO> SettingReportDOs) throws Exception {

        Map<String, WeatherCityFcDO> fcMap = WeatherCityFcDOS == null ? new HashMap<>() : WeatherCityFcDOS.stream().collect(Collectors.toMap(e -> e.getCityId() + "-" + e.getType() + "-" + e.getDate().getTime(), e -> e, (oldv, curv) -> curv));
        Map<String, WeatherCityHisDO> hisMap = WeatherCityHisDOS == null ? new HashMap<>() : WeatherCityHisDOS.stream().collect(Collectors.toMap(e -> e.getCityId() + "-" + e.getType() + "-" + e.getDate().getTime(), e -> e, (oldv, curv) -> curv));
        // Map<城市ID,考核准确率>
        Map<String, BigDecimal> standardAccuracyMap = SettingReportDOs == null ? new HashMap<>() : SettingReportDOs.stream().collect(Collectors.toMap(e -> e.getCityId(), e -> e.getStandardAccuracy(), (oldv, curv) -> curv));
        List<StatisticsWeatherCityDayFcStatDO> statDOS = new ArrayList<>();
        for (String key : fcMap.keySet()) {
            WeatherCityFcDO fcVO = fcMap.get(key);
            WeatherCityHisDO hisVO = hisMap.get(key);
            if (hisVO != null) {
                StatisticsWeatherCityDayFcStatDO weatherCityDayFcStatVO = new StatisticsWeatherCityDayFcStatDO();
                weatherCityDayFcStatVO.setCityId(hisVO.getCityId());
                weatherCityDayFcStatVO.setDate(hisVO.getDate());
                weatherCityDayFcStatVO.setType(hisVO.getType());
                //计算气象准确率
                BigDecimal weatherAccuracy = LoadCalUtil
                        .getWeatherAccuracy(hisVO, fcVO,
                        Constants.LOAD_CURVE_POINT_NUM);
                BigDecimal standAccuracy = standardAccuracyMap.get(hisVO.getCityId());
                weatherCityDayFcStatVO.setStandardAccuracy(standAccuracy);
                //如果气象准确率小于0 则置为0
                if (weatherAccuracy.compareTo(BigDecimal.ZERO) < 1) {
                    weatherAccuracy = new BigDecimal(0);
                }
                weatherCityDayFcStatVO.setAccuracy(weatherAccuracy);
                if (standAccuracy!=null && weatherAccuracy.compareTo(standAccuracy) > 0) {
                    weatherCityDayFcStatVO.setPass(new BigDecimal(1));
                } else {
                    weatherCityDayFcStatVO.setPass(new BigDecimal(0));
                }
                statDOS.add(weatherCityDayFcStatVO);
            }
        }
        return statDOS;
    }


    public List<StatisticsWeatherCityDayFcStatDO> doSaveOrUpdateStatisticsWeatherCityDayFcStatDOs(
            List<StatisticsWeatherCityDayFcStatDO> statisticsWeatherCityDayFcStatDOS) throws Exception {
        List<StatisticsWeatherCityDayFcStatDO> vos = new ArrayList<>();
        for (StatisticsWeatherCityDayFcStatDO statDO : statisticsWeatherCityDayFcStatDOS) {
            try {
                vos.add(this.doSaveOrUpdateStatisticsWeatherCityDayFcStatDO(statDO));
            } catch (Exception e) {
                logger.error("保存预测统计结果出错了", e);
            }
        }
        return vos;
    }


    /**
     * 保存或更新
     */
    public StatisticsWeatherCityDayFcStatDO doSaveOrUpdateStatisticsWeatherCityDayFcStatDO(
            StatisticsWeatherCityDayFcStatDO StatisticsWeatherCityDayFcStatDO)
            throws Exception {
        StatisticsWeatherCityDayFcStatDO oldVO = getStatisticsWeatherCityDayFcStatDO(
                StatisticsWeatherCityDayFcStatDO.getCityId(),
                StatisticsWeatherCityDayFcStatDO.getType(),
                StatisticsWeatherCityDayFcStatDO.getDate());
        if (oldVO != null) {
            String id = oldVO.getId();
            PropertyUtils.copyProperties(oldVO, StatisticsWeatherCityDayFcStatDO);
            oldVO.setId(id);
            return statisticsWeatherCityDayFcStatDAO.updateAndFlush(oldVO);
        } else {
            return statisticsWeatherCityDayFcStatDAO.createAndFlush(StatisticsWeatherCityDayFcStatDO);
        }
    }

}
