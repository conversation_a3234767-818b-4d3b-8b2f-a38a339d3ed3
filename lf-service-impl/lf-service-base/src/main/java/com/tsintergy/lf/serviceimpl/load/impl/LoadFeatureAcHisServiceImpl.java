/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.load.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.math.BigDecimalFunctions;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.airconditioner.api.*;
import com.tsintergy.lf.serviceapi.base.airconditioner.dto.*;
import com.tsintergy.lf.serviceapi.base.airconditioner.pojo.FoundationLoadHisMonthDO;
import com.tsintergy.lf.serviceapi.base.airconditioner.pojo.LoadAcHisBasicDO;
import com.tsintergy.lf.serviceapi.base.airconditioner.pojo.LoadFeatureAcHisDO;
import com.tsintergy.lf.serviceapi.base.airconditioner.pojo.LoadSolutionManageDO;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.common.enumeration.DateType2;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherEnum;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityHisService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityDayHisService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayHisDO;
import com.tsintergy.lf.serviceimpl.load.dao.LoadFeatureAcHisDAO;
import com.tsintergy.lf.serviceimpl.system.dao.SettingSystemDAO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * Description:<br>
 *
 * @Author:<EMAIL>
 * @Date: 2022/5/23 15:13
 * @Version:1.0.0
 */
@Service("loadFeatureAcHisService")
@Slf4j
public class LoadFeatureAcHisServiceImpl implements LoadFeatureAcHisService {

    @Autowired
    LoadFeatureAcHisDAO loadFeatureAcHisDAO;

    @Autowired
    WeatherFeatureCityDayHisService weatherFeatureCityDayHisService;

    @Autowired
    LoadAcHisBasicService loadAcHisBasicService;

    @Autowired
    LoadFeatureAcHisService loadFeatureAcHisService;

    @Autowired
    FoundationLoadHisMonthService foundationLoadHisMonthService;

    @Autowired
    WeatherCityHisService weatherCityHisService;

    @Autowired
    CityService cityService;

    @Autowired
    private SettingSystemDAO settingSystemDAO;

    @Autowired
    private LoadCityHisService loadCityHisService;

    @Autowired
    private LoadSolutionManageEntityService loadSolutionManageEntityService;

    @Autowired
    private AirConditionerLoadBaseManageService airConditionerLoadBaseManageService;

    @Override
    public List<AcFeatureAnalyseDTO> getLoadFeatureAcHis(String cityId, String caliberId, Date startDate,
                                                         Date endDate) throws Exception {
        //日特性
        List<LoadFeatureAcHisDO> loadFeatureAcHisDOList = loadFeatureAcHisDAO
                .getLoadFeatureCityDayHisDO(cityId, startDate, endDate, caliberId);
        List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisStatDOList = weatherFeatureCityDayHisService
                .listWeatherFeatureCityDayHisDO(cityId, startDate, endDate);
        List<LoadAcHisBasicDO> loadAcHisBasicDOS = loadAcHisBasicService.getloadAcHisBasicDOList(cityId, caliberId,
            startDate, endDate);
        if (CollectionUtils.isEmpty(loadFeatureAcHisDOList)) {
            throw TsieExceptionUtils.newBusinessException("T706");
        }
        //将气象的日期和最高温放到map中
        Map<Date, BigDecimal> highestTemperatureMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(weatherFeatureCityDayHisStatDOList)) {
            Map<Date, BigDecimal> highestTemperatureDateMap = weatherFeatureCityDayHisStatDOList.stream().collect(
                    Collectors.toMap(WeatherFeatureCityDayHisDO::getDate,
                            weatherFeatureCityDayHisDO -> weatherFeatureCityDayHisDO.getHighestTemperature() == null
                                    ? new BigDecimal(0) : weatherFeatureCityDayHisDO.getHighestTemperature()));
            highestTemperatureDateMap.forEach((k, v) ->
                    highestTemperatureMap.put(k, v)
            );
        }

        List<AcFeatureAnalyseDTO> acFeatureAnalyseDTOList = new ArrayList<>();

        loadFeatureAcHisDOList.forEach(powerFeatureCityDayHisStatDO -> {
            AcFeatureAnalyseDTO acFeatureAnalyseDTO = new AcFeatureAnalyseDTO();
            BeanUtils.copyProperties(powerFeatureCityDayHisStatDO, acFeatureAnalyseDTO);
            acFeatureAnalyseDTO
                    .setHighestTemperature(highestTemperatureMap.get(powerFeatureCityDayHisStatDO.getDate()));

            acFeatureAnalyseDTOList.add(acFeatureAnalyseDTO);
        });
        List<AcFeatureAnalyseDTO> resultAll = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(loadAcHisBasicDOS)) {
            for (AcFeatureAnalyseDTO acFeatureAnalyseDTO : acFeatureAnalyseDTOList) {
                for (LoadAcHisBasicDO loadAcHisBasicDO : loadAcHisBasicDOS) {
                    if (loadAcHisBasicDO.getDate().compareTo(acFeatureAnalyseDTO.getDate()) == 0) {
                        List<BigDecimal> bigDecimals = loadAcHisBasicDO.getloadList();
                        BigDecimal bigDecimal = BigDecimalFunctions.listMax(bigDecimals);
                        acFeatureAnalyseDTO.setMaxLoad(bigDecimal);
                    }
                }
                resultAll.add(acFeatureAnalyseDTO);
            }
        }
        List<AcFeatureAnalyseDTO> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(resultAll)) {
            for (AcFeatureAnalyseDTO acFeatureAnalyseDTO : resultAll) {
                BigDecimal maxLoad = acFeatureAnalyseDTO.getMaxLoad();
                if (maxLoad != null && maxLoad.compareTo(BigDecimal.ZERO) < 0) {
                    acFeatureAnalyseDTO.setMaxLoad(BigDecimal.valueOf(300));
                }
                result.add(acFeatureAnalyseDTO);
            }
        }
        return result;
    }


    @Override
    public AcFeatureStatisticsDTO doStatisticsAcFeature(String cityId, String caliberId, Date date) throws Exception {
        //去年今日
        Date lastYear = DateUtils.addYears(date, -1);
        //日特性
        List<LoadFeatureAcHisDO> loadFeatureAcHisDOList = loadFeatureAcHisDAO
                .getLoadFeatureCityDayHisDO(cityId, date, date, caliberId);

        List<LoadFeatureAcHisDO> lastYearLoadFeatureAcHisDOList = loadFeatureAcHisDAO
                .getLoadFeatureCityDayHisDO(cityId, lastYear, lastYear, caliberId);

        cityId = cityService.findWeatherCityId(cityId);
        WeatherFeatureCityDayHisDO featureCityHisVOByDate = weatherFeatureCityDayHisService
                .findWeatherFeatureCityHisVOByDate(cityId, date);
        WeatherFeatureCityDayHisDO lastYearFeatureCityHisVOByDate = weatherFeatureCityDayHisService
                .findWeatherFeatureCityHisVOByDate(cityId, lastYear);

        List<LoadAcHisBasicDO> loadAcHisBasicDOS = loadAcHisBasicService.getloadAcHisBasicDOList(cityId, caliberId,
            date, date);
        AcFeatureStatisticsDTO acFeatureStatisticsDTO = new AcFeatureStatisticsDTO();
        if (CollectionUtils.isNotEmpty(loadFeatureAcHisDOList)) {
            LoadFeatureAcHisDO loadFeatureAcHisDO = loadFeatureAcHisDOList.get(0);
            BeanUtils.copyProperties(loadFeatureAcHisDO, acFeatureStatisticsDTO);
        }
        if (CollectionUtils.isNotEmpty(loadAcHisBasicDOS)) {
            LoadAcHisBasicDO loadAcHisBasicDO = loadAcHisBasicDOS.get(0);
            List<BigDecimal> bigDecimals = loadAcHisBasicDO.getloadList();
            BigDecimal bigDecimal = BigDecimalFunctions.listMax(bigDecimals);
            acFeatureStatisticsDTO.setMaxLoad(bigDecimal);
        }
        if (acFeatureStatisticsDTO != null) {
            BigDecimal maxLoad = acFeatureStatisticsDTO.getMaxLoad();
            if (maxLoad.compareTo(BigDecimal.ZERO) <= 0) {
                acFeatureStatisticsDTO.setMaxLoad(new BigDecimal(300));
            }
        }
        if (CollectionUtils.isNotEmpty(lastYearLoadFeatureAcHisDOList)) {
            LoadFeatureAcHisDO lastYearLoadFeatureAcHisDO = lastYearLoadFeatureAcHisDOList.get(0);
            acFeatureStatisticsDTO.setLastYearMaxLoad(lastYearLoadFeatureAcHisDO.getMaxLoad());
            acFeatureStatisticsDTO.setLastYearMinLoad(lastYearLoadFeatureAcHisDO.getMinLoad());
            acFeatureStatisticsDTO.setLastYearAvgLoad(lastYearLoadFeatureAcHisDO.getAvgLoad());
            acFeatureStatisticsDTO.setLastYearMaxLoadProportion(lastYearLoadFeatureAcHisDO.getMaxLoadProportion());
            acFeatureStatisticsDTO.setLastYearEnergy(lastYearLoadFeatureAcHisDO.getEnergy());
        }
        acFeatureStatisticsDTO.setHighestTemperature(
                Optional.ofNullable(featureCityHisVOByDate).orElse(new WeatherFeatureCityDayHisDO())
                        .getHighestTemperature());
        acFeatureStatisticsDTO.setLastYearHighestTemperature(
                Optional.ofNullable(lastYearFeatureCityHisVOByDate).orElse(new WeatherFeatureCityDayHisDO())
                        .getHighestTemperature());

        return acFeatureStatisticsDTO;
    }

    @Override
    public AcLoadCurveDTO getLoadCurve(String cityId, String caliberId, Date startDate, Date endDate) {
        AcLoadCurveDTO acLoadCurveDTO = new AcLoadCurveDTO();
        String startYear = DateUtil.getDateToStrFORMAT(startDate, "yyyy-MM-dd").split("-")[0];
        String startMonth = DateUtil.getDateToStrFORMAT(startDate, "yyyy-MM-dd").split("-")[1];
        //初始化一个长度96，值为NULL的list
        final ArrayList<BigDecimal> emptyList = new ArrayList<>(96);
        IntStream.range(0, 96).forEach(i -> emptyList.add(null));
        //查询空调负荷特性
        List<LoadAcHisBasicDO> loadAcHisBasicDOList = loadAcHisBasicService
                .getloadAcHisBasicDOList(cityId, caliberId, startDate, endDate);
        List<BigDecimal> acLoad = new ArrayList<>();
        Map<Date, LoadAcHisBasicDO> mapData = loadAcHisBasicDOList.stream()
                .collect(Collectors.toMap(LoadAcHisBasicDO::getDate, Function.identity(), (o, n) -> n));
        List<Date> listDate = DateUtil.getListBetweenDay(startDate, endDate);
        for (Date date : listDate) {
            LoadAcHisBasicDO loadAcHisBasicDO = mapData.get(date);
            if (loadAcHisBasicDO == null) {
                acLoad.addAll(emptyList);
            } else {
                acLoad.addAll(BasePeriodUtils
                        .toList(loadAcHisBasicDO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO));
            }
        }

        List<BigDecimal> baseLoad = new ArrayList<>();
        List<BigDecimal> temperature = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        listDate.forEach(date -> {
            calendar.setTime(date);
            String year = String.valueOf(calendar.get(Calendar.YEAR));
            // 湖北逻辑
            String seasonType = getSeasonType(date);
            Boolean workingDay = DateUtil.isWorkingDay(date.getTime());
            //判断前一天是工作日还是休息日
            int type = 1;
            if (!workingDay) {
                type = 2;
            }
            DateType2 dateType2 = null;
            if (type == 1){
                dateType2 = DateType2.WORKDAY;
            }else {
                dateType2 = DateType2.HOLIDAY;
            }
            List<LoadSolutionManageDO> loadSolutionManageDOList = loadSolutionManageEntityService.findAllByQueryWrapper(
                    JpaWrappers.<LoadSolutionManageDO>lambdaQuery().eq(LoadSolutionManageDO::getCityId, cityId)
                            .eq(LoadSolutionManageDO::getCaliberId, caliberId)
                            .eq(LoadSolutionManageDO::getDateType, dateType2)
                            .eq(LoadSolutionManageDO::getSolutionYear, year)
                            .eq(seasonType != null,LoadSolutionManageDO::getSeasonType,seasonType));
            if (!CollectionUtils.isEmpty(loadSolutionManageDOList)){
                for (LoadSolutionManageDO loadSolutionManageDO : loadSolutionManageDOList){
                    if (loadSolutionManageDO != null){
                        Boolean enableCurve = loadSolutionManageDO.getEnableCurve();
                        if (enableCurve != null && enableCurve){
                            String id = loadSolutionManageDO.getId();
                            try {
                                // BaseLoadCurveRespDTO baseLoadCurveRespDTO = airConditionerLoadBaseManageService.queryBaseLoadCurve(id);
                                List<FoundationLoadHisMonthDO> foundationLoadHisMonth = foundationLoadHisMonthService.getFoundationLoadHisMonth(cityId, caliberId, startYear, startMonth, dateType2.getId(), null);
                                FoundationLoadHisMonthDO foundationLoadHisMonthDO = new FoundationLoadHisMonthDO();
                                if (CollectionUtils.isNotEmpty(foundationLoadHisMonth)) {
                                    foundationLoadHisMonthDO = foundationLoadHisMonth.get(0);
                                }
                                // List<BigDecimal> dataValue = baseLoadCurveRespDTO.getDataValue();
                                baseLoad.addAll(BasePeriodUtils
                                    .toList(foundationLoadHisMonthDO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO));
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }
                    }
                }
            }
            // 温度曲线
            try {
                List<BigDecimal> tempList = weatherCityHisService
                        .find96WeatherCityHisValue(date, cityId, WeatherEnum.TEMPERATURE.getType());
                temperature.addAll(CollectionUtils.isNotEmpty(tempList) ? tempList : emptyList);
            } catch (Exception e) {
                e.printStackTrace();
            }
        });

        acLoadCurveDTO.setTemperature(temperature);
        acLoadCurveDTO.setAcLoad(acLoad);
        acLoadCurveDTO.setBaseLoad(baseLoad);
        return acLoadCurveDTO;
    }

    private String getSeasonType(Date date){
        String monthDate = DateUtil.getMonthDate(date);
        int result = Integer.parseInt(monthDate);
        if (result >= 5 && result<= 9){
            // 春季
            return "0";
        }else if (result >= 11 || result <= 2){
            // 秋季
            return "1";
        }else {
            return null;
        }
    }

    @Override
    public void doHisLoadFeatureCityDay(Date startDate, Date endDate) throws Exception {
        //空调历史负荷信息
        List<LoadAcHisBasicDO> loadAcHisBasicDOS = loadAcHisBasicService.getloadAcHisBasicDOList(null, null, startDate,
                endDate);
        if (CollectionUtils.isNotEmpty(loadAcHisBasicDOS)) {
            List<LoadCityHisDO> loadCityHisDOS = loadCityHisService.findLoadCityHisDOS(null, startDate, endDate, null);

            // 预测负荷list to map
            Map<String, LoadCityHisDO> loadCityHisVOMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(loadCityHisDOS)) {
                for (LoadCityHisDO loadCityHisDO : loadCityHisDOS) {
                    String key =
                            loadCityHisDO.getCityId() + "-" + loadCityHisDO.getCaliberId() + "-" + loadCityHisDO.getDate()
                                    .getTime();
                    loadCityHisVOMap.put(key, loadCityHisDO);
                }
            }
            for (LoadAcHisBasicDO loadAcHisBasicDO : loadAcHisBasicDOS) {
                String cityId = loadAcHisBasicDO.getCityId();
                String caliberId = loadAcHisBasicDO.getCaliberId();
                java.sql.Date date = loadAcHisBasicDO.getDate();

                String key = cityId + "-" + caliberId + "-" + date.getTime();
                LoadCityHisDO loadCityHisDO = loadCityHisVOMap.get(key);
                LoadFeatureAcHisDO loadFeatureAcHisDO = this.findStatisticsDayFeature(loadAcHisBasicDO, loadCityHisDO);
                List<LoadFeatureAcHisDO> loadFeatureCityDayHisDO = loadFeatureAcHisDAO.getLoadFeatureCityDayHisDO(
                        cityId, date, date, caliberId);
                if (CollectionUtils.isNotEmpty(loadFeatureCityDayHisDO)) {
                    LoadFeatureAcHisDO loadFeatureAcHisDO1 = loadFeatureCityDayHisDO.get(0);
                    if (loadFeatureAcHisDO1 != null && StringUtils.isNotEmpty(loadFeatureAcHisDO1.getId())) {
                        loadFeatureAcHisDAO.delete(loadFeatureAcHisDO1);
                    }
                }
                loadFeatureAcHisDAO.saveOrUpdate(loadFeatureAcHisDO);
            }
        }
    }

    @Override
    public List<MaxAcLoadDTO> getMaxAcLoadDTOList(String cityId, String caliberId, String[] yearArr, String seasonType) throws Exception {
        List<String> yearList = new ArrayList<>();
        for (int i = 0; i < yearArr.length; i++) {
            yearList.add(yearArr[i]);
        }
        List<MaxAcLoadDTO> maxAcLoadDTOList = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("MM-dd");
        // 0 度夏 1 度冬
        for(String year : yearList){
            MaxAcLoadDTO maxAcLoadDTO = new MaxAcLoadDTO();
            List<BigDecimal> acLoadList = new ArrayList<>();
            List<String> maxAirLoadOccurDateList = new ArrayList<>();
            maxAcLoadDTO.setYear(year);
            List<Date> dateList = getDateListByType(seasonType, year);
            if (dateList != null){
                List<LoadFeatureAcHisDO> loadFeatureAcHisDOList = loadFeatureAcHisService.getLoadFeatureAcHisDOS(cityId,caliberId,dateList.get(0), dateList.get(dateList.size() - 1));
                if (loadFeatureAcHisDOList != null){
                    for (LoadFeatureAcHisDO loadFeatureAcHisDO : loadFeatureAcHisDOList){
                        if (loadFeatureAcHisDO == null){
                            continue;
                        }
                        acLoadList.add(loadFeatureAcHisDO.getMaxLoad());
                        java.sql.Date sqlDate = loadFeatureAcHisDO.getDate();
                        Date date = new Date(sqlDate.getTime());
                        maxAirLoadOccurDateList.add(sdf.format(date));
                    }
                }
            }
            maxAcLoadDTO.setAcLoad(acLoadList);
            maxAcLoadDTO.setDate(maxAirLoadOccurDateList);
            maxAcLoadDTOList.add(maxAcLoadDTO);
        }
        return maxAcLoadDTOList.stream()
                .filter(t->CollectionUtils.isNotEmpty(t.getDate()) && CollectionUtils.isNotEmpty(t.getAcLoad()))
                .collect(Collectors.toList());
    }

    @Override
    public List<LoadFeatureAcHisDO> getLoadFeatureAcHisDOS(String cityId, String caliberId, Date startDate, Date endDate) throws Exception {
        return loadFeatureAcHisDAO.getLoadFeatureCityDayHisDO(cityId,startDate,endDate,caliberId);
    }

    @Override
    public List<HighestTemperatureDTO> getHighestTemperatureDTOList(String cityId, String caliberId, String[] yearArr, String seasonType) throws Exception {
        List<String> yearList = new ArrayList<>();
        for (int i = 0; i < yearArr.length; i++) {
            yearList.add(yearArr[i]);
        }
        List<HighestTemperatureDTO> highestTemperatureDTOList = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("MM-dd");
        // 0 度夏 1 度冬
        for(String year : yearList){
            HighestTemperatureDTO highestTemperatureDTO = new HighestTemperatureDTO();
            highestTemperatureDTO.setYear(year);
            List<String> highestTemperatureOccurDateList = new ArrayList<>();
            List<BigDecimal> highestTemperatureList = new ArrayList<>();
            List<Date> dateList = getDateListByType(seasonType, year);

            // 最高温度曲线
            if (dateList != null){
                List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisDOS = weatherFeatureCityDayHisService.listWeatherFeatureCityDayHisDO(cityId, dateList.get(0), dateList.get(dateList.size() - 1));
                if (weatherFeatureCityDayHisDOS != null){
                    for (WeatherFeatureCityDayHisDO weatherFeatureCityDayHisDO : weatherFeatureCityDayHisDOS){
                        if (weatherFeatureCityDayHisDO == null){
                            continue;
                        }
                        highestTemperatureList.add(weatherFeatureCityDayHisDO.getHighestTemperature());
                        java.sql.Date sqlDate = weatherFeatureCityDayHisDO.getDate();
                        Date date = new Date(sqlDate.getTime());
                        highestTemperatureOccurDateList.add(sdf.format(date));
                    }
                }
            }
            highestTemperatureDTO.setTemperature(highestTemperatureList);
            highestTemperatureDTO.setDate(highestTemperatureOccurDateList);
            highestTemperatureDTOList.add(highestTemperatureDTO);
        }
        return highestTemperatureDTOList.stream()
                .filter(t->CollectionUtils.isNotEmpty(t.getDate()) && CollectionUtils.isNotEmpty(t.getTemperature()))
                .collect(Collectors.toList());
    }

    @Override
    public List<StatisticsAcDTO> getStatisticsAcDTOList(String cityId, String caliberId,String[] yearArr, String seasonType) throws Exception {
        List<String> yearList = new ArrayList<>();
        for (int i = 0; i < yearArr.length; i++) {
            yearList.add(yearArr[i]);
        }
        List<StatisticsAcDTO> statisticsAcDTOList = new ArrayList<>();


        // 0 度夏 1 度冬
        for(String year : yearList){
            Map<BigDecimal,String> maxLoadMap = new HashMap<>();
            Map<BigDecimal,String> highestTemperatureMap = new HashMap<>();
            StatisticsAcDTO statisticsAcDTO = new StatisticsAcDTO();
            statisticsAcDTO.setYear(year);
            List<Date> dateList = getDateListByType(seasonType, year);
            if (dateList != null){
                List<LoadFeatureAcHisDO> loadFeatureAcHisDOList = loadFeatureAcHisService.getLoadFeatureAcHisDOS(cityId,caliberId,dateList.get(0), dateList.get(dateList.size() - 1));
                if (loadFeatureAcHisDOList != null){
                    for (LoadFeatureAcHisDO loadFeatureAcHisDO : loadFeatureAcHisDOList){
                        if (loadFeatureAcHisDO != null){
                            String dateStr = DateUtils.date2String(new Date(loadFeatureAcHisDO.getDate().getTime()), DateFormatType.SIMPLE_DATE_FORMAT_STR);
                            maxLoadMap.put(loadFeatureAcHisDO.getMaxLoad(),dateStr + " " + loadFeatureAcHisDO.getMaxTime());
                        }
                    }
                    BigDecimal maxLoad = getMaxKey(maxLoadMap);
                    if (maxLoad != null){
                        String maxAirLoadOccurTime = maxLoadMap.get(maxLoad);
                        statisticsAcDTO.setMaxAirLoad(maxLoad);
                        statisticsAcDTO.setMaxAirLoadTime(maxAirLoadOccurTime);
                    }

                }
                List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisDOS = weatherFeatureCityDayHisService.listWeatherFeatureCityDayHisDO(cityId, dateList.get(0), dateList.get(dateList.size() - 1));
                if (weatherFeatureCityDayHisDOS != null){
                    for (WeatherFeatureCityDayHisDO weatherFeatureCityDayHisDO : weatherFeatureCityDayHisDOS){
                        if (weatherFeatureCityDayHisDO != null){
                            java.sql.Date sqlDate = weatherFeatureCityDayHisDO.getDate();
                            Date date = new Date(sqlDate.getTime());
                            highestTemperatureMap.put(weatherFeatureCityDayHisDO.getHighestTemperature(),DateUtils.date2String(date, DateFormatType.SIMPLE_DATE_FORMAT_STR) + " " + weatherFeatureCityDayHisDO.getHighestTemperatureTime());
                        }
                    }
                    BigDecimal highestTemperature = getMaxKey(highestTemperatureMap);
                    if (highestTemperature != null){
                        String highestTemperatureOccurTime = highestTemperatureMap.get(highestTemperature);
                        statisticsAcDTO.setHighestTemperature(highestTemperature);
                        statisticsAcDTO.setHighestTemperatureTime(highestTemperatureOccurTime);
                    }
                }
            }
            statisticsAcDTOList.add(statisticsAcDTO);
        }
        return statisticsAcDTOList;
    }

    private BigDecimal getMaxKey(Map<BigDecimal,String> map){
        List<BigDecimal> list = new ArrayList<>();
        Set<BigDecimal> bigDecimals = map.keySet();
        for (BigDecimal key : bigDecimals){
            if (key != null){
                list.add(key);
            }
        }
        Optional<BigDecimal> max = list.stream().max((t1, t2) -> t1.compareTo(t2));
        if (max != null && max.isPresent()){
            return max.get();
        }
        return null;
    }

    /**
     * 根据分析类型获取日期集合
     * @param analyzeType 0 度夏 1 度冬
     * @return
     */
    private List<Date> getDateListByType(String analyzeType,String year) {
        if (analyzeType.equals("0")){
            Date startDate = DateUtil.getDate(year+"-05-01","yyyy-MM-dd");
            Date endDate = DateUtil.getDate(year+"-09-30","yyyy-MM-dd");
            return DateUtil.getListBetweenDay(startDate,endDate);
        }
        return null;
    }

    private LoadFeatureAcHisDO findStatisticsDayFeature(LoadAcHisBasicDO loadAcHisBasicDO, LoadCityHisDO loadCityHisDO)
            throws Exception {
        if (loadAcHisBasicDO == null) {
            return null;
        }
        List<BigDecimal> peaks = new ArrayList<BigDecimal>();
        List<String> peakSectionTimes = settingSystemDAO.getPeakSectionTime();
        // 最小负荷发生时刻
        String minTime = null;
        // 最大负荷发生时刻
        String maxTime = null;
        Map<String, BigDecimal> loadMap = BasePeriodUtils.toMap(loadAcHisBasicDO, Constants.LOAD_CURVE_POINT_NUM,
                Constants.LOAD_CURVE_START_WITH_ZERO);
        List<BigDecimal> loadList = BasePeriodUtils.toList(loadAcHisBasicDO, Constants.LOAD_CURVE_POINT_NUM,
                Constants.LOAD_CURVE_START_WITH_ZERO);
        Map<String, BigDecimal> maxMixAvg = BasePeriodUtils.getMaxMinAvg(loadList, 4);
        for (String column : loadMap.keySet()) {
            BigDecimal load = loadMap.get(column);
            if (null != load) {
                column = column.substring(1);
                if (load.compareTo(maxMixAvg.get("max")) == 0) {
                    maxTime = column;
                }
                if (peakSectionTimes != null && peakSectionTimes.contains(column)) {
                    peaks.add(load);
                }
                if (load.compareTo(maxMixAvg.get("min")) == 0) {
                    minTime = column;
                }
            }
        }
        LoadFeatureAcHisDO result = new LoadFeatureAcHisDO();
        result.setDate(loadAcHisBasicDO.getDate());
        result.setCityId(loadAcHisBasicDO.getCityId());
        result.setCaliberId(loadAcHisBasicDO.getCaliberId());
//        result.setAlgorithmId(null);
//        result.setAlgorithmName(null);
        result.setMaxLoad(maxMixAvg.get("max"));
        result.setMinLoad(maxMixAvg.get("min"));
        result.setAvgLoad(maxMixAvg.get("avg"));
        if (maxTime != null) {
            result.setMaxTime(new StringBuffer(maxTime).insert(2, ":").toString());
        }
        if (minTime != null) {
            result.setMinTime(new StringBuffer(minTime).insert(2, ":").toString());
        }
//        // 峰谷差 = 日最大负荷 – 日最小负荷
//        result.setDifferent(BigDecimalUtils.sub(result.getMaxLoad(), result.getMinLoad()));
//        // 峰谷差率 = （日最大负荷 – 日最小负荷）/日最大负荷
//        if (result.getMaxLoad().compareTo(BigDecimal.ZERO) != 0) {
//            result.setGradient(BigDecimalUtils.divide(result.getDifferent(), result.getMaxLoad(), 4));
//        }
        // 积分电量 = 96点负荷之和/4
//        result.setIntegralLoad(BigDecimalUtils.divide(BigDecimalUtils.addAllValue(loadList), new BigDecimal(4), 4));
//        // 段峰电量 = 尖峰时段在数据库setting_system_init中，默认值为08：00~22:00
//        result.setPeakSectionLoad(BigDecimalUtils.divide(BigDecimalUtils.addAllValue(peaks), new BigDecimal(4), 4));
        String time = result.getMaxTime() == null ? "" : result.getMaxTime().replace(":", "");
        BigDecimal value = null;
        try {
            Method m = loadCityHisDO.getClass().getMethod("getT" + time);
            value = (BigDecimal) m.invoke(loadCityHisDO);
        } catch (NoSuchMethodException e) {
            log.error("空调负荷特性定时：无法获取反射属性：【{}】", e);
        } catch (NullPointerException e) {
            log.error("空调负荷特性定时：无法获取反射属性：【{}】", e);
        }

        if (value != null && value.compareTo(BigDecimal.ZERO) != 0) {
            // 最大空调负荷占比=（空调负荷最大值/空调负荷最大值对应时刻的总负荷）*100%
            result.setMaxLoadProportion(result.getMaxLoad().divide(value, 4, RoundingMode.HALF_UP));
        }
        // 累积电量=平均负荷×24
        result.setEnergy(result.getAvgLoad() == null ? null : result.getAvgLoad().multiply(new BigDecimal(24)));
        return result;
    }

}
