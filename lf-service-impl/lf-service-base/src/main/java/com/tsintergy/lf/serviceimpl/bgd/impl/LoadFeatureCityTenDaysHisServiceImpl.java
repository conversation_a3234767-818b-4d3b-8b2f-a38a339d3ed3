package com.tsintergy.lf.serviceimpl.bgd.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsintergy.lf.serviceapi.base.bgd.api.LoadFeatureCityTenDaysHisService;
import com.tsintergy.lf.serviceapi.base.bgd.pojo.LoadFeatureCityTenDaysHisServiceDO;
import com.tsintergy.lf.serviceimpl.bgd.dao.LoadFeatureCityTenDaysHisServiceDAO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Description: TODO <br>
 *
 * <AUTHOR>
 * @create 2023-02-16
 * @since 1.0.0
 */
@Service("loadFeatureCityTenDaysHisService")
public class LoadFeatureCityTenDaysHisServiceImpl extends BaseServiceImpl implements LoadFeatureCityTenDaysHisService {

    @Autowired
    LoadFeatureCityTenDaysHisServiceDAO loadFeatureCityTenDaysHisServiceDAO;

    @Override
    public List<LoadFeatureCityTenDaysHisServiceDO> findLoadFeatureCityTenDaysHisServiceDOs(String cityId,
        String caliberId, String year, String month, String type) {
        return loadFeatureCityTenDaysHisServiceDAO.findAll(JpaWrappers.<LoadFeatureCityTenDaysHisServiceDO>lambdaQuery()
            .eq(!StringUtils.isEmpty(year), LoadFeatureCityTenDaysHisServiceDO::getYear, year)
            .eq(!StringUtils.isEmpty(month), LoadFeatureCityTenDaysHisServiceDO::getMonth, month)
            .eq(!StringUtils.isEmpty(type), LoadFeatureCityTenDaysHisServiceDO::getType, type)
            .eq(!StringUtils.isEmpty(cityId), LoadFeatureCityTenDaysHisServiceDO::getCityId, cityId)
            .eq(!StringUtils.isEmpty(caliberId), LoadFeatureCityTenDaysHisServiceDO::getCaliberId, caliberId)
        );
    }
    @Override
    public List<LoadFeatureCityTenDaysHisServiceDO> findLoadFeatureCityTenDaysHisServiceDOs(String cityId, String caliberId, String year, List<String> monthList) {
        return loadFeatureCityTenDaysHisServiceDAO.findAll(JpaWrappers.<LoadFeatureCityTenDaysHisServiceDO>lambdaQuery()
            .eq(!StringUtils.isEmpty(year), LoadFeatureCityTenDaysHisServiceDO::getYear, year)
            .in(!CollectionUtils.isEmpty(monthList), LoadFeatureCityTenDaysHisServiceDO::getMonth, monthList)
            .eq(!StringUtils.isEmpty(cityId), LoadFeatureCityTenDaysHisServiceDO::getCityId, cityId)
            .eq(!StringUtils.isEmpty(caliberId), LoadFeatureCityTenDaysHisServiceDO::getCaliberId, caliberId)
        );
    }
    @Override
    public List<LoadFeatureCityTenDaysHisServiceDO> findLoadFeatureCityTenDaysHisServiceDOs(String cityId, String caliberId, List<String> yearList, String monthList) {
        return loadFeatureCityTenDaysHisServiceDAO.findAll(JpaWrappers.<LoadFeatureCityTenDaysHisServiceDO>lambdaQuery()
                .in(!CollectionUtils.isEmpty(yearList), LoadFeatureCityTenDaysHisServiceDO::getYear, yearList)
                .eq(!StringUtils.isEmpty(monthList), LoadFeatureCityTenDaysHisServiceDO::getMonth, monthList)
                .eq(!StringUtils.isEmpty(cityId), LoadFeatureCityTenDaysHisServiceDO::getCityId, cityId)
                .eq(!StringUtils.isEmpty(caliberId), LoadFeatureCityTenDaysHisServiceDO::getCaliberId, caliberId)
        );
    }

    @Override
    public List<LoadFeatureCityTenDaysHisServiceDO> findLoadFeatureCityTenDaysHisServiceDOs(List<String> cityIds,
                                                                                            String caliberId, String year, String month) {
        return loadFeatureCityTenDaysHisServiceDAO.findAll(JpaWrappers.<LoadFeatureCityTenDaysHisServiceDO>lambdaQuery()
                .eq(!StringUtils.isEmpty(year), LoadFeatureCityTenDaysHisServiceDO::getYear, year)
                .eq(!StringUtils.isEmpty(month), LoadFeatureCityTenDaysHisServiceDO::getMonth, month)
                .in(!CollectionUtils.isEmpty(cityIds), LoadFeatureCityTenDaysHisServiceDO::getCityId, cityIds)
                .eq(!StringUtils.isEmpty(caliberId), LoadFeatureCityTenDaysHisServiceDO::getCaliberId, caliberId)
        );
    }

    @Override
    public List<LoadFeatureCityTenDaysHisServiceDO> findByRangeCondition(String cityId, String caliberId, String startYM, String endYM) {
        List<LoadFeatureCityTenDaysHisServiceDO> tenDaysHisServiceDOS = loadFeatureCityTenDaysHisServiceDAO.findAll(JpaWrappers.<LoadFeatureCityTenDaysHisServiceDO>lambdaQuery()
                .ge(!StringUtils.isEmpty(startYM), LoadFeatureCityTenDaysHisServiceDO::getYear, startYM.substring(0, 4))
                .le(!StringUtils.isEmpty(endYM), LoadFeatureCityTenDaysHisServiceDO::getYear, endYM.substring(0, 4))
                .eq(!StringUtils.isEmpty(cityId), LoadFeatureCityTenDaysHisServiceDO::getCityId, cityId)
                .eq(!StringUtils.isEmpty(caliberId), LoadFeatureCityTenDaysHisServiceDO::getCaliberId, caliberId)
        );

        List<LoadFeatureCityTenDaysHisServiceDO> loadFeatureCityMonthHisVOs = new ArrayList<>();
        for (LoadFeatureCityTenDaysHisServiceDO loadFeatureCityMonthHisVO : tenDaysHisServiceDOS) {
            if ((loadFeatureCityMonthHisVO.getYear() + "-" + loadFeatureCityMonthHisVO.getMonth()).compareTo(startYM) > -1
                    && (loadFeatureCityMonthHisVO.getYear() + "-" + loadFeatureCityMonthHisVO.getMonth()).compareTo(endYM) < 1) {
                loadFeatureCityMonthHisVOs.add(loadFeatureCityMonthHisVO);
            }
        }
        return loadFeatureCityMonthHisVOs;
    }
}
