package com.tsintergy.lf.serviceimpl.weather.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityHisRpaService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisRpaDO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherCityHisRpaDAO;
import java.util.List;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2024-12-04
 * @since 1.0.0
 */
@Service("weatherCityHisRpaService")
public class WeatherCityHisRpaServiceImpl implements WeatherCityHisRpaService {

    @Autowired
    WeatherCityHisRpaDAO weatherCityHisRpaDAO;

    @Override
    public void doSaveOrUpdate(List<WeatherCityHisRpaDO> weatherCityHisRpaDOList) {
        for (WeatherCityHisRpaDO weatherCityHisRpaDO : weatherCityHisRpaDOList) {
            List<WeatherCityHisRpaDO> all = weatherCityHisRpaDAO.findAll(
                JpaWrappers.<WeatherCityHisRpaDO>lambdaQuery()
                    .eq(WeatherCityHisRpaDO::getCityId, weatherCityHisRpaDO.getCityId())
                    .eq(WeatherCityHisRpaDO::getStationId, weatherCityHisRpaDO.getStationId())
                    .eq(WeatherCityHisRpaDO::getType, weatherCityHisRpaDO.getType())
                    .eq(WeatherCityHisRpaDO::getDate, weatherCityHisRpaDO.getDate()));
            if (CollectionUtils.isEmpty(all)) {
                weatherCityHisRpaDAO.save(weatherCityHisRpaDO);
            } else {
                weatherCityHisRpaDO.setId(all.get(0).getId());
                weatherCityHisRpaDAO.saveOrUpdateByTemplate(weatherCityHisRpaDO);
            }
        }
    }
}
