/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.trade.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsintergy.lf.serviceapi.base.trade.api.TradeLoadFeatureDayHisService;
import com.tsintergy.lf.serviceapi.base.trade.pojo.TradeLoadFeatureDayHisDO;
import com.tsintergy.lf.serviceimpl.trade.dao.TradeLoadFeatureDayHisDAO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/9/2 15:07
 * @Version: 1.0.0
 */
@Service("tradeLoadFeatureDayHisService")
public class TradeLoadFeatureDayHisServiceImpl implements TradeLoadFeatureDayHisService {

    @Autowired
    TradeLoadFeatureDayHisDAO tradeLoadFeatureDayHisDAO;

    @Override
    public void saveOrUpdate(TradeLoadFeatureDayHisDO tradeLoadFeatureDayHisDO) {
        List<TradeLoadFeatureDayHisDO> list = findTradeLoadFeatureDayHisDO(
            tradeLoadFeatureDayHisDO.getDate(), tradeLoadFeatureDayHisDO.getDate(),
            tradeLoadFeatureDayHisDO.getTradeCode());

        if (CollectionUtils.isEmpty(list)){
            tradeLoadFeatureDayHisDAO.save(tradeLoadFeatureDayHisDO);
        }else{
            TradeLoadFeatureDayHisDO old = list.get(0);
            tradeLoadFeatureDayHisDO.setId(old.getId());
            tradeLoadFeatureDayHisDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            tradeLoadFeatureDayHisDAO.saveOrUpdateByTemplate(tradeLoadFeatureDayHisDO);
        }
    }

    @Override
    public List<TradeLoadFeatureDayHisDO> findTradeLoadFeatureDayHisDO(Date startDate, Date endDate, String codeId) {

        return tradeLoadFeatureDayHisDAO.findAll(JpaWrappers.<TradeLoadFeatureDayHisDO>lambdaQuery()
                .eq(!StringUtils.isEmpty(codeId), TradeLoadFeatureDayHisDO::getTradeCode, codeId)
                .ge(startDate != null, TradeLoadFeatureDayHisDO::getDate, startDate)
                .le(endDate != null, TradeLoadFeatureDayHisDO::getDate, endDate)
        );
    }

    @Override
    public List<TradeLoadFeatureDayHisDO> findTradeLoadFeatureDayHisDO(String cityId, Date startDate, Date endDate, List<String> codeIds) {
        return tradeLoadFeatureDayHisDAO.findAll(JpaWrappers.<TradeLoadFeatureDayHisDO>lambdaQuery()
                .eq(!CollectionUtils.isEmpty(codeIds), TradeLoadFeatureDayHisDO::getTradeCode, codeIds)
                .ge(startDate != null, TradeLoadFeatureDayHisDO::getDate, startDate)
                .le(endDate != null, TradeLoadFeatureDayHisDO::getDate, endDate)
        );
    }
}