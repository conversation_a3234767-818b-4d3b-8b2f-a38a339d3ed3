package com.tsintergy.lf.serviceimpl.ultra.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsintergy.lf.serviceapi.base.ultra.api.LoadCityFcUltraBatchService;
import com.tsintergy.lf.serviceapi.base.ultra.pojo.LoadCityFcUltraBatchDO;
import com.tsintergy.lf.serviceimpl.ultra.dao.LoadCityFcUltraBatchDAO;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * @Description
 * @Date 2023/1/10 10:42
 * <AUTHOR>
 **/
@Service
public class LoadCityFcUltraBatchServiceImpl implements LoadCityFcUltraBatchService {

    @Autowired
    LoadCityFcUltraBatchDAO loadCityFcUltraBatchDAO;

    @Override
    public List<LoadCityFcUltraBatchDO> listLoadCityFcUltraDOS(String cityId, String caliberId, String algorithmId, Integer tDelta, Integer periodType, String startPeriodTime, String endPeriodTime, Date startDate, Date endDate) {
        return loadCityFcUltraBatchDAO.findAll(
                JpaWrappers.<LoadCityFcUltraBatchDO>query()
                        .eq(!StringUtils.isBlank(cityId), "cityId", cityId)
                        .eq(!Objects.isNull(tDelta), "tvMeta.delta", tDelta)
                        .eq(!StringUtils.isBlank(caliberId), "caliberId", caliberId)
                        .eq(!StringUtils.isBlank(algorithmId), "algorithmId", algorithmId)
                        .ge(!Objects.isNull(periodType) && periodType == 2 && !StringUtils.isBlank(startPeriodTime), "startTime", startPeriodTime)
                        .le(!Objects.isNull(periodType) && periodType == 2 && !StringUtils.isBlank(endPeriodTime), "startTime", endPeriodTime)
                        .ge(!Objects.isNull(startDate), "dateTime", startDate)
                        .le(!Objects.isNull(endDate), "dateTime", endDate)

        );
    }

    @Override
    public LoadCityFcUltraBatchDO getLoadCityFcUltraDOById(String id) {
        return loadCityFcUltraBatchDAO.findOne(
                JpaWrappers.<LoadCityFcUltraBatchDO>lambdaQuery()
                        .eq(!StringUtils.isBlank(id), LoadCityFcUltraBatchDO::getId, id)
        );
    }

    @Override
    public List<LoadCityFcUltraBatchDO> findLoadCityFcUltraDOS(String startTime, Date date, String cityId, String caliberId, String algorithmId, Integer delta) {
        return loadCityFcUltraBatchDAO.findAll(
                JpaWrappers.<LoadCityFcUltraBatchDO>query()
                        .eq(startTime != null, "startTime", startTime)
                        .eq(date != null, "dateTime", date)
                        .eq(cityId != null, "cityId", cityId)
                        .eq(caliberId != null, "caliberId", caliberId)
                        .eq(algorithmId != null, "algorithmId", algorithmId)
                        .eq(delta != null, "t_delta", delta)
        );
    }

    @Override
    public LoadCityFcUltraBatchDO findUltraData(String startTime, Date date, String cityId, String caliberId, String algorithmId, Integer delta) {
        List<LoadCityFcUltraBatchDO> loadCityFcUltraDOS = findLoadCityFcUltraDOS(startTime, date, cityId, caliberId, algorithmId, delta);
        if (!CollectionUtils.isEmpty(loadCityFcUltraDOS)){
            return loadCityFcUltraDOS.get(0);
        }
        return null;
    }

    @Override
    public void doSaveOrUpdate(LoadCityFcUltraBatchDO LoadCityFcUltraBatchDO) {
        loadCityFcUltraBatchDAO.saveOrUpdateByTemplate(LoadCityFcUltraBatchDO);
    }
}
