
package com.tsintergy.lf.serviceimpl.ultra.impl;


import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.service.BaseFacadeServiceImpl;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.ultra.api.LoadFeatureCityFcUltraService;
import com.tsintergy.lf.serviceapi.base.ultra.api.UltraLoadAccuracyYearService;
import com.tsintergy.lf.serviceapi.base.ultra.dto.AccuracyDayUnitDTO;
import com.tsintergy.lf.serviceapi.base.ultra.dto.AccuracyLowDetailsMonthDTO;
import com.tsintergy.lf.serviceapi.base.ultra.dto.AccuracyMonthDTO;
import com.tsintergy.lf.serviceapi.base.ultra.dto.AccuracyMonthUltraData;
import com.tsintergy.lf.serviceapi.base.ultra.dto.AccuracyYearUltraData;
import com.tsintergy.lf.serviceapi.base.ultra.pojo.LoadFeatureCityMonthUltraFcDO;
import com.tsintergy.lf.serviceapi.base.ultra.pojo.LoadFeatureCityYearUltraFcDO;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityHisService;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Slf4j
@Service("loadAccuracyYearService")
public class LoadAccuracyYearServiceImpl extends BaseFacadeServiceImpl implements UltraLoadAccuracyYearService {

    private final Integer MONTH_TYPE = 1;

    @Autowired
    private LoadFeatureCityFcUltraService loadFeatureCityFcUltraService;

    @Autowired
    private WeatherCityHisService weatherCityHisService;

    @Autowired
    private WeatherCityFcService weatherCityFcService;

    @Autowired
    private CityService cityService;

    @Override
    public List<AccuracyMonthDTO> getLoadHistory(AccuracyYearUltraData data) throws Exception {
        List<LoadFeatureCityYearUltraFcDO> yearUltraFcDOS = loadFeatureCityFcUltraService
            .findLoadFeatureCityYearUltraFcDOS(data.getCityId(), data.getStartYear(), data.getEndYear(),
                data.getCaliberId(), data.getAlgorithmId(), data.getTimeSpan(), Constants.ULTRA_FORECAST_1);
        return process(yearUltraFcDOS);
    }

    @Override
    public List<AccuracyDayUnitDTO> getTimeAccuracy(AccuracyMonthUltraData data) {
        List<AccuracyDayUnitDTO> resultList = new ArrayList<>();
        Date date = data.getDate();
        String year = DateUtils.date2String(date, DateFormatType.SIMPLE_DATE_FORMAT_YEAR_MON).substring(0, 4);
        Date start = DateUtil.getYearFirst(Integer.valueOf(year));
        Date end = DateUtil.getYearEnd(year);
        List<LoadFeatureCityMonthUltraFcDO> loadFeatureCityMonthUltraFcDOS = loadFeatureCityFcUltraService
            .findLoadFeatureCityMonthUltraFcDOS(data.getCityId(), start, end, data.getCaliberId(),
                data.getAlgorithmId(), data.getTimeSpan(), Constants.ULTRA_FORECAST_1);
        if (CollectionUtils.isEmpty(loadFeatureCityMonthUltraFcDOS)) {
            return null;
        }
        for (LoadFeatureCityMonthUltraFcDO one : loadFeatureCityMonthUltraFcDOS) {
            AccuracyDayUnitDTO result = new AccuracyDayUnitDTO();
            result.setDate(
                DateUtils.date2String(one.getDateTime(), DateFormatType.SIMPLE_DATE_FORMAT_YEAR_MON).substring(4)
                    + "月");
            result.setAccuracy(one.getAvgAccuracy());
            resultList.add(result);
        }
        return resultList;
    }

    @Override
    public AccuracyLowDetailsMonthDTO getAccuracyLowDetails(AccuracyMonthUltraData data) throws Exception {
        AccuracyLowDetailsMonthDTO result = new AccuracyLowDetailsMonthDTO();
        Date date = data.getDate();
        LoadFeatureCityMonthUltraFcDO monthUltraFcDO = this.loadFeatureCityFcUltraService
            .findLoadFeatureCityMonthUltraFcDO(data.getCityId(), date, data.getCaliberId(), data.getAlgorithmId(),
                data.getTimeSpan());
        BeanUtils.copyProperties(monthUltraFcDO, result);
        result.setDateTime(DateUtils.date2String(monthUltraFcDO.getDateTime(), DateFormatType.YEAR_MONTH_STR));
        return result;
    }


    /**
     * 结果对象转换
     *
     * @param ultraFcDOS 数据库查询的准确率特性数据
     */
    private List<AccuracyMonthDTO> process(List<LoadFeatureCityYearUltraFcDO> ultraFcDOS) {
        List<AccuracyMonthDTO> resultList = new ArrayList<>();
        for (LoadFeatureCityYearUltraFcDO src : ultraFcDOS) {
            AccuracyMonthDTO result = new AccuracyMonthDTO();
            BeanUtils.copyProperties(src, result);
            result.setYearMonth(src.getYear());
            result.setMinAccuracyTime(src.getMinAccuracyTime().substring(0,7));
            resultList.add(result);
        }
        return resultList;
    }

}
