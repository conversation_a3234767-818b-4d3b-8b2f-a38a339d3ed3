package com.tsintergy.lf.serviceimpl.load.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.math.BigDecimalFunctions;
import com.tsintergy.aif.tool.core.utils.date.DateUtil;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityUserAddHisService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureUserAddDayHisService;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadUserAddTypicalDTO;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadUserFeatureDTO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityOrderHisDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureUserAddDayHisDO;
import com.tsintergy.lf.serviceimpl.load.dao.LoadFeatureUserAddDayHisDAO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class LoadFeatureUserAddDayHisServiceImpl implements LoadFeatureUserAddDayHisService {

    @Autowired
    LoadFeatureUserAddDayHisDAO loadFeatureUserAddDayHisDAO;

    @Autowired
    LoadCityUserAddHisService loadCityUserAddHisService;

    @Override
    public List<LoadFeatureUserAddDayHisDO> findByDate(String cityId, String caliberId, Date startDate, Date endDate) {
        return loadFeatureUserAddDayHisDAO.findAll(JpaWrappers. <LoadFeatureUserAddDayHisDO>lambdaQuery()
                .eq(StringUtils.isNotBlank(cityId), LoadFeatureUserAddDayHisDO::getCityId,cityId)
                .eq(StringUtils.isNotBlank(caliberId),LoadFeatureUserAddDayHisDO::getCaliberId,caliberId)
                .ge(startDate!= null,LoadFeatureUserAddDayHisDO::getDate,startDate)
                .le(endDate!= null,LoadFeatureUserAddDayHisDO::getDate,endDate));
    }

    @Override
    public List<LoadUserFeatureDTO> getUserWarn(String cityId, String caliberId, Date startDate, Date endDate) {
        List<LoadUserFeatureDTO> loadUserFeatureDTOS = new ArrayList<>();
        List<LoadFeatureUserAddDayHisDO> loadFeatureUserAddDayHisDOS = findByDate(cityId, caliberId, startDate, endDate);
        Map<java.sql.Date, List<LoadFeatureUserAddDayHisDO>> map = new HashMap<>();
        if (CollUtil.isNotEmpty(loadFeatureUserAddDayHisDOS)){
           map = loadFeatureUserAddDayHisDOS.stream().collect(Collectors.groupingBy(LoadFeatureUserAddDayHisDO::getDate));
        }
        List<LoadFeatureUserAddDayHisDO> sameList = findByDate(cityId, caliberId, DateUtils.addDays(startDate,-7), DateUtils.addDays(endDate,-7));
        Map<java.sql.Date, List<LoadFeatureUserAddDayHisDO>> sameMap = new HashMap<>();
        if (CollUtil.isNotEmpty(sameList)){
            sameMap = sameList.stream().collect(Collectors.groupingBy(LoadFeatureUserAddDayHisDO::getDate));
        }
        List<Date> dateList = DateUtil.getListBetweenDay(startDate, endDate);
        for (Date date : dateList) {
            LoadUserFeatureDTO loadUserFeatureDTO = new LoadUserFeatureDTO();
            loadUserFeatureDTO.setDate(DateUtil.date2String(date, DateFormatType.SIMPLE_DATE_FORMAT_STR));
            List<LoadFeatureUserAddDayHisDO> data = map.get(date);
            List<LoadFeatureUserAddDayHisDO> sameData = sameMap.get(DateUtils.addDays(date,-7));
            if (CollUtil.isNotEmpty(data)){
                loadUserFeatureDTO.setEnergy(data.get(0).getEnergy());
                loadUserFeatureDTO.setMaxLoad(data.get(0).getMaxLoad());
                loadUserFeatureDTO.setMaxLoadTime(data.get(0).getMaxTime());
            }
            if (CollUtil.isNotEmpty(sameData)){
                loadUserFeatureDTO.setLastEnergy(sameData.get(0).getEnergy());
                loadUserFeatureDTO.setLastMaxLoad(sameData.get(0).getMaxLoad());
                loadUserFeatureDTO.setLastMaxLoadTime(sameData.get(0).getMaxTime());
            }
            List<LoadFeatureUserAddDayHisDO> list = findByDate(cityId, caliberId, DateUtils.addDays(date,-7), DateUtils.addDays(date,-1));
            if (CollUtil.isNotEmpty(list)){
                List<BigDecimal> maxLoadList = list.stream().map(LoadFeatureUserAddDayHisDO::getMaxLoad).collect(Collectors.toList());
                List<BigDecimal> energyList = list.stream().map(LoadFeatureUserAddDayHisDO::getEnergy).collect(Collectors.toList());
                loadUserFeatureDTO.setLastAvgLoad(BigDecimalFunctions.listAvg(maxLoadList));
                loadUserFeatureDTO.setLastAvgEnergy(BigDecimalFunctions.listAvg(energyList));
                if (loadUserFeatureDTO.getMaxLoad()!= null){
                    loadUserFeatureDTO.setAvgLoadChange(BigDecimalFunctions.subtract(loadUserFeatureDTO.getMaxLoad(),loadUserFeatureDTO.getLastAvgLoad()));
                }
                if (loadUserFeatureDTO.getEnergy()!= null){
                    loadUserFeatureDTO.setAvgEnergyChange(BigDecimalFunctions.subtract(loadUserFeatureDTO.getEnergy(),loadUserFeatureDTO.getLastAvgEnergy()));
                }
            }
            if (loadUserFeatureDTO.getMaxLoad()!= null&&loadUserFeatureDTO.getLastMaxLoad()!= null){
                loadUserFeatureDTO.setMaxLoadChange(BigDecimalFunctions.subtract(loadUserFeatureDTO.getMaxLoad(),loadUserFeatureDTO.getLastMaxLoad()));
            }
            if (loadUserFeatureDTO.getEnergy()!= null&&loadUserFeatureDTO.getLastEnergy()!= null){
                loadUserFeatureDTO.setEnergyChange(BigDecimalFunctions.subtract(loadUserFeatureDTO.getEnergy(),loadUserFeatureDTO.getLastEnergy()));
            }
            //偏移量
            if (loadUserFeatureDTO.getMaxLoadTime()!= null&&loadUserFeatureDTO.getLastMaxLoadTime()!= null){
                loadUserFeatureDTO.setDeviation(getDeviation(loadUserFeatureDTO.getMaxLoadTime(),loadUserFeatureDTO.getLastMaxLoadTime()));
            }
            loadUserFeatureDTOS.add(loadUserFeatureDTO);
        }
        return loadUserFeatureDTOS;
    }

    private Integer getDeviation(String maxTime,String lastMaxTime){
        List<String> columns = ColumnUtil.getColumns(96, Constants.LOAD_CURVE_START_WITH_ZERO, false);
        Map<String,Integer> map = new HashMap<>();
       for (int i =1;i<columns.size()+1;i++){
           map.put(columns.get(i-1),i);
       }
        Integer num = map.get(maxTime.replace(":", ""));
        Integer lastNum = map.get(lastMaxTime.replace(":", ""));
        return num-lastNum;
    }


}
