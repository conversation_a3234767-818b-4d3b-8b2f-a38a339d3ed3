package com.tsintergy.lf.serviceimpl.bgd.impl;

import com.alibaba.nacos.common.utils.MapUtils;
import com.tsieframework.core.base.math.BigDecimalFunctions;
import com.tsieframework.core.base.service.BaseFacadeServiceImpl;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.lf.core.constants.AlgorithmConstants;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.AccuracyAssessDTO;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcService;
import com.tsintergy.lf.serviceapi.base.forecast.api.TenDaysLoadForecastService;
import com.tsintergy.lf.serviceapi.base.forecast.dto.*;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherEnum;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureStatService;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadFeatureFcDTO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcAdapterService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityDayFcService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityDayHisService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayFcDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayHisDO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherCityFcDAO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherCityHisDAO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description: 十日负荷预测
 *
 * <AUTHOR>
 * @create 2023-03-06
 * @since 1.0.0
 */
@Service("tenDaysLoadForecastService")
public class TenDaysLoadForecastServiceImpl extends BaseFacadeServiceImpl implements TenDaysLoadForecastService {

    @Autowired
    private LoadCityFcService loadCityFcService;

    @Autowired
    private LoadCityHisService loadCityHisService;

    @Autowired
    private LoadFeatureStatService loadFeatureStatService;

    @Autowired
    private WeatherFeatureCityDayFcService weatherFeatureCityDayFcService;

    @Autowired
    private WeatherFeatureCityDayHisService weatherFeatureCityDayHisService;

    @Autowired
    private WeatherCityFcService weatherCityFcService;

    @Autowired
    private WeatherCityFcDAO weatherCityFcDAO;

    @Autowired
    private WeatherCityFcAdapterService weatherCityFcAdapterService;

    @Autowired
    private WeatherCityHisDAO weatherCityHisDAO;

    @Override
    public List<TenDayForecastDTO> getListLoadCityFc(String cityId, String caliberId, Date startDate, Date endDate,
        String algorithmId) throws Exception {
        List<TenDayForecastDTO> tenDayForecastDTOS = new ArrayList<>();
        TenDayForecastDTO tenDayForecastFcDTO = new TenDayForecastDTO();
        TenDayForecastDTO tenDayForecastHisDTO = new TenDayForecastDTO();
        TenDayForecastDTO tenDayForecastAccuracyDTO = new TenDayForecastDTO();
        tenDayForecastFcDTO.setName("预测特性");
        tenDayForecastHisDTO.setName("实际特性");
        tenDayForecastAccuracyDTO.setName("考核点特性");
        // 预测气象
        Map<java.sql.Date, List<WeatherCityFcDO>> weatherCityFcDO = weatherCityFcAdapterService.findFcWeather(cityId, algorithmId, 5, startDate, endDate)
            .stream().collect(Collectors.groupingBy(WeatherCityFcDO::getDate));
        // 实际气象
        Map<java.sql.Date, List<WeatherCityHisDO>> weatherCityHisDO = weatherCityHisDAO.findWeatherCityHisDO(cityId, 5,
            startDate,
            endDate).stream().collect(Collectors.groupingBy(WeatherCityHisDO::getDate));
        // 预测负荷特性分析
        List<LoadCityFcDO> loadCityFcDOS = new ArrayList<>();
        if (AlgorithmConstants.MD_ALGORITHM_ID.equals(algorithmId)) {
            loadCityFcDOS = loadCityFcService.findReportLoadFc(cityId, caliberId, startDate, endDate);
        } else {
            loadCityFcDOS = loadCityFcService.listLoadCityFc(cityId, caliberId, startDate, endDate,
                algorithmId);
        }
        // 预测气象特性分析
        List<WeatherFeatureCityDayFcDO> weatherFeatureCityFcList = weatherCityFcAdapterService.findWeatherFeatureByAlgorithmId(algorithmId,
            cityId, startDate, endDate);
        // 实际负荷特性分析
        List<LoadCityHisDO> loadCityHisDOS = loadCityHisService.getLoadCityHisDOS(cityId, caliberId, startDate,
            endDate);
        // 实际气象特性分析
        List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisDOS = weatherFeatureCityDayHisService.listWeatherFeatureCityDayHisDO(
            cityId, startDate, endDate);
        if (!CollectionUtils.isEmpty(loadCityFcDOS) || !CollectionUtils.isEmpty(weatherFeatureCityFcList)) {
            List<TenDayFeatureValuesDTO> tenDayFeatureValuesDTOS = new ArrayList<>();
            List<TenDayWeatherFeatureDTO> tenDayWeatherFeatureDTOS = new ArrayList<>();
            for (LoadCityFcDO loadCityFcDO : loadCityFcDOS) {
                LoadFeatureFcDTO statisticsDayFeature = loadFeatureStatService.findStatisticsDayFeature(loadCityFcDO);
                TenDayFeatureValuesDTO tenDayFeatureValuesDTO = new TenDayFeatureValuesDTO();
                BeanUtils.copyProperties(statisticsDayFeature, tenDayFeatureValuesDTO);
                tenDayFeatureValuesDTO.setDate(loadCityFcDO.getDate());
                tenDayFeatureValuesDTOS.add(tenDayFeatureValuesDTO);
            }
            tenDayForecastFcDTO.setLoadDataList(tenDayFeatureValuesDTOS);
            for (WeatherFeatureCityDayFcDO weatherFeatureCityDayFcDO : weatherFeatureCityFcList) {
                TenDayWeatherFeatureDTO tenDayWeatherFeatureDTO = new TenDayWeatherFeatureDTO();
                tenDayWeatherFeatureDTO.setDate(weatherFeatureCityDayFcDO.getDate());
                if (MapUtils.isNotEmpty(weatherCityFcDO)) {
                    List<WeatherCityFcDO> weatherCityFcDOS = weatherCityFcDO.get(weatherFeatureCityDayFcDO.getDate());
                    List<BigDecimal> weatherList = new ArrayList<>();
                    if (!CollectionUtils.isEmpty(weatherCityFcDOS)) {
                        WeatherCityFcDO weatherCityFcDO1 = weatherCityFcDOS.get(0);
                        weatherList = weatherCityFcDO1.getWeatherList();
                    }
                    if (!CollectionUtils.isEmpty(weatherList)) {
                        BigDecimal bigDecimal = BigDecimalFunctions.listAvg(weatherList);
                        BigDecimal max = BigDecimalFunctions.listMax(weatherList);
                        BigDecimal min = BigDecimalFunctions.listMin(weatherList);
                        weatherFeatureCityDayFcDO.setAveEffectiveTemperature(bigDecimal);
                        weatherFeatureCityDayFcDO.setHighestEffectiveTemperature(max);
                        weatherFeatureCityDayFcDO.setLowestEffectiveTemperature(min);
                    }
                }
                BeanUtils.copyProperties(weatherFeatureCityDayFcDO, tenDayWeatherFeatureDTO);
                tenDayWeatherFeatureDTOS.add(tenDayWeatherFeatureDTO);
            }
            tenDayForecastFcDTO.setWeatherDataList(tenDayWeatherFeatureDTOS);
            List<AccuracyAssessDTO> accuracyAssessDTOS = loadFeatureStatService.doCalcAccuracyAssessUnitFeature(caliberId,
                    startDate, endDate, loadCityFcDOS);
            tenDayForecastAccuracyDTO.setAccuracyAssessList(accuracyAssessDTOS);
        }
        if (!CollectionUtils.isEmpty(loadCityHisDOS) || !CollectionUtils.isEmpty(weatherFeatureCityDayHisDOS)) {
            List<TenDayFeatureValuesDTO> tenDayFeatureValuesDTOS = new ArrayList<>();
            List<TenDayWeatherFeatureDTO> tenDayWeatherFeatureDTOS = new ArrayList<>();
            for (LoadCityHisDO loadCityFcDO : loadCityHisDOS) {
                LoadFeatureFcDTO statisticsDayFeature = loadFeatureStatService.findStatisticsDayFeature(loadCityFcDO);
                TenDayFeatureValuesDTO tenDayFeatureValuesDTO = new TenDayFeatureValuesDTO();
                tenDayFeatureValuesDTO.setDate(loadCityFcDO.getDate());
                BeanUtils.copyProperties(statisticsDayFeature, tenDayFeatureValuesDTO);
                tenDayFeatureValuesDTOS.add(tenDayFeatureValuesDTO);
            }
            tenDayForecastHisDTO.setLoadDataList(tenDayFeatureValuesDTOS);
            for (WeatherFeatureCityDayHisDO weatherFeatureCityDayFcDO : weatherFeatureCityDayHisDOS) {
                TenDayWeatherFeatureDTO tenDayWeatherFeatureDTO = new TenDayWeatherFeatureDTO();
                tenDayWeatherFeatureDTO.setDate(weatherFeatureCityDayFcDO.getDate());
                if (MapUtils.isNotEmpty(weatherCityHisDO)) {
                    List<WeatherCityHisDO> weatherCityFcDOS = weatherCityHisDO.get(weatherFeatureCityDayFcDO.getDate());
                    List<BigDecimal> weatherList = new ArrayList<>();
                    if (!CollectionUtils.isEmpty(weatherCityFcDOS)) {
                        WeatherCityHisDO weatherCityFcDO1 = weatherCityFcDOS.get(0);
                        weatherList = weatherCityFcDO1.getWeatherList();
                    }
                    if (!CollectionUtils.isEmpty(weatherList)) {
                        BigDecimal bigDecimal = BigDecimalFunctions.listAvg(weatherList);
                        BigDecimal max = BigDecimalFunctions.listMax(weatherList);
                        BigDecimal min = BigDecimalFunctions.listMin(weatherList);
                        weatherFeatureCityDayFcDO.setAveEffectiveTemperature(bigDecimal);
                        weatherFeatureCityDayFcDO.setHighestEffectiveTemperature(max);
                        weatherFeatureCityDayFcDO.setLowestEffectiveTemperature(min);
                    }
                }
                BeanUtils.copyProperties(weatherFeatureCityDayFcDO, tenDayWeatherFeatureDTO);
                tenDayWeatherFeatureDTOS.add(tenDayWeatherFeatureDTO);
            }
            tenDayForecastHisDTO.setWeatherDataList(tenDayWeatherFeatureDTOS);
        }
        tenDayForecastDTOS.add(tenDayForecastFcDTO);
        tenDayForecastDTOS.add(tenDayForecastHisDTO);
        tenDayForecastDTOS.add(tenDayForecastAccuracyDTO);
        return tenDayForecastDTOS;
    }

    @Override
    public TenDayForecastCurveDTO getListLoadCityFcCurve(String cityId, String caliberId, Date startDate,
        Date endDate, String algorithmId) throws Exception {
        TenDayForecastCurveDTO result = new TenDayForecastCurveDTO();
        TenDayCurveValuesDTO tenDayCurveValuesDTO1 = new TenDayCurveValuesDTO();
        List<TenDayCurveDateValuesDTO> tenDayCurveDateValuesDTOS = new ArrayList<>();
        List<TenDayCurveDateValuesDTO> tenDayCurveDateValuesDTOS1 = new ArrayList<>();
        List<TenDayCurveDateValuesDTO> tenDayCurveDateValuesDTOS2 = new ArrayList<>();
        // 气象外层
        List<TenDayWeatherCurveValuesDTO> tenDayWeatherCurveValuesDTOS = new ArrayList<>();
        // 96点预测算法负荷曲线
        List<LoadCityFcDO> loadCityFcDOS = loadCityFcService.listLoadCityFc(cityId, caliberId, startDate, endDate,
            algorithmId);
        // 96点上报负荷曲线
        List<LoadCityFcDO> reportLoadCityFcDO = loadCityFcService.getReportLoadCityFcDO(cityId, caliberId, startDate,
            endDate, 1);
        // 96点实际负荷曲线
        List<LoadCityHisDO> loadCityHisDOS = loadCityHisService.getLoadCityHisDOS(cityId, caliberId, startDate,
            endDate);
        // 96点预测气象曲线+实际曲线
        List<WeatherCityFcDO> weatherCityFcDO = weatherCityFcAdapterService.findFcWeather(cityId, algorithmId, null, startDate, endDate);
        List<WeatherCityHisDO> weatherCityHisDO = weatherCityHisDAO.findWeatherCityHisDO(cityId, null, startDate,
            endDate);
        TenDayCurveValuesDTO tenDayCurveValuesDTO = new TenDayCurveValuesDTO();

        List<Date> listBetweenDay = DateUtil.getListBetweenDay(startDate, endDate);
        /*for (Date date : listBetweenDay) {
            String dateToStr = DateUtil.getDateToStr(date);
        }*/
        if (!CollectionUtils.isEmpty(loadCityFcDOS)) {
            for (LoadCityFcDO loadCityFcDO : loadCityFcDOS) {
                TenDayCurveDateValuesDTO tenDayCurveDateValuesDTO = new TenDayCurveDateValuesDTO();
                tenDayCurveDateValuesDTO.setDate(loadCityFcDO.getDate());
                List<BigDecimal> bigDecimals = BasePeriodUtils.toList(loadCityFcDO, Constants.WEATHER_CURVE_POINT_NUM,
                    Constants.WEATHER_CURVE_START_WITH_ZERO);
                tenDayCurveDateValuesDTO.setBigDecimals(bigDecimals);
                tenDayCurveDateValuesDTOS.add(tenDayCurveDateValuesDTO);
            }
            tenDayCurveValuesDTO.setDateFcLoadList(tenDayCurveDateValuesDTOS);
        }
        if (!CollectionUtils.isEmpty(reportLoadCityFcDO)) {
            List<LoadCityFcDO> collect = reportLoadCityFcDO.stream().sorted(Comparator.comparing(LoadCityFcDO::getDate))
                .collect(Collectors.toList());
            for (LoadCityFcDO loadCityFcDO : collect) {
                TenDayCurveDateValuesDTO tenDayCurveDateValuesDTO = new TenDayCurveDateValuesDTO();
                tenDayCurveDateValuesDTO.setDate(loadCityFcDO.getDate());
                List<BigDecimal> bigDecimals = BasePeriodUtils.toList(loadCityFcDO, Constants.WEATHER_CURVE_POINT_NUM,
                    Constants.WEATHER_CURVE_START_WITH_ZERO);
                tenDayCurveDateValuesDTO.setBigDecimals(bigDecimals);
                tenDayCurveDateValuesDTOS1.add(tenDayCurveDateValuesDTO);
            }
            tenDayCurveValuesDTO.setDateReportLoadList(tenDayCurveDateValuesDTOS1);
        }
        if (!CollectionUtils.isEmpty(reportLoadCityFcDO)) {
            for (LoadCityHisDO loadCityHisDO : loadCityHisDOS) {
                TenDayCurveDateValuesDTO tenDayCurveDateValuesDTO = new TenDayCurveDateValuesDTO();
                tenDayCurveDateValuesDTO.setDate(loadCityHisDO.getDate());
                List<BigDecimal> bigDecimals = BasePeriodUtils.toList(loadCityHisDO, Constants.WEATHER_CURVE_POINT_NUM,
                    Constants.WEATHER_CURVE_START_WITH_ZERO);
                tenDayCurveDateValuesDTO.setBigDecimals(bigDecimals);
                tenDayCurveDateValuesDTOS2.add(tenDayCurveDateValuesDTO);
            }
            tenDayCurveValuesDTO.setDateHisLoadList(tenDayCurveDateValuesDTOS2);
        }
        tenDayCurveValuesDTO1.setDateFcLoadList(tenDayCurveDateValuesDTOS);
        tenDayCurveValuesDTO1.setDateReportLoadList(tenDayCurveDateValuesDTOS1);
        tenDayCurveValuesDTO1.setDateHisLoadList(tenDayCurveDateValuesDTOS2);

        if (!CollectionUtils.isEmpty(weatherCityFcDO) || !CollectionUtils.isEmpty(weatherCityHisDO)) {
            for (WeatherEnum value : WeatherEnum.values()) {
                if (1 == value.getType() || 2 == value.getType() || 3 == value.getType()
                    || 4 == value.getType() || 5 == value.getType()) {
                    TenDayWeatherCurveValuesDTO tenDayWeatherCurveValuesDTO = new TenDayWeatherCurveValuesDTO();
                    tenDayWeatherCurveValuesDTO.setName(value.getTypeName());

                    List<TenDayCurveWeatherDateDTO> tenDayCurveWeatherDateDTOS = new ArrayList<>();
                    List<TenDayCurveWeatherDateDTO> tenDayCurveWeatherDateDTOS1 = new ArrayList<>();

                    for (WeatherCityFcDO cityFcDO : weatherCityFcDO) {
                        if (value.getType().equals(cityFcDO.getType())) {
                            TenDayCurveWeatherDateDTO tenDayCurveWeatherDateDTO = new TenDayCurveWeatherDateDTO();
                            tenDayCurveWeatherDateDTO.setDate(cityFcDO.getDate());
                            List<BigDecimal> bigDecimals = BasePeriodUtils.toList(cityFcDO, Constants.WEATHER_CURVE_POINT_NUM,
                                Constants.WEATHER_CURVE_START_WITH_ZERO);
                            tenDayCurveWeatherDateDTO.setWeatherLoad(bigDecimals);
                            tenDayCurveWeatherDateDTOS.add(tenDayCurveWeatherDateDTO);
                        }
                    }
                    tenDayWeatherCurveValuesDTO.setWeather(tenDayCurveWeatherDateDTOS);

                    for (WeatherCityHisDO cityHisDO : weatherCityHisDO) {
                        if (value.getType().equals(cityHisDO.getType())) {
                            TenDayCurveWeatherDateDTO tenDayCurveWeatherDateDTO = new TenDayCurveWeatherDateDTO();
                            tenDayCurveWeatherDateDTO.setDate(cityHisDO.getDate());
                            List<BigDecimal> bigDecimals = BasePeriodUtils.toList(cityHisDO, Constants.WEATHER_CURVE_POINT_NUM,
                                Constants.WEATHER_CURVE_START_WITH_ZERO);
                            tenDayCurveWeatherDateDTO.setWeatherLoad(bigDecimals);
                            tenDayCurveWeatherDateDTOS1.add(tenDayCurveWeatherDateDTO);
                        }
                    }
                    tenDayWeatherCurveValuesDTO.setHisWeather(tenDayCurveWeatherDateDTOS1);
                    tenDayWeatherCurveValuesDTOS.add(tenDayWeatherCurveValuesDTO);
                }
            }
        }
        result.setLoadDataList(tenDayCurveValuesDTO1);
        result.setWeatherDataList(tenDayWeatherCurveValuesDTOS);
        return result;
    }
}
