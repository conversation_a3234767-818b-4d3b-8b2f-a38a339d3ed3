package com.tsintergy.lf.serviceimpl.area.dao;


import com.tsieframework.core.base.dao.hibernate.BaseAbstractDAO;
import com.tsieframework.core.base.dao.hibernate.BaseDAO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.dao.jpa.BaseJpaDAO;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.util.compatible.BeanUtils;
import com.tsieframework.util.compatible.StringUtils;
import com.tsintergy.lf.serviceapi.base.area.pojo.WeatherFeatureAreaDayHisDO;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version $Id: WeatherFeatureCityDayFcDAO.java, v 0.1 2018-01-31 11:00:15 tao Exp $$
 */

public interface WeatherFeatureAreaDayHisDAO extends BaseJpaDAO<WeatherFeatureAreaDayHisDO, String> {

}