package com.tsintergy.lf.serviceimpl.ultra.forecast.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsieframework.util.compatible.StringUtils;
import com.tsintergy.lf.serviceapi.base.ultra.forecast.api.SettingAlarmInitService;
import com.tsintergy.lf.serviceapi.base.ultra.forecast.pojo.SettingAlarmInitDO;
import com.tsintergy.lf.serviceapi.base.ultra.forecast.dto.ForecastAlarmConfigDTO;
import com.tsintergy.lf.serviceimpl.ultra.forecast.dao.SettingAlarmDAO;
import java.util.Objects;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2023-10-19
 * @since 1.0.0
 */
@Service("settingAlarmInitService")
public class SettingAlarmInitServiceImpl implements SettingAlarmInitService {

    @Autowired
    SettingAlarmDAO settingAlarmDAO;

    @Override
    public SettingAlarmInitDO findByCityId(String cityId) throws Exception {
        SettingAlarmInitDO one = settingAlarmDAO.findOne(
            JpaWrappers.<SettingAlarmInitDO>lambdaQuery()
                .eq(StringUtils.isNotBlank(cityId), SettingAlarmInitDO::getCityId, cityId)
        );
        return one;
    }

    @Override
    public void doSaveOrUpdate(ForecastAlarmConfigDTO forecastAlarmConfigDTO) throws Exception {
        SettingAlarmInitDO settingAlarmInitDO = new SettingAlarmInitDO();
        SettingAlarmInitDO one = settingAlarmDAO.findOne(
            JpaWrappers.<SettingAlarmInitDO>lambdaQuery()
                .eq(StringUtils.isNotBlank(forecastAlarmConfigDTO.getCityId()), SettingAlarmInitDO::getCityId,
                    forecastAlarmConfigDTO.getCityId())
        );
        if (Objects.isNull(one)) {
            BeanUtils.copyProperties(forecastAlarmConfigDTO, settingAlarmInitDO);
            settingAlarmInitDO.setFifteenAlarm(forecastAlarmConfigDTO.getFifteenAlarmLoad());
            settingAlarmInitDO.setFiveAlarm(forecastAlarmConfigDTO.getFiveAlarmLoad());
            settingAlarmDAO.save(settingAlarmInitDO);
        } else {
            String id = one.getId();
            BeanUtils.copyProperties(forecastAlarmConfigDTO, settingAlarmInitDO);
            settingAlarmInitDO.setFifteenAlarm(forecastAlarmConfigDTO.getFifteenAlarmLoad());
            settingAlarmInitDO.setFiveAlarm(forecastAlarmConfigDTO.getFiveAlarmLoad());
            settingAlarmInitDO.setId(id);
            settingAlarmDAO.saveOrUpdateByTemplate(settingAlarmInitDO);
        }
    }
}
