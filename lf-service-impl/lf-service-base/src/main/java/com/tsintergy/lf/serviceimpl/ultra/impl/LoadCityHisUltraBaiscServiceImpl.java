
package com.tsintergy.lf.serviceimpl.ultra.impl;


import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsieframework.core.base.dao.type.DataPointListMeta;
import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsintergy.aif.algorithm.serviceapi.base.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.ultra.api.LoadCityHisUltraBasicService;
import com.tsintergy.lf.serviceapi.base.ultra.pojo.LoadCityHisUltraBasicDO;
import com.tsintergy.lf.serviceimpl.ultra.dao.LoadCityHisUltraBasicDAO;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * 包含查询 5分钟 or 15分钟 数据；其中 lambdaQuery无法命中tvMeta的子属性；暂时使用query
 *
 * <AUTHOR>
 */
@Slf4j
@Service("loadCityHisUltraBasicService")
public class LoadCityHisUltraBaiscServiceImpl extends BaseServiceImpl implements LoadCityHisUltraBasicService {

    @Autowired
    LoadCityHisUltraBasicDAO loadCityHisDAO;

    @Override
    public List<LoadCityHisUltraBasicDO> getLoadCityHisDOS(String cityId, String caliberId, Date startDate, Date endDate, Integer delta) {
        List<LoadCityHisUltraBasicDO> loadCityFcDOS = loadCityHisDAO.findAll(
                JpaWrappers.<LoadCityHisUltraBasicDO>query()
                        .eq(cityId != null, "cityId", cityId)
                        .eq(caliberId != null, "caliberId", caliberId)
                        .eq(delta != null, "tvMeta.delta", delta)
                        .ge(startDate != null, "dateTime", startDate)
                        .le(endDate != null, "dateTime", endDate)
        );
        return loadCityFcDOS;
    }

    @Override
    public LoadCityHisUltraBasicDO getLoadCityHisDO(String cityId, String caliberId, Date date, Integer delta) throws Exception {
//      注：  lambdaQuery无法命中子属性；暂时使用query
        if (delta == null) {
            delta = 5;
        }
        return loadCityHisDAO.findOne(
                JpaWrappers.<LoadCityHisUltraBasicDO>query()
                        .eq(cityId!=null,"cityId", cityId)
                        .eq(caliberId!=null,"caliberId", caliberId)
                        .eq(date != null,"dateTime", date)
                        .eq(delta!=null,"tvMeta.delta", delta)
        );
    }

    @Override
    public void doSaveOrUpdateList(List<LoadCityHisUltraBasicDO> loadCityHisDOS) {
        loadCityHisDAO.saveOrUpdateBatchByTemplate(loadCityHisDOS);
    }

    @Override
    public List<BigDecimal> findLoadCityHisDO(String cityId, Date startDate, Date endDate, String caliberId, Integer delta) {
        List<LoadCityHisUltraBasicDO> powerLoadHisCityClctDOS = null;
        try {
            powerLoadHisCityClctDOS = loadCityHisDAO.findAll(
                    JpaWrappers.<LoadCityHisUltraBasicDO>query()
                            .eq(!StringUtils.isEmpty(cityId), "cityId", cityId)
                            .eq(!StringUtils.isEmpty(caliberId), "caliberId", caliberId)
                            .eq(!Objects.isNull(delta), "tvMeta.delta", delta)
                            .ge(!Objects.isNull(startDate), "dateTime", startDate)
                            .le(!Objects.isNull(endDate), "dateTime", endDate)
            );// this.getLoadCityHisDOS(cityId, caliberId, startDate, endDate);
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException("01C20180003");
        }
        List<BigDecimal> result = new ArrayList<>();
        int size = 0;
        if (DataPointListMeta.MINUTE_5 == delta){
            size = DataPointListMeta.POINTS_288;
        }else if (DataPointListMeta.MINUTE_15 == delta){
            size = DataPointListMeta.POINTS_96;
        }
        int finalSize = size;
        List nullList = new ArrayList() {
            {
                for (int i = 0; i < finalSize; i++) {
                    add(null);
                }
            }
        };
        if (!CollectionUtils.isEmpty(powerLoadHisCityClctDOS) && powerLoadHisCityClctDOS.size() > 0) {
            Map<Date, LoadCityHisUltraBasicDO> mapData = powerLoadHisCityClctDOS.stream()
                    .collect(Collectors.toMap(LoadCityHisUltraBasicDO::getDateTime, Function.identity(), (o, n) -> n));
            List<Date> listDate = DateUtil.getListBetweenDay(startDate, endDate);
            for (Date date : listDate) {
                LoadCityHisUltraBasicDO hisCityClctDO = mapData.get(date);
                if (hisCityClctDO == null) {
                    result.addAll(nullList);
                } else {
                    result.addAll(hisCityClctDO.getTvData());
                }
            }
        }
        return result;
    }

    @Override
    public List<LoadCityHisUltraBasicDO> listLoadCityHisDOS(List<String> cityIds, String caliberId, Integer delta, Date startDate, Date endDate) {
        return loadCityHisDAO.findAll(
                JpaWrappers.<LoadCityHisUltraBasicDO>query()
                        .eq(!StringUtils.isEmpty(caliberId), "caliberId", caliberId)
                        .eq(!Objects.isNull(delta), "tvMeta.delta", delta)
                        .ge(!Objects.isNull(startDate), "dateTime", startDate)
                        .le(!Objects.isNull(endDate), "dateTime", endDate)
                        .in(!CollectionUtils.isEmpty(cityIds), "cityId", cityIds)
        );
    }

    @Override
    public void deleteByCityIdAndCaliberIdList(String cityId, String caliberId, Date startDate, Date endDate) {
        loadCityHisDAO.delete(
                JpaWrappers.<LoadCityHisUltraBasicDO>lambdaUpdate()
                        .eq(!StringUtils.isEmpty(cityId), LoadCityHisUltraBasicDO::getCityId, cityId)
                        .eq(!StringUtils.isEmpty(caliberId), LoadCityHisUltraBasicDO::getCaliberId, caliberId)
                        .ge(!Objects.isNull(startDate), LoadCityHisUltraBasicDO::getDateTime, startDate)
                        .le(!Objects.isNull(endDate), LoadCityHisUltraBasicDO::getDateTime, endDate)
        );
    }
}
