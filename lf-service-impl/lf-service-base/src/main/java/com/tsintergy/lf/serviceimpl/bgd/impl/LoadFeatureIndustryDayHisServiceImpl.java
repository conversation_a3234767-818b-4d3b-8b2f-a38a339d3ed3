package com.tsintergy.lf.serviceimpl.bgd.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsieframework.core.base.service.BaseFacadeServiceImpl;
import com.tsintergy.lf.serviceapi.base.bgd.api.LoadFeatureIndustryDayHisService;
import com.tsintergy.lf.serviceapi.base.bgd.pojo.LoadFeatureIndustryDayHisServiceDO;
import com.tsintergy.lf.serviceimpl.bgd.dao.LoadFeatureIndustryDayHisServiceDAO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * Description: TODO <br>
 *
 * <AUTHOR>
 * @create 2023-02-16
 * @since 1.0.0
 */
@Service("loadFeatureIndustryDayHisService")
public class LoadFeatureIndustryDayHisServiceImpl extends BaseFacadeServiceImpl implements LoadFeatureIndustryDayHisService {

    @Autowired
    LoadFeatureIndustryDayHisServiceDAO loadFeatureIndustryDayHisServiceDAO;

    @Override
    public List<LoadFeatureIndustryDayHisServiceDO> getLoadFeatureIndustryDayHisServiceList(Date startDate, Date endDate, String cityId, String tradeCode) {
        return loadFeatureIndustryDayHisServiceDAO.findAll(JpaWrappers.<LoadFeatureIndustryDayHisServiceDO>lambdaQuery()
                .eq(LoadFeatureIndustryDayHisServiceDO::getCityId, cityId)
                .eq(LoadFeatureIndustryDayHisServiceDO::getType, tradeCode)
                .ge(LoadFeatureIndustryDayHisServiceDO::getDate, startDate)
                .le(LoadFeatureIndustryDayHisServiceDO::getDate, endDate));
    }
}
