package com.tsintergy.lf.serviceimpl.forecast.impl;

import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsieframework.core.component.cache.business.CacheService;
import com.tsieframework.core.component.cache.entity.CacheRequest;
import com.tsieframework.core.component.cache.entity.CacheResponse;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.serviceapi.base.forecast.api.AlgorithmParamService;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.AlgorithmParamDO;
import com.tsintergy.lf.serviceimpl.forecast.dao.AlgorithmParamDAO;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2023-06-20
 * @since 1.0.0
 */
@Service("AlgorithmParamService")
public class AlgorithmParamServiceImpl extends BaseServiceImpl implements AlgorithmParamService {

    private static final Logger logger = LogManager.getLogger( AlgorithmParamServiceImpl.class);

    @Autowired
    CacheService cacheService;

    @Autowired
    AlgorithmParamDAO algorithmParamDAO;



    @Override
    public List<AlgorithmParamDO> getAlgorithmParamVOsByAlgorithmId(String algorithmId) throws Exception {
        List<AlgorithmParamDO> algorithmParamDOS = new ArrayList<AlgorithmParamDO>();
        CacheRequest request = new CacheRequest();
        request.setCacheKey(Constants.CACHE_ID_ALGORITHM_PARAM_PREFIX+"*");
        CacheResponse response = cacheService.queryCacheItemList(request);
        if(response.getData() != null && response.getData().size() > 0) {
            for(Object obj : response.getData()) {
                AlgorithmParamDO algorithmParamDO = (AlgorithmParamDO)obj;
                if(algorithmParamDO.getAlgorithmId().equals(algorithmId)) {
                    algorithmParamDOS.add(algorithmParamDO);
                }
            }
        }
        return algorithmParamDOS;
    }

    @Override
    public AlgorithmParamDO getAlgorithmParamVOById(String id) throws Exception {
        CacheRequest request = new CacheRequest();
        request.setCacheKey(Constants.CACHE_ID_ALGORITHM_PARAM_PREFIX+id);
        CacheResponse response = cacheService.queryCacheItemList(request);
        if(response.getData() != null && response.getData().size() > 0) {
            return (AlgorithmParamDO)response.getData().get(0);
        }
        return null;
    }

    /**
     * 查询所有算法参数
     */
    @Override
    public List<AlgorithmParamDO> getAllAlgorithmParam() throws Exception {
        try {
            return (List<AlgorithmParamDO>)algorithmParamDAO.findAll();
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e){
            throw new BusinessException("T706",e.getMessage(), e);
        }
    }

    @Override
    public List<AlgorithmParamDO> getAlgorithmParamByAlgorithmIdNoCache(String algorithmId) throws Exception {
        return algorithmParamDAO.getParamsByAlgorithmId(algorithmId);
    }
}
