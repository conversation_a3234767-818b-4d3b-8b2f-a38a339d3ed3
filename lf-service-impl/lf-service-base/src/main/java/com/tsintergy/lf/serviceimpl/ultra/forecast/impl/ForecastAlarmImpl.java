package com.tsintergy.lf.serviceimpl.ultra.forecast.impl;

import com.tsieframework.core.base.dao.type.DataPointListMeta;
import com.tsintergy.aif.tool.core.enums.WeatherEnum;
import com.tsintergy.aif.tool.core.utils.data.ColumnUtil;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.ultra.forecast.api.SettingAlarmInitService;
import com.tsintergy.lf.serviceapi.base.ultra.forecast.pojo.SettingAlarmInitDO;
import com.tsintergy.lf.serviceapi.base.ultra.forecast.api.ForecastAlarmService;
import com.tsintergy.lf.serviceapi.base.ultra.forecast.dto.ForecastAlarmAllDTO;
import com.tsintergy.lf.serviceapi.base.ultra.forecast.dto.ForecastAlarmConfigDTO;
import com.tsintergy.lf.serviceapi.base.ultra.forecast.dto.ForecastAlarmCurveDTO;
import com.tsintergy.lf.serviceapi.base.ultra.forecast.dto.ForecastAlarmDTO;
import com.tsintergy.lf.serviceapi.base.ultra.load.pojo.LoadCityFcUltraDO;
import com.tsintergy.lf.serviceapi.base.ultra.load.api.LoadCityFcUltraService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityHisService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisDO;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2023-10-18
 * @since 1.0.0
 */
@Service("forecastAlarmService")
public class ForecastAlarmImpl implements ForecastAlarmService {

    @Autowired
    SettingAlarmInitService settingAlarmInitService;

    @Autowired
    LoadCityHisService loadCityHisService;

    @Autowired
    LoadCityFcUltraService loadCityFcUltraService;

    @Autowired
    WeatherCityFcService weatherCityFcService;

    @Autowired
    WeatherCityHisService weatherCityHisService;

    @Autowired
    CityService cityService;

    @Override
    public ForecastAlarmAllDTO findForecastAlarm(Date date, String cityId, Integer timeSpan, String caliberId,
        String algorithmId) throws Exception {
        ForecastAlarmAllDTO forecastAlarmAllDTO = new ForecastAlarmAllDTO();
        String dateToStr = DateUtil.getDateToStrFORMAT(date, "yyyy-MM-dd");
        List<ForecastAlarmDTO> result = new ArrayList<>();
        // 实际负荷
        List<BigDecimal> real = new ArrayList<>();
        // 预测负荷
        List<BigDecimal> ultraLine = new ArrayList<>();
        // 偏差量
        List<BigDecimal> fcCompare = new ArrayList<>();
        BigDecimal alarm = BigDecimal.valueOf(400);
        Integer totalColumn =
            DataPointListMeta.MINUTE_5 == timeSpan ? DataPointListMeta.POINTS_288 : DataPointListMeta.POINTS_96;
        List<BigDecimal> realMid = null;
        if(totalColumn.equals(DataPointListMeta.POINTS_96)){
             realMid = loadCityHisService.findLoadCityHisDO(date, cityId, caliberId);
        }else if(totalColumn.equals(DataPointListMeta.POINTS_288)){
             realMid = loadCityHisService.findLoadCityHis288DO(date, cityId, caliberId);
        }

        LoadCityFcUltraDO fcDO = loadCityFcUltraService
            .getLoadCityFcDO(date, cityId, caliberId, algorithmId, timeSpan, Constants.ULTRA_FORECAST_1);
        SettingAlarmInitDO byCityId = settingAlarmInitService.findByCityId(cityId);
        if (byCityId != null && timeSpan == DataPointListMeta.MINUTE_5) {
            alarm = byCityId.getFiveAlarm();
        } else if (byCityId != null && timeSpan == DataPointListMeta.MINUTE_15) {
            alarm = byCityId.getFifteenAlarm();
        }
        if (ObjectUtils.isEmpty(realMid)) {
            real.addAll(ColumnUtil.getZeroOrNullList(totalColumn, null));
        } else {
            real.addAll(realMid);
        }


        if (ObjectUtils.isEmpty(fcDO)) {
            ultraLine.addAll(ColumnUtil.getZeroOrNullList(totalColumn, null));
        } else {
            ultraLine.addAll(fcDO.getTvData());
        }
        BigDecimal divide = null;
        if (!CollectionUtils.isEmpty(ultraLine)) {
            for (int i = 0; i < ultraLine.size(); i++) {
                BigDecimal bigDecimal = ultraLine.get(i);
                BigDecimal bigDecimal1 = real.get(i);
                if (bigDecimal != null && bigDecimal1 != null && alarm != null) {
                    divide = bigDecimal.subtract(bigDecimal1).setScale(2, RoundingMode.HALF_UP);
                    fcCompare.add(divide);
                    if (divide.abs().compareTo(alarm) >= 0) {
                        ForecastAlarmDTO forecastAlarmDTO = new ForecastAlarmDTO();
                        List<String> timePoint = getTimePoint(i, timeSpan);
                        forecastAlarmDTO.setAlarmTime(dateToStr + " " + timePoint.get(0));
                        forecastAlarmDTO.setAlarmLoad(alarm);
                        forecastAlarmDTO.setFcLoad(bigDecimal);
                        forecastAlarmDTO.setHisLoad(bigDecimal1);
                        forecastAlarmDTO.setFcCompare(divide);
                        result.add(forecastAlarmDTO);
                    }
                }
            }
        }
        forecastAlarmAllDTO.setFcCompare(fcCompare);
        forecastAlarmAllDTO.setForecastAlarmDTOList(result);
        return forecastAlarmAllDTO;
    }

    @Override
    public ForecastAlarmConfigDTO findForecastAlarmConfig(String cityId) throws Exception {
        SettingAlarmInitDO byCityId = settingAlarmInitService.findByCityId(cityId);
        ForecastAlarmConfigDTO forecastAlarmConfigDTO = new ForecastAlarmConfigDTO();
        if (Objects.nonNull(byCityId)) {
            forecastAlarmConfigDTO.setFifteenType(byCityId.getFifteenType());
            forecastAlarmConfigDTO.setFiveAlarmLoad(byCityId.getFiveAlarm());
            forecastAlarmConfigDTO.setFiveType(byCityId.getFiveType());
            forecastAlarmConfigDTO.setFifteenAlarmLoad(byCityId.getFifteenAlarm());
            forecastAlarmConfigDTO.setCityId(cityId);
        }
        return forecastAlarmConfigDTO;
    }

    @Override
    public void saveForecastAlarmConfig(ForecastAlarmConfigDTO forecastAlarmConfigDTO) throws Exception {
        settingAlarmInitService.doSaveOrUpdate(forecastAlarmConfigDTO);
    }

    @Override
    public ForecastAlarmCurveDTO findForecastAlarmCurve(Date date, String cityId, Integer timeSpan, String caliberId,
        String algorithmId) throws Exception {
        ForecastAlarmCurveDTO forecastAlarmCurveDTO = new ForecastAlarmCurveDTO();
        // 实际负荷
        List<BigDecimal> real = new ArrayList<>();
        // 预测负荷
        List<BigDecimal> ultraLine = new ArrayList<>();
        List<BigDecimal> fcTem = new ArrayList<>();
        List<BigDecimal> hisTem = new ArrayList<>();
        List<BigDecimal> fcHum = new ArrayList<>();
        List<BigDecimal> hisHum = new ArrayList<>();
        List<BigDecimal> fcRain = new ArrayList<>();
        List<BigDecimal> hisRain = new ArrayList<>();
        List<BigDecimal> fcWind = new ArrayList<>();
        List<BigDecimal> hisWind = new ArrayList<>();
        Integer totalColumn =
            DataPointListMeta.MINUTE_5 == timeSpan ? DataPointListMeta.POINTS_288 : DataPointListMeta.POINTS_96;
        String weatherCityId = cityService.findWeatherCityId(cityId);
        LoadCityHisDO loadCityHisDO = loadCityHisService.getLoadCityHisDO(cityId, caliberId, date);
        LoadCityFcUltraDO fcDO = loadCityFcUltraService
            .getLoadCityFcDO(date, cityId, caliberId, algorithmId, timeSpan, Constants.ULTRA_FORECAST_1);
        List<WeatherCityHisDO> weatherCityHisVOS = weatherCityHisService.findWeatherCityHisDOs(weatherCityId, null,
            new java.sql.Date(date.getTime()), new java.sql.Date(date.getTime()));
        List<WeatherCityFcDO> fcVOS = weatherCityFcService.findWeatherCityFcDOs(weatherCityId, null, date, date);
        if (ObjectUtils.isEmpty(loadCityHisDO)) {
            real.addAll(ColumnUtil.getZeroOrNullList(totalColumn, null));
        } else {
            real.addAll(loadCityHisDO.getloadList());
        }
        if (ObjectUtils.isEmpty(fcDO)) {
            ultraLine.addAll(ColumnUtil.getZeroOrNullList(totalColumn, null));
        } else {
            ultraLine.addAll(fcDO.getTvData());
        }
        forecastAlarmCurveDTO.setFcLoad(ultraLine);
        forecastAlarmCurveDTO.setHisLoad(real);
        if (!CollectionUtils.isEmpty(weatherCityHisVOS)) {
            for (WeatherCityHisDO weatherCityHisVO : weatherCityHisVOS) {
                if (WeatherEnum.HUMIDITY.getType().equals(weatherCityHisVO.getType())) {
                    hisHum = weatherCityHisVO.getWeatherList();
                    forecastAlarmCurveDTO.setHisHum(hisHum);
                } else if (WeatherEnum.TEMPERATURE.getType().equals(weatherCityHisVO.getType())) {
                    hisTem = weatherCityHisVO.getWeatherList();
                    forecastAlarmCurveDTO.setHisTem(hisTem);
                } else if (WeatherEnum.RAINFALL.getType().equals(weatherCityHisVO.getType())) {
                    hisRain = weatherCityHisVO.getWeatherList();
                    forecastAlarmCurveDTO.setHisRain(hisRain);
                } else if (WeatherEnum.WINDSPEED.getType().equals(weatherCityHisVO.getType())) {
                    hisWind = weatherCityHisVO.getWeatherList();
                    forecastAlarmCurveDTO.setHisWind(hisWind);
                }
            }
        }
        if (!CollectionUtils.isEmpty(fcVOS)) {
            for (WeatherCityFcDO fcVO : fcVOS) {
                if (WeatherEnum.HUMIDITY.getType().equals(fcVO.getType())) {
                    fcHum = fcVO.getWeatherList();
                    forecastAlarmCurveDTO.setHisHum(fcHum);
                } else if (WeatherEnum.TEMPERATURE.getType().equals(fcVO.getType())) {
                    fcTem = fcVO.getWeatherList();
                    forecastAlarmCurveDTO.setHisTem(fcTem);
                } else if (WeatherEnum.RAINFALL.getType().equals(fcVO.getType())) {
                    fcRain = fcVO.getWeatherList();
                    forecastAlarmCurveDTO.setHisRain(fcRain);
                } else if (WeatherEnum.WINDSPEED.getType().equals(fcVO.getType())) {
                    fcWind = fcVO.getWeatherList();
                    forecastAlarmCurveDTO.setHisWind(fcWind);
                }
            }
        }
        return forecastAlarmCurveDTO;
    }

    public static List<String> getTimePoint(Integer num, Integer timeStr) {
        List<String> list = new ArrayList<>();
        for (int i = 0; i < 1440; i += timeStr) {
            int hour = i / 60;
            int min = i % 60;
            String format = String.format("%04d", hour * 100 + min);
            list.add(format.substring(0, 2) + ":" + format.substring(2, 4));
        }
        if (num == null) {
            return list;
        } else {
            return Arrays.asList(list.get(num));
        }
    }
}
