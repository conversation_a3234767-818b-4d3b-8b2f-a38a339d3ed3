package com.tsintergy.lf.serviceimpl.weather.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityHisMeteoService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisMeteoDO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherCityHisMeteoDAO;
import lombok.SneakyThrows;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2024-12-04
 * @since 1.0.0
 */
@Service("weatherCityHisMeteoService")
public class WeatherCityHisMeteoServiceImpl implements WeatherCityHisMeteoService {

    @Autowired
    WeatherCityHisMeteoDAO weatherCityHisMeteoDAO;

    @Override
    public void doSaveOrUpdate(WeatherCityHisMeteoDO weatherCityHisMeteoDO) {
        List<WeatherCityHisMeteoDO> all = weatherCityHisMeteoDAO.findAll(
                JpaWrappers.<WeatherCityHisMeteoDO>lambdaQuery()
                        .eq(WeatherCityHisMeteoDO::getCityId, weatherCityHisMeteoDO.getCityId())
                        .eq(WeatherCityHisMeteoDO::getType, weatherCityHisMeteoDO.getType())
                        .eq(WeatherCityHisMeteoDO::getDate, weatherCityHisMeteoDO.getDate()));
        if (CollectionUtils.isEmpty(all)) {
            weatherCityHisMeteoDAO.save(weatherCityHisMeteoDO);
        } else {
            weatherCityHisMeteoDO.setId(all.get(0).getId());
            weatherCityHisMeteoDAO.saveOrUpdateByTemplate(weatherCityHisMeteoDO);
        }
    }

    @SneakyThrows
    @Override
    public void insertYesterday24HourData(WeatherCityHisMeteoDO weatherCityHisMeteoDO) {
        Date yesterday = DateUtils.addDays(weatherCityHisMeteoDO.getDate(), -1);
        WeatherCityHisMeteoDO yesterdayDO = weatherCityHisMeteoDAO.findOne(
                JpaWrappers.<WeatherCityHisMeteoDO>lambdaQuery()
                        .eq(WeatherCityHisMeteoDO::getCityId, weatherCityHisMeteoDO.getCityId())
                        .eq(WeatherCityHisMeteoDO::getType, weatherCityHisMeteoDO.getType())
                        .eq(WeatherCityHisMeteoDO::getDate, yesterday));
        if (yesterdayDO == null) {
            return;
        }
        yesterdayDO.setT2400(weatherCityHisMeteoDO.getT0000());
        List<BigDecimal> weatherList = yesterdayDO.getWeatherList();
        ColumnUtil.supplimentPoit(weatherList);
        Map<String, BigDecimal> decimalMap = ColumnUtil.listToMap(weatherList, Constants.LOAD_CURVE_START_WITH_ZERO);
        BasePeriodUtils.setAllFiled(yesterdayDO, decimalMap);
        weatherCityHisMeteoDAO.saveOrUpdateByTemplate(yesterdayDO);
    }

    @Override
    public List<WeatherCityHisMeteoDO> getListByCondition(String cityId, Integer type, Date startDate, Date endDate) {
        return weatherCityHisMeteoDAO.findAll(
                JpaWrappers.<WeatherCityHisMeteoDO>lambdaQuery()
                        .eq(cityId != null, WeatherCityHisMeteoDO::getCityId, cityId)
                        .eq(type != null, WeatherCityHisMeteoDO::getType, type)
                        .ge(WeatherCityHisMeteoDO::getDate, new java.sql.Date(startDate.getTime()))
                        .le(WeatherCityHisMeteoDO::getDate, new java.sql.Date(endDate.getTime())));
    }

    @Override
    public List<WeatherCityHisMeteoDO> getWeatherCityHisDOS(List<String> cityIds, Integer type, Date startDate, Date endDate) {
        return weatherCityHisMeteoDAO.findAll(
                JpaWrappers.<WeatherCityHisMeteoDO>lambdaQuery()
                        .in(CollectionUtils.isNotEmpty(cityIds), WeatherCityHisMeteoDO::getCityId, cityIds)
                        .eq(type != null, WeatherCityHisMeteoDO::getType, type)
                        .ge(WeatherCityHisMeteoDO::getDate, new java.sql.Date(startDate.getTime()))
                        .le(WeatherCityHisMeteoDO::getDate, new java.sql.Date(endDate.getTime())));
    }
}
