
package com.tsintergy.lf.serviceimpl.ultra.load.impl;


import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.serviceimpl.ultra.forecast.impl.BaseServiceImpl;
import com.tsintergy.lf.core.enums.IfEnum;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcService;
import com.tsintergy.lf.serviceapi.base.ultra.load.pojo.LoadCityFcUltraDO;
import com.tsintergy.lf.serviceapi.base.ultra.load.api.LoadCityFcUltraService;
import com.tsintergy.lf.serviceimpl.ultra.load.dao.LoadCityFcUltraDAO;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import javax.persistence.EntityManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR> 包含查询 5分钟 or 15分钟 数据；其中 lambdaQuery无法命中tvMeta的子属性；暂时使用query
 */
@Service("loadCityFcUltraService")
@Slf4j
@Transactional
public class LoadCityFcUltraServiceImpl extends BaseServiceImpl implements LoadCityFcUltraService {

    @Autowired
    CityService cityService;

    @Autowired
    LoadCityFcService loadCityFcService;

    @Autowired
    LoadCityFcUltraDAO loadCityFcUltraDAO;

    @Autowired
    EntityManager entityManager;

    @Override
    public LoadCityFcUltraDO doCreate(LoadCityFcUltraDO vo) throws Exception {
        try {
            return loadCityFcUltraDAO.saveOrUpdateByTemplate(vo);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException(e.getMessage(), e);
        }
    }


    @Override
    public LoadCityFcUltraDO getLoadCityFcDO(Date date, String cityId, String caliberId,
        String algorithmId, Integer delta, Integer multipointType) {
        if (delta == null) {
            delta = 5;
        }
        List<LoadCityFcUltraDO> LoadCityFcDOS = loadCityFcUltraDAO.findAll(
            JpaWrappers.<LoadCityFcUltraDO>query()
                .eq(cityId != null, "cityId", cityId)
                .eq(caliberId != null, "caliberId", caliberId)
                .eq(algorithmId != null, "algorithmId", algorithmId)
                .eq(date != null, "dateTime", date)
                .eq(delta != null, "tvMeta.delta", delta)
                .eq(multipointType != null, "multipointType", multipointType)
        );
        if (LoadCityFcDOS.size() < 1) {
            return null;
        }
        return LoadCityFcDOS.get(0);
    }


    @Override
    public List<LoadCityFcUltraDO> getLoadCityFcDO(String cityId, String caliberId,
        String algorithmId, Date startDate, Date endDate, Integer delta, Integer multipointType) {
        return loadCityFcUltraDAO.findAll(
            JpaWrappers.<LoadCityFcUltraDO>query()
                .eq(cityId != null, "cityId", cityId)
                .eq(caliberId != null, "caliberId", caliberId)
                .eq(algorithmId != null, "algorithmId", algorithmId)
                .eq(delta != null, "tvMeta.delta", delta)
                .ge(startDate != null, "dateTime", startDate)
                .le(endDate != null, "dateTime", endDate)
                .eq(multipointType != null, "multipointType", multipointType)
        );
    }


    @Override
    public LoadCityFcUltraDO getReportLoadCityFcDO(Date date, String cityId, String caliberId, IfEnum report,
        Integer delta, Integer multipointType)
        throws Exception {
        List<LoadCityFcUltraDO> LoadCityFcDOS = loadCityFcUltraDAO.findAll(
            JpaWrappers.<LoadCityFcUltraDO>query()
                .eq(cityId != null, "cityId", cityId)
                .eq(caliberId != null, "caliberId", caliberId)
                .eq(report != null, "report", report)
                .eq(date != null, "dateTime", date)
                .eq(delta != null, "tvMeta.delta", delta)
                .eq(multipointType != null, "multipointType", multipointType)
        );
        if (LoadCityFcDOS.size() < 1) {
            return null;
        }
        return LoadCityFcDOS.get(0);
    }

    @Override
    public LoadCityFcUltraDO doSaveOrUpdateLatest(LoadCityFcUltraDO loadCityFcDO) throws Exception {
        LoadCityFcUltraDO reportLoadCityFcDO = this
            .getReportLoadCityFcDO(loadCityFcDO.getDateTime(), loadCityFcDO.getCityId(), loadCityFcDO.getCaliberId(),
                loadCityFcDO.getReport(), loadCityFcDO.getTvMeta()
                    .getDelta(), loadCityFcDO.getMultipointType());
        if (!ObjectUtils.isEmpty(reportLoadCityFcDO)) {
            //只更新最新点
            List<BigDecimal> tvData = reportLoadCityFcDO.getTvData();
            List<BigDecimal> bigDecimals = copyList(loadCityFcDO.getTvData(), tvData);
            loadCityFcDO.setTvData(bigDecimals);
        }
        return loadCityFcUltraDAO.saveOrUpdateByTemplate(loadCityFcDO);
    }

    /**
     * 后续调整为工具类
     */
    private List<BigDecimal> copyList(List<BigDecimal> newList, List<BigDecimal> oldList) {
        List<BigDecimal> result = new ArrayList<>();
        for (int i = 0; i < oldList.size(); i++) {
            if (newList.get(i) != null) {
                result.add(newList.get(i));
            } else {
                result.add(oldList.get(i));
            }
        }
        return result;
    }

    @Override
    public List<LoadCityFcUltraDO> listLoadCityFcDOS(String cityId, String caliberId, String algoId, Date startDate,
        Date endDate, Integer delta, Integer multipointType) {
        return loadCityFcUltraDAO.findAll(
            JpaWrappers.<LoadCityFcUltraDO>query()
                .eq(!StringUtils.isEmpty(cityId), "cityId", cityId)
                .eq(!StringUtils.isEmpty(caliberId), "caliberId", caliberId)
                .eq(!StringUtils.isEmpty(algoId), "algorithmId", algoId)
                .eq(!Objects.isNull(delta), "tvMeta.delta", delta)
                .ge(!Objects.isNull(startDate), "dateTime", startDate)
                .le(!Objects.isNull(endDate), "dateTime", endDate)
                .eq(multipointType != null, "multipointType", multipointType)
        );
    }

    @Override
    public List<LoadCityFcUltraDO> listLoadCityFcDOSByCitys(List<String> cityIds, Date startDate, Date endDate,
        String caliberId, String algoId, Integer delta, Integer multipointType) {
        return loadCityFcUltraDAO.findAll(
            JpaWrappers.<LoadCityFcUltraDO>query()
                .in(!CollectionUtils.isEmpty(cityIds), "cityId", cityIds)
                .eq(!StringUtils.isEmpty(caliberId), "caliberId", caliberId)
                .eq(!StringUtils.isEmpty(algoId), "algorithmId", algoId)
                .eq(!Objects.isNull(delta), "tvMeta.delta", delta)
                .eq(!Objects.isNull(multipointType), "multipointType", multipointType)
                .ge(!Objects.isNull(startDate), "dateTime", startDate)
                .le(!Objects.isNull(endDate), "dateTime", endDate)
        );
    }

    @Override
    public void doSaveOrUpdateList(List<LoadCityFcUltraDO> result) {
        loadCityFcUltraDAO.saveOrUpdateBatchByTemplate(result);
    }

    @Override
    public void deleteByCityCaliberAlgoList(String cityId, String caliberId, String algoId, Date startDate,
        Date endDate) {
        loadCityFcUltraDAO.delete(
            JpaWrappers.<LoadCityFcUltraDO>lambdaUpdate()
                .eq(!StringUtils.isEmpty(cityId), LoadCityFcUltraDO::getCityId, cityId)
                .eq(!StringUtils.isEmpty(caliberId), LoadCityFcUltraDO::getCaliberId, caliberId)
                .eq(!StringUtils.isEmpty(algoId), LoadCityFcUltraDO::getAlgorithmId, algoId)
                .ge(!Objects.isNull(startDate), LoadCityFcUltraDO::getDateTime, startDate)
                .le(!Objects.isNull(endDate), LoadCityFcUltraDO::getDateTime, endDate)
        );
    }

    @Override
    public LoadCityFcUltraDO findLoadFc(String cityId, String caliberId, String algorithmId, Date date, Integer delta,
        IfEnum report, Integer multipointType) {
        return loadCityFcUltraDAO.findOne(
            JpaWrappers.<LoadCityFcUltraDO>query()
                .eq(cityId != null, "cityId", cityId)
                .eq(caliberId != null, "caliberId", caliberId)
                .eq(algorithmId != null, "algorithmId", algorithmId)
                .eq(date != null, "dateTime", date)
                .eq(delta != null, "tvMeta.delta", delta)
                .eq(report != null, "report", report)
                .eq(multipointType != null, "multipointType", multipointType)

        );
    }

    @Override
    public List<LoadCityFcUltraDO> findLoadCityFcD0(String cityId, String caliberId, String algorithmId, Date startDate,
        Date endDate, Boolean report, Integer delta) {
        return loadCityFcUltraDAO.findAll(
            JpaWrappers.<LoadCityFcUltraDO>query()
                .eq(!StringUtils.isEmpty(cityId), "cityId", cityId)
                .eq(!StringUtils.isEmpty(caliberId), "caliberId", caliberId)
                .eq(!StringUtils.isEmpty(algorithmId), "algorithmId", algorithmId)
                .ge(!Objects.isNull(startDate), "dateTime", startDate)
                .le(!Objects.isNull(endDate), "dateTime", endDate)
                .eq(!Objects.isNull(report), "report", report)
                .eq(!Objects.isNull(delta), "tvMeta.delta", delta)
        );
    }

    @Override
    public void deleteDO(java.sql.Date dateTime, String cityId, String caliberId, String algorithmId, IfEnum report,
        Integer timeSpan, Integer multipointType) {

        LoadCityFcUltraDO one = loadCityFcUltraDAO.findOne(
            JpaWrappers.<LoadCityFcUltraDO>query()
                .eq(!StringUtils.isEmpty(cityId), "cityId", cityId)
                .eq(!StringUtils.isEmpty(caliberId), "caliberId", caliberId)
                .eq(!StringUtils.isEmpty(algorithmId), "algorithmId", algorithmId)
                .eq(!Objects.isNull(report), "report", report)
                .eq(!Objects.isNull(timeSpan), "tvMeta.delta", timeSpan)
                .eq(!Objects.isNull(multipointType), "multipointType", multipointType)
        );
        if (!Objects.isNull(one)) {
            loadCityFcUltraDAO.delete(one);
        }
    }


    @Override
    public LoadCityFcUltraDO report(LoadCityFcUltraDO loadCityFcDO) throws Exception {

//        //校验上报 todo wangchen  暂时注掉，演示方便
//        if (getSettingSystemService().isBeforeReportTime(loadCityFcDO.getDate())) {
//            throw TsieExceptionUtils.newBusinessException("03D20180601");
//        }
        // 先把原来已上报的结果设置为未上报
        try {
            LoadCityFcUltraDO oldVO = this
                .getReportLoadCityFcDO(loadCityFcDO.getDateTime(), loadCityFcDO.getCityId(),
                    loadCityFcDO.getCaliberId(), IfEnum.YES,
                    loadCityFcDO.getTvMeta().getDelta(), Constants.ULTRA_FORECAST_1);
            oldVO.setReport(IfEnum.NO);
            oldVO.setSucceed(false);
            doSaveOrUpdateByTemplate(oldVO);
            getCurrentSession().flush();
            getCurrentSession().clear();
            loadCityFcDO.setReport(IfEnum.YES);
            loadCityFcDO.setSucceed(true);
            loadCityFcDO.setReportTime(new Timestamp(System.currentTimeMillis()));
        } catch (Exception e) {
            // 将新结果置为上报
            loadCityFcDO.setReport(IfEnum.YES);
            loadCityFcDO.setSucceed(true);
        }
        //将forecast_info_basic
//        LoadCityFcDO oldVOAlgorithm = this
//            .getLoadCityFcDO(loadCityFcDO.getDateTime(), loadCityFcDO.getCityId(),
//                loadCityFcDO.getCaliberId(), loadCityFcDO.getAlgorithmId(),
//                loadCityFcDO.getTvMeta().getDelta(), Constants.ULTRA_FORECAST_1);
//        loadCityFcDO.setId(oldVOAlgorithm.getId());
        return doSaveOrUpdateByTemplate(loadCityFcDO);
//        return loadCityFcDO;
    }

    @Override
    public LoadCityFcUltraDO doSaveOrUpdateByTemplate(LoadCityFcUltraDO fcDO) {
        return loadCityFcUltraDAO.saveOrUpdateByTemplate(fcDO);
    }

}
