package com.tsintergy.lf.serviceimpl.ultra.dao;


import com.tsieframework.core.base.dao.jpa.BaseJpaDAO;
import com.tsieframework.core.component.datasource.dynamic.annotation.DataSource;
import com.tsintergy.lf.serviceapi.base.ultra.pojo.LoadCityHisUltraBasicDO;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @version $Id: LoadCityHisDAO.java, v 0.1 2018-01-31 10:50:22 tao Exp $$
 */
@Repository
@DataSource("ultra")
public interface LoadCityHisUltraBasicDAO extends BaseJpaDAO<LoadCityHisUltraBasicDO, String> {


}