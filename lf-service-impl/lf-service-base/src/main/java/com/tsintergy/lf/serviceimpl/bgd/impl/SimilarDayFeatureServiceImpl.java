package com.tsintergy.lf.serviceimpl.bgd.impl;

import com.alibaba.nacos.common.utils.UuidUtils;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.math.BigDecimalFunctions;
import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsieframework.util.BeanCopierUtils;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.algorithm.dto.SimilarDateBean;
import com.tsintergy.lf.serviceapi.algorithm.dto.SimilarDayParam;
import com.tsintergy.lf.serviceapi.base.bgd.api.SimilarDayFeatureService;
import com.tsintergy.lf.serviceapi.base.bgd.dto.DateValuesDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.SimilarDayFeatureDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.SimilarDayListDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.SimilarDayResultDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.SimilarFeatureDTO;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcService;
import com.tsintergy.lf.serviceapi.base.forecast.api.SimilarDayService;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherEnum;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityDayHisService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityDayHisDO;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityHisService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityDayHisService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayHisDO;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2023-05-05
 * @since 1.0.0
 */
@Service
public class SimilarDayFeatureServiceImpl extends BaseServiceImpl implements SimilarDayFeatureService {

    @Autowired
    LoadCityHisService loadCityHisService;

    @Autowired
    LoadCityFcService loadCityFcService;

    @Autowired
    WeatherCityHisService weatherCityHisService;

    @Autowired
    WeatherFeatureCityDayHisService weatherFeatureCityDayHisService;

    @Autowired
    LoadFeatureCityDayHisService LoadFeatureCityDayHisService;

    @Autowired
    SimilarDayService similarDayService;

    @Autowired
    LoadFeatureCityDayHisService loadFeatureCityDayHisService;

    private final HashMap<String, List<SimilarDayListDTO>> hashMap = new HashMap<>();

    private final HashMap<String, List<SimilarDayListDTO>> hashMapFc = new HashMap<>();

    @Override
    public SimilarDayResultDTO queryResult(SimilarFeatureDTO similarFeatureDTO) throws Exception {
        String cityId = similarFeatureDTO.getCityId();
        String caliberId = similarFeatureDTO.getCaliberId();
        Date targetDate = similarFeatureDTO.getTargetDate();
        String targetDateStr = DateFormatUtils.format(targetDate, DateFormatType.SIMPLE_DATE_FORMAT_STR.getValue());
        Date startDate = similarFeatureDTO.getStartDate();
        Date endDate = similarFeatureDTO.getEndDate();
        String algoId = similarFeatureDTO.getAlgoId();
        List<DateValuesDTO> loadFcList = new ArrayList<>();

        if (DateUtil.differentDaysByCalendar(endDate, targetDate) <= 0){
            endDate = DateUtils.addDays(targetDate, -1);
            similarFeatureDTO.setEndDate(endDate);
        }
        //判断时间参数是否满足算法需求 查询的历史数据时间范围必须在目标日期之前 且不能包含目标日期
        if (DateUtil.differentDaysByCalendar(startDate, targetDate) <= 0){
            return null;
        }
        //判断目标日期是否有数据
        LoadCityHisDO targetDO = null;
        LoadCityFcDO loadCityFcDO = null;
        BigDecimal max = null;
        if (StringUtils.isNotBlank(similarFeatureDTO.getMaxResultCount())){
            targetDO = loadCityHisService.getLoadCityHisDO(cityId, caliberId, targetDate);
            if (Objects.isNull(targetDO)){
                return null;
            }
            max = BigDecimalFunctions.listMax(targetDO.getloadList());
        }else {
            loadCityFcDO = loadCityFcService.getLoadCityFcDO(targetDate, cityId, caliberId, algoId);
            if (Objects.isNull(loadCityFcDO)){
                return null;
            }
            max = BigDecimalFunctions.listMax(loadCityFcDO.getloadList());
        }

        //目标日期最大负荷

        //相似日负荷曲线对比
        List<DateValuesDTO> loadList = new ArrayList<>();
        //气温曲线对比
        List<DateValuesDTO> weatherList = new ArrayList<>();
        //特性统计
        List<SimilarDayFeatureDTO> featureList = new ArrayList<>();
        //相似度列表结果数据
        //历史
        List<SimilarDayListDTO> similarDayList = new ArrayList<>();
        //预测
        List<SimilarDayListDTO> similarDayFcList = new ArrayList<>();

        //填充目标日期数据 保底至少目标日期数据能够显示
        targetDateData(loadFcList, loadList, weatherList, featureList, cityId, caliberId, targetDate, algoId);
        //记录查询条件 使在相同查询条件下已有的相似日数据不再查询一遍
        String s = similarFeatureDTO.toString();
        StringBuilder flag = new StringBuilder(s);
        List<Date> showDates = null;
        List<Date> showDatesFc = null;
        if (StringUtils.isNotBlank(similarFeatureDTO.getMaxResultCount())){
            if (!hashMap.containsKey(flag.toString())){
                //经过过滤掉相似度阈值的数据  或者 当选择指定日期获取相似度数据
                //历史目标日期
                similarFeatureDTO.setTargetDataType("0");
                List<SimilarDateBean> similarDateBeans = generateSimilarData(similarFeatureDTO);
                if (!CollectionUtils.isEmpty(similarDateBeans)){
                    querySimilarDayListDTOList(similarDateBeans, cityId, caliberId, similarDayList, max);
                    hashMap.put(flag.toString(), similarDayList);
                }
            }else {
                similarDayList = hashMap.get(flag.toString());
            }
            //选择展示的日期
            showDates = genShowDate(similarDayList);
            //负荷对比曲线相似日数据
            if (!CollectionUtils.isEmpty(showDates)){
                loadCompare(showDates, cityId, caliberId, loadList);
            }
            //次日页面不需要气象和特性曲线
            if (!CollectionUtils.isEmpty(showDates)){
                //查询气象相似日曲线
                weatherCompare(showDates, cityId, weatherList);
                //查询特性统计
                queryFeatureStatics(showDates, cityId, caliberId, featureList);
            }
        }else {
            if (!hashMapFc.containsKey(flag.toString())){
                //预测目标日期
                //如果只是相似日页面就不需要预测
                similarFeatureDTO.setTargetDataType("1");
                List<SimilarDateBean> similarDateFcBeans = generateSimilarData(similarFeatureDTO);
                if (!CollectionUtils.isEmpty(similarDateFcBeans)){
                    querySimilarDayListDTOList(similarDateFcBeans, cityId, caliberId, similarDayFcList, max);
                    hashMapFc.put(flag.toString(), similarDayFcList);
                }
            }else {
                similarDayFcList = hashMapFc.get(flag.toString());
            }
            //选择展示的日期
            showDatesFc = genShowDate(similarDayFcList);
            //负荷对比曲线相似日数据
            if (!CollectionUtils.isEmpty(showDatesFc)){
                loadCompare(showDatesFc, cityId, caliberId, loadFcList);
            }
        }

        SimilarDayResultDTO result = new SimilarDayResultDTO();
        result.setSimilarDayList(similarDayList);
        result.setLoadList(loadList);
        result.setWeatherList(weatherList);
        result.setFeatureList(featureList);
        result.setSimilarDayFcList(similarDayFcList);
        result.setLoadListFc(loadFcList);
        return result;
    }

    //收集目标日期数据
    public void targetDateData(List<DateValuesDTO> loadFcList, List<DateValuesDTO> loadList, List<DateValuesDTO> weatherList, List<SimilarDayFeatureDTO> featureList,
        String cityId, String caliberId, Date targetDate, String algoId) throws Exception {

        String targetDateStr = DateFormatUtils.format(targetDate, DateFormatType.SIMPLE_DATE_FORMAT_STR.getValue());
        //预测负荷曲线
        LoadCityFcDO loadCityFcDO = loadCityFcService.getLoadCityFcDO(targetDate, cityId, caliberId, algoId);
        if (!Objects.isNull(loadCityFcDO)){
            DateValuesDTO load = new DateValuesDTO();
            load.setDate(targetDateStr);
            load.setValues(BigDecimalFunctions.listScale(BigDecimalFunctions.listDivideValue(loadCityFcDO.getloadList(), new BigDecimal("10")), 1));
            loadFcList.add(load);
        }

        //历史负荷曲线
        LoadCityHisDO loadCityHisDO = loadCityHisService.getLoadCityHisDO(cityId, caliberId, targetDate);
        if (!Objects.isNull(loadCityHisDO)){
            DateValuesDTO load = new DateValuesDTO();
            load.setDate(targetDateStr);
            load.setValues(BigDecimalFunctions.listScale(BigDecimalFunctions.listDivideValue(loadCityHisDO.getloadList(), new BigDecimal("10")), 1));
            loadList.add(load);
        }
        //气温对比
        WeatherCityHisDO weatherCityHisDO = weatherCityHisService.findWeatherCityHisDO(cityId, WeatherEnum.TEMPERATURE.getType(), targetDate);
        if (!Objects.isNull(weatherCityHisDO)){
            DateValuesDTO weather = new DateValuesDTO();
            weather.setDate(targetDateStr);
            weather.setValues(weatherCityHisDO.getWeatherList());
            weatherList.add(weather);
        }
        //特性统计
        SimilarDayFeatureDTO feature = new SimilarDayFeatureDTO();
        feature.setDate(targetDateStr);
        String targetDateType = DateUtil.getDayOfWeekByDate(targetDate);
        feature.setDateType(targetDateType);

        WeatherFeatureCityDayHisDO weatherFeatureCityHisDO = weatherFeatureCityDayHisService.findWeatherFeatureCityHisVOByDate(cityId, targetDate);
        if (!Objects.isNull(weatherFeatureCityHisDO)){
            feature.setMaxTemp(weatherFeatureCityHisDO.getHighestTemperature());
            feature.setMinTemp(weatherFeatureCityHisDO.getLowestTemperature());
            feature.setSumAvgTemp(weatherFeatureCityHisDO.getTotalAvgTemperature());
        }
        List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisDOS = loadFeatureCityDayHisService.findLoadFeatureCityDayHisDOS(
            cityId, targetDate, targetDate, caliberId);
        if (!CollectionUtils.isEmpty(loadFeatureCityDayHisDOS)) {
            LoadFeatureCityDayHisDO loadFeatureCityDayHisDO = loadFeatureCityDayHisDOS.get(0);
            if (!Objects.isNull(loadFeatureCityDayHisDO)) {
                feature.setMaxLoad(loadFeatureCityDayHisDO.getMaxLoad());
                feature.setMinLoad(loadFeatureCityDayHisDO.getMinLoad());
            }
        }
        featureList.add(feature);
    }

    //生成相似日
    public List<SimilarDateBean> generateSimilarData(SimilarFeatureDTO similarFeatureDTO) throws Exception {
        SimilarDayParam similarDayParam = BeanCopierUtils.copyProperties(similarFeatureDTO, SimilarDayParam.class);
        List<Date> chooseDateList = similarFeatureDTO.getChooseDateList();
        String exclude = similarFeatureDTO.getExclude();
        similarDayParam.setUserId(UuidUtils.generateUuid());
        String startTime = get96Num(similarDayParam.getStartTime());
        String endTime = get96Num(similarDayParam.getEndTime());
        similarDayParam.setStartTime(startTime);
        similarDayParam.setEndTime(endTime);
        String maxResultCount = similarDayParam.getMaxResultCount();
        if (StringUtils.isBlank(maxResultCount)){
            similarDayParam.setMaxResultCount("10");
        }
        List<SimilarDateBean> similarDateBeans = null;
        try {
            similarDateBeans = similarDayService.querySimilarDayList(similarDayParam);
        }catch (Exception e){
            System.out.println("生成相似日有误");
        }
        if (!CollectionUtils.isEmpty(similarDateBeans)){
            List<String> chooseDateListStr = chooseDateList.stream()
                .map(date -> DateFormatUtils.format(date, DateFormatType.SIMPLE_DATE_FORMAT_STR.getValue()))
                .collect(Collectors.toList());
            if (StringUtils.isNotBlank(exclude)){
                BigDecimal standard = BigDecimal.valueOf(Integer.parseInt(exclude));
                similarDateBeans = similarDateBeans.stream()
                    .filter(iterm -> BigDecimalFunctions.ge(BigDecimalFunctions.multiply(iterm.getDegree(), new BigDecimal("100")), standard)).collect(Collectors.toList());
            }
            if (!CollectionUtils.isEmpty(chooseDateListStr) && !CollectionUtils.isEmpty(similarDateBeans)){
                similarDateBeans = similarDateBeans.stream()
                    .filter(iterm -> chooseDateListStr.contains(DateFormatUtils.format(iterm.getDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR.getValue()))).collect(Collectors.toList());
            }
            return similarDateBeans.stream()
                .filter(iterm -> !iterm.getDate().equals(similarFeatureDTO.getTargetDate())).collect(Collectors.toList());
        }
        return similarDateBeans;
    }

    //返回相似日列表
    public void querySimilarDayListDTOList(List<SimilarDateBean> similarDateBeans, String cityId, String caliberId, List<SimilarDayListDTO> similarDayList, BigDecimal max) throws Exception {
        List<Date> days = similarDateBeans.stream().map(SimilarDateBean::getDate).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(days)){
            return;
        }
        List<LoadFeatureCityDayHisDO> hisData = loadFeatureCityDayHisService.listLoadFeature(cityId ,caliberId, days);
        days.forEach(date -> {
            SimilarDayListDTO similarDayListDTO = new SimilarDayListDTO();
            similarDayListDTO.setDate(DateFormatUtils.format(date, DateFormatType.SIMPLE_DATE_FORMAT_STR.getValue()));
            Optional<SimilarDateBean> first = similarDateBeans.stream().filter(iterm -> iterm.getDate().equals(date)).findFirst();
            if (first.isPresent()){
                SimilarDateBean similarDateBean = first.get();
                similarDayListDTO.setDegree(similarDateBean.getDegree());
            }
            Optional<LoadFeatureCityDayHisDO> hisDO = hisData.stream().filter(iterm -> iterm.getDate().equals(date)).findFirst();
            if (hisDO.isPresent()){
                LoadFeatureCityDayHisDO loadFeatureCityDayHisDO = hisDO.get();
                similarDayListDTO.setDeviation(BigDecimalFunctions.subtract(max, loadFeatureCityDayHisDO.getMaxLoad()));
            }
            similarDayList.add(similarDayListDTO);
        });
    }

    //获取时间点
    public String get96Num(String time){
        String[] split = time.split(":");
        String hour = split[0];
        String minute = split[1];

        int hourInt = Integer.parseInt(hour);
        int minuteInt = Integer.parseInt(minute);

        int count = 0;
        count += hourInt * 4;
        count += minuteInt / 15;
        return String.valueOf(count);
    }

    //生成显示相似日日期
    public List<Date> genShowDate(List<SimilarDayListDTO> similarDayListDTOS){
        if (!CollectionUtils.isEmpty(similarDayListDTOS)){
            return similarDayListDTOS.stream()
                .map(iterm -> DateUtils.string2Date(iterm.getDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR))
                .collect(Collectors.toList());
        }
        return null;
    }

    //负荷对比曲线相似日数据
    public void loadCompare(List<Date> showDates, String cityId, String caliberId, List<DateValuesDTO> loadList){
        if (CollectionUtils.isEmpty(showDates)){
            return;
        }
        List<LoadCityHisDO> showLoadList = loadCityHisService.queryLoadCityHisDOList(showDates, cityId, caliberId);
        if (!CollectionUtils.isEmpty(showLoadList)){
            showLoadList.forEach(iterm -> {
                DateValuesDTO load = new DateValuesDTO();
                load.setDate(DateFormatUtils.format(iterm.getDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR.getValue()));
                load.setValues(BigDecimalFunctions.listScale(BigDecimalFunctions.listDivideValue(iterm.getloadList(), new BigDecimal("10")), 1));
                loadList.add(load);
            });
        }
    }

    //查询气象相似日曲线
    public void weatherCompare(List<Date> showDates, String cityId, List<DateValuesDTO> weatherList){
        if (CollectionUtils.isEmpty(showDates)){
            return;
        }
        List<WeatherCityHisDO> weatherHisList = weatherCityHisService.queryWeatherCityHisDOList(showDates, cityId, WeatherEnum.TEMPERATURE.getType());
        if (!CollectionUtils.isEmpty(weatherHisList)){
            weatherHisList.forEach(iterm -> {
                DateValuesDTO weather = new DateValuesDTO();
                weather.setDate(DateFormatUtils.format(iterm.getDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR.getValue()));
                weather.setValues(iterm.getWeatherList());
                weatherList.add(weather);
            });
        }
    }
    //查询特性统计
    public void queryFeatureStatics(List<Date> showDates, String cityId, String caliberId, List<SimilarDayFeatureDTO> featureList) throws Exception {
        List<WeatherFeatureCityDayHisDO> featureStatics = weatherFeatureCityDayHisService.listWeatherFeatureCityDayHisDO(showDates, cityId);
        List<LoadFeatureCityDayHisDO> loadFeatureHis = loadFeatureCityDayHisService.listLoadFeature(cityId, caliberId, showDates);
        showDates.forEach(date -> {
            SimilarDayFeatureDTO featureDTO = new SimilarDayFeatureDTO();
            String dateType = DateUtil.getDayOfWeekByDate(date);
            featureDTO.setDate(DateFormatUtils.format(date, DateFormatType.SIMPLE_DATE_FORMAT_STR.getValue()));
            featureDTO.setDateType(dateType);
            Optional<WeatherFeatureCityDayHisDO> weather = featureStatics.stream().filter(iterm -> iterm.getDate().equals(date)).findFirst();
            if (weather.isPresent()){
                WeatherFeatureCityDayHisDO temp = weather.get();
                featureDTO.setMaxTemp(temp.getHighestTemperature());
                featureDTO.setMinTemp(temp.getLowestTemperature());
                featureDTO.setSumAvgTemp(temp.getTotalAvgTemperature());
            }
            Optional<LoadFeatureCityDayHisDO> loadFeature = loadFeatureHis.stream().filter(iterm -> iterm.getDate().equals(date)).findFirst();
            if (loadFeature.isPresent()){
                LoadFeatureCityDayHisDO feature = loadFeature.get();
                featureDTO.setMaxLoad(feature.getMaxLoad());
                featureDTO.setMinLoad(feature.getMinLoad());
            }
            featureList.add(featureDTO);
        });
    }
}
