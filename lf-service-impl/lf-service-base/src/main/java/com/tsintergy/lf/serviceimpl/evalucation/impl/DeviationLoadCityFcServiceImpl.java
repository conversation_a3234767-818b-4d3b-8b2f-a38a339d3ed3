
package com.tsintergy.lf.serviceimpl.evalucation.impl;

import com.tsieframework.core.base.dao.DataPackage;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.evalucation.api.DeviationLoadCityFcService;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.DeviationLoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.load.dto.CityValueDTO;
import com.tsintergy.lf.serviceimpl.evalucation.dao.DeviationLoadCityFcDAO;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version $Id: DeviationLoadCityFcServiceImpl.java, v 0.1 2018-01-31 10:15:46 tao Exp $$
 */

@Service("deviationLoadCityFcService")
public class DeviationLoadCityFcServiceImpl extends BaseServiceImpl implements DeviationLoadCityFcService {
    private static final Logger logger = LogManager.getLogger(DeviationLoadCityFcServiceImpl.class);

    @Autowired
    private CityService cityService;
    @Autowired
    private DeviationLoadCityFcDAO deviationLoadCityFcDAO;

    @Override
    public DataPackage queryDeviationLoadCityFcDO(DBQueryParam param) throws Exception {
        try {
            return deviationLoadCityFcDAO.query(param);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException(e.getMessage(), e);
        }
    }

    @Override
    public DeviationLoadCityFcDO doCreate(DeviationLoadCityFcDO vo) throws Exception {
        try {
            return deviationLoadCityFcDAO.create(vo);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException(e.getMessage(), e);
        }
    }

    @Override
    public void doRemoveDeviationLoadCityFcDO(DeviationLoadCityFcDO vo) throws Exception {
        try {
            deviationLoadCityFcDAO.remove(vo);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException(e.getMessage(), e);
        }
    }

    @Override
    public void doRemoveDeviationLoadCityFcDOByPK(Serializable pk) throws Exception {
        try {
            deviationLoadCityFcDAO.removeByPk(pk);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException(e.getMessage(), e);
        }
    }

    @Override
    public DeviationLoadCityFcDO doUpdateDeviationLoadCityFcDO(DeviationLoadCityFcDO vo) throws Exception {
        try {
            return deviationLoadCityFcDAO.update(vo);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException(e.getMessage(), e);
        }
    }

    @Override
    public DeviationLoadCityFcDO findDeviationLoadCityFcDOByPk(Serializable pk) throws Exception {
        try {
            return deviationLoadCityFcDAO.findByPk(pk);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException(e.getMessage(), e);
        }
    }

    @Override
    public List<CityValueDTO> find24DeviationLoadCityFcByDateAndCityIds(Date date, List<String> cityIds) {
        return find24DeviationLoadCityFcByDateAndCityIds(date, null, cityIds);
    }

    @Override
    public List<CityValueDTO> find24DeviationLoadCityFcByDateAndCityIds(Date date, String caliberId, List<String> cityIds) {
        List<CityValueDTO> cityValueDTOS = new ArrayList<CityValueDTO>();
        try {
            List<DeviationLoadCityFcDO> deviationLoadCityFcVOS = deviationLoadCityFcDAO.getDeviationLoadCityFcDOS(cityIds, caliberId, date, date, true);
            if (deviationLoadCityFcVOS.size() < 1) {
                throw TsieExceptionUtils.newBusinessException("01C20180002");
            } else {
                for (DeviationLoadCityFcDO deviationLoadCityFcVO : deviationLoadCityFcVOS) {
                    CityValueDTO cityValueDTO = new CityValueDTO();
                    cityValueDTO.setCityId(deviationLoadCityFcVO.getCityId());
                    cityValueDTO.setCity(cityService.findCityById(deviationLoadCityFcVO.getCityId()).getCity());
                    cityValueDTO.setValue(BasePeriodUtils.toList(deviationLoadCityFcVO, 24, Constants.LOAD_CURVE_START_WITH_ZERO)); // 转24点数据
                    cityValueDTOS.add(cityValueDTO);
                }
            }
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException("01C20180003", e);

        }
        return cityValueDTOS;
    }


    @Override
    public List<DeviationLoadCityFcDO> findDeviationLoadCityFcDO(String cityId, String caliberId, String algorithmId, Date startDate, Date endDate) throws Exception {
        return deviationLoadCityFcDAO.getDeviationLoadCityFcDO(cityId, caliberId, algorithmId, startDate, endDate);
    }

    @Override
    public List<DeviationLoadCityFcDO> doSaveOrUpdateDeviationLoadCityFcDOs(
        List<DeviationLoadCityFcDO> accuracyLoadCityFcVOS) throws Exception {
        List<DeviationLoadCityFcDO> deviationLoadCityFcDOS = deviationLoadCityFcDAO
            .doSaveOrUpdateDeviationLoadCityFcDOs(accuracyLoadCityFcVOS);
        return deviationLoadCityFcDOS;
    }


}
