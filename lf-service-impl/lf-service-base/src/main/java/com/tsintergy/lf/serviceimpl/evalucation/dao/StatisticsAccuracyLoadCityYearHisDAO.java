package com.tsintergy.lf.serviceimpl.evalucation.dao;

import com.tsieframework.core.base.dao.hibernate.BaseAbstractDAO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.dao.hibernate.QueryOp;
import com.tsieframework.util.compatible.StringUtils;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.StatisticsAccuracyLoadCityYearHisDO;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class StatisticsAccuracyLoadCityYearHisDAO extends BaseAbstractDAO<StatisticsAccuracyLoadCityYearHisDO> {

    public List<StatisticsAccuracyLoadCityYearHisDO> getStatisticsAccuracyLoadCityMonthHisDOs(String cityId, String caliberId, String startYear, String endYear) throws  Exception{
        DBQueryParamBuilder param = DBQueryParamBuilder.create().queryDataOnly();
        param.pageSize("0");
        if (!StringUtils.isBlank(cityId)) {
            param.where(QueryOp.StringEqualTo, "cityId", cityId);
        }
        if (!StringUtils.isBlank(caliberId)) {
            param.where(QueryOp.StringEqualTo, "caliberId", caliberId);
        }
        if (null != startYear) {
            param.where(QueryOp.StringNoLessThan, "year", startYear);
        }
        if (null != endYear) {
            param.where(QueryOp.StringNoMoreThan, "year", endYear);
        }

        List<StatisticsAccuracyLoadCityYearHisDO> datas = query(param.build()).getDatas();
        return datas;

    }

    public List<StatisticsAccuracyLoadCityYearHisDO> findAccuracyMonth(String year, String city, String caliberId){
        DBQueryParamBuilder param = DBQueryParamBuilder.create().queryDataOnly();
        param.pageSize("0");
        if (!StringUtils.isBlank(city)) {
            param.where(QueryOp.StringEqualTo, "cityId", city);
        }
        if (!StringUtils.isBlank(year)) {
            param.where(QueryOp.StringEqualTo, "year", year);
        }
        if (!StringUtils.isBlank(caliberId)) {
            param.where(QueryOp.StringEqualTo, "caliberId", caliberId);
        }

        return query(param.build()).getDatas();
    }
}