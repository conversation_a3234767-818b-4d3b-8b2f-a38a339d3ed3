package com.tsintergy.lf.serviceimpl.trade.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsintergy.lf.serviceapi.base.trade.api.IndustryCityLoadDayHisClctService;
import com.tsintergy.lf.serviceapi.base.trade.pojo.IndustryCityLoadDayHisClctDO;
import com.tsintergy.lf.serviceimpl.trade.dao.IndustryCityLoadDayHisClctDAO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;

@Service("industryCityLoadDayHisClctSerivce")
public class industryCityLoadDayHisClctServiceImpl implements IndustryCityLoadDayHisClctService {

    @Autowired
    private IndustryCityLoadDayHisClctDAO industryCityLoadDayHisClctDAO;

    @Override
    public List<IndustryCityLoadDayHisClctDO> findIndustryCityLoadDayHisClctDOS(String cityId, List<String> tradeCodes, Date startDate, Date endDate) {
        return industryCityLoadDayHisClctDAO.findAll(JpaWrappers.<IndustryCityLoadDayHisClctDO>lambdaQuery()
                .in(!CollectionUtils.isEmpty(tradeCodes), IndustryCityLoadDayHisClctDO::getTradeCode, tradeCodes)
                .eq(!StringUtils.isEmpty(cityId), IndustryCityLoadDayHisClctDO::getCityId, cityId)
                .le(endDate != null, IndustryCityLoadDayHisClctDO::getDate, endDate)
                .ge(startDate != null, IndustryCityLoadDayHisClctDO::getDate, startDate)
        );
    }

    @Override
    public List<IndustryCityLoadDayHisClctDO> findIndustryCityLoadDayHisClctDOS(String cityId, List<String> tradeCodes, List<Date> dates) {
        return industryCityLoadDayHisClctDAO.findAll(JpaWrappers.<IndustryCityLoadDayHisClctDO>lambdaQuery()
                .in(!CollectionUtils.isEmpty(tradeCodes), IndustryCityLoadDayHisClctDO::getTradeCode, tradeCodes)
                .in(!CollectionUtils.isEmpty(dates), IndustryCityLoadDayHisClctDO::getDate, dates)
                .eq(!StringUtils.isEmpty(cityId), IndustryCityLoadDayHisClctDO::getCityId, cityId)
        );
    }
}
