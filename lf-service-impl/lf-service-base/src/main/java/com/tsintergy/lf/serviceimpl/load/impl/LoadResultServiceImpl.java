/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.load.impl;

import com.alibaba.nacos.common.utils.MapUtils;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.math.BigDecimalFunctions;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.lf.core.constants.CityConstants;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.enums.ReportTimeEnum;
import com.tsintergy.lf.core.util.DataUtil;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.core.util.LoadCalUtil;
import com.tsintergy.lf.core.util.QuarterUtil;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.bgd.api.LoadFeatureCityTenDaysFcService;
import com.tsintergy.lf.serviceapi.base.bgd.api.LoadFeatureCityTenDaysHisService;
import com.tsintergy.lf.serviceapi.base.bgd.api.StatisticsCityMonthFcFeatureService;
import com.tsintergy.lf.serviceapi.base.bgd.pojo.LoadCityDayReportServiceDO;
import com.tsintergy.lf.serviceapi.base.bgd.pojo.LoadCityMonthAccuracyReportDO;
import com.tsintergy.lf.serviceapi.base.bgd.pojo.LoadFeatureCityTenDaysFcServiceDO;
import com.tsintergy.lf.serviceapi.base.bgd.pojo.LoadFeatureCityTenDaysHisServiceDO;
import com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils;
import com.tsintergy.lf.serviceapi.base.evalucation.api.*;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.DayAssessDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.AccuracyAssessDO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.AccuracyCompositeDO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.StatisticsCityDayFcDO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.StatisticsCityDayFcFeatureDO;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcService;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.load.api.*;
import com.tsintergy.lf.serviceapi.base.load.dto.*;
import com.tsintergy.lf.serviceapi.base.load.pojo.*;
import com.tsintergy.lf.serviceapi.base.system.api.ReportSystemService;
import com.tsintergy.lf.serviceapi.base.system.pojo.ReportSystemInitDO;
import com.tsintergy.lf.serviceimpl.common.util.DateUtils;
import com.tsintergy.lf.serviceimpl.evalucation.dao.AccuracyCompositeDAO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.Map.Entry;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/3/31 14:21
 * @Version: 1.0.0
 */
@Service(value = "loadResultService")
public class LoadResultServiceImpl implements LoadResultService {

    private static final String CITY_EVALUATION_ALGORITHM_ID = "21";

    @Autowired
    private LoadCityFcService loadCityFcService;

    @Autowired
    private LoadCityHisService loadCityHisService;

    @Autowired
    private StatisticsCityDayFcService statisticsCityDayFcService;

    @Autowired
    private LoadFeatureCityDayFcService loadFeatureCityDayFcService;

    @Autowired
    private LoadFeatureCityDayHisService loadFeatureCityDayHisService;

    @Autowired
    private LoadFeatureCityMonthFcService loadFeatureCityMonthFcService;

    @Autowired
    private LoadFeatureCityMonthHisService loadFeatureCityMonthHisService;

    @Autowired
    private LoadFeatureCityTenDaysFcService loadFeatureCityTenDaysFcService;

    @Autowired
    private LoadFeatureCityTenDaysHisService loadFeatureCityTenDaysHisService;

    @Autowired
    private CityService cityService;

    @Autowired
    private ReportSystemService reportSystemService;

    @Autowired
    private StatisticsCityDayFcFeatureService statisticsCityDayFcFeatureService;

    @Autowired
    private StatisticsCityMonthFcFeatureService statisticsCityMonthFcFeatureService;

    @Autowired
    private AccuracyAssessService accuracyAssessService;

    @Autowired
    private AccuracyCompositeService accuracyCompositeService;

    @Resource
    private AccuracyCompositeDAO accuracyCompositeDAO;

    @Autowired
    private BatchDataFilterService batchDataFilterService;

    @Override
    public LoadAccuracyDTO findAccuracy(String cityId, String caliberId, Date date) throws Exception {
        LoadAccuracyDTO loadAccuracyDTO = new LoadAccuracyDTO();
        //实际负荷
        List<LoadCityHisDO> loadCityHisDOS = loadCityHisService.getLoadCityHisDOS(cityId, caliberId, date, date);
        LoadCityHisDO loadCityHisDO = null;
        if (CollectionUtils.isNotEmpty(loadCityHisDOS)) {
            loadCityHisDO = loadCityHisDOS.get(0);
            loadAccuracyDTO.setHisLoad(BasePeriodUtils
                .toList(loadCityHisDO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO));
        }
        //最终上报
        LoadCityFcDO fcDO = loadCityFcService.getReport(cityId, caliberId, date);
        if (fcDO != null) {
            List<BigDecimal> load = BasePeriodUtils
                .toList(fcDO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
            loadAccuracyDTO.setReportCurve(load);
            //预测准确率
            List<BigDecimal> hisLoad = loadAccuracyDTO.getHisLoad();
            List<BigDecimal> reportCurve = loadAccuracyDTO.getReportCurve();
            List<BigDecimal> pointAccuracys = new ArrayList<>();
            for (int i = 0; i <= 95; i++) {
                BigDecimal his = hisLoad.get(i);
                BigDecimal fc = reportCurve.get(i);
                BigDecimal accuracy = LoadCalUtil.getAccuracy(his, fc);
                pointAccuracys.add(accuracy);
            }
            loadAccuracyDTO.setAccuracy(pointAccuracys);
            List<BigDecimal> deviation = getDeviation(fcDO, loadCityHisDO);
            loadAccuracyDTO.setDeviation(deviation);
            String algorithmId = fcDO.getAlgorithmId();
            List<StatisticsCityDayFcDO> dayFcDOS = statisticsCityDayFcService
                .getDayAccuracyList(cityId, caliberId, algorithmId, date, date);
            if (CollectionUtils.isNotEmpty(dayFcDOS)) {
                loadAccuracyDTO.setDayAccuracy(dayFcDOS.get(0).getAccuracy());
            } else {
                BigDecimal dayAccuracy = LoadCalUtil
                    .getDayAccuracy(loadCityHisDO, fcDO, Constants.LOAD_CURVE_POINT_NUM);
                loadAccuracyDTO.setDayAccuracy(dayAccuracy);
            }
        }
        return loadAccuracyDTO;
    }

    @Override
    public List<LoadDaysAccuracyDTO> findDaysAccuracy(String cityId, String caliberId, Date startDate, Date endDate)
        throws Exception {
        List<LoadDaysAccuracyDTO> result = new ArrayList<>();
        // 96点实际负荷
        List<LoadCityHisDO> loadCityHisDOS = loadCityHisService.getLoadCityHisDOS(cityId, caliberId, startDate, endDate);
        Map<java.sql.Date, LoadCityHisDO> hisCollect = new HashMap<>();
        if (CollectionUtils.isNotEmpty(loadCityHisDOS)) {
            hisCollect = loadCityHisDOS.stream()
                .collect(Collectors.toMap(t -> t.getDate(), t -> t));
        }
        // 96点最终上报
        List<LoadCityFcDO> reportLoadFc = loadCityFcService.findReportLoadFc(cityId, caliberId, startDate, endDate);
        Map<java.sql.Date, LoadCityFcDO> fcCollect = new HashMap<>();
        if (CollectionUtils.isNotEmpty(reportLoadFc)) {
            fcCollect = reportLoadFc.stream()
                .collect(Collectors.toMap(t -> t.getDate(), t -> t));
        }
        // 实际负荷
        List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisDOS = loadFeatureCityDayHisService.findLoadFeatureCityDayHisDOS(
            cityId, startDate, endDate, caliberId);
        Map<java.sql.Date, LoadFeatureCityDayHisDO> featureHisCollect = new HashMap<>();
        if (CollectionUtils.isNotEmpty(loadFeatureCityDayHisDOS)) {
            featureHisCollect = loadFeatureCityDayHisDOS.stream()
                .collect(Collectors.toMap(t -> t.getDate(), t -> t));
        }
        // 最终上报
        List<LoadFeatureCityDayFcDO> loadFeatureCityDayFcReportList = loadFeatureCityDayFcService.findLoadFeatureCityDayFcReportList(
            cityId, startDate, endDate, caliberId);
        Map<java.sql.Date, LoadFeatureCityDayFcDO> featureFcCollect = loadFeatureCityDayFcReportList.stream()
            .collect(Collectors.toMap(t -> t.getDate(), t -> t));
        // 上报准确率
        List<StatisticsCityDayFcFeatureDO> statisticsCityDayFcFeatureDOReportList = statisticsCityDayFcFeatureService.getStatisticsCityDayFcFeatureDOReportList(
            cityId, caliberId, startDate, endDate);
        Map<Date, StatisticsCityDayFcFeatureDO> collect = statisticsCityDayFcFeatureDOReportList.stream()
            .collect(Collectors.toMap(t -> t.getDate(), t -> t));
        List<Date> listBetweenDay = DateUtil.getListBetweenDay(startDate, endDate);
        for (Date date : listBetweenDay) {
            LoadDaysAccuracyDTO loadDaysAccuracyDTO = new LoadDaysAccuracyDTO();
            loadDaysAccuracyDTO.setDate(date);
            LoadCityHisDO loadCityHisDO = new LoadCityHisDO();
            LoadCityFcDO loadCityFcDO = new LoadCityFcDO();
            LoadFeatureCityDayFcDO loadFeatureCityDayFcDO = new LoadFeatureCityDayFcDO();
            LoadFeatureCityDayHisDO loadFeatureCityDayHisDO = new LoadFeatureCityDayHisDO();
            StatisticsCityDayFcFeatureDO statisticsCityDayFcFeatureDO = new StatisticsCityDayFcFeatureDO();
            if (MapUtils.isNotEmpty(hisCollect)) {
                loadCityHisDO = hisCollect.get(date);
            }
            if (MapUtils.isNotEmpty(fcCollect)) {
                loadCityFcDO = fcCollect.get(date);
            }
            if (MapUtils.isNotEmpty(featureHisCollect)) {
                loadFeatureCityDayHisDO = featureHisCollect.get(date);
            }
            if (MapUtils.isNotEmpty(featureFcCollect)) {
                loadFeatureCityDayFcDO = featureFcCollect.get(date);
            }
            if (MapUtils.isNotEmpty(collect)) {
                statisticsCityDayFcFeatureDO = collect.get(date);
            }
            // 日均准确率
            BigDecimal dayAccuracy = null;
            if (loadCityHisDO != null && loadCityFcDO != null) {
                dayAccuracy = LoadCalUtil.getDayAccuracy(loadCityHisDO, loadCityFcDO,
                    Constants.LOAD_CURVE_POINT_NUM);
            }
            BigDecimal maxLoadAccuracy = null;
            BigDecimal minLoadAccuracy = null;
            if (loadFeatureCityDayHisDO !=null && loadFeatureCityDayFcDO != null) {
                // 日最大负荷
                maxLoadAccuracy = LoadCalUtil.getAccuracy(loadFeatureCityDayHisDO.getMaxLoad(),
                    loadFeatureCityDayFcDO.getMaxLoad());
                // 日最小负荷
                minLoadAccuracy = LoadCalUtil.getAccuracy(loadFeatureCityDayHisDO.getMinLoad(),
                    loadFeatureCityDayFcDO.getMinLoad());
            }
            if (statisticsCityDayFcFeatureDO != null) {
                maxLoadAccuracy = statisticsCityDayFcFeatureDO.getMaxLoadAccuracy();
                minLoadAccuracy = statisticsCityDayFcFeatureDO.getMinLoadAccuracy();
            }
            loadDaysAccuracyDTO.setDayAccuracy(dayAccuracy);
            loadDaysAccuracyDTO.setMaxLoadAccuracy(maxLoadAccuracy);
            loadDaysAccuracyDTO.setMinLoadAccuracy(minLoadAccuracy);
            List<BigDecimal> bigDecimalSum = new ArrayList<>();
            if (dayAccuracy != null) {
                bigDecimalSum.add(dayAccuracy);
            }
            if (maxLoadAccuracy != null) {
                bigDecimalSum.add(maxLoadAccuracy);
            }
            if (minLoadAccuracy != null) {
                bigDecimalSum.add(minLoadAccuracy);
            }
            if (CollectionUtils.isNotEmpty(bigDecimalSum)) {
                loadDaysAccuracyDTO.setAvgAccuracy(BigDecimalUtils.listAvg(bigDecimalSum));
            }
            result.add(loadDaysAccuracyDTO);
        }
        return result;

    }

    @Override
    public List<LoadDaysAccuracyDTO> findMonthAccuracy(String cityId, String caliberId, Date startDate, Date endDate)
        throws Exception {
        startDate = DateUtil.getFirstDayOfMonth(startDate);
        endDate = DateUtil.getLastDayOfMonth(endDate);
        List<LoadDaysAccuracyDTO> result = new ArrayList<>();
        List<String> monthBetween = DateUtil.getMonthBetween(DateUtils.getDateToStrFORMAT(startDate, "yyyy-MM"),
            DateUtils.getDateToStrFORMAT(endDate, "yyyy-MM"));
        List<String> monthList = new ArrayList<>();
        for (String s : monthBetween) {
            monthList.add(s.split("-")[1]);
        }
        // 96点实际负荷
        List<LoadCityHisDO> loadCityHisDOS = loadCityHisService.getLoadCityHisDOS(cityId, caliberId, startDate, endDate);
        Map<Object, List<LoadCityHisDO>> hisCollect = loadCityHisDOS.stream()
            .collect(
                Collectors.groupingBy(t -> DateUtils.getDateToStrFORMAT(t.getDate(), "yyyy-MM")));
        // 96点最终上报
        List<LoadCityFcDO> reportLoadFc = loadCityFcService.findReportLoadFc(cityId, caliberId, startDate, endDate);
        Map<String, List<LoadCityFcDO>> fcCollect = reportLoadFc.stream()
            .collect(
                Collectors.groupingBy(t -> DateUtils.getDateToStrFORMAT(t.getDate(), "yyyy-MM")));
        //日特性准确率
        Map<String, List<StatisticsCityDayFcFeatureDO>> dayFeatureAccuracyMap = statisticsCityDayFcFeatureService.getStatisticsCityDayFcFeatureDOReportList(cityId, caliberId, startDate, endDate).stream().collect(Collectors.groupingBy(t -> DateUtils.getDateToStrFORMAT(t.getDate(), "yyyy-MM")));
        // 上报

        for (String yM : monthBetween) {
            LoadDaysAccuracyDTO loadDaysAccuracyDTO = new LoadDaysAccuracyDTO();
            loadDaysAccuracyDTO.setYearMonth(yM);
            List<BigDecimal> mList = new ArrayList<>();
            //当月实际负荷
            List<LoadCityHisDO> monthHis = hisCollect.get(yM);
            //当月预测
            List<LoadCityFcDO> monthFc = fcCollect.get(yM);
            //96点准确率
            if (CollectionUtils.isNotEmpty(monthHis) && CollectionUtils.isNotEmpty(monthFc)){
                Map<java.sql.Date, LoadCityFcDO> dayFcMap = monthFc.stream().collect(Collectors.toMap(LoadCityFcDO::getDate, Function.identity()));

                monthHis.forEach(his->{
                    LoadCityFcDO fc = dayFcMap.get(his.getDate());
                    if (Objects.nonNull(fc)){
                        try {
                            BigDecimal dayAccuracy = LoadCalUtil.getDayAccuracy(his, fc,
                                    Constants.LOAD_CURVE_POINT_NUM);
                            mList.add(dayAccuracy);
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    }
                });
                loadDaysAccuracyDTO.setDayAccuracy(BigDecimalUtils.listAvg(mList));
            }

            List<StatisticsCityDayFcFeatureDO> dayFcFeatureDO = dayFeatureAccuracyMap.get(yM);
            if(Objects.nonNull(dayFcFeatureDO)){
                loadDaysAccuracyDTO.setMaxLoadAccuracy(BigDecimalUtils.listAvg(dayFcFeatureDO.stream().map(StatisticsCityDayFcFeatureDO::getMaxLoadAccuracy).collect(Collectors.toList())));
                loadDaysAccuracyDTO.setMinLoadAccuracy(BigDecimalUtils.listAvg(dayFcFeatureDO.stream().map(StatisticsCityDayFcFeatureDO::getMinLoadAccuracy).collect(Collectors.toList())));
            }


            List<BigDecimal> bigDecimalSum = new ArrayList<>();
            if (loadDaysAccuracyDTO.getDayAccuracy() != null) {
                bigDecimalSum.add(loadDaysAccuracyDTO.getDayAccuracy());
            }
            if (loadDaysAccuracyDTO.getMaxLoadAccuracy() != null) {
                bigDecimalSum.add(loadDaysAccuracyDTO.getMaxLoadAccuracy());
            }
            if (loadDaysAccuracyDTO.getMinLoadAccuracy() != null) {
                bigDecimalSum.add(loadDaysAccuracyDTO.getMinLoadAccuracy());
            }
            if (CollectionUtils.isNotEmpty(bigDecimalSum)) {
                loadDaysAccuracyDTO.setAvgAccuracy(BigDecimalUtils.listAvg(bigDecimalSum));
            }
            result.add(loadDaysAccuracyDTO);
        }
        return result;
    }

    @Override
    public List<LoadDaysAccuracyDTO> findYearAccuracy(String cityId, String caliberId, String startYear, String endYear)
        throws Exception {

        Date startDate = DateUtil.getYearStat(startYear);
        Date endDate = DateUtil.getYearEnd(endYear);
        List<LoadDaysAccuracyDTO> monthAccuracy = findMonthAccuracy(cityId, caliberId, startDate, endDate);
        List<LoadDaysAccuracyDTO> result = new ArrayList<>();
        //根据年分组
        Map<String, List<LoadDaysAccuracyDTO>> yearMap = monthAccuracy.stream().collect(Collectors.groupingBy(t -> t.getYearMonth().split("-")[0]));
        yearMap.forEach((year,value)->{
            LoadDaysAccuracyDTO loadDaysAccuracyDTO = new LoadDaysAccuracyDTO();
            loadDaysAccuracyDTO.setYear(year);
            List<BigDecimal> dayList = value.stream().map(LoadDaysAccuracyDTO::getDayAccuracy).filter(Objects::nonNull).collect(Collectors.toList());
            List<BigDecimal> maxLoadList = value.stream().map(LoadDaysAccuracyDTO::getMaxLoadAccuracy).filter(Objects::nonNull).collect(Collectors.toList());
            List<BigDecimal> minLoadList = value.stream().map(LoadDaysAccuracyDTO::getMinLoadAccuracy).filter(Objects::nonNull).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(dayList)){
                loadDaysAccuracyDTO.setDayAccuracy(BigDecimalFunctions.listAvg(dayList));
            }
            if (CollectionUtils.isNotEmpty(maxLoadList)){
                loadDaysAccuracyDTO.setMaxLoadAccuracy(BigDecimalFunctions.listAvg(maxLoadList));
            }
            if (CollectionUtils.isNotEmpty(minLoadList)){
                loadDaysAccuracyDTO.setMinLoadAccuracy(BigDecimalFunctions.listAvg(minLoadList));
            }
            List<BigDecimal> bigDecimalSum = new ArrayList<>();
            if (loadDaysAccuracyDTO.getDayAccuracy() != null) {
                bigDecimalSum.add(loadDaysAccuracyDTO.getDayAccuracy());
            }
            if (loadDaysAccuracyDTO.getMaxLoadAccuracy() != null) {
                bigDecimalSum.add(loadDaysAccuracyDTO.getMaxLoadAccuracy());
            }
            if (loadDaysAccuracyDTO.getMinLoadAccuracy() != null) {
                bigDecimalSum.add(loadDaysAccuracyDTO.getMinLoadAccuracy());
            }
            if (CollectionUtils.isNotEmpty(bigDecimalSum)) {
                loadDaysAccuracyDTO.setAvgAccuracy(BigDecimalUtils.listAvg(bigDecimalSum));
            }
            result.add(loadDaysAccuracyDTO);
        });
//        List<String> yearList = new ArrayList<>();
//        Date startDate = org.apache.commons.lang3.time.DateUtils.addDays(new Date(), -1);
//        String year1 = DateUtil.getYearByDate(startDate);
//        String month1 = DateUtil.getMonthByDate(startDate);
//        for (int i = 0; i <= Integer.valueOf(endYear) - Integer.valueOf(startYear); i++) {
//            int i1 = Integer.valueOf(startYear) + i;
//            yearList.add(String.valueOf(i1));
//        }
//        String year = null;
//        for (int i = 0; i < yearList.size(); i++) {
//            year = yearList.get(i);
//            Date firstDay = DateUtil.getFirstDay(Integer.valueOf(year), 1);
//            Date lastDay = DateUtil.getLastDay(Integer.valueOf(year), 12);
//            String dateToStrFORMAT = DateUtils.getDateToStrFORMAT(firstDay, "yyyy-MM");
//            String dateToStrFORMAT1 = DateUtils.getDateToStrFORMAT(lastDay, "yyyy-MM");
//            List<String> monthList = DateUtil.getMonthList();
//            String sYear = DateUtils.getDateToStrFORMAT(firstDay, "yyyy-MM").split("-")[0];
//            // 96点实际负荷
//            List<LoadCityHisDO> loadCityHisDOS = loadCityHisService.getLoadCityHisDOS(cityId, caliberId, firstDay, lastDay);
//            Map<Object, List<LoadCityHisDO>> hisCollect = loadCityHisDOS.stream()
//                .collect(
//                    Collectors.groupingBy(t -> DateUtils.getDateToStrFORMAT(t.getDate(), "yyyy-MM-dd")));
//            // 96点最终上报
//            List<LoadCityFcDO> reportLoadFc = loadCityFcService.findReportLoadFc(cityId, caliberId, firstDay, lastDay);
//            Map<String, List<LoadCityFcDO>> fcCollect = reportLoadFc.stream()
//                .collect(
//                    Collectors.groupingBy(t -> DateUtils.getDateToStrFORMAT(t.getDate(), "yyyy-MM-dd")));
//            // 实际负荷
//            List<LoadFeatureCityMonthHisDO> loadFeatureHisList = loadFeatureCityMonthHisService.getLoadFeatureCityMonthHisDOS(
//                cityId, dateToStrFORMAT, dateToStrFORMAT1, caliberId);
//            // 上报
//            List<LoadFeatureCityMonthFcDO> loadFeatureFcList = loadFeatureCityMonthFcService.findLoadFeatureMonthList(
//                    monthList, sYear, cityId, caliberId, null).stream()
//                .filter(t -> t.getReport() != null && Boolean.TRUE.equals(t.getReport()))
//                .sorted(Comparator.comparing(LoadFeatureCityMonthFcDO::getMonth)).collect(Collectors.toList());
//            List<Date> listBetweenDay = DateUtil.getListBetweenDay(firstDay, lastDay);
//            LoadDaysAccuracyDTO loadDaysAccuracyDTO = new LoadDaysAccuracyDTO();
//            loadDaysAccuracyDTO.setYear(sYear);
//            List<BigDecimal> mList = new ArrayList();
//            for (Date date : listBetweenDay) {
//                String dateToStrFORMAT2 = DateUtils.getDateToStrFORMAT(date, "yyyy-MM-dd");
//                LoadCityHisDO loadCityHisDO = new LoadCityHisDO();
//                LoadCityFcDO loadCityFcDO = new LoadCityFcDO();
//                if (MapUtils.isNotEmpty(hisCollect)) {
//                    List<LoadCityHisDO> loadHisDOS = hisCollect.get(dateToStrFORMAT2);
//                    if (CollectionUtils.isNotEmpty(loadHisDOS)) {
//                        loadCityHisDO = loadHisDOS.get(0);
//                    }
//                }
//                if (MapUtils.isNotEmpty(fcCollect)) {
//                    List<LoadCityFcDO> loadFcDOS = fcCollect.get(dateToStrFORMAT2);
//                    if (CollectionUtils.isNotEmpty(loadFcDOS)) {
//                        loadCityFcDO = loadFcDOS.get(0);
//                    }
//                }
//                // 日均准确率
//                if (loadCityHisDO.getCityId() != null && loadCityFcDO.getCityId() != null) {
//                    BigDecimal dayAccuracy = LoadCalUtil.getDayAccuracy(loadCityHisDO, loadCityFcDO,
//                        Constants.LOAD_CURVE_POINT_NUM);
//                    mList.add(dayAccuracy);
//                }
//            }
//            if (year.equals(year1)) {
//                int sizeHis = loadFeatureHisList.size();
//                loadFeatureFcList = loadFeatureFcList.stream().limit(sizeHis).collect(Collectors.toList());
//            }
//            // 最大负荷准确率
//            BigDecimal maxLoad = loadFeatureHisList.stream()
//                .max(Comparator.comparing(LoadFeatureCityMonthHisDO::getMaxLoad)).get().getMaxLoad();
//            BigDecimal maxLoad1 = loadFeatureFcList.stream()
//                .max(Comparator.comparing(LoadFeatureCityMonthFcDO::getMaxLoad)).get().getMaxLoad();
//            BigDecimal bigDecimal1 = LoadCalUtil.getAccuracy(maxLoad,
//                maxLoad1);
//            // 最小负荷准确率
//            BigDecimal minLoad = loadFeatureHisList.stream()
//                .max(Comparator.comparing(LoadFeatureCityMonthHisDO::getMinLoad)).get().getMinLoad();
//            BigDecimal minLoad1 = loadFeatureFcList.stream()
//                .max(Comparator.comparing(LoadFeatureCityMonthFcDO::getMinLoad)).get().getMinLoad();
//            BigDecimal minBigDecimal1 = LoadCalUtil.getAccuracy(minLoad,
//                minLoad1);
//            // 96点准确率
//            BigDecimal bigDecimal = null;
//            if (CollectionUtils.isNotEmpty(mList)) {
//                bigDecimal = BigDecimalUtils.listAvg(mList);
//            }
//            loadDaysAccuracyDTO.setDayAccuracy(bigDecimal);
//            loadDaysAccuracyDTO.setMaxLoadAccuracy(bigDecimal1);
//            loadDaysAccuracyDTO.setMinLoadAccuracy(minBigDecimal1);
//            List<BigDecimal> bigDecimalSum = new ArrayList<>();
//            if (bigDecimal != null) {
//                bigDecimalSum.add(bigDecimal);
//            }
//            if (bigDecimal1 != null) {
//                bigDecimalSum.add(bigDecimal1);
//            }
//            if (minBigDecimal1 != null) {
//                bigDecimalSum.add(minBigDecimal1);
//            }
//            if (CollectionUtils.isNotEmpty(bigDecimalSum)) {
//                loadDaysAccuracyDTO.setAvgAccuracy(BigDecimalUtils.listAvg(bigDecimalSum));
//            }
//            result.add(loadDaysAccuracyDTO);
//        }
        return result;
    }

    @Override
    public List<LoadLongTermAccuracyDTO> findLongMonthAccuracy(String cityId, String caliberId, Date startDate,
        Date endDate) throws Exception {
        List<LoadLongTermAccuracyDTO> result = new ArrayList<>();
        List<String> monthList = DateUtil.getMonthBetween(DateUtils.getDateToStrFORMAT(startDate, "yyyy-MM"),
            DateUtils.getDateToStrFORMAT(endDate, "yyyy-MM"));
        List<String> months = new ArrayList<>();
        for (String month : monthList) {
            String sYear = month.split("-")[0];
            String sMonth = month.split("-")[1];
            // 上报预测
            List<LoadFeatureCityTenDaysFcServiceDO> fcCollect = loadFeatureCityTenDaysFcService.getMonthReportVO(
                    cityId, caliberId, sYear, sMonth).stream().
                filter(t -> t.getReport() != null && Boolean.TRUE.equals(t.getReport()))
                .collect(Collectors.toList());
            // 实际负荷
            List<LoadFeatureCityTenDaysHisServiceDO> hisCollect = loadFeatureCityTenDaysHisService.findLoadFeatureCityTenDaysHisServiceDOs(
                    cityId, caliberId, sYear, sMonth, null);
            // 最大/小负荷准确率
            BigDecimal maxFcLoad = null;
            BigDecimal minFcLoad = null;
            BigDecimal energyFcReduce = null;
            if (CollectionUtils.isNotEmpty(fcCollect)) {
                maxFcLoad = fcCollect.stream()
                    .max(Comparator.comparing(LoadFeatureCityTenDaysFcServiceDO::getMaxLoad)).get().getMaxLoad();
                minFcLoad = fcCollect.stream()
                    .min(Comparator.comparing(LoadFeatureCityTenDaysFcServiceDO::getMinLoad)).get().getMinLoad();
                energyFcReduce = fcCollect.stream().filter(t -> t.getEnergy() != null)
                    .map(LoadFeatureCityTenDaysFcServiceDO::getEnergy)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            }
            BigDecimal maxHisLoad = null;
            BigDecimal minHisLoad = null;
            BigDecimal energyHisReduce = null;
            if (CollectionUtils.isNotEmpty(hisCollect)) {
                maxHisLoad = hisCollect.stream()
                    .max(Comparator.comparing(LoadFeatureCityTenDaysHisServiceDO::getMaxLoad)).get().getMaxLoad();
                minHisLoad = hisCollect.stream()
                    .min(Comparator.comparing(LoadFeatureCityTenDaysHisServiceDO::getMinLoad)).get().getMinLoad();
                energyHisReduce = hisCollect.stream().filter(t -> t.getEnergy() != null)
                    .map(LoadFeatureCityTenDaysHisServiceDO::getEnergy)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            }
            BigDecimal maxBigDecimal = LoadCalUtil.getAccuracy(maxHisLoad, maxFcLoad);
            BigDecimal minBigDecimal = LoadCalUtil.getAccuracy(minHisLoad, minFcLoad);
            BigDecimal energyBigDecimal = LoadCalUtil.getAccuracy(energyHisReduce, energyFcReduce);

            LoadLongTermAccuracyDTO loadLongTermAccuracyDTO = new LoadLongTermAccuracyDTO();
            loadLongTermAccuracyDTO.setDate(month);
            loadLongTermAccuracyDTO.setMaxLoadAccuracy(maxBigDecimal);
            loadLongTermAccuracyDTO.setMinLoadAccuracy(minBigDecimal);
            loadLongTermAccuracyDTO.setEnergyAccuracy(energyBigDecimal);
            List<BigDecimal> bigDecimalSum = new ArrayList<>();
            if (energyBigDecimal != null) {
                bigDecimalSum.add(energyBigDecimal);
            }
            if (maxBigDecimal != null) {
                bigDecimalSum.add(maxBigDecimal);
            }
            if (minBigDecimal != null) {
                bigDecimalSum.add(minBigDecimal);
            }
            if (CollectionUtils.isNotEmpty(bigDecimalSum)) {
                loadLongTermAccuracyDTO.setAvgAccuracy(BigDecimalUtils.listAvg(bigDecimalSum));
            }
            result.add(loadLongTermAccuracyDTO);
        }
        return result;
    }

    @Override
    public SeasonDTO findSeasonAccuracy(String cityId, String caliberId, String year, String season)
        throws Exception {
        int nextYear = Integer.valueOf(year) + 1;
        SeasonDTO seasonDTO = new SeasonDTO();
        List<LoadLongTermAccuracyDTO> result = new ArrayList<>();
        List<String> months = new ArrayList<>();
        List<LoadFeatureCitySeasonFcDO> seasonFcDOList = new ArrayList<>();
        List<LoadFeatureCityMonthHisDO> seasonHisDOList = new ArrayList<>();
        if ("1".equals(season)) {
            months = QuarterUtil.getMonthsBySeason(season);
            // 重新预测
            seasonFcDOList = loadFeatureCityMonthFcService.getSummerSeasonReportFeature(year, cityId, caliberId).stream().collect(Collectors.toList());
            // 夏季月实际
            seasonHisDOList = loadFeatureCityMonthHisService.getSummerMonthsFeature(year, cityId, caliberId);
        } else if ("2".equals(season)) {
            months = QuarterUtil.getMonthsBySeason(null);
            // 重新预测
            seasonFcDOList = loadFeatureCityMonthFcService
                .getWinterSeasonReportFeatures(year, cityId, caliberId).stream().collect(Collectors.toList());
            // 冬季月实际
            seasonHisDOList = loadFeatureCityMonthHisService.getWinterMonthsFeature(String.valueOf(nextYear), cityId, caliberId);
        }
        for (String month : months) {
            String finalMonth = "";
            if ("11".equals(month) || "12".equals(month)) {
                finalMonth = year + "-" + month;
            } else if ("01".equals(month) || "02".equals(month)) {
                finalMonth = Integer.valueOf(year) + 1 + "-" + month;
            } else {
                finalMonth = year + "-" + month;
            }
            Map<String, List<LoadFeatureCitySeasonFcDO>> fcCollect = seasonFcDOList.stream()
                .collect(Collectors.groupingBy(LoadFeatureCitySeasonFcDO::getMonth));
            Map<String, List<LoadFeatureCityMonthHisDO>> hisCollect = seasonHisDOList.stream()
                .collect(Collectors.groupingBy(LoadFeatureCityMonthHisDO::getMonth));
            LoadFeatureCitySeasonFcDO loadFeatureFc = new LoadFeatureCitySeasonFcDO();
            LoadFeatureCityMonthHisDO loadFeatureHis = new LoadFeatureCityMonthHisDO();
            if (MapUtils.isNotEmpty(fcCollect)) {
                List<LoadFeatureCitySeasonFcDO> loadFeatureCityMonthFcDOS = fcCollect.get(month);
                if (CollectionUtils.isNotEmpty(loadFeatureCityMonthFcDOS)) {
                    loadFeatureFc = loadFeatureCityMonthFcDOS.get(0);
                }
            }
            if (MapUtils.isNotEmpty(hisCollect)) {
                List<LoadFeatureCityMonthHisDO> loadFeatureCityMonthHisDOS = hisCollect.get(month);
                if (CollectionUtils.isNotEmpty(loadFeatureCityMonthHisDOS)) {
                    loadFeatureHis = loadFeatureCityMonthHisDOS.get(0);
                }
            }
            BigDecimal maxBigDecimal = LoadCalUtil.getAccuracy(loadFeatureHis.getMaxLoad(), loadFeatureFc.getMaxLoad());
            BigDecimal minBigDecimal = LoadCalUtil.getAccuracy(loadFeatureHis.getMinLoad(), loadFeatureFc.getMinLoad());
            BigDecimal energyBigDecimal = LoadCalUtil.getAccuracy(loadFeatureHis.getEnergy(), loadFeatureFc.getEnergy());
            LoadLongTermAccuracyDTO loadLongTermAccuracyDTO = new LoadLongTermAccuracyDTO();
            loadLongTermAccuracyDTO.setDate(finalMonth);
            loadLongTermAccuracyDTO.setMaxLoadAccuracy(maxBigDecimal);
            loadLongTermAccuracyDTO.setMinLoadAccuracy(minBigDecimal);
            loadLongTermAccuracyDTO.setEnergyAccuracy(energyBigDecimal);
            List<BigDecimal> bigDecimalSum = new ArrayList<>();
            if (energyBigDecimal != null) {
                bigDecimalSum.add(energyBigDecimal);
            }
            if (maxBigDecimal != null) {
                bigDecimalSum.add(maxBigDecimal);
            }
            if (minBigDecimal != null) {
                bigDecimalSum.add(minBigDecimal);
            }
            if (CollectionUtils.isNotEmpty(bigDecimalSum)) {
                loadLongTermAccuracyDTO.setAvgAccuracy(BigDecimalUtils.listAvg(bigDecimalSum));
            }
            result.add(loadLongTermAccuracyDTO);
        }
        // 求季度
        BigDecimal maxFcLoad = null;
        Optional<LoadFeatureCitySeasonFcDO> max = seasonFcDOList.stream()
            .max(Comparator.comparing(LoadFeatureCitySeasonFcDO::getMaxLoad));
        if (max.isPresent()) {
            maxFcLoad = max.get().getMaxLoad();
        }
        BigDecimal maxHisLoad = null;
        Optional<LoadFeatureCityMonthHisDO> max1 = seasonHisDOList.stream()
            .max(Comparator.comparing(LoadFeatureCityMonthHisDO::getMaxLoad));
        if (max1.isPresent()) {
            maxHisLoad = max1.get().getMaxLoad();
        }
        BigDecimal minFcLoad = null;
        Optional<LoadFeatureCitySeasonFcDO> max2 = seasonFcDOList.stream()
            .min(Comparator.comparing(LoadFeatureCitySeasonFcDO::getMinLoad));
        if (max2.isPresent()) {
            minFcLoad = max2.get().getMinLoad();
        }
        BigDecimal minHisLoad = null;
        Optional<LoadFeatureCityMonthHisDO> max3 = seasonHisDOList.stream()
            .min(Comparator.comparing(LoadFeatureCityMonthHisDO::getMinLoad));
        if (max3.isPresent()) {
            minHisLoad = max3.get().getMinLoad();
        }
        BigDecimal fcReduce = seasonFcDOList.stream().filter(t -> t.getEnergy() != null)
            .map(LoadFeatureCitySeasonFcDO::getEnergy).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal hisReduce = seasonHisDOList.stream().filter(t -> t.getEnergy() != null)
            .map(LoadFeatureCityMonthHisDO::getEnergy).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal maxBigDecimal = LoadCalUtil.getAccuracy(maxHisLoad, maxFcLoad);
        BigDecimal minBigDecimal = LoadCalUtil.getAccuracy(minHisLoad, minFcLoad);
        BigDecimal energyBigDecimal = LoadCalUtil.getAccuracy(hisReduce, fcReduce);
        seasonDTO.setLoadLongTermAccuracyDTOList(result);
        SeasonLongTermDTO seasonLongTermDTO = new SeasonLongTermDTO();
        seasonLongTermDTO.setSeasonMaxLoadAccuracy(maxBigDecimal);
        seasonLongTermDTO.setSeasonMinLoadAccuracy(minBigDecimal);
        seasonLongTermDTO.setSeasonEnergyAccuracy(energyBigDecimal);
        List<BigDecimal> bigDecimalSum = new ArrayList<>();
        if (energyBigDecimal != null) {
            bigDecimalSum.add(energyBigDecimal);
        }
        if (maxBigDecimal != null) {
            bigDecimalSum.add(maxBigDecimal);
        }
        if (minBigDecimal != null) {
            bigDecimalSum.add(minBigDecimal);
        }
        if (CollectionUtils.isNotEmpty(bigDecimalSum)) {
            seasonLongTermDTO.setSeasonAvgAccuracy(BigDecimalUtils.listAvg(bigDecimalSum));
        }
        seasonDTO.setSeasonLongTermDTO(seasonLongTermDTO);
        return seasonDTO;
    }

    @Override
    public SeasonDTO findLongYearAccuracy(String cityId, String caliberId, String year, String season)
        throws Exception {
        SeasonDTO seasonDTO = new SeasonDTO();
        List<LoadLongTermAccuracyDTO> result = new ArrayList<>();
        List<String> monthList = DateUtil.getMonthList();
        List<LoadFeatureCitySeasonFcDO> seasonFcDOList = loadFeatureCityMonthFcService.getYearReportFeatures(
            year, monthList, cityId, caliberId, null);
        List<LoadFeatureCityMonthHisDO> seasonHisDOList = loadFeatureCityMonthHisService.getLoadFeatureCityMonthHisDOList(
            cityId, year, monthList, caliberId);
        for (String month : monthList) {
            String finalMonth = year + "-" + month;
            Map<String, List<LoadFeatureCitySeasonFcDO>> fcCollect = seasonFcDOList.stream()
                .collect(Collectors.groupingBy(LoadFeatureCitySeasonFcDO::getMonth));
            Map<String, List<LoadFeatureCityMonthHisDO>> hisCollect = seasonHisDOList.stream()
                .collect(Collectors.groupingBy(LoadFeatureCityMonthHisDO::getMonth));
            LoadFeatureCitySeasonFcDO loadFeatureFc = new LoadFeatureCitySeasonFcDO();
            LoadFeatureCityMonthHisDO loadFeatureHis = new LoadFeatureCityMonthHisDO();
            if (MapUtils.isNotEmpty(fcCollect)) {
                List<LoadFeatureCitySeasonFcDO> loadFeatureCityMonthFcDOS = fcCollect.get(month);
                if (CollectionUtils.isNotEmpty(loadFeatureCityMonthFcDOS)) {
                    loadFeatureFc = loadFeatureCityMonthFcDOS.get(0);
                }
            }
            if (MapUtils.isNotEmpty(hisCollect)) {
                List<LoadFeatureCityMonthHisDO> loadFeatureCityMonthHisDOS = hisCollect.get(month);
                if (CollectionUtils.isNotEmpty(loadFeatureCityMonthHisDOS)) {
                    loadFeatureHis = loadFeatureCityMonthHisDOS.get(0);
                }
            }
            BigDecimal maxBigDecimal = LoadCalUtil.getAccuracy(loadFeatureHis.getMaxLoad(), loadFeatureFc.getMaxLoad());
            BigDecimal minBigDecimal = LoadCalUtil.getAccuracy(loadFeatureHis.getMinLoad(), loadFeatureFc.getMinLoad());
            BigDecimal energyBigDecimal = LoadCalUtil.getAccuracy(loadFeatureHis.getEnergy(), loadFeatureFc.getEnergy());
            LoadLongTermAccuracyDTO loadLongTermAccuracyDTO = new LoadLongTermAccuracyDTO();
            loadLongTermAccuracyDTO.setDate(finalMonth);
            loadLongTermAccuracyDTO.setMaxLoadAccuracy(maxBigDecimal);
            loadLongTermAccuracyDTO.setMinLoadAccuracy(minBigDecimal);
            loadLongTermAccuracyDTO.setEnergyAccuracy(energyBigDecimal);
            List<BigDecimal> bigDecimalSum = new ArrayList<>();
            if (energyBigDecimal != null && !new BigDecimal("0").equals(energyBigDecimal)) {
                bigDecimalSum.add(energyBigDecimal);
            }
            if (maxBigDecimal != null && !new BigDecimal("0").equals(maxBigDecimal)) {
                bigDecimalSum.add(maxBigDecimal);
            }
            if (minBigDecimal != null && !new BigDecimal("0").equals(minBigDecimal)) {
                bigDecimalSum.add(minBigDecimal);
            }
            if (CollectionUtils.isNotEmpty(bigDecimalSum)) {
                loadLongTermAccuracyDTO.setAvgAccuracy(BigDecimalUtils.listAvg(bigDecimalSum));
            }
            result.add(loadLongTermAccuracyDTO);
        }
        // 求季度
        BigDecimal maxFcLoad = null;
        Optional<LoadFeatureCitySeasonFcDO> max = seasonFcDOList.stream()
            .max(Comparator.comparing(LoadFeatureCitySeasonFcDO::getMaxLoad));
        if (max.isPresent()) {
            maxFcLoad = max.get().getMaxLoad();
        }
        BigDecimal maxHisLoad = null;
            Optional<LoadFeatureCityMonthHisDO> max1 = seasonHisDOList.stream()
            .max(Comparator.comparing(LoadFeatureCityMonthHisDO::getMaxLoad));
        if (max1.isPresent()) {
            maxHisLoad = max1.get().getMaxLoad();
        }
        BigDecimal minFcLoad = null;
            Optional<LoadFeatureCitySeasonFcDO> max2 = seasonFcDOList.stream()
            .max(Comparator.comparing(LoadFeatureCitySeasonFcDO::getMinLoad));
        if (max2.isPresent()) {
            minFcLoad = max2.get().getMinLoad();
        }
        BigDecimal minHisLoad = null;
            Optional<LoadFeatureCityMonthHisDO> max3 = seasonHisDOList.stream()
            .max(Comparator.comparing(LoadFeatureCityMonthHisDO::getMinLoad));
        if (max3.isPresent()) {
            minHisLoad = max3.get().getMinLoad();
        }
        BigDecimal fcReduce = seasonFcDOList.stream().filter(t -> t.getEnergy() != null)
            .map(LoadFeatureCitySeasonFcDO::getEnergy).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal hisReduce = seasonHisDOList.stream().filter(t -> t.getEnergy() != null)
            .map(LoadFeatureCityMonthHisDO::getEnergy).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal maxBigDecimal = LoadCalUtil.getAccuracy(maxHisLoad, maxFcLoad);
        BigDecimal minBigDecimal = LoadCalUtil.getAccuracy(minHisLoad, minFcLoad);
        BigDecimal energyBigDecimal = LoadCalUtil.getAccuracy(hisReduce, fcReduce);
        seasonDTO.setLoadLongTermAccuracyDTOList(result);
        SeasonLongTermDTO seasonLongTermDTO = new SeasonLongTermDTO();
        seasonLongTermDTO.setSeasonMaxLoadAccuracy(maxBigDecimal);
        seasonLongTermDTO.setSeasonMinLoadAccuracy(minBigDecimal);
        seasonLongTermDTO.setSeasonEnergyAccuracy(energyBigDecimal);
        List<BigDecimal> bigDecimalSum = new ArrayList<>();
        if (energyBigDecimal != null && !new BigDecimal("0").equals(energyBigDecimal)) {
            bigDecimalSum.add(energyBigDecimal);
        }
        if (maxBigDecimal != null && !new BigDecimal("0").equals(maxBigDecimal)) {
            bigDecimalSum.add(maxBigDecimal);
        }
        if (minBigDecimal != null && !new BigDecimal("0").equals(minBigDecimal)) {
            bigDecimalSum.add(minBigDecimal);
        }
        if (CollectionUtils.isNotEmpty(bigDecimalSum)) {
            seasonLongTermDTO.setSeasonAvgAccuracy(BigDecimalUtils.listAvg(bigDecimalSum));
        }
        seasonDTO.setSeasonLongTermDTO(seasonLongTermDTO);
        return seasonDTO;
    }

    @Override
    public List<DayAssessDTO> findDayAssess(Date start, Date end, String caliberId, String batchId) throws Exception {
        List<DayAssessDTO> result;
        List<CityDO> allCity = cityService.findAllCitys();
        List<CityDO> cityDOs = allCity.stream().filter(t -> !CityConstants.PROVINCE_ID.equals(t.getId())).collect(Collectors.toList());
        List<String> cityIds = cityDOs.stream().map(CityDO::getId).collect(Collectors.toList());
        Map<String, List<AccuracyAssessDO>> cityAssessMap = accuracyAssessService.findAccuracyListByCondition(cityIds, caliberId, CITY_EVALUATION_ALGORITHM_ID, start, end)
                .stream().collect(Collectors.groupingBy(AccuracyAssessDO::getCityId));
        //多批次过滤数据
        for (Entry<String, List<AccuracyAssessDO>> cityAssessEntry : cityAssessMap.entrySet()) {
            List<AccuracyAssessDO> filterAssessByBatchId = batchDataFilterService.filterAssessByBatchId(cityAssessEntry.getValue(), cityAssessEntry.getKey(), batchId, 1);
            cityAssessEntry.setValue(filterAssessByBatchId);
        }
        result = cityDOs.stream().map(
                        cityDO -> {
                            List<AccuracyCompositeDO> accuracyCompositeDOS = accuracyCompositeDAO.selectListByAlgorithmId(cityDO.getId(), caliberId, CITY_EVALUATION_ALGORITHM_ID, start, end, null);
                            accuracyCompositeDOS = batchDataFilterService.filterCompositeByBatchId(accuracyCompositeDOS, cityDO.getId(), batchId, 1);
                            return this.buildCityDayAssessDTO(cityDO, cityAssessMap.get(cityDO.getId()), accuracyCompositeDOS);
                        })
                .collect(Collectors.toList());
        result.sort(Comparator.comparing(DayAssessDTO::getDailyOverallAccuracy, Comparator.nullsLast(Comparator.reverseOrder())));
        return result;
    }

    private DayAssessDTO buildCityDayAssessDTO(CityDO city, List<AccuracyAssessDO> accuracyAssessDOS,
                                               List<AccuracyCompositeDO> accuracyCompositeDOS) {
        DayAssessDTO dayAssessDTO = new DayAssessDTO();
        dayAssessDTO.setCityName(city.getCity());
        if (CollectionUtils.isEmpty(accuracyAssessDOS)) {
            return dayAssessDTO;
        }
        //根据AssessName分组后求平均值
        Map<String, BigDecimal> assessAccuracyMap = accuracyAssessDOS.stream()
                .collect(Collectors.groupingBy(
                        AccuracyAssessDO::getAssessName,
                        Collectors.mapping(AccuracyAssessDO::getAccuracy, Collectors.toList())))
                .entrySet()
                .stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> BigDecimalUtils.listAvg(entry.getValue())
                ));

        for (Map.Entry<String, BigDecimal> entry : assessAccuracyMap.entrySet()) {
            if ("日最大负荷".equals(entry.getKey())) {
                dayAssessDTO.setDailyMaxAccuracy(entry.getValue());
            }
            if (entry.getKey().contains("日保供")) {
                dayAssessDTO.setDailySupplyAccuracy(entry.getValue());
            }
            if (entry.getKey().contains("午间低谷")) {
                dayAssessDTO.setMiddayMinLoadAccuracy(entry.getValue());
            }
            if (entry.getKey().contains("夜间低谷")) {
                dayAssessDTO.setNighttimeMinLoadAccuracy(entry.getValue());
            }
        }
        //计算综合准确率的平均值
        if (CollectionUtils.isEmpty(accuracyCompositeDOS)) {
            return dayAssessDTO;
        }
        List<BigDecimal> accuracyValues = accuracyCompositeDOS.stream()
                .map(AccuracyCompositeDO::getAccuracy)
                .collect(Collectors.toList());
        dayAssessDTO.setDailyOverallAccuracy(BigDecimalUtils.listAvg(accuracyValues));
        return dayAssessDTO;
    }

    @Override
    public List<LoadAssessDTO> findMonthAssess(Date startDate, String caliberId) throws Exception {
        List<LoadAssessDTO> result = new ArrayList<>();
        List<String> mMonth = new ArrayList<>();
        String sYear = DateUtils.getDateToStrFORMAT(startDate, "yyyy-MM-dd").split("-")[0];
        String sMonth = DateUtils.getDateToStrFORMAT(startDate, "yyyy-MM-dd").split("-")[1];
        mMonth.add(sMonth);
        Date start = DateUtils.getFirstDayDateOfMonth(startDate);
        Date end = DateUtils.getLastDayOfMonth(startDate);
        List<Date> listBetweenDay = DateUtils.getListBetweenDay(start, end);
        List<CityDO> allCity = cityService.findAllCitys();
        Map<String, List<CityDO>> collectCity = allCity.stream().filter(t -> !"1".equals(t.getId())).collect(
                Collectors.groupingBy(t -> t.getId()));
        List<String> collect = allCity.stream().filter(t -> !"1".equals(t.getId())).map(CityDO::getId)
                .collect(Collectors.toList());
        // 96点实际负荷
        List<LoadCityHisDO> loadCityHisDOS = loadCityHisService.getLoadCityHisDOSByCityIds(collect, start, end, caliberId);
        Map<String, List<LoadCityHisDO>> hisCollect = loadCityHisDOS.stream()
                .collect(
                        Collectors.groupingBy(t -> t.getCityId()));
        // 96点最终上报
        List<LoadCityFcDO> reportLoadFc = loadCityFcService.findLoadCityReportFc(collect, start, end, caliberId);
        Map<String, List<LoadCityFcDO>> fcCollect = reportLoadFc.stream()
                .collect(
                        Collectors.groupingBy(t -> t.getCityId()));
        // 实际负荷
        /*List<LoadFeatureCityMonthHisDO> loadFeatureHisList = loadFeatureCityMonthHisService.findMonthLoadFeatureList(
            collect, sYear, sMonth, caliberId);*/
        Map<String, List<LoadFeatureCityTenDaysHisServiceDO>> collect1 = loadFeatureCityTenDaysHisService.findLoadFeatureCityTenDaysHisServiceDOs(
                collect, caliberId, sYear, sMonth).stream().collect(
                Collectors.groupingBy(t -> t.getCityId()));

        Map<String, List<LoadFeatureCityDayHisDO>> collectDayHis = loadFeatureCityDayHisService.findLoadFeatureCityDayHisVOS(
                        collect, start, end, caliberId).stream()
                .filter(t -> !"1".equals(t.getId()))
                .collect(
                        Collectors.groupingBy(t -> t.getCityId()));
        // 上报
        List<LoadFeatureCityMonthFcDO> loadFeatureFcList = loadFeatureCityMonthFcService.getMonthReportList(
                        sYear, mMonth, collect, caliberId).stream()
                .filter(t -> !"1".equals(t.getId())).collect(Collectors.toList());
        Map<String, List<LoadFeatureCityTenDaysFcServiceDO>> collect2 = loadFeatureCityTenDaysFcService.getMonthReportVO(
                        collect, caliberId, sYear, sMonth).stream()
                .filter(t -> !"1".equals(t.getId())).collect(Collectors.groupingBy(t -> t.getCityId()));

        Map<String, List<LoadFeatureCityDayFcDO>> collectDayFc = new HashMap<>();
        List<LoadFeatureCityDayFcDO> reportList = loadFeatureCityDayFcService.findReportList(collect,
                caliberId, start, end);
        if (CollectionUtils.isNotEmpty(reportList)) {
            collectDayFc = reportList.stream()
                    .filter(t -> !"1".equals(t.getId()))
                    .collect(
                            Collectors.groupingBy(t -> t.getCityId()));
        }
        for (String cityId : collect) {
            if (cityId.equals("16") || cityId.equals("17")) {
                continue;
            }
            LoadAssessDTO loadAssessDTO = new LoadAssessDTO();
            List<BigDecimal> mList = new ArrayList<>();
            for (Date date : listBetweenDay) {
                LoadCityHisDO loadCityHisDO = new LoadCityHisDO();
                LoadCityFcDO loadCityFcDO = new LoadCityFcDO();
                if (MapUtils.isNotEmpty(hisCollect)) {
                    List<LoadCityHisDO> loadCityHis = hisCollect.get(cityId);
                    if (CollectionUtils.isNotEmpty(loadCityHis)) {
                        for (LoadCityHisDO loadCityDO : loadCityHis) {
                            if (date.equals(loadCityDO.getDate())) {
                                loadCityHisDO = loadCityDO;
                            }
                        }
                    }
                }
                if (MapUtils.isNotEmpty(hisCollect)) {
                    List<LoadCityFcDO> loadCityFc = fcCollect.get(cityId);
                    if (CollectionUtils.isNotEmpty(loadCityFc)) {
                        for (LoadCityFcDO loadCityDO : loadCityFc) {
                            if (date.equals(loadCityDO.getDate())) {
                                loadCityFcDO = loadCityDO;
                            }
                        }
                    }
                }
                // 日均准确率
                if (loadCityHisDO.getCityId() != null && loadCityFcDO.getCityId() != null) {
                    BigDecimal dayAccuracy = LoadCalUtil.getDayAccuracy(loadCityHisDO, loadCityFcDO,
                            Constants.LOAD_CURVE_POINT_NUM);
                    mList.add(dayAccuracy);
                }
            }
            // 96点准确率
            BigDecimal bigDecimal = null;
            if (CollectionUtils.isNotEmpty(mList)) {
                bigDecimal = BigDecimalUtils.listAvg(mList);
            }
            // 月最大负荷、电量准确率
            BigDecimal maxHisLoad = null;
            BigDecimal energyHis = null;
            BigDecimal maxFcLoad = null;
            BigDecimal energyFc = null;
            if (MapUtils.isNotEmpty(collect1)) {
                for (Entry<String, List<LoadFeatureCityTenDaysHisServiceDO>> stringListEntry : collect1.entrySet()) {
                    if (cityId.equals(stringListEntry.getKey())) {
                        maxHisLoad = stringListEntry.getValue().stream()
                                .max(Comparator.comparing(LoadFeatureCityTenDaysHisServiceDO::getMaxLoad)).get()
                                .getMaxLoad();
                        energyHis = stringListEntry.getValue().stream().filter(t -> t.getEnergy() != null)
                                .map(LoadFeatureCityTenDaysHisServiceDO::getEnergy)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                    }
                }
            }
            /*if (CollectionUtils.isNotEmpty(loadFeatureHisList)) {
                for (LoadFeatureCityMonthHisDO loadFeatureCityMonthHisDO : loadFeatureHisList) {
                    if (cityId.equals(loadFeatureCityMonthHisDO.getCityId())) {
                        maxHisLoad = loadFeatureCityMonthHisDO.getMaxLoad();
                        energyHis = loadFeatureCityMonthHisDO.getEnergy();
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(loadFeatureFcList)) {
                for (LoadFeatureCityMonthFcDO loadFeatureCityMonthFcDO : loadFeatureFcList) {
                    if (cityId.equals(loadFeatureCityMonthFcDO.getCityId())) {
                        maxFcLoad = loadFeatureCityMonthFcDO.getMaxLoad();
                        energyFc = loadFeatureCityMonthFcDO.getEnergy();
                    }
                }
            }*/
            if (MapUtils.isNotEmpty(collect2)) {
                for (Entry<String, List<LoadFeatureCityTenDaysFcServiceDO>> stringListEntry : collect2.entrySet()) {
                    if (cityId.equals(stringListEntry.getKey())) {
                        maxFcLoad = stringListEntry.getValue().stream()
                                .max(Comparator.comparing(LoadFeatureCityTenDaysFcServiceDO::getMaxLoad)).get()
                                .getMaxLoad();
                        energyFc = stringListEntry.getValue().stream().filter(t -> t.getEnergy() != null)
                                .map(LoadFeatureCityTenDaysFcServiceDO::getEnergy)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                    }
                }
            }
            // 日最大负荷最小负荷准确率
            BigDecimal maxDayHisLoad = null;
            BigDecimal minDayHisLoad = null;
            BigDecimal maxDayFcLoad = null;
            BigDecimal minDayFcLoad = null;
            if (MapUtils.isNotEmpty(collectDayHis)) {
                List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisDOS = collectDayHis.get(cityId);
                if (CollectionUtils.isNotEmpty(loadFeatureCityDayHisDOS)) {
                    Optional<LoadFeatureCityDayHisDO> max = loadFeatureCityDayHisDOS.stream()
                            .max(Comparator.comparing(LoadFeatureCityDayHisDO::getMaxLoad));
                    if (max.isPresent()) {
                        maxDayHisLoad = max.get().getMaxLoad();
                    }
                    Optional<LoadFeatureCityDayHisDO> min = loadFeatureCityDayHisDOS.stream()
                            .min(Comparator.comparing(LoadFeatureCityDayHisDO::getMinLoad));
                    if (min.isPresent()) {
                        minDayHisLoad = min.get().getMinLoad();
                    }
                }
            }
            if (MapUtils.isNotEmpty(collectDayFc)) {
                List<LoadFeatureCityDayFcDO> loadFeatureCityDayFcDOS = collectDayFc.get(cityId);
                if (CollectionUtils.isNotEmpty(loadFeatureCityDayFcDOS)) {
                    Optional<LoadFeatureCityDayFcDO> max = loadFeatureCityDayFcDOS.stream()
                            .max(Comparator.comparing(LoadFeatureCityDayFcDO::getMaxLoad));
                    if (max.isPresent()) {
                        maxDayFcLoad = max.get().getMaxLoad();
                    }
                    Optional<LoadFeatureCityDayFcDO> min = loadFeatureCityDayFcDOS.stream()
                            .min(Comparator.comparing(LoadFeatureCityDayFcDO::getMinLoad));
                    if (min.isPresent()) {
                        minDayFcLoad = min.get().getMinLoad();
                    }
                }
            }
            BigDecimal accuracy = LoadCalUtil.getAccuracy(maxHisLoad, maxFcLoad);
            BigDecimal accuracy1 = LoadCalUtil.getAccuracy(energyHis, energyFc);
            BigDecimal accuracy2 = LoadCalUtil.getAccuracy(maxDayHisLoad, maxDayFcLoad);
            BigDecimal accuracy3 = LoadCalUtil.getAccuracy(minDayHisLoad, minDayFcLoad);
            loadAssessDTO.setCityName(collectCity.get(cityId).get(0).getCity());
            loadAssessDTO.setDayAccuracy(bigDecimal);
            loadAssessDTO.setMonthMaxLoadAccuracy(accuracy);
            loadAssessDTO.setMonthEnergyAccuracy(accuracy1);
            loadAssessDTO.setMaxLoadAccuracy(accuracy2);
            loadAssessDTO.setMinLoadAccuracy(accuracy3);
            loadAssessDTO.setWeightAverageAccuracy(getWeightAverageAccuracy(bigDecimal, accuracy2, accuracy3, accuracy, accuracy1));
            result.add(loadAssessDTO);
        }
        List<LoadAssessDTO> collectResult = result.stream()
                .sorted(Comparator.comparing(LoadAssessDTO::getWeightAverageAccuracy).reversed())
                .collect(Collectors.toList());
        return collectResult;
    }

    @Override
    public List<LoadAssessDTO> findYearAssess(Date startDate, Date endDate, String caliberId) throws Exception {
        List<LoadAssessDTO> result = new ArrayList<>();
        List<String> mMonth = new ArrayList<>();
        String sYear = DateUtils.getDateToStrFORMAT(startDate, "yyyy-MM-dd").split("-")[0];
        String sMonth = DateUtils.getDateToStrFORMAT(startDate, "yyyy-MM-dd").split("-")[1];
        String endMonth = DateUtils.getDateToStrFORMAT(endDate, "yyyy-MM-dd").split("-")[1];
        Date start = DateUtils.getFirstDayDateOfMonth(startDate);
        Date end = DateUtils.getFirstDayDateOfMonth(endDate);
        List<String> monthList = DateUtil.getMonthList();
        mMonth.add(sMonth);
        List<Date> listBetweenDay = DateUtils.getListBetweenDay(start, end);
        List<CityDO> allCity = cityService.findAllCitys();
        Map<String, List<CityDO>> collectCity = allCity.stream().filter(t -> !"1".equals(t.getId())).collect(
            Collectors.groupingBy(t -> t.getId()));
        List<String> collect = allCity.stream().filter(t -> !"1".equals(t.getId())).map(CityDO::getId)
            .collect(Collectors.toList());
        // 96点实际负荷
        /*List<LoadCityHisDO> loadCityHisDOS = loadCityHisService.getLoadCityHisDOSByCityIds(collect, start, end, caliberId);
        Map<String, List<LoadCityHisDO>> hisCollect = loadCityHisDOS.stream()
            .collect(
                Collectors.groupingBy(t -> t.getCityId()));*/
        // 直接获取准确率
        List<LoadCityMonthAccuracyReportDO> loadMonthReportAccuracy = loadCityFcService.findLoadMonthReportAccuracy(
            collect, sYear, sMonth, endMonth);
        Map<String, List<LoadCityMonthAccuracyReportDO>> collect1 = loadMonthReportAccuracy.stream()
            .collect(
                Collectors.groupingBy(t -> t.getCityId()));

        // 96点最终上报
        /*List<LoadCityFcDO> reportLoadFc = loadCityFcService.findLoadCityReportFc(collect, start, end, caliberId);
        Map<String, List<LoadCityFcDO>> fcCollect = reportLoadFc.stream()
            .collect(
                Collectors.groupingBy(t -> t.getCityId()));*/
        // 实际负荷
        Map<String, List<LoadFeatureCityMonthHisDO>> loadFeatureHisList = loadFeatureCityMonthHisService.getLoadFeatureCityMonthHisDOList(
                collect, sYear, monthList, caliberId).stream()
            .collect(
                Collectors.groupingBy(t -> t.getCityId()));
        Map<String, List<LoadFeatureCityDayHisDO>> collectDayHis = loadFeatureCityDayHisService.findLoadFeatureCityDayHisVOS(
                collect, start, end, caliberId).stream()
            .filter(t -> !"1".equals(t.getId()))
            .collect(
                Collectors.groupingBy(t -> t.getCityId()));
        // 上报
        Map<String, List<LoadFeatureCityMonthFcDO>> loadFeatureFcList = loadFeatureCityMonthFcService.getMonthReportList(
                sYear, monthList, collect, caliberId).stream()
            .filter(t -> !"1".equals(t.getId())).collect(
                Collectors.groupingBy(t -> t.getCityId()));
        List<LoadFeatureCityDayFcDO> reportList = loadFeatureCityDayFcService.findReportList(collect,
            caliberId, start, end);
        Map<String, List<LoadFeatureCityDayFcDO>> collectDayFc = new HashMap<>();
        if (CollectionUtils.isNotEmpty(reportList)) {
            collectDayFc = reportList.stream()
                .filter(t -> !"1".equals(t.getId()) && t != null)
                .collect(
                    Collectors.groupingBy(t -> t.getCityId()));
        }
        for (String cityId : collect) {
            if (cityId.equals("16") || cityId.equals("17")) {
                continue;
            }
            LoadAssessDTO loadAssessDTO = new LoadAssessDTO();
            List<BigDecimal> mList = new ArrayList<>();
            for (Date date : listBetweenDay) {
                /*LoadCityHisDO loadCityHisDO = new LoadCityHisDO();
                LoadCityFcDO loadCityFcDO = new LoadCityFcDO();
                if (MapUtils.isNotEmpty(hisCollect)) {
                    List<LoadCityHisDO> loadCityHis = hisCollect.get(cityId);
                    if (CollectionUtils.isNotEmpty(loadCityHis)) {
                        for (LoadCityHisDO loadCityDO : loadCityHis) {
                            if (date.equals(loadCityDO.getDate())) {
                                loadCityHisDO = loadCityDO;
                            }
                        }
                    }
                }
                if (MapUtils.isNotEmpty(fcCollect)) {
                    List<LoadCityFcDO> loadCityFc = fcCollect.get(cityId);
                    if (CollectionUtils.isNotEmpty(loadCityFc)) {
                        for (LoadCityFcDO loadCityDO : loadCityFc) {
                            if (date.equals(loadCityDO.getDate())) {
                                loadCityFcDO = loadCityDO;
                            }
                        }
                    }
                }*/
                // 日均准确率
                /*if (loadCityHisDO.getCityId() != null && loadCityFcDO.getCityId() != null) {
                    BigDecimal dayAccuracy = LoadCalUtil.getDayAccuracy(loadCityHisDO, loadCityFcDO,
                        Constants.LOAD_CURVE_POINT_NUM);
                    mList.add(dayAccuracy);
                }*/
            }
            if (MapUtils.isNotEmpty(collect1)) {
                List<LoadCityMonthAccuracyReportDO> loadCityReportDOS = collect1.get(cityId);
                if (CollectionUtils.isNotEmpty(loadCityReportDOS)) {
                    for (LoadCityMonthAccuracyReportDO loadCityReportDO : loadCityReportDOS) {
                        mList.add(loadCityReportDO.getDayLoadAccuracy());
                    }
                }
            }
            // 96点准确率
            BigDecimal bigDecimal = null;
            if (CollectionUtils.isNotEmpty(mList)) {
                bigDecimal = BigDecimalUtils.listAvg(mList);
            }
            // 月最大负荷、电量准确率
            BigDecimal maxHisLoad = null;
            BigDecimal energyHis = null;
            BigDecimal maxFcLoad = null;
            BigDecimal energyFc = null;
            if (MapUtils.isNotEmpty(loadFeatureHisList)) {
                List<LoadFeatureCityMonthHisDO> loadFeatureCityMonthHisDOS = loadFeatureHisList.get(cityId);
                if (CollectionUtils.isNotEmpty(loadFeatureCityMonthHisDOS)) {
                    Optional<LoadFeatureCityMonthHisDO> max = loadFeatureCityMonthHisDOS.stream().filter(t -> t.getMaxLoad() != null)
                        .max(Comparator.comparing(LoadFeatureCityMonthHisDO::getMaxLoad));
                    if (max.isPresent()) {
                        maxHisLoad = max.get().getMaxLoad();
                    }
                    Optional<LoadFeatureCityMonthHisDO> min = loadFeatureCityMonthHisDOS.stream().filter(t -> t.getEnergy() != null)
                        .max(Comparator.comparing(LoadFeatureCityMonthHisDO::getEnergy));
                    if (min.isPresent()) {
                        energyHis = min.get().getEnergy();
                    }
                }
            }
            if (MapUtils.isNotEmpty(loadFeatureFcList)) {
                List<LoadFeatureCityMonthFcDO> loadFeatureCityMonthFcDOS = loadFeatureFcList.get(cityId);
                if (CollectionUtils.isNotEmpty(loadFeatureCityMonthFcDOS)) {
                    Optional<LoadFeatureCityMonthFcDO> max = loadFeatureCityMonthFcDOS.stream().filter(t -> t.getMaxLoad() != null)
                        .max(Comparator.comparing(LoadFeatureCityMonthFcDO::getMaxLoad));
                    if (max.isPresent()) {
                        maxFcLoad = max.get().getMaxLoad();
                    }
                    Optional<LoadFeatureCityMonthFcDO> min = loadFeatureCityMonthFcDOS.stream().filter(t -> t.getEnergy() != null)
                        .max(Comparator.comparing(LoadFeatureCityMonthFcDO::getEnergy));
                    if (min.isPresent()) {
                        energyFc = min.get().getEnergy();
                    }
                }
            }
            // 日最大负荷最小负荷准确率
            BigDecimal maxDayHisLoad = null;
            BigDecimal minDayHisLoad = null;
            BigDecimal maxDayFcLoad = null;
            BigDecimal minDayFcLoad = null;
            if (MapUtils.isNotEmpty(collectDayHis)) {
                List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisDOS = collectDayHis.get(cityId);
                if (CollectionUtils.isNotEmpty(loadFeatureCityDayHisDOS)) {
                    Optional<LoadFeatureCityDayHisDO> max = loadFeatureCityDayHisDOS.stream().filter(t -> t.getMaxLoad() != null)
                        .max(Comparator.comparing(LoadFeatureCityDayHisDO::getMaxLoad));
                    if (max.isPresent()) {
                        maxDayHisLoad = max.get().getMaxLoad();
                    }
                    Optional<LoadFeatureCityDayHisDO> min = loadFeatureCityDayHisDOS.stream().filter(t -> t.getMinLoad() != null)
                        .min(Comparator.comparing(LoadFeatureCityDayHisDO::getMinLoad));
                    if (min.isPresent()) {
                        minDayHisLoad = min.get().getMinLoad();
                    }
                }
            }
            if (MapUtils.isNotEmpty(collectDayFc)) {
                List<LoadFeatureCityDayFcDO> loadFeatureCityDayFcDOS = collectDayFc.get(cityId);
                if (CollectionUtils.isNotEmpty(loadFeatureCityDayFcDOS)) {
                    Optional<LoadFeatureCityDayFcDO> max = loadFeatureCityDayFcDOS.stream().filter(t -> t.getMaxLoad() != null)
                        .max(Comparator.comparing(LoadFeatureCityDayFcDO::getMaxLoad));
                    if (max.isPresent()) {
                        maxDayFcLoad = max.get().getMaxLoad();
                    }
                    Optional<LoadFeatureCityDayFcDO> min = loadFeatureCityDayFcDOS.stream().filter(t -> t.getMinLoad() != null)
                        .min(Comparator.comparing(LoadFeatureCityDayFcDO::getMinLoad));
                    if (min.isPresent()) {
                        minDayFcLoad = min.get().getMinLoad();
                    }
                }
            }
            BigDecimal accuracy = LoadCalUtil.getAccuracy(maxHisLoad, maxFcLoad);
            BigDecimal accuracy1 = LoadCalUtil.getAccuracy(energyHis, energyFc);
            BigDecimal accuracy2 = LoadCalUtil.getAccuracy(maxDayHisLoad, maxDayFcLoad);
            BigDecimal accuracy3 = LoadCalUtil.getAccuracy(minDayHisLoad, minDayFcLoad);
            loadAssessDTO.setCityName(collectCity.get(cityId).get(0).getCity());
            loadAssessDTO.setDayAccuracy(bigDecimal);
            loadAssessDTO.setMonthMaxLoadAccuracy(accuracy);
            loadAssessDTO.setMonthEnergyAccuracy(accuracy1);
            loadAssessDTO.setMaxLoadAccuracy(accuracy2);
            loadAssessDTO.setMinLoadAccuracy(accuracy3);
            loadAssessDTO.setWeightAverageAccuracy(getWeightAverageAccuracy(bigDecimal, accuracy2, accuracy3, accuracy, accuracy1));
            result.add(loadAssessDTO);
        }
        List<LoadAssessDTO> collectResult = result.stream()
            .sorted(Comparator.comparing(LoadAssessDTO::getWeightAverageAccuracy).reversed())
            .collect(Collectors.toList());
        return collectResult;
    }

    @Override
    public List<CityReportStateDTO> findCityReportState(Date startDate, String caliberId) throws Exception {
        List<CityReportStateDTO> result = new ArrayList<>();
        List<CityDO> allCity = cityService.findAllCitys();
        List<String> cityIdList = allCity.stream().map(CityDO::getId).filter(id -> !"1".equals(id))
            .collect(Collectors.toList());
        Map<String, List<CityDO>> collectCity = allCity.stream().filter(t -> !"1".equals(t.getId())).collect(
            Collectors.groupingBy(t -> t.getId()));
        List<ReportSystemInitDO> reportSystemConfig = reportSystemService.findReportSystemConfig((String) null, null,
            ReportTimeEnum.SHORT.getDescription());
        // 查询上报状态详情
        List<LoadCityDayReportServiceDO> loadCityDayReportDetail = loadCityFcService.findLoadCityDayReportDetail(
            cityIdList, startDate);
        Map<String, List<LoadCityDayReportServiceDO>> collect = loadCityDayReportDetail.stream()
            .collect(Collectors.groupingBy(t -> t.getCityId()));
        for (String cityId : cityIdList) {
            CityReportStateDTO cityReportStateDTO = new CityReportStateDTO();
            cityReportStateDTO.setCityName(collectCity.get(cityId).get(0).getCity());
            for (ReportSystemInitDO reportSystemInitDO : reportSystemConfig) {
                if (cityId.equals(reportSystemInitDO.getCityId())) {
                    String firstStr = reportSystemInitDO.getValue().split("-")[0];
                    String lastStr = reportSystemInitDO.getValue().split("-")[1];
                    cityReportStateDTO.setFirstReportEndTime(firstStr);
                    cityReportStateDTO.setLastReportEndTime(lastStr);
                }
            }
            if (CollectionUtils.isNotEmpty(loadCityDayReportDetail)) {
                for (LoadCityDayReportServiceDO loadCityDayDO : loadCityDayReportDetail) {
                    if (cityId.equals(loadCityDayDO.getCityId())) {
                        cityReportStateDTO.setFirstReportTime(
                            com.tsieframework.core.base.format.datetime.DateUtils.date2String(
                                (Date) loadCityDayDO.getFirstReportTime(), DateFormatType.DATE_FORMAT_STR));
                        cityReportStateDTO.setLastReportTime(
                            com.tsieframework.core.base.format.datetime.DateUtils.date2String(
                                (Date) loadCityDayDO.getLastReportTime(), DateFormatType.DATE_FORMAT_STR));
                        if (loadCityDayDO.getFirstReportTime() != null) {
                            cityReportStateDTO.setFirstReportStatus("已上报");
                        }
                        if (loadCityDayDO.getLastReportEndTime() != null) {
                            cityReportStateDTO.setLastReportStatus("已上报");
                        }
                        result.add(cityReportStateDTO);
                    }
                }
            } else {
                result.add(cityReportStateDTO);
            }
        }
        return result;
    }

    @Override
    public void doCityReportTimeConfig(String startDate, String endDate, List<String> firstCityIds,
        List<String> lastCityIds, String type1, String type2) throws Exception {
        if (startDate == null || endDate == null) {
            return;
        }
        List<String> hbTrue = new ArrayList<>();
        List<String> hbTrue1 = new ArrayList<>();
        List<String> hbFalse = new ArrayList<>();
        List<String> hbFalse1 = new ArrayList<>();
        List<String> cityTrue = new ArrayList<>();
        List<String> cityTrue1 = new ArrayList<>();
        List<String> cityFalse = new ArrayList<>();
        List<String> cityFalse1 = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(firstCityIds)) {
            cityFalse1.add("0");
            // 对该地市做限制
            if (firstCityIds.get(0).equals("all")) {
                cityTrue1 = null;
            } else {
                cityTrue1 = firstCityIds;
            }
        } else {
            cityTrue1.add("0");
        }
        if (type1 != null) {
            hbFalse1.add("0");
            hbTrue1.add("1");
        } else {
            hbFalse1.add("1");
            hbTrue1.add("0");
        }
        if (type2 != null) {
            hbFalse.add("0");
            hbTrue.add("1");
        } else {
            hbFalse.add("1");
            hbTrue.add("0");
        }
        if (CollectionUtils.isNotEmpty(lastCityIds)) {
            cityFalse.add("0");
            // 对该地市做限制
            if (lastCityIds.get(0).equals("all")) {
                cityTrue = null;
            } else {
                cityTrue = lastCityIds;
            }
        } else {
            cityTrue.add("0");
        }
        List<ReportSystemInitDO> reportHbTrue = reportSystemService.findReportSystemConfig(hbTrue, null,
            ReportTimeEnum.SHORT.getDescription());
        List<ReportSystemInitDO> reportHbTrue1 = reportSystemService.findReportSystemConfig(hbTrue1, null,
            ReportTimeEnum.SHORT.getDescription());
        List<ReportSystemInitDO> reportHbFalse = reportSystemService.findReportSystemConfig(hbFalse, null,
            ReportTimeEnum.SHORT.getDescription());
        List<ReportSystemInitDO> reportHbFalse1 = reportSystemService.findReportSystemConfig(hbFalse1, null,
            ReportTimeEnum.SHORT.getDescription());
        List<ReportSystemInitDO> reportCityTrue = reportSystemService.findReportSystemConfig(cityTrue, null,
            ReportTimeEnum.SHORT.getDescription());
        List<ReportSystemInitDO> reportCityTrue1 = reportSystemService.findReportSystemConfig(cityTrue1, null,
            ReportTimeEnum.SHORT.getDescription());
        List<ReportSystemInitDO> reportCityFalse = reportSystemService.findReportSystemConfig(cityFalse, null,
            ReportTimeEnum.SHORT.getDescription());
        List<ReportSystemInitDO> reportCityFalse1 = reportSystemService.findReportSystemConfig(cityFalse1, null,
            ReportTimeEnum.SHORT.getDescription());
        // 全网截止时间限制
        if (CollectionUtils.isNotEmpty(reportHbTrue)) {
            for (ReportSystemInitDO reportSystemInitDO : reportHbTrue) {
                reportSystemInitDO.setStatus(true);
                String firstTimeStr = reportSystemInitDO.getValue().split("-")[0];
                reportSystemInitDO.setValue(firstTimeStr + "-" + endDate);
                reportSystemService.doSaveOrUpdate(reportSystemInitDO);
            }
        }
        // 全网截止时间限制
        if (CollectionUtils.isNotEmpty(reportHbTrue1)) {
            for (ReportSystemInitDO reportSystemInitDO : reportHbTrue1) {
                reportSystemInitDO.setType(true);
                String firstTimeStr = reportSystemInitDO.getValue().split("-")[1];
                reportSystemInitDO.setValue(startDate + "-" + firstTimeStr);
                reportSystemService.doSaveOrUpdate(reportSystemInitDO);
            }
        }
        if (CollectionUtils.isNotEmpty(reportHbFalse1)) {
            for (ReportSystemInitDO reportSystemInitDO : reportHbFalse1) {
                reportSystemInitDO.setType(null);
                reportSystemService.doSaveOrUpdate(reportSystemInitDO);
            }
        }
        if (CollectionUtils.isNotEmpty(reportHbFalse)) {
            for (ReportSystemInitDO reportSystemInitDO : reportHbFalse) {
                reportSystemInitDO.setStatus(null);
                reportSystemService.doSaveOrUpdate(reportSystemInitDO);
            }
        }
        // 地市截止时间限制
        if (CollectionUtils.isNotEmpty(reportCityTrue1)) {
            for (ReportSystemInitDO reportSystemInitDO : reportCityTrue1) {
                if (Constants.PROVINCE_ID.equals(reportSystemInitDO.getCityId())) {
                    continue;
                }
                reportSystemInitDO.setType(true);
                String firstTimeStr = reportSystemInitDO.getValue().split("-")[1];
                reportSystemInitDO.setValue(startDate + "-" + firstTimeStr);
                reportSystemService.doSaveOrUpdate(reportSystemInitDO);
            }
        }
        // 地市截止时间限制
        if (CollectionUtils.isNotEmpty(reportCityTrue)) {
            for (ReportSystemInitDO reportSystemInitDO : reportCityTrue) {
                if (Constants.PROVINCE_ID.equals(reportSystemInitDO.getCityId())) {
                    continue;
                }
                reportSystemInitDO.setStatus(true);
                String firstTimeStr = reportSystemInitDO.getValue().split("-")[0];
                reportSystemInitDO.setValue(firstTimeStr + "-" + endDate);
                reportSystemService.doSaveOrUpdate(reportSystemInitDO);
            }
        }
        if (CollectionUtils.isNotEmpty(reportCityFalse1)) {
            for (ReportSystemInitDO reportSystemInitDO : reportCityFalse1) {
                if (Constants.PROVINCE_ID.equals(reportSystemInitDO.getCityId())) {
                    continue;
                }
                reportSystemInitDO.setType(null);
                reportSystemService.doSaveOrUpdate(reportSystemInitDO);
            }
        }
        if (CollectionUtils.isNotEmpty(reportCityFalse)) {
            for (ReportSystemInitDO reportSystemInitDO : reportCityFalse) {
                if (Constants.PROVINCE_ID.equals(reportSystemInitDO.getCityId())) {
                    continue;
                }
                reportSystemInitDO.setStatus(null);
                reportSystemService.doSaveOrUpdate(reportSystemInitDO);
            }
        }
    }

    @Override
    public CityReportResultDTO getCityReportTimeConfig(List<String> firstCityIds, List<String> lastCityIds)
        throws Exception {
        CityReportResultDTO cityReportResultDTO = new CityReportResultDTO();
        List<CityReportTimeDTO> firstReportTimeDTOS = new ArrayList<>();
        List<CityReportTimeDTO> lastReportTimeDTOS = new ArrayList<>();
        if (firstCityIds.get(0).equals("all")) {
            firstCityIds = null;
        }
        if (lastCityIds.get(0).equals("all")) {
            lastCityIds = null;
        }
        List<String> cityOne = new ArrayList<>();
        List<String> cityTwo = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(firstCityIds)) {
            cityOne = firstCityIds;
        }
        if (CollectionUtils.isNotEmpty(lastCityIds)) {
            cityTwo = lastCityIds;
        }
        List<CityDO> allCitys = cityService.findAllCitys();
        for (ReportTimeEnum value : ReportTimeEnum.values()) {
            if (!value.getType().equals(ReportTimeEnum.SHORT.getType())) {
                continue;
            }
            List<ReportSystemInitDO> reportSystemConfig = reportSystemService.findReportSystemConfig(cityOne, null,
                value.getDescription());
            List<ReportSystemInitDO> reportSystemConfig1 = reportSystemService.findReportSystemConfig(cityTwo, null,
                value.getDescription());
            for (ReportSystemInitDO reportSystemInitDO : reportSystemConfig) {
                CityReportTimeDTO cityReportTimeDTO = new CityReportTimeDTO();
                for (CityDO allCity : allCitys) {
                    if (allCity.getId().equals(reportSystemInitDO.getCityId())) {
                        cityReportTimeDTO.setCityName(allCity.getCity());
                    }
                }
                String sTimeStr = reportSystemInitDO.getValue().split("-")[0];
                cityReportTimeDTO.setDateStr(sTimeStr);
                cityReportTimeDTO.setStatus(reportSystemInitDO.getType());
                firstReportTimeDTOS.add(cityReportTimeDTO);
            }
            for (ReportSystemInitDO reportSystemInitDO : reportSystemConfig1) {
                CityReportTimeDTO cityReportTimeDTO = new CityReportTimeDTO();
                for (CityDO allCity : allCitys) {
                    if (allCity.getId().equals(reportSystemInitDO.getCityId())) {
                        cityReportTimeDTO.setCityName(allCity.getCity());
                    }
                }
                String sTimeStr = reportSystemInitDO.getValue().split("-")[1];
                cityReportTimeDTO.setDateStr(sTimeStr);
                cityReportTimeDTO.setStatus(reportSystemInitDO.getStatus());
                lastReportTimeDTOS.add(cityReportTimeDTO);
            }
            cityReportResultDTO.setFirstReportTimeDTOS(firstReportTimeDTOS);
            cityReportResultDTO.setLastReportTimeDTOS(lastReportTimeDTOS);
        }
        return cityReportResultDTO;
    }

    @Override
    public LongCityReportResultDTO getLongCityReportTimeConfig(List<String> monthCityIds, List<String> summerCityIds,
        List<String> winterCityIds, List<String> yearCityIds) throws Exception {
        LongCityReportResultDTO longCityReportResultDTO = new LongCityReportResultDTO();
        List<CityReportTimeDTO> monthReportTimeDTOS = new ArrayList<>();
        List<CityReportTimeDTO> summerReportTimeDTOS = new ArrayList<>();
        List<CityReportTimeDTO> winterReportTimeDTOS = new ArrayList<>();
        List<CityReportTimeDTO> yearReportTimeDTOS = new ArrayList<>();
        if (monthCityIds.get(0).equals("all")) {
            monthCityIds = new ArrayList<>();
        }
        if (summerCityIds.get(0).equals("all")) {
            summerCityIds = new ArrayList<>();
        }
        if (winterCityIds.get(0).equals("all")) {
            winterCityIds = new ArrayList<>();
        }
        if (yearCityIds.get(0).equals("all")) {
            yearCityIds = new ArrayList<>();
        }
        List<CityDO> allCitys = cityService.findAllCitys();
        for (ReportTimeEnum value : ReportTimeEnum.values()) {
            if (value.getType().equals(ReportTimeEnum.SHORT.getType())) {
                continue;
            }
            if (value.getType().equals(ReportTimeEnum.MONTH.getType())) {
                List<ReportSystemInitDO> reportSystemConfig = reportSystemService.findReportSystemConfig(monthCityIds,
                    null,
                    value.getDescription());
                for (ReportSystemInitDO reportSystemInitDO : reportSystemConfig) {
                    /*if (Constants.PROVINCE_ID.equals(reportSystemInitDO.getCityId()) && reportSystemInitDO.getStatus() == null) {
                        continue;
                    }*/
                    CityReportTimeDTO cityReportTimeDTO = new CityReportTimeDTO();
                    for (CityDO allCity : allCitys) {
                        if (allCity.getId().equals(reportSystemInitDO.getCityId())) {
                            cityReportTimeDTO.setCityName(allCity.getCity());
                        }
                    }
                    cityReportTimeDTO.setStatus(reportSystemInitDO.getStatus());
                    cityReportTimeDTO.setDateStr(reportSystemInitDO.getValue());
                    monthReportTimeDTOS.add(cityReportTimeDTO);
                }
                longCityReportResultDTO.setMonthReportTimeDTOS(monthReportTimeDTOS);
            }
            if (value.getType().equals(ReportTimeEnum.SUMMER.getType())) {
                List<ReportSystemInitDO> reportSystemConfig1 = reportSystemService.findReportSystemConfig(summerCityIds,
                    null,
                    value.getDescription());
                for (ReportSystemInitDO reportSystemInitDO : reportSystemConfig1) {
                    /*if (Constants.PROVINCE_ID.equals(reportSystemInitDO.getCityId()) && reportSystemInitDO.getStatus() == null) {
                        continue;
                    }*/
                    CityReportTimeDTO cityReportTimeDTO = new CityReportTimeDTO();
                    for (CityDO allCity : allCitys) {
                        if (allCity.getId().equals(reportSystemInitDO.getCityId())) {
                            cityReportTimeDTO.setCityName(allCity.getCity());
                        }
                    }
                    cityReportTimeDTO.setStatus(reportSystemInitDO.getStatus());
                    cityReportTimeDTO.setDateStr(reportSystemInitDO.getValue());
                    summerReportTimeDTOS.add(cityReportTimeDTO);
                }
                longCityReportResultDTO.setSummerReportTimeDTOS(summerReportTimeDTOS);
            }
            if (value.getType().equals(ReportTimeEnum.WINTER.getType())) {
                List<ReportSystemInitDO> reportSystemConfig2 = reportSystemService.findReportSystemConfig(winterCityIds,
                    null,
                    value.getDescription());
                for (ReportSystemInitDO reportSystemInitDO : reportSystemConfig2) {
                    /*if (Constants.PROVINCE_ID.equals(reportSystemInitDO.getCityId()) && reportSystemInitDO.getStatus() == null) {
                        continue;
                    }*/
                    CityReportTimeDTO cityReportTimeDTO = new CityReportTimeDTO();
                    for (CityDO allCity : allCitys) {
                        if (allCity.getId().equals(reportSystemInitDO.getCityId())) {
                            cityReportTimeDTO.setCityName(allCity.getCity());
                        }
                    }
                    cityReportTimeDTO.setStatus(reportSystemInitDO.getStatus());
                    cityReportTimeDTO.setDateStr(reportSystemInitDO.getValue());
                    winterReportTimeDTOS.add(cityReportTimeDTO);
                }
                longCityReportResultDTO.setWinterReportTimeDTOS(winterReportTimeDTOS);
            }
            if (value.getType().equals(ReportTimeEnum.YEAR.getType())) {
                List<ReportSystemInitDO> reportSystemConfig3 = reportSystemService.findReportSystemConfig(yearCityIds,
                    null,
                    value.getDescription());
                for (ReportSystemInitDO reportSystemInitDO : reportSystemConfig3) {
                    /*if (Constants.PROVINCE_ID.equals(reportSystemInitDO.getCityId()) && reportSystemInitDO.getStatus() == null) {
                        continue;
                    }*/
                    CityReportTimeDTO cityReportTimeDTO = new CityReportTimeDTO();
                    for (CityDO allCity : allCitys) {
                        if (allCity.getId().equals(reportSystemInitDO.getCityId())) {
                            cityReportTimeDTO.setCityName(allCity.getCity());
                        }
                    }
                    cityReportTimeDTO.setStatus(reportSystemInitDO.getStatus());
                    cityReportTimeDTO.setDateStr(reportSystemInitDO.getValue());
                    yearReportTimeDTOS.add(cityReportTimeDTO);
                }
                longCityReportResultDTO.setYearReportTimeDTOS(yearReportTimeDTOS);
            }
        }
        return longCityReportResultDTO;
    }

    @Override
    public void doCityLongReportTimeConfig(String monthDate, String summerDate, String winterDate, String yearDate,
        List<String> monthCityIds, List<String> summerCityIds, List<String> winterCityIds, List<String> yearCityIds,
        String monthType, String summerType, String winterType,
        String yearType)
        throws Exception {
        if (monthDate == null || summerDate == null || winterDate == null || yearDate == null) {
            return;
        }
        List<String> month = monthCityIds;
        List<String> summer = summerCityIds;
        List<String> winter = winterCityIds;
        List<String> year = yearCityIds;
        if (CollectionUtils.isNotEmpty(monthCityIds)) {
            if (monthCityIds.get(0).equals("all")) {
                monthCityIds = new ArrayList<>();
            }
        }
        if (CollectionUtils.isNotEmpty(summerCityIds)) {
            if (summerCityIds.get(0).equals("all")) {
                summerCityIds = new ArrayList<>();
            }
        }
        if (CollectionUtils.isNotEmpty(winterCityIds)) {
            if (winterCityIds.get(0).equals("all")) {
                winterCityIds = new ArrayList<>();
            }
        }
        if (CollectionUtils.isNotEmpty(yearCityIds)) {
            if (yearCityIds.get(0).equals("all")) {
                yearCityIds = new ArrayList<>();
            }
        }
        List<String> hbTrue = new ArrayList<>();
        List<String> hbFalse = new ArrayList<>();
        List<String> monthTrue = new ArrayList<>();
        List<String> monthFalse = new ArrayList<>();
        //String dateToStrFORMAT = DateUtil.getDateToStrFORMAT(monthDate, "yyyy-MM-dd HH:mm:ss");
        //String dateToStrFORMAT1 = DateUtil.getDateToStrFORMAT(summerDate, "yyyy-MM-dd HH:mm:ss");
        //String dateToStrFORMAT2 = DateUtil.getDateToStrFORMAT(winterDate, "yyyy-MM-dd HH:mm:ss");
        //String dateToStrFORMAT3 = DateUtil.getDateToStrFORMAT(yearDate, "yyyy-MM-dd HH:mm:ss");
        //String month = dateToStrFORMAT.substring(8, 10);
        //String summer = dateToStrFORMAT1.substring(5, 10);
        //String winter = dateToStrFORMAT2.substring(5, 10);
        //String year = dateToStrFORMAT3.substring(5, 10);
        List<String> cityOne = new ArrayList<>();
        List<String> cityTwo = new ArrayList<>();
        List<String> cityThree = new ArrayList<>();
        List<String> cityFour = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(monthCityIds)) {
            cityOne = monthCityIds;
        }
        if (CollectionUtils.isNotEmpty(summerCityIds)) {
            cityTwo = summerCityIds;
        }
        if (CollectionUtils.isNotEmpty(winterCityIds)) {
            cityThree = winterCityIds;
        }
        if (CollectionUtils.isNotEmpty(yearCityIds)) {
            cityFour = yearCityIds;
        }
        for (ReportTimeEnum value : ReportTimeEnum.values()) {
            if (value.getType().equals(ReportTimeEnum.SHORT.getType())) {
                continue;
            }
            if (value.getType().equals(ReportTimeEnum.MONTH.getType())) {
                List<ReportSystemInitDO> reportSystemConfig = reportSystemService.findReportSystemConfig(cityOne, null,
                    value.getDescription());
                List<ReportSystemInitDO> all = reportSystemService.findReportSystemConfig(new ArrayList<>(), null,
                    value.getDescription());
                List<ReportSystemInitDO> hb = reportSystemService.findReportSystemConfig(
                    Arrays.asList(Constants.PROVINCE_ID), null,
                    value.getDescription());
                // 湖北的设置为null
                List<ReportSystemInitDO> reportSystemConfig1 = reportSystemService.findReportSystemConfig(
                    Arrays.asList(Constants.PROVINCE_ID), null, value.getDescription());
                ReportSystemInitDO reportSystemInitDO1 = reportSystemConfig1.get(0);
                reportSystemInitDO1.setStatus(null);
                reportSystemService.doSaveOrUpdate(reportSystemInitDO1);
                for (ReportSystemInitDO reportSystemInitDO : reportSystemConfig) {
                    reportSystemInitDO.setStatus(true);
                    reportSystemInitDO.setValue(monthDate);
                    reportSystemService.doSaveOrUpdate(reportSystemInitDO);
                }
                // 取消地市的
                if (CollectionUtils.isEmpty(month)) {
                    for (ReportSystemInitDO reportSystemInitDO : all) {
                        if (Constants.PROVINCE_ID.equals(reportSystemInitDO.getCityId())) {
                            continue;
                        }
                        reportSystemInitDO.setStatus(null);
                        reportSystemService.doSaveOrUpdate(reportSystemInitDO);
                    }
                }
                if (monthType != null) {
                    for (ReportSystemInitDO reportSystemInitDO : hb) {
                        reportSystemInitDO.setValue(monthDate);
                        reportSystemInitDO.setStatus(true);
                        reportSystemService.doSaveOrUpdate(reportSystemInitDO);
                    }
                } else {
                    for (ReportSystemInitDO reportSystemInitDO : hb) {
                        reportSystemInitDO.setValue(monthDate);
                        reportSystemInitDO.setStatus(null);
                        reportSystemService.doSaveOrUpdate(reportSystemInitDO);
                    }
                }
            }
            if (value.getType().equals(ReportTimeEnum.SUMMER.getType())) {
                List<ReportSystemInitDO> reportSystemConfig1 = reportSystemService.findReportSystemConfig(cityTwo, null,
                    value.getDescription());
                List<ReportSystemInitDO> all = reportSystemService.findReportSystemConfig(new ArrayList<>(), null,
                    value.getDescription());
                List<ReportSystemInitDO> hb = reportSystemService.findReportSystemConfig(
                    Arrays.asList(Constants.PROVINCE_ID), null,
                    value.getDescription());
                // 湖北的设置为null
                List<ReportSystemInitDO> reportSystemConfig2 = reportSystemService.findReportSystemConfig(
                    Arrays.asList(Constants.PROVINCE_ID), null, value.getDescription());
                ReportSystemInitDO reportSystemInitDO1 = reportSystemConfig2.get(0);
                reportSystemInitDO1.setStatus(null);
                reportSystemService.doSaveOrUpdate(reportSystemInitDO1);
                for (ReportSystemInitDO reportSystemInitDO : reportSystemConfig1) {
                    reportSystemInitDO.setStatus(true);
                    reportSystemInitDO.setValue(summerDate);
                    reportSystemService.doSaveOrUpdate(reportSystemInitDO);
                }
                // 取消地市的
                if (CollectionUtils.isEmpty(summer)) {
                    for (ReportSystemInitDO reportSystemInitDO : all) {
                        if (Constants.PROVINCE_ID.equals(reportSystemInitDO.getCityId())) {
                            continue;
                        }
                        reportSystemInitDO.setStatus(null);
                        reportSystemService.doSaveOrUpdate(reportSystemInitDO);
                    }
                }
                if (summerType != null) {
                    for (ReportSystemInitDO reportSystemInitDO : hb) {
                        reportSystemInitDO.setValue(summerDate);
                        reportSystemInitDO.setStatus(true);
                        reportSystemService.doSaveOrUpdate(reportSystemInitDO);
                    }
                } else {
                    for (ReportSystemInitDO reportSystemInitDO : hb) {
                        reportSystemInitDO.setValue(summerDate);
                        reportSystemInitDO.setStatus(null);
                        reportSystemService.doSaveOrUpdate(reportSystemInitDO);
                    }
                }
            }
            if (value.getType().equals(ReportTimeEnum.WINTER.getType())) {
                List<ReportSystemInitDO> reportSystemConfig2 = reportSystemService.findReportSystemConfig(cityThree, null,
                    value.getDescription());
                List<ReportSystemInitDO> all = reportSystemService.findReportSystemConfig(new ArrayList<>(), null,
                    value.getDescription());
                List<ReportSystemInitDO> hb = reportSystemService.findReportSystemConfig(
                    Arrays.asList(Constants.PROVINCE_ID), null,
                    value.getDescription());
                // 湖北的设置为null
                List<ReportSystemInitDO> reportSystemConfig3 = reportSystemService.findReportSystemConfig(
                    Arrays.asList(Constants.PROVINCE_ID), null, value.getDescription());
                ReportSystemInitDO reportSystemInitDO1 = reportSystemConfig3.get(0);
                reportSystemInitDO1.setStatus(null);
                reportSystemService.doSaveOrUpdate(reportSystemInitDO1);
                for (ReportSystemInitDO reportSystemInitDO : reportSystemConfig2) {
                    reportSystemInitDO.setStatus(true);
                    reportSystemInitDO.setValue(winterDate);
                    reportSystemService.doSaveOrUpdate(reportSystemInitDO);
                }
                // 取消地市的
                if (CollectionUtils.isEmpty(winter)) {
                    for (ReportSystemInitDO reportSystemInitDO : all) {
                        if (Constants.PROVINCE_ID.equals(reportSystemInitDO.getCityId())) {
                            continue;
                        }
                        reportSystemInitDO.setStatus(null);
                        reportSystemService.doSaveOrUpdate(reportSystemInitDO);
                    }
                }
                if (winterType != null) {
                    for (ReportSystemInitDO reportSystemInitDO : hb) {
                        reportSystemInitDO.setValue(winterDate);
                        reportSystemInitDO.setStatus(true);
                        reportSystemService.doSaveOrUpdate(reportSystemInitDO);
                    }
                } else {
                    for (ReportSystemInitDO reportSystemInitDO : hb) {
                        reportSystemInitDO.setValue(winterDate);
                        reportSystemInitDO.setStatus(null);
                        reportSystemService.doSaveOrUpdate(reportSystemInitDO);
                    }
                }
            }
            if (value.getType().equals(ReportTimeEnum.YEAR.getType())) {
                List<ReportSystemInitDO> reportSystemConfig3 = reportSystemService.findReportSystemConfig(cityFour, null,
                    value.getDescription());
                List<ReportSystemInitDO> all = reportSystemService.findReportSystemConfig(new ArrayList<>(), null,
                    value.getDescription());
                List<ReportSystemInitDO> hb = reportSystemService.findReportSystemConfig(
                    Arrays.asList(Constants.PROVINCE_ID), null,
                    value.getDescription());
                // 湖北的设置为null
                List<ReportSystemInitDO> reportSystemConfig4 = reportSystemService.findReportSystemConfig(
                    Arrays.asList(Constants.PROVINCE_ID), null, value.getDescription());
                ReportSystemInitDO reportSystemInitDO1 = reportSystemConfig4.get(0);
                reportSystemInitDO1.setStatus(null);
                reportSystemService.doSaveOrUpdate(reportSystemInitDO1);
                for (ReportSystemInitDO reportSystemInitDO : reportSystemConfig3) {
                    reportSystemInitDO.setStatus(true);
                    reportSystemInitDO.setValue(yearDate);
                    reportSystemService.doSaveOrUpdate(reportSystemInitDO);
                }
                // 取消地市的
                if (CollectionUtils.isEmpty(year)) {
                    for (ReportSystemInitDO reportSystemInitDO : all) {
                        if (Constants.PROVINCE_ID.equals(reportSystemInitDO.getCityId())) {
                            continue;
                        }
                        reportSystemInitDO.setStatus(null);
                        reportSystemService.doSaveOrUpdate(reportSystemInitDO);
                    }
                }
                if (yearType != null) {
                    for (ReportSystemInitDO reportSystemInitDO : hb) {
                        reportSystemInitDO.setValue(yearDate);
                        reportSystemInitDO.setStatus(true);
                        reportSystemService.doSaveOrUpdate(reportSystemInitDO);
                    }
                } else {
                    for (ReportSystemInitDO reportSystemInitDO : hb) {
                        reportSystemInitDO.setValue(yearDate);
                        reportSystemInitDO.setStatus(null);
                        reportSystemService.doSaveOrUpdate(reportSystemInitDO);
                    }
                }
            }
        }
    }

    /**
     * 预测偏差计算公式，预测偏差=预测值-实际值；
     */
    private List<BigDecimal> getDeviation(LoadCityFcDO loadCityFcDO, LoadCityHisDO loadCityHisDO) {
        Map<String, BigDecimal> map = new HashMap<>();
        if (loadCityFcDO != null && loadCityHisDO != null) {
            Map<String, BigDecimal> hisMap = BasePeriodUtils
                .toMap(loadCityHisDO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
            Map<String, BigDecimal> fcMap = BasePeriodUtils
                .toMap(loadCityFcDO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
            for (String column : hisMap.keySet()) {
                BigDecimal his = hisMap.get(column);
                BigDecimal fc = fcMap.get(column);
                map.put(column, getDeviationNumber(fc, his));
            }
        }
        TreeMap<String, BigDecimal> treeMap = new TreeMap<>(map);
        List<BigDecimal> deviationList = DataUtil.mapToList(treeMap);
        return deviationList;
    }
    /**
     * 两个数相减（减数-被减数）
     */
    private BigDecimal getDeviationNumber(BigDecimal meiosis, BigDecimal minuend) {
        if (meiosis == null || minuend == null) {
            return null;
        }
        return meiosis.subtract(minuend);
    }

    /**
     * 计算加权平均（日最大负荷权重50%，月最大负荷权重20%,其他各占10%）
     */
    private BigDecimal getWeightAverageAccuracy(BigDecimal dayAccuracy, BigDecimal maxLoadAccuracy,
        BigDecimal minLoadAccuracy, BigDecimal monthMaxLoadAccuracy, BigDecimal monthEnergyAccuracy) {
        List<BigDecimal> res = new ArrayList<>();
        BigDecimal result1 = null;
        BigDecimal result2 = null;
        BigDecimal result3 = null;
        BigDecimal result4 = null;
        BigDecimal result5 = null;
        if (maxLoadAccuracy != null) {
            result1 = com.tsieframework.util.compatible.BigDecimalUtils.multiply(maxLoadAccuracy, new BigDecimal("0.5"));
            res.add(result1);
        }
        if (monthMaxLoadAccuracy != null) {
            result2 = com.tsieframework.util.compatible.BigDecimalUtils.multiply(monthMaxLoadAccuracy, new BigDecimal("0.2"));
            res.add(result2);
        }
        if (dayAccuracy != null) {
            result3 = com.tsieframework.util.compatible.BigDecimalUtils.multiply(dayAccuracy, new BigDecimal("0.1"));
            res.add(result3);
        }
        if (minLoadAccuracy != null) {
            result4 = com.tsieframework.util.compatible.BigDecimalUtils.multiply(minLoadAccuracy, new BigDecimal("0.1"));
            res.add(result4);
        }
        if (monthEnergyAccuracy != null) {
            result5 = com.tsieframework.util.compatible.BigDecimalUtils.multiply(monthEnergyAccuracy, new BigDecimal("0.1"));
            res.add(result5);
        }
        return BigDecimalUtils.listSum(res);
    }

}
