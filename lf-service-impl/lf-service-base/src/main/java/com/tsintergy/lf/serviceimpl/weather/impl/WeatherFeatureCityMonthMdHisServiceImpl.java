package com.tsintergy.lf.serviceimpl.weather.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsieframework.util.compatible.StringUtils;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityMonthMdHisService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityMonthMdHisDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityYearHisDO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherFeatureCityMonthMdHisDAO;
import java.sql.Timestamp;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2023-06-18
 * @since 1.0.0
 */
@Service("weatherFeatureCityMonthMdHisService")
public class WeatherFeatureCityMonthMdHisServiceImpl extends BaseServiceImpl implements
    WeatherFeatureCityMonthMdHisService {

    @Autowired
    private WeatherFeatureCityMonthMdHisDAO weatherFeatureCityMonthMdHisDAO;

    @Override
    public List<WeatherFeatureCityMonthMdHisDO> findWeatherFeatureCityTenDaysDTO(String cityId, String year,
        String month) {
        return weatherFeatureCityMonthMdHisDAO.findAll(JpaWrappers.<WeatherFeatureCityMonthMdHisDO>lambdaQuery()
            .eq(!StringUtils.isEmpty(cityId), WeatherFeatureCityMonthMdHisDO::getCityId, cityId)
            .eq(!StringUtils.isEmpty(year), WeatherFeatureCityMonthMdHisDO::getYear, year)
            .eq(!StringUtils.isEmpty(month), WeatherFeatureCityMonthMdHisDO::getMonth, month)
        );
    }

    @Override
    public void doSaveOrUpdate(WeatherFeatureCityMonthMdHisDO weatherFeatureCityMonthMdHisDO) {
        List<WeatherFeatureCityMonthMdHisDO> all = weatherFeatureCityMonthMdHisDAO.findAll(
            JpaWrappers.<WeatherFeatureCityMonthMdHisDO>lambdaQuery()
                .eq(!StringUtils.isEmpty(weatherFeatureCityMonthMdHisDO.getCityId()),
                    WeatherFeatureCityMonthMdHisDO::getCityId, weatherFeatureCityMonthMdHisDO.getCityId())
                .eq(!StringUtils.isEmpty(weatherFeatureCityMonthMdHisDO.getYear()),
                    WeatherFeatureCityMonthMdHisDO::getYear,
                    weatherFeatureCityMonthMdHisDO.getYear())
                .eq(!StringUtils.isEmpty(weatherFeatureCityMonthMdHisDO.getMonth()),
                    WeatherFeatureCityMonthMdHisDO::getMonth,
                    weatherFeatureCityMonthMdHisDO.getMonth())
        );
        if (CollectionUtils.isEmpty(all)) {
            weatherFeatureCityMonthMdHisDAO.save(weatherFeatureCityMonthMdHisDO);
        } else {
            weatherFeatureCityMonthMdHisDO.setId(all.get(0).getId());
            if (weatherFeatureCityMonthMdHisDO.getHighestTemperature() == null) {
                weatherFeatureCityMonthMdHisDO.setHighestTemperature(all.get(0).getHighestTemperature());
            }
            if (weatherFeatureCityMonthMdHisDO.getLowestTemperature() == null) {
                weatherFeatureCityMonthMdHisDO.setLowestTemperature(all.get(0).getLowestTemperature());
            }
            if (weatherFeatureCityMonthMdHisDO.getAveTemperature() == null) {
                weatherFeatureCityMonthMdHisDO.setAveTemperature(all.get(0).getAveTemperature());
            }
            if (weatherFeatureCityMonthMdHisDO.getExtremeHighestTemperature() == null) {
                weatherFeatureCityMonthMdHisDO.setExtremeHighestTemperature(all.get(0).getExtremeHighestTemperature());
            }
            if (weatherFeatureCityMonthMdHisDO.getExtremeLowestTemperature() == null) {
                weatherFeatureCityMonthMdHisDO.setExtremeLowestTemperature(all.get(0).getExtremeLowestTemperature());
            }
            if (weatherFeatureCityMonthMdHisDO.getExtremeAveTemperature() == null) {
                weatherFeatureCityMonthMdHisDO.setExtremeAveTemperature(all.get(0).getExtremeAveTemperature());
            }
            if (weatherFeatureCityMonthMdHisDO.getMaxTem() == null) {
                weatherFeatureCityMonthMdHisDO.setMaxTem(all.get(0).getMaxTem());
            }
            if (weatherFeatureCityMonthMdHisDO.getMinTem() == null) {
                weatherFeatureCityMonthMdHisDO.setMinTem(all.get(0).getMinTem());
            }
            if (weatherFeatureCityMonthMdHisDO.getAveTem() == null) {
                weatherFeatureCityMonthMdHisDO.setAveTem(all.get(0).getAveTem());
            }
            if (weatherFeatureCityMonthMdHisDO.getExtremeMaxTem() == null) {
                weatherFeatureCityMonthMdHisDO.setExtremeMaxTem(all.get(0).getExtremeMaxTem());
            }
            if (weatherFeatureCityMonthMdHisDO.getExtremeMinTem() == null) {
                weatherFeatureCityMonthMdHisDO.setExtremeMinTem(all.get(0).getExtremeMinTem());
            }
            if (weatherFeatureCityMonthMdHisDO.getExtremeAveTem() == null) {
                weatherFeatureCityMonthMdHisDO.setExtremeAveTem(all.get(0).getExtremeAveTem());
            }
            weatherFeatureCityMonthMdHisDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            weatherFeatureCityMonthMdHisDAO.saveOrUpdateByTemplate(weatherFeatureCityMonthMdHisDO);
        }
    }
}
