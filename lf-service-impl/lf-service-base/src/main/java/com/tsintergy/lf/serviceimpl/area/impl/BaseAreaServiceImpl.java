/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.area.impl;

import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.serviceapi.base.area.api.BaseAreaService;
import com.tsintergy.lf.serviceapi.base.area.api.BasePlanService;
import com.tsintergy.lf.serviceapi.base.area.dto.AreaDTO;
import com.tsintergy.lf.serviceapi.base.area.dto.BaseCityDTO;
import com.tsintergy.lf.serviceapi.base.area.dto.CityAndAreaDTO;
import com.tsintergy.lf.serviceapi.base.area.pojo.BaseAreaDO;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceimpl.area.dao.BaseAreaDAO;
import io.swagger.annotations.ApiModel;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/8/4 9:41
 * @Version: 1.0.0
 */
@Service
public class BaseAreaServiceImpl extends BaseServiceImpl implements BaseAreaService {

    @Autowired
    private BaseAreaDAO baseAreaDAO;
    @Autowired
    private BasePlanService basePlanService;
    @Autowired
    private CityService cityService;
    @Override
    public List<BaseAreaDO> findDOBYPlan(String planId) throws Exception {
        List<String> areaIdsByPlanId = basePlanService.findAreaIdsByPlanId(planId);
        List<BaseAreaDO> byIds = baseAreaDAO.findByIds(areaIdsByPlanId);
        return byIds;
    }
    @Override
    public CityAndAreaDTO findCityAndAreaDTO(String planId) throws Exception {
        List<CityDO> allCitys = cityService.findAllCitys();
        Map<String, String> cityMap = allCitys.stream().collect(Collectors.toMap(CityDO::getId, CityDO::getCity));
        CityAndAreaDTO cityAndAreaDTO = new CityAndAreaDTO();
        List<String> cityIds=new ArrayList<>();;
        if (planId!=null){
           List<BaseAreaDO> dobyPlan = findDOBYPlan(planId);
                     List<AreaDTO> areaDTOList=new ArrayList<>();

           for (BaseAreaDO baseAreaDO : dobyPlan) {
               AreaDTO areaDTO = new AreaDTO();
               areaDTO.setAreaId(baseAreaDO.getId());
               areaDTO.setAreaName(baseAreaDO.getName());
               String ids = baseAreaDO.getCityIds();
               String[] split = ids.split(",");
               List<String> list = Arrays.asList(split);
               cityIds.addAll(list);
               List<BaseCityDTO> baseCityDTOS=new ArrayList<>();
               for (String s : list) {
                   String cityName = cityMap.get(s);
                   BaseCityDTO baseCityDTO = new BaseCityDTO();
                   baseCityDTO.setCityId(s);
                   baseCityDTO.setCityName(cityName);
                   baseCityDTOS.add(baseCityDTO);
               }
               areaDTO.setAreaCityList(baseCityDTOS);
               areaDTOList.add(areaDTO);
           }
            cityAndAreaDTO.setAreaList(areaDTOList);
       }
        List<BaseCityDTO> baseCityDTOS=new ArrayList<>();
        for (Entry<String, String> entry : cityMap.entrySet()) {
            String k = entry.getKey();
            String v = entry.getValue();
            BaseCityDTO baseCityDTO = new BaseCityDTO();
            baseCityDTO.setCityName(v);
            baseCityDTO.setCityId(k);
            if (planId==null)
            {
               baseCityDTO.setChecked(false);
            }
            else {
                if (!cityIds.contains(k))
                {
                    baseCityDTO.setChecked(true);
                }else {
                    baseCityDTO.setChecked(false);
                }
            }
            baseCityDTOS.add(baseCityDTO);
        }

        cityAndAreaDTO.setCityList(baseCityDTOS);
        return cityAndAreaDTO;
    }

    @Override
    public List<String> getCityIdsByAreaId(String areaId) {
        BaseAreaDO baseAreaDO = baseAreaDAO.findById(areaId);
        String cityIds = baseAreaDO.getCityIds();
        String[] split = cityIds.split(Constants.SEPARATOR_PUNCTUATION);
        List<String> cityIdList =Arrays.asList(split);
        return cityIdList;
    }

    @Override
    public  List<String> doSaveOrUpdate(List<AreaDTO> areaDTOS) {
        List<String> areaIds=new ArrayList<>();
        for (AreaDTO areaDTO : areaDTOS) {
            BaseAreaDO baseAreaDO = new BaseAreaDO();
            baseAreaDO.setId(areaDTO.getAreaId());
            StringBuffer stringBuffer=new StringBuffer();
            List<String> cityIds = areaDTO.getCityIds();
            for (int i = 0; i < cityIds.size(); i++) {
                if (i==0)
                {
                    stringBuffer.append(cityIds.get(i));
                }else {
                    stringBuffer.append(","+cityIds.get(i));
                }
            }
            baseAreaDO.setCityIds(stringBuffer.toString());
            baseAreaDO.setUpdateTime(new Timestamp(System.currentTimeMillis()));
            baseAreaDO.setName(areaDTO.getAreaName());
            baseAreaDAO.saveOrUpdate(baseAreaDO);
            areaIds.add(baseAreaDO.getId());
        }
        return areaIds;
    }

    @Override
    public void doDelete(List<String> areaList) {
        for (String s : areaList) {
            baseAreaDAO.deleteByPk(s);
        }
    }
}