/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.area.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.math.BigDecimalFunctions;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.util.compatible.BigDecimalUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.core.util.PeriodDataUtil;
import com.tsintergy.lf.serviceapi.base.area.api.BaseAreaService;
import com.tsintergy.lf.serviceapi.base.area.api.WeatherAreaHisService;
import com.tsintergy.lf.serviceapi.base.area.pojo.BasePlanDO;
import com.tsintergy.lf.serviceapi.base.area.pojo.LoadAreaHisDO;
import com.tsintergy.lf.serviceapi.base.area.pojo.WeatherAreaFcDO;
import com.tsintergy.lf.serviceapi.base.area.pojo.WeatherAreaHisDO;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherNewEnum;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityHisService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.BaseWeatherDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisDO;
import com.tsintergy.lf.serviceimpl.area.dao.WeatherAreaHisDAO;
import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/8/3 17:26
 * @Version: 1.0.0
 */

@Slf4j
@Service("weatherAreaHisService")
public class WeatherAreaHisServiceImpl implements WeatherAreaHisService {


    @Autowired
    CityService cityService;
    
    @Autowired
    WeatherAreaHisDAO weatherAreaHisDAO;

    @Autowired
    private WeatherCityHisService weatherCityHisService;

    @Autowired
    private BaseAreaService baseAreaService;
    @Override
    public List<WeatherAreaHisDO> findWeatherCityHisDOs(String areaId, Integer type, Date startDate, Date endDate)
        {
        try {
            List<WeatherAreaHisDO> weatherAreaHisDOS = weatherAreaHisDAO.findAll(JpaWrappers.<WeatherAreaHisDO>lambdaQuery()
                .eq(areaId != null, WeatherAreaHisDO::getAreaId, areaId)
                .eq(type != null, WeatherAreaHisDO::getType, type)
                .le(endDate != null, WeatherAreaHisDO::getDate, endDate)
                .ge(startDate != null, WeatherAreaHisDO::getDate, startDate)
            );
            return weatherAreaHisDOS;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("", e.getMessage(), e);
        }
    }

    @Override
    public void doInsertOrUpdate(WeatherAreaHisDO weatherAreaHisDO)  {
        List<WeatherAreaHisDO> weatherAreaHisDOS = this.findWeatherCityHisDOs(weatherAreaHisDO.getAreaId(), weatherAreaHisDO.getType(), weatherAreaHisDO.getDate(),
                weatherAreaHisDO.getDate());
        if (CollectionUtils.isEmpty(weatherAreaHisDOS)) {
            weatherAreaHisDAO.save(weatherAreaHisDO);
            return;
        }
        weatherAreaHisDO.setId(weatherAreaHisDOS.get(0).getId());
        weatherAreaHisDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
        weatherAreaHisDAO.saveOrUpdateByTemplate(weatherAreaHisDO);
    }

    @Override
    public void doInsertOrUpdateList(List<WeatherAreaHisDO> weatherAreaHisDOS)  {
        for (WeatherAreaHisDO weatherAreaHisDO : weatherAreaHisDOS) {
            this.doInsertOrUpdate(weatherAreaHisDO);
        }
    }

    @Override
    public void doInsert(WeatherAreaHisDO weatherAreaHisDO) {
        if (weatherAreaHisDO!=null) {
            weatherAreaHisDAO.save(weatherAreaHisDO);
        }
    }

    @Override
    public void statAreaHisWeather(String areaId, Date startDate, Date endDate) throws Exception {
        List<Date> dateList = DateUtil.getListBetweenDay(startDate,endDate);
        long start = System.currentTimeMillis();

        WeatherNewEnum[] values = WeatherNewEnum.values();
        Map<String, BaseWeatherDO> collect = weatherCityHisService
            .findWeatherCityHisDOs(null, null, startDate, endDate).stream()
            .collect(Collectors.toMap(t -> {
                return t.getCityId() + DateUtil.getDateToStr(t.getDate()) + t.getType();
            }, t -> t));

        long select = System.currentTimeMillis();
        log.info("查询时间:----"+(select-start));

        List<String> cityIds = baseAreaService.getCityIdsByAreaId(areaId);

        List<WeatherAreaHisDO> weatherAreaHisDOS = new ArrayList<>();
        for (Date date : dateList) {
            for (WeatherNewEnum value : values) {
                Map<String, BigDecimal> aveAreaWeatherValueMap = getAveAreaWeatherValueMap(cityIds, collect,date,value.getType());
                if (!CollectionUtils.isEmpty(aveAreaWeatherValueMap)){
                    WeatherAreaHisDO weatherAreaHisDO = new WeatherAreaHisDO();
                    BasePeriodUtils.setAllFiled(weatherAreaHisDO,aveAreaWeatherValueMap);
                    weatherAreaHisDO.setAreaId(areaId);
                    weatherAreaHisDO.setDate(new java.sql.Date(date.getTime()));
                    weatherAreaHisDO.setType(value.getType());
                    weatherAreaHisDOS.add(weatherAreaHisDO);
                }
            }
        }
        log.info("准备数据时间:----"+(System.currentTimeMillis()-select));

        updateAreaWeather(weatherAreaHisDOS,startDate,endDate,areaId);
    }

    private void updateAreaWeather(List<WeatherAreaHisDO> weatherAreaHisDOS, Date startDate, Date endDate,
        String areaId) {
        long update = System.currentTimeMillis();

        if (CollectionUtils.isEmpty(weatherAreaHisDOS)){
            return;
        }
        weatherAreaHisDAO.delete(JpaWrappers.<WeatherAreaHisDO>lambdaQuery()
            .eq(areaId!=null,WeatherAreaHisDO::getAreaId,areaId)
            .le(endDate != null, WeatherAreaHisDO::getDate, endDate)
            .ge(startDate != null, WeatherAreaHisDO::getDate, startDate));

        weatherAreaHisDAO.saveOrUpdateBatch(weatherAreaHisDOS);

        log.info("删除插入时间:----"+(System.currentTimeMillis()-update));

    }

    @Override
    public void deleteByAreasIds(List<String> areaIds) throws Exception {
        weatherAreaHisDAO.delete(JpaWrappers.<WeatherAreaHisDO>lambdaQuery()
        .in(!CollectionUtils.isEmpty(areaIds),WeatherAreaHisDO::getAreaId,areaIds));
    }

    @Override
    public Map<String, BigDecimal> getAveAreaWeatherValueMap(List<String> cityIds,Map<String, BaseWeatherDO> map,Date date,Integer type)
            throws Exception {
        //将各个地市的数据放到集合中
        List<List<BigDecimal>> weatherSum = new ArrayList<>();
        for (String cityId : cityIds) {
            BaseWeatherDO weatherCityHisDO = map.get(cityId+DateUtil.getDateToStr(date)+type);
            if (weatherCityHisDO == null){
                continue;
            }
            weatherSum.add(BasePeriodUtils
                    .toList(weatherCityHisDO, Constants.LOAD_CURVE_POINT_NUM,
                            Constants.LOAD_CURVE_START_WITH_ZERO));
        }

        if (CollectionUtils.isEmpty(weatherSum)){
            return null;
        }

        List<BigDecimal> weatherValue = ColumnUtil.getZeroOrNullList(96,null);

        Map<Integer,Integer> countMap = new HashMap<>(Constants.WEATHER_CURVE_POINT_NUM);
        int count = 0;
        for (int i = 0; i < weatherSum.size(); i++) {
            List<BigDecimal> values =  weatherSum.get(i);
            if (CollectionUtils.isEmpty(values)){
                continue;
            }
            for (int j = 0; j < Constants.WEATHER_CURVE_POINT_NUM; j++) {
                if (values.get(j)!=null){
                    if (weatherValue.get(j) == null){
                        weatherValue.set(j,BigDecimal.ZERO);
                    }
                    Integer size = countMap.get(j);
                    if (size == null){
                        count = 1;
                        countMap.put(j,count);
                    }else{
                        countMap.put(j,++size);
                    }
                    weatherValue.set(j,weatherValue.get(j).add(values.get(j)));
                }
            }
        }

        //气象求和之后除以城市数求平均
        for (int i = 0; i < weatherValue.size(); i++) {
            if (weatherValue.get(i)!=null){
                weatherValue.set(i, BigDecimalUtils.divide(weatherValue.get(i),BigDecimal.valueOf(countMap.get(i)),2));
            }
        }
        Map<String, BigDecimal> valueMap = ColumnUtil.listToMap(weatherValue, Constants.WEATHER_CURVE_START_WITH_ZERO);

        return valueMap;
    }
}
