package com.tsintergy.lf.serviceimpl.report.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.report.api.ReportAccuracySynthesizeCumulativeService;
import com.tsintergy.lf.serviceapi.base.report.dto.ReportAccuracySynthesizeDTO;
import com.tsintergy.lf.serviceapi.base.report.dto.ReportAccuracySynthesizeInfoDTO;
import com.tsintergy.lf.serviceapi.base.report.pojo.ReportAccuracySynthesizeCumulativeDO;
import com.tsintergy.lf.serviceimpl.common.util.DateUtils;
import com.tsintergy.lf.serviceimpl.report.dao.ReportAccuracySynthesizeCumulativeDAO;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("reportAccuracySynthesizeCumulativeService")
public class ReportAccuracySynthesizeCumulativeServiceImpl implements ReportAccuracySynthesizeCumulativeService {

    @Autowired
    private ReportAccuracySynthesizeCumulativeDAO reportAccuracySynthesizeCumulativeDAO;

    @Autowired
    CityService cityService;

    @Override
    public List<ReportAccuracySynthesizeDTO> findByDate(Date start, Date end, Integer type) throws Exception {
        List<CityDO> allCitysExcludeAoLocal = cityService.findAllCitysExcludeAoLocal(true);
        if ((start == null || end == null)) {
            List<ReportAccuracySynthesizeCumulativeDO> reportAccuracySynthesizeCumulativeDOS1 = reportAccuracySynthesizeCumulativeDAO
                .selectList(new LambdaQueryWrapper<ReportAccuracySynthesizeCumulativeDO>()
                    .orderByDesc(ReportAccuracySynthesizeCumulativeDO::getDate));
            if (reportAccuracySynthesizeCumulativeDOS1 != null
                && reportAccuracySynthesizeCumulativeDOS1.size() > 0) {
                ReportAccuracySynthesizeCumulativeDO reportAccuracySynthesizeCumulativeDO = reportAccuracySynthesizeCumulativeDOS1
                    .get(0);
                start = reportAccuracySynthesizeCumulativeDO.getDate();
                end = reportAccuracySynthesizeCumulativeDO.getDate();
            } else {
                return null;
            }
        }
        List<ReportAccuracySynthesizeDTO> dtos = new ArrayList<>();
        for (CityDO cityDO : allCitysExcludeAoLocal) {
            ReportAccuracySynthesizeCumulativeDO reportAccuracySynthesizeCumulativeDO = reportAccuracySynthesizeCumulativeDAO
                .selectOne(new LambdaQueryWrapper<ReportAccuracySynthesizeCumulativeDO>()
                    .eq(ReportAccuracySynthesizeCumulativeDO::getType, type)
                    .ge(ReportAccuracySynthesizeCumulativeDO::getDate, start)
                    .le(ReportAccuracySynthesizeCumulativeDO::getDate, end)
                    .eq(ReportAccuracySynthesizeCumulativeDO::getCityId, cityDO.getId()));
            ReportAccuracySynthesizeDTO reportAccuracySynthesizeDTO = new ReportAccuracySynthesizeDTO();
            reportAccuracySynthesizeDTO.setCityName(cityDO.getCity());
            reportAccuracySynthesizeDTO.setCityId(cityDO.getId());
            String substring = DateUtils.getDateToStr(start).substring(0, 7);
            reportAccuracySynthesizeDTO.setDate(substring);
            if (reportAccuracySynthesizeCumulativeDO == null) {
                dtos.add(reportAccuracySynthesizeDTO);
                continue;
            }
            reportAccuracySynthesizeDTO.setComprehensiveAccuracy(reportAccuracySynthesizeCumulativeDO.getAccuracy());
            reportAccuracySynthesizeDTO
                .setComprehensiveAccuracyRight(reportAccuracySynthesizeCumulativeDO.getAccuracyCorrect());
            reportAccuracySynthesizeDTO.setId(reportAccuracySynthesizeCumulativeDO.getId());
            dtos.add(reportAccuracySynthesizeDTO);
        }
        return dtos;
    }

    @Override
    public List<ReportAccuracySynthesizeInfoDTO> findSynthesizeCumulative(java.util.Date date, Integer type,
        String cityId)
        throws Exception {
        List<CityDO> allCitysExcludeAoLocal = new ArrayList<>();
        if (cityId.equals("all")) {
            allCitysExcludeAoLocal = cityService.findAllCitysExcludeAoLocal(true);
        } else {
            allCitysExcludeAoLocal.add(cityService.findCityById(cityId));
        }
        if (date == null) {
            List<ReportAccuracySynthesizeCumulativeDO> reportAccuracySynthesizeCumulativeDOS1 = reportAccuracySynthesizeCumulativeDAO
                .selectList(new LambdaQueryWrapper<ReportAccuracySynthesizeCumulativeDO>()
                    .orderByDesc(ReportAccuracySynthesizeCumulativeDO::getDate));
            if (reportAccuracySynthesizeCumulativeDOS1 != null
                && reportAccuracySynthesizeCumulativeDOS1.size() > 0) {
                ReportAccuracySynthesizeCumulativeDO reportAccuracySynthesizeCumulativeDO = reportAccuracySynthesizeCumulativeDOS1
                    .get(0);
                date = reportAccuracySynthesizeCumulativeDO.getDate();
            } else {
                return null;
            }
        }
        List<ReportAccuracySynthesizeInfoDTO> dtos = new ArrayList<>();
        for (CityDO cityDO : allCitysExcludeAoLocal) {
            ReportAccuracySynthesizeCumulativeDO reportAccuracySynthesizeCumulativeDO = reportAccuracySynthesizeCumulativeDAO
                .selectOne(new LambdaQueryWrapper<ReportAccuracySynthesizeCumulativeDO>()
                    .eq(ReportAccuracySynthesizeCumulativeDO::getType, type)
                    .eq(ReportAccuracySynthesizeCumulativeDO::getDate, date)
                    .eq(ReportAccuracySynthesizeCumulativeDO::getCityId, cityDO.getId()));
            ReportAccuracySynthesizeInfoDTO reportAccuracySynthesizeInfoDTO = new ReportAccuracySynthesizeInfoDTO();
            reportAccuracySynthesizeInfoDTO.setCityName(cityDO.getCity());
            String substring = DateUtils.getDateToStr(date).substring(0, 7);
            reportAccuracySynthesizeInfoDTO.setDate(substring);
            if (reportAccuracySynthesizeCumulativeDO == null) {
                dtos.add(reportAccuracySynthesizeInfoDTO);
                continue;
            }
            BigDecimal accuracy = reportAccuracySynthesizeCumulativeDO.getAccuracyCorrect();
            if (accuracy == null) {
                accuracy = reportAccuracySynthesizeCumulativeDO.getAccuracy();
            }
            BeanUtils.copyProperties(reportAccuracySynthesizeCumulativeDO, reportAccuracySynthesizeInfoDTO);
            reportAccuracySynthesizeInfoDTO.setAccuracy(accuracy);
            dtos.add(reportAccuracySynthesizeInfoDTO);
        }
        return dtos;
    }

    @Override
    public ReportAccuracySynthesizeCumulativeDO findByDateAndCityId(Date date, String cityId, Integer type)
        throws Exception {
        List<ReportAccuracySynthesizeCumulativeDO> reportAccuracySynthesizeCumulativeDOS = reportAccuracySynthesizeCumulativeDAO
            .selectList(
                new LambdaQueryWrapper<ReportAccuracySynthesizeCumulativeDO>()
                    .eq(ReportAccuracySynthesizeCumulativeDO::getType, type)
                    .eq(ReportAccuracySynthesizeCumulativeDO::getCityId, cityId)
                    .eq(ReportAccuracySynthesizeCumulativeDO::getDate, date));
        if (reportAccuracySynthesizeCumulativeDOS == null || reportAccuracySynthesizeCumulativeDOS.size() == 0) {
            return null;
        } else {
            return reportAccuracySynthesizeCumulativeDOS.get(0);
        }

    }

    @Override
    public void updateAccuracyCorrect(String cityId, BigDecimal decimal, java.util.Date date, Integer type)
        throws Exception {
        ReportAccuracySynthesizeCumulativeDO reportAccuracySynthesizeCumulativeDO = new ReportAccuracySynthesizeCumulativeDO();
        ReportAccuracySynthesizeCumulativeDO reportAccuracySynthesizeCumulativeDO1 = reportAccuracySynthesizeCumulativeDAO
            .selectOne(new LambdaQueryWrapper<ReportAccuracySynthesizeCumulativeDO>()
                .eq(ReportAccuracySynthesizeCumulativeDO::getCityId, cityId)
                .eq(ReportAccuracySynthesizeCumulativeDO::getType, type)
                .eq(ReportAccuracySynthesizeCumulativeDO::getDate, date));
        if (reportAccuracySynthesizeCumulativeDO1 == null) {
            reportAccuracySynthesizeCumulativeDO.setAccuracyCorrect(decimal);
            reportAccuracySynthesizeCumulativeDO.setCityId(cityId);
            reportAccuracySynthesizeCumulativeDO.setDate(new java.sql.Date(date.getTime()));
            reportAccuracySynthesizeCumulativeDO.setType(type);
            reportAccuracySynthesizeCumulativeDO.setReportTime(new Timestamp(System.currentTimeMillis()));
            reportAccuracySynthesizeCumulativeDO.setCreateTime(new java.util.Date());
            reportAccuracySynthesizeCumulativeDO.setUpdateTime(new java.util.Date());
            reportAccuracySynthesizeCumulativeDAO.insert(reportAccuracySynthesizeCumulativeDO);
        } else {
            UpdateWrapper<ReportAccuracySynthesizeCumulativeDO> eq = new UpdateWrapper<ReportAccuracySynthesizeCumulativeDO>()
                .set("accuracy_correct", decimal).set("report_time",new Timestamp(System.currentTimeMillis())).eq("city_id", cityId).eq("type", type).eq("date", date);
            reportAccuracySynthesizeCumulativeDAO.update(reportAccuracySynthesizeCumulativeDO, eq);
        }
    }

    @Override
    public void update(ReportAccuracySynthesizeCumulativeDO reportAccuracySynthesizeCumulativeDO) throws Exception {
        reportAccuracySynthesizeCumulativeDAO
            .updateById(reportAccuracySynthesizeCumulativeDO);
    }

    @Override
    public void insert(ReportAccuracySynthesizeCumulativeDO reportAccuracySynthesizeCumulativeDO) throws Exception {
        reportAccuracySynthesizeCumulativeDAO
            .insert(reportAccuracySynthesizeCumulativeDO);
    }

    public List<ReportAccuracySynthesizeCumulativeDO> findByCityId(Date start, Date end, Integer type, String cityId)
        throws Exception {
        List<ReportAccuracySynthesizeCumulativeDO> reportAccuracySynthesizeCumulativeDOS = reportAccuracySynthesizeCumulativeDAO
            .selectList(
                new LambdaQueryWrapper<ReportAccuracySynthesizeCumulativeDO>()
                    .eq(ReportAccuracySynthesizeCumulativeDO::getType, type)
                    .ge(ReportAccuracySynthesizeCumulativeDO::getDate, start)
                    .le(ReportAccuracySynthesizeCumulativeDO::getDate, end)
                    .eq(ReportAccuracySynthesizeCumulativeDO::getCityId, cityId));
        return reportAccuracySynthesizeCumulativeDOS;
    }
}
