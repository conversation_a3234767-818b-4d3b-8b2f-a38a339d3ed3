package com.tsintergy.lf.serviceimpl.load.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsintergy.lf.serviceapi.base.load.api.SettingWarnInitService;
import com.tsintergy.lf.serviceapi.base.load.pojo.SettingWarnInitDO;
import com.tsintergy.lf.serviceimpl.load.dao.SettingWarnInitDAO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

@Service
public class SettingWarnInitServiceImpl implements SettingWarnInitService {

    @Autowired
    private SettingWarnInitDAO settingWarnInitDAO;

    @Override
    public List<SettingWarnInitDO> findByCityId(String cityId) {
        return settingWarnInitDAO.findAll(JpaWrappers.<SettingWarnInitDO>lambdaQuery()
                .eq(StringUtils.isNotBlank(cityId),SettingWarnInitDO::getCityId,cityId));
    }

    @Override
    public void saveOrUpdate(List<SettingWarnInitDO> settingWarnInitDOS) {
        for (SettingWarnInitDO settingWarnInitDO : settingWarnInitDOS) {
            settingWarnInitDAO.saveOrUpdateBatch(Arrays.asList(settingWarnInitDO));
        }
    }
}
