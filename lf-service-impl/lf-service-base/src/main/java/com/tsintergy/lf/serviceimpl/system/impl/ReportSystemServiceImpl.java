package com.tsintergy.lf.serviceimpl.system.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsintergy.lf.serviceapi.base.system.api.ReportSystemService;
import com.tsintergy.lf.serviceapi.base.system.pojo.ReportSystemInitDO;
import com.tsintergy.lf.serviceimpl.system.dao.ReportSystemInitDAO;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2023-05-08
 * @since 1.0.0
 */
@Service("reportSystemService")
public class ReportSystemServiceImpl extends BaseServiceImpl implements ReportSystemService {

    @Autowired
    ReportSystemInitDAO reportSystemInitDAO;

    @Override
    public List<ReportSystemInitDO> findReportSystemConfig(String cityId, String field, String name) {
        return reportSystemInitDAO.findAll(JpaWrappers.<ReportSystemInitDO>lambdaQuery()
            .eq(!StringUtils.isEmpty(cityId), ReportSystemInitDO::getCityId, cityId)
            .eq(!StringUtils.isEmpty(field), ReportSystemInitDO::getField, field)
            .eq(!StringUtils.isEmpty(name), ReportSystemInitDO::getName, name));
    }

    @Override
    public List<ReportSystemInitDO> findReportSystemConfig(List<String> cityIds, String field, String name) {
        return reportSystemInitDAO.findAll(JpaWrappers.<ReportSystemInitDO>lambdaQuery()
            .in(!CollectionUtils.isEmpty(cityIds), ReportSystemInitDO::getCityId, cityIds)
            .eq(!StringUtils.isEmpty(field), ReportSystemInitDO::getField, field)
            .eq(!StringUtils.isEmpty(name), ReportSystemInitDO::getName, name));
    }

    @Override
    public void doSaveOrUpdate(ReportSystemInitDO reportSystemInitDO) {
        List<ReportSystemInitDO> all = reportSystemInitDAO.findAll(JpaWrappers.<ReportSystemInitDO>lambdaQuery()
            .eq(!StringUtils.isEmpty(reportSystemInitDO.getCityId()), ReportSystemInitDO::getCityId,
                reportSystemInitDO.getCityId())
            .eq(!StringUtils.isEmpty(reportSystemInitDO.getField()), ReportSystemInitDO::getField,
                reportSystemInitDO.getField())
            .eq(!StringUtils.isEmpty(reportSystemInitDO.getName()), ReportSystemInitDO::getName,
                reportSystemInitDO.getName()));
        if (CollectionUtils.isEmpty(all)){
            reportSystemInitDAO.save(reportSystemInitDO);
        } else {
            String id = all.get(0).getId();
            reportSystemInitDO.setId(id);
            reportSystemInitDAO.saveOrUpdateByTemplate(reportSystemInitDO);
        }
    }
}
