/**
 *   
 *  Copyright (C), 2015‐2020, 北京清能互联科技有限公司  
 *  Author:  EDZ  
 *  Date:  2020/9/23 16:51  
 *  History:  
 *  <author> <time> <version> <desc> 
 */
package com.tsintergy.lf.serviceimpl.evalucation.dao;

import com.tsieframework.core.base.dao.hibernate.BaseAbstractDAO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.dao.hibernate.QueryOp;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.serviceapi.base.datamanage.pojo.DataCheckInfoDO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.StatisticsAccuracyLoadCityMonthHisDO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.StatisticsCityDayFcDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityMonthHisDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**  
 * Description:  <br> 
 * 
 * <AUTHOR>  
 * @create 2020/9/23  
 * @since 1.0.0  
 */
@Slf4j
@Component
public class StatisticsAccuracyLoadCityMonthHisDAO extends BaseAbstractDAO<StatisticsAccuracyLoadCityMonthHisDO> {

    /**
     * 根据年份和月份查询准确率值
     * @param cityId
     * @param caliberId
     * @param startYM 开始年月
     * @param endYM 结束年月
     * @return
     * @throws Exception
     */
    public List<StatisticsAccuracyLoadCityMonthHisDO> getStatisticsAccuracyLoadCityMonthHisDOs(String cityId, String caliberId, String startYM, String endYM) throws  Exception{
        DBQueryParamBuilder param = DBQueryParamBuilder.create().queryDataOnly();
        param.pageSize("0");
        if (!StringUtils.isBlank(cityId)) {
            param.where(QueryOp.StringEqualTo, "cityId", cityId);
        }
        if (!StringUtils.isBlank(caliberId)) {
            param.where(QueryOp.StringEqualTo, "caliberId", caliberId);
        }
        if (null != startYM) {
            param.where(QueryOp.StringNoLessThan, "year", startYM.substring(0,4));
        }
        if (null != endYM) {
            param.where(QueryOp.StringNoMoreThan, "year", endYM.substring(0,4));
        }

        List<StatisticsAccuracyLoadCityMonthHisDO> datas = query(param.build()).getDatas();
        List<StatisticsAccuracyLoadCityMonthHisDO> statisticsAccuracyLoadCityMonthHisDOS = new ArrayList<>();
        for(StatisticsAccuracyLoadCityMonthHisDO date : datas) {
            if((date.getYear()+"-"+date.getMonth()).compareTo(startYM) > -1 && (date.getYear()+"-"+date.getMonth()).compareTo(endYM) < 1) {
                statisticsAccuracyLoadCityMonthHisDOS.add(date);
            }
        }
        return statisticsAccuracyLoadCityMonthHisDOS;

    }


    public List<StatisticsAccuracyLoadCityMonthHisDO> findAccuracyMonth(String year, String month, String city, String caliberId){
        DBQueryParamBuilder param = DBQueryParamBuilder.create().queryDataOnly();
        param.pageSize("0");
        if (!StringUtils.isBlank(city)) {
            param.where(QueryOp.StringEqualTo, "cityId", city);
        }
        if (!StringUtils.isBlank(caliberId)) {
            param.where(QueryOp.StringEqualTo, "caliberId", caliberId);
        }
        if (!StringUtils.isBlank(year)) {
            param.where(QueryOp.StringEqualTo, "year", year);
        }
        if (!StringUtils.isBlank(month)) {
            param.where(QueryOp.StringEqualTo, "month", month);
        }

       return query(param.build()).getDatas();
    }

}