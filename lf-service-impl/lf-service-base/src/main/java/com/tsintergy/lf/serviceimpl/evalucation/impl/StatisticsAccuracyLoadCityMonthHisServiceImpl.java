/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.evalucation.impl;

import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.check.api.SettingCheckService;
import com.tsintergy.lf.serviceapi.base.evalucation.api.StatisticsAccuracyLoadCityMonthHisService;
import com.tsintergy.lf.serviceapi.base.evalucation.api.StatisticsCityDayFcService;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.MultipleAccuracyDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.SingleAccuraryDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.StatisticsAccuracyLoadCityMonthHisDO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.StatisticsCityDayFcDO;
import com.tsintergy.lf.serviceimpl.evalucation.dao.StatisticsAccuracyLoadCityMonthHisDAO;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/3/31 15:22
 * @Version: 1.0.0
 */
@Service("statisticsAccuracyLoadCityMonthHisService")
public class StatisticsAccuracyLoadCityMonthHisServiceImpl implements StatisticsAccuracyLoadCityMonthHisService {

    @Autowired
    private StatisticsAccuracyLoadCityMonthHisDAO statisticsAccuracyLoadCityMonthHisDAO;
    @Autowired
    private StatisticsCityDayFcService statisticsCityDayFcService;

    @Autowired
    private SettingCheckService settingCheckService;
    /**
     * 返回区间内月准确率平均值
     */
    @Override
    public List<MultipleAccuracyDTO> getAvgAccuracyLoadCityMonthHisDOs(Map<String, String> cityMap, String caliberId,
        String startYM, String endYM) throws Exception {
        List<MultipleAccuracyDTO> accuracyDTOList = new ArrayList<>();
        for (Map.Entry<String, String> city : cityMap.entrySet()) {
            List<StatisticsAccuracyLoadCityMonthHisDO> datas = statisticsAccuracyLoadCityMonthHisDAO
                .getStatisticsAccuracyLoadCityMonthHisDOs(city.getKey(), caliberId, startYM, endYM);
            if (CollectionUtils.isEmpty(datas)) {
                continue;
            }

            MultipleAccuracyDTO dto = new MultipleAccuracyDTO();
            BigDecimal avgAccuracy = new BigDecimal("0.0000");
            for (StatisticsAccuracyLoadCityMonthHisDO hisDO : datas) {
                avgAccuracy = avgAccuracy.add(hisDO.getAccuracy());
            }
            dto.setCityId(city.getKey());
            dto.setName(city.getValue());
            dto.setAveAccuracy(avgAccuracy.divide(new BigDecimal(datas.size()), 4));
            accuracyDTOList.add(dto);
        }

        return accuracyDTOList;
    }
    /**
     *  返回区间内月准确率列表
     * @param cityName
     * @param cityId
     * @param caliberId
     * @param startYM
     * @param endYM
     * @return
     * @throws Exception
     */
    @Override
    public List<SingleAccuraryDTO> getStatisticsAccuracyLoadCityMonthHisDOs(String cityName, String cityId, String caliberId, String startYM, String endYM) throws Exception {

        List<SingleAccuraryDTO> singleAccuraryDTOList = new ArrayList<>();
        List<StatisticsAccuracyLoadCityMonthHisDO> datas = statisticsAccuracyLoadCityMonthHisDAO.getStatisticsAccuracyLoadCityMonthHisDOs(cityId, caliberId, startYM, endYM);
        //数据为空时直接返回空列表
        if(CollectionUtils.isEmpty(datas)){
            return singleAccuraryDTOList;
        }

        for(StatisticsAccuracyLoadCityMonthHisDO hisDO :datas){
            SingleAccuraryDTO singleAccuraryDTO = new SingleAccuraryDTO();
            singleAccuraryDTO.setDatetime(hisDO.getYear() + "-" + hisDO.getMonth());
            singleAccuraryDTO.setAccuracy(hisDO.getAccuracy());
            singleAccuraryDTO.setCityName(cityName);
            singleAccuraryDTOList.add(singleAccuraryDTO);
        }
        return singleAccuraryDTOList;
    }
    /**
     *  统计城市月准确率
     * @param cityVOS
     * @param caliberId
     * @param startYM
     * @param endYM
     * @return
     * @throws Exception
     */
    @Override
    public List<StatisticsAccuracyLoadCityMonthHisDO> getReportMonthAccuracy(List<CityDO> cityVOS, String caliberId, String startYM, String endYM) throws Exception{

        List<StatisticsAccuracyLoadCityMonthHisDO> loadCityMonthHisDOS= new ArrayList<>();
        List<String> monthBetween = getMonthBetween(startYM, endYM);
        for(String date : monthBetween) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date startDate = sdf.parse(date + "-01");
            Date endDate = sdf.parse(getLastDayOfMonth1(date.substring(0, 4), date.substring(5, 7)));
            for (CityDO city : cityVOS) {
                StatisticsAccuracyLoadCityMonthHisDO statisticsAccuracyLoadCityMonthHisDO = new StatisticsAccuracyLoadCityMonthHisDO();
                List<StatisticsCityDayFcDO> dayAccuracyList = statisticsCityDayFcService.getDayAccuracyList(city.getId(), caliberId, null, startDate, endDate,true);
                Map<String, List<Date>> passCheckMap = settingCheckService.findPassCheckMap(city.getId());
                List<Date> passCheckList = passCheckMap.get(city.getId());
                if(CollectionUtils.isEmpty(dayAccuracyList)){
                    continue;
                }

                BigDecimal avgAccuracy = getAvgAccuracy(dayAccuracyList, passCheckList);
                statisticsAccuracyLoadCityMonthHisDO.setAccuracy(avgAccuracy);
                statisticsAccuracyLoadCityMonthHisDO.setCaliberId(caliberId);
                statisticsAccuracyLoadCityMonthHisDO.setCityId(city.getId());
                statisticsAccuracyLoadCityMonthHisDO.setMonth(date.substring(5, 7));
                statisticsAccuracyLoadCityMonthHisDO.setYear(date.substring(0, 4));
                loadCityMonthHisDOS.add(statisticsAccuracyLoadCityMonthHisDO);
            }
        }

        return loadCityMonthHisDOS;
    }
    private BigDecimal getAvgAccuracy(List<StatisticsCityDayFcDO> dayAccuracyList, List<Date> passCheckList){
        BigDecimal avgAccuracy = new BigDecimal("0.0000");
        if(CollectionUtils.isEmpty(dayAccuracyList)){
            return avgAccuracy;
        }

        Iterator<StatisticsCityDayFcDO> iterator = dayAccuracyList.iterator();
        while(iterator.hasNext()) {
            StatisticsCityDayFcDO statisticsCityDayFcDO = iterator.next();
            if(!CollectionUtils.isEmpty(passCheckList) && passCheckList.contains(statisticsCityDayFcDO.getDate())){
                iterator.remove();
                continue;
            }
            avgAccuracy = avgAccuracy.add(statisticsCityDayFcDO.getAccuracy());
        }

        return avgAccuracy.divide(new BigDecimal(dayAccuracyList.size()), 4);
    }
    /**
     *  获取两月之间月份
     * @param minDate
     * @param maxDate
     * @return
     * @throws ParseException
     */
    private List<String> getMonthBetween(String minDate, String maxDate) {

        ArrayList<String> result = new ArrayList<String>();
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");//格式化为年月

            Calendar min = Calendar.getInstance();
            Calendar max = Calendar.getInstance();

            min.setTime(sdf.parse(minDate));
            min.set(min.get(Calendar.YEAR), min.get(Calendar.MONTH), 1);

            max.setTime(sdf.parse(maxDate));
            max.set(max.get(Calendar.YEAR), max.get(Calendar.MONTH), 2);

            Calendar curr = min;
            while (curr.before(max)) {
                result.add(sdf.format(curr.getTime()));
                curr.add(Calendar.MONTH, 1);
            }
        }catch (ParseException e){
            e.printStackTrace();
        }

        return result;
    }
    /**
     * 获取指定年月的最后一天
     * @param year
     * @param month
     * @return
     */
    private String getLastDayOfMonth1(String year, String month) {
        Calendar cal = Calendar.getInstance();
        //设置年份
        cal.set(Calendar.YEAR, Integer.valueOf(year));
        //设置月份
        cal.set(Calendar.MONTH, Integer.valueOf(month)-1);
        //获取某月最大天数
        int lastDay = cal.getActualMaximum(Calendar.DATE);
        //设置日历中月份的最大天数
        cal.set(Calendar.DAY_OF_MONTH, lastDay);
        //格式化日期
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(cal.getTime());
    }

    @Override
    public void doSaveOrUpdate(List<StatisticsAccuracyLoadCityMonthHisDO> AccuracyMonthDOS) throws Exception {
        if (CollectionUtils.isEmpty(AccuracyMonthDOS) || AccuracyMonthDOS.size() < 1) {
            return;
        }
        for (StatisticsAccuracyLoadCityMonthHisDO accuracyMonthVO : AccuracyMonthDOS) {
            List<StatisticsAccuracyLoadCityMonthHisDO> month = statisticsAccuracyLoadCityMonthHisDAO
                .findAccuracyMonth(accuracyMonthVO.getYear(), accuracyMonthVO.getMonth(),
                    accuracyMonthVO.getCityId(),accuracyMonthVO.getCaliberId());
            if (!CollectionUtils.isEmpty(month) && month.size() > 0) {
                StatisticsAccuracyLoadCityMonthHisDO monthVO = month.get(0);
                BeanUtils.copyProperties(accuracyMonthVO, monthVO, "id");
                monthVO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
                statisticsAccuracyLoadCityMonthHisDAO.updateAndFlush(monthVO);
            } else {
                statisticsAccuracyLoadCityMonthHisDAO.createAndFlush(accuracyMonthVO);
            }
        }
    }
}