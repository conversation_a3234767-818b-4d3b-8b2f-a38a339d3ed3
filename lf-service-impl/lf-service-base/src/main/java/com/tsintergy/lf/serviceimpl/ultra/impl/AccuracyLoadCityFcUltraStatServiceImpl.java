package com.tsintergy.lf.serviceimpl.ultra.impl;

import com.tsieframework.core.base.dao.type.DataPointListMeta;
import com.tsieframework.core.base.dao.type.hibernate.DeltaUnit;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.util.compatible.BigDecimalUtils;
import com.tsintergy.aif.tool.core.utils.date.DateUtil;
import com.tsintergy.lf.serviceapi.base.ultra.load.pojo.LoadCityFcUltraDO;
import com.tsintergy.lf.serviceapi.base.ultra.load.api.LoadCityFcUltraService;
import com.tsintergy.lf.serviceapi.base.ultra.api.AccuracyLoadCityFcUltraService;
import com.tsintergy.lf.serviceapi.base.ultra.api.AccuracyLoadCityFcUltraStatService;
import com.tsintergy.lf.serviceapi.base.ultra.api.LoadCityHisUltraBasicService;
import com.tsintergy.lf.serviceapi.base.ultra.pojo.AccuracyLoadCityFcUltraDO;
import com.tsintergy.lf.serviceapi.base.ultra.pojo.LoadCityHisUltraBasicDO;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * @Description
 * @Date 2023/2/1 10:52
 * <AUTHOR>
 **/
@Service
public class AccuracyLoadCityFcUltraStatServiceImpl implements AccuracyLoadCityFcUltraStatService {

    /** Logger **/
    private static final Logger logger = LoggerFactory.getLogger(AccuracyLoadCityFcUltraStatServiceImpl.class);

    @Autowired
    LoadCityHisUltraBasicService loadCityHisUltraBasicService;

    @Autowired
    LoadCityFcUltraService loadCityFcUltraService;

    @Autowired
    AccuracyLoadCityFcUltraService accuracyLoadCityFcUltraService;

    /**
     * 计算并入库超短期多点准确率-市调公式；横表
     */
    @Override
    public void doStatUltraAccuracyLoadCityFcDOList(String cityId, String caliberId, Date startDate, Date endDate, Integer delta) throws Exception {
       try {
           List<LoadCityHisUltraBasicDO> hisDOS = loadCityHisUltraBasicService.getLoadCityHisDOS(cityId, caliberId, startDate, endDate, delta);

           List<LoadCityFcUltraDO> loadCityFc = loadCityFcUltraService.findLoadCityFcD0(cityId, caliberId, null, startDate, endDate, null, delta);
           List<AccuracyLoadCityFcUltraDO> accuracyLoadCityFcDOS = this.calculateAccuracy(hisDOS, loadCityFc);
          if(!CollectionUtils.isEmpty(accuracyLoadCityFcDOS)){
              accuracyLoadCityFcUltraService.doSaveOrUpdateList(accuracyLoadCityFcDOS);
          }
       }catch (Exception e){
           e.printStackTrace();
       }
    }

    /**
     * 计算288超短期的预测准确率
     *
     * @param loadCityHisDOS 历史数据
     * @param loadCityFcDOS  预测数据
     * @return
     */
    public List<AccuracyLoadCityFcUltraDO> calculateAccuracy(List<LoadCityHisUltraBasicDO> loadCityHisDOS, List<LoadCityFcUltraDO> loadCityFcDOS) throws Exception {

        Map<String, LoadCityHisUltraBasicDO> loadCityHisVOMap = new HashMap<>();
        for (LoadCityHisUltraBasicDO loadCityHisDO : loadCityHisDOS) {
            String key = loadCityHisDO.getCityId() + "-" + loadCityHisDO.getCaliberId() + "-" + loadCityHisDO.getDateTime().getTime();
            loadCityHisVOMap.put(key, loadCityHisDO);
        }

        Map<String, List<LoadCityFcUltraDO>> loadCityFcVOMap = new HashMap<>();
        for (LoadCityFcUltraDO loadCityFcDO : loadCityFcDOS) {
            String key = loadCityFcDO.getCityId() + "-" + loadCityFcDO.getCaliberId() + "-" + loadCityFcDO.getDateTime().getTime();
            loadCityFcVOMap.computeIfAbsent(key, k -> new ArrayList<>());
            loadCityFcVOMap.get(key).add(loadCityFcDO);
        }

        List<AccuracyLoadCityFcUltraDO> accuracyLoadCityFcDOS = new ArrayList<>();
        for (String key : loadCityHisVOMap.keySet()) {
            if (loadCityFcVOMap.get(key) != null) {
                for (LoadCityFcUltraDO loadCityFcDO : loadCityFcVOMap.get(key)) {
                    try {
                        accuracyLoadCityFcDOS.add(this.calculateAccuracy(loadCityHisVOMap.get(key), loadCityFcDO));
                    } catch (Exception e) {
                        logger.error("统计准确率出错了", e);
                    }
                }
            }
        }

        return accuracyLoadCityFcDOS;
    }
    /**
     * 计算预测准确率
     *
     * @param loadCityHisVO 历史数据
     * @param loadCityFcDO  预测数据
     * @return
     */
    public AccuracyLoadCityFcUltraDO calculateAccuracy(LoadCityHisUltraBasicDO loadCityHisVO, LoadCityFcUltraDO loadCityFcDO) throws Exception {

        if (!loadCityHisVO.getCityId().equals(loadCityFcDO.getCityId())) {
            logger.error("历史数据和预测数据的城市不同，不可以计算");
            return null;
        }
        if (loadCityHisVO.getDateTime().getTime() != loadCityFcDO.getDateTime().getTime()) {
            logger.error("历史数据和预测数据的日期不同，不可以计算");
            return null;
        }

        List<BigDecimal> his = loadCityHisVO.getTvData();
        List<BigDecimal> fc = loadCityFcDO.getTvData();
        if (his.size() == fc.size()){
            List<BigDecimal> accList = new ArrayList<>();
            for (int i = 0; i < his.size(); i++) {
                accList.add(getAccuracy(his.get(i), fc.get(i)));
            }
            int delta = his.size() == DataPointListMeta.POINTS_96 ? DataPointListMeta.MINUTE_15 : DataPointListMeta.MINUTE_5;
            AccuracyLoadCityFcUltraDO accuracyLoadCityFcVO = new AccuracyLoadCityFcUltraDO();
            accuracyLoadCityFcVO.setAlgorithmId(loadCityFcDO.getAlgorithmId());
            accuracyLoadCityFcVO.setMultipointType(loadCityFcDO.getMultipointType());
            accuracyLoadCityFcVO.setCityId(loadCityFcDO.getCityId());
            accuracyLoadCityFcVO.setDateTime(loadCityFcDO.getDateTime());
            accuracyLoadCityFcVO.setCaliberId(loadCityFcDO.getCaliberId());
            accuracyLoadCityFcVO.setMultipointType(loadCityFcDO.getMultipointType());
            if (!Objects.isNull(loadCityFcDO.getReport())){
                accuracyLoadCityFcVO.setReport(loadCityFcDO.getReport().getId() == 1);
            }
            accuracyLoadCityFcVO.setTvData(accList);
            String dateStr = DateFormatUtils.format(loadCityFcDO.getDateTime(), DateFormatType.SIMPLE_DATE_FORMAT_STR.getValue()) + " 00:00:00";
            Date date = DateUtil.string2Date(dateStr, DateFormatType.DATE_FORMAT_STR);
            accuracyLoadCityFcVO.setTvMeta(DataPointListMeta.create(date, his.size(), delta, DeltaUnit.MINUTES));
            return accuracyLoadCityFcVO;
        }
        return null;
    }

    /**
     * 计算准确率
     *
     * @param his
     * @param fc
     * @return
     */
    public static BigDecimal getAccuracy(BigDecimal his, BigDecimal fc) {
        if (his == null || fc == null) {
            return null;
        }
        if (his.compareTo(BigDecimal.ZERO) == 0) {
            return new BigDecimal(1);
        }
        if (fc.compareTo(BigDecimal.ZERO) == 0) {
            return new BigDecimal(0);
        }
        return new BigDecimal(1).subtract(BigDecimalUtils.divide(his.subtract(fc).abs(), his, 4));
    }
}
