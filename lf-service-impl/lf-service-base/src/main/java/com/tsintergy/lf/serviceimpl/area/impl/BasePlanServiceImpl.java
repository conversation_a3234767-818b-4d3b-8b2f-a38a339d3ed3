/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.area.impl;

import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.serviceapi.base.area.api.BaseAreaService;
import com.tsintergy.lf.serviceapi.base.area.api.BasePlanService;
import com.tsintergy.lf.serviceapi.base.area.dto.BasePlanDTO;
import com.tsintergy.lf.serviceapi.base.area.pojo.BasePlanDO;
import com.tsintergy.lf.serviceimpl.area.dao.BaseAreaDAO;
import com.tsintergy.lf.serviceimpl.area.dao.BasePlanDAO;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/8/4 9:40
 * @Version: 1.0.0
 */
@Service
public class BasePlanServiceImpl extends BaseServiceImpl implements BasePlanService {


    @Autowired
    private BasePlanDAO basePlanDAO;

    @Override
    public List<BasePlanDTO> findAllPlans() {
        List<BasePlanDO> collect = findAllDO();
        List<BasePlanDTO> list = new ArrayList<>();
        for (BasePlanDO basePlanDO : collect) {
            BasePlanDTO basePlanDTO = new BasePlanDTO();
            basePlanDTO.setValid(basePlanDO.getValid());
            basePlanDTO.setId(basePlanDO.getId());
            basePlanDTO.setName(basePlanDO.getName());
            basePlanDTO.setFcMode(basePlanDO.getFcMode());
            list.add(basePlanDTO);
        }
        return list;
    }

    @Override
    public void findPlanByName(String name,String id) throws Exception {
        BasePlanDO basePlanDO = basePlanDAO.findVOByPk(id);
        BasePlanDO basePlanDO1 = basePlanDAO.selectByName(name);
        if (basePlanDO1!=null&&basePlanDO!=null&&!basePlanDO.getId().equals(basePlanDO1.getId()))
        {
            throw new Exception("当前存在同名方案");
        }
        return;
    }

    @Override
    public List<BasePlanDO> findAllDO() {
        Collection<BasePlanDO> all = basePlanDAO.findAll();
        List<BasePlanDO> collect = all.stream().collect(Collectors.toList());
        return collect;
    }

    @Override
    public List<String> findAreaIdsByPlanId(String planId) {
        BasePlanDO voByPk = basePlanDAO.findVOByPk(planId);
        String areaIds = voByPk.getAreaIds();
        String[] split = areaIds.split(",");
        List<String> list = Arrays.asList(split);
        return list;
    }

    @Override
    public BasePlanDO getDefaultPlan() {
        BasePlanDO defaultPlan = basePlanDAO.getDefaultPlan();
        return defaultPlan;
    }

    @Override
    public List<String> getDefaultAreaIds() {
        BasePlanDO defaultPlan = this.getDefaultPlan();
        String areaIds = defaultPlan.getAreaIds();
        String[] split = areaIds.split(Constants.SEPARATOR_PUNCTUATION);
        List<String> defaultAreaIds = Arrays.asList(split);
        return defaultAreaIds;
    }



    @Override
    public void doUpdate(String planId, String name, Integer valid, List<String> areaIds,Integer fcMode) {
        StringBuffer stringBuffer = new StringBuffer();
        if (areaIds!=null)
        {
            for (int i = 0; i < areaIds.size(); i++) {
                if (i == 0) {
                    stringBuffer.append(areaIds.get(i));
                } else {
                    stringBuffer.append("," + areaIds.get(i));
                }
            }
        }
        if (valid != null && valid.equals(1)) {
            BasePlanDO defaultPlan = getDefaultPlan();
            if (defaultPlan!=null){
                defaultPlan.setValid(0);
                defaultPlan.setUpdateTime(new Timestamp(System.currentTimeMillis()));
                basePlanDAO.saveOrUpdate(defaultPlan);
            }

            BasePlanDO voByPk = basePlanDAO.findVOByPk(planId);
            if (voByPk != null) {
                if (areaIds != null) {
                    voByPk.setAreaIds(stringBuffer.toString());
                }
                if (name != null) {
                    voByPk.setName(name);
                }
                if (valid != null) {
                    voByPk.setValid(valid);
                }
                if (fcMode!=null)
                {
                    voByPk.setFcMode(fcMode);
                }
            }
            basePlanDAO.saveOrUpdate(voByPk);
            return;
        }

        BasePlanDO basePlanDO = basePlanDAO.getByPk(planId);
        basePlanDO.setName(name);
        basePlanDO.setAreaIds(stringBuffer.toString());
        basePlanDO.setFcMode(fcMode);
        basePlanDO.setUpdateTime(new Timestamp(System.currentTimeMillis()));
        basePlanDO.setDescription(name);
        basePlanDAO.saveOrUpdate(basePlanDO);


    }

    @Override
    public void doInsert(String planName, List<String> areaIds,Integer fcMode) {
        StringBuffer stringBuffer = new StringBuffer();
        for (int i = 0; i < areaIds.size(); i++) {
            if (i == 0) {
                stringBuffer.append(areaIds.get(i));
            } else {
                stringBuffer.append("," + areaIds.get(i));
            }
        }
        BasePlanDO basePlanDO = new BasePlanDO();
        basePlanDO.setAreaIds(stringBuffer.toString());
        basePlanDO.setValid(0);
        basePlanDO.setFcMode(fcMode);
        basePlanDO.setName(planName);
        basePlanDO.setDescription(planName);
        basePlanDO.setCreateTime(new Timestamp(System.currentTimeMillis()));
        basePlanDO.setUpdateTime(new Timestamp(System.currentTimeMillis()));
        basePlanDAO.save(basePlanDO);
    }

    @Override
    public void doDelete(String planId) {
        basePlanDAO.deleteByPk(planId);
    }
}