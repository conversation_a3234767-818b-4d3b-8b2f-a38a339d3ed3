package com.tsintergy.lf.serviceimpl.bgd.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsieframework.core.base.service.BaseFacadeServiceImpl;
import com.tsintergy.lf.serviceapi.base.bgd.api.WeatherLongSituationService;
import com.tsintergy.lf.serviceapi.base.bgd.pojo.WeatherLongSituationDO;
import com.tsintergy.lf.serviceimpl.bgd.dao.WeatherLongSituationDAO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service("weatherLongSituationService")
public class WeatherLongSituationServiceImpl extends BaseFacadeServiceImpl implements WeatherLongSituationService {

    @Autowired
    private WeatherLongSituationDAO weatherLongSituationDAO;

    @Override
    public WeatherLongSituationDO getWeatherLongSituationDO(String cityId, String year, String month) {
        return weatherLongSituationDAO.findOne(JpaWrappers.<WeatherLongSituationDO>lambdaQuery()
        .eq(WeatherLongSituationDO::getYear,year)
        .eq(WeatherLongSituationDO::getCityId,cityId)
        .eq(WeatherLongSituationDO::getMonth,month));
    }
}
