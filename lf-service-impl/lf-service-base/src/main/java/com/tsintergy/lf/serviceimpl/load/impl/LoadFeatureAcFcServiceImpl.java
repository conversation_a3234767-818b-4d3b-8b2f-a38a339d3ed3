/**
 * Copyright (C), 2015‐2022, 北京清能互联科技有限公司 Author:  <EMAIL> Date:  2022/5/26 15:08 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceimpl.load.impl;

import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.serviceapi.base.airconditioner.api.LoadAcFcBasicService;
import com.tsintergy.lf.serviceapi.base.airconditioner.pojo.LoadAcFcBasicDO;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcService;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureAcFcService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureAcFcDO;
import com.tsintergy.lf.serviceimpl.load.dao.LoadFeatureAcFcDAO;
import com.tsintergy.lf.serviceimpl.system.dao.SettingSystemDAO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * Description: 空调预测负荷特性 <br>
 *
 * <AUTHOR>
 * @create 2022/5/26
 * @since 1.0.0
 */
@Service("loadFeatureAcFcService")
@Slf4j
public class LoadFeatureAcFcServiceImpl implements LoadFeatureAcFcService {

    @Autowired
    private LoadAcFcBasicService loadAcFcBasicService;

    @Autowired
    private LoadFeatureAcFcDAO loadFeatureAcFcDAO;

    @Autowired
    private SettingSystemDAO settingSystemDAO;

    @Autowired
    private LoadCityFcService loadCityFcService;

    @Override
    public void doFcLoadFeatureCityDay(Date startDate, Date endDate) throws Exception {
        List<LoadAcFcBasicDO> loadAcFcBasicDOS = loadAcFcBasicService.getloadAcFcBasicDOList(null, null, startDate,
                endDate);
        if (CollectionUtils.isNotEmpty(loadAcFcBasicDOS)) {
            List<LoadCityFcDO> loadCityFcDOS = loadCityFcService.findReportLoadFc(null, null, startDate, endDate);

            // 预测负荷list to map
            Map<String, LoadCityFcDO> loadCityFcVOMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(loadCityFcDOS)) {
                for (LoadCityFcDO loadCityFcDO : loadCityFcDOS) {
                    String key =
                            loadCityFcDO.getCityId() + "-" + loadCityFcDO.getCaliberId()
                                    + "-" + loadCityFcDO.getDate().getTime();
                    loadCityFcVOMap.put(key, loadCityFcDO);
                }
            }
            for (LoadAcFcBasicDO loadAcFcBasicDO : loadAcFcBasicDOS) {
                String cityId = loadAcFcBasicDO.getCityId();
                String caliberId = loadAcFcBasicDO.getCaliberId();
                String algorithmId = loadAcFcBasicDO.getAlgorithmId();
                java.sql.Date date = loadAcFcBasicDO.getDate();

                String key = cityId + "-" + caliberId + "-" + date.getTime();
                LoadCityFcDO loadCityFcDO = loadCityFcVOMap.get(key);

                LoadFeatureAcFcDO loadFeatureAcFcDO = this.findStatisticsDayFeature(loadAcFcBasicDO, loadCityFcDO);
                List<LoadFeatureAcFcDO> loadFeatureCityDayFcDO = loadFeatureAcFcDAO.getLoadFeatureCityDayFcDO(
                        cityId, date, date, caliberId, algorithmId);
                if (CollectionUtils.isNotEmpty(loadFeatureCityDayFcDO)) {
                    LoadFeatureAcFcDO loadFeatureAcFcDO1 = loadFeatureCityDayFcDO.get(0);
                    if (loadFeatureAcFcDO1 != null && StringUtils.isNotEmpty(loadFeatureAcFcDO1.getId())) {
                        loadFeatureAcFcDAO.delete(loadFeatureAcFcDO1);
                    }
                }
                loadFeatureAcFcDAO.saveOrUpdate(loadFeatureAcFcDO);
            }
        }
    }

    private LoadFeatureAcFcDO findStatisticsDayFeature(LoadAcFcBasicDO loadAcFcBasicDO, LoadCityFcDO loadCityFcDO)
            throws Exception {
        if (loadAcFcBasicDO == null) {
            return null;
        }
        List<BigDecimal> peaks = new ArrayList<>();
        List<String> peakSectionTimes = settingSystemDAO.getPeakSectionTime();
        // 最小负荷发生时刻
        String minTime = null;
        // 最大负荷发生时刻
        String maxTime = null;
        Map<String, BigDecimal> loadMap = BasePeriodUtils.toMap(loadAcFcBasicDO, Constants.LOAD_CURVE_POINT_NUM,
                Constants.LOAD_CURVE_START_WITH_ZERO);
        List<BigDecimal> loadList = BasePeriodUtils.toList(loadAcFcBasicDO, Constants.LOAD_CURVE_POINT_NUM,
                Constants.LOAD_CURVE_START_WITH_ZERO);
        Map<String, BigDecimal> maxMixAvg = BasePeriodUtils.getMaxMinAvg(loadList, 4);
        for (String column : loadMap.keySet()) {
            BigDecimal load = loadMap.get(column);
            if (null != load) {
                column = column.substring(1);
                if (load.compareTo(maxMixAvg.get("max")) == 0) {
                    maxTime = column;
                }
                if (peakSectionTimes != null && peakSectionTimes.contains(column)) {
                    peaks.add(load);
                }
                if (load.compareTo(maxMixAvg.get("min")) == 0) {
                    minTime = column;
                }
            }
        }
        LoadFeatureAcFcDO result = new LoadFeatureAcFcDO();
        result.setDate(loadAcFcBasicDO.getDate());
        result.setCityId(loadAcFcBasicDO.getCityId());
        result.setCaliberId(loadAcFcBasicDO.getCaliberId());
        result.setAlgorithmId(loadAcFcBasicDO.getAlgorithmId());
//        result.setAlgorithmName(null);
        result.setMaxLoad(maxMixAvg.get("max"));
        result.setMinLoad(maxMixAvg.get("min"));
        result.setAvgLoad(maxMixAvg.get("avg"));
        if (maxTime != null) {
            result.setMaxTime(new StringBuffer(maxTime).insert(2, ":").toString());
        }
        if (minTime != null) {
            result.setMinTime(new StringBuffer(minTime).insert(2, ":").toString());
        }
//        // 峰谷差 = 日最大负荷 – 日最小负荷
//        result.setDifferent(BigDecimalUtils.sub(result.getMaxLoad(), result.getMinLoad()));
//        // 峰谷差率 = （日最大负荷 – 日最小负荷）/日最大负荷
//        if (result.getMaxLoad().compareTo(BigDecimal.ZERO) != 0) {
//            result.setGradient(BigDecimalUtils.divide(result.getDifferent(), result.getMaxLoad(), 4));
//        }
        // 积分电量 = 96点负荷之和/4
//        result.setIntegralLoad(BigDecimalUtils.divide(BigDecimalUtils.addAllValue(loadList), new BigDecimal(4), 4));
//        // 段峰电量 = 尖峰时段在数据库setting_system_init中，默认值为08：00~22:00
//        result.setPeakSectionLoad(BigDecimalUtils.divide(BigDecimalUtils.addAllValue(peaks), new BigDecimal(4), 4));
        String time = result.getMaxTime() == null ? "" : result.getMaxTime().replace(":", "");
        BigDecimal value = null;
        try {
            Method m = loadCityFcDO.getClass().getMethod("getT" + time);
            value = (BigDecimal) m.invoke(loadCityFcDO);
        } catch (NoSuchMethodException e) {
            log.error("空调负荷特性定时：无法获取反射属性：【{}】", e);
        } catch (NullPointerException e) {
            log.error("空调负荷特性定时：无法获取反射属性：【{}】", e);
        }

        if (value != null && value.compareTo(BigDecimal.ZERO) != 0) {
            // 最大空调负荷占比=（空调负荷最大值/空调负荷最大值对应时刻的总负荷）*100%
            result.setMaxLoadProportion(result.getMaxLoad().divide(value, 4, RoundingMode.HALF_UP));
        }
        // 累积电量=平均负荷×24
        result.setEnergy(result.getAvgLoad() == null ? null : result.getAvgLoad().multiply(new BigDecimal(24)));
        return result;
    }
}
