
package com.tsintergy.lf.serviceimpl.weather.impl;

import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityHisMeteoService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityDayHisMeteoService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayHisMeteoDO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherFeatureCityDayHisMeteoDAO;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version $Id: WeatherFeatureCityDayHisServiceImpl.java, v 0.1 2018-01-31 11:00:39 tao Exp $$
 */

@Service("weatherFeatureCityDayHisMeteoService")
@Transactional
public class WeatherFeatureCityDayHisMeteoServiceImpl extends BaseServiceImpl implements WeatherFeatureCityDayHisMeteoService {

    private static final Logger logger = LogManager.getLogger(WeatherFeatureCityDayHisMeteoServiceImpl.class);

    @Autowired
    CityService cityService;


    @Autowired
    WeatherCityHisMeteoService weatherCityHisService;

    @Autowired
    WeatherFeatureCityDayHisMeteoDAO weatherFeatureCityDayHisDAO;

    @Override
    public List<WeatherFeatureCityDayHisMeteoDO> findWeatherFeatureCityDayHisMeteoDO(List<String> cityIds, Date startDate,
                                                                           Date endDate) throws Exception {
        return weatherFeatureCityDayHisDAO.listWeatherFeatureCityDayHisMeteoDO(cityIds, startDate, endDate);
    }

    @Override
    public List<WeatherFeatureCityDayHisMeteoDO> findWeatherFeatureCityDayHisMeteoDO(String cityId, Date startDate, Date endDate)
            throws Exception {
        cityId = cityService.findWeatherCityId(cityId);
        return weatherFeatureCityDayHisDAO.getWeatherFeatureCityDayHisMeteoDOs(cityId, startDate, endDate);
    }


}

