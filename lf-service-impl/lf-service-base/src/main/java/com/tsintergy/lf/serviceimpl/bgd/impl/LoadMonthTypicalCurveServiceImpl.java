package com.tsintergy.lf.serviceimpl.bgd.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsintergy.lf.serviceapi.base.bgd.api.LoadMonthTypicalCurveService;
import com.tsintergy.lf.serviceapi.base.bgd.pojo.LoadCityMonthCurveFcBasicDO;
import com.tsintergy.lf.serviceimpl.bgd.dao.LoadMonthTypicalCurveServiceDAO;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * Description: 月度典型曲线
 *
 * <AUTHOR>
 * @create 2023-03-09
 * @since 1.0.0
 */
@Service("loadMonthTypicalCurveService")
public class LoadMonthTypicalCurveServiceImpl implements LoadMonthTypicalCurveService {

    @Autowired
    LoadMonthTypicalCurveServiceDAO loadMonthTypicalCurveServiceDAO;

    @Override
    public List<LoadCityMonthCurveFcBasicDO> queryMonthTypicalCurve(String cityId, String yearByDate, String monthByDate) {
        return loadMonthTypicalCurveServiceDAO.findAll(JpaWrappers.<LoadCityMonthCurveFcBasicDO>lambdaQuery()
            .eq(!StringUtils.isEmpty(yearByDate), LoadCityMonthCurveFcBasicDO::getYear, yearByDate)
            .eq(!StringUtils.isEmpty(monthByDate), LoadCityMonthCurveFcBasicDO::getMonth, monthByDate)
            .eq(!StringUtils.isEmpty(cityId), LoadCityMonthCurveFcBasicDO::getCityId, cityId));
    }
}
