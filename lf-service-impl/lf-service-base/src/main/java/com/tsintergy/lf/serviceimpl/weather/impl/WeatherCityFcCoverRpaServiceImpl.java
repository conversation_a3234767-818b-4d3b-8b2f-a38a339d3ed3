package com.tsintergy.lf.serviceimpl.weather.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcCoverRpaService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcRpaCoverDO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherCityFcCoverRpaDAO;
import java.util.List;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2024-12-04
 * @since 1.0.0
 */
@Service("weatherCityFcCoverRpaService")
public class WeatherCityFcCoverRpaServiceImpl implements WeatherCityFcCoverRpaService {

    @Autowired
    WeatherCityFcCoverRpaDAO weatherCityFcCoverRpaDAO;

    @Override
    public void doSaveOrUpdate(List<WeatherCityFcRpaCoverDO> weatherCityHisRpaDOList) {
        for (WeatherCityFcRpaCoverDO weatherCityHisRpaDO : weatherCityHisRpaDOList) {
            List<WeatherCityFcRpaCoverDO> all = weatherCityFcCoverRpaDAO.findAll(
                JpaWrappers.<WeatherCityFcRpaCoverDO>lambdaQuery()
                    .eq(WeatherCityFcRpaCoverDO::getCityId, weatherCityHisRpaDO.getCityId())
                    .eq(WeatherCityFcRpaCoverDO::getStationId, weatherCityHisRpaDO.getStationId())
                    .eq(WeatherCityFcRpaCoverDO::getType, weatherCityHisRpaDO.getType())
                    .eq(WeatherCityFcRpaCoverDO::getDate, weatherCityHisRpaDO.getDate()));
            if (CollectionUtils.isEmpty(all)) {
                weatherCityFcCoverRpaDAO.save(weatherCityHisRpaDO);
            } else {
                weatherCityHisRpaDO.setId(all.get(0).getId());
                weatherCityFcCoverRpaDAO.saveOrUpdateByTemplate(weatherCityHisRpaDO);
            }
        }
    }

    @Override
    public void doSave(List<WeatherCityFcRpaCoverDO> weatherCityFcRpaCoverDOList) {
        weatherCityFcCoverRpaDAO.saveOrUpdateBatchByTemplate(weatherCityFcRpaCoverDOList);
    }
}
