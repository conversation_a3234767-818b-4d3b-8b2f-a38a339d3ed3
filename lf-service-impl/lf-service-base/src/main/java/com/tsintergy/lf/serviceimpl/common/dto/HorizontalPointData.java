package com.tsintergy.lf.serviceimpl.common.dto;

import com.tsieframework.core.base.dao.BaseVO;
import lombok.Data;

/**
 *  excel横表96点数据实体对象
 *
 * @Version: 1.0.0
 * @Author: wa<PERSON><PERSON>@tsintergy.com
 * @Date: 2022/06/17 14:10
 */
@Data
public class HorizontalPointData extends BaseVO {

    /**
     * 功能描述:<br> 不识别excel横坐标，只顺序获取96个数据
     */
    private String t1;

    private String t2;

    private String t3;

    private String t4;

    private String t5;

    private String t6;

    private String t7;

    private String t8;

    private String t9;

    private String t10;

    private String t11;

    private String t12;

    private String t13;

    private String t14;

    private String t15;

    private String t16;

    private String t17;

    private String t18;

    private String t19;

    private String t20;

    private String t21;

    private String t22;

    private String t23;

    private String t24;

    private String t25;

    private String t26;

    private String t27;

    private String t28;

    private String t29;

    private String t30;

    private String t31;

    private String t32;

    private String t33;

    private String t34;

    private String t35;

    private String t36;

    private String t37;

    private String t38;

    private String t39;

    private String t40;

    private String t41;

    private String t42;

    private String t43;

    private String t44;

    private String t45;

    private String t46;

    private String t47;

    private String t48;

    private String t49;

    private String t50;

    private String t51;

    private String t52;

    private String t53;

    private String t54;

    private String t55;

    private String t56;

    private String t57;

    private String t58;

    private String t59;

    private String t60;

    private String t61;

    private String t62;

    private String t63;

    private String t64;

    private String t65;

    private String t66;

    private String t67;

    private String t68;

    private String t69;

    private String t70;

    private String t71;

    private String t72;

    private String t73;

    private String t74;

    private String t75;

    private String t76;

    private String t77;

    private String t78;

    private String t79;

    private String t80;

    private String t81;

    private String t82;

    private String t83;

    private String t84;

    private String t85;

    private String t86;

    private String t87;

    private String t88;

    private String t89;

    private String t90;

    private String t91;

    private String t92;

    private String t93;

    private String t94;

    private String t95;

    private String t96;


}
