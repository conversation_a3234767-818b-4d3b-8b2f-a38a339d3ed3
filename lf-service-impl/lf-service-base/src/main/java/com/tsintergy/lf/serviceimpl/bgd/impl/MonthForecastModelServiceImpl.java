/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.bgd.impl;

import com.alibaba.nacos.common.utils.MapUtils;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.math.BigDecimalFunctions;
import com.tsieframework.core.base.service.BaseFacadeServiceImpl;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.lf.core.constants.AlgorithmConstants;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.enums.MonthAlgorithmModelEnum;
import com.tsintergy.lf.core.enums.MonthAlgorithmModelEnums;
import com.tsintergy.lf.core.enums.TenDaysTypeEnum;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.core.util.LoadCalUtil;
import com.tsintergy.lf.serviceapi.base.bgd.api.LoadFeatureCityTenDaysFcService;
import com.tsintergy.lf.serviceapi.base.bgd.api.LoadFeatureCityTenDaysHisService;
import com.tsintergy.lf.serviceapi.base.bgd.api.LoadMonthTypicalCurveService;
import com.tsintergy.lf.serviceapi.base.bgd.api.MonthForecastModelService;
import com.tsintergy.lf.serviceapi.base.bgd.api.StatisticsCityTenDaysFcFeatureService;
import com.tsintergy.lf.serviceapi.base.bgd.dto.FeatureDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.MonthModelForecastDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.MonthReportDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.MonthTypicalCurveDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.MonthWeatherDefaultDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.MonthWeatherResultDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.MonthWeatherSettingDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.TenDaysOfMonthAccuracyDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.TenDaysOfMonthFeatureDTO;
import com.tsintergy.lf.serviceapi.base.bgd.pojo.LoadCityMonthCurveFcBasicDO;
import com.tsintergy.lf.serviceapi.base.bgd.pojo.LoadFeatureCityTenDaysFcServiceDO;
import com.tsintergy.lf.serviceapi.base.bgd.pojo.LoadFeatureCityTenDaysHisServiceDO;
import com.tsintergy.lf.serviceapi.base.bgd.pojo.StatisticsCityTenDaysFcFeatureServiceDO;
import com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityMonthFcService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityMonthHisService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCitySeasonFcDO;
import com.tsintergy.lf.serviceapi.base.system.api.ReportSystemService;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceapi.base.system.pojo.ReportSystemInitDO;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityDayHisService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityMonthMdHisService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityTenDaysHisService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityYearHisService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayHisDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityMonthMdHisDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityTenDaysHisDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityYearHisDO;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/11/17 18:44
 * @Version: 1.0.0
 */
@Service("monthForecastModelService")
public class MonthForecastModelServiceImpl extends BaseFacadeServiceImpl implements MonthForecastModelService {

    @Autowired
    private LoadFeatureCityTenDaysFcService loadFeatureCityTenDaysFcService;

    @Autowired
    private LoadFeatureCityTenDaysHisService loadFeatureCityTenDaysHisService;

    @Autowired
    private StatisticsCityTenDaysFcFeatureService statisticsCityTenDaysFcFeatureService;

    @Autowired
    private SettingSystemService settingSystemService;

    @Autowired
    private ReportSystemService reportSystemService;

    @Autowired
    LoadMonthTypicalCurveService loadMonthTypicalCurveService;

    @Autowired
    LoadFeatureCityMonthFcService loadFeatureCityMonthFcService;

    @Autowired
    LoadFeatureCityMonthHisService loadFeatureCityMonthHisService;

    @Autowired
    private WeatherFeatureCityDayHisService weatherFeatureCityDayHisService;

    @Autowired
    private WeatherFeatureCityTenDaysHisService weatherFeatureCityTenDaysHisService;

    @Autowired
    private WeatherFeatureCityMonthMdHisService weatherFeatureCityMonthMdHisService;

    @Autowired
    private WeatherFeatureCityYearHisService weatherFeatureCityYearHisService;

    @Override
    public List<TenDaysOfMonthFeatureDTO> getMonthLoadFc(String cityId, String caliberId, Date date,
        String algorithmId) {
        List<TenDaysOfMonthFeatureDTO> result = new ArrayList<>();
        String month = DateUtil.getMonthDate(date);
        String year = DateUtil.getYearByDate(date);
        String defaultAlgorithmId = settingSystemService.getTenDaysDefaultAlgorithmId();
        Map<String, LoadFeatureCityTenDaysFcServiceDO> tenDaysMap = loadFeatureCityTenDaysFcService
            .findLoadFeatureCityTenDaysFcServiceDOs(cityId, caliberId, null, year, month, null).stream().collect(
                Collectors.toMap(t -> t.getAlgorithmId()+Constants.SEPARATOR_BROKEN_LINE+t.getType(),t->t));
        String lastYear = DateUtil.getYearByDate(DateUtils.addYears(date, -1));
        Map<String, LoadFeatureCityTenDaysHisServiceDO> lastYearMap = loadFeatureCityTenDaysHisService
            .findLoadFeatureCityTenDaysHisServiceDOs(cityId, caliberId, lastYear, month, null).stream().collect(
                Collectors.toMap(t -> t.getType(), t -> t));

        for (MonthAlgorithmModelEnums monthAlgorithmModelEnums : MonthAlgorithmModelEnums.values()) {
            if (MonthAlgorithmModelEnums.SUBNET_SUM.getId().equals(monthAlgorithmModelEnums.getId())){
                continue;
            }
            if (algorithmId.equals(monthAlgorithmModelEnums.getId())) {
                String algorithmName = monthAlgorithmModelEnums.getName();
                String algorithmIds = monthAlgorithmModelEnums.getId();
                for (TenDaysTypeEnum tenDaysTypeEnum : TenDaysTypeEnum.values()) {
                    TenDaysOfMonthFeatureDTO tenDaysOfMonthFeatureDTO = new TenDaysOfMonthFeatureDTO();
                    tenDaysOfMonthFeatureDTO.setAlgorithmName(algorithmName);
                    tenDaysOfMonthFeatureDTO.setType(tenDaysTypeEnum.getName());
                    LoadFeatureCityTenDaysFcServiceDO loadFeatureCityTenDaysFcServiceDO = tenDaysMap
                        .get(algorithmIds + Constants.SEPARATOR_BROKEN_LINE + tenDaysTypeEnum.getName());
                    if (loadFeatureCityTenDaysFcServiceDO != null) {
                        tenDaysOfMonthFeatureDTO.setMaxLoad(loadFeatureCityTenDaysFcServiceDO.getMaxLoad());
                        tenDaysOfMonthFeatureDTO.setMinLoad(loadFeatureCityTenDaysFcServiceDO.getMinLoad());
                        tenDaysOfMonthFeatureDTO.setMonthEnergy(loadFeatureCityTenDaysFcServiceDO.getEnergy());
                        tenDaysOfMonthFeatureDTO.setNoonTimeLoad(loadFeatureCityTenDaysFcServiceDO.getDayUnbalance());
                        tenDaysOfMonthFeatureDTO.setExtremeMaxLoad(
                            loadFeatureCityTenDaysFcServiceDO.getExtremeMaxLoad());
                        tenDaysOfMonthFeatureDTO.setExtremeMinLoad(
                            loadFeatureCityTenDaysFcServiceDO.getExtremeMinLoad());
                        tenDaysOfMonthFeatureDTO.setExtremeNoonTimeLoad(
                            loadFeatureCityTenDaysFcServiceDO.getExtremeDayUnbalance());
                        tenDaysOfMonthFeatureDTO.setExtremeMonthEnergy(
                            loadFeatureCityTenDaysFcServiceDO.getExtremeEnergy());
                    }
                    LoadFeatureCityTenDaysHisServiceDO lastYearFeature = lastYearMap.get(tenDaysTypeEnum.getName());
                    if (lastYearFeature != null) {
                        tenDaysOfMonthFeatureDTO.setLastYearMaxLoad(lastYearFeature.getMaxLoad());
                        tenDaysOfMonthFeatureDTO.setLastYearMinLoad(lastYearFeature.getMinLoad());
                        tenDaysOfMonthFeatureDTO.setLastYearEnergy(lastYearFeature.getEnergy());
                        tenDaysOfMonthFeatureDTO.setLastYearNoonTimeLoad(lastYearFeature.getDayUnbalance());
                    }
                    result.add(tenDaysOfMonthFeatureDTO);
                }
            }
        }

//        List<LoadFeatureCityTenDaysFcServiceDO> fcServiceDOList = loadFeatureCityTenDaysFcService.findLoadFeatureCityTenDaysFcServiceDOs(cityId, caliberId, null, year, month, null);
//        if (CollectionUtils.isNotEmpty(fcServiceDOList)){
//            if (CollectionUtils.isNotEmpty(result) && result.get(0).getMaxLoad() == null) {
//                List<TenDaysOfMonthFeatureDTO> results = new ArrayList<>();
//                Map<String, List<LoadFeatureCityTenDaysFcServiceDO>> reportMap = fcServiceDOList.stream()
//                    .filter(t -> t.getReport() != null && Boolean.TRUE.equals(t.getReport()))
//                    .collect(Collectors.groupingBy(LoadFeatureCityTenDaysFcServiceDO::getType));
//                for (TenDaysTypeEnum tenDaysTypeEnum : TenDaysTypeEnum.values()){
//                    List<LoadFeatureCityTenDaysFcServiceDO> serviceDOList = reportMap.get(tenDaysTypeEnum.getName());
//                    if (CollectionUtils.isNotEmpty(serviceDOList)){
//                        LoadFeatureCityTenDaysFcServiceDO serviceDO = serviceDOList.get(0);
//                        TenDaysOfMonthFeatureDTO tenDaysOfMonthFeatureDTO = genTenDaysOfMonthFeatureDTO(serviceDO, lastYearMap, tenDaysTypeEnum.getName());
//                        results.add(tenDaysOfMonthFeatureDTO);
//                    }else {
//                        List<LoadFeatureCityTenDaysFcServiceDO> collect = fcServiceDOList
//                            .stream()
//                            .filter(t -> t.getAlgorithmId().equals(defaultAlgorithmId) && tenDaysTypeEnum.getType().equals(t.getType()))
//                            .collect(Collectors.toList());
//                        if (CollectionUtils.isNotEmpty(collect)){
//                            LoadFeatureCityTenDaysFcServiceDO serviceDO = collect.get(0);
//                            TenDaysOfMonthFeatureDTO tenDaysOfMonthFeatureDTO = genTenDaysOfMonthFeatureDTO(serviceDO, lastYearMap, tenDaysTypeEnum.getName());
//                            results.add(tenDaysOfMonthFeatureDTO);
//                        }
//                    }
//                }
//                return results;
//            }
//        }
        return result;
    }

    @Override
    public MonthTypicalCurveDTO getMonthTypicalCurve(String cityId, String caliberId, Date date) {
        String yearByDate = DateUtil.getYearByDate(date);
        String monthByDate = DateUtil.getMonthByDate(date).split("-")[1];
        // 查询所有日典型曲线负荷
        List<LoadCityMonthCurveFcBasicDO> loadCityMonthCurveFcBasicDOS = loadMonthTypicalCurveService.queryMonthTypicalCurve(
            cityId, yearByDate, monthByDate);
        Map<String, LoadCityMonthCurveFcBasicDO> collect = loadCityMonthCurveFcBasicDOS.stream()
            .collect(Collectors.toMap(t -> t.getType(), t -> t));
        MonthTypicalCurveDTO monthTypicalCurveDTO = new MonthTypicalCurveDTO();
        if (!MapUtils.isEmpty(collect)){
            Set<String> stringType = collect.keySet();
            for (String s : stringType) {
                List<BigDecimal> bigDecimals = BasePeriodUtils.toList(collect.get(s), Constants.LOAD_CURVE_POINT_NUM,
                    Constants.LOAD_CURVE_START_WITH_ZERO);
                if ("1".equals(s)) {
                    monthTypicalCurveDTO.setWorkdayList(bigDecimals);
                } else {
                    monthTypicalCurveDTO.setWeekendList(bigDecimals);
                }
            }
        }
        return monthTypicalCurveDTO;
    }

    @Override
    public List<TenDaysOfMonthFeatureDTO> getMonthLoadFcCompare(String cityId, String caliberId, Date date,
        String algorithmId) throws Exception {
        List<TenDaysOfMonthFeatureDTO> result = new ArrayList<>();
        List<TenDaysOfMonthFeatureDTO> resultList = new ArrayList<>();
        String month = DateUtil.getMonthDate(date);
        String year = DateUtil.getYearByDate(date);
        String defaultAlgorithmId = settingSystemService.getTenDaysDefaultAlgorithmId();
        Map<String, LoadFeatureCityTenDaysFcServiceDO> tenDaysMap = loadFeatureCityTenDaysFcService
            .findLoadFeatureCityTenDaysFcServiceDOs(cityId, caliberId, null, year, month, null).stream().collect(
                Collectors.toMap(t -> t.getAlgorithmId()+Constants.SEPARATOR_BROKEN_LINE+t.getType(),t->t));
        Map<String, LoadFeatureCityTenDaysHisServiceDO> yearHisMap = loadFeatureCityTenDaysHisService
            .findLoadFeatureCityTenDaysHisServiceDOs(cityId, caliberId, year, month, null).stream().collect(
                Collectors.toMap(t -> t.getType(), t -> t));
        // TODO
        List<StatisticsCityTenDaysFcFeatureServiceDO> statisticsCityTenDaysFcFeature = statisticsCityTenDaysFcFeatureService.findStatisticsCityTenDaysFcFeature(
            cityId, caliberId, algorithmId, year, month, null);
        for (MonthAlgorithmModelEnums monthAlgorithmModelEnums : MonthAlgorithmModelEnums.values()) {
            if (MonthAlgorithmModelEnums.SUBNET_SUM.getId().equals(monthAlgorithmModelEnums.getId())){
                continue;
            }
            if (algorithmId.equals(monthAlgorithmModelEnums.getId())) {
                String algorithmName = monthAlgorithmModelEnums.getName();
                String algorithmIds = monthAlgorithmModelEnums.getId();
                for (TenDaysTypeEnum tenDaysTypeEnum : TenDaysTypeEnum.values()) {
                    TenDaysOfMonthFeatureDTO tenDaysOfMonthFeatureDTO = new TenDaysOfMonthFeatureDTO();
                    tenDaysOfMonthFeatureDTO.setAlgorithmName(algorithmName);
                    tenDaysOfMonthFeatureDTO.setType(tenDaysTypeEnum.getName());
                    LoadFeatureCityTenDaysFcServiceDO loadFeatureCityTenDaysFcServiceDO = tenDaysMap
                        .get(algorithmIds + Constants.SEPARATOR_BROKEN_LINE + tenDaysTypeEnum.getName());
                    if (loadFeatureCityTenDaysFcServiceDO != null) {
                        tenDaysOfMonthFeatureDTO.setMaxLoad(loadFeatureCityTenDaysFcServiceDO.getMaxLoad());
                        tenDaysOfMonthFeatureDTO.setMinLoad(loadFeatureCityTenDaysFcServiceDO.getMinLoad());
                        tenDaysOfMonthFeatureDTO.setMonthEnergy(loadFeatureCityTenDaysFcServiceDO.getEnergy());
                        tenDaysOfMonthFeatureDTO.setNoonTimeLoad(loadFeatureCityTenDaysFcServiceDO.getDayUnbalance());
                    }
                    LoadFeatureCityTenDaysHisServiceDO yearHisFeature = yearHisMap.get(tenDaysTypeEnum.getName());
                    if (yearHisFeature != null) {
                        tenDaysOfMonthFeatureDTO.setLastYearMaxLoad(yearHisFeature.getMaxLoad());
                        tenDaysOfMonthFeatureDTO.setLastYearMinLoad(yearHisFeature.getMinLoad());
                        tenDaysOfMonthFeatureDTO.setLastYearEnergy(yearHisFeature.getEnergy());
                        tenDaysOfMonthFeatureDTO.setLastYearNoonTimeLoad(yearHisFeature.getDayUnbalance());
                    }
                    result.add(tenDaysOfMonthFeatureDTO);
                }
            }
        }

        List<LoadFeatureCityTenDaysFcServiceDO> fcServiceDOList = loadFeatureCityTenDaysFcService.findLoadFeatureCityTenDaysFcServiceDOs(cityId, caliberId, null, year, month, null);
        if (CollectionUtils.isNotEmpty(fcServiceDOList)){
            if (CollectionUtils.isNotEmpty(result) && result.get(0).getMaxLoad() == null) {
                List<TenDaysOfMonthFeatureDTO> results = new ArrayList<>();
                Map<String, List<LoadFeatureCityTenDaysFcServiceDO>> reportMap = fcServiceDOList.stream()
                    .filter(t -> t.getReport() != null && Boolean.TRUE.equals(t.getReport()))
                    .collect(Collectors.groupingBy(LoadFeatureCityTenDaysFcServiceDO::getType));
                for (TenDaysTypeEnum tenDaysTypeEnum : TenDaysTypeEnum.values()){
                    List<LoadFeatureCityTenDaysFcServiceDO> serviceDOList = reportMap.get(tenDaysTypeEnum.getName());
                    if (CollectionUtils.isNotEmpty(serviceDOList)){
                        LoadFeatureCityTenDaysFcServiceDO serviceDO = serviceDOList.get(0);
                        TenDaysOfMonthFeatureDTO tenDaysOfMonthFeatureDTO = genTenDaysOfMonthFeatureDTO(serviceDO, yearHisMap, tenDaysTypeEnum.getName());
                        if (!CollectionUtils.isEmpty(statisticsCityTenDaysFcFeature)) {
                            for (StatisticsCityTenDaysFcFeatureServiceDO statisticsCityTenDaysFcDO : statisticsCityTenDaysFcFeature) {
                                if (tenDaysTypeEnum.getName().equals(statisticsCityTenDaysFcDO.getType())) {
                                    tenDaysOfMonthFeatureDTO.setMaxLoadAccuracy(statisticsCityTenDaysFcDO.getMaxLoadAccuracy().abs());
                                    tenDaysOfMonthFeatureDTO.setMinLoadAccuracy(statisticsCityTenDaysFcDO.getMinLoadAccuracy().abs());
                                    tenDaysOfMonthFeatureDTO.setEnergyAccuracy(statisticsCityTenDaysFcDO.getEnergyAccuracy().abs());
                                }
                            }
                        }
                        results.add(tenDaysOfMonthFeatureDTO);
                    }else {
                        List<LoadFeatureCityTenDaysFcServiceDO> collect = fcServiceDOList
                            .stream()
                            .filter(t -> t.getAlgorithmId().equals(defaultAlgorithmId) && tenDaysTypeEnum.getType().equals(t.getType()))
                            .collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(collect)){
                            LoadFeatureCityTenDaysFcServiceDO serviceDO = collect.get(0);
                            TenDaysOfMonthFeatureDTO tenDaysOfMonthFeatureDTO = genTenDaysOfMonthFeatureDTO(serviceDO, yearHisMap, tenDaysTypeEnum.getName());
                            if (!CollectionUtils.isEmpty(statisticsCityTenDaysFcFeature)) {
                                for (StatisticsCityTenDaysFcFeatureServiceDO statisticsCityTenDaysFcDO : statisticsCityTenDaysFcFeature) {
                                    if (tenDaysTypeEnum.getName().equals(statisticsCityTenDaysFcDO.getType())) {
                                        tenDaysOfMonthFeatureDTO.setMaxLoadAccuracy(statisticsCityTenDaysFcDO.getMaxLoadAccuracy().abs());
                                        tenDaysOfMonthFeatureDTO.setMinLoadAccuracy(statisticsCityTenDaysFcDO.getMinLoadAccuracy().abs());
                                        tenDaysOfMonthFeatureDTO.setEnergyAccuracy(statisticsCityTenDaysFcDO.getEnergyAccuracy().abs());
                                    }
                                }
                            }
                            results.add(tenDaysOfMonthFeatureDTO);
                        }
                    }
                }
                return results;
            }
        }
        for (TenDaysOfMonthFeatureDTO tenDaysOfMonthFeatureDTO : result) {
            BigDecimal accuracy = LoadCalUtil.getAccuracy(tenDaysOfMonthFeatureDTO.getLastYearMaxLoad(),
                tenDaysOfMonthFeatureDTO.getMaxLoad());
            BigDecimal accuracy1 = LoadCalUtil.getAccuracy(tenDaysOfMonthFeatureDTO.getLastYearMinLoad(),
                tenDaysOfMonthFeatureDTO.getMinLoad());
            BigDecimal accuracy2 = LoadCalUtil.getAccuracy(tenDaysOfMonthFeatureDTO.getLastYearEnergy(),
                tenDaysOfMonthFeatureDTO.getMonthEnergy());
            BigDecimal accuracy3 = LoadCalUtil.getAccuracy(tenDaysOfMonthFeatureDTO.getLastYearNoonTimeLoad(),
                tenDaysOfMonthFeatureDTO.getNoonTimeLoad());
            tenDaysOfMonthFeatureDTO.setMaxLoadAccuracy(accuracy);
            tenDaysOfMonthFeatureDTO.setMinLoadAccuracy(accuracy1);
            tenDaysOfMonthFeatureDTO.setEnergyAccuracy(accuracy2);
            tenDaysOfMonthFeatureDTO.setNoonTimeLoadAccuracy(accuracy3);
        }
        return result;
    }

    @Override
    public List<TenDaysOfMonthAccuracyDTO> getHistoryAccuracy(String cityId, String caliberId, Date date,
                                                    Integer type) throws Exception{
        String yearByDate = DateUtil.getYearByDate(date);
        String monthByDate = DateUtil.getMonthByDate(date).split("-")[1];
        List<TenDaysOfMonthAccuracyDTO> result = new ArrayList<>();
        List<String> strings = new ArrayList<>();
        strings.add(monthByDate);
        // 查询预测值
        List<LoadFeatureCityTenDaysFcServiceDO> loadFeatureCityTenDaysFcServiceDOs = loadFeatureCityTenDaysFcService.findLoadFeatureCityTenDaysFcServiceDOs(
            cityId, caliberId, "", yearByDate, monthByDate, "");
        // 查询历史值
        List<LoadFeatureCityTenDaysHisServiceDO> loadFeatureCityTenDaysHisServiceDOs = loadFeatureCityTenDaysHisService.findLoadFeatureCityTenDaysHisServiceDOs(
            cityId, caliberId, yearByDate, monthByDate, "");

        Map<String, List<LoadFeatureCityTenDaysFcServiceDO>> loadFeatureFc = loadFeatureCityTenDaysFcServiceDOs.stream()
            .collect(Collectors.groupingBy(LoadFeatureCityTenDaysFcServiceDO::getAlgorithmId));

        MonthModelForecastDTO monthModelForecastList = new MonthModelForecastDTO();
        for (MonthAlgorithmModelEnums value : MonthAlgorithmModelEnums.values()) {
            if (value.equals(MonthAlgorithmModelEnums.SUBNET_SUM) || value.equals(MonthAlgorithmModelEnums.SENSITIVITY_FORECAST)) {
                continue;
            }
            if (!MapUtils.isEmpty(loadFeatureFc)) {
                Set<String> algorithmIds = loadFeatureFc.keySet();
                for (String algorithmId : algorithmIds) {
                    if (value.getId().equals(algorithmId)) {
                        LoadFeatureCityTenDaysFcServiceDO fcMax = null;
                        LoadFeatureCityTenDaysFcServiceDO fcMin = null;
                        LoadFeatureCityTenDaysFcServiceDO fcEnergy = null;
                        LoadFeatureCityTenDaysFcServiceDO fcNoontimeLoad = null;
                        LoadFeatureCityTenDaysHisServiceDO hisMaxLoad = null;
                        LoadFeatureCityTenDaysHisServiceDO hisMinLoad = null;
                        LoadFeatureCityTenDaysHisServiceDO hisMaxEnergy = null;
                        LoadFeatureCityTenDaysHisServiceDO hisNoontimeLoad = null;
                        Optional<LoadFeatureCityTenDaysFcServiceDO> max = loadFeatureFc.get(algorithmId).stream()
                            .max(Comparator.comparing(LoadFeatureCityTenDaysFcServiceDO::getMaxLoad));
                        if (max.isPresent()) {
                            fcMax = max.get();
                        }
                        Optional<LoadFeatureCityTenDaysFcServiceDO> maxs = loadFeatureFc.get(algorithmId).stream()
                            .min(Comparator.comparing(LoadFeatureCityTenDaysFcServiceDO::getDayUnbalance));
                        if (maxs.isPresent()) {
                            fcNoontimeLoad = maxs.get();
                        }
                        Optional<LoadFeatureCityTenDaysFcServiceDO> min = loadFeatureFc.get(algorithmId).stream()
                            .min(Comparator.comparing(LoadFeatureCityTenDaysFcServiceDO::getMinLoad));
                        if (min.isPresent()) {
                            fcMin = min.get();
                        }
                        BigDecimal reduceEnergy = loadFeatureFc.get(algorithmId).stream().map(LoadFeatureCityTenDaysFcServiceDO::getEnergy)
                            .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);

                        Optional<LoadFeatureCityTenDaysHisServiceDO> hisMax = loadFeatureCityTenDaysHisServiceDOs.stream()
                            .max(Comparator.comparing(LoadFeatureCityTenDaysHisServiceDO::getMaxLoad));
                        if (hisMax.isPresent()) {
                            hisMaxLoad = hisMax.get();
                        }
                        Optional<LoadFeatureCityTenDaysHisServiceDO> hisMaxs = loadFeatureCityTenDaysHisServiceDOs.stream()
                            .min(Comparator.comparing(LoadFeatureCityTenDaysHisServiceDO::getDayUnbalance));
                        if (hisMaxs.isPresent()) {
                            hisNoontimeLoad = hisMaxs.get();
                        }
                        Optional<LoadFeatureCityTenDaysHisServiceDO> hisMin = loadFeatureCityTenDaysHisServiceDOs.stream()
                            .min(Comparator.comparing(LoadFeatureCityTenDaysHisServiceDO::getMinLoad));
                        if (hisMin.isPresent()) {
                            hisMinLoad = hisMin.get();
                        }
                        BigDecimal reduceHisEnergy = loadFeatureCityTenDaysHisServiceDOs.stream()
                            .map(LoadFeatureCityTenDaysHisServiceDO::getEnergy).filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                        BigDecimal maxLoadAccuracy = LoadCalUtil.getAccuracy(Objects.nonNull(hisMaxLoad)?hisMaxLoad.getMaxLoad():null, Objects.nonNull(fcMax)?fcMax.getMaxLoad():null);
                        BigDecimal minLoadAccuracy = LoadCalUtil.getAccuracy(Objects.nonNull(hisMinLoad)?hisMinLoad.getMinLoad():null, Objects.nonNull(fcMin)?fcMin.getMinLoad():null);
                        BigDecimal NoontimeAccuracy = LoadCalUtil.getAccuracy(Objects.nonNull(hisNoontimeLoad)?hisNoontimeLoad.getDayUnbalance():null,
                            Objects.nonNull(fcNoontimeLoad)?fcNoontimeLoad.getDayUnbalance():null);
                        BigDecimal energyAccuracy = LoadCalUtil.getAccuracy(reduceHisEnergy, reduceEnergy);

                        TenDaysOfMonthAccuracyDTO tenDaysOfMonthAccuracyDTO = new TenDaysOfMonthAccuracyDTO();
                        tenDaysOfMonthAccuracyDTO.setAlgorithmName(value.getName());
                        tenDaysOfMonthAccuracyDTO.setMaxLoad(maxLoadAccuracy);
                        tenDaysOfMonthAccuracyDTO.setMinLoad(minLoadAccuracy);
                        tenDaysOfMonthAccuracyDTO.setMonthEnergy(energyAccuracy);
                        tenDaysOfMonthAccuracyDTO.setNoontimeLoad(NoontimeAccuracy);
                        result.add(tenDaysOfMonthAccuracyDTO);
                    }
                }
            }
        }
        return result;
    }

    @Override
    public List<FeatureDTO> getTenDayAccuracy(String cityId, Date date, Integer type, String algorithmId,String caliberId) {
        List<FeatureDTO> featureDTOList = new ArrayList<>(16);
        Date lastMonthDate = DateUtils.addMonths(date, -type);
        List<FeatureDTO> collect = new ArrayList<>(16);
        String year = DateUtil.getYearByDate(lastMonthDate);
        //当前月份
        String month = DateUtil.getMonthDate(lastMonthDate);
        //开始月份
        String startMonth = DateUtil.getMonthDate(DateUtils.addMonths(date, -type));
        String startYm = year + Constants.SEPARATOR_BROKEN_LINE + startMonth;
        String endYm = year + Constants.SEPARATOR_BROKEN_LINE + month;
        List<StatisticsCityTenDaysFcFeatureServiceDO> statisticsCityTenDaysFcFeatures = statisticsCityTenDaysFcFeatureService
            .findStatisticsCityTenDaysFcFeatures(cityId, caliberId, algorithmId, startYm, endYm);
        if (MonthAlgorithmModelEnums.REPORT.getId().equals(algorithmId) && CollectionUtils.isEmpty(statisticsCityTenDaysFcFeatures)){
            String tenDaysDefaultAlgorithmId = settingSystemService.getTenDaysDefaultAlgorithmId();
            statisticsCityTenDaysFcFeatures = statisticsCityTenDaysFcFeatureService
                .findStatisticsCityTenDaysFcFeatures(cityId, caliberId, tenDaysDefaultAlgorithmId, startYm, endYm);
        }
        if (CollectionUtils.isNotEmpty(statisticsCityTenDaysFcFeatures)) {
            Map<String, List<StatisticsCityTenDaysFcFeatureServiceDO>> accuracyMap = statisticsCityTenDaysFcFeatures.stream()
                .collect(Collectors.groupingBy(StatisticsCityTenDaysFcFeatureServiceDO::getType));
            List<BigDecimal> maxLoadList = new ArrayList<>(4);
            List<BigDecimal> minLoadList = new ArrayList<>(4);
            List<BigDecimal> energyLoadList = new ArrayList<>(4);
            for (TenDaysTypeEnum tenDaysTypeEnum : TenDaysTypeEnum.values()) {
                List<StatisticsCityTenDaysFcFeatureServiceDO> serviceDOList = accuracyMap.get(tenDaysTypeEnum.getName());
                if (CollectionUtils.isNotEmpty(serviceDOList)) {
                    FeatureDTO featureDTO = new FeatureDTO();
                    featureDTO.setType(tenDaysTypeEnum.getName());

                    Optional<StatisticsCityTenDaysFcFeatureServiceDO> maxLoad = serviceDOList
                        .stream()
                        .max(Comparator.comparing(StatisticsCityTenDaysFcFeatureServiceDO::getMaxLoadAccuracy));
                    maxLoad.ifPresent(statisticsCityTenDaysFcFeatureServiceDO -> featureDTO.setMaxLoad(statisticsCityTenDaysFcFeatureServiceDO.getMaxLoadAccuracy().multiply(new BigDecimal(100))));
                    maxLoad.ifPresent(statisticsCityTenDaysFcFeatureServiceDO -> maxLoadList.add(statisticsCityTenDaysFcFeatureServiceDO.getMaxLoadAccuracy()));
                    Optional<StatisticsCityTenDaysFcFeatureServiceDO> minLoad = serviceDOList
                        .stream()
                        .max(Comparator.comparing(StatisticsCityTenDaysFcFeatureServiceDO::getMinLoadAccuracy));
                    minLoad.ifPresent(statisticsCityTenDaysFcFeatureServiceDO -> featureDTO.setMinLoad(statisticsCityTenDaysFcFeatureServiceDO.getMinLoadAccuracy().multiply(new BigDecimal(100))));
                    minLoad.ifPresent(statisticsCityTenDaysFcFeatureServiceDO -> minLoadList.add(statisticsCityTenDaysFcFeatureServiceDO.getMinLoadAccuracy()));
                    Optional<StatisticsCityTenDaysFcFeatureServiceDO> energy = serviceDOList
                        .stream()
                        .max(Comparator.comparing(StatisticsCityTenDaysFcFeatureServiceDO::getEnergyAccuracy));
                    energy.ifPresent(statisticsCityTenDaysFcFeatureServiceDO -> featureDTO.setTenDayEnergy(statisticsCityTenDaysFcFeatureServiceDO.getEnergyAccuracy().multiply(new BigDecimal(100))));
                    energy.ifPresent(statisticsCityTenDaysFcFeatureServiceDO -> energyLoadList.add(statisticsCityTenDaysFcFeatureServiceDO.getEnergyAccuracy()));
                    featureDTOList.add(featureDTO);
                }
            }

            collect = featureDTOList
                .stream()
                .sorted(Comparator.comparing(FeatureDTO::getType))
                .collect(Collectors.toList());

            FeatureDTO featureDTO = new FeatureDTO();
            if (CollectionUtils.isNotEmpty(maxLoadList)) {
                featureDTO.setMaxLoad(BigDecimalFunctions.listAvg(maxLoadList).multiply(new BigDecimal(100)));
            }
            if (CollectionUtils.isNotEmpty(minLoadList)) {
                featureDTO.setMinLoad(BigDecimalFunctions.listAvg(minLoadList).multiply(new BigDecimal(100)));
            }
            if (CollectionUtils.isNotEmpty(energyLoadList)) {
                featureDTO.setTenDayEnergy(BigDecimalFunctions.listAvg(energyLoadList).multiply(new BigDecimal(100)));
            }
            featureDTO.setType("平均");
            collect.add(featureDTO);
        }
        return collect;
    }

    @Override
    public List<FeatureDTO> getReportList(String cityId, String caliberId, String algorithmName, Date date) {
        List<FeatureDTO> result = new ArrayList<>();
        String year = DateUtil.getYearByDate(date);
        String month = DateUtil.getMonthDate(date);
        for (MonthAlgorithmModelEnum monthAlgorithmModelEnums : MonthAlgorithmModelEnum.values()) {
            String algorithmNames = monthAlgorithmModelEnums.getName();
            if (algorithmNames.equals(algorithmName)) {
                String algorithmId = monthAlgorithmModelEnums.getId();
                Map<String, LoadFeatureCityTenDaysFcServiceDO> tenDaysFcServiceDOMap = loadFeatureCityTenDaysFcService
                    .findLoadFeatureCityTenDaysFcServiceDOs(cityId, caliberId, algorithmId, year, month, null).stream().collect(
                        Collectors.toMap(t -> t.getAlgorithmId() + Constants.SEPARATOR_BROKEN_LINE + t.getType(), t -> t));
                Map<String, LoadFeatureCityTenDaysFcServiceDO> collect = loadFeatureCityTenDaysFcService.getMonthReportVO(
                    cityId, caliberId, year, month).stream().collect(
                    Collectors.toMap(t -> t.getType(), t -> t));
                String lastYear = DateUtil.getYearByDate(DateUtils.addYears(date, -1));
                Map<String, LoadFeatureCityTenDaysHisServiceDO> lastYearMap = loadFeatureCityTenDaysHisService
                    .findLoadFeatureCityTenDaysHisServiceDOs(cityId, caliberId, lastYear, month, null).stream().collect(
                        Collectors.toMap(t -> t.getType(), t -> t));
                for (TenDaysTypeEnum tenDaysTypeEnum : TenDaysTypeEnum.values()) {
                    MonthModelForecastDTO monthModelForecastDTO = new MonthModelForecastDTO();
                    monthModelForecastDTO.setType(tenDaysTypeEnum.getName());
                    LoadFeatureCityTenDaysFcServiceDO algorithmVo = new LoadFeatureCityTenDaysFcServiceDO();
                    if (MapUtils.isNotEmpty(tenDaysFcServiceDOMap)) {
                        algorithmVo = tenDaysFcServiceDOMap
                            .get(algorithmId + Constants.SEPARATOR_BROKEN_LINE + tenDaysTypeEnum.getName());
                    } else {
                        algorithmVo = collect.get(tenDaysTypeEnum.getName());
                    }
                    FeatureDTO featureDTO = mergeFeature(algorithmVo);
                    featureDTO.setNoonTimeLoad(algorithmVo.getDayUnbalance());
                    featureDTO.setType(tenDaysTypeEnum.getName());
                    LoadFeatureCityTenDaysHisServiceDO lastYearFeature = lastYearMap.get(tenDaysTypeEnum.getName());
                    if (lastYearFeature != null) {
                        featureDTO.setLastYearMaxLoad(lastYearFeature.getMaxLoad());
                        featureDTO.setLastYearMonthEnergy(lastYearFeature.getEnergy());
                        featureDTO.setLastYearMinLoad(lastYearFeature.getMinLoad());
                        featureDTO.setLastYearNoonTimeLoad(lastYearFeature.getDayUnbalance());
                    }
                    result.add(featureDTO);
                }
            }
        }
        return result;
    }

    @Override
    public void report(List<MonthReportDTO> monthReportDTOS) {
        if (CollectionUtils.isEmpty(monthReportDTOS)){
            return ;
        }
        MonthReportDTO monthReportDTO = monthReportDTOS.get(0);
        Date date = DateUtil.getDate(monthReportDTO.getDate(),"yyyy-MM");
        String caliberId;
        String cityId = monthReportDTO.getCityId();
        if (Constants.PROVINCE_TYPE == Integer.parseInt(cityId)){
            caliberId =  Constants.CALIBER_ID_BG_QW;
        }else{
            caliberId = Constants.CALIBER_ID_BG_DS;
            //date =  DateUtils.addMonths(date, 1);
        }
//        Date nextMonthDate = DateUtils.addMonths(date, 1);
        String nextMonth = DateUtil.getMonthDate(date);
        String year = DateUtil.getYearByDate(date);

        Map<String, LoadFeatureCityTenDaysFcServiceDO> fcServiceDOMap = loadFeatureCityTenDaysFcService
            .getMonthReportVO(cityId, caliberId, year, nextMonth).stream()
            .collect(Collectors.toMap(t -> t.getType(), t -> t));

        for (MonthReportDTO reportDTO : monthReportDTOS) {
            LoadFeatureCityTenDaysFcServiceDO loadFeatureCityTenDaysFcServiceDO = fcServiceDOMap
                .get(reportDTO.getType());
            //先将之前上报取消
            if (loadFeatureCityTenDaysFcServiceDO != null){
                loadFeatureCityTenDaysFcServiceDO.setReport(false);
                loadFeatureCityTenDaysFcServiceDO.setDayUnbalance(reportDTO.getNoonTimeLoad());
                loadFeatureCityTenDaysFcService.doSaveOrUpdate(loadFeatureCityTenDaysFcServiceDO);
            }
            LoadFeatureCityTenDaysFcServiceDO reportDO = new LoadFeatureCityTenDaysFcServiceDO();
            reportDO.setAlgorithmId(AlgorithmConstants.MD_ALGORITHM_ID);
            reportDO.setReport(true);
            reportDO.setMaxLoad(reportDTO.getMaxLoad());
            reportDO.setMinLoad(reportDTO.getMinLoad());
            reportDO.setEnergy(reportDTO.getEnergy());
            reportDO.setDayUnbalance(reportDTO.getNoonTimeLoad());
            reportDO.setExtremeMaxLoad(reportDTO.getExtremeMaxLoad());
            reportDO.setExtremeMinLoad(reportDTO.getExtremeMinLoad());
            reportDO.setExtremeEnergy(reportDTO.getExtremeEnergy());
            reportDO.setExtremeDayUnbalance(reportDTO.getExtremeNoonTimeLoad());
            reportDO.setYear(year);
            reportDO.setMonth(nextMonth);
            reportDO.setCityId(cityId);
            reportDO.setCaliberId(caliberId);
            reportDO.setType(reportDTO.getType());
            reportDO.setReportTime(new Timestamp(System.currentTimeMillis()));
            loadFeatureCityTenDaysFcService.doSaveOrUpdate(reportDO);
        }
    }

    @Override
    public String getReportTime(String cityId, String caliberId, Date date) {
        Date nextMonth = DateUtils.addMonths(date, 0);
        String year = DateUtil.getYearByDate(nextMonth);
        String month = DateUtil.getMonthDate(nextMonth);
        //上个月的20号
        List<LoadFeatureCityTenDaysFcServiceDO> loadFeatureCityTenDaysFcServiceList = loadFeatureCityTenDaysFcService
            .getMonthReportVO(cityId, caliberId, year,month);
        if (CollectionUtils.isEmpty(loadFeatureCityTenDaysFcServiceList)){
            return "暂未上报";
        }

        List<LoadFeatureCityTenDaysFcServiceDO> collect = loadFeatureCityTenDaysFcServiceList.stream().filter(t->t.getReportTime()!=null)
            .sorted(Comparator.comparing(LoadFeatureCityTenDaysFcServiceDO::getReportTime).reversed()).collect(
                Collectors.toList());
        return CollectionUtils.isNotEmpty(collect)?DateUtil.getDateToStrFORMAT(collect.get(0).getReportTime(),"yyyy-MM-dd HH:mm"):"暂未上报";

    }

    @Override
    public String getForecastTime(String cityId, String caliberId, Date date, String algorithmId) {
        if ("0".equals(algorithmId)) {
            algorithmId = "16";
        }
        Date nextMonth = DateUtils.addMonths(date, 0);
        String year = DateUtil.getYearByDate(nextMonth);
        String month = DateUtil.getMonthDate(nextMonth);
        //上个月的20号
        List<LoadFeatureCityTenDaysFcServiceDO> loadFeatureCityTenDaysFcServiceList = loadFeatureCityTenDaysFcService
            .getMonthForecastVO(cityId, caliberId, year,month, algorithmId);
        if (CollectionUtils.isEmpty(loadFeatureCityTenDaysFcServiceList)){
            return "暂未预测";
        }

        List<LoadFeatureCityTenDaysFcServiceDO> collect = loadFeatureCityTenDaysFcServiceList.stream().filter(t->t.getUpdatetime()!=null)
            .sorted(Comparator.comparing(LoadFeatureCityTenDaysFcServiceDO::getUpdatetime).reversed()).collect(
                Collectors.toList());
        return CollectionUtils.isNotEmpty(collect)?DateUtil.getDateToStrFORMAT(collect.get(0).getUpdatetime(),"yyyy-MM-dd HH:mm"):"暂未预测";

    }

    @Override
    public String getSeasonForecastTime(String cityId, String caliberId, Date date, String algorithmId, String season) {
        if ("0".equals(algorithmId)) {
            algorithmId = "16";
        }
        Date nextMonth = DateUtils.addMonths(date, 0);
        String year = DateUtil.getYearByDate(nextMonth);
        String month = DateUtil.getMonthDate(nextMonth);
        String sMonth = com.tsintergy.lf.serviceimpl.common.util.DateUtils.getDateToStrFORMAT(date, "yyyy-MM")
            .split("-")[1];
        String sYear = com.tsintergy.lf.serviceimpl.common.util.DateUtils.getDateToStrFORMAT(date, "yyyy-MM")
            .split("-")[0];
        List<LoadFeatureCitySeasonFcDO> seasonForecastVO = new ArrayList<>();
        if ("1".equals(season)) {
            // 年度
            seasonForecastVO = loadFeatureCityTenDaysFcService
                .getSeasonForecastVO(cityId, caliberId, sYear, sMonth, algorithmId, "5");
        } else {
            // 冬夏
            if ("06".equals(sMonth)) {
                seasonForecastVO = loadFeatureCityTenDaysFcService
                    .getSeasonForecastVO(cityId, caliberId, sYear,sMonth, algorithmId, "2");
            } else {
                seasonForecastVO = loadFeatureCityTenDaysFcService
                    .getSeasonForecastVO(cityId, caliberId, sYear,sMonth, algorithmId, "4");
            }
        }
        /*List<LoadFeatureCityTenDaysFcServiceDO> loadFeatureCityTenDaysFcServiceList = loadFeatureCityTenDaysFcService
            .getMonthForecastVO(cityId, caliberId, year,month, algorithmId);*/
        if (CollectionUtils.isEmpty(seasonForecastVO)){
            return "暂未预测";
        }
        List<LoadFeatureCitySeasonFcDO> collect = seasonForecastVO.stream().filter(t->t.getUpdatetime()!=null)
            .sorted(Comparator.comparing(LoadFeatureCitySeasonFcDO::getUpdatetime).reversed()).collect(
                Collectors.toList());
        return CollectionUtils.isNotEmpty(collect) ? DateUtil.getDateToStrFORMAT(collect.get(0).getUpdatetime(),
            "yyyy-MM-dd HH:mm") : "暂未预测";
    }

    @Override
    public List<MonthWeatherResultDTO> getWeatherSetting(MonthWeatherSettingDTO monthWeatherSettingDTO)
        throws Exception {
        List<MonthWeatherResultDTO> result = new ArrayList<>();
        String cityId = monthWeatherSettingDTO.getCityId();
        Integer type = monthWeatherSettingDTO.getType();
        List<String> hisCity = monthWeatherSettingDTO.getHisYearDate();
        Date date = monthWeatherSettingDTO.getDate();
        String dateToStr = DateUtil.getDateToStrFORMAT(date, "yyyy-MM-dd");
        String sYear = dateToStr.split("-")[0];
        String sMonth = dateToStr.split("-")[1];
        Date startDate = null;
        Date endDate = null;
        List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisDOS = new ArrayList<>();
        List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisResult = new ArrayList<>();
        // 气象list
        List<BigDecimal> maxTem = new ArrayList<>();
        List<BigDecimal> minTem = new ArrayList<>();
        List<BigDecimal> aveTem = new ArrayList<>();
        List<BigDecimal> maxTem1 = new ArrayList<>();
        List<BigDecimal> minTem1 = new ArrayList<>();
        List<BigDecimal> aveTem1 = new ArrayList<>();
        List<BigDecimal> maxTem2 = new ArrayList<>();
        List<BigDecimal> minTem2 = new ArrayList<>();
        List<BigDecimal> aveTem2 = new ArrayList<>();
        for (String s : hisCity) {
            String dateStartStr = s + "-" + sMonth + "-01";
            startDate = DateUtil.getDate(dateStartStr,"yyyy-MM-dd");
            endDate = DateUtil.getLastDayOfMonth(startDate);
            weatherFeatureCityDayHisDOS = weatherFeatureCityDayHisService.listWeatherFeatureCityDayHisDO(
                cityId, startDate, endDate);
            if (CollectionUtils.isNotEmpty(weatherFeatureCityDayHisDOS)) {
                // 上旬
                List<WeatherFeatureCityDayHisDO> collect = weatherFeatureCityDayHisDOS.stream().limit(10)
                    .collect(Collectors.toList());
                BigDecimal highestTemperature = collect.stream().filter(t -> t.getHighestTemperature() != null)
                    .max(Comparator.comparing(WeatherFeatureCityDayHisDO::getHighestTemperature)).get()
                    .getHighestTemperature();
                maxTem.add(highestTemperature);
                BigDecimal lowestTemperature = collect.stream().filter(t -> t.getLowestTemperature() != null)
                    .max(Comparator.comparing(WeatherFeatureCityDayHisDO::getLowestTemperature)).get()
                    .getLowestTemperature();
                minTem.add(lowestTemperature);
                BigDecimal aveTemperature = collect.stream().filter(t -> t.getAveTemperature() != null)
                    .max(Comparator.comparing(WeatherFeatureCityDayHisDO::getAveTemperature)).get()
                    .getAveTemperature();
                aveTem.add(aveTemperature);
                weatherFeatureCityDayHisDOS.subList(0, 10).clear();
                // 中旬
                List<WeatherFeatureCityDayHisDO> collect1 = weatherFeatureCityDayHisDOS.stream().limit(10)
                    .collect(Collectors.toList());
                BigDecimal highestTemperature1 = collect1.stream().filter(t -> t.getHighestTemperature() != null)
                    .max(Comparator.comparing(WeatherFeatureCityDayHisDO::getHighestTemperature)).get()
                    .getHighestTemperature();
                maxTem1.add(highestTemperature1);
                BigDecimal lowestTemperature1 = collect1.stream().filter(t -> t.getLowestTemperature() != null)
                    .max(Comparator.comparing(WeatherFeatureCityDayHisDO::getLowestTemperature)).get()
                    .getLowestTemperature();
                minTem1.add(lowestTemperature1);
                BigDecimal aveTemperature1 = collect1.stream().filter(t -> t.getAveTemperature() != null)
                    .max(Comparator.comparing(WeatherFeatureCityDayHisDO::getAveTemperature)).get()
                    .getAveTemperature();
                aveTem1.add(aveTemperature1);
                weatherFeatureCityDayHisDOS.subList(0, 10).clear();
                // 下旬
                List<WeatherFeatureCityDayHisDO> collect2 = weatherFeatureCityDayHisDOS.stream().limit(10)
                    .collect(Collectors.toList());
                BigDecimal highestTemperature2 = collect2.stream().filter(t -> t.getHighestTemperature() != null)
                    .max(Comparator.comparing(WeatherFeatureCityDayHisDO::getHighestTemperature)).get()
                    .getHighestTemperature();
                maxTem2.add(highestTemperature2);
                BigDecimal lowestTemperature2 = collect2.stream().filter(t -> t.getLowestTemperature() != null)
                    .max(Comparator.comparing(WeatherFeatureCityDayHisDO::getLowestTemperature)).get()
                    .getLowestTemperature();
                minTem2.add(lowestTemperature2);
                BigDecimal aveTemperature2 = collect2.stream().filter(t -> t.getAveTemperature() != null)
                    .max(Comparator.comparing(WeatherFeatureCityDayHisDO::getAveTemperature)).get()
                    .getAveTemperature();
                aveTem2.add(aveTemperature2);
            }
        }
        if (type == 0) {
            for (TenDaysTypeEnum value : TenDaysTypeEnum.values()) {
                MonthWeatherResultDTO monthWeatherResultDTO = new MonthWeatherResultDTO();
                String dateStr = sYear + "-" +sMonth + value.getName();
                monthWeatherResultDTO.setDateTime(dateStr);
                if (value.getType().equals("1")) {
                    monthWeatherResultDTO.setMaxTem(BigDecimalUtils.listAvg(maxTem));
                    monthWeatherResultDTO.setMinTem(BigDecimalUtils.listAvg(minTem));
                    monthWeatherResultDTO.setAveTem(BigDecimalUtils.listAvg(aveTem));
                    monthWeatherResultDTO.setExtremeMaxTem(
                        BigDecimalUtils.listAvg(maxTem).compareTo(Constants.normalTem) > 0 ? BigDecimalUtils.listAvg(
                            maxTem).add(Constants.extremeTem)
                            : BigDecimalUtils.listAvg(maxTem).subtract(Constants.extremeTem));
                    monthWeatherResultDTO.setExtremeMinTem(
                        BigDecimalUtils.listAvg(maxTem).compareTo(Constants.normalTem) > 0 ? BigDecimalUtils.listAvg(
                            minTem).add(Constants.extremeTem)
                            : BigDecimalUtils.listAvg(minTem).subtract(Constants.extremeTem));
                    monthWeatherResultDTO.setExtremeAveTem(
                        BigDecimalUtils.listAvg(maxTem).compareTo(Constants.normalTem) > 0 ? BigDecimalUtils.listAvg(
                            aveTem).add(Constants.extremeTem)
                            : BigDecimalUtils.listAvg(aveTem).subtract(Constants.extremeTem));
                    monthWeatherResultDTO.setHisMaxTem(BigDecimalUtils.listAvg(maxTem));
                    monthWeatherResultDTO.setHisMinTem(BigDecimalUtils.listAvg(minTem));
                    monthWeatherResultDTO.setHisAveTem(BigDecimalUtils.listAvg(aveTem));
                } else if (value.getType().equals("2")) {
                    monthWeatherResultDTO.setMaxTem(BigDecimalUtils.listAvg(maxTem1));
                    monthWeatherResultDTO.setMinTem(BigDecimalUtils.listAvg(minTem1));
                    monthWeatherResultDTO.setAveTem(BigDecimalUtils.listAvg(aveTem1));
                    monthWeatherResultDTO.setExtremeMaxTem(
                        BigDecimalUtils.listAvg(maxTem1).compareTo(Constants.normalTem) > 0 ? BigDecimalUtils.listAvg(
                            maxTem1).add(Constants.extremeTem)
                            : BigDecimalUtils.listAvg(maxTem1).subtract(Constants.extremeTem));
                    monthWeatherResultDTO.setExtremeMinTem(
                        BigDecimalUtils.listAvg(maxTem1).compareTo(Constants.normalTem) > 0 ? BigDecimalUtils.listAvg(
                            minTem1).add(Constants.extremeTem)
                            : BigDecimalUtils.listAvg(minTem1).subtract(Constants.extremeTem));
                    monthWeatherResultDTO.setExtremeAveTem(
                        BigDecimalUtils.listAvg(maxTem1).compareTo(Constants.normalTem) > 0 ? BigDecimalUtils.listAvg(
                            aveTem1).add(Constants.extremeTem)
                            : BigDecimalUtils.listAvg(aveTem1).subtract(Constants.extremeTem));
                    monthWeatherResultDTO.setHisMaxTem(BigDecimalUtils.listAvg(maxTem1));
                    monthWeatherResultDTO.setHisMinTem(BigDecimalUtils.listAvg(minTem1));
                    monthWeatherResultDTO.setHisAveTem(BigDecimalUtils.listAvg(aveTem1));
                } else {
                    monthWeatherResultDTO.setMaxTem(BigDecimalUtils.listAvg(maxTem2));
                    monthWeatherResultDTO.setMinTem(BigDecimalUtils.listAvg(minTem2));
                    monthWeatherResultDTO.setAveTem(BigDecimalUtils.listAvg(aveTem2));
                    monthWeatherResultDTO.setExtremeMaxTem(
                        BigDecimalUtils.listAvg(maxTem2).compareTo(Constants.normalTem) > 0 ? BigDecimalUtils.listAvg(
                            maxTem2).add(Constants.extremeTem)
                            : BigDecimalUtils.listAvg(maxTem2).subtract(Constants.extremeTem));
                    monthWeatherResultDTO.setExtremeMinTem(
                        BigDecimalUtils.listAvg(maxTem2).compareTo(Constants.normalTem) > 0 ? BigDecimalUtils.listAvg(
                            minTem2).add(Constants.extremeTem)
                            : BigDecimalUtils.listAvg(minTem2).subtract(Constants.extremeTem));
                    monthWeatherResultDTO.setExtremeAveTem(
                        BigDecimalUtils.listAvg(maxTem2).compareTo(Constants.normalTem) > 0 ? BigDecimalUtils.listAvg(
                            aveTem2).add(Constants.extremeTem)
                            : BigDecimalUtils.listAvg(aveTem2).subtract(Constants.extremeTem));
                    monthWeatherResultDTO.setHisMaxTem(BigDecimalUtils.listAvg(maxTem2));
                    monthWeatherResultDTO.setHisMinTem(BigDecimalUtils.listAvg(minTem2));
                    monthWeatherResultDTO.setHisAveTem(BigDecimalUtils.listAvg(aveTem2));
                }
                result.add(monthWeatherResultDTO);
            }
        } else {
            // 直接从人工设置表中查
            List<WeatherFeatureCityTenDaysHisDO> weatherFeatureCityTenDaysDTO = weatherFeatureCityTenDaysHisService.findWeatherFeatureCityTenDaysDTO(
                    cityId, sYear, sMonth, null).stream()
                .filter(t -> t.getStatus() != null && Boolean.TRUE.equals(t.getStatus())).collect(Collectors.toList());
            for (TenDaysTypeEnum value : TenDaysTypeEnum.values()) {
                MonthWeatherResultDTO monthWeatherResultDTO = new MonthWeatherResultDTO();
                String dateStr = sYear + "-" +sMonth + value.getName();
                monthWeatherResultDTO.setDateTime(dateStr);
                if (value.getType().equals("1")) {
                    monthWeatherResultDTO.setHisMaxTem(BigDecimalUtils.listAvg(maxTem));
                    monthWeatherResultDTO.setHisMinTem(BigDecimalUtils.listAvg(minTem));
                    monthWeatherResultDTO.setHisAveTem(BigDecimalUtils.listAvg(aveTem));
                } else if (value.getType().equals("2")) {
                    monthWeatherResultDTO.setHisMaxTem(BigDecimalUtils.listAvg(maxTem1));
                    monthWeatherResultDTO.setHisMinTem(BigDecimalUtils.listAvg(minTem1));
                    monthWeatherResultDTO.setHisAveTem(BigDecimalUtils.listAvg(aveTem1));
                } else {
                    monthWeatherResultDTO.setHisMaxTem(BigDecimalUtils.listAvg(maxTem2));
                    monthWeatherResultDTO.setHisMinTem(BigDecimalUtils.listAvg(minTem2));
                    monthWeatherResultDTO.setHisAveTem(BigDecimalUtils.listAvg(aveTem2));
                }
                if (CollectionUtils.isNotEmpty(weatherFeatureCityTenDaysDTO)) {
                    for (WeatherFeatureCityTenDaysHisDO weatherFeatureCityTenDaysHisDO : weatherFeatureCityTenDaysDTO) {
                        if (value.getName().equals(weatherFeatureCityTenDaysHisDO.getType())) {
                            monthWeatherResultDTO.setMaxTem(weatherFeatureCityTenDaysHisDO.getHighestTemperature());
                            monthWeatherResultDTO.setMinTem(weatherFeatureCityTenDaysHisDO.getLowestTemperature());
                            monthWeatherResultDTO.setAveTem(weatherFeatureCityTenDaysHisDO.getAveTemperature());
                            monthWeatherResultDTO.setExtremeMaxTem(
                                weatherFeatureCityTenDaysHisDO.getExtremeHighestTemperature());
                            monthWeatherResultDTO.setExtremeMinTem(
                                weatherFeatureCityTenDaysHisDO.getExtremeLowestTemperature());
                            monthWeatherResultDTO.setExtremeAveTem(weatherFeatureCityTenDaysHisDO.getExtremeAveTemperature());
                        }
                    }
                }
                result.add(monthWeatherResultDTO);
            }
        }
        return result;
    }

    @Override
    public void saveWeatherSetting(List<MonthWeatherResultDTO> monthWeatherResultDTOS) throws Exception {
        String vaule = "";
        if (CollectionUtils.isNotEmpty(monthWeatherResultDTOS)) {
            List<String> hisYearDate = monthWeatherResultDTOS.get(0).getHisYearDate();
            for (String s : hisYearDate) {
                vaule = vaule + s + ",";
            }
            String substring = vaule.substring(0, vaule.length() - 1);
            ReportSystemInitDO reportSystemInitDO = new ReportSystemInitDO();
            reportSystemInitDO.setField(Constants.MONTH_DEFAULT);
            reportSystemInitDO.setValue(substring);
            reportSystemInitDO.setCityId("1");
            reportSystemInitDO.setName("月度历年平均年份");
            reportSystemService.doSaveOrUpdate(reportSystemInitDO);
            for (MonthWeatherResultDTO monthWeatherResultDTO : monthWeatherResultDTOS) {
                WeatherFeatureCityTenDaysHisDO weatherFeatureCityTenDaysHisDO = new WeatherFeatureCityTenDaysHisDO();
                String dateTime = monthWeatherResultDTO.getDateTime();
                String type = dateTime.substring(7);
                String year = dateTime.substring(0, 4);
                String month = dateTime.substring(5, 7);
                weatherFeatureCityTenDaysHisDO.setType(type);
                weatherFeatureCityTenDaysHisDO.setYear(year);
                weatherFeatureCityTenDaysHisDO.setMonth(month);
                weatherFeatureCityTenDaysHisDO.setCityId(monthWeatherResultDTO.getCityId());
                weatherFeatureCityTenDaysHisDO.setStatus(false);
                weatherFeatureCityTenDaysHisDO.setFlag(true);
                if (monthWeatherResultDTO.getType() == 1) {
                    weatherFeatureCityTenDaysHisDO.setStatus(true);
                }
                weatherFeatureCityTenDaysHisDO.setHighestTemperature(monthWeatherResultDTO.getMaxTem());
                weatherFeatureCityTenDaysHisDO.setLowestTemperature(monthWeatherResultDTO.getMinTem());
                weatherFeatureCityTenDaysHisDO.setAveTemperature(monthWeatherResultDTO.getAveTem());
                weatherFeatureCityTenDaysHisDO.setExtremeHighestTemperature(monthWeatherResultDTO.getExtremeMaxTem());
                weatherFeatureCityTenDaysHisDO.setExtremeLowestTemperature(monthWeatherResultDTO.getExtremeMinTem());
                weatherFeatureCityTenDaysHisDO.setExtremeAveTemperature(monthWeatherResultDTO.getExtremeAveTem());
                List<WeatherFeatureCityTenDaysHisDO> collect = weatherFeatureCityTenDaysHisService.findWeatherFeatureCityTenDaysDTO(
                    weatherFeatureCityTenDaysHisDO.getCityId(),
                    weatherFeatureCityTenDaysHisDO.getYear(), weatherFeatureCityTenDaysHisDO.getMonth(),
                    weatherFeatureCityTenDaysHisDO.getType());
                List<WeatherFeatureCityTenDaysHisDO> collect1 = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(collect)) {
                    collect1 = collect.stream()
                        .filter(t -> t.getFlag() != null && Boolean.TRUE.equals(t.getFlag()))
                        .collect(Collectors.toList());
                }
                if (CollectionUtils.isNotEmpty(collect1)) {
                    WeatherFeatureCityTenDaysHisDO weatherFeatureCityTenDaysHisDO1 = collect1.get(0);
                    weatherFeatureCityTenDaysHisDO1.setFlag(false);
                    weatherFeatureCityTenDaysHisService.doSaveOrUpdate(weatherFeatureCityTenDaysHisDO1);
                }
                weatherFeatureCityTenDaysHisService.doSaveOrUpdate(weatherFeatureCityTenDaysHisDO);
            }
        }
    }

    @Override
    public List<MonthWeatherResultDTO> getWeatherInfo(String dateStr, String cityId) throws Exception {
        List<MonthWeatherResultDTO> result = new ArrayList<>();
        String year = dateStr.split("-")[0];
        String month = dateStr.split("-")[1];
        List<WeatherFeatureCityTenDaysHisDO> weatherFeatureCityTenDaysDTO = weatherFeatureCityTenDaysHisService.
            findWeatherFeatureCityTenDaysDTO(cityId, year, month, null);
        List<WeatherFeatureCityTenDaysHisDO> collect3 = weatherFeatureCityTenDaysDTO.stream()
            .filter(t -> t.getStatus() != null && Boolean.TRUE.equals(t.getStatus())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect3)) {
            weatherFeatureCityTenDaysDTO = collect3;
        }
        String dateStartStr = String.valueOf(Integer.valueOf(year) - 1) + "-" + month + "-01";
        Date startDate = DateUtil.getDate(dateStartStr,"yyyy-MM-dd");
        Date endDate = DateUtil.getLastDayOfMonth(startDate);
        List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisDOS = weatherFeatureCityDayHisService.listWeatherFeatureCityDayHisDO(
            cityId, startDate, endDate);
        BigDecimal maxTem = null;
        BigDecimal aveTem = null;
        BigDecimal minTem = null;
        BigDecimal maxTem1 = null;
        BigDecimal aveTem1 = null;
        BigDecimal minTem1 = null;
        BigDecimal maxTem2 = null;
        BigDecimal aveTem2 = null;
        BigDecimal minTem2 = null;
        if (CollectionUtils.isNotEmpty(weatherFeatureCityDayHisDOS)) {
            // 上旬
            List<WeatherFeatureCityDayHisDO> collect = weatherFeatureCityDayHisDOS.stream().limit(10)
                .collect(Collectors.toList());
            BigDecimal highestTemperature = collect.stream().filter(t -> t.getHighestTemperature() != null)
                .max(Comparator.comparing(WeatherFeatureCityDayHisDO::getHighestTemperature)).get()
                .getHighestTemperature();
            maxTem = highestTemperature;
            BigDecimal lowestTemperature = collect.stream().filter(t -> t.getLowestTemperature() != null)
                .max(Comparator.comparing(WeatherFeatureCityDayHisDO::getLowestTemperature)).get()
                .getLowestTemperature();
            minTem = lowestTemperature;
            BigDecimal aveTemperature = collect.stream().filter(t -> t.getAveTemperature() != null)
                .max(Comparator.comparing(WeatherFeatureCityDayHisDO::getAveTemperature)).get()
                .getAveTemperature();
            aveTem = aveTemperature;
            weatherFeatureCityDayHisDOS.subList(0, 10).clear();
            // 中旬
            List<WeatherFeatureCityDayHisDO> collect1 = weatherFeatureCityDayHisDOS.stream().limit(10)
                .collect(Collectors.toList());
            BigDecimal highestTemperature1 = collect1.stream().filter(t -> t.getHighestTemperature() != null)
                .max(Comparator.comparing(WeatherFeatureCityDayHisDO::getHighestTemperature)).get()
                .getHighestTemperature();
            maxTem1 = highestTemperature1;
            BigDecimal lowestTemperature1 = collect1.stream().filter(t -> t.getLowestTemperature() != null)
                .max(Comparator.comparing(WeatherFeatureCityDayHisDO::getLowestTemperature)).get()
                .getLowestTemperature();
            minTem1 = lowestTemperature1;
            BigDecimal aveTemperature1 = collect1.stream().filter(t -> t.getAveTemperature() != null)
                .max(Comparator.comparing(WeatherFeatureCityDayHisDO::getAveTemperature)).get()
                .getAveTemperature();
            aveTem1 = aveTemperature1;
            weatherFeatureCityDayHisDOS.subList(0, 10).clear();
            // 下旬
            List<WeatherFeatureCityDayHisDO> collect2 = weatherFeatureCityDayHisDOS.stream().limit(10)
                .collect(Collectors.toList());
            BigDecimal highestTemperature2 = collect2.stream().filter(t -> t.getHighestTemperature() != null)
                .max(Comparator.comparing(WeatherFeatureCityDayHisDO::getHighestTemperature)).get()
                .getHighestTemperature();
            maxTem2 = highestTemperature2;
            BigDecimal lowestTemperature2 = collect2.stream().filter(t -> t.getLowestTemperature() != null)
                .max(Comparator.comparing(WeatherFeatureCityDayHisDO::getLowestTemperature)).get()
                .getLowestTemperature();
            minTem2 = lowestTemperature2;
            BigDecimal aveTemperature2 = collect2.stream().filter(t -> t.getAveTemperature() != null)
                .max(Comparator.comparing(WeatherFeatureCityDayHisDO::getAveTemperature)).get()
                .getAveTemperature();
            aveTem2 = aveTemperature2;
        }

        for (TenDaysTypeEnum value : TenDaysTypeEnum.values()) {
            MonthWeatherResultDTO monthWeatherResultDTO = new MonthWeatherResultDTO();
            String dateTimeStr = year + "-" + month + value.getName();
            monthWeatherResultDTO.setDateTime(dateTimeStr);
            if (TenDaysTypeEnum.First.getName().equals(value.getName())) {
                monthWeatherResultDTO.setHisMaxTem(maxTem);
                monthWeatherResultDTO.setHisMinTem(minTem);
                monthWeatherResultDTO.setHisAveTem(aveTem);
            } else if (TenDaysTypeEnum.Second.getName().equals(value.getName())) {
                monthWeatherResultDTO.setHisMaxTem(maxTem1);
                monthWeatherResultDTO.setHisMinTem(minTem1);
                monthWeatherResultDTO.setHisAveTem(aveTem1);
            } else {
                monthWeatherResultDTO.setHisMaxTem(maxTem2);
                monthWeatherResultDTO.setHisMinTem(minTem2);
                monthWeatherResultDTO.setHisAveTem(aveTem2);
            }
            if (CollectionUtils.isNotEmpty(weatherFeatureCityTenDaysDTO)) {
                for (WeatherFeatureCityTenDaysHisDO weatherFeatureCityTenDaysHisDO : weatherFeatureCityTenDaysDTO) {
                    if (value.getName().equals(weatherFeatureCityTenDaysHisDO.getType())) {
                        monthWeatherResultDTO.setMaxTem(weatherFeatureCityTenDaysHisDO.getHighestTemperature());
                        monthWeatherResultDTO.setMinTem(weatherFeatureCityTenDaysHisDO.getLowestTemperature());
                        monthWeatherResultDTO.setAveTem(weatherFeatureCityTenDaysHisDO.getAveTemperature());
                    }
                }
            }
            result.add(monthWeatherResultDTO);
        }
        return result;
    }

    @Override
    public MonthWeatherDefaultDTO getMonthWeather(String cityId, String dateStr, String season) throws Exception {
        MonthWeatherDefaultDTO monthWeatherDefaultDTO = new MonthWeatherDefaultDTO();
        List<String> years = new ArrayList<>();
        String filed = Constants.MONTH_DEFAULT;
        String year = dateStr.split("-")[0];
        if (season.equals("2")) {
            filed = Constants.SUMMER_DEFAULT;
        } else if (season.equals("3")) {
            filed = Constants.WINTER_DEFAULT;
        } else if (season.equals("4")) {
            filed = Constants.YEAR_DEFAULT;
        }
        for (int i = 2018; i < Integer.valueOf(year); i++) {
            if (Integer.valueOf(year) >= i) {
                years.add(String.valueOf(i));
            }
        }
        List<ReportSystemInitDO> reportSystemConfig = reportSystemService.findReportSystemConfig("1",
            filed, null);
        String[] split = reportSystemConfig.get(0).getValue().split(",");
        /*for (int i = 0; i < split.length; i++) {
            years.add(split[i]);
        }*/
        monthWeatherDefaultDTO.setYears(years);
        if (season.equals("2") || season.equals("3")) {
            // 冬夏
            List<WeatherFeatureCityMonthMdHisDO> weatherFeatureCityTenDaysDTO1 = weatherFeatureCityMonthMdHisService.findWeatherFeatureCityTenDaysDTO(
                cityId, dateStr.split("-")[0], dateStr.split("-")[1]);
            if (CollectionUtils.isNotEmpty(weatherFeatureCityTenDaysDTO1)) {
                List<WeatherFeatureCityMonthMdHisDO> collect = weatherFeatureCityTenDaysDTO1.stream()
                    .filter(t -> t.getType().equals("1"))
                    .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(collect)) {
                    // 保存的是历史
                    monthWeatherDefaultDTO.setType(0);
                } else {
                    monthWeatherDefaultDTO.setType(1);
                }
            }
        } else if (season.equals("4")) {
            // 年度
            List<WeatherFeatureCityYearHisDO> weatherFeatureCityTenDaysDTO2 = weatherFeatureCityYearHisService.findWeatherFeatureCityTenDaysDTO(
                cityId, dateStr.split("-")[0], dateStr.split("-")[1]);
            if (CollectionUtils.isNotEmpty(weatherFeatureCityTenDaysDTO2)) {
                List<WeatherFeatureCityYearHisDO> collect = weatherFeatureCityTenDaysDTO2.stream()
                    .filter(t -> t.getFlag() != null && Boolean.TRUE.equals(t.getFlag()))
                    .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(collect)) {
                    // 保存的是人工
                    monthWeatherDefaultDTO.setType(1);
                } else {
                    monthWeatherDefaultDTO.setType(0);
                }
            }
        } else {
            List<WeatherFeatureCityTenDaysHisDO> weatherFeatureCityTenDaysDTO = weatherFeatureCityTenDaysHisService.findWeatherFeatureCityTenDaysDTO(
                cityId, dateStr.split("-")[0], dateStr.split("-")[1], null);
            if (CollectionUtils.isNotEmpty(weatherFeatureCityTenDaysDTO)) {
                List<WeatherFeatureCityTenDaysHisDO> collect = weatherFeatureCityTenDaysDTO.stream()
                    .filter(t -> t.getFlag() != null && Boolean.TRUE.equals(t.getFlag()))
                    .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(collect)) {
                    // 有保存
                    if (Boolean.TRUE.equals(collect.get(0).getStatus())) {
                        monthWeatherDefaultDTO.setType(1);
                    } else {
                        monthWeatherDefaultDTO.setType(0);
                    }
                } else {
                    // 取默认
                    monthWeatherDefaultDTO.setType(0);
                }
            }
        }
        return monthWeatherDefaultDTO;
    }

    private FeatureDTO mergeFeature(Map<String, List<StatisticsCityTenDaysFcFeatureServiceDO>> statisticsCityTenDaysFeatureMap, String algorithmId) {
        FeatureDTO featureDTO = new FeatureDTO();
        List<BigDecimal> maxLoads = new ArrayList<>();
        List<BigDecimal> minLoads = new ArrayList<>();
        List<BigDecimal> energies = new ArrayList<>();
        List<StatisticsCityTenDaysFcFeatureServiceDO> statisticsCityMonthFcFeatureServiceDOS = statisticsCityTenDaysFeatureMap
            .get(algorithmId);
        if (MonthAlgorithmModelEnums.REPORT.getId().equals(algorithmId)){
            if (CollectionUtils.isEmpty(statisticsCityMonthFcFeatureServiceDOS)){
                String tenDaysDefaultAlgorithmId = settingSystemService.getTenDaysDefaultAlgorithmId();
                statisticsCityMonthFcFeatureServiceDOS = statisticsCityTenDaysFeatureMap
                    .get(tenDaysDefaultAlgorithmId);
            }
        }
        if (CollectionUtils.isNotEmpty(statisticsCityMonthFcFeatureServiceDOS)) {
            for (StatisticsCityTenDaysFcFeatureServiceDO statisticsCityMonthFcFeatureServiceDO : statisticsCityMonthFcFeatureServiceDOS) {
                maxLoads.add(statisticsCityMonthFcFeatureServiceDO.getMaxLoadAccuracy());
                minLoads.add(statisticsCityMonthFcFeatureServiceDO.getMinLoadAccuracy());
                energies.add(statisticsCityMonthFcFeatureServiceDO.getEnergyAccuracy());
            }
        }
        if (CollectionUtils.isNotEmpty(maxLoads)) {
            featureDTO.setMaxLoad(BigDecimalFunctions.listAvg(maxLoads).multiply(new BigDecimal(100)));
        }
        if (CollectionUtils.isNotEmpty(minLoads)) {
            featureDTO.setMinLoad(BigDecimalFunctions.listAvg(minLoads).multiply(new BigDecimal(100)));
        }
        if (CollectionUtils.isNotEmpty(energies)) {
            featureDTO.setMonthEnergy(BigDecimalFunctions.listAvg(energies).multiply(new BigDecimal(100)));
        }
        return featureDTO;
    }

    private FeatureDTO  mergeFeature(LoadFeatureCityTenDaysFcServiceDO vo) {
        FeatureDTO featureDTO = new FeatureDTO();
        if (vo != null) {
            featureDTO.setMaxLoad(vo.getMaxLoad());
            featureDTO.setMonthEnergy(vo.getEnergy());
            featureDTO.setMinLoad(vo.getMinLoad());
        }
        return featureDTO;
    }

    private TenDaysOfMonthFeatureDTO genTenDaysOfMonthFeatureDTO(LoadFeatureCityTenDaysFcServiceDO serviceDO,Map<String, LoadFeatureCityTenDaysHisServiceDO> lastYearMap,String type){
        TenDaysOfMonthFeatureDTO tenDaysOfMonthFeatureDTO = new TenDaysOfMonthFeatureDTO();
        tenDaysOfMonthFeatureDTO.setType(type);
        tenDaysOfMonthFeatureDTO.setAlgorithmName("校核上报");
        tenDaysOfMonthFeatureDTO.setMaxLoad(serviceDO.getMaxLoad());
        tenDaysOfMonthFeatureDTO.setMinLoad(serviceDO.getMinLoad());
        tenDaysOfMonthFeatureDTO.setMonthEnergy(serviceDO.getEnergy());
        tenDaysOfMonthFeatureDTO.setNoonTimeLoad(serviceDO.getDayUnbalance());
        tenDaysOfMonthFeatureDTO.setExtremeMaxLoad(serviceDO.getExtremeMaxLoad());
        tenDaysOfMonthFeatureDTO.setExtremeMinLoad(serviceDO.getExtremeMinLoad());
        tenDaysOfMonthFeatureDTO.setExtremeMonthEnergy(serviceDO.getExtremeEnergy());
        tenDaysOfMonthFeatureDTO.setExtremeNoonTimeLoad(serviceDO.getExtremeDayUnbalance());
        LoadFeatureCityTenDaysHisServiceDO lastYearFeature = lastYearMap.get(type);
        if (lastYearFeature != null) {
            tenDaysOfMonthFeatureDTO.setLastYearMaxLoad(lastYearFeature.getMaxLoad());
            tenDaysOfMonthFeatureDTO.setLastYearEnergy(lastYearFeature.getEnergy());
            tenDaysOfMonthFeatureDTO.setLastYearMinLoad(lastYearFeature.getMinLoad());
            tenDaysOfMonthFeatureDTO.setLastYearNoonTimeLoad(lastYearFeature.getDayUnbalance());
        }
        return tenDaysOfMonthFeatureDTO;
    }

}
