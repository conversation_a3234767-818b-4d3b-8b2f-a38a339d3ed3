package com.tsintergy.lf.serviceimpl.ultra.forecast.dao;

import com.tsieframework.core.base.dao.jpa.BaseJpaDAO;
import com.tsieframework.core.component.datasource.dynamic.annotation.DataSource;
import com.tsintergy.lf.serviceapi.base.ultra.forecast.pojo.SettingAlarmInitDO;
import org.springframework.stereotype.Repository;

/**
 * Description: TODO <br>
 *
 * <AUTHOR>
 * @create 2023-10-19
 * @since 1.0.0
 */
@Repository
@DataSource("ultra")
public interface SettingAlarmDAO extends BaseJpaDAO<SettingAlarmInitDO, String> {

}
