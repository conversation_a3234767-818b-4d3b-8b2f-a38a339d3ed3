package com.tsintergy.lf.serviceimpl.weather.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcMeteoBatchService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcMeteoBatchDO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherCityFcMeteoBatchDAO;
import lombok.SneakyThrows;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2024-12-04
 * @since 1.0.0
 */
@Service("weatherCityFcMeteoBatchService")
public class WeatherCityFcMeteoBatchServiceImpl implements WeatherCityFcMeteoBatchService {

    @Autowired
    WeatherCityFcMeteoBatchDAO weatherCityFcMeteoBatchDAO;

    @Autowired
    private WeatherCityFcService weatherCityFcService;

    @Override
    public void doSaveOrUpdate(WeatherCityFcMeteoBatchDO weatherCityFcMeteoBatchDO) {
        List<WeatherCityFcMeteoBatchDO> all = weatherCityFcMeteoBatchDAO.findAll(
                JpaWrappers.<WeatherCityFcMeteoBatchDO>lambdaQuery()
                        .eq(WeatherCityFcMeteoBatchDO::getCityId, weatherCityFcMeteoBatchDO.getCityId())
                        .eq(WeatherCityFcMeteoBatchDO::getType, weatherCityFcMeteoBatchDO.getType())
                        .eq(WeatherCityFcMeteoBatchDO::getDate, weatherCityFcMeteoBatchDO.getDate())
                        .eq(WeatherCityFcMeteoBatchDO::getBatchId, weatherCityFcMeteoBatchDO.getBatchId()));
        if (CollectionUtils.isEmpty(all)) {
            weatherCityFcMeteoBatchDAO.save(weatherCityFcMeteoBatchDO);
        } else {
            weatherCityFcMeteoBatchDO.setId(all.get(0).getId());
            weatherCityFcMeteoBatchDAO.saveOrUpdateByTemplate(weatherCityFcMeteoBatchDO);
        }
    }

    @SneakyThrows
    @Override
    public void insertYesterday24HourData(WeatherCityFcMeteoBatchDO weatherCityFcMeteoBatchDO) {
        Date yesterday = DateUtils.addDays(weatherCityFcMeteoBatchDO.getDate(), -1);
        WeatherCityFcMeteoBatchDO yesterdayDO = weatherCityFcMeteoBatchDAO.findOne(
                JpaWrappers.<WeatherCityFcMeteoBatchDO>lambdaQuery()
                        .eq(WeatherCityFcMeteoBatchDO::getCityId, weatherCityFcMeteoBatchDO.getCityId())
                        .eq(WeatherCityFcMeteoBatchDO::getType, weatherCityFcMeteoBatchDO.getType())
                        .eq(WeatherCityFcMeteoBatchDO::getDate, yesterday)
                        .eq(WeatherCityFcMeteoBatchDO::getBatchId, weatherCityFcMeteoBatchDO.getBatchId()));

        if (yesterdayDO == null) {
            return;
        }
        yesterdayDO.setT2400(weatherCityFcMeteoBatchDO.getT0000());
        List<BigDecimal> weatherList = yesterdayDO.getWeatherList();
        ColumnUtil.supplimentPoit(weatherList);
        Map<String, BigDecimal> decimalMap = ColumnUtil.listToMap(weatherList, Constants.LOAD_CURVE_START_WITH_ZERO);
        BasePeriodUtils.setAllFiled(yesterdayDO, decimalMap);
        weatherCityFcMeteoBatchDAO.saveOrUpdateByTemplate(yesterdayDO);
    }

    @Override
    public List<WeatherCityFcMeteoBatchDO> findBatchWeatherFcByHour(String cityId, Date date, Integer type, String hour) {
        // 1. 获取前一天日期
        String dateStr = com.tsieframework.core.base.format.datetime.DateUtils.date2String(
                com.tsieframework.core.base.format.datetime.DateUtils.addDays(date, -1),
                DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR
        );

        // 2. 构造目标 orderId 前缀
        String targetOrderIdPrefix = dateStr + hour;

        // 3. 查询数据
        List<WeatherCityFcMeteoBatchDO> weatherCityFcDOList;
        if (Constants.PROVINCE_ID.equals(cityId)) {
            // 如果 cityId 为 1，查询所有城市的数据
            weatherCityFcDOList = this.findWeatherCityFcDO(null, type, date, null); // 查询所有 type
        } else {
            // 否则，查询指定城市的数据
            weatherCityFcDOList = this.findWeatherCityFcDO(cityId, type, date, null); // 查询所有 type
        }

        // 4. 按 cityId 和 type 分组
        Map<String, Map<Integer, List<WeatherCityFcMeteoBatchDO>>> cityAndTypeGroupMap = weatherCityFcDOList.stream()
                .collect(Collectors.groupingBy(
                        WeatherCityFcMeteoBatchDO::getCityId,
                        Collectors.groupingBy(WeatherCityFcMeteoBatchDO::getType)
                ));

        // 5. 过滤每个城市和 type 的数据
        List<WeatherCityFcMeteoBatchDO> result = new ArrayList<>();
        for (Map.Entry<String, Map<Integer, List<WeatherCityFcMeteoBatchDO>>> cityEntry : cityAndTypeGroupMap.entrySet()) {
            Map<Integer, List<WeatherCityFcMeteoBatchDO>> typeGroupMap = cityEntry.getValue();

            for (Map.Entry<Integer, List<WeatherCityFcMeteoBatchDO>> typeEntry : typeGroupMap.entrySet()) {
                List<WeatherCityFcMeteoBatchDO> cityDataList = typeEntry.getValue();

                // 过滤逻辑：筛选出 orderId 小于 targetOrderIdPrefix 的记录，并按 orderId 降序排序取第一条
                WeatherCityFcMeteoBatchDO latestRecord = cityDataList.stream()
                        .filter(data -> {
                            String orderId = data.getBatchId();
                            return orderId.compareTo(targetOrderIdPrefix) <= 0;
                        })
                        .max(Comparator.comparing(WeatherCityFcMeteoBatchDO::getBatchId))
                        .orElse(null);

                if (latestRecord != null) {
                    result.add(latestRecord);
                }
            }
        }

        // 6. 如果是省份 ID，统计全省数据
        if (Constants.PROVINCE_ID.equals(cityId)) {
            List<WeatherCityFcDO> weatherCityFcDOS = weatherCityFcService.statProvinceWeather(result);
            result = weatherCityFcDOS.stream().map(src -> {
                WeatherCityFcMeteoBatchDO cityFcMeteoBatchDO = new WeatherCityFcMeteoBatchDO();
                BeanUtils.copyProperties(src, cityFcMeteoBatchDO);
                return cityFcMeteoBatchDO;
            }).collect(Collectors.toList());
        }
        return result;
    }

    public List<WeatherCityFcMeteoBatchDO> findWeatherCityFcDO(String cityId, Integer type, Date date, String orderId) {
        return weatherCityFcMeteoBatchDAO.findAll(
                JpaWrappers.<WeatherCityFcMeteoBatchDO>lambdaQuery()
                        .eq(cityId != null, WeatherCityFcMeteoBatchDO::getCityId, cityId)
                        .eq(type != null, WeatherCityFcMeteoBatchDO::getType, type)
                        .eq(WeatherCityFcMeteoBatchDO::getDate, date)
                        .eq(orderId != null, WeatherCityFcMeteoBatchDO::getBatchId, orderId));
    }
}
