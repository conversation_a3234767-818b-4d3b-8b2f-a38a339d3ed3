
package com.tsintergy.lf.serviceimpl.evalucation.impl;


import com.tsieframework.core.base.dao.DataPackage;
import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.math.BigDecimalFunctions;
import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.util.compatible.BigDecimalUtils;
import com.tsieframework.util.compatible.StringUtils;
import com.tsintergy.lf.core.constants.CityConstants;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.api.HolidayService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.base.pojo.HolidayDO;
import com.tsintergy.lf.serviceapi.base.check.pojo.SettingReportDO;
import com.tsintergy.lf.serviceapi.base.evalucation.api.AccuracyLoadCityFcService;
import com.tsintergy.lf.serviceapi.base.evalucation.api.BatchDataFilterService;
import com.tsintergy.lf.serviceapi.base.evalucation.api.EvalucationService;
import com.tsintergy.lf.serviceapi.base.evalucation.api.StatisticsCityDayFcFeatureService;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.*;
import com.tsintergy.lf.serviceapi.base.evalucation.enums.CityAccuracyEnum;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.AccuracyLoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.DispersionLoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.StatisticsCityDayFcDO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.StatisticsCityDayFcFeatureDO;
import com.tsintergy.lf.serviceapi.base.forecast.api.AlgorithmService;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcService;
import com.tsintergy.lf.serviceapi.base.forecast.dto.AlgorithmAccuracyDTO;
import com.tsintergy.lf.serviceapi.base.forecast.dto.AlgorithmResultStatDTO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.AlgorithmDO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityDayHisService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityDayHisDO;
import com.tsintergy.lf.serviceapi.base.recall.api.AccuracyLoadCityFcRecallService;
import com.tsintergy.lf.serviceapi.base.system.api.SettingBatchInitService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityHisService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityDayFcService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityDayHisService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayFcDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayHisDO;
import com.tsintergy.lf.serviceimpl.check.dao.SettingReportDAO;
import com.tsintergy.lf.serviceimpl.evalucation.dao.*;
import lombok.SneakyThrows;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Map.Entry;
import java.util.stream.Collectors;


/**
 * 预测后评估服务
 *
 * <AUTHOR>
 * @version $Id: EvalucationServiceImpl.java, v 0.1 2018-01-31 10:15:17 tao Exp $$
 */
@Service("evalucationService")
public class EvalucationServiceImpl extends BaseServiceImpl implements EvalucationService {

    private static final Logger logger = LogManager.getLogger(EvalucationServiceImpl.class);

    private final String REPORT = "report";
    @Autowired
    LoadCityHisService loadCityHisService;
    @Autowired
    LoadCityFcService loadCityFcService;
    @Autowired
    AccuracyLoadCityFcService accuracyLoadCityFcService;
    @Autowired
    AccuracyLoadCityFcRecallService accuracyLoadCityFcRecallService;
    @Autowired
    WeatherCityFcService weatherCityFcService;
    @Autowired
    WeatherCityHisService weatherCityHisService;
    @Autowired
    StatisticsCityDayFcFeatureService statisticsCityDayFcFeatureService;
    @Autowired
    private AccuracyLoadCityFcDAO accuracyLoadCityFcDAO;
    @Autowired
    private PassLoadCityFcDAO passLoadCityFcDAO;
    @Autowired
    private DispersionLoadCityFcDAO dispersionLoadCityFcDAO;
    @Autowired
    private AlgorithmService algorithmService;
    @Autowired
    private StatisticsCityDayFcDAO statisticsCityDayFcDAO;
    @Autowired
    private StatisticsCityDayFcBatchDAO statisticsCityDayFcBatchDAO;
    @Autowired
    private SettingReportDAO settingReportDAO;
    @Autowired
    private WeatherFeatureCityDayHisService weatherFeatureCityDayHisService;
    @Autowired
    private WeatherFeatureCityDayFcService weatherFeatureCityDayFcService;
    @Autowired
    private HolidayService holidayService;
    @Autowired
    private CityService cityService;
    @Autowired
    private LoadFeatureCityDayHisService loadFeatureCityDayHisService;
    @Autowired
    private SettingBatchInitService settingBatchInitService;

    @Autowired
    private BatchDataFilterService batchDataFilterService;

    @Override
    public List<CityAccuracyDTO> getCityAccuracy(String cityId, String caliberId, Date startDate, Date endDate,
                                                 String startPeriod, String endPeriod, String isHoliday, String batchId, Integer dayNum) {

        List<CityAccuracyDTO> results = new ArrayList<CityAccuracyDTO>();

        try {

            Map<Date, Map<String, BigDecimal>> dataMap = new HashMap<Date, Map<String, BigDecimal>>();

            // 查询一天的准确率
            if (ColumnUtil.isAllColumn(startPeriod, endPeriod)) {
                List<StatisticsCityDayFcDO> statisticsCityDayFcDOS = statisticsCityDayFcDAO
                        .getStatisticsCityDayFcDOs(cityId, caliberId, null, startDate, endDate, null);
                //按批次+日前n天清洗数据
//                List<StatisticsCityDayFcBatchDO> statisticsBatchDOS = batchDataFilterService.filterStatisticsByBatchId(statisticsCityDayFcBatchDOs, cityId, batchId, dayNum);

                for (StatisticsCityDayFcDO statisticsCityDayFcBatchDO : statisticsCityDayFcDOS) {
                    dataMap.computeIfAbsent(statisticsCityDayFcBatchDO.getDate(), k -> new HashMap<String, BigDecimal>());
                    dataMap.get(statisticsCityDayFcBatchDO.getDate())
                            .put(statisticsCityDayFcBatchDO.getAlgorithmId(), statisticsCityDayFcBatchDO.getAccuracy());
                    if (statisticsCityDayFcBatchDO.getReport() != null && statisticsCityDayFcBatchDO.getReport()) {
                        dataMap.get(statisticsCityDayFcBatchDO.getDate()).put(REPORT, statisticsCityDayFcBatchDO.getAccuracy());
                    }
                }
            }
            // 查询时段内的准确率
            else {
                List<AccuracyLoadCityFcDO> AccuracyLoadCityFcDOS = accuracyLoadCityFcDAO
                        .getAccuracyLoadCityFcDOs(cityId, caliberId, null, startDate, endDate, null);
                // map<date,list<AccuracyLoadCityFcDO>
                Map<Date, List<AccuracyLoadCityFcDO>> AccuracyLoadCityFcDOMap = new HashMap<Date, List<AccuracyLoadCityFcDO>>();
                if (AccuracyLoadCityFcDOS.size() > 0) {
                    for (AccuracyLoadCityFcDO AccuracyLoadCityFcDO : AccuracyLoadCityFcDOS) {
                        if (dataMap.get(AccuracyLoadCityFcDO.getDate()) == null) {
                            dataMap.put(AccuracyLoadCityFcDO.getDate(), new HashMap<String, BigDecimal>());
                        }
                        dataMap.get(AccuracyLoadCityFcDO.getDate()).put(AccuracyLoadCityFcDO.getAlgorithmId(),
                                accuracyLoadCityFcDAO.calculateAvgAccuracy(AccuracyLoadCityFcDO, startPeriod, endPeriod));
                    }
                }
            }

            if (dataMap.size() != 0) {
                List<String> days = DateUtil
                        .getListBetweenDay(DateUtil.formateDate(startDate), DateUtil.formateDate(endDate), "yyyy-MM-dd");
                List<AlgorithmDO> AlgorithmDOS1 = algorithmService.getAllAlgorithmsNotCache();
                AlgorithmDOS1 = algorithmService.fillRegionalCustomAlgoName(AlgorithmDOS1, cityId);
                List<AlgorithmDO> AlgorithmDOS = AlgorithmDOS1.stream().sorted(Comparator.comparing(AlgorithmDO::getOrderNo))
                        .collect(Collectors.toList());
                List<AlgorithmDO> pageAlgorithms = algorithmService.getPageAlgorithms(AlgorithmDOS1, cityId);
                //获取日期区间内的节假日区间
                List<String> holidayList = getHolidayList(startDate, endDate);
                for (String dayStr : days) {
                    //判断是否包含节假日，该天是否是节假日
                    if ("0".equals(isHoliday) && holidayList.contains(dayStr)) {
                        continue;
                    }
                    Date day = DateUtil.getDate(dayStr, "yyyy-MM-dd");
                    CityAccuracyDTO cityAccuracyDTO = new CityAccuracyDTO();
                    cityAccuracyDTO.setAlgorithmDetail(new ArrayList<AlgorithmAccuracyDTO>());
                    cityAccuracyDTO.setDate(day);
                    for (AlgorithmDO AlgorithmDO : AlgorithmDOS) {
                        //排除不展示
                        if (isExclude(cityId, AlgorithmDO.getId(), pageAlgorithms)) {
                            continue;
                        }
                        if (!"1".equals(cityId) && "分区预测".equals(AlgorithmDO.getAlgorithmCn())) {
                            continue;
                        }
                        AlgorithmAccuracyDTO algorithmAccuracyDTO = new AlgorithmAccuracyDTO();
                        algorithmAccuracyDTO.setAlgorithmId(AlgorithmDO.getId());
                        algorithmAccuracyDTO.setName(AlgorithmDO.getAlgorithmCn());
                        if ("1".equals(cityId) && "分区预测".equals(algorithmAccuracyDTO.getName())) {
                            algorithmAccuracyDTO.setName("子网累加");
                        }
                        if (dataMap.get(day) != null && dataMap.get(day).get(AlgorithmDO.getId()) != null) {
                            algorithmAccuracyDTO.setAccuracy(dataMap.get(day).get(AlgorithmDO.getId()));
                        }
                        cityAccuracyDTO.getAlgorithmDetail().add(algorithmAccuracyDTO);
                    }

                 /*  暂时屏蔽最终上报，最终上报为人工修正
                    //加入上报准确率
                    AlgorithmAccuracyDTO algorithmAccuracyDTO = new AlgorithmAccuracyDTO();
                    algorithmAccuracyDTO.setAlgorithmId(REPORT);
                    algorithmAccuracyDTO.setName("最终上报");
                    if (dataMap.get(day) != null && dataMap.get(day).get(REPORT) != null) {
                        algorithmAccuracyDTO.setAccuracy(dataMap.get(day).get(REPORT));
                    }
                    cityAccuracyDTO.getAlgorithmDetail().add(algorithmAccuracyDTO);*/
                    cityAccuracyDTO.getAlgorithmDetail().sort(Comparator.comparing(AlgorithmAccuracyDTO::getAlgorithmId));
                    results.add(cityAccuracyDTO);
                }
            }
        } catch (Exception e) {
            logger.error(e);
            throw TsieExceptionUtils.newBusinessException("02F20180001");
        }
        return results;
    }

    @SneakyThrows
    @Override
    public List<CityAccuracyDTO> getCityAccuracyByType(String cityId, String caliberId, Date startDate, Date endDate,
                                                       String isHoliday, String type) {
        List<CityAccuracyDTO> result = new ArrayList<>();
        List<StatisticsCityDayFcFeatureDO> statisticsCityDayFcFeatureDOReportList = statisticsCityDayFcFeatureService.
                getStatisticsCityDayFcFeatureDOReportList(cityId, caliberId, startDate, endDate, null);
        Map<String, List<StatisticsCityDayFcFeatureDO>> collect = new HashMap<>();
        if (CollectionUtils.isNotEmpty(statisticsCityDayFcFeatureDOReportList)) {
            collect = statisticsCityDayFcFeatureDOReportList.stream().collect(Collectors.groupingBy(
                    t -> DateUtil.getDateToStrFORMAT(t.getDate(), "yyyy-MM-dd") + "-" + t.getAlgorithmId()));
        }
        List<AlgorithmDO> AlgorithmDOS = algorithmService.getAllAlgorithmsNotCache();
        AlgorithmDOS = algorithmService.fillRegionalCustomAlgoName(AlgorithmDOS, cityId);
        List<AlgorithmDO> collectAlgorithmDOS = AlgorithmDOS.stream().sorted(Comparator.comparing(AlgorithmDO::getOrderNo))
                .collect(Collectors.toList());
        List<AlgorithmDO> pageAlgorithms = algorithmService.getPageAlgorithms(collectAlgorithmDOS, cityId);

        //获取日期区间内的节假日区间
        List<String> holidayList = getHolidayList(startDate, endDate);
        List<Date> listBetweenDay = DateUtil.getListBetweenDay(startDate, endDate);
        for (Date date : listBetweenDay) {
            String dateToStrFORMAT = DateUtil.getDateToStrFORMAT(date, "yyyy-MM-dd");
            //判断是否包含节假日，该天是否是节假日
            if ("0".equals(isHoliday) && holidayList.contains(dateToStrFORMAT)) {
                continue;
            }
            CityAccuracyDTO cityAccuracyDTO = new CityAccuracyDTO();
            List<AlgorithmAccuracyDTO> algorithmDetail = new ArrayList<>();
            cityAccuracyDTO.setDate(date);
            for (AlgorithmDO collectAlgorithmDO : collectAlgorithmDOS) {
                //排除不展示
                if (isExclude(cityId, collectAlgorithmDO.getId(), pageAlgorithms)) {
                    continue;
                }
                if (!"1".equals(cityId) && "分区预测".equals(collectAlgorithmDO.getAlgorithmCn())) {
                    continue;
                }
                AlgorithmAccuracyDTO algorithmAccuracyDTO = new AlgorithmAccuracyDTO();
                algorithmAccuracyDTO.setAlgorithmId(collectAlgorithmDO.getId());
                algorithmAccuracyDTO.setName(collectAlgorithmDO.getAlgorithmCn());
                if ("1".equals(cityId) && "分区预测".equals(algorithmAccuracyDTO.getName())) {
                    algorithmAccuracyDTO.setName("子网累加");
                }
                String key = dateToStrFORMAT + "-" + collectAlgorithmDO.getId();
                List<StatisticsCityDayFcFeatureDO> statisticsCityDayFcFeatureDOS = collect.get(key);
                if (CollectionUtils.isNotEmpty(statisticsCityDayFcFeatureDOS)) {
                    for (StatisticsCityDayFcFeatureDO statisticsCityDayFcFeatureDO : statisticsCityDayFcFeatureDOS) {
                        algorithmAccuracyDTO.setAccuracy(statisticsCityDayFcFeatureDO.getMinLoadAccuracy());
                        if (CityAccuracyEnum.MAX_LOAD_ACCURACY.getTypeId().equals(type)) {
                            algorithmAccuracyDTO.setAccuracy(statisticsCityDayFcFeatureDO.getMaxLoadAccuracy());
                        }
                    }
                }
                algorithmDetail.add(algorithmAccuracyDTO);
            }
            cityAccuracyDTO.setAlgorithmDetail(algorithmDetail);
            result.add(cityAccuracyDTO);
        }
        return result;
    }

    @Override
    public List<AlgorithmResultStatDTO> getAlgorithmFcStat(String cityId, String caliberId, Date startDate, Date endDate, String startPeriod,
                                                           String endPeriod, String isHoliday, String batchId, Integer days) throws Exception {
        List<AlgorithmResultStatDTO> results = new ArrayList<AlgorithmResultStatDTO>();
        List<AlgorithmResultStatDTO> resultAll = new ArrayList<AlgorithmResultStatDTO>();
        try {
            // Map<AlgorithmID,Map<type,value>>
            Map<String, Map<String, BigDecimal>> dataMap = new HashMap<String, Map<String, BigDecimal>>();
            List<String> holidayList = getHolidayList(startDate, endDate);

            List<AlgorithmDO> algorithmDOS = algorithmService.getAllAlgorithmsNotCache();
            List<AlgorithmDO> pageAlgorithms = algorithmService.getPageAlgorithms(algorithmDOS, cityId);

            // 查询一天的准确率
            if (ColumnUtil.isAllColumn(startPeriod, endPeriod)) {
                List<StatisticsCityDayFcDO> statisticsCityDayFcDOS = statisticsCityDayFcDAO
                        .getStatisticsCityDayFcDOs(cityId, caliberId, null, startDate, endDate, null);
                //按批次+日前n天清洗数据
                //List<StatisticsCityDayFcBatchDO> statisticsBatchDOS = batchDataFilterService.filterStatisticsByBatchId(statisticsCityDayFcBatchDOs, cityId, batchId, days);

                // map<algorithmId,map<type,list<value>>>
                Map<String, Map<String, List<BigDecimal>>> statisticsMap = new HashMap<String, Map<String, List<BigDecimal>>>();

                //初始化上报统Map
                Map<String, List<BigDecimal>> reportMap = initEmptyMap();
                statisticsMap.put(REPORT, reportMap);

                for (StatisticsCityDayFcDO statisticsCityDayFcBatchDO : statisticsCityDayFcDOS) {
                    if ("0".equals(isHoliday) && holidayList.contains(DateUtils
                            .date2String(statisticsCityDayFcBatchDO.getDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR))) {
                        continue;
                    }
                    //排除不展示
                    if (isExclude(cityId, statisticsCityDayFcBatchDO.getAlgorithmId(), pageAlgorithms)) {
                        continue;
                    }
                    if (!"1".equals(cityId) && "17".equals(statisticsCityDayFcBatchDO.getAlgorithmId())) {
                        continue;
                    }
                    //初始化各算法统计Map
                    if (statisticsMap.get(statisticsCityDayFcBatchDO.getAlgorithmId()) == null) {
                        Map<String, List<BigDecimal>> map = initEmptyMap();
                        statisticsMap.put(statisticsCityDayFcBatchDO.getAlgorithmId(), map);
                    }

                    //统计合格率、准确率、离散度到Map   注意statistics_city_day_fc_service合格率、准确率、离散度都不能为空！！！
                    if (statisticsCityDayFcBatchDO.getPass() != null && statisticsCityDayFcBatchDO.getAccuracy() != null
                            && statisticsCityDayFcBatchDO.getDispersion() != null) {
                        statisticsMap.get(statisticsCityDayFcBatchDO.getAlgorithmId()).get("pass")
                                .add(statisticsCityDayFcBatchDO.getPass());
                        statisticsMap.get(statisticsCityDayFcBatchDO.getAlgorithmId()).get("accuracy")
                                .add(statisticsCityDayFcBatchDO.getAccuracy());
                        statisticsMap.get(statisticsCityDayFcBatchDO.getAlgorithmId()).get("dispersion")
                                .add(statisticsCityDayFcBatchDO.getDispersion());

                        if (statisticsCityDayFcBatchDO.getReport() != null && statisticsCityDayFcBatchDO.getReport()) {
                            statisticsMap.get(REPORT).get("pass").add(statisticsCityDayFcBatchDO.getPass());
                            statisticsMap.get(REPORT).get("accuracy").add(statisticsCityDayFcBatchDO.getAccuracy());
                            statisticsMap.get(REPORT).get("dispersion").add(statisticsCityDayFcBatchDO.getDispersion());
                        }
                    }
                }

                Set<String> algorithmIdSet = new HashSet<>();
                algorithmIdSet.addAll(statisticsMap.keySet());
                algorithmIdSet.add(REPORT);
                for (String algorithmId : algorithmIdSet) {
                    Map<String, BigDecimal> map = new HashMap<String, BigDecimal>();
                    List<BigDecimal> passList = statisticsMap.get(algorithmId).get("pass");
//                    //合格率的计算是 合格的天数除总天数
                    BigDecimal pass = new BigDecimal(0);
                    if (passList.size() < 1 || statisticsMap.get(algorithmId).get("accuracy") == null
                            || statisticsMap.get(algorithmId).get("dispersion") == null) {
                        logger.error(
                                "数据异常,请查" + DateUtils.date2String(startDate, DateFormatType.SIMPLE_DATE_FORMAT_STR) + "到"
                                        + DateUtils.date2String(endDate, DateFormatType.SIMPLE_DATE_FORMAT_STR)
                                        + "这段时间准确率、合格率、离散度");
//                         throw  new BusinessException("01C20180002");
                        continue;
                    }
                    map.put("pass", BigDecimalUtils.avgList(statisticsMap.get(algorithmId).get("pass"), 4, true));
                    map.put("accuracy",
                            BigDecimalUtils.avgList(statisticsMap.get(algorithmId).get("accuracy"), 4, false));
                    map.put("dispersion",
                            BigDecimalUtils.avgList(statisticsMap.get(algorithmId).get("dispersion"), 4, false));
                    dataMap.put(algorithmId, map);
                }
            }
            // 查询时段内的准确率
            else {
                // 查询日期段内的准确率
                List<AccuracyLoadCityFcDO> accuracyList = accuracyLoadCityFcDAO
                        .getAccuracyLoadCityFcDOs(cityId, caliberId, null, startDate, endDate, null);
                Map<String, List<AccuracyLoadCityFcDO>> accuracyMap = new HashMap<String, List<AccuracyLoadCityFcDO>>(); // map<AlgorithmId,list<AccuracyLoadCityFcDO>
                for (AccuracyLoadCityFcDO AccuracyLoadCityFcDO : accuracyList) {
                    if ("0".equals(isHoliday) && holidayList.contains(
                            DateUtils.date2String(AccuracyLoadCityFcDO.getDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR))) {
                        continue;
                    }
                    if (!accuracyMap.containsKey(AccuracyLoadCityFcDO.getAlgorithmId())) {
                        accuracyMap.put(AccuracyLoadCityFcDO.getAlgorithmId(), new ArrayList<AccuracyLoadCityFcDO>());
                    }
                    accuracyMap.get(AccuracyLoadCityFcDO.getAlgorithmId()).add(AccuracyLoadCityFcDO);
                }

                //计算合格率
                List<CityAccuracyDTO> cityAccuracyDTOS = this
                        .getCityAccuracy(cityId, caliberId, startDate, endDate, startPeriod, endPeriod, isHoliday, batchId, days);
                // 查询标准准确率
                SettingReportDO SettingReportDO = settingReportDAO.getSettingReportDO(cityId);
                BigDecimal standAccuracy = SettingReportDO.getStandardAccuracy();
                Map<String, BigDecimal> passCountMap = new HashMap<>();
                for (CityAccuracyDTO cityAccuracyDTO : cityAccuracyDTOS) {
                    List<AlgorithmAccuracyDTO> algorithmAccuracyDTOList = cityAccuracyDTO.getAlgorithmDetail();
                    for (AlgorithmAccuracyDTO algorithmAccuracyDTO : algorithmAccuracyDTOList) {
                        BigDecimal accuracy = algorithmAccuracyDTO.getAccuracy();
                        if (accuracy == null) {
                            continue;
                        }
                        if (passCountMap.get(algorithmAccuracyDTO.getAlgorithmId()) == null) {
                            passCountMap.put(algorithmAccuracyDTO.getAlgorithmId(), new BigDecimal(0));
                        }
                        if (accuracy.compareTo(standAccuracy) >= 0) {
                            passCountMap.put(algorithmAccuracyDTO.getAlgorithmId(),
                                    passCountMap.get(algorithmAccuracyDTO.getAlgorithmId()).add(BigDecimal.ONE));
                        }
                    }
                }
                // 查询日期段内的合格率
//                List<PassLoadCityFcVO> passList = getPassLoadCityFcBO().getPassLoadCityFcVO(cityId, caliberId, null, startDate, endDate, null);
//                Map<String, List<PassLoadCityFcVO>> passMap = new HashMap<String, List<PassLoadCityFcVO>>(); // map<AlgorithmId,list<PassLoadCityFcVO>
//                for (PassLoadCityFcVO passLoadCityFcVO : passList) {
//                    if (!passMap.containsKey(passLoadCityFcVO.getAlgorithmId())) {
//                        passMap.put(passLoadCityFcVO.getAlgorithmId(), new ArrayList<PassLoadCityFcVO>());
//                    }
//                    passMap.get(passLoadCityFcVO.getAlgorithmId()).add(passLoadCityFcVO);
//                }
                // 查询日期段内的离散率
                List<DispersionLoadCityFcDO> dispersionList = dispersionLoadCityFcDAO
                        .getDispersionLoadCityFcDO(cityId, caliberId, null, startDate, endDate, null);
                Map<String, List<DispersionLoadCityFcDO>> dispersionMap = new HashMap<String, List<DispersionLoadCityFcDO>>(); // map<AlgorithmId,list<DispersionLoadCityFcDO>
                for (DispersionLoadCityFcDO DispersionLoadCityFcDO : dispersionList) {
                    if ("0".equals(isHoliday) && holidayList.contains(DateUtils
                            .date2String(DispersionLoadCityFcDO.getDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR))) {
                        continue;
                    }
                    if (!dispersionMap.containsKey(DispersionLoadCityFcDO.getAlgorithmId())) {
                        dispersionMap
                                .put(DispersionLoadCityFcDO.getAlgorithmId(), new ArrayList<DispersionLoadCityFcDO>());
                    }
                    dispersionMap.get(DispersionLoadCityFcDO.getAlgorithmId()).add(DispersionLoadCityFcDO);
                }

                for (String algorithmId : accuracyMap.keySet()) {
                    Map<String, BigDecimal> map = new HashMap<String, BigDecimal>();
                    map.put("accuracy", accuracyLoadCityFcDAO
                            .calculateAvgAccuracy(accuracyMap.get(algorithmId), startPeriod, endPeriod));
                    BigDecimal count = passCountMap.get(algorithmId);
                    List<Date> betweenDays = DateUtil.getListBetweenDay(startDate, endDate);
                    //合格率计算是准确率合格的点除以总天数
                    map.put("pass", count == null ? null
                            : count.divide(new BigDecimal(betweenDays.size()), 2, BigDecimal.ROUND_HALF_UP));
                    map.put("dispersion", dispersionLoadCityFcDAO
                            .calculateAvgDispersion(dispersionMap.get(algorithmId), startPeriod, endPeriod));
                    dataMap.put(algorithmId, map);
                }
            }
            if (dataMap.size() != 0) {
                for (AlgorithmDO AlgorithmDO : algorithmDOS) {
                    //排除不展示
                    if (isExclude(cityId, AlgorithmDO.getId(), pageAlgorithms)) {
                        continue;
                    }
                    AlgorithmResultStatDTO dto = new AlgorithmResultStatDTO();
                    dto.setAlgorithmId(AlgorithmDO.getId());
                    dto.setName(AlgorithmDO.getAlgorithmCn());
                    if ("1".equals(cityId) && "分区预测".equals(AlgorithmDO.getAlgorithmCn())) {
                        dto.setName("子网累加");
                    }
                    if (dataMap.get(AlgorithmDO.getId()) != null) {
                        dto.setAccuracy(dataMap.get(AlgorithmDO.getId()).get("accuracy"));
                        dto.setPass(dataMap.get(AlgorithmDO.getId()).get("pass"));
                        dto.setDiscrete(dataMap.get(AlgorithmDO.getId()).get("dispersion"));
                    }
                    results.add(dto);
                }

               /* 暂时不展示最终上报
                if (dataMap.get(REPORT) != null) {
                    AlgorithmResultStatDTO dto = new AlgorithmResultStatDTO();
                    dto.setAlgorithmId(REPORT);
                    dto.setName("最终上报");
                    if (dataMap.get(REPORT) != null) {
                        dto.setAccuracy(dataMap.get(REPORT).get("accuracy"));
                        dto.setPass(dataMap.get(REPORT).get("pass"));
                        dto.setDiscrete(dataMap.get(REPORT).get("dispersion"));
                    }
                    results.add(dto);
                }*/
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(), e);
        }
        for (AlgorithmResultStatDTO result : results) {
            if (!"1".equals(cityId) && "17".equals(result.getAlgorithmId())) {
                continue;
            }
            resultAll.add(result);
        }
        return resultAll;
    }

    @Override
    public List<AlgorithmResultStatDTO> getAlgorithmFcStatByType(String cityId, String caliberId, Date startDate,
        Date endDate, String isHoliday, String type) throws Exception {
        List<AlgorithmResultStatDTO> result = new ArrayList<>();
        //获取日期区间内的节假日区间
        List<String> holidayList = getHolidayList(startDate, endDate);
        List<StatisticsCityDayFcFeatureDO> statisticsCityDayFcFeatureDOReportList = statisticsCityDayFcFeatureService.
            getStatisticsCityDayFcFeatureDOReportList(cityId, caliberId, startDate, endDate, null);
        Map<String, List<StatisticsCityDayFcFeatureDO>> collect = new HashMap<>();
        if (CollectionUtils.isNotEmpty(statisticsCityDayFcFeatureDOReportList)) {
            if ("0".equals(isHoliday)) {
                collect = statisticsCityDayFcFeatureDOReportList.stream().filter(t -> !holidayList.contains(
                        DateUtil.getDateToStrFORMAT(t.getDate(), "yyyy-MM-dd"))).collect(Collectors.groupingBy(
                        t -> t.getAlgorithmId()));
            } else {
                collect = statisticsCityDayFcFeatureDOReportList.stream().collect(Collectors.groupingBy(
                        t -> t.getAlgorithmId()));
            }
        }

        List<AlgorithmDO> algorithmDOS = algorithmService.getAllAlgorithmsNotCache();
        List<AlgorithmDO> pageAlgorithms = algorithmService.getPageAlgorithms(algorithmDOS, cityId);

        List<AlgorithmDO> collectAlgorithmDOS = algorithmDOS.stream().sorted(Comparator.comparing(AlgorithmDO::getOrderNo))
                .collect(Collectors.toList());
        for (AlgorithmDO collectAlgorithmDO : collectAlgorithmDOS) {
            for (Entry<String, List<StatisticsCityDayFcFeatureDO>> stringListEntry : collect.entrySet()) {
                String key = stringListEntry.getKey();
                if (key.equals(collectAlgorithmDO.getId())) {
                    List<StatisticsCityDayFcFeatureDO> value = stringListEntry.getValue();
                    List<BigDecimal> collectAccuracy = new ArrayList<>();
                    if (CollectionUtils.isNotEmpty(value)) {
                        if (CityAccuracyEnum.MAX_LOAD_ACCURACY.getTypeId().equals(type)) {
                            collectAccuracy = value.stream().filter(t -> t.getMaxLoadAccuracy() != null).map(
                                    StatisticsCityDayFcFeatureDO::getMaxLoadAccuracy).collect(Collectors.toList());
                        } else {
                            collectAccuracy = value.stream().filter(t -> t.getMinLoadAccuracy() != null).map(
                                StatisticsCityDayFcFeatureDO::getMinLoadAccuracy).collect(Collectors.toList());
                        }
                    }
                    //排除不展示
                    if (isExclude(cityId, collectAlgorithmDO.getId(), pageAlgorithms)) {
                        continue;
                    }
                    if (!"1".equals(cityId) && "分区预测".equals(collectAlgorithmDO.getAlgorithmCn())) {
                        continue;
                    }
                    AlgorithmResultStatDTO algorithmResultStatDTO = new AlgorithmResultStatDTO();
                    algorithmResultStatDTO.setAlgorithmId(collectAlgorithmDO.getId());
                    algorithmResultStatDTO.setName(collectAlgorithmDO.getAlgorithmCn());
                    if ("1".equals(cityId) && "分区预测".equals(algorithmResultStatDTO.getName())) {
                        algorithmResultStatDTO.setName("子网累加");
                    }
                    algorithmResultStatDTO.setAccuracy(BigDecimalFunctions.listAvg(collectAccuracy));
                    result.add(algorithmResultStatDTO);
                }
            }
        }
        return result;
    }

    private Map<String, List<BigDecimal>> initEmptyMap() {
        Map<String, List<BigDecimal>> reportMap = new HashMap<String, List<BigDecimal>>();
        reportMap.put("accuracy", new ArrayList<BigDecimal>());
        reportMap.put("dispersion", new ArrayList<BigDecimal>());
        reportMap.put("pass", new ArrayList<BigDecimal>());
        return reportMap;
    }

    private boolean isExclude(String cityId, String algorithmId, List<AlgorithmDO> pageAlgorithms) throws Exception {
        List<AlgorithmDO> viewAlgorithms;
        List<String> collect;
        if (StringUtils.isNotBlank(cityId) && cityId.equals(CityConstants.PROVINCE_ID)) {
            viewAlgorithms = pageAlgorithms.stream().filter(t -> t.getProvinceView() == true)
                    .collect(Collectors.toList());
            collect = viewAlgorithms.stream().map(AlgorithmDO::getId).collect(Collectors.toList());
            if (collect.contains(algorithmId)) {
                return false;
            }
        } else {
            viewAlgorithms = pageAlgorithms.stream().filter(t -> t.getCityView() == true).collect(Collectors.toList());
            collect = viewAlgorithms.stream().map(AlgorithmDO::getId).collect(Collectors.toList());
            if (collect.contains(algorithmId)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 获取节假日日期列表
     *
     * @param startDate
     * @param endDate
     * @return
     */
    private List<String> getHolidayList(Date startDate, Date endDate) {
        List<HolidayDO> holidayLists = holidayService.findHolidayVOS(startDate, endDate);
        List<String> holidayList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(holidayLists)) {
            for (HolidayDO holidayVO : holidayLists) {
                List<String> listBetweenDay = DateUtil.getListBetweenDay(DateUtil.formateDate(holidayVO.getStartDate()),
                        DateUtil.formateDate(holidayVO.getEndDate()));
                holidayList.addAll(listBetweenDay);
            }
        }
        return holidayList;
    }


    @Override
    public List<PredictionAssessmentDTO> findWeatherPredictionAccuracyBaseByDay(String cityId, Date startTime,
                                                                                Date endTime, String type) throws Exception {

        cityId = cityService.findWeatherCityId(cityId);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        List<PredictionAssessmentDTO> resultList = new ArrayList<PredictionAssessmentDTO>();
        //历史气象特性统计
        List<WeatherFeatureCityDayHisDO> hisWeatherFeature = weatherFeatureCityDayHisService
                .listWeatherFeatureCityDayHisDO(cityId, startTime, endTime);
        Map<java.sql.Date, WeatherFeatureCityDayHisDO> weatherFeatureHisMap = hisWeatherFeature.stream().collect(
                Collectors
                        .toMap(WeatherFeatureCityDayHisDO::getDate, WeatherFeatureCityDayHisDO -> WeatherFeatureCityDayHisDO));
        //预测气象统计
        DataPackage dataPackage = weatherFeatureCityDayFcService
                .listWeatherFeatureCityDayByPage(cityId, startTime, endTime);
        List<WeatherFeatureCityDayFcDO> datas = dataPackage.getDatas();
        Map<java.sql.Date, WeatherFeatureCityDayFcDO> weatherFeaturePreMap = datas.stream().collect(Collectors
                .toMap(WeatherFeatureCityDayFcDO::getDate, WeatherFeatureCityDayFcDO -> WeatherFeatureCityDayFcDO));
        List<String> dateList = getDayBetween(sdf.format(startTime), sdf.format(endTime));
        for (int i = 0; i < dateList.size(); i++) {

            WeatherFeatureCityDayFcDO weatherFeaturePreDO = weatherFeaturePreMap
                    .get(new java.sql.Date(sdf.parse(dateList.get(i)).getTime()));
            WeatherFeatureCityDayHisDO wetherFeatureHisStatDO = weatherFeatureHisMap
                    .get(new java.sql.Date(sdf.parse(dateList.get(i)).getTime()));

            PredictionAssessmentDTO assessmentDTO = new PredictionAssessmentDTO();
            assessmentDTO.setDate(dateList.get(i));


            WeatherProAndRealDTO temperature = new WeatherProAndRealDTO();
            temperature.setDate(dateList.get(i));


            WeatherProAndRealDTO rain = new WeatherProAndRealDTO();
            rain.setDate(dateList.get(i));


            WeatherProAndRealDTO humidity = new WeatherProAndRealDTO();
            humidity.setDate(dateList.get(i));

            WeatherProAndRealDTO winds = new WeatherProAndRealDTO();
            winds.setDate(dateList.get(i));

            WeatherProAndRealDTO effectiveTemperature = new WeatherProAndRealDTO();
            effectiveTemperature.setDate(dateList.get(i));

            WeatherProAndRealDTO coldness = new WeatherProAndRealDTO();
            coldness.setDate(dateList.get(i));

            if (wetherFeatureHisStatDO != null) {
                temperature.setRealLow(wetherFeatureHisStatDO.getLowestTemperature());
                temperature.setRealHigh(wetherFeatureHisStatDO.getHighestTemperature());
                temperature.setRealAvg(wetherFeatureHisStatDO.getAveTemperature());
                rain.setRealHigh(wetherFeatureHisStatDO.getRainfall());
                humidity.setRealLow(wetherFeatureHisStatDO.getLowestHumidity());
                humidity.setRealHigh(wetherFeatureHisStatDO.getHighestHumidity());
                humidity.setRealAvg(wetherFeatureHisStatDO.getAveHumidity());
                winds.setRealLow(wetherFeatureHisStatDO.getMinWinds());
                winds.setRealHigh(wetherFeatureHisStatDO.getMaxWinds());
                winds.setRealAvg(wetherFeatureHisStatDO.getAveWinds());
                effectiveTemperature.setRealLow(wetherFeatureHisStatDO.getLowestEffectiveTemperature());
                effectiveTemperature.setRealHigh(wetherFeatureHisStatDO.getHighestEffectiveTemperature());
                effectiveTemperature.setRealAvg(wetherFeatureHisStatDO.getAveEffectiveTemperature());
                coldness.setRealLow(wetherFeatureHisStatDO.getMinColdness());
                coldness.setRealHigh(wetherFeatureHisStatDO.getMaxColdness());
                coldness.setRealAvg(wetherFeatureHisStatDO.getAveColdness());
            }
            if (weatherFeaturePreDO != null) {
                temperature.setPreLow(weatherFeaturePreDO.getLowestTemperature());
                temperature.setPreHigh(weatherFeaturePreDO.getHighestTemperature());
                temperature.setPreAvg(weatherFeaturePreDO.getAveTemperature());
                rain.setPreHigh(weatherFeaturePreDO.getRainfall());
                humidity.setPreLow(weatherFeaturePreDO.getLowestHumidity());
                humidity.setPreHigh(weatherFeaturePreDO.getHighestHumidity());
                humidity.setPreAvg(weatherFeaturePreDO.getAveHumidity());
                winds.setPreLow(weatherFeaturePreDO.getMinWinds());
                winds.setPreHigh(weatherFeaturePreDO.getMaxWinds());
                winds.setPreAvg(weatherFeaturePreDO.getAveWinds());
                effectiveTemperature.setPreLow(weatherFeaturePreDO.getLowestEffectiveTemperature());
                effectiveTemperature.setPreHigh(weatherFeaturePreDO.getHighestEffectiveTemperature());
                effectiveTemperature.setPreAvg(weatherFeaturePreDO.getAveEffectiveTemperature());
                coldness.setPreLow(weatherFeaturePreDO.getMinColdness());
                coldness.setPreHigh(weatherFeaturePreDO.getMaxColdness());
                coldness.setPreAvg(weatherFeaturePreDO.getAveColdness());
            }

            assessmentDTO.setTem(temperature);
            assessmentDTO.setRain(rain);
            assessmentDTO.setHumidity(humidity);
            assessmentDTO.setWinds(winds);
            assessmentDTO.setEffectiveTemperature(effectiveTemperature);
            assessmentDTO.setColdness(coldness);
            resultList.add(assessmentDTO);
        }
        if (resultList == null || resultList.size() == 0) {
            throw TsieExceptionUtils.newBusinessException("T706");
        }
        return resultList;
    }

    @Override
    public WeatherPreAccuracyDTO getAccuracyStatiesByDate(String cityId, Date startDate, Date endDate, String type)
            throws Exception {

        List<Map<String, Object>> weatherPredictionDataByDate = findWeatherPredictionDataByDate(cityId, startDate,
                endDate, "2");
        WeatherPreAccuracyDTO weatherAccuracyResult = getWeatherAccuracyResult(weatherPredictionDataByDate, cityId,
                null, null, null);
        return weatherAccuracyResult;
    }


    @Override
    public List<WeatherDayDeviationDTO> getDayDeviation(String cityId, String startDate, String endDate, String type)
            throws Exception {

        cityId = cityService.findWeatherCityId(cityId);
        //表示按天查询
        List<WeatherDayDeviationDTO> resultList = new ArrayList<WeatherDayDeviationDTO>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date startTime = sdf.parse(startDate);
        Date endTime = sdf.parse(endDate);

        //历史气象特性统计
        List<WeatherFeatureCityDayHisDO> hisWeatherFeature = weatherFeatureCityDayHisService
                .listWeatherFeatureCityDayHisDO(cityId, startTime, endTime);
        Map<java.sql.Date, WeatherFeatureCityDayHisDO> weatherFeatureHisMap = hisWeatherFeature.stream().collect(
                Collectors
                        .toMap(WeatherFeatureCityDayHisDO::getDate, WeatherFeatureCityDayHisDO -> WeatherFeatureCityDayHisDO));
        //预测气象统计
        DataPackage dataPackage = weatherFeatureCityDayFcService
                .listWeatherFeatureCityDayByPage(cityId, startTime, endTime);
        List<WeatherFeatureCityDayFcDO> datas = dataPackage.getDatas();
        Map<java.sql.Date, WeatherFeatureCityDayFcDO> weatherFeaturePreMap = datas.stream().collect(Collectors
                .toMap(WeatherFeatureCityDayFcDO::getDate, WeatherFeatureCityDayFcDO -> WeatherFeatureCityDayFcDO));

        if (weatherFeatureHisMap.size() == 0 && weatherFeaturePreMap.size() == 0) {
            throw TsieExceptionUtils.newBusinessException("T706");
        }
        //通过dateList返回日期列表的顺序，来控制返回前端数据的顺序
        List<String> dateList = getDayBetween(startDate, endDate);
        for (int i = 0; i < dateList.size(); i++) {
            WeatherFeatureCityDayFcDO weatherFeaturePreDO = weatherFeaturePreMap
                    .get(new java.sql.Date(sdf.parse(dateList.get(i)).getTime()));
            WeatherFeatureCityDayHisDO wetherFeatureHisStatDO = weatherFeatureHisMap
                    .get(new java.sql.Date(sdf.parse(dateList.get(i)).getTime()));
            //单日当预测或者真实数据为空时也要展示，不能返回0(0也是数，表明存在)，所以这里新创建对象，属性值为null;
            if (weatherFeaturePreDO == null) {
                weatherFeaturePreDO = new WeatherFeatureCityDayFcDO();
                continue;
            }
            if (wetherFeatureHisStatDO == null) {
                wetherFeatureHisStatDO = new WeatherFeatureCityDayHisDO();
                continue;
            }


            WeatherDayDeviationDTO dto = new WeatherDayDeviationDTO();
            dto.setDate(sdf.parse(dateList.get(i)));

            BigDecimal realAvgTempeture = wetherFeatureHisStatDO.getAveTemperature();
            BigDecimal preAvgTempeture = weatherFeaturePreDO.getAveTemperature();
//            if (realAvgTempeture ==null) {
//                realAvgTempeture = BigDecimal.ZERO;
//            }
//            if (weatherFeaturePreDO.getAveTemperature() == null) {
//                preAvgTempeture = BigDecimal.ZERO;
//            }

            if (realAvgTempeture != null && preAvgTempeture != null) {
                dto.setAvgTemDayDeviation(preAvgTempeture.subtract(realAvgTempeture).setScale(2, BigDecimal.ROUND_HALF_UP));
            }


            BigDecimal realLowTempeture = wetherFeatureHisStatDO.getLowestTemperature();
            BigDecimal preLowTempeture = weatherFeaturePreDO.getLowestTemperature();
//            if (wetherFeatureHisStatDO.getLowestTemperature() == null) {
//                realLowTempeture = BigDecimal.ZERO;
//            }
//            if (weatherFeaturePreDO.getLowestTemperature() == null) {
//                preLowTempeture = BigDecimal.ZERO;
//            }
            if (realLowTempeture != null && preLowTempeture != null) {
                dto.setLowTemDayDeviation(preLowTempeture.subtract(realLowTempeture).setScale(2, BigDecimal.ROUND_HALF_UP));
            }


            BigDecimal realHighTempeture = wetherFeatureHisStatDO.getHighestTemperature();
            BigDecimal preHighTempeture = weatherFeaturePreDO.getHighestTemperature();
//            if (wetherFeatureHisStatDO.getHighestTemperature() == null) {
//                realHighTempeture = BigDecimal.ZERO;
//            }
//            if (weatherFeaturePreDO.getHighestTemperature() == null) {
//                preHighTempeture = BigDecimal.ZERO;
//            }

            if (realHighTempeture != null && preHighTempeture != null) {
                dto.setHighTemDayDeviation(
                        preHighTempeture.subtract(realHighTempeture).setScale(2, BigDecimal.ROUND_HALF_UP));
            }

            resultList.add(dto);
        }
        return resultList;
    }


    @Override
    public List<PeakLoadDeviationMonitorDTO> getPeakLoadDeviationMonitorDTOList(List<String> cityIds, String caliberId, Date date) throws Exception {
        List<PeakLoadDeviationMonitorDTO> dtoList = new ArrayList<>(cityIds.size());
        // cityId 为1 表示获取全网实际负荷的最大时刻
        List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisDOList = loadFeatureCityDayHisService.findLoadFeatureCityDayHisDOS("1", date, date, caliberId);
        Map<java.sql.Date, List<LoadFeatureCityDayHisDO>> loadFeatureCityDayHisDOMap = new HashMap<>();
        if (loadFeatureCityDayHisDOList != null) {
            loadFeatureCityDayHisDOMap = loadFeatureCityDayHisDOList.stream().collect(Collectors.groupingBy(LoadFeatureCityDayHisDO::getDate));
        }
        java.sql.Date sqlDate = new java.sql.Date(date.getTime());
        List<LoadFeatureCityDayHisDO> cityDayHisDOList = loadFeatureCityDayHisDOMap.get(sqlDate);
        // 全网实际负荷的最大时刻
        String loadHisMaxTime = "";
        if (cityDayHisDOList != null) {
            if (cityDayHisDOList.get(0) != null) {
                loadHisMaxTime = cityDayHisDOList.get(0).getMaxTime();
            }
        }
        String methodName = "";
        if (!"".equals(loadHisMaxTime)) {
            methodName = accuracyLoadCityFcRecallService.getMethodName(loadHisMaxTime);
        }

        // 获取实际高峰
        List<LoadCityHisDO> loadCityHisDOSByCityIdList = loadCityHisService.getLoadCityHisDOSByCityIds(cityIds, date, date, caliberId);
        Map<String, List<LoadCityHisDO>> loadCityHisDOSByCityIdMap = new HashMap<>();
        if (loadCityHisDOSByCityIdList != null){
            loadCityHisDOSByCityIdMap = loadCityHisDOSByCityIdList.stream().collect(Collectors.groupingBy(LoadCityHisDO::getCityId));
        }

        // 获取预测高峰
        List<LoadCityFcDO> loadCityFcDOList = loadCityFcService.findLoadCityFc(cityIds, date, date, caliberId);
        Map<String, List<LoadCityFcDO>> loadCityFcDOMap = new HashMap<>();
        if (loadCityFcDOList != null){
            loadCityFcDOMap = loadCityFcDOList.stream().collect(Collectors.groupingBy(LoadCityFcDO::getCityId));
        }

        // 获取高峰预测准确率
        List<AccuracyLoadCityFcDO> accuracyLoadCityFcDOList = accuracyLoadCityFcService.getAccuracyLoadCityFcDOList(cityIds, "0", date, date, caliberId, null);
        Map<String, List<AccuracyLoadCityFcDO>> accuracyLoadCityFcDOMap = new HashMap<>();
        if (accuracyLoadCityFcDOList != null){
            accuracyLoadCityFcDOMap = accuracyLoadCityFcDOList.stream().collect(Collectors.groupingBy(AccuracyLoadCityFcDO::getCityId));
        }

        Map<String, BigDecimal> gtZeroDeviationMap = new HashMap<>();
        Map<String, BigDecimal> ltZeroDeviationMap = new HashMap<>();

        for (String cityId : cityIds) {
            PeakLoadDeviationMonitorDTO monitorDTO = new PeakLoadDeviationMonitorDTO();
            monitorDTO.setCityId(cityId);

            CityDO cityDO = cityService.findCityById(cityId);
            if (cityDO != null && cityDO.getCity() != null) {
                monitorDTO.setCityName(cityDO.getCity());
            }

            List<LoadCityHisDO> cityHisDOS = loadCityHisDOSByCityIdMap.get(cityId);
            BigDecimal actualPeak = null;
            if (cityHisDOS != null && cityHisDOS.get(0) != null) {
                if (methodName != null && !"".equals(methodName)) {
                    actualPeak = accuracyLoadCityFcRecallService.getBigDecimalVal(cityHisDOS.get(0), methodName);
                    if (actualPeak != null) {
                        monitorDTO.setPeakActualLoad(actualPeak);
                    }
                }
            }


            List<LoadCityFcDO> cityFcDOS = loadCityFcDOMap.get(cityId);
            BigDecimal fcPeak = null;
            if (cityFcDOS != null && cityFcDOS.get(0) != null) {
                if (methodName != null && !"".equals(methodName)) {
                    fcPeak = accuracyLoadCityFcRecallService.getBigDecimalVal(cityFcDOS.get(0), methodName);
                    if(fcPeak != null){
                        monitorDTO.setPeakForecastLoad(fcPeak);
                    }
                }
            }

            if (actualPeak != null && fcPeak != null) {
                BigDecimal deviation = fcPeak.subtract(actualPeak);
                monitorDTO.setForecastDeviation(deviation);
                if (deviation.compareTo(new BigDecimal("0")) == 1) {
                    gtZeroDeviationMap.put(cityId, deviation);
                } else {
                    ltZeroDeviationMap.put(cityId, deviation);
                }
            }

            List<AccuracyLoadCityFcDO> fcDOS = accuracyLoadCityFcDOMap.get(cityId);
            BigDecimal accuracy = null;
            if (fcDOS != null && fcDOS.get(0) != null) {
                if (methodName != null && !"".equals(methodName)) {
                    accuracy = accuracyLoadCityFcRecallService.getBigDecimalVal(fcDOS.get(0), methodName);
                    if (accuracy != null) {
                        monitorDTO.setPeakAccuracy(accuracy);
                    }
                }
            }
            dtoList.add(monitorDTO);
        }


        BigDecimal gtZeroSum = gtZeroDeviationMap.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal ltZeroSum = ltZeroDeviationMap.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);

        for (PeakLoadDeviationMonitorDTO dto : dtoList) {

            if (dto.getForecastDeviation() != null) {
                if (dto.getForecastDeviation().compareTo(new BigDecimal("0")) == 1) {
                    if (gtZeroDeviationMap.containsKey(dto.getCityId())) {
                        dto.setDeviation(dto.getForecastDeviation().divide(gtZeroSum, 4));
                    }
                } else {
                    if (ltZeroDeviationMap.containsKey(dto.getCityId())) {
                        dto.setDeviation(dto.getForecastDeviation().divide(ltZeroSum, 4).negate());
                    }
                }
            }
        }

        // 默认按照高峰准确率降序
        ArrayList<PeakLoadDeviationMonitorDTO> sortList = new ArrayList<>(dtoList.size());
        for (PeakLoadDeviationMonitorDTO dto : dtoList){
            if (dto.getPeakAccuracy() != null){
                sortList.add(dto);
            }
        }
        ArrayList<PeakLoadDeviationMonitorDTO> sortedList = new ArrayList<>(sortList.size());
        sortList.stream().sorted(Comparator.comparing(PeakLoadDeviationMonitorDTO::getPeakAccuracy).reversed()).collect(Collectors.toList()).forEach(t -> sortedList.add(t));
        return sortedList;
    }

    @Override
    public PeakLoadDTO getPeakLoadDTO(String cityId, String caliberId, Date startDate, Date endDate) throws Exception {
        // cityId 为1 表示获取全网实际负荷的最大时刻
        List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisDOList = loadFeatureCityDayHisService.findLoadFeatureCityDayHisDOS("1", startDate, endDate, caliberId);
        Map<java.sql.Date, List<LoadFeatureCityDayHisDO>> loadFeatureCityDayHisDOMap = new HashMap<>();
        if (loadFeatureCityDayHisDOList != null){
            loadFeatureCityDayHisDOMap = loadFeatureCityDayHisDOList.stream().collect(Collectors.groupingBy(LoadFeatureCityDayHisDO::getDate));
        }

        // 实际高峰
        List<LoadCityHisDO> loadCityHisDOList = loadCityHisService.getLoadCityHisDOS(cityId, caliberId, startDate, endDate);
        Map<java.sql.Date, List<LoadCityHisDO>> loadCityHisDOMap = new HashMap<>();
        if (loadCityHisDOList != null){
            loadCityHisDOMap = loadCityHisDOList.stream().collect(Collectors.groupingBy(LoadCityHisDO::getDate));
        }


        // 预测高峰
        List<LoadCityFcDO> loadCityFcDOList = loadCityFcService.listLoadCityFc(cityId, caliberId, startDate, endDate, "0");
        Map<java.sql.Date, List<LoadCityFcDO>> loadCityFcDOMap = new HashMap<>();
        if (loadCityFcDOList != null){
            loadCityFcDOMap = loadCityFcDOList.stream().collect(Collectors.groupingBy(LoadCityFcDO::getDate));
        }


        //预测气温
        List<WeatherCityFcDO> weatherCityFcDOList = weatherCityFcService.findWeatherCityFcDOs(cityId, 2, startDate, endDate);
        Map<java.sql.Date, List<WeatherCityFcDO>> weatherCityFcDOMap = new HashMap<>();
        if (weatherCityFcDOList != null){
            weatherCityFcDOMap = weatherCityFcDOList.stream().collect(Collectors.groupingBy(WeatherCityFcDO::getDate));
        }


        // 实际气温
        List<WeatherCityHisDO> weatherCityHisDOList = weatherCityHisService.findWeatherCityHisDOs(cityId,2,startDate,endDate);
        Map<java.sql.Date, List<WeatherCityHisDO>> weatherCityHisDOMap = new HashMap<>();
        if (weatherCityHisDOList != null){
            weatherCityHisDOMap = weatherCityHisDOList.stream().collect(Collectors.groupingBy(WeatherCityHisDO::getDate));
        }

        List<Date> dateList = DateUtil.getListBetweenDay(startDate, endDate);
        ArrayList<BigDecimal> forecastPeakList = new ArrayList<>(dateList.size());
        ArrayList<BigDecimal> actualPeakList = new ArrayList<>(dateList.size());
        ArrayList<BigDecimal> forecastTemperatureList = new ArrayList<>(dateList.size());
        ArrayList<BigDecimal> actualTemperatureList = new ArrayList<>(dateList.size());
        PeakLoadDTO peakLoadDTO = new PeakLoadDTO();
        for (Date date : dateList) {
            java.sql.Date sqlDate = new java.sql.Date(date.getTime());
            List<LoadFeatureCityDayHisDO> hisDOS = loadFeatureCityDayHisDOMap.get(sqlDate);
            String loadHisMaxTime = "";
            String methodName = "";
            if (hisDOS != null && hisDOS.get(0) != null && hisDOS.get(0).getMaxTime() != null) {
                loadHisMaxTime = hisDOS.get(0).getMaxTime();
            }
            if (!"".equals(loadHisMaxTime)) {
                methodName = accuracyLoadCityFcRecallService.getMethodName(loadHisMaxTime);
            }

            List<LoadCityHisDO> hisDOList = loadCityHisDOMap.get(sqlDate);
            if (hisDOList != null && hisDOList.get(0) != null && !"".equals(methodName)) {
                BigDecimal actualPeak = accuracyLoadCityFcRecallService.getBigDecimalVal(hisDOList.get(0), methodName);
                if (actualPeak != null) {
                    actualPeakList.add(actualPeak);
                }
            }

            List<LoadCityFcDO> cityFcDOS = loadCityFcDOMap.get(sqlDate);
            if (cityFcDOS != null && cityFcDOS.get(0) != null && !"".equals(methodName)) {
                BigDecimal forecastPeak = accuracyLoadCityFcRecallService.getBigDecimalVal(cityFcDOS.get(0), methodName);
                if (forecastPeak != null) {
                    forecastPeakList.add(forecastPeak);
                }
            }

            List<WeatherCityFcDO> fcDOS = weatherCityFcDOMap.get(sqlDate);
            if (fcDOS != null) {
                WeatherCityFcDO weatherCityFcDO = fcDOS.get(0);
                if (weatherCityFcDO != null && !"".equals(methodName)){
                    BigDecimal forecastTemperature = accuracyLoadCityFcRecallService.getBigDecimalVal(weatherCityFcDO, methodName);
                    if (forecastTemperature != null) {
                        forecastTemperatureList.add(forecastTemperature);
                    }
                }

            }

            List<WeatherCityHisDO> cityHisDOS = weatherCityHisDOMap.get(sqlDate);
            if (cityHisDOS != null && cityHisDOS.get(0) != null && !"".equals(methodName)) {
                BigDecimal actualTemperature = accuracyLoadCityFcRecallService.getBigDecimalVal(cityHisDOS.get(0), methodName);
                if (actualTemperature != null) {
                    actualTemperatureList.add(actualTemperature);
                }
            }
        }
        peakLoadDTO.setActualPeakList(actualPeakList);
        peakLoadDTO.setActualTemperatureList(actualTemperatureList);
        peakLoadDTO.setForecastPeakList(forecastPeakList);
        peakLoadDTO.setForecastTemperatureList(forecastTemperatureList);

        return peakLoadDTO;
    }

    @Override
    public List<DayFcEvaluateDTO> getDayFcEvaluateDTOList(List<String> cityIds, Date timeDate, String caliberId)throws Exception {
        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd");
        Date date = sdf1.parse(sdf1.format(timeDate));

        // 预测负荷
        List<LoadCityFcDO> loadCityFcList = loadCityFcService.findLoadCityFc(cityIds, date, date, caliberId);
        Map<String, List<LoadCityFcDO>> loadCityFcMap = new HashMap<>();
        if (loadCityFcList != null){
            loadCityFcMap = loadCityFcList.stream().collect(Collectors.groupingBy(LoadCityFcDO::getCityId));
        }

        // 实际负荷
        List<LoadCityHisDO> loadByCityIds = loadCityHisService.getLoadCityHisDOSByCityIds(cityIds, date, date, caliberId);
        Map<String, List<LoadCityHisDO>> loadByCityIdsMap = new HashMap<>();
        if (loadByCityIds != null){
            loadByCityIdsMap = loadByCityIds.stream().collect(Collectors.groupingBy(LoadCityHisDO::getCityId));
        }


        // 预测气温
        List<WeatherCityFcDO> weatherCityFcDOList = weatherCityFcService.getWeatherCityFcDOList(cityIds, 2, date, date);
        Map<String, List<WeatherCityFcDO>> weatherCityFcDOMap = new HashMap<>();
        if (weatherCityFcDOList != null){
            weatherCityFcDOMap = weatherCityFcDOList.stream().collect(Collectors.groupingBy(WeatherCityFcDO::getCityId));
        }


        // 实际气温 气象类型 2 为温度
        List<WeatherCityHisDO> cityHisDOSByCityIds = weatherCityHisService.findWeatherCityHisDOSByCityIds(cityIds, 2, date);
        Map<String, List<WeatherCityHisDO>> cityHisDOSByCityIdsMap = new HashMap<>();
        if (cityHisDOSByCityIds != null){
            cityHisDOSByCityIdsMap = cityHisDOSByCityIds.stream().collect(Collectors.groupingBy(WeatherCityHisDO::getCityId));
        }

        // 计算考核标准
        List<StatisticsCityDayFcDO> statisticsCityDayFcDOList = statisticsCityDayFcDAO.getStatisticsCityDayFcDOs(cityIds,caliberId,"0",date,null);
        Map<String, List<StatisticsCityDayFcDO>> statisticsCityDayFcDOMap = new HashMap<>();
        if (statisticsCityDayFcDOList != null){
            statisticsCityDayFcDOMap = statisticsCityDayFcDOList.stream().collect(Collectors.groupingBy(StatisticsCityDayFcDO::getCityId));
        }

        List<DayFcEvaluateDTO> dayFcEvaluateDTOList = new ArrayList<>();
        for (String cityId : cityIds){
            DayFcEvaluateDTO evaluateDTO = new DayFcEvaluateDTO();
            evaluateDTO.setCityName(cityService.findCityById(cityId).getCity());
            List<BigDecimal> loadFcList = new ArrayList<>();
            List<BigDecimal> loadHisList = new ArrayList<>();
            List<BigDecimal> temperateFcList = new ArrayList<>();
            List<BigDecimal> temperateHisList = new ArrayList<>();

            List<StatisticsCityDayFcDO> cityDayFcDOS = statisticsCityDayFcDOMap.get(cityId);
            if (cityDayFcDOS != null){
                StatisticsCityDayFcDO dayFcDO = cityDayFcDOS.get(0);
                if (dayFcDO != null){
                    BigDecimal standardAccuracy = dayFcDO.getStandardAccuracy();
                    BigDecimal accuracy = dayFcDO.getAccuracy();
                    if (accuracy != null){
                        evaluateDTO.setPrecisionFc(accuracy);
                    }
                    if (standardAccuracy != null){
                        evaluateDTO.setStandard(standardAccuracy);
                    }
                }
            }

            List<LoadCityFcDO> fcDOS = loadCityFcMap.get(cityId);
            if (fcDOS != null && fcDOS.get(0) != null){
                loadFcList = BasePeriodUtils
                        .toList(fcDOS.get(0), 96, Constants.LOAD_CURVE_START_WITH_ZERO);
            }
            if (loadFcList != null){
                evaluateDTO.setLoadFcList(loadFcList);
            }

            List<LoadCityHisDO> hisDOS = loadByCityIdsMap.get(cityId);
            if (hisDOS != null && hisDOS.get(0) != null){
                loadHisList = BasePeriodUtils
                        .toList(hisDOS.get(0), 96, Constants.LOAD_CURVE_START_WITH_ZERO);
            }
            if (loadHisList != null){
                evaluateDTO.setLoadHisList(loadHisList);
            }

            List<WeatherCityFcDO> weatherCityFcDOS = weatherCityFcDOMap.get(cityId);
            if (weatherCityFcDOS != null && weatherCityFcDOS.get(0) != null){
                temperateFcList = BasePeriodUtils
                        .toList(weatherCityFcDOS.get(0), 96, Constants.LOAD_CURVE_START_WITH_ZERO);
            }
            if (temperateFcList != null){
                evaluateDTO.setTemperateFcList(temperateFcList);
            }

            List<WeatherCityHisDO> weatherCityHisDOS = cityHisDOSByCityIdsMap.get(cityId);
            if (weatherCityHisDOS != null && weatherCityHisDOS.get(0) != null){
                temperateHisList = BasePeriodUtils
                        .toList(weatherCityHisDOS.get(0), 96, Constants.LOAD_CURVE_START_WITH_ZERO);
            }
            if (temperateHisList != null){
                evaluateDTO.setTemperateHisList(temperateHisList);
            }
            dayFcEvaluateDTOList.add(evaluateDTO);
        }
        // 按照standard降序排列
        for(int i = 0;i<dayFcEvaluateDTOList.size()-1;i++){
            for (int j=0;j<dayFcEvaluateDTOList.size()-i-1;j++){
                if (dayFcEvaluateDTOList.get(j).getStandard() != null && dayFcEvaluateDTOList.get(j+1).getStandard() != null){
                    if (dayFcEvaluateDTOList.get(j).getStandard().compareTo(dayFcEvaluateDTOList.get(j+1).getStandard()) == 1){
                        DayFcEvaluateDTO dayFcEvaluateDTO = dayFcEvaluateDTOList.get(j);
                        dayFcEvaluateDTOList.set(j,dayFcEvaluateDTOList.get(j+1));
                        dayFcEvaluateDTOList.set(j+1,dayFcEvaluateDTO);
                    }
                }
            }
        }
        return dayFcEvaluateDTOList;
    }


    public List<Map<String, Object>> findWeatherPredictionDataByDate(String cityId, Date startDate, Date endDate,
                                                                     String type) throws Exception {
        List<Map<String, Object>> dataList = new ArrayList<>();
        List<PredictionAssessmentDTO> accuracyByDay = findWeatherPredictionAccuracyBaseByDay(cityId, startDate, endDate,
                type);
        for (PredictionAssessmentDTO assessmentDTO : accuracyByDay) {
            //计算每天的数据，比如是否准确
            Map<String, Object> map = new HashMap<>();
            WeatherProAndRealDTO dto = assessmentDTO.getTem();
            //高温
            BigDecimal realHighTempeture = dto.getRealHigh();
            BigDecimal predictionHighTempeture = dto.getPreHigh();
            if (predictionHighTempeture != null && realHighTempeture != null) {
                if (predictionHighTempeture.subtract(realHighTempeture).abs().compareTo(BigDecimal.ONE) < 0) {
                    //1表示准确
                    map.put("isHighTempetureaccurately", 1);
                } else {
                    //0表示不准确，偏高或者偏低
                    map.put("isHighTempetureaccurately", 0);

                    //最高温预测偏高
                    if (predictionHighTempeture.subtract(realHighTempeture).compareTo(BigDecimal.ONE) >= 0) {
                        map.put("isHighTempetureHigh", 1);
                        map.put("isHighTempetureHighValue",
                                predictionHighTempeture.subtract(realHighTempeture).setScale(2, BigDecimal.ROUND_HALF_UP));
                    } else {
                        map.put("isHighTempetureHigh", 0);
                    }

                    //最高温预测偏低
                    if (predictionHighTempeture.subtract(realHighTempeture).compareTo(BigDecimal.ONE) <= 0) {
                        map.put("isHighTempetureLow", 1);
                        map.put("isHighTempetureLowValue",
                                realHighTempeture.subtract(predictionHighTempeture).setScale(2, BigDecimal.ROUND_HALF_UP));
                    } else {
                        map.put("isHighTempetureLow", 0);
                    }
                }


                //低温
                BigDecimal realLowTempeture = dto.getRealLow();
                BigDecimal predictionLowTempeture = dto.getPreLow();
                if (predictionLowTempeture != null && realLowTempeture != null) {
                    if (predictionLowTempeture.subtract(realLowTempeture).abs().compareTo(BigDecimal.ONE) < 0) {
                        //1表示准确
                        map.put("isLowTempetureaccurately", 1);
                    } else {
                        //0表示不准确 偏高或偏低
                        map.put("isLowTempetureaccurately", 0);

                        //最低温预测偏高
                        if (predictionLowTempeture.subtract(realLowTempeture).compareTo(BigDecimal.ONE) >= 0) {
                            map.put("isLowTempetureHigh", 1);
                            map.put("isLowTempetureHighValue",
                                    predictionLowTempeture.subtract(realLowTempeture)
                                            .setScale(2, BigDecimal.ROUND_HALF_UP));
                        } else {
                            map.put("isLowTempetureHigh", 0);
                        }
                        //最低温预测偏低
                        if (predictionLowTempeture.subtract(realLowTempeture).compareTo(BigDecimal.ONE) <= 0) {
                            map.put("isLowTempetureLow", 1);
                            map.put("isLowTempetureLowValue",
                                    realLowTempeture.subtract(predictionLowTempeture)
                                            .setScale(2, BigDecimal.ROUND_HALF_UP));
                        } else {
                            map.put("isLowTempetureLow", 0);
                        }
                    }
                }
            }

            //平均温度
            BigDecimal realAvgTempeture = dto.getRealAvg();
            BigDecimal predictionAvgTempeture = dto.getPreAvg();
            if (realAvgTempeture != null && predictionAvgTempeture != null) {
                if (predictionAvgTempeture.subtract(realAvgTempeture).abs().compareTo(BigDecimal.ONE) < 0) {
                    //1表示准确
                    map.put("isAvgTempetureaccurately", 1);
                } else {
                    //0表示不准确 偏高或偏低
                    map.put("isAvgTempetureaccurately", 0);

                    //平均温度预测偏高
                    if (predictionAvgTempeture.subtract(realAvgTempeture).compareTo(BigDecimal.ONE) >= 0) {
                        map.put("isAvgTempetureHigh", 1);
                        map.put("isAvgTempetureHighValue",
                                predictionAvgTempeture.subtract(realAvgTempeture).setScale(2, BigDecimal.ROUND_HALF_UP));
                    } else {
                        map.put("isAvgTempetureHigh", 0);
                    }
                    //平均温度预测偏低
                    if (predictionAvgTempeture.subtract(realAvgTempeture).compareTo(BigDecimal.ONE) <= 0) {
                        map.put("isAvgTempetureLow", 1);
                        map.put("isAvgTempetureLowValue",
                                realAvgTempeture.subtract(predictionAvgTempeture).setScale(2, BigDecimal.ROUND_HALF_UP));
                    } else {
                        map.put("isAvgTempetureLow", 0);
                    }
                }
            }
            map.put("date", dto.getDate());
            dataList.add(map);
        }
        return dataList;
    }


    public WeatherPreAccuracyDTO getWeatherAccuracyResult(List<Map<String, Object>> data, String cityId,
                                                          String cityName, Calendar start, SimpleDateFormat sdf) throws Exception {

        WeatherPreAccuracyDTO weatherPreAccuracyDTO = new WeatherPreAccuracyDTO();
        WeatherAvgTemAccuracyDTO weatherAvgTemAccuracyDTO = new WeatherAvgTemAccuracyDTO();
        WeatherLowTemAccuracyDTO weatherLowTemAccuracyDTO = new WeatherLowTemAccuracyDTO();
        WeatherHighTemAccuracyDTO weatherHighTemAccuracyDTO = new WeatherHighTemAccuracyDTO();

        String highTempetureAccurateLyKey = "isHighTempetureaccurately";
        String highTempetureHighFalgKey = "isHighTempetureHigh";
        String highTempetureLowFalgKey = "isHighTempetureLow";
        String highTempetureHighFalgValueKey = "isHighTempetureHighValue";
        String highTempetureLowFalgValueKey = "isHighTempetureLowValue";
        weatherHighTemAccuracyDTO = getTypeDate(data, highTempetureAccurateLyKey, highTempetureHighFalgKey,
                highTempetureLowFalgKey, highTempetureHighFalgValueKey, highTempetureLowFalgValueKey,
                weatherHighTemAccuracyDTO);

        String lowTempetureAccurateLyKey = "isLowTempetureaccurately";
        String lowTempetureHighFalgKey = "isLowTempetureHigh";
        String lowTempetureLowFalgKey = "isLowTempetureLow";
        String lowTempetureHighFalgValueKey = "isLowTempetureHighValue";
        String lowTempetureLowFalgValueKey = "isLowTempetureLowValue";
        weatherLowTemAccuracyDTO = getTypeDate(data, lowTempetureAccurateLyKey, lowTempetureHighFalgKey,
                lowTempetureLowFalgKey, lowTempetureHighFalgValueKey, lowTempetureLowFalgValueKey,
                weatherLowTemAccuracyDTO);

        String avgTempetureAccurateLyKey = "isAvgTempetureaccurately";
        String avgTempetureHighFalgKey = "isAvgTempetureHigh";
        String avgTempetureLowFalgKey = "isAvgTempetureLow";
        String avgTempetureHighFalgValueKey = "isAvgTempetureHighValue";
        String avgTempetureLowFalgValueKey = "isAvgTempetureLowValue";
        weatherAvgTemAccuracyDTO = getTypeDate(data, avgTempetureAccurateLyKey, avgTempetureHighFalgKey,
                avgTempetureLowFalgKey, avgTempetureHighFalgValueKey, avgTempetureLowFalgValueKey,
                weatherAvgTemAccuracyDTO);

        weatherPreAccuracyDTO.setAvgTempeture(weatherAvgTemAccuracyDTO);
        weatherPreAccuracyDTO.setHighTempeture(weatherHighTemAccuracyDTO);
        weatherPreAccuracyDTO.setLowTempeture(weatherLowTemAccuracyDTO);

        weatherPreAccuracyDTO.setCityId(cityId);
        if (sdf != null && start != null) {
            weatherPreAccuracyDTO.setDate(sdf.format(start.getTime()));
        }
        return weatherPreAccuracyDTO;
    }


    public <T> T getTypeDate(List<Map<String, Object>> data, String accuracyatelyKey, String highFlagKey,
                             String lowFlagKey, String highFlagValueKey, String lowFlagValueKey, T bean) throws Exception {

        Map<String, Object> typeMap = new HashMap<>();
        //预测准确天数
        int countAccuracylyDate = 0;
        //预测偏高天数和偏高的差值
        int countHigh = 0;
        BigDecimal countHighValue = BigDecimal.ZERO;
        //预测偏低天数和偏低的差值
        int countLow = 0;
        BigDecimal countLowValue = BigDecimal.ZERO;
        //计算总计有数据的天数
        int count = 0;

        for (Map<String, Object> map : data) {
            if (map.get(accuracyatelyKey) == null) {
                continue;
            }
            count++;
            if (1 == (Integer) map.get(accuracyatelyKey)) {
                countAccuracylyDate++;
            } else {
                if (1 == (Integer) map.get(highFlagKey)) {
                    countHigh++;
                    countHighValue = countHighValue.add((BigDecimal) map.get(highFlagValueKey));
                } else if (1 == (Integer) map.get(lowFlagKey)) {
                    countLow++;
                    countLowValue = countLowValue.add((BigDecimal) map.get(lowFlagValueKey));
                }
            }
        }


        Field accuracyRate = bean.getClass().getDeclaredField("accuracyRate");
        accuracyRate.setAccessible(true);
        if (countAccuracylyDate == 0) {
            accuracyRate.set(bean, 0.0 * 100);
        } else {
            accuracyRate.set(bean,
                    new BigDecimal(countAccuracylyDate * 1.0 / count * 100).setScale(2, BigDecimal.ROUND_HALF_UP)
                            .doubleValue());
        }

        Field highSide = bean.getClass().getDeclaredField("highSide");
        highSide.setAccessible(true);
        if (countHigh == 0) {
            highSide.set(bean, 0.0 * 100);
        } else {
            highSide.set(bean, new BigDecimal(countHigh * 1.0 / count * 100).setScale(2, BigDecimal.ROUND_HALF_UP)
                    .doubleValue());
        }


        Field lowSide = bean.getClass().getDeclaredField("lowSide");
        lowSide.setAccessible(true);
        if (countLow == 0) {
            lowSide.set(bean, 0.0 * 100);
        } else {

            lowSide.set(bean,
                    new BigDecimal(countLow * 1.0 / count * 100).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
        }
        return bean;
    }


    //获得俩字符串日期之间的日期(天)
    public List<String> getDayBetween(String minDate, String maxDate) throws Exception {
        ArrayList<String> result = new ArrayList<String>();
        //格式化为年月
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        Calendar min = Calendar.getInstance();
        Calendar max = Calendar.getInstance();

        min.setTime(sdf.parse(minDate));
        max.setTime(sdf.parse(maxDate));
        max.add(Calendar.DATE, 1);

        Calendar curr = min;
        while (curr.before(max)) {

            result.add(sdf.format(curr.getTime()));
            curr.add(Calendar.DATE, 1);
        }
        min = null;
        max = null;
        curr = null;
        return result;
    }
}
