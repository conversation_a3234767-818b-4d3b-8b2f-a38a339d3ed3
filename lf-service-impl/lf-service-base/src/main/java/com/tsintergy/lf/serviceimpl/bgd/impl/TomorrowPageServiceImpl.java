package com.tsintergy.lf.serviceimpl.bgd.impl;

import com.alibaba.excel.util.StringUtils;
import com.tsieframework.core.base.service.BaseFacadeServiceImpl;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.bgd.api.TomorrowPageService;
import com.tsintergy.lf.serviceapi.base.bgd.api.WeatherLongSeasonSituationService;
import com.tsintergy.lf.serviceapi.base.bgd.api.WeatherLongSituationService;
import com.tsintergy.lf.serviceapi.base.bgd.dto.LongInfoDTO;
import com.tsintergy.lf.serviceapi.base.bgd.pojo.WeatherLongSeasonSituationDO;
import com.tsintergy.lf.serviceapi.base.bgd.pojo.WeatherLongSituationDO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * 次日页面所涉及接口实现
 */
@Service("tomorrowPageService")
public class TomorrowPageServiceImpl extends BaseFacadeServiceImpl implements TomorrowPageService {

    @Autowired
    private WeatherLongSituationService weatherLongSituationService;

    @Autowired
    private WeatherLongSeasonSituationService weatherLongSeasonSituationService;


    @Override
    public LongInfoDTO getLongInfoDTO(String cityId, Date date, Integer dateType, String season) {
        LongInfoDTO longInfoDTO = new LongInfoDTO();
        String yearByDate = DateUtil.getYearByDate(date);
        String monthDate = DateUtil.getMonthDate(date);
        if (Constants.TYPE_MONTH.equals(dateType)){
            WeatherLongSituationDO weatherLongSituationDO = weatherLongSituationService.getWeatherLongSituationDO(cityId, yearByDate, monthDate);
            if (weatherLongSituationDO != null){
                longInfoDTO.setMonthInfo(weatherLongSituationDO.getSituation());
            }
            String nextMonth = genNextMonth(monthDate);
            if (!StringUtils.isEmpty(nextMonth)){
                WeatherLongSituationDO nextMonthDO = weatherLongSituationService.getWeatherLongSituationDO(cityId, yearByDate, nextMonth);
                if (nextMonthDO != null){
                    longInfoDTO.setNextMonthInfo(nextMonthDO.getSituation());
                }
            }
        }else {
            if (!StringUtils.isEmpty(season)){
                dateType = Constants.TYPE_SUMMER;
            }
            WeatherLongSeasonSituationDO weatherLongSeasonSituationDO = weatherLongSeasonSituationService.getWeatherLongSeasonSituationDO(cityId, yearByDate, dateType);
            if (weatherLongSeasonSituationDO != null){
                if (Constants.TYPE_WINTER.equals(dateType)){
                    longInfoDTO.setWinterInfo(weatherLongSeasonSituationDO.getSituation());
                }else {
                    longInfoDTO.setSummerInfo(weatherLongSeasonSituationDO.getSituation());
                }
            }
        }
        return longInfoDTO;
    }

    private String genNextMonth(String monthDate){
        int month = Integer.parseInt(monthDate);
        String nextMonth = null;
        if (month >= 9){
            nextMonth =  String.valueOf(month+1);
        }
        if (month <= 8){
            month = month + 1;
            nextMonth = "0" + month;
        }
        return nextMonth;
    }

}
