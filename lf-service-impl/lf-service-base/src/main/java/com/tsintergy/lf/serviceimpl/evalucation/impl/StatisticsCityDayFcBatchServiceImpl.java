package com.tsintergy.lf.serviceimpl.evalucation.impl;

import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum;
import com.tsintergy.lf.serviceapi.base.evalucation.api.StatisticsCityDayFcBatchService;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.StatisticsDayBatchDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.StatisticsCityDayFcBatchDO;
import com.tsintergy.lf.serviceapi.base.system.api.SettingBatchInitService;
import com.tsintergy.lf.serviceimpl.evalucation.dao.StatisticsCityDayFcBatchDAO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version $Id: StatisticsCityDayFcServiceImpl.java, v 0.1 2018-01-31 10:20:10 tao Exp $$
 */

@Service("statisticsCityDayFcBatchServiceImpl")
@Slf4j
public class StatisticsCityDayFcBatchServiceImpl extends BaseServiceImpl implements StatisticsCityDayFcBatchService {

    @Autowired
    private StatisticsCityDayFcBatchDAO statisticsCityDayFcDAO;

    @Autowired
    private SettingBatchInitService settingBatchInitService;

    @Autowired
    private StatisticsCityDayFcBatchDAO statisticsCityDayFcBatchDAO;

    @Override
    public List<StatisticsDayBatchDTO> getDayAccuracy(String cityId, String caliberId, List<String> algorithmIds, List<Integer> batchIds, Date startDate,
                                                      Date endDate) throws Exception {
        List<StatisticsDayBatchDTO> statisticsDayDTOS = new ArrayList<>();
        List<StatisticsCityDayFcBatchDO> algorithmList = statisticsCityDayFcDAO
                .getStatisticsCityDayFcBatchDOs(cityId, caliberId, algorithmIds, batchIds, startDate, endDate);

        for (StatisticsCityDayFcBatchDO data : algorithmList) {
            StatisticsDayBatchDTO result = new StatisticsDayBatchDTO();
            result.setDate(data.getDate());
            result.setBatch(String.valueOf(data.getBatchId()));
            AlgorithmEnum byId = AlgorithmEnum.findById(data.getAlgorithmId());
            result.setAlgorithm(byId == null ? null : byId.getDescription());
            result.setAlgorithmId(data.getAlgorithmId());
            result.setAccuracy(data.getAccuracy());
            result.setAlgoForeTime(data.getAlgoForeTime());
            statisticsDayDTOS.add(result);
        }

        // 结果排序
        List<StatisticsDayBatchDTO> sortedResult = statisticsDayDTOS.stream()
                .sorted(Comparator.comparing(StatisticsDayBatchDTO::getDate)
                        .thenComparing((one, two) -> {
                            Integer oneInt = Integer.parseInt(one.getBatch());
                            Integer twoInt = Integer.parseInt(two.getBatch());
                            return twoInt.compareTo(oneInt);
                        })).collect(Collectors.toList());
        return sortedResult;
    }

    public List<StatisticsCityDayFcBatchDO> doSaveOrUpdateStatisticsCityDayFcDOs(List<StatisticsCityDayFcBatchDO> statisticsCityDayFcVOS) throws Exception {
        List<StatisticsCityDayFcBatchDO> vos = new ArrayList<>();
        for (StatisticsCityDayFcBatchDO statisticsCityDayFcVO : statisticsCityDayFcVOS) {
            try {
                vos.add(statisticsCityDayFcBatchDAO.saveOrUpdateByTemplate(statisticsCityDayFcVO));
            } catch (Exception e) {
                log.error("保存预测统计结果出错了", e);
            }
        }
        return vos;
    }
}
