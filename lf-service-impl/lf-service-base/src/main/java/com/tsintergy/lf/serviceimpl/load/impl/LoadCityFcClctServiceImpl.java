/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司 Author:  wangchen Date:  2019/3/11 14:57 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceimpl.load.impl;


import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityFcClctService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityFcClctDO;
import com.tsintergy.lf.serviceimpl.load.dao.LoadCityFcClctDAO;
import java.util.Date;
import java.util.List;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2019/3/11
 * @since 1.0.0
 */
@Service("loadCityFcClctService")
public class LoadCityFcClctServiceImpl extends BaseServiceImpl implements LoadCityFcClctService {


    @Autowired
    CityService cityService;

    @Autowired
    LoadCityFcClctDAO loadCityFcClctDAO;


    @Override
    public void saveOrUpdateBatch(List<LoadCityFcClctDO> loadCityFcClctDOS) {
        loadCityFcClctDAO.doInsertOrUpdateBatch(loadCityFcClctDOS);
    }

    @Override
    public LoadCityFcClctDO findLoadCityFcClctDO(Date date, String caliberId, String cityId) {
        DBQueryParam param = DBQueryParamBuilder.create().queryDataOnly().build();
        param.getQueryConditions().put("_ne_date", new java.sql.Date(date.getTime()));
        param.getQueryConditions().put("_ne_cityId", cityId);
        param.getQueryConditions().put("_ne_caliberId", caliberId);
        List<LoadCityFcClctDO> datas = loadCityFcClctDAO.query(param).getDatas();
        if (CollectionUtils.isNotEmpty(datas)) {
            return datas.get(0);
        }
        return null;
    }

    @Override
    public void updateById(LoadCityFcClctDO loadCityFcClctDO) {
        loadCityFcClctDAO.update(loadCityFcClctDO);
    }
}