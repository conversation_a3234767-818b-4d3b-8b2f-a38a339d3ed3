
package com.tsintergy.lf.serviceimpl.ultra.impl;


import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.service.BaseFacadeServiceImpl;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.aif.tool.core.enums.WeatherEnum;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.ultra.api.WeatherDataManagerService;
import com.tsintergy.lf.serviceapi.base.ultra.dto.DataManagerDTO;
import com.tsintergy.lf.serviceapi.base.ultra.dto.WeatherNameDTO;
import com.tsintergy.lf.serviceapi.base.weather.annotation.FcWeatherDataSource;
import com.tsintergy.lf.serviceapi.base.weather.api.StatisticsSynthesizeWeatherCityDayHisService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityHisService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.BaseWeatherDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisDO;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 */
@Service
@FcWeatherDataSource(source = "")
public class WeatherDataManagerServiceImpl extends BaseFacadeServiceImpl implements WeatherDataManagerService {

    @Autowired
    CityService cityService;

    @Autowired
    WeatherCityFcService weatherCityFcService;

    @Autowired
    WeatherCityHisService weatherCityHisService;

    @Autowired
    StatisticsSynthesizeWeatherCityDayHisService statisticsSynthesizeWeatherCityDayHisService;

    @Override
    public <T extends BaseWeatherDO> List<T> findFcWeatherData(String cityId, Integer type, Date startDate,
        Date endDate) throws Exception {
        return null;
    }

    /**
     * 查找预测数据
     */
    @Override
    public List<DataManagerDTO> findWeatherCityFcDTOs(String cityId, Integer type, Date startDate, Date endDate)
        throws Exception {
        try {
            String weatherCity = null;
            if (StringUtils.hasLength(cityId)) {
                weatherCity = cityService.findWeatherCityId(cityId);
            }
            List<WeatherCityFcDO> weatherCityFcVOS =
                weatherCityFcService.findWeatherCityFcDOs(weatherCity, type, startDate, endDate);
            if (weatherCityFcVOS.size() < 1) {
                return new ArrayList<>();
            }
            List<DataManagerDTO> dataManagerDTOS = new ArrayList<DataManagerDTO>(10);
            for (WeatherCityFcDO weatherCityHisVO : weatherCityFcVOS) {
                DataManagerDTO dataManagerDTO = new DataManagerDTO();
                dataManagerDTO.setId(weatherCityHisVO.getId());
                dataManagerDTO.setDate(weatherCityHisVO.getDate());
                dataManagerDTO.setWeek(DateUtil.getWeek(weatherCityHisVO.getDate()));
                dataManagerDTO.setCity(cityService.findCityById(weatherCityHisVO.getCityId()).getCity());
                dataManagerDTO.setData(weatherCityHisVO.getWeatherList());
                dataManagerDTOS.add(dataManagerDTO);
            }
            return dataManagerDTOS;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("", e.getMessage(), e);
        }
    }

    @Override
    public List<DataManagerDTO> findWeatherCityHisDTOs(String cityId, Integer type, Date startDate, Date endDate)
        throws Exception {
        try {
            String weatherCityId = null;
            if (StringUtils.hasLength(cityId)) {
                weatherCityId = cityService.findWeatherCityId(cityId);
            }
            if (WeatherEnum.EFFECTIVE_TEMPERATURE.getType().equals(type) || WeatherEnum.COLD_DAMPNESS.getType()
                .equals(type)) {
                List<? extends BaseWeatherDO> weatherVOS =
                    statisticsSynthesizeWeatherCityDayHisService
                        .findStatisticsSynthesizeWeatherCityDayHisDO(weatherCityId, type, startDate, endDate);
                List<DataManagerDTO> dataManagerDTOS = getWeatherDTOS(weatherVOS);
                if (dataManagerDTOS != null) {
                    return dataManagerDTOS;
                }
            }
            List<? extends BaseWeatherDO> weatherCityVOS =
                weatherCityHisService.findWeatherCityHisDOs(weatherCityId, type, new java.sql.Date(startDate.getTime()),
                    new java.sql.Date(endDate.getTime()));
            List<DataManagerDTO> dataManagerDTOS = getWeatherDTOS(weatherCityVOS);
            if (dataManagerDTOS != null) {
                return dataManagerDTOS;
            }
            return new ArrayList<>();
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException(e.getMessage(), e.getMessage());
        }
    }

    @Override
    public List<WeatherNameDTO> findHisAllByDateAndCityId(String cityId, Date targetDate) throws Exception {
        List<WeatherNameDTO> weatherNameDTOS = new ArrayList<WeatherNameDTO>(4);
        List<WeatherCityHisDO> weatherCityHisVOS = weatherCityHisService
            .findWeatherCityHisDOs(cityId, null, new java.sql.Date(targetDate.getTime()),
                new java.sql.Date(targetDate.getTime()));
        if (weatherCityHisVOS.size() < 1) {
            return null;
        }
        for (WeatherCityHisDO weatherCityHisVO : weatherCityHisVOS) {
            WeatherNameDTO weatherNameDTO = new WeatherNameDTO();
            weatherNameDTO.setName(WeatherEnum.getValueByName(weatherCityHisVO.getType()));
            weatherNameDTO.setWeather(weatherCityHisVO.getWeatherList());
            weatherNameDTOS.add(weatherNameDTO);
        }
        return weatherNameDTOS;
    }

    @Override
    public List<WeatherNameDTO> findFcAllByDateAndCityId(String cityId, Date targetDate) throws Exception {
        List<WeatherNameDTO> weatherNameDTOS = new ArrayList<WeatherNameDTO>(4);
        List<WeatherCityFcDO> weatherCityFcVOS = weatherCityFcService
            .findWeatherCityFcDOs(cityId, null, targetDate, targetDate);
        if (weatherCityFcVOS.size() < 1) {
            return null;
        }
        for (WeatherCityFcDO weatherCityFcVO : weatherCityFcVOS) {
            WeatherNameDTO weatherNameDTO = new WeatherNameDTO();
            weatherNameDTO.setName(WeatherEnum.getValueByName(weatherCityFcVO.getType()));
            weatherNameDTO.setWeather(weatherCityFcVO.getWeatherList());
            weatherNameDTOS.add(weatherNameDTO);
        }
        return weatherNameDTOS;
    }

    private List<DataManagerDTO> getWeatherDTOS(List<? extends BaseWeatherDO> weatherVOS) throws Exception {
        if (CollectionUtils.isNotEmpty(weatherVOS)) {
            List<DataManagerDTO> dataManagerDTOS = new ArrayList<>(weatherVOS.size());
            for (BaseWeatherDO weatherVO : weatherVOS) {
                DataManagerDTO dataManagerDTO = wrapWeatherDTO(weatherVO);
                dataManagerDTOS.add(dataManagerDTO);
            }
            return dataManagerDTOS;
        }
        return null;
    }

    private DataManagerDTO wrapWeatherDTO(BaseWeatherDO weatherVO) throws Exception {
        DataManagerDTO dataManagerDTO = new DataManagerDTO();
        dataManagerDTO.setId(weatherVO.getId());
        dataManagerDTO.setDate(weatherVO.getDate());
        dataManagerDTO.setWeek(DateUtil.getWeek(weatherVO.getDate()));
        dataManagerDTO.setCity(cityService.findCityById(weatherVO.getCityId()).getCity());
        dataManagerDTO.setData(BasePeriodUtils.toList(weatherVO, Constants.WEATHER_CURVE_POINT_NUM,Constants.WEATHER_CURVE_START_WITH_ZERO));
        return dataManagerDTO;
    }
}