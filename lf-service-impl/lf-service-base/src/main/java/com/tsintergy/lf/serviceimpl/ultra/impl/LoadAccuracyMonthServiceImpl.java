
package com.tsintergy.lf.serviceimpl.ultra.impl;


import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.math.BigDecimalFunctions;
import com.tsieframework.core.base.service.BaseFacadeServiceImpl;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.core.util.VslfDateUtil;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherEnum;
import com.tsintergy.lf.serviceapi.base.ultra.api.LoadFeatureCityFcUltraService;
import com.tsintergy.lf.serviceapi.base.ultra.api.UltraLoadAccuracyMonthService;
import com.tsintergy.lf.serviceapi.base.ultra.dto.AccuracyDayUnitDTO;
import com.tsintergy.lf.serviceapi.base.ultra.dto.AccuracyLowDetailsMonthDTO;
import com.tsintergy.lf.serviceapi.base.ultra.dto.AccuracyLowDetailsQuarterDTO;
import com.tsintergy.lf.serviceapi.base.ultra.dto.AccuracyMonthDTO;
import com.tsintergy.lf.serviceapi.base.ultra.dto.AccuracyMonthUltraData;
import com.tsintergy.lf.serviceapi.base.ultra.pojo.LoadFeatureCityDayUltraFcDO;
import com.tsintergy.lf.serviceapi.base.ultra.pojo.LoadFeatureCityMonthUltraFcDO;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityHisService;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Slf4j
@Service("loadAccuracyMonthService")
public class LoadAccuracyMonthServiceImpl extends BaseFacadeServiceImpl implements UltraLoadAccuracyMonthService {

    private final Integer MONTH_TYPE = 1;

    @Autowired
    private LoadFeatureCityFcUltraService loadFeatureCityFcUltraService;

    @Autowired
    private WeatherCityHisService weatherCityHisService;

    @Autowired
    private WeatherCityFcService weatherCityFcService;

    @Autowired
    private CityService cityService;

    @Override
    public List<AccuracyMonthDTO> getLoadHistory(AccuracyMonthUltraData data, Integer type) throws Exception {
        //只查询首点
        if (MONTH_TYPE.equals(type)) {
            //月度查询
            List<LoadFeatureCityMonthUltraFcDO> ultraFcDOS = loadFeatureCityFcUltraService
                .findLoadFeatureCityMonthUltraFcDOS(data.getCityId(), data.getStartDate(), data.getEndDate(),
                    data.getCaliberId(), data.getAlgorithmId(), data.getTimeSpan(), Constants.ULTRA_FORECAST_1);
            return process(ultraFcDOS);
        } else {
            //季度查询
            String substring = DateUtil.formateDate(data.getStartDate()).substring(0, 4);
            List<LoadFeatureCityMonthUltraFcDO> ultraFcDOS = loadFeatureCityFcUltraService
                .findLoadFeatureCityMonthUltraFcDOS(data.getCityId(), DateUtil.getYearFirst(Integer.valueOf(substring)),
                    DateUtil.getYearLast(Integer.valueOf(substring)),
                    data.getCaliberId(), data.getAlgorithmId(), data.getTimeSpan(), Constants.ULTRA_FORECAST_1);
            Map<String, List<LoadFeatureCityMonthUltraFcDO>> collect = ultraFcDOS.stream()
                .collect(Collectors.groupingBy(src -> VslfDateUtil.getSeasonStr(src.getDateTime())));
            return processMap(collect);
        }
    }


    @Override
    public List<AccuracyDayUnitDTO> getDayAccuracy(AccuracyMonthUltraData data, Integer type) {
        List<AccuracyDayUnitDTO> resultList = new ArrayList<>();
        if (MONTH_TYPE.equals(type)) {
            List<LoadFeatureCityDayUltraFcDO> loadFeatureCityDayUltraFcDOS = this.loadFeatureCityFcUltraService
                .findLoadFeatureCityDayUltraFcDOS(data.getCityId(), DateUtil.getFirstDayOfMonth(data.getDate()),
                    DateUtil.getLastDayOfMonth(data.getDate()), data.getCaliberId(), data.getAlgorithmId(),
                    data.getTimeSpan(), Constants.ULTRA_FORECAST_1);
            for (LoadFeatureCityDayUltraFcDO one : loadFeatureCityDayUltraFcDOS) {
                AccuracyDayUnitDTO result = new AccuracyDayUnitDTO();
                result.setDate(DateUtils.date2String(one.getDateTime(), DateFormatType.SIMPLE_DATE_FORMAT_STR));
                result.setAccuracy(one.getAvgAccuracy());
                resultList.add(result);
            }
            return resultList;
        } else {
            Date yearMonth = data.getDate();
            List<Date> dateList = VslfDateUtil.getSeasonDateList(yearMonth);
            List<LoadFeatureCityMonthUltraFcDO> loadFeatureCityDayUltraFcDOS = this.loadFeatureCityFcUltraService
                .findLoadFeatureCityMonthUltraFcDOS(data.getCityId(), dateList.get(0),
                    dateList.get(dateList.size()-1), data.getCaliberId(), data.getAlgorithmId(),
                    data.getTimeSpan(), Constants.ULTRA_FORECAST_1);
            for (LoadFeatureCityMonthUltraFcDO one : loadFeatureCityDayUltraFcDOS) {
                AccuracyDayUnitDTO result = new AccuracyDayUnitDTO();
                result.setDate(DateUtils.date2String(one.getDateTime(), DateFormatType.YEAR_MONTH_STR));
                result.setAccuracy(one.getAvgAccuracy());
                resultList.add(result);
            }
            return resultList;
        }
    }

    @Override
    public AccuracyLowDetailsMonthDTO getAccuracyLowMonthDetails(AccuracyMonthUltraData data) throws Exception {
        AccuracyLowDetailsMonthDTO result = new AccuracyLowDetailsMonthDTO();
        List<LoadFeatureCityDayUltraFcDO> loadFeatureCityDayUltraFcDOS = loadFeatureCityFcUltraService
            .findLoadFeatureCityDayUltraFcDOS(data.getCityId(), data.getDate(), data.getDate(),
                data.getCaliberId(), data.getAlgorithmId(), data.getTimeSpan(), Constants.ULTRA_FORECAST_1);
        if (CollectionUtils.isEmpty(loadFeatureCityDayUltraFcDOS)) {
            return null;
        }
        String weatherCityId = cityService.findWeatherCityId(data.getCityId());
        List<BigDecimal> weatherCityFcValue = this.weatherCityFcService
            .find96WeatherCityFcValue(data.getDate(), weatherCityId, WeatherEnum.TEMPERATURE.getType());
        List<BigDecimal> weatherCityHisValue = this.weatherCityHisService
            .find96WeatherCityHisValue(data.getDate(), weatherCityId, WeatherEnum.TEMPERATURE.getType());
        LoadFeatureCityDayUltraFcDO ultraFcDO = loadFeatureCityDayUltraFcDOS.get(0);
        BeanUtils.copyProperties(ultraFcDO, result);
        result.setDateTime(DateUtils.date2String(data.getDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR));
        result.setAvgFcTemp(weatherCityFcValue == null ? null : BigDecimalFunctions.listAvg(weatherCityFcValue));
        result.setAvgHisTemp(weatherCityHisValue == null ? null : BigDecimalFunctions.listAvg(weatherCityHisValue));
        return result;
    }

    @Override
    public AccuracyLowDetailsQuarterDTO getAccuracyLowQuarterDetails(AccuracyMonthUltraData data) throws Exception {
        AccuracyLowDetailsQuarterDTO result = new AccuracyLowDetailsQuarterDTO();
        Date date = data.getDate();
        List<LoadFeatureCityMonthUltraFcDO> ultraFcDOS = loadFeatureCityFcUltraService
            .findLoadFeatureCityMonthUltraFcDOS(data.getCityId(), date, date,
                data.getCaliberId(), data.getAlgorithmId(), data.getTimeSpan(), Constants.ULTRA_FORECAST_1);
        if (CollectionUtils.isEmpty(ultraFcDOS)) {
            return null;
        }
        LoadFeatureCityMonthUltraFcDO ultraFcDO = ultraFcDOS.get(0);
        result.setAvgAccuracy(ultraFcDO.getAvgAccuracy());
        result.setMaxAccuracy(ultraFcDO.getMaxAccuracy());
        result.setMinAccuracy(ultraFcDO.getMinAccuracy());
        result.setMinAccuracyDate(ultraFcDO.getMinAccuracyTime());
        result.setMaxLoad(ultraFcDO.getMaxLoad());
        result.setMaxLoadAccuracy(ultraFcDO.getMaxLoadAccuracy());
        result.setMaxDate(ultraFcDO.getMaxTime());
        result.setMinLoad(ultraFcDO.getMinLoad());
        result.setMinLoadAccuracy(ultraFcDO.getMinLoadAccuracy());
        result.setMinDate(ultraFcDO.getMinTime());
        result.setDateTime(DateUtils.date2String(date, DateFormatType.YEAR_MONTH_STR));
        return result;
    }

    /**
     * 结果对象转换
     *
     * @param ultraFcDOS 数据库查询的准确率特性数据
     */
    private List<AccuracyMonthDTO> process(List<LoadFeatureCityMonthUltraFcDO> ultraFcDOS) {
        List<AccuracyMonthDTO> resultList = new ArrayList<>();
        for (LoadFeatureCityMonthUltraFcDO src : ultraFcDOS) {
            AccuracyMonthDTO result = new AccuracyMonthDTO();
            BeanUtils.copyProperties(src, result);
            result.setYearMonth(DateUtils.date2String(src.getDateTime(), DateFormatType.YEAR_MONTH_STR));
            resultList.add(result);
        }
        return resultList;
    }

    private List<AccuracyMonthDTO> processMap(Map<String, List<LoadFeatureCityMonthUltraFcDO>> ultraFcDOS) {
        List<AccuracyMonthDTO> resultList = new ArrayList<>();

        for(Map.Entry<String, List<LoadFeatureCityMonthUltraFcDO>> src : ultraFcDOS.entrySet())  {
            AccuracyMonthDTO result = new AccuracyMonthDTO();
            result.setYearMonth(src.getKey());
            List<BigDecimal> avgAccuracyList = src.getValue().stream().map(LoadFeatureCityMonthUltraFcDO::getAvgAccuracy)
                .collect(Collectors.toList());
//            List<BigDecimal> maxAccuracyList = src.getValue().stream().map(LoadFeatureCityMonthUltraFcDO::getMaxAccuracy)
//                .collect(Collectors.toList());
//            List<BigDecimal> minAccuracyList = src.getValue().stream().map(LoadFeatureCityMonthUltraFcDO::getMinAccuracy)
//                .collect(Collectors.toList());
            List<BigDecimal> maxLoadList = src.getValue().stream().map(LoadFeatureCityMonthUltraFcDO::getMaxLoad)
                .collect(Collectors.toList());
            List<BigDecimal> minLoadList = src.getValue().stream().map(LoadFeatureCityMonthUltraFcDO::getMinLoad)
                .collect(Collectors.toList());
            List<BigDecimal> avgLoadList = src.getValue().stream().map(LoadFeatureCityMonthUltraFcDO::getAvgLoad)
                .collect(Collectors.toList());
            BigDecimal minAccuracy = BigDecimalFunctions.listMin(avgAccuracyList);
            BigDecimal maxLoad = BigDecimalFunctions.listMax(maxLoadList);
            BigDecimal minLoad = BigDecimalFunctions.listMin(minLoadList);
            BigDecimal avgLoad = BigDecimalFunctions.listAvg(avgLoadList);
            List<LoadFeatureCityMonthUltraFcDO> minData = src.getValue().stream()
                .filter(srcData -> srcData.getAvgAccuracy().compareTo(minAccuracy) == 0).collect(Collectors.toList());
            List<LoadFeatureCityMonthUltraFcDO> maxLoadData = src.getValue().stream()
                .filter(srcData -> srcData.getMaxLoad().compareTo(maxLoad) == 0).collect(Collectors.toList());
            List<LoadFeatureCityMonthUltraFcDO> minLoadData = src.getValue().stream()
                .filter(srcData -> srcData.getMinLoad().compareTo(minLoad) == 0).collect(Collectors.toList());
            result.setAvgAccuracy(BigDecimalFunctions.listAvg(avgAccuracyList));
            result.setMaxAccuracy(BigDecimalFunctions.listMax(avgAccuracyList));
            result.setMinAccuracy(minAccuracy);
            result.setMinAccuracyTime(minData.get(0).getMinAccuracyTime().substring(0,7));
            result.setMaxLoad(maxLoad);
            result.setMaxLoadAccuracy(maxLoadData.get(0).getMaxLoadAccuracy());
            result.setMaxTime(maxLoadData.get(0).getMaxTime());
            result.setMinLoad(minLoad);
            result.setAvgLoad(avgLoad);
            result.setMinLoadAccuracy(minLoadData.get(0).getMinLoadAccuracy());
            result.setMinTime(minLoadData.get(0).getMinTime());
            resultList.add(result);
        }
        return resultList;
    }

}
