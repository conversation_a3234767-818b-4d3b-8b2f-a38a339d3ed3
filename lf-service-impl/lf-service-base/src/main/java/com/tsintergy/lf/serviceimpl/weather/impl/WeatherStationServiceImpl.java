/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.weather.impl;

import com.tsintergy.lf.serviceapi.base.weather.api.WeatherStationService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherStationInfoDO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherStationInfoDAO;
import java.sql.Timestamp;
import java.util.List;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/5/31 16:38
 * @Version: 1.0.0
 */

@Service("weatherStationService")
public class WeatherStationServiceImpl implements WeatherStationService {

    @Autowired
    WeatherStationInfoDAO weatherStationInfoDAO;
    @Override
    public void doSaveOrUpdate(WeatherStationInfoDO weatherStationInfoDO) {
        List<WeatherStationInfoDO> weatherStationInfo = weatherStationInfoDAO
            .getWeatherStationInfo(null,weatherStationInfoDO.getStationId());
        if (CollectionUtils.isEmpty(weatherStationInfo)) {
            weatherStationInfoDAO.createAndFlush(weatherStationInfoDO);
        }else{
            weatherStationInfoDAO.getSession().flush();
            weatherStationInfoDAO.getSession().clear();
            weatherStationInfoDO.setId(weatherStationInfo.get(0).getId());
            weatherStationInfoDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            weatherStationInfoDAO.updateAndFlush(weatherStationInfoDO);
        }
    }

    @Override
    public List<WeatherStationInfoDO> getWeatherStationInfo(String cityId,String stationId) {
        return weatherStationInfoDAO.getWeatherStationInfo(cityId,stationId);
    }
}