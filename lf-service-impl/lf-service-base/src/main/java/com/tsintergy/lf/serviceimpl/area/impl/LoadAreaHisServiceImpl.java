/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.area.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsieframework.core.base.math.BigDecimalFunctions;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.area.api.BaseAreaService;
import com.tsintergy.lf.serviceapi.base.area.api.LoadAreaHisService;
import com.tsintergy.lf.serviceapi.base.area.pojo.LoadAreaHisDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceimpl.area.dao.LoadAreaHisDAO;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/8/3 17:13
 * @Version: 1.0.0
 */
@Service("loadAreaHisService")
@Slf4j
public class LoadAreaHisServiceImpl implements LoadAreaHisService {

    @Autowired
    private LoadAreaHisDAO loadAreaHisDAO;

    @Autowired
    private LoadCityHisService loadCityHisService;

    @Autowired
    private BaseAreaService baseAreaService;
    @Override
    public List<LoadAreaHisDO> findLoadCityHisDOS(String areaId, Date startDate, Date endDate, String caliberId)
        throws Exception {
        List<LoadAreaHisDO> loadAreaHisDOS = loadAreaHisDAO.findAll(JpaWrappers.<LoadAreaHisDO>lambdaQuery()
            .eq(areaId != null, LoadAreaHisDO::getAreaId, areaId)
            .eq(caliberId != null, LoadAreaHisDO::getCaliberId, caliberId)
            .le(endDate != null, LoadAreaHisDO::getDate, endDate)
            .ge(startDate != null, LoadAreaHisDO::getDate, startDate)
        );
        return loadAreaHisDOS;
    }

    @Override
    public void doInsertOrUpdate(LoadAreaHisDO loadAreaHisDO) throws Exception {
        List<LoadAreaHisDO> loadCityHisDOS = findLoadCityHisDOS(loadAreaHisDO.getAreaId(), loadAreaHisDO.getDate(),
            loadAreaHisDO.getDate(), loadAreaHisDO.getCaliberId());

        if (CollectionUtils.isEmpty(loadCityHisDOS)) {
            loadAreaHisDAO.save(loadAreaHisDO);
            return;
        }else{
            LoadAreaHisDO LoadCityHisDOTemp = loadCityHisDOS.get(0);
            loadAreaHisDO.setId(LoadCityHisDOTemp.getId());
            loadAreaHisDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            loadAreaHisDAO.saveOrUpdateByTemplate(loadAreaHisDO);
        }
    }

    @Override
    public void deleteByAreasIds(List<String> areaIds) throws Exception {
        loadAreaHisDAO.delete(JpaWrappers.<LoadAreaHisDO>lambdaQuery().in(!CollectionUtils.isEmpty(areaIds),LoadAreaHisDO::getAreaId,areaIds));
    }

    @Override
    public void doInsert(LoadAreaHisDO loadAreaHisDO) {
        if (loadAreaHisDO != null) {
            loadAreaHisDAO.save(loadAreaHisDO);
        }
    }

    @Override
    public void doInsertOrUpdateBatch(List<LoadAreaHisDO> loadAreaHisDOS) throws Exception {
        for (LoadAreaHisDO loadAreaHisDO : loadAreaHisDOS) {
            this.doInsertOrUpdate(loadAreaHisDO);
        }
    }

    @Override
    public void statAreaHisLoad(String caliberId, String areaId, Date startDate, Date end)
        throws Exception {
        long start = System.currentTimeMillis();
        List<Date> listBetweenDay = DateUtil.getListBetweenDay(startDate, end);
        Map<String, LoadCityHisDO> collect = loadCityHisService
            .getLoadCityHisDOS(null, caliberId, startDate, end).stream()
            .collect(Collectors.toMap(t -> {
                return t.getCityId() + DateUtil.getDateToStr(t.getDate());
            }, t -> t));

        long select = System.currentTimeMillis();
        log.info("查询时间:----"+(select-start));
        List<String> cityIds = baseAreaService.getCityIdsByAreaId(areaId);
        List<LoadAreaHisDO> loadAreaHisDOS = new ArrayList<>();
        for (Date date : listBetweenDay) {
            List<List<BigDecimal>> loadCityHisValueSum = new ArrayList<>();
            for (String cityId : cityIds) {
                LoadCityHisDO loadCityHisDO = collect.get(cityId+DateUtil.getDateToStr(date));
                if (loadCityHisDO == null) {
                    continue;
                }
                loadCityHisValueSum.add(BasePeriodUtils
                    .toList(loadCityHisDO, com.tsintergy.lf.core.constants.Constants.LOAD_CURVE_POINT_NUM,
                        com.tsintergy.lf.core.constants.Constants.LOAD_CURVE_START_WITH_ZERO));
            }
            if (CollectionUtils.isEmpty(loadCityHisValueSum)){
                continue;
            }
            List<BigDecimal> zeroList = ColumnUtil.getZeroOrNullList(96, BigDecimal.ZERO);
            for (List<BigDecimal> bigDecimals : loadCityHisValueSum) {
                zeroList = BigDecimalFunctions.listAdd(zeroList, bigDecimals);
            }

            LoadAreaHisDO loadAreaHisDO = new LoadAreaHisDO();
            Map<String, BigDecimal> valueMap = ColumnUtil.listToMap(zeroList, false);
            BasePeriodUtils.setAllFiled(loadAreaHisDO,valueMap);
            loadAreaHisDO.setCaliberId(caliberId);
            loadAreaHisDO.setAreaId(areaId);
            loadAreaHisDO.setDate(new java.sql.Date(date.getTime()));
            loadAreaHisDOS.add(loadAreaHisDO);
        }

        log.info("准备数据时间:----"+(System.currentTimeMillis()-select));
        updateAreaLoad(loadAreaHisDOS,startDate,end,caliberId,areaId);
    }

    private void updateAreaLoad(List<LoadAreaHisDO> loadAreaHisDOS,Date startDate, Date endDate,String caliberId,String areaId)
        throws Exception {
        if (CollectionUtils.isEmpty(loadAreaHisDOS)){
            return;
        }
        long update = System.currentTimeMillis();
//        LoadAreaHisService loadAreaHisService = (LoadAreaHisServiceImpl) SpringContextManager.getApplicationContext()
//            .getBean("lf-loadAreaHisService");
        loadAreaHisDAO.delete(JpaWrappers.<LoadAreaHisDO>lambdaQuery()
            .eq(areaId != null, LoadAreaHisDO::getAreaId, areaId)
            .eq(caliberId != null, LoadAreaHisDO::getCaliberId, caliberId)
            .le(endDate != null, LoadAreaHisDO::getDate, endDate)
            .ge(startDate != null, LoadAreaHisDO::getDate, startDate)
        );


        long del = System.currentTimeMillis();
        log.info("删除时间:----"+(System.currentTimeMillis()-update));

        loadAreaHisDAO.saveOrUpdateBatch(loadAreaHisDOS);
        log.info("插入:----"+(System.currentTimeMillis()-del));

    }
}