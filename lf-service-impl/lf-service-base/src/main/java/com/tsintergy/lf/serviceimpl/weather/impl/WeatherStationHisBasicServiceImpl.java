/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.weather.impl;

import com.tsintergy.lf.core.util.PeriodDataUtil;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherStationHisBasicService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherStationHisBasicDO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherStationHisBasicDAO;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/5/20 15:45
 * @Version: 1.0.0
 */
@Service("weatherStationHisBasicService")
public class WeatherStationHisBasicServiceImpl implements WeatherStationHisBasicService {

    @Autowired
    WeatherStationHisBasicDAO weatherStationHisBasicDAO;
    @Override
    public List<WeatherStationHisBasicDO> getWeatherStationHisBasicDO(String cityId, String stationId, Date startDate,
        Date endDate, Integer type) {
        return weatherStationHisBasicDAO.getWeatherStationHisBasicDO(cityId,stationId, startDate,endDate,type);
    }

    @Override
    public void doSaveOrUpdate(WeatherStationHisBasicDO weatherStationHisBasicDO) {
        WeatherStationHisBasicDO weatherStationTemp = weatherStationHisBasicDAO
            .getWeatherStationHisBasicDO(weatherStationHisBasicDO.getStationId(),
                weatherStationHisBasicDO.getDate(),
                weatherStationHisBasicDO.getType());
        if (weatherStationTemp == null) {
            weatherStationHisBasicDAO.createAndFlush(weatherStationHisBasicDO);
        }else{
            weatherStationHisBasicDAO.getSession().flush();
            weatherStationHisBasicDAO.getSession().clear();
            weatherStationHisBasicDO.setId(weatherStationTemp.getId());
            weatherStationHisBasicDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            weatherStationHisBasicDAO.updateAndFlush(weatherStationHisBasicDO);
        }

    }


    @Override
    public void doSaveOrUpdateList(List<WeatherStationHisBasicDO> result) throws Exception {
        for (WeatherStationHisBasicDO value : result) {
            System.out.println("***************************************" + value.getDate());
            PeriodDataUtil.do24To96VO(value);
            this.doSaveOrUpdate(value);
        }
    }

    @Override
    public void doSaveOrUpdateBatch(List<WeatherStationHisBasicDO> weatherStationHisBasicDOS) {
        if (CollectionUtils.isNotEmpty(weatherStationHisBasicDOS)) {
            weatherStationHisBasicDOS.forEach(weatherStationHisBasicDO -> {
                this.doSaveOrUpdate(weatherStationHisBasicDO);
            });
        }
    }
}