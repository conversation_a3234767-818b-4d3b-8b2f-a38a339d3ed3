/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.area.dao;

import com.tsieframework.core.base.dao.hibernate.BaseAbstractDAO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.dao.hibernate.QueryOp;
import com.tsieframework.util.compatible.StringUtils;
import com.tsintergy.lf.serviceapi.base.area.pojo.BaseAreaDO;
import com.tsintergy.lf.serviceapi.base.check.pojo.SettingCheckDO;
import com.tsintergy.lf.serviceapi.base.check.pojo.SettingCheckPrecisionDO;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/8/4 9:39
 * @Version: 1.0.0
 */
@Component
public class BaseAreaDAO extends BaseAbstractDAO<BaseAreaDO> {
    public List<BaseAreaDO> findByIds(List<String> ids) throws Exception {
        DBQueryParamBuilder param = DBQueryParamBuilder.create().queryDataOnly();
        if (null != ids&&ids.size()>0) {
            param.where(QueryOp.StringIsIn, "id", ids);
        }
        List<BaseAreaDO> datas = query(param.build()).getDatas();
        return datas;
    }


    public BaseAreaDO findById(String areaId) {
        DBQueryParamBuilder param = DBQueryParamBuilder.create().queryDataOnly();
        if (null != areaId) {
            param.where(QueryOp.StringEqualTo, "id", areaId);
        }
        List<BaseAreaDO> datas = query(param.build()).getDatas();
        if (CollectionUtils.isEmpty(datas)){
            return null;
        }
        return datas.get(0);
    }
}