package com.tsintergy.lf.serviceimpl.bgd.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsieframework.core.base.service.BaseFacadeServiceImpl;
import com.tsintergy.lf.serviceapi.base.bgd.api.LoadFeatureIndustryMonthHisService;
import com.tsintergy.lf.serviceapi.base.bgd.pojo.LoadFeatureIndustryMonthHisServiceDO;
import com.tsintergy.lf.serviceimpl.bgd.dao.LoadFeatureIndustryMonthHisServiceDAO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service("loadFeatureIndustryMonthHisService")
public class LoadFeatureIndustryMonthHisServiceImpl extends BaseFacadeServiceImpl implements LoadFeatureIndustryMonthHisService {

    @Autowired
    private LoadFeatureIndustryMonthHisServiceDAO loadFeatureIndustryMonthHisServiceDAO;

    @Override
    public List<LoadFeatureIndustryMonthHisServiceDO> getLoadFeatureIndustryMonthHisServiceList(String cityId, String tradeCode, String startYM, String endYM) {
        List<LoadFeatureIndustryMonthHisServiceDO> industryMonthHisServiceDOS = loadFeatureIndustryMonthHisServiceDAO.findAll(
                JpaWrappers.<LoadFeatureIndustryMonthHisServiceDO>lambdaQuery()
                        .ge(LoadFeatureIndustryMonthHisServiceDO::getYear, startYM.substring(0, 4))
                        .le(LoadFeatureIndustryMonthHisServiceDO::getYear, endYM.substring(0, 4))
                        .eq(LoadFeatureIndustryMonthHisServiceDO::getCityId, cityId)
                        .eq(LoadFeatureIndustryMonthHisServiceDO::getType, tradeCode)
        );
        List<LoadFeatureIndustryMonthHisServiceDO> loadFeatureCityMonthHisVOs2 = new ArrayList<>();
        for (LoadFeatureIndustryMonthHisServiceDO weatherMonth : industryMonthHisServiceDOS) {
            if ((weatherMonth.getYear() + "-" + weatherMonth.getMonth()).compareTo(startYM) > -1
                    && (weatherMonth.getYear() + "-" + weatherMonth.getMonth()).compareTo(endYM) < 1) {
                loadFeatureCityMonthHisVOs2.add(weatherMonth);
            }
        }
        return loadFeatureCityMonthHisVOs2;
    }


    @Override
    public void saveOrUpdate(LoadFeatureIndustryMonthHisServiceDO loadFeatureIndustryMonthHisServiceDO) {
        List<LoadFeatureIndustryMonthHisServiceDO> list = loadFeatureIndustryMonthHisServiceDAO.findAll(
                JpaWrappers.<LoadFeatureIndustryMonthHisServiceDO>lambdaQuery()
                        .eq(LoadFeatureIndustryMonthHisServiceDO::getCityId, loadFeatureIndustryMonthHisServiceDO.getCityId())
                        .eq(LoadFeatureIndustryMonthHisServiceDO::getType, loadFeatureIndustryMonthHisServiceDO.getType())
                        .eq(LoadFeatureIndustryMonthHisServiceDO::getMonth, loadFeatureIndustryMonthHisServiceDO.getMonth())
                        .eq(LoadFeatureIndustryMonthHisServiceDO::getYear, loadFeatureIndustryMonthHisServiceDO.getYear())
        );

        if (CollectionUtils.isEmpty(list)) {
            loadFeatureIndustryMonthHisServiceDAO.save(loadFeatureIndustryMonthHisServiceDO);
        } else {
            LoadFeatureIndustryMonthHisServiceDO loadFeatureEveryIndustryDO = list.get(0);
            loadFeatureIndustryMonthHisServiceDO.setId(loadFeatureEveryIndustryDO.getId());
            loadFeatureIndustryMonthHisServiceDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            loadFeatureIndustryMonthHisServiceDAO.saveOrUpdateByTemplate(loadFeatureIndustryMonthHisServiceDO);
        }
    }
}
