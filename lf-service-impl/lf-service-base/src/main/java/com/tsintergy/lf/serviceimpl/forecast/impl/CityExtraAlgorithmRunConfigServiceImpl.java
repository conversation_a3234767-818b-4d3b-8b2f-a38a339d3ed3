package com.tsintergy.lf.serviceimpl.forecast.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsintergy.lf.serviceapi.base.forecast.api.CityExtraAlgorithmRunConfigService;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.CityExtraAlgorithmRunConfigDO;
import com.tsintergy.lf.serviceimpl.forecast.dao.CityExtraAlgorithmRunConfigDAO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("cityExtraAlgorithmRunConfigService")
public class CityExtraAlgorithmRunConfigServiceImpl extends BaseServiceImpl implements CityExtraAlgorithmRunConfigService {

    @Autowired
    private CityExtraAlgorithmRunConfigDAO cityExtraAlgorithmRunConfigDAO;

    @Override
    public CityExtraAlgorithmRunConfigDO findExtraRunConfigDOByType(String algorithmType) {
        if (algorithmType == null) {
            return null;
        }
        return cityExtraAlgorithmRunConfigDAO.findOne(
                JpaWrappers.<CityExtraAlgorithmRunConfigDO>lambdaQuery()
                        .eq(CityExtraAlgorithmRunConfigDO::getAlgorithmType, algorithmType));
    }

    @Override
    public List<CityExtraAlgorithmRunConfigDO> findAll() {
        return cityExtraAlgorithmRunConfigDAO.findAll();
    }
}
