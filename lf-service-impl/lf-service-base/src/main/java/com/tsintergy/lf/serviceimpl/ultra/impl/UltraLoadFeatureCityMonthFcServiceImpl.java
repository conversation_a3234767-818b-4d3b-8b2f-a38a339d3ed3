/**
 * Copyright (C), 2015‐2020, 北京清能互联科技有限公司 Author:  wangchen Date:  2020/4/19 17:04 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceimpl.ultra.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsieframework.util.compatible.BigDecimalUtils;
import com.tsieframework.util.compatible.StringUtils;
import com.tsintergy.aif.algorithm.serviceapi.base.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.ultra.api.UltraLoadFeatureCityMonthFcService;
import com.tsintergy.lf.serviceapi.base.ultra.pojo.LoadFeatureCityDayUltraFcDO;
import com.tsintergy.lf.serviceapi.base.ultra.pojo.UltraLoadFeatureCityMonthFcDO;
import com.tsintergy.lf.serviceimpl.ultra.dao.UltraLoadFeatureCityMonthFcDAO;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2020/4/19
 * @since 1.0.0
 */
@Service(value = "ultraLoadFeatureCityMonthFcService")
public class UltraLoadFeatureCityMonthFcServiceImpl extends BaseServiceImpl implements UltraLoadFeatureCityMonthFcService {

    private static final Logger logger = LoggerFactory.getLogger(UltraLoadFeatureCityMonthFcServiceImpl.class);

    @Autowired
    private UltraLoadFeatureCityMonthFcDAO ultraLoadFeatureCityMonthFcDAO;

    @Override
    public void doSaveOrUpdateLoadFeatureCityMonthFcDOS(List<UltraLoadFeatureCityMonthFcDO> monthFcVOS) {
        for (UltraLoadFeatureCityMonthFcDO monthVO : monthFcVOS) {
            try {
                saveOrUpdate(monthVO);
            } catch (Exception e) {
                logger.error("保存预测-月-负荷特性出错了", e);
            }
        }
    }

    @Override
    public List<UltraLoadFeatureCityMonthFcDO> findLoadFeatureMonth(String cityId, String algorithmId,
                                                               String startYm,
                                                               String endYm, String caliberId) throws Exception {
        List<UltraLoadFeatureCityMonthFcDO> reportAccuracyMonthVOS = new ArrayList<>();
        String startYear = startYm.substring(0, 4);
        String endYear = endYm.substring(0, 4);
        List<UltraLoadFeatureCityMonthFcDO> monthVOList =
            ultraLoadFeatureCityMonthFcDAO.findAll(
                        JpaWrappers.<UltraLoadFeatureCityMonthFcDO>lambdaQuery()
                                .eq(!StringUtils.isBlank(cityId), UltraLoadFeatureCityMonthFcDO::getCityId, cityId)
                                .eq(!StringUtils.isBlank(algorithmId), UltraLoadFeatureCityMonthFcDO::getAlgorithmId, algorithmId)
                                .eq(!StringUtils.isBlank(caliberId), UltraLoadFeatureCityMonthFcDO::getCaliberId, caliberId)
                                .ge(!StringUtils.isBlank(startYear), UltraLoadFeatureCityMonthFcDO::getYear, startYear)
                                .le(!StringUtils.isBlank(endYear), UltraLoadFeatureCityMonthFcDO::getYear, endYear)
                );
        for (UltraLoadFeatureCityMonthFcDO monthVO : monthVOList) {
            if ((monthVO.getYear() + "-" + monthVO.getMonth()).compareTo(startYm) > -1
                    && (monthVO.getYear() + "-" + monthVO.getMonth()).compareTo(endYm) < 1) {
                reportAccuracyMonthVOS.add(monthVO);
            }
        }
        return reportAccuracyMonthVOS;
    }

    private void saveOrUpdate(UltraLoadFeatureCityMonthFcDO fcVO) throws Exception {
        fcVO.setUpdateTime(new Timestamp(System.currentTimeMillis()));
        ultraLoadFeatureCityMonthFcDAO.saveOrUpdateByTemplate(fcVO);
    }

    @Override
    public List<UltraLoadFeatureCityMonthFcDO> statisticsMonthFeatures(List<LoadFeatureCityDayUltraFcDO> FcVOS) {
        List<UltraLoadFeatureCityMonthFcDO> monthFcList = new ArrayList<>();
        if (FcVOS != null) {
            Map<String, List<LoadFeatureCityDayUltraFcDO>> loadFeatureMap = new HashMap<>(16);
            for (LoadFeatureCityDayUltraFcDO loadFeatureCityDayHisVO : FcVOS) {
                String key = loadFeatureCityDayHisVO.getCityId() + "_" + loadFeatureCityDayHisVO.getAlgorithmId() + "_"
                        + loadFeatureCityDayHisVO.getCaliberId() + "_" + DateUtil
                        .getMonthByDate(loadFeatureCityDayHisVO.getDateTime()) + "_" +
                        loadFeatureCityDayHisVO.getType() + "_" +
                    loadFeatureCityDayHisVO.getMultipointType();
                if (!loadFeatureMap.containsKey(key)) {
                    loadFeatureMap.put(key, new ArrayList<>());
                }
                loadFeatureMap.get(key).add(loadFeatureCityDayHisVO);
            }

            for (String key : loadFeatureMap.keySet()) {
                try {
                    UltraLoadFeatureCityMonthFcDO loadFeatureCityMonthHisVO = statisticsMonthFeature(
                            loadFeatureMap.get(key));
                    monthFcList.add(loadFeatureCityMonthHisVO);
                } catch (Exception e) {
                    logger.error("统计月负荷特性出错了", e);
                }
            }

        }
        return monthFcList;
    }

    /**
     * 统计月负荷特性
     *
     * @param voList 一个月的日负荷特性
     */
    public UltraLoadFeatureCityMonthFcDO statisticsMonthFeature(List<LoadFeatureCityDayUltraFcDO> voList) throws Exception {
        if (voList.size() > 31) {
            logger.error("统计月负荷特性有误：日负荷特性数据的超过31天，无法统计");
            return null;
        }

        if (voList.size() > 0) {
            String cityId = voList.get(0).getCityId();
            String caliberId = voList.get(0).getCaliberId();
            String algorithmId = voList.get(0).getAlgorithmId();
            Integer type = voList.get(0).getType();
            String ym = DateUtil.getMonthByDate(voList.get(0).getDateTime()); // 年月
            BigDecimal maxLoad = null; // 最大负荷
            BigDecimal minLoad = null; // 最小负荷
            String maxTime = null;
            String minTime = null;
            Date maxDate = null;
            List<BigDecimal> avgLoads = new ArrayList<BigDecimal>();
            List<BigDecimal> peaks = new ArrayList<BigDecimal>();
            List<BigDecimal> troughs = new ArrayList<BigDecimal>();
            List<BigDecimal> maxLoads = new ArrayList<BigDecimal>();
            BigDecimal energy = new BigDecimal(0).setScale(4);
            for (LoadFeatureCityDayUltraFcDO dayVO : voList) {
                if (!ym.equals(DateUtil.getMonthByDate(dayVO.getDateTime()))) {
                    logger.error("统计月负荷特性有误：日负荷特性数据的日期不是同一个月，无法统计");
                    return null;
                }
                if (maxLoad == null || maxLoad.compareTo(dayVO.getMaxLoad()) < 0) {
                    maxLoad = dayVO.getMaxLoad();
                    maxTime = dayVO.getMaxTime();
                    maxDate = dayVO.getDateTime();
                }

                if (minLoad == null || minLoad.compareTo(dayVO.getMinLoad()) > 0) {
                    minLoad = dayVO.getMinLoad();
                    minTime = dayVO.getMinTime();
                }

                avgLoads.add(dayVO.getAvgLoad());
                peaks.add(dayVO.getPeak());
                troughs.add(dayVO.getTrough());
                maxLoads.add(dayVO.getMaxLoad());
                energy = BigDecimalUtils.add(energy, dayVO.getEnergy());

            }

            UltraLoadFeatureCityMonthFcDO loadFeatureCityMonthHisVO = new UltraLoadFeatureCityMonthFcDO();
            loadFeatureCityMonthHisVO.setCityId(cityId);
            loadFeatureCityMonthHisVO.setCaliberId(caliberId);
            loadFeatureCityMonthHisVO.setYear(ym.substring(0, 4));
            loadFeatureCityMonthHisVO.setMonth(ym.substring(5, 7));
            loadFeatureCityMonthHisVO.setType(type);
            loadFeatureCityMonthHisVO.setMaxDate(maxDate);
            loadFeatureCityMonthHisVO.setMaxTime(maxTime);
            loadFeatureCityMonthHisVO.setMinTime(minTime);
            loadFeatureCityMonthHisVO.setAlgorithmId(algorithmId);
            loadFeatureCityMonthHisVO.setMaxLoad(maxLoad);
            loadFeatureCityMonthHisVO.setMinLoad(minLoad);
            loadFeatureCityMonthHisVO.setAveLoad(BigDecimalUtils.avgList(avgLoads, 4, false));
            loadFeatureCityMonthHisVO.setDifferent(
                    BigDecimalUtils.sub(loadFeatureCityMonthHisVO.getMaxLoad(), loadFeatureCityMonthHisVO.getMinLoad()));
            loadFeatureCityMonthHisVO.setGradient(BigDecimalUtils
                    .divide(loadFeatureCityMonthHisVO.getDifferent(), loadFeatureCityMonthHisVO.getMaxLoad(), 4));
            loadFeatureCityMonthHisVO.setLoadGradient(BigDecimalUtils
                    .divide(loadFeatureCityMonthHisVO.getAveLoad(), loadFeatureCityMonthHisVO.getMaxLoad(), 4));
            loadFeatureCityMonthHisVO.setPeak(BigDecimalUtils.avgList(peaks, 4, true));
            loadFeatureCityMonthHisVO.setTrough(BigDecimalUtils.avgList(troughs, 4, false));
            loadFeatureCityMonthHisVO.setEnergy(energy);
            loadFeatureCityMonthHisVO.setMultipointType(voList.get(0).getMultipointType());
            // 日不均衡系数 = average(月内各日最大负荷)/月最大负荷
            loadFeatureCityMonthHisVO.setDayUnbalance(BigDecimalUtils.avgList(maxLoads, 4, false));

            return loadFeatureCityMonthHisVO;

        }

        return null;

    }


}
