package com.tsintergy.lf.serviceimpl.system.impl;

import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsintergy.lf.core.constants.CityConstants;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.constants.SystemConstant;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.system.api.SettingBatchInitService;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceapi.base.system.pojo.SettingBatchInitDO;
import com.tsintergy.lf.serviceimpl.system.dao.SettingBatchInitDAO;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/3/5 14:00
 **/
@Service("settingBatchInitService")
public class SettingBatchInitServiceImpl implements SettingBatchInitService {

    @Autowired
    private SettingBatchInitDAO settingBatchInitDAO;

    @Autowired
    private CityService cityService;

    @Autowired
    private SettingSystemService settingSystemService;

    @Override
    @SneakyThrows
    public List<SettingBatchInitDO> getBatchList() {
        return settingBatchInitDAO.getBatchList();
    }

    @Override
    public Map<String, String> getBatchNameMap() {
        List<SettingBatchInitDO> batchList = this.getBatchList();
        Map<String, String> collect = batchList.stream()
                .collect(Collectors.toMap(SettingBatchInitDO::getId, SettingBatchInitDO::getName, (o, n) -> o));
        return collect;
    }

    @Override
    public Integer getBatchIdByTime(Date date) {
        String day = DateUtil.getStrDate(date, DateUtil.DATE_FORMAT2);
        List<SettingBatchInitDO> batchList = this.getBatchList();
        for (SettingBatchInitDO batchInitDO : batchList) {
            Date startDateTime = DateUtil
                    .getDateFromString(day + " " + batchInitDO.getStartTime() + ":00", DateUtil.DATE_FORMAT1);
            Date endDateTime = DateUtil
                    .getDateFromString(day + " " + batchInitDO.getEndTime() + ":00", DateUtil.DATE_FORMAT1);
            if (date.after(startDateTime) && date.before(endDateTime)) {
                return Integer.valueOf(batchInitDO.getId());
            }
        }
        //没有匹配上系统设置的批次执行时间端，返回默认批次id；
        return 1;
    }

    @Override
    public SettingBatchInitDO getBatchByCondition(String batchName, String cityId) {
        return settingBatchInitDAO.getBatchByCondition(batchName, cityId);
    }

    @Override
    public void doChangeSavedTimeByCityID(String cityId, String time) throws Exception {
        //检查时间是否符合要求
        String[] reportConfig;
        Integer type = cityService.findCityById(cityId).getType();
        if (CityConstants.PROVINCE_TYPE.equals(type)) {
            reportConfig = settingSystemService.findByFieldId(SystemConstant.PROVINCE_END_REPORT_TIME).getValue().split(Constants.SEPARATOR_PUNCTUATION);
        } else {
            reportConfig = settingSystemService.findByFieldId(SystemConstant.END_REPORT_TIME).getValue().split(Constants.SEPARATOR_PUNCTUATION);
        }
        String endReportEnable = reportConfig[0];
        String endReportTime = reportConfig[1];
        if ("1".equals(endReportEnable) && DateUtil.isAfterGivenTime(time, endReportTime)) {
            throw TsieExceptionUtils.newBusinessException("指定的时间 [" + time + "] 不符合规定，不能晚于设定的最晚上报时间 [" + endReportTime + "].");
        }

        List<SettingBatchInitDO> settingBatchInitDOS = settingBatchInitDAO.getListByCityId(cityId);
        if (CollectionUtils.isEmpty(settingBatchInitDOS) || settingBatchInitDOS.size() < 2) {
            throw TsieExceptionUtils.newBusinessException("该城市没有批次配置，请先完善批次信息.");
        }
        settingBatchInitDOS.sort(Comparator.comparing(SettingBatchInitDO::getId));

        //更新第一批次的结束时间
        SettingBatchInitDO firstBatchDO = settingBatchInitDOS.get(0);
        firstBatchDO.setEndTime(time);
        settingBatchInitDAO.saveOrUpdateByTemplate(firstBatchDO);

        //更新第二批次的起始时间，起始时间为time + 1分钟
        SettingBatchInitDO secondBatchDO = settingBatchInitDOS.get(1);
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm");
        LocalTime localTime = LocalTime.parse(time, timeFormatter);
        LocalTime newTime = localTime.plusMinutes(1);
        secondBatchDO.setStartTime(newTime.format(timeFormatter));
        settingBatchInitDAO.saveOrUpdateByTemplate(secondBatchDO);
    }

    @Override
    public SettingBatchInitDO getBatchById(String id) {
        if (id != null) {
            return settingBatchInitDAO.getBatchById(id);
        } else {
            SettingBatchInitDO batchInitDO = new SettingBatchInitDO();
            batchInitDO.setStartTime("00:00");
            batchInitDO.setEndTime("23:59");
            return batchInitDO;
        }
    }
}
