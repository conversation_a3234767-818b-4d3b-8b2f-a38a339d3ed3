package com.tsintergy.lf.serviceimpl.load.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tsieframework.core.base.dao.jpa.query.JpaQueryWrapper;
import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityOrderHisService;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadHisDataDTO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityOrderHisDO;
import com.tsintergy.lf.serviceimpl.load.dao.LoadCityOrderHisDAO;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class LoadCityOrderHisServiceImpl implements LoadCityOrderHisService {

    @Resource
    private LoadCityOrderHisDAO loadCityOrderHisDAO;

    @Resource
    private CityService cityService;


    @Override
    public List<LoadCityOrderHisDO> findByDate(String cityId, String caliberId, Date startDate, Date endDate) {
        return loadCityOrderHisDAO.findAll(JpaWrappers.<LoadCityOrderHisDO>lambdaQuery()
                        .eq(StringUtils.isNotBlank(cityId),LoadCityOrderHisDO::getCityId,cityId)
                        .eq(StringUtils.isNotBlank(caliberId),LoadCityOrderHisDO::getCaliberId,caliberId)
                        .ge(startDate!= null,LoadCityOrderHisDO::getDate,startDate)
                        .le(endDate!= null,LoadCityOrderHisDO::getDate,endDate)
                );
    }

    @SneakyThrows
    @Override
    public List<LoadHisDataDTO> findLoadOrderByCityId(Date startDate, Date endDate, String cityId, String caliberId) {
        List<LoadCityOrderHisDO> loadCityOrderHisDOS = findByDate(cityId,caliberId ,startDate, endDate);
        if (loadCityOrderHisDOS.size() < 1) {
            return new ArrayList<>();
        }
        Map<String, String> cityMap = cityService.findAllCitys().stream()
                .collect(Collectors.toMap(t -> t.getId(), t -> t.getCity()));
        List<LoadHisDataDTO> loadCommonDTOS = new ArrayList<LoadHisDataDTO>(10);
        for (LoadCityOrderHisDO loadCityOrderHisDO : loadCityOrderHisDOS) {
            LoadHisDataDTO loadCommonDTO = new LoadHisDataDTO();
            loadCommonDTO.setId(loadCityOrderHisDO.getId());
            loadCommonDTO.setDate(loadCityOrderHisDO.getDate());
            loadCommonDTO.setData(loadCityOrderHisDO.getloadList());
            loadCommonDTO.setWeek(DateUtil.getWeek(loadCityOrderHisDO.getDate()));
            loadCommonDTO.setCity(cityMap.get(loadCityOrderHisDO.getCityId()));
            loadCommonDTOS.add(loadCommonDTO);
        }
        return loadCommonDTOS;
    }
}
