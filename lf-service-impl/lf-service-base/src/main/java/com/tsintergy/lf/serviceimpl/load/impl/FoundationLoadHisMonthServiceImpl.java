/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.load.impl;

import com.tsintergy.lf.serviceapi.base.airconditioner.api.FoundationLoadHisMonthService;
import com.tsintergy.lf.serviceapi.base.airconditioner.pojo.FoundationLoadHisMonthDO;
import com.tsintergy.lf.serviceimpl.load.dao.FoundationLoadHisMonthDAO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Description:<br>
 *
 * @Author:<EMAIL>
 * @Date: 2022/5/24 10:43
 * @Version:1.0.0
 */
@Service("foundationLoadHisMonthService")
public class FoundationLoadHisMonthServiceImpl implements FoundationLoadHisMonthService {

    @Autowired
    FoundationLoadHisMonthDAO foundationLoadHisMonthDAO;



    @Override
    public List<FoundationLoadHisMonthDO> getFoundationLoadHisMonth(String cityId, String caliberId, String year,
                                                                    String month, Integer type,String seasonType) {
        return foundationLoadHisMonthDAO.getFoundationLoadHisMonth(cityId, caliberId, year, month, type,seasonType);
    }

    @Override
    public void doSave(FoundationLoadHisMonthDO foundationLoadHisMonthDO) {
        foundationLoadHisMonthDAO.save(foundationLoadHisMonthDO);
    }

    @Override
    public void doUpdate(FoundationLoadHisMonthDO foundationLoadHisMonthDO) {
        foundationLoadHisMonthDAO.update(foundationLoadHisMonthDO);
    }

    @Override
    public void delete(FoundationLoadHisMonthDO foundationLoadHisMonthDO) {
        foundationLoadHisMonthDAO.delete(foundationLoadHisMonthDO);
    }


}