package com.tsintergy.lf.serviceimpl.bgd.impl;

import com.tsieframework.core.base.math.BigDecimalFunctions;
import com.tsieframework.core.base.service.BaseFacadeServiceImpl;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.util.compatible.BigDecimalUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.enums.TenDaysTypeEnum;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.bgd.api.CityAllReportService;
import com.tsintergy.lf.serviceapi.base.bgd.api.LoadFeatureCityTenDaysFcService;
import com.tsintergy.lf.serviceapi.base.bgd.api.LoadFeatureCityTenDaysHisService;
import com.tsintergy.lf.serviceapi.base.bgd.dto.ShortDateValuesDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.ShortReportResultDTO;
import com.tsintergy.lf.serviceapi.base.bgd.enums.CityReportTypeEnum;
import com.tsintergy.lf.serviceapi.base.bgd.pojo.LoadFeatureCityTenDaysFcServiceDO;
import com.tsintergy.lf.serviceapi.base.bgd.pojo.LoadFeatureCityTenDaysHisServiceDO;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcService;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityMonthFcService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityMonthHisService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityMonthHisDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCitySeasonFcDO;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityDayFcService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayFcDO;
import com.tsintergy.lf.serviceimpl.system.dao.SettingSystemDAO;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2023-06-30
 * @since 1.0.0
 */
@Service("cityAllReportService")
public class CityAllReportServiceImpl extends BaseFacadeServiceImpl implements CityAllReportService {

    @Autowired
    private LoadCityFcService loadCityFcService;

    @Autowired
    private CityService cityService;

    @Autowired
    private SettingSystemDAO settingSystemDAO;

    @Autowired
    private WeatherFeatureCityDayFcService weatherFeatureCityDayFcService;

    @Autowired
    private LoadFeatureCityTenDaysFcService loadFeatureCityTenDaysFcService;

    @Autowired
    private LoadFeatureCityTenDaysHisService loadFeatureCityTenDaysHisService;

    @Autowired
    private LoadFeatureCityMonthFcService loadFeatureCityMonthFcService;

    @Autowired
    private LoadFeatureCityMonthHisService loadFeatureCityMonthHisService;

    @Override
    public List<ShortReportResultDTO> getShortReport(String startStr, String endStr, String typeName,
        String caliberName, Integer featureType, String loadType) throws Exception {
        List<ShortReportResultDTO> result = new ArrayList<>(16);
        Date startDate = DateUtil.getDate(startStr, "yyyy-MM-dd");
        Date endDate = DateUtil.getDate(endStr, "yyyy-MM-dd");
        List<Date> listBetweenDay = DateUtil.getListBetweenDay(startDate, endDate);
        // 默认设置等
        String caliberId = Constants.CALIBER_ID_BG_DS;
        if (CityReportTypeEnum.CITY_ELECTRICITY.getName().equals(typeName)) {
            typeName = CityReportTypeEnum.CITY_ELECTRICITY.getName();
            for (CityReportTypeEnum value : CityReportTypeEnum.values()) {
                if (value.getName().equals(caliberName)) {
                    caliberName = value.getName();
                    caliberId = value.getType();
                }
            }
        } else {
            caliberId = "";
        }
        // 查询历史数据
        Map<String, List<LoadCityFcDO>> collect = new HashMap<>(16);
        List<LoadCityFcDO> collectCity = new ArrayList<>(16);
        List<LoadCityFcDO> reportLoadFc = new ArrayList<>(16);
        if (CityReportTypeEnum.CITY_DIAODU.getName().equals(caliberName)) {
            // 上报
            reportLoadFc = loadCityFcService.findReportLoadFc(null, null, startDate, endDate);
        } else {
            reportLoadFc = loadCityFcService.listLoadCityFc(null, null, startDate, endDate, null);
        }
        if (CollectionUtils.isNotEmpty(reportLoadFc)) {
            collect = reportLoadFc.stream()
                .collect(Collectors.groupingBy(t -> t.getCityId()));
            if (featureType == 2) {
                collectCity = reportLoadFc.stream().filter(t -> !t.getCityId().equals("1"))
                    .collect(Collectors.toList());
            }
        }
        List<CityDO> allCitys = cityService.findAllCitys();
        List<String> totals = new ArrayList<>(16);
        for (CityDO allCity : allCitys) {
            String name = "";
            ShortReportResultDTO shortReportResultDTO = new ShortReportResultDTO();
            if (Constants.PROVINCE_ID.equals(allCity.getId()) && CityReportTypeEnum.CITY_ELECTRICITY.getName()
                .equals(typeName)) {
                continue;
            }
            if ("".equals(caliberId) && CityReportTypeEnum.CITY_DIAODU.getName().equals(caliberName)) {
                if (Constants.PROVINCE_ID.equals(allCity.getId())) {
                    caliberId = Constants.CALIBER_ID_BG_QW;
                } else {
                    caliberId = Constants.CALIBER_ID_BG_DS;
                }
            }
            if ("".equals(caliberId) && CityReportTypeEnum.CITY_WANGGONG.getName().equals(caliberName)) {
                if (Constants.PROVINCE_ID.equals(allCity.getId())) {
                    caliberId = Constants.CALIBER_ID_TD;
                } else {
                    caliberId = Constants.CALIBER_ID_WG;
                }
            }
            // 地区拼接
            if (CityReportTypeEnum.CITY_ELECTRICITY.getName().equals(typeName)) {
                name = allCity.getCity() + caliberName;
            } else {
                if (CityReportTypeEnum.CITY_WANGGONG.getName().equals(caliberName) && Constants.PROVINCE_ID.equals(allCity.getId())) {
                    name = "湖北统调";
                } else if (CityReportTypeEnum.CITY_WANGGONG.getName().equals(caliberName) && !Constants.PROVINCE_ID.equals(allCity.getId())) {
                    name = allCity.getCity() + "网供";
                } else if (CityReportTypeEnum.CITY_DIAODU.getName().equals(caliberName) && Constants.PROVINCE_ID.equals(allCity.getId())) {
                    name = "湖北净负荷";
                } else if (CityReportTypeEnum.CITY_DIAODU.getName().equals(caliberName) && !Constants.PROVINCE_ID.equals(allCity.getId())) {
                    name = allCity.getCity() + "调度";
                }
            }
            shortReportResultDTO.setRegionType(name);
            shortReportResultDTO.setType(loadType);
            // 最大负荷、最小负荷、最小腰荷、日电量
            List<LoadCityFcDO> loadCityFcDOS = collect.get(allCity.getId());
            List<LoadCityFcDO> loadCityFcDOSRes = new ArrayList<>();
            if (CollectionUtils.isEmpty(loadCityFcDOS)) {
                continue;
            }
            if (CityReportTypeEnum.CITY_ELECTRICITY.getName().equals(typeName)) {
                if (CityReportTypeEnum.CITY_FENGDIAN.getName().equals(caliberName)) {
                    loadCityFcDOSRes = loadCityFcDOS.stream().filter(t -> t.getCaliberId().equals("9")).collect(Collectors.toList());
                } else if (CityReportTypeEnum.CITY_GUANGFU.getName().equals(caliberName)) {
                    loadCityFcDOSRes = loadCityFcDOS.stream().filter(t -> t.getCaliberId().equals("8")).collect(Collectors.toList());
                } else if (CityReportTypeEnum.CITY_SHUIDIAN.getName().equals(caliberName)) {
                    loadCityFcDOSRes = loadCityFcDOS.stream().filter(t -> t.getCaliberId().equals("6")).collect(Collectors.toList());
                } else {
                    loadCityFcDOSRes = loadCityFcDOS.stream().filter(t -> t.getCaliberId().equals("7")).collect(Collectors.toList());
                }
            } else {
                if (CityReportTypeEnum.CITY_DIAODU.getName().equals(caliberName) && Constants.PROVINCE_ID.equals(allCity.getId())) {
                    // 调度，湖北的口径为4 地区的为2
                    loadCityFcDOSRes = loadCityFcDOS.stream().filter(t -> t.getCaliberId().equals("4")).collect(Collectors.toList());
                } else if (CityReportTypeEnum.CITY_DIAODU.getName().equals(caliberName) && !Constants.PROVINCE_ID.equals(allCity.getId())) {
                    loadCityFcDOSRes = loadCityFcDOS.stream().filter(t -> t.getCaliberId().equals("2")).collect(Collectors.toList());
                } else if (CityReportTypeEnum.CITY_WANGGONG.getName().equals(caliberName) && Constants.PROVINCE_ID.equals(allCity.getId())) {
                    loadCityFcDOSRes = loadCityFcDOS.stream().filter(t -> t.getCaliberId().equals("12")).collect(Collectors.toList());
                } else if (CityReportTypeEnum.CITY_WANGGONG.getName().equals(caliberName)
                    && !Constants.PROVINCE_ID.equals(allCity.getId())) {
                    loadCityFcDOSRes = loadCityFcDOS.stream()
                        .filter(t -> t.getCaliberId().equals("1")).collect(Collectors.toList());
                }
            }
            // 地市特性值
            if (featureType == 1) {
                if (CollectionUtils.isNotEmpty(loadCityFcDOSRes)) {
                    List<ShortDateValuesDTO> shortDateValuesDTOS1 = doLoad(loadCityFcDOSRes, loadType, listBetweenDay, null);
                    shortReportResultDTO.setShortDateValuesDTOS(shortDateValuesDTOS1);
                }
                shortReportResultDTO.setFeatureType("地市特性值");
                result.add(shortReportResultDTO);
            } else {
                if (CollectionUtils.isNotEmpty(collectCity)) {
                    List<String> strings = doLoad1(collectCity, loadType, listBetweenDay, typeName, caliberName);
                    if (CollectionUtils.isNotEmpty(loadCityFcDOSRes)) {
                        List<ShortDateValuesDTO> shortDateValuesDTOS1 = doLoad(loadCityFcDOSRes, loadType, listBetweenDay, strings);
                        shortReportResultDTO.setShortDateValuesDTOS(shortDateValuesDTOS1);
                    }
                    totals = strings;
                }
                shortReportResultDTO.setFeatureType("合计特性值");
                result.add(shortReportResultDTO);
            }
        }
        // 合计
        List<ShortDateValuesDTO> totalList = new ArrayList<>();
        for (Date date : listBetweenDay) {
            ShortDateValuesDTO shortDateValuesDTO = new ShortDateValuesDTO();
            BigDecimal decimal = new BigDecimal("0");
            String dateStr = DateUtil.getDateToStrFORMAT(date, "yyyy-MM-dd");
            shortDateValuesDTO.setDate(dateStr);
            List<ShortReportResultDTO> allCity = result.stream().filter(t -> !t.getRegionType().substring(0, 2).equals("湖北"))
                .collect(Collectors.toList());
            for (ShortReportResultDTO shortReportResultDTO : allCity) {
                Map<String, List<ShortDateValuesDTO>> collect1 = new HashMap<>(16);
                if (CollectionUtils.isNotEmpty(shortReportResultDTO.getShortDateValuesDTOS())) {
                    collect1 = shortReportResultDTO.getShortDateValuesDTOS()
                        .stream().collect(Collectors.groupingBy(t -> t.getDate()));
                }
                List<ShortDateValuesDTO> shortDateValuesDTOS = collect1.get(dateStr);
                BigDecimal decimal1 = null;
                if (CollectionUtils.isNotEmpty(shortDateValuesDTOS)) {
                    decimal1 = shortDateValuesDTOS.get(0).getDecimal();
                }
                if (decimal1 != null) {
                    decimal = decimal.add(decimal1);
                }
            }
            if (featureType == 1 || loadType.equals("日电量")) {
                if (new BigDecimal("0").equals(decimal)) {
                    shortDateValuesDTO.setDecimal(null);
                } else {
                    shortDateValuesDTO.setDecimal(decimal);
                }
            } else if (featureType == 2 && !loadType.equals("日电量")) {
                if (CollectionUtils.isNotEmpty(totals)) {
                    for (String total : totals) {
                        if (dateStr.equals(total.split("~")[0])) {
                            if ("0".equals(total.split("~")[2])) {
                                shortDateValuesDTO.setDecimal(null);
                            } else {
                                shortDateValuesDTO.setDecimal(new BigDecimal(total.split("~")[2]));
                            }
                        }
                    }
                }
            }
            totalList.add(shortDateValuesDTO);
        }
        // 插入合计
        ShortReportResultDTO shortReportResultDTO = new ShortReportResultDTO();
        shortReportResultDTO.setType(loadType);
        shortReportResultDTO.setRegionType("合计");
        shortReportResultDTO.setShortDateValuesDTOS(totalList);
        shortReportResultDTO.setFeatureType("地市特性值");
        if (featureType == 2) {
            shortReportResultDTO.setFeatureType("合计特性值");
        }
        result.add(shortReportResultDTO);
        return result;
    }

    @Override
    public List<ShortReportResultDTO> getMonthReport(String startStr, String type, Integer featureType, String loadType)
        throws Exception {
        String sYear = startStr.split("-")[0];
        String sMonth = startStr.split("-")[1];
        List<String> yearList = new ArrayList<>(16);
        for (int i = 1; i <= 5; i++) {
            yearList.add(String.valueOf(Integer.valueOf(sYear) - i));
        }
        List<ShortReportResultDTO> result = new ArrayList<>(16);
        List<LoadFeatureCityTenDaysFcServiceDO> all = new ArrayList<>(16);
        List<LoadFeatureCityTenDaysHisServiceDO> hisAll = new ArrayList<>(16);
        List<LoadFeatureCityTenDaysFcServiceDO> monthReportVO = loadFeatureCityTenDaysFcService.getMonthReportVO(
            Constants.PROVINCE_ID, Constants.CALIBER_ID_BG_QW, startStr.split("-")[0], startStr.split("-")[1]);
        List<LoadFeatureCityTenDaysFcServiceDO> cityReportVO = loadFeatureCityTenDaysFcService.getMonthReportVO("",
                Constants.CALIBER_ID_BG_DS, startStr.split("-")[0], startStr.split("-")[1]).stream()
            .filter(t -> !t.getCityId().equals("1")).collect(Collectors.toList());
        List<LoadFeatureCityTenDaysHisServiceDO> collect1 = loadFeatureCityTenDaysHisService.findLoadFeatureCityTenDaysHisServiceDOs(
                null, Constants.CALIBER_ID_BG_DS, yearList, sMonth).stream()
            .filter(t -> !t.getCityId().equals("1")).collect(Collectors.toList());
        List<LoadFeatureCityTenDaysHisServiceDO> collect2 = loadFeatureCityTenDaysHisService.findLoadFeatureCityTenDaysHisServiceDOs(
            Constants.PROVINCE_ID, Constants.CALIBER_ID_BG_QW, yearList, sMonth);
        hisAll.addAll(collect1);
        hisAll.addAll(collect2);
        all.addAll(monthReportVO);
        all.addAll(cityReportVO);
        Map<String, List<LoadFeatureCityTenDaysFcServiceDO>> collect = all.stream()
            .collect(Collectors.groupingBy(t -> t.getCityId()));
        List<CityDO> allCity = cityService.findAllCitys();
        for (CityDO cityDO : allCity) {
            if (cityDO.getId().equals("16") || cityDO.getId().equals("17")) {
                continue;
            }
            ShortReportResultDTO shortReportResultDTO = new ShortReportResultDTO();
            shortReportResultDTO.setRegionType(cityDO.getCity() + "调度");
            if (cityDO.getId().equals(Constants.PROVINCE_ID)) {
                shortReportResultDTO.setRegionType(cityDO.getCity() + "净负荷");
            }
            shortReportResultDTO.setDateStr(startStr);
            if (!type.equals("月度")) {
                shortReportResultDTO.setDateStr(startStr + type);
            }
            shortReportResultDTO.setType(loadType);
            List<LoadFeatureCityTenDaysFcServiceDO> loadFeatureCityTenDaysFcServiceDOS = collect.get(
                cityDO.getId());
            if (featureType == 1 || !type.equals("月度") || loadType.equals("总电量")) {
                if (CollectionUtils.isNotEmpty(loadFeatureCityTenDaysFcServiceDOS)) {
                    // 月度
                    if (type.equals("月度")) {
                        if (loadType.equals("最大负荷")) {
                            Optional<LoadFeatureCityTenDaysFcServiceDO> max = loadFeatureCityTenDaysFcServiceDOS.stream()
                                .filter(t -> t.getMaxLoad() != null)
                                .max(Comparator.comparing(LoadFeatureCityTenDaysFcServiceDO::getMaxLoad));
                            if (max.isPresent()) {
                                BigDecimal maxLoad = max.get().getMaxLoad();
                                shortReportResultDTO.setNormalData(maxLoad);
                            }
                            Optional<LoadFeatureCityTenDaysFcServiceDO> max1 = loadFeatureCityTenDaysFcServiceDOS.stream()
                                .filter(t -> t.getExtremeMaxLoad() != null)
                                .max(Comparator.comparing(LoadFeatureCityTenDaysFcServiceDO::getExtremeMaxLoad));
                            if (max1.isPresent()) {
                                BigDecimal extremeMaxLoad = max1.get().getExtremeMaxLoad();
                                shortReportResultDTO.setExtremeData(extremeMaxLoad);
                            }
                        } else if (loadType.equals("最小负荷")) {
                            Optional<LoadFeatureCityTenDaysFcServiceDO> min = loadFeatureCityTenDaysFcServiceDOS.stream()
                                .filter(t -> t.getMinLoad() != null)
                                .min(Comparator.comparing(LoadFeatureCityTenDaysFcServiceDO::getMinLoad));
                            if (min.isPresent()) {
                                BigDecimal minLoad = min.get().getMinLoad();
                                shortReportResultDTO.setNormalData(minLoad);
                            }
                            Optional<LoadFeatureCityTenDaysFcServiceDO> min1 = loadFeatureCityTenDaysFcServiceDOS.stream()
                                .filter(t -> t.getExtremeMinLoad() != null)
                                .min(Comparator.comparing(LoadFeatureCityTenDaysFcServiceDO::getExtremeMinLoad));
                            if (min1.isPresent()) {
                                BigDecimal extremeMinLoad = min1.get().getExtremeMinLoad();
                                shortReportResultDTO.setExtremeData(extremeMinLoad);
                            }
                        } else if (loadType.equals("最小腰荷")) {
                            Optional<LoadFeatureCityTenDaysFcServiceDO> min = loadFeatureCityTenDaysFcServiceDOS.stream()
                                .filter(t -> t.getDayUnbalance() != null)
                                .min(Comparator.comparing(LoadFeatureCityTenDaysFcServiceDO::getDayUnbalance));
                            if (min.isPresent()) {
                                BigDecimal dayUnbalance = min.get().getDayUnbalance();
                                shortReportResultDTO.setNormalData(dayUnbalance);
                            }
                            Optional<LoadFeatureCityTenDaysFcServiceDO> min1 = loadFeatureCityTenDaysFcServiceDOS.stream()
                                .filter(t -> t.getExtremeDayUnbalance() != null)
                                .min(Comparator.comparing(LoadFeatureCityTenDaysFcServiceDO::getExtremeDayUnbalance));
                            if (min1.isPresent()) {
                                BigDecimal extremeDayUnbalance = min1.get().getExtremeDayUnbalance();
                                shortReportResultDTO.setExtremeData(extremeDayUnbalance);
                            }
                        } else {
                            BigDecimal energy = loadFeatureCityTenDaysFcServiceDOS.stream()
                                .filter(t -> t.getEnergy() != null).map(LoadFeatureCityTenDaysFcServiceDO::getEnergy)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                            BigDecimal energy1 = loadFeatureCityTenDaysFcServiceDOS.stream()
                                .filter(t -> t.getExtremeEnergy() != null).map(LoadFeatureCityTenDaysFcServiceDO::getExtremeEnergy)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                            shortReportResultDTO.setNormalData(energy);
                            shortReportResultDTO.setExtremeData(energy1);
                            if (new BigDecimal("0").equals(energy)) {
                                shortReportResultDTO.setNormalData(null);
                            }
                            if (new BigDecimal("0").equals(energy1)) {
                                shortReportResultDTO.setExtremeData(null);
                            }
                        }
                    } else {
                        // 分旬
                        for (LoadFeatureCityTenDaysFcServiceDO fenXun : loadFeatureCityTenDaysFcServiceDOS) {
                            if (fenXun.getType().equals(type)) {
                                if (loadType.equals("最大负荷")) {
                                    BigDecimal maxLoad = fenXun.getMaxLoad();
                                    BigDecimal extremeMaxLoad = fenXun.getExtremeMaxLoad();
                                    shortReportResultDTO.setNormalData(maxLoad);
                                    shortReportResultDTO.setExtremeData(extremeMaxLoad);
                                } else if (loadType.equals("最小负荷")) {
                                    BigDecimal minLoad = fenXun.getMinLoad();
                                    BigDecimal extremeMinLoad = fenXun.getExtremeMinLoad();
                                    shortReportResultDTO.setNormalData(minLoad);
                                    shortReportResultDTO.setExtremeData(extremeMinLoad);
                                } else if (loadType.equals("最小腰荷")) {
                                    BigDecimal dayUnbalance = fenXun.getDayUnbalance();
                                    BigDecimal extremeDayUnbalance = fenXun.getExtremeDayUnbalance();
                                    shortReportResultDTO.setNormalData(dayUnbalance);
                                    shortReportResultDTO.setExtremeData(extremeDayUnbalance);
                                } else {
                                    BigDecimal energy = fenXun.getEnergy();
                                    BigDecimal extremeEnergy = fenXun.getExtremeEnergy();
                                    shortReportResultDTO.setNormalData(energy);
                                    shortReportResultDTO.setExtremeData(extremeEnergy);
                                }
                            }
                        }
                    }
                    // 统计历史
                    List<ShortDateValuesDTO> shortDateValuesDTOS = doHisLoad(hisAll, yearList, cityDO.getId(), type,
                        loadType);
                    shortReportResultDTO.setShortDateValuesDTOS(shortDateValuesDTOS);
                }
            } else {
                // 合计特性值（月度）
                String s = doFeatureLoad(all, hisAll, loadType);
                LoadFeatureCityTenDaysFcServiceDO loadFeatureFcDO = new LoadFeatureCityTenDaysFcServiceDO();
                if (CollectionUtils.isNotEmpty(loadFeatureCityTenDaysFcServiceDOS)) {
                    for (LoadFeatureCityTenDaysFcServiceDO loadFeatureCityTenDaysFcServiceDO : loadFeatureCityTenDaysFcServiceDOS) {
                        if (loadFeatureCityTenDaysFcServiceDO.getType().equals(s)) {
                            loadFeatureFcDO = loadFeatureCityTenDaysFcServiceDO;
                        }
                    }
                }
                BigDecimal data = null;
                BigDecimal extremeData = null;
                switch (loadType) {
                    case "最大负荷":
                        data = loadFeatureFcDO.getMaxLoad();
                        extremeData = loadFeatureFcDO.getExtremeMaxLoad();
                        break;
                    case "最小负荷":
                        data = loadFeatureFcDO.getMinLoad();
                        extremeData = loadFeatureFcDO.getExtremeMinLoad();
                        break;
                    case "最小腰荷":
                        data = loadFeatureFcDO.getDayUnbalance();
                        extremeData = loadFeatureFcDO.getExtremeDayUnbalance();
                        break;
                    case "总电量":
                        data = loadFeatureFcDO.getEnergy();
                        extremeData = loadFeatureFcDO.getExtremeEnergy();
                        break;
                    default:
                        data = null;
                }
                shortReportResultDTO.setNormalData(data);
                shortReportResultDTO.setExtremeData(extremeData);
                // 统计历史
                List<ShortDateValuesDTO> shortDateValuesDTOS = doHisLoad(hisAll, yearList, cityDO.getId(), s,
                    loadType);
                shortReportResultDTO.setShortDateValuesDTOS(shortDateValuesDTOS);
            }
            shortReportResultDTO.setFeatureType("地市特性值");
            if (featureType == 2) {
                shortReportResultDTO.setFeatureType("合计特性值");
            }
            result.add(shortReportResultDTO);
        }
        // 合计
        BigDecimal reduce = null;
        BigDecimal reduce1 = null;
        ShortReportResultDTO shortReportResultDTO = new ShortReportResultDTO();
        List<ShortDateValuesDTO> res = new ArrayList<>();
        ShortReportResultDTO shortReportResultDTO1 = new ShortReportResultDTO();
        if (featureType == 1 || !type.equals("月度")) {
            reduce = result.stream().filter(t -> t.getNormalData() != null)
                .filter(t -> !t.getRegionType().substring(0, 2).equals("湖北"))
                .map(ShortReportResultDTO::getNormalData)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            reduce1 = result.stream().filter(t -> t.getExtremeData() != null)
                .filter(t -> !t.getRegionType().substring(0, 2).equals("湖北"))
                .map(ShortReportResultDTO::getExtremeData)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            List<List<ShortDateValuesDTO>> collect3 = result.stream()
                .filter(t -> !t.getRegionType().substring(0, 2).equals("湖北"))
                .map(ShortReportResultDTO::getShortDateValuesDTOS)
                .collect(Collectors.toList());
            for (String s : yearList) {
                String key = s + "年实际";
                List<BigDecimal> list = new ArrayList<>(16);
                ShortDateValuesDTO allDTO = new ShortDateValuesDTO();
                if (CollectionUtils.isNotEmpty(collect3)) {
                    for (List<ShortDateValuesDTO> shortDateValuesDTOS : collect3) {
                        if (CollectionUtils.isNotEmpty(shortDateValuesDTOS)) {
                            for (ShortDateValuesDTO shortDateValuesDTO : shortDateValuesDTOS) {
                                if (shortDateValuesDTO.getDate().equals(key)) {
                                    list.add(shortDateValuesDTO.getDecimal());
                                }
                            }
                        }
                    }
                }
                allDTO.setDate(key);
                BigDecimal bigDecimal = BigDecimalUtils.addAllValue(list);
                allDTO.setDecimal(bigDecimal);
                if (new BigDecimal("0").equals(bigDecimal)) {
                    allDTO.setDecimal(null);
                }
                res.add(allDTO);
            }
        } else {
            // 合计特性值月度情况
            shortReportResultDTO1 = doFeatureTotalLoad(all, hisAll, loadType, yearList);
        }
        shortReportResultDTO.setRegionType("合计");
        shortReportResultDTO.setType(loadType);
        shortReportResultDTO.setNormalData(reduce);
        shortReportResultDTO.setExtremeData(reduce1);
        shortReportResultDTO.setShortDateValuesDTOS(res);
        if (featureType == 2 && type.equals("月度")) {
            BigDecimal normalData = shortReportResultDTO1.getNormalData();
            BigDecimal extremeData = shortReportResultDTO1.getExtremeData();
            shortReportResultDTO.setNormalData(shortReportResultDTO1.getNormalData());
            shortReportResultDTO.setExtremeData(shortReportResultDTO1.getExtremeData());
            if (new BigDecimal("0").equals(normalData)) {
                shortReportResultDTO.setNormalData(null);
            }
            if (new BigDecimal("0").equals(extremeData)) {
                shortReportResultDTO.setExtremeData(null);
            }
            shortReportResultDTO.setShortDateValuesDTOS(shortReportResultDTO1.getShortDateValuesDTOS());
        }
        shortReportResultDTO.setFeatureType("地市特性值");
        if (featureType == 2) {
            shortReportResultDTO.setFeatureType("合计特性值");
        }
        result.add(shortReportResultDTO);
        return result;
    }

    @Override
    public List<ShortReportResultDTO> getShortWeather(String startStr, String endStr)
        throws Exception {
        List<String> weatherTypeList = new ArrayList<>();
        weatherTypeList.add("最高温度");
        weatherTypeList.add("最低温度");
        List<ShortReportResultDTO> result = new ArrayList<>(16);
        Date startDate = DateUtil.getDate(startStr, "yyyy-MM-dd");
        Date endDate = DateUtil.getDate(endStr, "yyyy-MM-dd");
        List<Date> listBetweenDay = DateUtil.getListBetweenDay(startDate, endDate);
        List<WeatherFeatureCityDayFcDO> weatherFeatureCityDayFcDOList = weatherFeatureCityDayFcService.getWeatherFeatureCityDayFcDOList(
            null, startDate, endDate);
        Map<String, List<WeatherFeatureCityDayFcDO>> collect = new HashMap<>(16);
        if (CollectionUtils.isNotEmpty(weatherFeatureCityDayFcDOList)) {
            collect = weatherFeatureCityDayFcDOList.stream()
                .collect(Collectors.groupingBy(t -> t.getCityId()));
        }
        List<CityDO> allCity = cityService.findAllCitys();
        for (String s : weatherTypeList) {
            for (CityDO cityDO : allCity) {
                List<ShortDateValuesDTO> shortDateValuesDTOS = new ArrayList<>(16);
                ShortReportResultDTO shortReportResultDTO = new ShortReportResultDTO();
                shortReportResultDTO.setRegionType(cityDO.getCity());
                shortReportResultDTO.setType(s);
                String id = cityDO.getId();
                if (id.equals("16") || id.equals("17")) {
                    continue;
                }
                List<WeatherFeatureCityDayFcDO> weatherFeatureCityDayFcDOS = collect.get(id);
                if (CollectionUtils.isNotEmpty(weatherFeatureCityDayFcDOS)) {
                    for (Date date : listBetweenDay) {
                        ShortDateValuesDTO shortDateValuesDTO = new ShortDateValuesDTO();
                        String dateStr = DateUtil.getDateToStrFORMAT(date, "yyyy-MM-dd");
                        shortDateValuesDTO.setDate(dateStr);
                        for (WeatherFeatureCityDayFcDO weatherFeatureCityDayFcDO : weatherFeatureCityDayFcDOS) {
                            if (date.compareTo(weatherFeatureCityDayFcDO.getDate()) == 0) {
                                BigDecimal highestTemperature = weatherFeatureCityDayFcDO.getHighestTemperature();
                                if (s.equals("最低温度")) {
                                    highestTemperature = weatherFeatureCityDayFcDO.getLowestTemperature();
                                }
                                shortDateValuesDTO.setDecimal(highestTemperature);
                            }
                        }
                        shortDateValuesDTOS.add(shortDateValuesDTO);
                    }
                }
                shortReportResultDTO.setShortDateValuesDTOS(shortDateValuesDTOS);
                result.add(shortReportResultDTO);
            }
        }
        return result;
    }

    @Override
    public List<ShortReportResultDTO> getSeasonReport(Date startStr, Integer type, Date endStr, Integer featureType,
        String loadType) throws Exception {
        List<ShortReportResultDTO> result = new ArrayList<>(16);
        String strStrat = DateUtil.getDateToStrFORMAT(startStr, "yyyy-MM");
        String strEnd = DateUtil.getDateToStrFORMAT(endStr, "yyyy-MM");
        String lastStart = null;
        String lastEnd = null;
        String caliberId = Constants.CALIBER_ID_BG_DS;
        List<LoadFeatureCitySeasonFcDO> fcAll = new ArrayList<>(16);
        List<String> targetYearList = DateUtil.getTargetYearList(strStrat, strEnd);
        if (type == 1) {
            // 度夏
            List<LoadFeatureCitySeasonFcDO> winterNewReportFeatures = loadFeatureCityMonthFcService.getWinterNewReportFeatures(
                strStrat, strEnd, "1", "4", "2");
            List<LoadFeatureCitySeasonFcDO> collect = loadFeatureCityMonthFcService.getWinterNewReportFeatures(
                    strStrat, strEnd, null, "2", "2").stream().filter(t -> !t.getCityId().equals("1"))
                .collect(Collectors.toList());
            fcAll.addAll(winterNewReportFeatures);
            fcAll.addAll(collect);
        } else {
            // 度冬
            List<LoadFeatureCitySeasonFcDO> winterNewReportFeatures = loadFeatureCityMonthFcService.getWinterNewReportFeatures(
                strStrat, strEnd, "1", "4", "4");
            List<LoadFeatureCitySeasonFcDO> collect = loadFeatureCityMonthFcService.getWinterNewReportFeatures(
                    strStrat, strEnd, null, "2", "4").stream().filter(t -> !t.getCityId().equals("1"))
                .collect(Collectors.toList());
            fcAll.addAll(winterNewReportFeatures);
            fcAll.addAll(collect);
        }
        Map<String, List<LoadFeatureCitySeasonFcDO>> collectFc = new HashMap<>(16);
        if (CollectionUtils.isNotEmpty(fcAll)) {
            collectFc = fcAll.stream().collect(Collectors.groupingBy(t -> t.getCityId()));
        }
        List<CityDO> allCity = cityService.findAllCitys();
        if (featureType == 1 || loadType.equals("总电量")) {
            for (CityDO cityDO : allCity) {
                if (cityDO.getId().equals("16") || cityDO.getId().equals("17")) {
                    continue;
                }
                if (cityDO.getId().equals(Constants.PROVINCE_ID)) {
                    caliberId = Constants.CALIBER_ID_BG_QW;
                }
                ShortReportResultDTO shortReportResultDTO = new ShortReportResultDTO();
                shortReportResultDTO.setRegionType(cityDO.getCity() + "调度");
                if (cityDO.getId().equals(Constants.PROVINCE_ID)) {
                    shortReportResultDTO.setRegionType(cityDO.getCity() + "净负荷");
                }
                shortReportResultDTO.setDateStr(strStrat + "~" + strEnd);
                shortReportResultDTO.setType(loadType);
                shortReportResultDTO.setFeatureType("地市特性值");
                if (featureType == 2 && loadType.equals("总电量")) {
                    shortReportResultDTO.setFeatureType("合计特性值");
                }
                // 正常情况
                List<LoadFeatureCitySeasonFcDO> loadFeatureCitySeasonFcDOS = collectFc.get(cityDO.getId());
                BigDecimal data = null;
                BigDecimal extremeData = null;
                if (CollectionUtils.isNotEmpty(loadFeatureCitySeasonFcDOS)) {
                    switch (loadType){
                        case "最大负荷":
                            Optional<LoadFeatureCitySeasonFcDO> max = loadFeatureCitySeasonFcDOS.stream()
                                .filter(t -> t.getMaxLoad() != null)
                                .max(Comparator.comparing(LoadFeatureCitySeasonFcDO::getMaxLoad));
                            if (max.isPresent()) {
                                data = max.get().getMaxLoad();
                            }
                            Optional<LoadFeatureCitySeasonFcDO> max1 = loadFeatureCitySeasonFcDOS.stream()
                                .filter(t -> t.getExtremeMaxLoad() != null)
                                .max(Comparator.comparing(LoadFeatureCitySeasonFcDO::getExtremeMaxLoad));
                            if (max1.isPresent()) {
                                extremeData = max1.get().getExtremeMaxLoad();
                            }
                            break;
                        case "最小负荷":
                            Optional<LoadFeatureCitySeasonFcDO> min = loadFeatureCitySeasonFcDOS.stream()
                                .filter(t -> t.getMinLoad() != null)
                                .min(Comparator.comparing(LoadFeatureCitySeasonFcDO::getMinLoad));
                            if (min.isPresent()) {
                                data = min.get().getMinLoad();
                            }
                            Optional<LoadFeatureCitySeasonFcDO> min1 = loadFeatureCitySeasonFcDOS.stream()
                                .filter(t -> t.getExtremeMinLoad() != null)
                                .min(Comparator.comparing(LoadFeatureCitySeasonFcDO::getExtremeMinLoad));
                            if (min1.isPresent()) {
                                extremeData = min1.get().getExtremeMinLoad();
                            }
                            break;
                        case "最小腰荷":
                            Optional<LoadFeatureCitySeasonFcDO> min2 = loadFeatureCitySeasonFcDOS.stream()
                                .filter(t -> t.getNoontimeLoad() != null)
                                .min(Comparator.comparing(LoadFeatureCitySeasonFcDO::getNoontimeLoad));
                            if (min2.isPresent()) {
                                data = min2.get().getNoontimeLoad();
                            }
                            Optional<LoadFeatureCitySeasonFcDO> min3 = loadFeatureCitySeasonFcDOS.stream()
                                .filter(t -> t.getExtremeNoontimeLoad() != null)
                                .min(Comparator.comparing(LoadFeatureCitySeasonFcDO::getExtremeNoontimeLoad));
                            if (min3.isPresent()) {
                                extremeData = min3.get().getExtremeNoontimeLoad();
                            }
                            break;
                        case "总电量":
                            data = loadFeatureCitySeasonFcDOS.stream()
                                .filter(t -> t.getEnergy() != null)
                                .map(LoadFeatureCitySeasonFcDO::getEnergy)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                            extremeData = loadFeatureCitySeasonFcDOS.stream()
                                .filter(t -> t.getExtremeEnergy() != null)
                                .map(LoadFeatureCitySeasonFcDO::getExtremeEnergy)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                            break;
                        default:
                            data = null;
                            extremeData = null;
                    }
                }
                shortReportResultDTO.setNormalData(data);
                // 极端情况
                shortReportResultDTO.setExtremeData(extremeData);
                if (new BigDecimal("0").equals(data)) {
                    shortReportResultDTO.setNormalData(null);
                }
                if (new BigDecimal("0").equals(extremeData)) {
                    shortReportResultDTO.setExtremeData(null);
                }
                // 历史5年
                List<ShortDateValuesDTO> res = new ArrayList<>(16);
                for (int i = 1; i <= 5; i++) {
                    ShortDateValuesDTO shortDateValuesDTO = new ShortDateValuesDTO();
                    lastStart = String.valueOf(Integer.valueOf(strStrat.split("-")[0]) - i) + "-" + strStrat.split("-")[1];
                    lastEnd = String.valueOf(Integer.valueOf(strEnd.split("-")[0]) - i) + "-" + strEnd.split("-")[1];
                    shortDateValuesDTO.setDate(lastStart.split("-")[0] + "年实际");
                    List<LoadFeatureCityMonthHisDO> loadFeatureCityMonthHisList = loadFeatureCityMonthHisService.getLoadFeatureCityMonthHisList(
                        cityDO.getId(), lastStart, lastEnd, caliberId);
                    // 一年一年的处理
                    Map<String, List<LoadFeatureCityMonthHisDO>> collect = new HashMap<>(16);
                    if (CollectionUtils.isNotEmpty(loadFeatureCityMonthHisList)) {
                        collect = loadFeatureCityMonthHisList.stream()
                            .collect(Collectors.groupingBy(t -> t.getCityId()));
                        List<LoadFeatureCityMonthHisDO> loadFeatureCityMonthHisDOS = collect.get(cityDO.getId());
                        if (CollectionUtils.isNotEmpty(loadFeatureCityMonthHisDOS)) {
                            BigDecimal dataHis = null;
                            switch (loadType){
                                case "最大负荷":
                                    Optional<LoadFeatureCityMonthHisDO> max = loadFeatureCityMonthHisDOS.stream()
                                        .filter(t -> t.getMaxLoad() != null)
                                        .max(Comparator.comparing(LoadFeatureCityMonthHisDO::getMaxLoad));
                                    if (max.isPresent()) {
                                        dataHis = max.get().getMaxLoad();
                                    }
                                    break;
                                case "最小负荷":
                                    Optional<LoadFeatureCityMonthHisDO> min = loadFeatureCityMonthHisDOS.stream()
                                        .filter(t -> t.getMinLoad() != null)
                                        .min(Comparator.comparing(LoadFeatureCityMonthHisDO::getMinLoad));
                                    if (min.isPresent()) {
                                        dataHis = min.get().getMinLoad();
                                    }
                                    break;
                                case "最小腰荷":
                                    Optional<LoadFeatureCityMonthHisDO> min1 = loadFeatureCityMonthHisDOS.stream()
                                        .filter(t -> t.getNoontimeLoad() != null)
                                        .min(Comparator.comparing(LoadFeatureCityMonthHisDO::getNoontimeLoad));
                                    if (min1.isPresent()) {
                                        dataHis = min1.get().getNoontimeLoad();
                                    }
                                    break;
                                case "总电量":
                                    dataHis = loadFeatureCityMonthHisDOS.stream()
                                        .filter(t -> t.getEnergy() != null)
                                        .map(LoadFeatureCityMonthHisDO::getEnergy)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                                    break;
                                default:
                                    dataHis = null;
                            }
                            shortDateValuesDTO.setDecimal(dataHis);
                        }
                    }
                    res.add(shortDateValuesDTO);
                }
                shortReportResultDTO.setShortDateValuesDTOS(res);
                result.add(shortReportResultDTO);
            }
            // 处理合计
            List<BigDecimal> collect = result.stream().filter(t -> t.getNormalData() != null)
                .filter(t -> !t.getRegionType().substring(0, 2).equals("湖北"))
                .map(ShortReportResultDTO::getNormalData)
                .collect(Collectors.toList());
            List<BigDecimal> collect1 = result.stream().filter(t -> t.getExtremeData() != null)
                .filter(t -> !t.getRegionType().substring(0, 2).equals("湖北"))
                .map(ShortReportResultDTO::getExtremeData)
                .collect(Collectors.toList());
            BigDecimal reduce = null;
            BigDecimal reduce1 = null;
            if (CollectionUtils.isNotEmpty(collect)) {
                reduce = collect.stream().filter(t -> t != null)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            }
            if (CollectionUtils.isNotEmpty(collect1)) {
                reduce1 = collect1.stream().filter(t -> t != null)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            }

            List<ShortDateValuesDTO> his = new ArrayList<>(16);
            List<List<ShortDateValuesDTO>> collect2 = result.stream()
                .filter(t -> !t.getRegionType().substring(0, 2).equals("湖北"))
                .map(ShortReportResultDTO::getShortDateValuesDTOS)
                .collect(Collectors.toList());
            for (int i = 1; i <=5; i++) {
                int a = Integer.valueOf(strStrat.split("-")[0]);
                a = a - i;
                ShortDateValuesDTO shortDateValuesDTO1 = new ShortDateValuesDTO();
                shortDateValuesDTO1.setDate(a + "年实际");
                BigDecimal decimal = null;
                List<BigDecimal> hisBigDecimal = new ArrayList<>(16);
                for (List<ShortDateValuesDTO> shortDateValuesDTOS : collect2) {
                    Map<String, List<ShortDateValuesDTO>> collect3 = shortDateValuesDTOS.stream()
                        .collect(Collectors.groupingBy(ShortDateValuesDTO::getDate));
                    List<ShortDateValuesDTO> shortDateValuesDTOS1 = collect3.get(a + "年实际");
                    if (CollectionUtils.isNotEmpty(shortDateValuesDTOS1)) {
                        ShortDateValuesDTO shortDateValuesDTO = shortDateValuesDTOS1.get(0);
                        BigDecimal decimal1 = shortDateValuesDTO.getDecimal();
                        if (decimal1 != null) {
                            hisBigDecimal.add(decimal1);
                        }
                    }
                }
                if (CollectionUtils.isNotEmpty(hisBigDecimal)) {
                    shortDateValuesDTO1.setDecimal(BigDecimalUtils.addAllValue(hisBigDecimal));
                }
                his.add(shortDateValuesDTO1);
            }
            ShortReportResultDTO shortReportResultDTO = new ShortReportResultDTO();
            shortReportResultDTO.setRegionType("合计");
            shortReportResultDTO.setNormalData(reduce);
            shortReportResultDTO.setExtremeData(reduce1);
            shortReportResultDTO.setType(loadType);
            shortReportResultDTO.setFeatureType("地市特性值");
            if (featureType == 2 && loadType.equals("总电量")) {
                shortReportResultDTO.setFeatureType("合计特性值");
            }
            shortReportResultDTO.setShortDateValuesDTOS(his);
            result.add(shortReportResultDTO);
        } else {
            int i = Integer.valueOf(strStrat.split("-")[0]);
            ShortReportResultDTO shortReportResultDTO = new ShortReportResultDTO();
            List<Map<String, BigDecimal>> maps = doWinterSummerFc(fcAll, loadType, targetYearList);
            List<Map<String, BigDecimal>> maps1 = doWinterSummerHis(loadType, targetYearList, strStrat, strEnd);
            // 合计特性取数
            List<ShortReportResultDTO> shortReportResultDTOS = featureSummerWinter(fcAll, loadType, strStrat, strEnd,
                maps, maps1);
            if (CollectionUtils.isNotEmpty(maps)) {
                Map<String, BigDecimal> stringBigDecimalMap = maps.get(0);
                Map<String, BigDecimal> stringBigDecimalMap1 = maps.get(1);
                for (String s : stringBigDecimalMap.keySet()) {
                    shortReportResultDTO.setNormalData(stringBigDecimalMap.get(s));
                }
                for (String s : stringBigDecimalMap1.keySet()) {
                    shortReportResultDTO.setExtremeData(stringBigDecimalMap1.get(s));
                }
            }
            List<ShortDateValuesDTO> his = new ArrayList<>(16);
            if (CollectionUtils.isNotEmpty(maps1)) {
                for (Map<String, BigDecimal> stringBigDecimalMap : maps1) {
                    ShortDateValuesDTO shortDateValuesDTO = new ShortDateValuesDTO();
                    for (String s : stringBigDecimalMap.keySet()) {
                        shortDateValuesDTO.setDecimal(stringBigDecimalMap.get(s));
                    }
                    i = i - 1;
                    shortDateValuesDTO.setDate(i + "年实际");
                    his.add(shortDateValuesDTO);
                }
            }
            shortReportResultDTO.setRegionType("合计");
            shortReportResultDTO.setType(loadType);
            shortReportResultDTO.setFeatureType("合计特性值");
            shortReportResultDTO.setShortDateValuesDTOS(his);
            // 处理合计
            result.addAll(shortReportResultDTOS);
            result.add(shortReportResultDTO);
        }
        return result;
    }

    @Override
    public List<ShortReportResultDTO> getYearReport(String startStr, String loadType, Integer featureType,
        String dataType) throws Exception {
        List<ShortReportResultDTO> result = new ArrayList<>(16);
        String year = startStr;
        Map<String, BigDecimal> map = new LinkedHashMap<>(16);
        List<String> strings = Arrays.asList("01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12");
        List<LoadFeatureCitySeasonFcDO> allFc = new ArrayList<>(16);
        List<LoadFeatureCitySeasonFcDO> allJD = new ArrayList<>(16);
        List<LoadFeatureCitySeasonFcDO> yearReportFeatures = loadFeatureCityMonthFcService.getYearReportFeatures(year,
            null, Constants.PROVINCE_ID, Constants.CALIBER_ID_BG_QW, "5");
        List<LoadFeatureCitySeasonFcDO> yearReportFeatures1 = loadFeatureCityMonthFcService.getYearReportFeatures(year,
            null, null, Constants.CALIBER_ID_BG_DS, "5").stream().filter(t -> !t.getCityId().equals("1")).collect(
            Collectors.toList());
        allFc.addAll(yearReportFeatures);
        allFc.addAll(yearReportFeatures1);
        if (dataType.equals("极端情况")) {
            for (LoadFeatureCitySeasonFcDO loadFeatureCitySeasonFcDO : allFc) {
                loadFeatureCitySeasonFcDO.setMaxLoad(loadFeatureCitySeasonFcDO.getExtremeMaxLoad());
                loadFeatureCitySeasonFcDO.setMinLoad(loadFeatureCitySeasonFcDO.getExtremeMinLoad());
                loadFeatureCitySeasonFcDO.setNoontimeLoad(loadFeatureCitySeasonFcDO.getNoontimeLoad());
                loadFeatureCitySeasonFcDO.setEnergy(loadFeatureCitySeasonFcDO.getExtremeEnergy());
                allJD.add(loadFeatureCitySeasonFcDO);
            }
            allFc = allJD;
        }
        Map<String, List<LoadFeatureCitySeasonFcDO>> collectFc = new HashMap<>(16);
        if (CollectionUtils.isNotEmpty(allFc)) {
            collectFc = allFc.stream().collect(Collectors.groupingBy(t -> t.getCityId()));
        }
        List<CityDO> allCity = cityService.findAllCitys();
        if (featureType == 1 || loadType.equals("总电量")) {
            for (CityDO cityDO : allCity) {
                if (cityDO.getId().equals("16") || cityDO.getId().equals("17")) {
                    continue;
                }
                ShortReportResultDTO shortReportResultDTO = new ShortReportResultDTO();
                shortReportResultDTO.setRegionType(cityDO.getCity() + "调度");
                if (cityDO.getId().equals(Constants.PROVINCE_ID)) {
                    shortReportResultDTO.setRegionType(cityDO.getCity() + "净负荷");
                }
                shortReportResultDTO.setType(loadType);
                shortReportResultDTO.setFeatureType("地市特性值");
                if (featureType == 2 && loadType.equals("总电量")) {
                    shortReportResultDTO.setFeatureType("合计特性值");
                }
                shortReportResultDTO.setDateType(dataType);
                List<LoadFeatureCitySeasonFcDO> loadFeatureCitySeasonFcDOS = collectFc.get(cityDO.getId());
                List<ShortDateValuesDTO> res = new ArrayList<>(16);
                List<BigDecimal> resNum = new ArrayList<>(16);
                if (CollectionUtils.isNotEmpty(loadFeatureCitySeasonFcDOS)) {
                    Map<String, List<LoadFeatureCitySeasonFcDO>> collect = loadFeatureCitySeasonFcDOS.stream()
                        .collect(Collectors.groupingBy(t -> t.getMonth()));
                    // 分月
                    for (String string : strings) {
                        ShortDateValuesDTO shortDateValuesDTO = new ShortDateValuesDTO();
                        BigDecimal data = null;
                        if (MapUtils.isNotEmpty(collect)) {
                            List<LoadFeatureCitySeasonFcDO> loadFeatureCitySeasonFcDOS1 = collect.get(string);
                            if (CollectionUtils.isNotEmpty(loadFeatureCitySeasonFcDOS1)) {
                                LoadFeatureCitySeasonFcDO loadFeatureCitySeasonFcDO = loadFeatureCitySeasonFcDOS1.get(0);
                                switch (loadType){
                                    case "最大负荷":
                                        data = loadFeatureCitySeasonFcDO.getMaxLoad();
                                        break;
                                    case "最小负荷":
                                        data = loadFeatureCitySeasonFcDO.getMinLoad();
                                        break;
                                    case "最小腰荷":
                                        data = loadFeatureCitySeasonFcDO.getNoontimeLoad();
                                        break;
                                    case "总电量":
                                        data = loadFeatureCitySeasonFcDO.getEnergy();
                                        break;
                                    default:
                                        data = null;
                                }
                            }
                            shortDateValuesDTO.setDecimal(data);
                            if (new BigDecimal("0").equals(data)) {
                                shortDateValuesDTO.setDecimal(null);
                            }
                            resNum.add(data);
                        }
                        if (Integer.valueOf(string) < 10) {
                            string = string.substring(1,2);
                        }
                        shortDateValuesDTO.setDate(string + "月");
                        if (!cityDO.getId().equals(Constants.PROVINCE_ID)) {
                            if (MapUtils.isNotEmpty(map)) {
                                BigDecimal bigDecimal = map.get(string + "月");
                                if (bigDecimal != null && data != null) {
                                    map.put(string + "月", bigDecimal.add(data));
                                } else {
                                    map.put(string + "月", data);
                                }
                            } else {
                                map.put(string + "月", data);
                            }
                        }
                        res.add(shortDateValuesDTO);
                    }
                    BigDecimal min = BigDecimalUtils.getMin(resNum);
                    if (loadType.equals("最大负荷")) {
                        min = BigDecimalUtils.getMax(resNum);
                    } else if (loadType.equals("总电量")) {
                        min = BigDecimalUtils.addAllValue(resNum);
                    }
                    shortReportResultDTO.setYearData(min);
                    if (new BigDecimal("0").equals(min)) {
                        shortReportResultDTO.setYearData(null);
                    }
                    shortReportResultDTO.setShortDateValuesDTOS(res);
                    // 2023年度
                }
                result.add(shortReportResultDTO);
            }
            // 合计
            ShortReportResultDTO shortReportResultDTO1 = new ShortReportResultDTO();
            shortReportResultDTO1.setRegionType("合计");
            shortReportResultDTO1.setType(loadType);
            shortReportResultDTO1.setFeatureType("地市特性值");
            if (featureType == 2 && loadType.equals("总电量")) {
                shortReportResultDTO1.setFeatureType("合计特性值");
            }
            shortReportResultDTO1.setDateType(dataType);
            BigDecimal reduce = result.stream().filter(t -> t.getYearData() != null)
                .filter(t -> !t.getRegionType().substring(0, 2).equals("湖北"))
                .map(ShortReportResultDTO::getYearData).reduce(BigDecimal.ZERO, BigDecimal::add);
            shortReportResultDTO1.setYearData(reduce);
            if (new BigDecimal("0").equals(reduce)) {
                shortReportResultDTO1.setYearData(null);
            }
            List<ShortDateValuesDTO> total = new ArrayList<>(16);
            for (String s : map.keySet()) {
                ShortDateValuesDTO shortDateValuesDTO = new ShortDateValuesDTO();
                shortDateValuesDTO.setDate(s);
                shortDateValuesDTO.setDecimal(map.get(s));
                if (new BigDecimal("0").equals(map.get(s))) {
                    shortDateValuesDTO.setDecimal(null);
                }
                total.add(shortDateValuesDTO);
            }
            shortReportResultDTO1.setShortDateValuesDTOS(total);
            result.add(shortReportResultDTO1);
        } else {
            // 合计特性值
            Map<String, BigDecimal> stringBigDecimalMap = yearTotal(allFc, loadType);
            String month = "";
            BigDecimal totalBigdecimal = null;
            for (String s : stringBigDecimalMap.keySet()) {
                month = s;
                totalBigdecimal = stringBigDecimalMap.get(s);
            }
            for (CityDO cityDO : allCity) {
                if (cityDO.getId().equals("16") || cityDO.getId().equals("17")) {
                    continue;
                }
                ShortReportResultDTO shortReportResultDTO = new ShortReportResultDTO();
                shortReportResultDTO.setRegionType(cityDO.getCity() + "调度");
                if (cityDO.getId().equals(Constants.PROVINCE_ID)) {
                    shortReportResultDTO.setRegionType(cityDO.getCity() + "净负荷");
                }
                shortReportResultDTO.setType(loadType);
                shortReportResultDTO.setFeatureType("合计特性值");
                shortReportResultDTO.setDateType(dataType);
                List<LoadFeatureCitySeasonFcDO> loadFeatureCitySeasonFcDOS = collectFc.get(cityDO.getId());
                List<ShortDateValuesDTO> res = new ArrayList<>(16);
                List<BigDecimal> resNum = new ArrayList<>(16);

                if (CollectionUtils.isNotEmpty(loadFeatureCitySeasonFcDOS)) {
                    Map<String, List<LoadFeatureCitySeasonFcDO>> collect = loadFeatureCitySeasonFcDOS.stream()
                        .collect(Collectors.groupingBy(t -> t.getMonth()));
                    // 分月
                    for (String string : strings) {
                        ShortDateValuesDTO shortDateValuesDTO = new ShortDateValuesDTO();
                        BigDecimal data = null;
                        if (MapUtils.isNotEmpty(collect)) {
                            List<LoadFeatureCitySeasonFcDO> loadFeatureCitySeasonFcDOS1 = collect.get(string);
                            if (CollectionUtils.isNotEmpty(loadFeatureCitySeasonFcDOS1)) {
                                LoadFeatureCitySeasonFcDO loadFeatureCitySeasonFcDO = loadFeatureCitySeasonFcDOS1.get(0);
                                switch (loadType){
                                    case "最大负荷":
                                        data = loadFeatureCitySeasonFcDO.getMaxLoad();
                                        break;
                                    case "最小负荷":
                                        data = loadFeatureCitySeasonFcDO.getMinLoad();
                                        break;
                                    case "最小腰荷":
                                        data = loadFeatureCitySeasonFcDO.getNoontimeLoad();
                                        break;
                                    case "总电量":
                                        data = loadFeatureCitySeasonFcDO.getEnergy();
                                        break;
                                    default:
                                        data = null;
                                }
                            }
                            shortDateValuesDTO.setDecimal(data);
                            if (new BigDecimal("0").equals(data)) {
                                shortDateValuesDTO.setDecimal(null);
                            }
                            resNum.add(data);
                        }
                        if (Integer.valueOf(string) < 10) {
                            string = string.substring(1,2);
                        }
                        shortDateValuesDTO.setDate(string + "月");
                        if (!cityDO.getId().equals(Constants.PROVINCE_ID)) {
                            if (MapUtils.isNotEmpty(map)) {
                                BigDecimal bigDecimal = map.get(string + "月");
                                if (bigDecimal != null && data != null) {
                                    map.put(string + "月", bigDecimal.add(data));
                                } else {
                                    map.put(string + "月", data);
                                }
                            } else {
                                map.put(string + "月", data);
                            }
                        }
                        res.add(shortDateValuesDTO);
                    }
                    BigDecimal data = null;
                    if (MapUtils.isNotEmpty(collect)) {
                        List<LoadFeatureCitySeasonFcDO> loadFeatureCitySeasonFcDOS1 = collect.get(month);
                        if (CollectionUtils.isNotEmpty(loadFeatureCitySeasonFcDOS1)) {
                            LoadFeatureCitySeasonFcDO loadFeatureCitySeasonFcDO = loadFeatureCitySeasonFcDOS1.get(0);
                            switch (loadType){
                                case "最大负荷":
                                    data = loadFeatureCitySeasonFcDO.getMaxLoad();
                                    break;
                                case "最小负荷":
                                    data = loadFeatureCitySeasonFcDO.getMinLoad();
                                    break;
                                case "最小腰荷":
                                    data = loadFeatureCitySeasonFcDO.getNoontimeLoad();
                                    break;
                                case "总电量":
                                    data = loadFeatureCitySeasonFcDO.getEnergy();
                                    break;
                                default:
                                    data = null;
                            }
                        }
                    }
                    shortReportResultDTO.setYearData(data);
                    if (new BigDecimal("0").equals(data)) {
                        shortReportResultDTO.setYearData(null);
                    }
                    shortReportResultDTO.setYearData(data);
                    shortReportResultDTO.setShortDateValuesDTOS(res);
                    // 2023年度
                }
                result.add(shortReportResultDTO);
            }
            // 合计
            ShortReportResultDTO shortReportResultDTO1 = new ShortReportResultDTO();
            shortReportResultDTO1.setRegionType("合计");
            shortReportResultDTO1.setType(loadType);
            shortReportResultDTO1.setFeatureType("合计特性值");
            shortReportResultDTO1.setDateType(dataType);
            BigDecimal reduce = result.stream().filter(t -> t.getYearData() != null)
                .filter(t -> !t.getRegionType().substring(0, 2).equals("湖北"))
                .map(ShortReportResultDTO::getYearData).reduce(BigDecimal.ZERO, BigDecimal::add);
            //.filter(t -> !t.getRegionType().substring(0, 2).equals("湖北"))
            shortReportResultDTO1.setYearData(totalBigdecimal);
            if (new BigDecimal("0").equals(totalBigdecimal)) {
                shortReportResultDTO1.setYearData(null);
            }
            List<ShortDateValuesDTO> total = new ArrayList<>(16);
            for (String s : map.keySet()) {
                ShortDateValuesDTO shortDateValuesDTO = new ShortDateValuesDTO();
                shortDateValuesDTO.setDate(s);
                shortDateValuesDTO.setDecimal(map.get(s));
                if (new BigDecimal("0").equals(map.get(s))) {
                    shortDateValuesDTO.setDecimal(null);
                }
                total.add(shortDateValuesDTO);
            }
            shortReportResultDTO1.setShortDateValuesDTOS(total);
            result.add(shortReportResultDTO1);
        }
        return result;
    }

    private Map<String, BigDecimal> yearTotal(List<LoadFeatureCitySeasonFcDO> allFc, String loadType) throws Exception {
        Map<String, BigDecimal> map = new HashMap<>(16);
        if (CollectionUtils.isNotEmpty(allFc)) {
            Map<String, List<LoadFeatureCitySeasonFcDO>> collect = allFc.stream()
                .filter(t -> !t.getCityId().equals("1"))
                .collect(Collectors.groupingBy(LoadFeatureCitySeasonFcDO::getMonth));
            for (String s : collect.keySet()) {
                List<LoadFeatureCitySeasonFcDO> loadFeatureCitySeasonFcDOS = collect.get(s);
                // 每个月所有的地市
                BigDecimal data = null;
                switch (loadType){
                    case "最大负荷":
                        data = loadFeatureCitySeasonFcDOS.stream()
                            .filter(t -> t.getMaxLoad() != null)
                            .map(LoadFeatureCitySeasonFcDO::getMaxLoad)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                        break;
                    case "最小负荷":
                        data = loadFeatureCitySeasonFcDOS.stream()
                            .filter(t -> t.getMinLoad() != null)
                            .map(LoadFeatureCitySeasonFcDO::getMinLoad)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                        break;
                    case "最小腰荷":
                        data = loadFeatureCitySeasonFcDOS.stream()
                            .filter(t -> t.getNoontimeLoad() != null)
                            .map(LoadFeatureCitySeasonFcDO::getNoontimeLoad)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                        break;
                    default:
                        data = null;
                }
                if (MapUtils.isEmpty(map)) {
                    map.put(s, data);
                } else {
                    for (String s1 : map.keySet()) {
                        BigDecimal bigDecimal = map.get(s1);
                        if (loadType.equals("最大负荷")) {
                            if (data != null && bigDecimal != null && data.compareTo(bigDecimal) > 0) {
                                map.clear();
                                map.put(s, data);
                            }
                        } else {
                            if (data != null && bigDecimal != null && data.compareTo(bigDecimal) < 0) {
                                map.clear();
                                map.put(s, data);
                            }
                        }
                    }
                }
            }
        }
        return map;
    }

    private List<ShortReportResultDTO> featureSummerWinter(List<LoadFeatureCitySeasonFcDO> fcAll, String loadType, String strStrat,
        String strEnd, List<Map<String, BigDecimal>> mapFc, List<Map<String, BigDecimal>> mapHis) throws Exception {
        List<ShortReportResultDTO> result = new ArrayList<>(16);
        // 预测
        String normal = "";
        String extreme = "";
        if (CollectionUtils.isNotEmpty(mapFc)) {
            Map<String, BigDecimal> stringBigDecimalMap1 = mapFc.get(0);
            Map<String, BigDecimal> stringBigDecimalMap2 = mapFc.get(1);
            for (String s : stringBigDecimalMap1.keySet()) {
                normal = s;
            }
            for (String s : stringBigDecimalMap2.keySet()) {
                extreme = s;
            }
        }
        Map<String, List<LoadFeatureCitySeasonFcDO>> collectFc = new HashMap<>(16);
        if (CollectionUtils.isNotEmpty(fcAll)) {
            collectFc = fcAll.stream().collect(Collectors.groupingBy(t -> t.getCityId()));
        }
        List<CityDO> allCity = cityService.findAllCitys();
        for (CityDO cityDO : allCity) {
            if (cityDO.getId().equals("16") || cityDO.getId().equals("17")) {
                continue;
            }
            int a = Integer.valueOf(strStrat.split("-")[0]);
            String caliberId = Constants.CALIBER_ID_BG_DS;
            ShortReportResultDTO shortReportResultDTO = new ShortReportResultDTO();
            shortReportResultDTO.setRegionType(cityDO.getCity() + "调度");
            if (cityDO.getId().equals(Constants.PROVINCE_ID)) {
                shortReportResultDTO.setRegionType(cityDO.getCity() + "净负荷");
            }
            shortReportResultDTO.setDateStr(strStrat + "~" + strEnd);
            shortReportResultDTO.setType(loadType);
            shortReportResultDTO.setFeatureType("合计特性值");
            // 正常情况
            List<LoadFeatureCitySeasonFcDO> loadFeatureCitySeasonFcDOS = collectFc.get(cityDO.getId());
            BigDecimal data = null;
            BigDecimal extremeData = null;
            if (CollectionUtils.isNotEmpty(loadFeatureCitySeasonFcDOS)) {
                switch (loadType){
                    case "最大负荷":
                        String finalNormal = normal;
                        String finalNormal1 = extreme;
                        Optional<LoadFeatureCitySeasonFcDO> max = loadFeatureCitySeasonFcDOS.stream()
                            .filter(t -> t.getMonth().equals(finalNormal))
                            .max(Comparator.comparing(LoadFeatureCitySeasonFcDO::getMaxLoad));
                        if (max.isPresent()) {
                            data = max.get().getMaxLoad();
                        }
                        Optional<LoadFeatureCitySeasonFcDO> max1 = loadFeatureCitySeasonFcDOS.stream()
                            .filter(t -> t.getMonth().equals(finalNormal1))
                            .filter(t -> t.getExtremeMaxLoad() != null)
                            .max(Comparator.comparing(LoadFeatureCitySeasonFcDO::getExtremeMaxLoad));
                        if (max1.isPresent()) {
                            extremeData = max1.get().getExtremeMaxLoad();
                        }
                        break;
                    case "最小负荷":
                        String finalNormal2 = normal;
                        String finalNormal3 = extreme;
                        Optional<LoadFeatureCitySeasonFcDO> min = loadFeatureCitySeasonFcDOS.stream()
                            .filter(t -> t.getMonth().equals(finalNormal2))
                            .min(Comparator.comparing(LoadFeatureCitySeasonFcDO::getMinLoad));
                        if (min.isPresent()) {
                            data = min.get().getMinLoad();
                        }
                        Optional<LoadFeatureCitySeasonFcDO> min1 = loadFeatureCitySeasonFcDOS.stream()
                            .filter(t -> t.getMonth().equals(finalNormal3))
                            .min(Comparator.comparing(LoadFeatureCitySeasonFcDO::getExtremeMinLoad));
                        if (min1.isPresent()) {
                            extremeData = min1.get().getExtremeMinLoad();
                        }
                        break;
                    case "最小腰荷":
                        String finalNormal4 = normal;
                        String finalNormal5 = extreme;
                        Optional<LoadFeatureCitySeasonFcDO> min2 = loadFeatureCitySeasonFcDOS.stream()
                            .filter(t -> t.getMonth().equals(finalNormal4))
                            .min(Comparator.comparing(LoadFeatureCitySeasonFcDO::getNoontimeLoad));
                        if (min2.isPresent()) {
                            data = min2.get().getNoontimeLoad();
                        }
                        Optional<LoadFeatureCitySeasonFcDO> min3 = loadFeatureCitySeasonFcDOS.stream()
                            .filter(t -> t.getMonth().equals(finalNormal5))
                            .min(Comparator.comparing(LoadFeatureCitySeasonFcDO::getExtremeNoontimeLoad));
                        if (min3.isPresent()) {
                            extremeData = min3.get().getExtremeNoontimeLoad();
                        }
                        break;
                    default:
                        data = null;
                        extremeData = null;
                }
            }
            shortReportResultDTO.setNormalData(data);
            shortReportResultDTO.setExtremeData(extremeData);
            List<ShortDateValuesDTO> hisList = new ArrayList<>(16);
            // 历史
            String lastStart = null;
            String lastEnd = null;
            for (int i = 1; i <= 5; i++) {
                String hisNormal = "";
                a = a - 1;
                if (CollectionUtils.isNotEmpty(mapHis)) {
                    for (Map<String, BigDecimal> mapHi : mapHis) {
                        ShortDateValuesDTO shortDateValuesDTO = new ShortDateValuesDTO();
                        for (String s : mapHi.keySet()) {
                            hisNormal = s;
                        }
                    }
                }
                ShortDateValuesDTO shortDateValuesDTO = new ShortDateValuesDTO();
                shortDateValuesDTO.setDate(a + "年实际");
                if (cityDO.getId().equals(Constants.PROVINCE_ID)) {
                    caliberId = Constants.CALIBER_ID_BG_QW;
                }
                lastStart = String.valueOf(Integer.valueOf(strStrat.split("-")[0]) - i) + "-" + strStrat.split("-")[1];
                lastEnd = String.valueOf(Integer.valueOf(strEnd.split("-")[0]) - i) + "-" + strEnd.split("-")[1];
                List<LoadFeatureCityMonthHisDO> loadFeatureCityMonthHisList = loadFeatureCityMonthHisService.getLoadFeatureCityMonthHisList(
                        cityDO.getId(), lastStart, lastEnd, caliberId).stream().collect(Collectors.toList());
                Map<String, List<LoadFeatureCityMonthHisDO>> collect = new HashMap<>(16);
                if (CollectionUtils.isNotEmpty(loadFeatureCityMonthHisList)) {
                    collect = loadFeatureCityMonthHisList.stream()
                        .collect(Collectors.groupingBy(t -> t.getMonth()));
                }
                List<LoadFeatureCityMonthHisDO> loadFeatureCityMonthHisDOS = collect.get(hisNormal);
                BigDecimal dataHis = null;
                if (CollectionUtils.isNotEmpty(loadFeatureCityMonthHisDOS)) {
                    switch (loadType){
                        case "最大负荷":
                            Optional<LoadFeatureCityMonthHisDO> max = loadFeatureCityMonthHisDOS.stream()
                                .filter(t -> t.getMaxLoad() != null)
                                .max(Comparator.comparing(LoadFeatureCityMonthHisDO::getMaxLoad));
                            if (max.isPresent()) {
                                dataHis = max.get().getMaxLoad();
                            }
                            break;
                        case "最小负荷":
                            Optional<LoadFeatureCityMonthHisDO> min = loadFeatureCityMonthHisDOS.stream()
                                .filter(t -> t.getMaxLoad() != null)
                                .min(Comparator.comparing(LoadFeatureCityMonthHisDO::getMinLoad));
                            if (min.isPresent()) {
                                dataHis = min.get().getMinLoad();
                            }
                            break;
                        case "最小腰荷":
                            Optional<LoadFeatureCityMonthHisDO> min1 = loadFeatureCityMonthHisDOS.stream()
                                .filter(t -> t.getNoontimeLoad() != null)
                                .min(Comparator.comparing(LoadFeatureCityMonthHisDO::getNoontimeLoad));
                            if (min1.isPresent()) {
                                dataHis = min1.get().getNoontimeLoad();
                            }
                            break;
                        default:
                            dataHis = null;
                    }
                }
                shortDateValuesDTO.setDecimal(dataHis);
                hisList.add(shortDateValuesDTO);
            }
            shortReportResultDTO.setShortDateValuesDTOS(hisList);
            result.add(shortReportResultDTO);
        }
        return result;
    }

    private List<Map<String, BigDecimal>> doWinterSummerHis(String loadType, List<String> targetYearList,
        String strStrat, String strEnd)
        throws Exception {
        String lastStart = null;
        String lastEnd = null;
        List<Map<String, BigDecimal>> result = new ArrayList<>(16);
        // 历史5年
        List<ShortDateValuesDTO> res = new ArrayList<>(16);
        for (int i = 1; i <= 5; i++) {
            Map<String, BigDecimal> normal = new HashMap<>(16);
            ShortDateValuesDTO shortDateValuesDTO = new ShortDateValuesDTO();
            lastStart = String.valueOf(Integer.valueOf(strStrat.split("-")[0]) - i) + "-" + strStrat.split("-")[1];
            lastEnd = String.valueOf(Integer.valueOf(strEnd.split("-")[0]) - i) + "-" + strEnd.split("-")[1];
            shortDateValuesDTO.setDate(lastStart.split("-")[0]);
            List<LoadFeatureCityMonthHisDO> loadFeatureCityMonthHisList = loadFeatureCityMonthHisService.getLoadFeatureCityMonthHisList(
                    null, lastStart, lastEnd, "2").stream().filter(t -> !t.getCityId().equals("1"))
                .collect(Collectors.toList());
            List<String> targetYearList1 = DateUtil.getTargetYearList(lastStart, lastEnd);
            for (String s : targetYearList1) {
                String month = s.split("-")[1];
                Map<String, List<LoadFeatureCityMonthHisDO>> collect = new HashMap<>(16);
                if (CollectionUtils.isNotEmpty(loadFeatureCityMonthHisList)) {
                    collect = loadFeatureCityMonthHisList.stream()
                        .collect(Collectors.groupingBy(t -> t.getMonth()));
                }
                List<LoadFeatureCityMonthHisDO> loadFeatureCityMonthHisDOS = collect.get(month);
                if (CollectionUtils.isNotEmpty(loadFeatureCityMonthHisDOS)) {
                    BigDecimal dataHis = null;
                    switch (loadType){
                        case "最大负荷":
                            dataHis = loadFeatureCityMonthHisDOS.stream()
                                .filter(t -> t.getMaxLoad() != null)
                                .map(LoadFeatureCityMonthHisDO::getMaxLoad)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                            break;
                        case "最小负荷":
                            dataHis = loadFeatureCityMonthHisDOS.stream()
                                .filter(t -> t.getMinLoad() != null)
                                .map(LoadFeatureCityMonthHisDO::getMinLoad)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                            break;
                        case "最小腰荷":
                            dataHis = loadFeatureCityMonthHisDOS.stream()
                                .filter(t -> t.getNoontimeLoad() != null)
                                .map(LoadFeatureCityMonthHisDO::getNoontimeLoad)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                            break;
                        default:
                            dataHis = null;
                    }
                    if (MapUtils.isNotEmpty(normal)) {
                        for (String s1 : normal.keySet()) {
                            BigDecimal bigDecimal = normal.get(s1);
                            if (bigDecimal.compareTo(dataHis) < 0) {
                                normal.clear();
                                normal.put(month, dataHis);
                            }
                        }
                    } else {
                        normal.put(month, dataHis);
                    }
                }
            }
            result.add(normal);
        }
        return result;
    }

    private List<Map<String, BigDecimal>> doWinterSummerFc(List<LoadFeatureCitySeasonFcDO> fcAll, String loadType, List<String> targetYearList)
        throws Exception {
        List<Map<String, BigDecimal>> result = new ArrayList<>(16);
        Map<String, BigDecimal> normal = new HashMap<>(16);
        Map<String, BigDecimal> extreme = new HashMap<>(16);
        List<LoadFeatureCitySeasonFcDO> collect1 = fcAll.stream().filter(t -> !t.getCityId().equals("1"))
            .collect(Collectors.toList());
        for (String s : targetYearList) {
            String month = s.split("-")[1];
            if (CollectionUtils.isNotEmpty(collect1)) {
                Map<String, List<LoadFeatureCitySeasonFcDO>> collect = collect1.stream()
                    .collect(Collectors.groupingBy(t -> t.getMonth()));
                List<LoadFeatureCitySeasonFcDO> loadFeatureCitySeasonFcDOS = collect.get(month);
                if (CollectionUtils.isNotEmpty(loadFeatureCitySeasonFcDOS)) {
                    BigDecimal data = null;
                    BigDecimal extremeData = null;
                    switch (loadType){
                        case "最大负荷":
                            data = loadFeatureCitySeasonFcDOS.stream()
                                .filter(t -> t.getMaxLoad() != null)
                                .map(LoadFeatureCitySeasonFcDO::getMaxLoad)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                            extremeData = loadFeatureCitySeasonFcDOS.stream()
                                .filter(t -> t.getExtremeMaxLoad() != null)
                                .map(LoadFeatureCitySeasonFcDO::getExtremeMaxLoad)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                            break;
                        case "最小负荷":
                            data = loadFeatureCitySeasonFcDOS.stream()
                                .filter(t -> t.getMinLoad() != null)
                                .map(LoadFeatureCitySeasonFcDO::getMinLoad)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                            extremeData = loadFeatureCitySeasonFcDOS.stream()
                                .filter(t -> t.getExtremeMinLoad() != null)
                                .map(LoadFeatureCitySeasonFcDO::getExtremeMinLoad)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                            break;
                        case "最小腰荷":
                            data = loadFeatureCitySeasonFcDOS.stream()
                                .filter(t -> t.getNoontimeLoad() != null)
                                .map((LoadFeatureCitySeasonFcDO::getNoontimeLoad))
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                            extremeData = loadFeatureCitySeasonFcDOS.stream()
                                .filter(t -> t.getExtremeNoontimeLoad() != null)
                                .map(LoadFeatureCitySeasonFcDO::getExtremeNoontimeLoad)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                            break;
                        default:
                            data = null;
                    }
                    if (MapUtils.isNotEmpty(normal)) {
                        for (String s1 : normal.keySet()) {
                            BigDecimal bigDecimal = normal.get(s1);
                            if (bigDecimal.compareTo(data) < 0) {
                                normal.clear();
                                normal.put(month, data);
                            }
                        }

                    } else {
                        normal.put(month, data);
                    }
                    if (MapUtils.isNotEmpty(extreme)) {
                        for (String s1 : extreme.keySet()) {
                            BigDecimal bigDecimal = extreme.get(s1);
                            if (bigDecimal.compareTo(extremeData) < 0) {
                                extreme.clear();
                                extreme.put(month, extremeData);
                            }
                        }
                    } else {
                        extreme.put(month, extremeData);
                    }
                }
            }
        }
        result.add(normal);
        result.add(extreme);
        return result;
    }

    private ShortReportResultDTO doFeatureTotalLoad(List<LoadFeatureCityTenDaysFcServiceDO> fcAll,
        List<LoadFeatureCityTenDaysHisServiceDO> hisAll, String type, List<String> strings) throws Exception {
        ShortReportResultDTO shortReportResultDTO = new ShortReportResultDTO();
        List<ShortDateValuesDTO> resHis = new ArrayList<>(16);
        if (CollectionUtils.isNotEmpty(fcAll)) {
            Map<String, List<LoadFeatureCityTenDaysFcServiceDO>> collect = fcAll.stream()
                .filter(t -> !t.getCityId().equals("1")).collect(Collectors.groupingBy(t -> t.getType()));
            List<BigDecimal> res = new ArrayList<>(16);
            List<BigDecimal> extremeRes = new ArrayList<>(16);
            for (TenDaysTypeEnum value : TenDaysTypeEnum.values()) {
                List<LoadFeatureCityTenDaysFcServiceDO> loadFeatureAllDOS = collect.get(
                    value.getName());
                if (CollectionUtils.isNotEmpty(loadFeatureAllDOS)) {
                    BigDecimal data = null;
                    BigDecimal extremeData = null;
                    switch (type){
                        case "最大负荷":
                            data = loadFeatureAllDOS.stream()
                                .filter(t -> t.getMaxLoad() != null)
                                .map(LoadFeatureCityTenDaysFcServiceDO::getMaxLoad)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                            extremeData = loadFeatureAllDOS.stream()
                                .filter(t -> t.getExtremeMaxLoad() != null)
                                .map(LoadFeatureCityTenDaysFcServiceDO::getExtremeMaxLoad)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                            break;
                        case "最小负荷":
                            data = loadFeatureAllDOS.stream()
                                .filter(t -> t.getMinLoad() != null)
                                .map(LoadFeatureCityTenDaysFcServiceDO::getMinLoad)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                            extremeData = loadFeatureAllDOS.stream()
                                .filter(t -> t.getExtremeMinLoad() != null)
                                .map(LoadFeatureCityTenDaysFcServiceDO::getExtremeMinLoad)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                            break;
                        case "最小腰荷":
                            data = loadFeatureAllDOS.stream()
                                .filter(t -> t.getDayUnbalance() != null)
                                .map((LoadFeatureCityTenDaysFcServiceDO::getDayUnbalance))
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                            extremeData = loadFeatureAllDOS.stream()
                                .filter(t -> t.getDayUnbalance() != null)
                                .map(LoadFeatureCityTenDaysFcServiceDO::getDayUnbalance)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                            break;
                        case "总电量":
                            data = loadFeatureAllDOS.stream()
                                .filter(t -> t.getEnergy() != null)
                                .map(LoadFeatureCityTenDaysFcServiceDO::getEnergy)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                            extremeData = loadFeatureAllDOS.stream()
                                .filter(t -> t.getExtremeEnergy() != null)
                                .map(LoadFeatureCityTenDaysFcServiceDO::getExtremeEnergy)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                            break;
                        default:
                            data = null;
                    }
                    res.add(data);
                    extremeRes.add(extremeData);
                }
            }
            BigDecimal max = BigDecimalUtils.getMin(res);
            BigDecimal max1 = BigDecimalUtils.getMin(extremeRes);
            if (type.equals("最大负荷")) {
                max = BigDecimalUtils.getMax(res);
                max1 = BigDecimalUtils.getMax(extremeRes);
            }
            shortReportResultDTO.setNormalData(max);
            shortReportResultDTO.setExtremeData(max1);
            if (new BigDecimal("0").equals(max)) {
                shortReportResultDTO.setNormalData(null);
            }
            if (new BigDecimal("0").equals(max1)) {
                shortReportResultDTO.setExtremeData(null);
            }
        }
        if (CollectionUtils.isNotEmpty(hisAll)) {
            Map<String, List<LoadFeatureCityTenDaysHisServiceDO>> collect = hisAll.stream()
                .filter(t -> !t.getCityId().equals("1")).collect(Collectors.groupingBy(t -> t.getType() + "-" + t.getYear()));
            for (String string : strings) {
                ShortDateValuesDTO shortDateValuesDTO = new ShortDateValuesDTO();
                List<BigDecimal> res = new ArrayList<>(16);
                for (TenDaysTypeEnum value : TenDaysTypeEnum.values()) {
                    String key = value.getName() + "-" + string;
                    List<LoadFeatureCityTenDaysHisServiceDO> loadFeatureAllDOS = collect.get(key);
                    if (CollectionUtils.isNotEmpty(loadFeatureAllDOS)) {
                        BigDecimal data = null;
                        switch (type){
                            case "最大负荷":
                                data = loadFeatureAllDOS.stream()
                                    .filter(t -> t.getMaxLoad() != null)
                                    .map(LoadFeatureCityTenDaysHisServiceDO::getMaxLoad)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                                break;
                            case "最小负荷":
                                data = loadFeatureAllDOS.stream()
                                    .filter(t -> t.getMinLoad() != null)
                                    .map(LoadFeatureCityTenDaysHisServiceDO::getMinLoad)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                                break;
                            case "最小腰荷":
                                data = loadFeatureAllDOS.stream()
                                    .filter(t -> t.getDayUnbalance() != null)
                                    .map((LoadFeatureCityTenDaysHisServiceDO::getDayUnbalance))
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                                break;
                            case "总电量":
                                data = loadFeatureAllDOS.stream()
                                    .filter(t -> t.getEnergy() != null)
                                    .map(LoadFeatureCityTenDaysHisServiceDO::getEnergy)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                                break;
                            default:
                                data = null;
                        }
                        res.add(data);
                    }
                }
                BigDecimal max = BigDecimalUtils.getMin(res);
                if (type.equals("最大负荷")) {
                    max = BigDecimalUtils.getMax(res);
                }
                shortDateValuesDTO.setDate(string + "年实际");
                shortDateValuesDTO.setDecimal(max);
                if (new BigDecimal("0").equals(max)) {
                    shortDateValuesDTO.setDecimal(null);
                }
                resHis.add(shortDateValuesDTO);
            }
        }
        shortReportResultDTO.setShortDateValuesDTOS(resHis);
        return shortReportResultDTO;
    }

    private String doFeatureLoad(List<LoadFeatureCityTenDaysFcServiceDO> fcAll,
        List<LoadFeatureCityTenDaysHisServiceDO> hisAll, String type) throws Exception {
        String monthType = "";
        if (CollectionUtils.isNotEmpty(fcAll)) {
            int a = 0;
            Map<Object, List<LoadFeatureCityTenDaysFcServiceDO>> collect = fcAll.stream()
                .filter(t -> !t.getCityId().equals("1")).collect(Collectors.groupingBy(t -> t.getType()));
            List<LoadFeatureCityTenDaysFcServiceDO> first = collect.get("上旬");
            List<LoadFeatureCityTenDaysFcServiceDO> second = collect.get("中旬");
            List<LoadFeatureCityTenDaysFcServiceDO> third = collect.get("下旬");
            List<BigDecimal> res = new ArrayList<>(16);
            if (CollectionUtils.isNotEmpty(first)) {
                BigDecimal data = null;
                switch (type){
                    case "最大负荷":
                        data = first.stream()
                            .filter(t -> t.getMaxLoad() != null)
                            .map(LoadFeatureCityTenDaysFcServiceDO::getMaxLoad)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                        break;
                    case "最小负荷":
                        data = first.stream()
                            .filter(t -> t.getMinLoad() != null)
                            .map(LoadFeatureCityTenDaysFcServiceDO::getMinLoad)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                        break;
                    case "最小腰荷":
                        data = first.stream()
                            .filter(t -> t.getDayUnbalance() != null)
                            .map((LoadFeatureCityTenDaysFcServiceDO::getDayUnbalance))
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                        break;
                    case "总电量":
                        data = first.stream()
                            .filter(t -> t.getEnergy() != null).map(LoadFeatureCityTenDaysFcServiceDO::getEnergy)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                        break;
                    default:
                        data = null;
                }
                res.add(data);
            }
            //second
            if (CollectionUtils.isNotEmpty(second)) {
                BigDecimal data = null;
                switch (type){
                    case "最大负荷":
                        data = second.stream()
                            .filter(t -> t.getMaxLoad() != null)
                            .map(LoadFeatureCityTenDaysFcServiceDO::getMaxLoad)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                        break;
                    case "最小负荷":
                        data = second.stream()
                            .filter(t -> t.getMinLoad() != null)
                            .map(LoadFeatureCityTenDaysFcServiceDO::getMinLoad)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                        break;
                    case "最小腰荷":
                        data = second.stream()
                            .filter(t -> t.getDayUnbalance() != null)
                            .map((LoadFeatureCityTenDaysFcServiceDO::getDayUnbalance))
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                        break;
                    case "总电量":
                        data = second.stream()
                            .filter(t -> t.getEnergy() != null).map(LoadFeatureCityTenDaysFcServiceDO::getEnergy)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                        break;
                    default:
                        data = null;
                }
                res.add(data);
            }
            if (CollectionUtils.isNotEmpty(third)) {
                BigDecimal data = null;
                switch (type){
                    case "最大负荷":
                        data = third.stream()
                            .filter(t -> t.getMaxLoad() != null)
                            .map(LoadFeatureCityTenDaysFcServiceDO::getMaxLoad)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                        break;
                    case "最小负荷":
                        data = third.stream()
                            .filter(t -> t.getMinLoad() != null)
                            .map(LoadFeatureCityTenDaysFcServiceDO::getMinLoad)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                        break;
                    case "最小腰荷":
                        data = third.stream()
                            .filter(t -> t.getDayUnbalance() != null)
                            .map((LoadFeatureCityTenDaysFcServiceDO::getDayUnbalance))
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                        break;
                    case "总电量":
                        data = third.stream()
                            .filter(t -> t.getEnergy() != null).map(LoadFeatureCityTenDaysFcServiceDO::getEnergy)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                        break;
                    default:
                        data = null;
                }
                res.add(data);
            }
            if (CollectionUtils.isNotEmpty(res)) {
                BigDecimal max = BigDecimalUtils.getMin(res);
                if (type.equals("最大负荷")) {
                    max = BigDecimalUtils.getMax(res);
                }
                for (int i = 0; i < 3; i++) {
                    if (max.compareTo(res.get(i)) == 0) {
                        a = i;
                    }
                }
            }
            if (a == 0) {
                monthType = "上旬";
            } else if (a == 1) {
                monthType = "中旬";
            } else {
                monthType = "下旬";
            }
        }
        return monthType;
        /*if (CollectionUtils.isNotEmpty(hisAll)) {
            List<LoadFeatureCityTenDaysHisServiceDO> collect = hisAll.stream().filter(t -> !t.getCityId().equals("1"))
                .collect(Collectors.toList());
        }*/
    }

    private List<ShortDateValuesDTO> doHisLoad(List<LoadFeatureCityTenDaysHisServiceDO> hisAll, List<String> hisYear,
        String cityId, String type, String loadType)
        throws Exception {
        List<ShortDateValuesDTO> result = new ArrayList<>(16);
        Map<String, List<LoadFeatureCityTenDaysHisServiceDO>> collect = new HashMap<>(16);
        if (CollectionUtils.isNotEmpty(hisAll)) {
            collect = hisAll.stream().collect(
                Collectors.groupingBy(t -> t.getYear() + "-" + t.getCityId()));
        }
        for (String s : hisYear) {
            ShortDateValuesDTO shortDateValuesDTO = new ShortDateValuesDTO();
            shortDateValuesDTO.setDate(s + "年实际");
            String key = s + "-" + cityId;
            if (MapUtils.isNotEmpty(collect)) {
                List<LoadFeatureCityTenDaysHisServiceDO> loadFeatureCityTenDaysHisServiceDOS = collect.get(key);
                if (CollectionUtils.isNotEmpty(loadFeatureCityTenDaysHisServiceDOS)) {
                    if (type.equals("月度")) {
                        BigDecimal data = null;
                        switch (loadType){
                            case "最大负荷":
                                data = loadFeatureCityTenDaysHisServiceDOS.stream()
                                    .filter(t -> t.getMaxLoad() != null)
                                    .max(Comparator.comparing(LoadFeatureCityTenDaysHisServiceDO::getMaxLoad)).get()
                                    .getMaxLoad();
                                break;
                            case "最小负荷":
                                data = loadFeatureCityTenDaysHisServiceDOS.stream()
                                    .filter(t -> t.getMinLoad() != null)
                                    .min(Comparator.comparing(LoadFeatureCityTenDaysHisServiceDO::getMinLoad)).get()
                                    .getMinLoad();
                                break;
                            case "最小腰荷":
                                data = loadFeatureCityTenDaysHisServiceDOS.stream()
                                    .filter(t -> t.getDayUnbalance() != null)
                                    .min(Comparator.comparing(LoadFeatureCityTenDaysHisServiceDO::getDayUnbalance)).get()
                                    .getDayUnbalance();
                                break;
                            case "总电量":
                                data = loadFeatureCityTenDaysHisServiceDOS.stream()
                                    .filter(t -> t.getEnergy() != null).map(LoadFeatureCityTenDaysHisServiceDO::getEnergy)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                                break;
                            default: data = null;
                        }
                        shortDateValuesDTO.setDecimal(data);
                    }
                    else {
                        for (LoadFeatureCityTenDaysHisServiceDO loadFeatureCityTenDaysHisServiceDO : loadFeatureCityTenDaysHisServiceDOS) {
                            if (loadFeatureCityTenDaysHisServiceDO.getType().equals(type)) {
                                BigDecimal data = null;
                                switch (loadType){
                                    case "最大负荷":
                                        data = loadFeatureCityTenDaysHisServiceDO.getMaxLoad();
                                        break;
                                    case "最小负荷":
                                        data = loadFeatureCityTenDaysHisServiceDO.getMinLoad();
                                        break;
                                    case "最小腰荷":
                                        data = loadFeatureCityTenDaysHisServiceDO.getDayUnbalance();
                                        break;
                                    case "总电量":
                                        data = loadFeatureCityTenDaysHisServiceDO.getEnergy();
                                        break;
                                    default: data = null;
                                }
                                shortDateValuesDTO.setDecimal(data);
                            }
                        }
                    }
                }
            }
            result.add(shortDateValuesDTO);
        }
        return result;
    }

    private List<String> doLoad1(List<LoadCityFcDO> loadCityFcDOS, String loadType,
        List<Date> listBetweenDay, String typeName, String caliberName)
        throws Exception {
        int timeStr = 0;
        List<String> strings = new ArrayList<>();
        List<LoadCityFcDO> collect = new ArrayList<>(16);
        if (typeName.equals("地区发电")) {
            if (caliberName.equals("风电")) {
                collect = loadCityFcDOS.stream().filter(t -> t.getCaliberId().equals("9"))
                    .collect(Collectors.toList());
            } else if (caliberName.equals("光伏")) {
                collect = loadCityFcDOS.stream().filter(t -> t.getCaliberId().equals("8"))
                    .collect(Collectors.toList());
            } else if (caliberName.equals("水电")) {
                collect = loadCityFcDOS.stream().filter(t -> t.getCaliberId().equals("6"))
                    .collect(Collectors.toList());
            } else {
                collect = loadCityFcDOS.stream().filter(t -> t.getCaliberId().equals("7"))
                    .collect(Collectors.toList());
            }
        } else {
            if (caliberName.equals("地区调度")) {
                collect = loadCityFcDOS.stream().filter(t -> t.getCaliberId().equals("2"))
                    .collect(Collectors.toList());
            } else {
                collect = loadCityFcDOS.stream().filter(t -> t.getCaliberId().equals("1"))
                    .collect(Collectors.toList());
            }
        }
        if (CollectionUtils.isNotEmpty(collect)) {
            Map<String, List<LoadCityFcDO>> collect1 = collect.stream()
                .collect(Collectors.groupingBy(t -> DateUtil.getDateToStrFORMAT(t.getDate(), "yyyy-MM-dd")));
            for (Date date : listBetweenDay) {
                List<List<BigDecimal>> loadList = new ArrayList<>();
                String dateStr = DateUtil.getDateToStrFORMAT(date, "yyyy-MM-dd");
                List<LoadCityFcDO> loadCityFcDOS1 = collect1.get(dateStr);
                List<BigDecimal> zeroList = ColumnUtil.getZeroOrNullList(96, BigDecimal.ZERO);
                if (CollectionUtils.isNotEmpty(loadCityFcDOS1)) {
                    for (LoadCityFcDO loadCityFcDO : loadCityFcDOS1) {
                        if (loadCityFcDO.getT0015() == null) {
                            continue;
                        }
                        List<BigDecimal> bigDecimals = loadCityFcDO.getloadList();
                        zeroList = BigDecimalFunctions.listAdd(zeroList, bigDecimals);
                    }
                }
                // 取时间点
                String timeStr1 = getTimeStr(zeroList, loadType);
                strings.add(dateStr + "~" + timeStr1);
            }
        }
        return strings;
    }

    private String getTimeStr(List<BigDecimal> zeroList, String loadType) throws Exception {
        String num = "";
        if (loadType.equals("最小腰荷")) {
            List<BigDecimal> res = new ArrayList<>();
            for (int i = 0; i < 96; i++) {
                if (i >= 48 && i <= 64) {
                    res.add(zeroList.get(i));
                }
            }
            BigDecimal min = BigDecimalUtils.getMin(res);
            for (int i = 0; i < 96; i++) {
                if (min != null && zeroList.get(i) != null) {
                    if (min.compareTo(zeroList.get(i)) == 0) {
                        num = i + "~" + min;
                    }
                }
            }
        } else {
            BigDecimal max = BigDecimalUtils.getMax(zeroList);
            if (loadType.equals("最小负荷")) {
                max = BigDecimalUtils.getMin(zeroList);
            }
            for (int i = 0; i < 96; i++) {
                if (max != null && zeroList.get(i) != null) {
                    if (max.compareTo(zeroList.get(i)) == 0) {
                        num = i + "~" + max;
                    }
                }
            }
        }
        return num;
    }

    private List<ShortDateValuesDTO> doLoad(List<LoadCityFcDO> loadCityFcDOS, String loadType,
        List<Date> listBetweenDay, List<String> strings) throws Exception {
        Map<String, List<LoadCityFcDO>> collect = loadCityFcDOS.stream()
            .collect(Collectors.groupingBy(t -> DateUtil.getDateToStrFORMAT(t.getDate(), "yyyy-MM-dd")));
        List<String> noontimePeak = settingSystemDAO.getNoontimePeak();
        List<ShortDateValuesDTO> res = new ArrayList<>();
        for (Date date : listBetweenDay) {
            String dateStr = DateUtil.getDateToStrFORMAT(date, "yyyy-MM-dd");
            ShortDateValuesDTO shortDateValuesDTO = new ShortDateValuesDTO();
            shortDateValuesDTO.setDate(dateStr);
            List<LoadCityFcDO> loadCityFcDOS1 = collect.get(dateStr);
            if (CollectionUtils.isNotEmpty(loadCityFcDOS1)) {
                LoadCityFcDO loadCityFcDO = loadCityFcDOS1.get(0);
                List<BigDecimal> noonTimes = new ArrayList<BigDecimal>(16);
                List<BigDecimal> loadList = BasePeriodUtils
                    .toList(loadCityFcDO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
                Map<String, BigDecimal> loadMap = BasePeriodUtils
                    .toMap(loadCityFcDO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
                Map<String, BigDecimal> maxMixAvg = BasePeriodUtils.getMaxMinAvg(loadList, 4);
                for (String column : loadMap.keySet()) {
                    BigDecimal load = loadMap.get(column);
                    if (null != load) {
                        column = column.substring(1);
                        if (noontimePeak != null && noontimePeak.contains(column)) {
                            noonTimes.add(load);
                        }
                    }
                }
                if ("最大负荷".equals(loadType)) {
                    shortDateValuesDTO.setDecimal(maxMixAvg.get("max"));
                } else if ("最小负荷".equals(loadType)) {
                    shortDateValuesDTO.setDecimal(maxMixAvg.get("min"));
                } else if ("最小腰荷".equals(loadType)) {
                    shortDateValuesDTO.setDecimal(BigDecimalFunctions.listMin(noonTimes));
                } else {
                    shortDateValuesDTO.setDecimal(
                        BigDecimalUtils.divide(BigDecimalUtils.addAllValue(loadList), new BigDecimal(4), 4));
                }
            }
            res.add(shortDateValuesDTO);
        }
        if (CollectionUtils.isNotEmpty(strings) && !loadType.equals("日电量")) {
            for (ShortDateValuesDTO re : res) {
                for (String string : strings) {
                    String dateStr = string.split("~")[0];
                    int num = Integer.valueOf(string.split("~")[1]);
                    if (re.getDate().equals(dateStr)) {
                        List<LoadCityFcDO> loadCityFcDOS1 = collect.get(dateStr);
                        if (CollectionUtils.isNotEmpty(loadCityFcDOS1)) {
                            LoadCityFcDO loadCityFcDO = loadCityFcDOS1.get(0);
                            List<BigDecimal> bigDecimals = loadCityFcDO.getloadList();
                            BigDecimal bigDecimal = bigDecimals.get(num);
                            re.setDecimal(bigDecimal);
                        }
                    }
                }
            }
        }
        return res;
    }
}
