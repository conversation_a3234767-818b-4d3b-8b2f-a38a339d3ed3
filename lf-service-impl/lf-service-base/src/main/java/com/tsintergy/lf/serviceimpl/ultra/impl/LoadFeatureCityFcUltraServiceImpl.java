package com.tsintergy.lf.serviceimpl.ultra.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsieframework.util.compatible.StringUtils;

import com.tsintergy.lf.serviceapi.base.ultra.api.LoadFeatureCityFcUltraService;
import com.tsintergy.lf.serviceapi.base.ultra.pojo.LoadFeatureCityDayUltraFcDO;
import com.tsintergy.lf.serviceapi.base.ultra.pojo.LoadFeatureCityMonthUltraFcDO;
import com.tsintergy.lf.serviceapi.base.ultra.pojo.LoadFeatureCityYearUltraFcDO;
import com.tsintergy.lf.serviceimpl.ultra.dao.LoadFeatureCityDayUltraFcDAO;
import com.tsintergy.lf.serviceimpl.ultra.dao.LoadFeatureCityMonthUltraFcDAO;
import com.tsintergy.lf.serviceimpl.ultra.dao.LoadFeatureCityYearUltraFcDAO;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class LoadFeatureCityFcUltraServiceImpl implements LoadFeatureCityFcUltraService {

    @Autowired
    private LoadFeatureCityDayUltraFcDAO loadFeatureCityDayUltraFcDAO;

    @Autowired
    private LoadFeatureCityMonthUltraFcDAO loadFeatureCityMonthUltraFcDAO;

    @Autowired
    private LoadFeatureCityYearUltraFcDAO loadFeatureCityYearUltraFcDAO;

    @Override
    public void doSaveOrUpdateLoadFeatureCityDayFcDOS(List<LoadFeatureCityDayUltraFcDO> loadFeatureCityDayUltraFcDOS) {
        try {
            loadFeatureCityDayUltraFcDAO.saveOrUpdateBatchByTemplate(loadFeatureCityDayUltraFcDOS);
        } catch (Exception e) {
            log.error("存储超短期预测负荷特性异常...", e);
        }
    }

    @Override
    public LoadFeatureCityDayUltraFcDO findLoadFeatureCityDayUltraFcDO(String cityId, Date date, String caliberId,
        String algorithmId, Integer type) {
        return loadFeatureCityDayUltraFcDAO.findOne(
            JpaWrappers.<LoadFeatureCityDayUltraFcDO>lambdaQuery()
                .eq(StringUtils.isNotBlank(cityId), LoadFeatureCityDayUltraFcDO::getCityId, cityId)
                .eq(StringUtils.isNotBlank(caliberId), LoadFeatureCityDayUltraFcDO::getCaliberId, caliberId)
                .eq(StringUtils.isNotBlank(algorithmId), LoadFeatureCityDayUltraFcDO::getAlgorithmId, algorithmId)
                .eq(!Objects.isNull(date), LoadFeatureCityDayUltraFcDO::getDateTime, date)
                .eq(!Objects.isNull(type), LoadFeatureCityDayUltraFcDO::getType, type)
        );
    }

    @Override
    public List<LoadFeatureCityDayUltraFcDO> findLoadFeatureCityDayUltraFcDOS(String cityId, Date startDate,
        Date endDate, String caliberId, String algorithmId, Integer tDelta, Integer multipointType) {
        return loadFeatureCityDayUltraFcDAO.findAll(
            JpaWrappers.<LoadFeatureCityDayUltraFcDO>lambdaQuery()
                .eq(!Objects.isNull(multipointType), LoadFeatureCityDayUltraFcDO::getMultipointType, multipointType)
                .eq(StringUtils.isNotBlank(cityId), LoadFeatureCityDayUltraFcDO::getCityId, cityId)
                .eq(StringUtils.isNotBlank(caliberId), LoadFeatureCityDayUltraFcDO::getCaliberId, caliberId)
                .eq(StringUtils.isNotBlank(algorithmId), LoadFeatureCityDayUltraFcDO::getAlgorithmId, algorithmId)
                .eq(!Objects.isNull(tDelta), LoadFeatureCityDayUltraFcDO::getType, tDelta)
                .ge(!Objects.isNull(startDate), LoadFeatureCityDayUltraFcDO::getDateTime, startDate)
                .le(!Objects.isNull(endDate), LoadFeatureCityDayUltraFcDO::getDateTime, endDate)
        );
    }



    @Override
    public void doSaveOrUpdateLoadFeatureCityMonthFcDOS(List<LoadFeatureCityMonthUltraFcDO> loadFeatureCityDayUltraFcDOS) {
        try {
            loadFeatureCityMonthUltraFcDAO.saveOrUpdateBatchByTemplate(loadFeatureCityDayUltraFcDOS);
        } catch (Exception e) {
            log.error("存储超短期预测负荷特性异常...", e);
        }
    }

    @Override
    public LoadFeatureCityMonthUltraFcDO findLoadFeatureCityMonthUltraFcDO(String cityId, Date date, String caliberId,
        String algorithmId, Integer type) {
        return loadFeatureCityMonthUltraFcDAO.findOne(
            JpaWrappers.<LoadFeatureCityMonthUltraFcDO>lambdaQuery()
                .eq(StringUtils.isNotBlank(cityId), LoadFeatureCityMonthUltraFcDO::getCityId, cityId)
                .eq(StringUtils.isNotBlank(caliberId), LoadFeatureCityMonthUltraFcDO::getCaliberId, caliberId)
                .eq(StringUtils.isNotBlank(algorithmId), LoadFeatureCityMonthUltraFcDO::getAlgorithmId, algorithmId)
                .eq(!Objects.isNull(date), LoadFeatureCityMonthUltraFcDO::getDateTime, date)
                .eq(!Objects.isNull(type), LoadFeatureCityMonthUltraFcDO::getType, type)
        );
    }

    @Override
    public List<LoadFeatureCityMonthUltraFcDO> findLoadFeatureCityMonthUltraFcDOS(String cityId, Date startDate,
        Date endDate, String caliberId, String algorithmId, Integer tDelta, Integer multipointType) {
        return loadFeatureCityMonthUltraFcDAO.findAll(
            JpaWrappers.<LoadFeatureCityMonthUltraFcDO>lambdaQuery()
                .eq(!Objects.isNull(multipointType), LoadFeatureCityMonthUltraFcDO::getMultipointType, multipointType)
                .eq(StringUtils.isNotBlank(cityId), LoadFeatureCityMonthUltraFcDO::getCityId, cityId)
                .eq(StringUtils.isNotBlank(caliberId), LoadFeatureCityMonthUltraFcDO::getCaliberId, caliberId)
                .eq(StringUtils.isNotBlank(algorithmId), LoadFeatureCityMonthUltraFcDO::getAlgorithmId, algorithmId)
                .eq(!Objects.isNull(tDelta), LoadFeatureCityMonthUltraFcDO::getType, tDelta)
                .ge(!Objects.isNull(startDate), LoadFeatureCityMonthUltraFcDO::getDateTime, startDate)
                .le(!Objects.isNull(endDate), LoadFeatureCityMonthUltraFcDO::getDateTime, endDate)
        );
    }

    @Override
    public void doSaveOrUpdateLoadFeatureCityYearFcDOS(List<LoadFeatureCityYearUltraFcDO> loadFeatureCityYearUltraFcDOS) {
        try {
            loadFeatureCityYearUltraFcDAO.saveOrUpdateBatchByTemplate(loadFeatureCityYearUltraFcDOS);
        } catch (Exception e) {
            log.error("存储超短期预测负荷特性异常...", e);
        }
    }

    @Override
    public LoadFeatureCityYearUltraFcDO findLoadFeatureCityYearUltraFcDO(String cityId, String year, String caliberId,
        String algorithmId, Integer type) {
        return loadFeatureCityYearUltraFcDAO.findOne(
            JpaWrappers.<LoadFeatureCityYearUltraFcDO>lambdaQuery()
                .eq(StringUtils.isNotBlank(cityId), LoadFeatureCityYearUltraFcDO::getCityId, cityId)
                .eq(StringUtils.isNotBlank(caliberId), LoadFeatureCityYearUltraFcDO::getCaliberId, caliberId)
                .eq(StringUtils.isNotBlank(algorithmId), LoadFeatureCityYearUltraFcDO::getAlgorithmId, algorithmId)
                .eq(!Objects.isNull(year), LoadFeatureCityYearUltraFcDO::getYear, year)
                .eq(!Objects.isNull(type), LoadFeatureCityYearUltraFcDO::getType, type)
        );
    }

    @Override
    public List<LoadFeatureCityYearUltraFcDO> findLoadFeatureCityYearUltraFcDOS(String cityId, String startYear,
        String endYear, String caliberId, String algorithmId, Integer tDelta, Integer multipointType) {
        return loadFeatureCityYearUltraFcDAO.findAll(
            JpaWrappers.<LoadFeatureCityYearUltraFcDO>lambdaQuery()
                .eq(!Objects.isNull(multipointType), LoadFeatureCityYearUltraFcDO::getMultipointType, multipointType)
                .eq(StringUtils.isNotBlank(cityId), LoadFeatureCityYearUltraFcDO::getCityId, cityId)
                .eq(StringUtils.isNotBlank(caliberId), LoadFeatureCityYearUltraFcDO::getCaliberId, caliberId)
                .eq(StringUtils.isNotBlank(algorithmId), LoadFeatureCityYearUltraFcDO::getAlgorithmId, algorithmId)
                .eq(!Objects.isNull(tDelta), LoadFeatureCityYearUltraFcDO::getType, tDelta)
                .ge(!Objects.isNull(startYear), LoadFeatureCityYearUltraFcDO::getYear, startYear)
                .le(!Objects.isNull(endYear), LoadFeatureCityYearUltraFcDO::getYear, endYear)
        );
    }

}
