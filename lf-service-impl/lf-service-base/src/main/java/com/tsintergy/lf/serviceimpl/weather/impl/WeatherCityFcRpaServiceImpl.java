package com.tsintergy.lf.serviceimpl.weather.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcRpaService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcRpaDO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherCityFcRpaDAO;
import java.util.List;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2024-12-04
 * @since 1.0.0
 */
@Service("weatherCityFcRpaService")
public class WeatherCityFcRpaServiceImpl implements WeatherCityFcRpaService {

    @Autowired
    WeatherCityFcRpaDAO weatherCityFcRpaDAO;

    @Override
    public void doSaveOrUpdate(List<WeatherCityFcRpaDO> weatherCityFcRpaDOList) {
        for (WeatherCityFcRpaDO weatherCityHisRpaDO : weatherCityFcRpaDOList) {
            List<WeatherCityFcRpaDO> all = weatherCityFcRpaDAO.findAll(
                JpaWrappers.<WeatherCityFcRpaDO>lambdaQuery()
                    .eq(WeatherCityFcRpaDO::getCityId, weatherCityHisRpaDO.getCityId())
                    .eq(WeatherCityFcRpaDO::getStationId, weatherCityHisRpaDO.getStationId())
                    .eq(WeatherCityFcRpaDO::getType, weatherCityHisRpaDO.getType())
                    .eq(WeatherCityFcRpaDO::getUploadDate, weatherCityHisRpaDO.getUploadDate())
                    .eq(WeatherCityFcRpaDO::getDate, weatherCityHisRpaDO.getDate()));
            if (CollectionUtils.isEmpty(all)) {
                weatherCityFcRpaDAO.save(weatherCityHisRpaDO);
            } else {
                weatherCityHisRpaDO.setId(all.get(0).getId());
                weatherCityFcRpaDAO.saveOrUpdateByTemplate(weatherCityHisRpaDO);
            }
        }
    }

    @Override
    public void doSave(List<WeatherCityFcRpaDO> weatherCityFcRpaDOList) {
        weatherCityFcRpaDAO.saveAll(weatherCityFcRpaDOList);
    }
}
