/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.trade.impl;

import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.math.BigDecimalFunctions;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.core.util.LoadCalUtil;
import com.tsintergy.lf.serviceapi.base.bgd.api.IndustryBaseInitService;
import com.tsintergy.lf.serviceapi.base.bgd.api.LoadFeatureIndustryDayHisService;
import com.tsintergy.lf.serviceapi.base.bgd.api.LoadFeatureIndustryMonthHisService;
import com.tsintergy.lf.serviceapi.base.bgd.pojo.IndustryBaseInitDO;
import com.tsintergy.lf.serviceapi.base.bgd.pojo.LoadFeatureIndustryDayHisServiceDO;
import com.tsintergy.lf.serviceapi.base.bgd.pojo.LoadFeatureIndustryMonthHisServiceDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityDayHisService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityMonthHisService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityDayHisDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityMonthHisDO;
import com.tsintergy.lf.serviceapi.base.trade.api.*;
import com.tsintergy.lf.serviceapi.base.trade.dto.*;
import com.tsintergy.lf.serviceapi.base.trade.pojo.BaseTradeInfoDO;
import com.tsintergy.lf.serviceapi.base.trade.pojo.IndustryCityLoadDayHisClctDO;
import com.tsintergy.lf.serviceapi.base.trade.pojo.TradeLoadFeatureDayHisDO;
import com.tsintergy.lf.serviceapi.base.trade.pojo.TradeLoadHisStatDO;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/9/2 16:10
 * @Version: 1.0.0
 */
@Slf4j
@Service("tradeService")
public class TradeServiceImpl implements TradeService {
    public static List<String> HAVE_SUB_TRADE_LIST = Arrays.asList("A000", "GG00", "E000", "F000", "G000", "M000");
    public static Map<String, String> INDUSTRY_ID_MAP = new LinkedHashMap<>();
    public static Map<String, String> TRADE_ID_MAP = new LinkedHashMap<>();
    public static Map<String, Integer> ORDER_MAP = new HashMap<>();

    static {
        TRADE_ID_MAP.put("A000", "一、农、林、牧、渔业");
        TRADE_ID_MAP.put("GG00", "二、工业");
        TRADE_ID_MAP.put("E000", "三、建筑业");
        TRADE_ID_MAP.put("F000", "四、交通运输、仓储和邮政业");
        TRADE_ID_MAP.put("G000", "五、信息传输、软件和信息技术服务业");
        TRADE_ID_MAP.put("H000", "六、批发和零售业");
        TRADE_ID_MAP.put("I000", "七、住宿和餐饮业");
        TRADE_ID_MAP.put("J000", "八、金融业");
        TRADE_ID_MAP.put("7200", "九、房地产业");
        TRADE_ID_MAP.put("L000", "十、租赁和商务服务业");
        TRADE_ID_MAP.put("M000", "十一、公共服务及管理组织");
        TRADE_ID_MAP.put("9900", "十二、城乡居民生活用电合计");

        INDUSTRY_ID_MAP.put("99A0", "第一产业");
        INDUSTRY_ID_MAP.put("99B0", "第二产业");
        INDUSTRY_ID_MAP.put("99C0", "第三产业");
        INDUSTRY_ID_MAP.put("9900", "城乡居民生活用电合计");
    }

    static {
        // "全社会"
        ORDER_MAP.put("AAAA", 0);
        // "一、农、林、牧、渔业"
        ORDER_MAP.put("A000", 1);
        // "二、工业"
        ORDER_MAP.put("GG00", 2);
        // "三、建筑业"
        ORDER_MAP.put("E000", 3);
        // "四、交通运输、仓储和邮政业"
        ORDER_MAP.put("F000", 4);
        // "五、信息传输、软件和信息技术服务业"
        ORDER_MAP.put("G000", 5);
        // "六、批发和零售业"
        ORDER_MAP.put("H000", 6);
        // "七、住宿和餐饮业"
        ORDER_MAP.put("I000", 7);
        // "八、金融业"
        ORDER_MAP.put("J000", 8);
        // "九、房地产业"
        ORDER_MAP.put("7200", 9);
        // "十、租赁和商务服务业"
        ORDER_MAP.put("L000", 10);
        // "十一、公共服务及管理组织"
        ORDER_MAP.put("M000", 11);
        // "十二、城乡居民生活用电合计"
        ORDER_MAP.put("9900", 12);
        // "第一产业"
        ORDER_MAP.put("99A0", 1);
        // "第二产业"
        ORDER_MAP.put("99B0", 2);
        // "第三产业"
        ORDER_MAP.put("99C0", 3);
    }

    @Autowired
    BaseTradeInfoService baseTradeInfoService;

    @Autowired
    TradeLoadHisStatService tradeLoadHisStatService;

    @Autowired
    TradeLoadFeatureDayHisService tradeLoadFeatureDayHisService;

    @Autowired
    IndustryCityLoadDayHisClctService industryCityLoadDayHisClctService;

    @Autowired
    LoadFeatureIndustryDayHisService loadFeatureIndustryDayHisService;

    @Autowired
    LoadFeatureCityDayHisService loadFeatureCityDayHisService;

    @Autowired
    LoadFeatureCityMonthHisService loadFeatureCityMonthHisService;

    @Autowired
    LoadCityHisService loadCityHisService;

    @Autowired
    IndustryBaseInitService industryBaseInitService;

    @Autowired
    LoadFeatureIndustryMonthHisService loadFeatureIndustryMonthHisService;

    private static boolean shouldInclude(IndustryCityLoadDayHisClctDO src, Map<String, List<String>> tradeInfoMap, IndustryLoadMaxFeatureDTO industryDTO) {
        List<String> subTradeCodes = tradeInfoMap.get(industryDTO.getTradeCode());
        if (subTradeCodes == null || subTradeCodes.isEmpty()) {
            return true;
        } else {
            return subTradeCodes.contains(src.getTradeCode());
        }
    }

    private void mergeTradeInfoList(List<TradeInfoDTO> tradeInfoDTOList, String pCode, String name) {
        List<BaseTradeInfoDO> childTradeInfo = baseTradeInfoService.findBaseTradeInfo(name, null, pCode, null);
        for (BaseTradeInfoDO tradeInfoDO : childTradeInfo) {
            TradeInfoDTO tradeInfoDTO = new TradeInfoDTO();
            tradeInfoDTO.setKey(tradeInfoDO.getCode());
            tradeInfoDTO.setTitle(tradeInfoDO.getName());
            if (tradeInfoDO.getLevel() != 3) {
                List<TradeInfoDTO> childTradeInfoDOS = new ArrayList<>();
                mergeTradeInfoList(childTradeInfoDOS, tradeInfoDO.getCode(), name);
                childTradeInfoDOS = childTradeInfoDOS.stream().sorted(Comparator.comparing(t -> t.getKey())).collect(
                        Collectors.toList());
                tradeInfoDTO.setChildren(childTradeInfoDOS);
            }
            tradeInfoDTOList.add(tradeInfoDTO);
        }
    }

    @Override
    public List<TradeCurveDTO> getTradeCurve(String cityId, Date startDate, Date endDate, List<String> codeIds) {
        List<TradeCurveDTO> values = new ArrayList<>();
        Map<String, TradeLoadHisStatDO> collect = tradeLoadHisStatService
            .findTradeLoadHisBasicDO(null, startDate, endDate)
            .stream().collect(Collectors.toMap(t -> t.getTradeCode() + DateUtil.formateDate(t.getDate()), t -> t));
        Map<String, String> nameMap = baseTradeInfoService.findBaseTradeInfo(null, null, null, null).stream()
            .collect(Collectors.toMap(t -> t.getCode(), t -> t.getName()));
        List<Date> dates = DateUtil.getListBetweenDay(startDate, endDate);
        for (String codeId : codeIds) {
            TradeCurveDTO tradeCurveDTO = new TradeCurveDTO();
            String name = nameMap.get(codeId);
            tradeCurveDTO.setCodeId(name);
            tradeCurveDTO.setDate(dates);
            List<BigDecimal> data = new ArrayList<>();
                for (Date date : dates) {
                    TradeLoadHisStatDO tradeLoadHisStatDO = collect.get(codeId+DateUtil.formateDate(date));
                    List<BigDecimal> zeroOrNullList = ColumnUtil
                        .getZeroOrNullList(Constants.LOAD_CURVE_POINT_NUM_24, null);
                    if (tradeLoadHisStatDO!=null){
                        List<BigDecimal> bigDecimals = tradeLoadHisStatDO.getloadList();
                        for (int i = 0; i < bigDecimals.size(); i++) {
                            if (bigDecimals.get(i)!=null){
                                zeroOrNullList.set(i,bigDecimals.get(i));
                            }
                        }
                    }
                    data.addAll(zeroOrNullList);
            }
            tradeCurveDTO.setData(data);
            values.add(tradeCurveDTO);
        }


        return values;
    }

    @Override
    public List<TradeElectricityProportionDTO> getTradeElectricityProportion(String cityId, Date startDate,
        Date endDate) {
        List<TradeElectricityProportionDTO> result = new ArrayList<>();

        List<TradeLoadFeatureDayHisDO> tradeLoadFeatureDayHisDO = tradeLoadFeatureDayHisService
            .findTradeLoadFeatureDayHisDO(startDate, endDate, null);
        Map<String, List<TradeLoadFeatureDayHisDO>> tradeEnergyListMap = tradeLoadFeatureDayHisDO.stream()
            .collect(Collectors.groupingBy(t -> t.getTradeCode()));
        List<BaseTradeInfoDO> baseTradeInfo = baseTradeInfoService.findBaseTradeInfo(null, null, null, null);

        Map<Integer, List<BaseTradeInfoDO>> baseTradeInfoMap = baseTradeInfo
            .stream().collect(
                Collectors.groupingBy(t -> t.getLevel()));

        TradeElectricityProportionDTO tradeElectricity = new TradeElectricityProportionDTO();
        tradeElectricity.setLevel(0);
        List<ElectricityProportionDTO> tradeElectricityList = new ArrayList<>();
        //细分行业
        TradeElectricityProportionDTO subdivisionTradeElectricity = new TradeElectricityProportionDTO();
        subdivisionTradeElectricity.setLevel(1);
        List<ElectricityProportionDTO> subdivisionTradeElectricityList = new ArrayList<>();
        //行业
        mergeEnergyList(baseTradeInfoMap,tradeElectricityList,tradeEnergyListMap,2);
        //细分
        mergeEnergyList(baseTradeInfoMap,subdivisionTradeElectricityList,tradeEnergyListMap,3);

        tradeElectricity.setList(tradeElectricityList);
        subdivisionTradeElectricity.setList(subdivisionTradeElectricityList);
        result.add(tradeElectricity);
        result.add(subdivisionTradeElectricity);
        return result;
    }

    private void mergeEnergyList(Map<Integer, List<BaseTradeInfoDO>> baseTradeInfoMap,List<ElectricityProportionDTO> tradeElectricityList
        ,Map<String, List<TradeLoadFeatureDayHisDO>> tradeEnergyListMap,Integer level) {
        List<BaseTradeInfoDO> tradeInfoDOS = baseTradeInfoMap.get(level);
        //为细分行业时按照code排序
        if (level == 3){
            tradeInfoDOS = tradeInfoDOS.stream().sorted(Comparator.comparing(BaseTradeInfoDO::getCode)).collect(Collectors.toList());
        }
        List<String> codeList = tradeInfoDOS.stream().map(t -> t.getCode()).collect(Collectors.toList());
        List<BigDecimal> totalEnergyList = new ArrayList<>();
        for (String codeId : codeList) {
            List<TradeLoadFeatureDayHisDO> list = tradeEnergyListMap.get(codeId);
            if (!CollectionUtils.isEmpty(list)){
                totalEnergyList.addAll(list.stream().map(t->t.getEnergy()).collect(Collectors.toList()));
            }
        }
        if (CollectionUtils.isEmpty(totalEnergyList)){
            return;
        }
        BigDecimal totalEnergy = BigDecimalFunctions.listSum(totalEnergyList);
        for (BaseTradeInfoDO tradeInfoDO : tradeInfoDOS) {
            if (tradeInfoDO.getLevel() == level){
                ElectricityProportionDTO electricityProportionDTO = new ElectricityProportionDTO();
                electricityProportionDTO.setName(tradeInfoDO.getName());
                List<TradeLoadFeatureDayHisDO> tradeList = tradeEnergyListMap.get(tradeInfoDO.getCode());
                BigDecimal energy = BigDecimal.ZERO;
                if (!CollectionUtils.isEmpty(tradeList)) {
                    energy = BigDecimalFunctions
                            .listSum(tradeList.stream().map(t -> t.getEnergy()).collect(Collectors.toList()));
                }
                electricityProportionDTO.setValue(energy.divide(totalEnergy, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)));
                tradeElectricityList.add(electricityProportionDTO);
            }
        }
    }

    @Override
    public TradeInfoDTO getTradeInfo(String cityId, String name) {
        List<BaseTradeInfoDO> baseTradeInfo = baseTradeInfoService.findBaseTradeInfo(name, null, null, 1);
        BaseTradeInfoDO baseTradeInfoDO = baseTradeInfo.get(0);
        List<TradeInfoDTO> tradeInfoDTOList = new ArrayList<>();
        mergeTradeInfoList(tradeInfoDTOList, baseTradeInfoDO.getCode(), name);
        TradeInfoDTO tradeInfoDTO = new TradeInfoDTO();
        tradeInfoDTO.setKey(baseTradeInfoDO.getCode());
        tradeInfoDTO.setTitle(baseTradeInfoDO.getName());
        tradeInfoDTO.setChildren(tradeInfoDTOList);
        return tradeInfoDTO;
    }

    @Override
    public List<TradeFeatureDTO> getTradeFeature(String cityId, Date startDate, Date endDate, List<String> codeIds) {

        List<TradeFeatureDTO> tradeFeatureDTOS = new ArrayList<>();
        Map<String, List<TradeLoadFeatureDayHisDO>> featureMap = tradeLoadFeatureDayHisService
                .findTradeLoadFeatureDayHisDO(startDate, endDate, null).stream()
                .collect(Collectors.groupingBy(t -> t.getTradeCode()));
        Map<String, BaseTradeInfoDO> tradeInfoMap = baseTradeInfoService.findBaseTradeInfo(null, null, null, null).stream()
                .collect(Collectors.toMap(t -> t.getCode(), t -> t));
        for (String codeId : codeIds) {
            List<TradeLoadFeatureDayHisDO> list = featureMap.get(codeId);
            TradeFeatureDTO tradeFeatureDTO = new TradeFeatureDTO();
            tradeFeatureDTO.setName(tradeInfoMap.get(codeId).getName());
            if (!CollectionUtils.isEmpty(list)){
                TradeLoadFeatureDayHisDO maxLoad = list.stream().sorted(Comparator.comparing(t->t.getMaxLoad())).collect(Collectors.toList()).get(list.size()-1);
                TradeLoadFeatureDayHisDO minLoad = list.stream().sorted(Comparator.comparing(t->t.getMinLoad())).collect(Collectors.toList()).get(0);
                List<BigDecimal> aveLoad = list.stream().map(t -> t.getAveLoad()).collect(Collectors.toList());
                List<BigDecimal> energy = list.stream().map(t -> t.getEnergy()).collect(Collectors.toList());
                tradeFeatureDTO.setMaxLoad(maxLoad.getMaxLoad());
                tradeFeatureDTO.setMaxLoadTime(DateUtil.formateDate(maxLoad.getDate())+ " " +maxLoad.getMaxTime());
                tradeFeatureDTO.setMinLoad(minLoad.getMinLoad());
                tradeFeatureDTO.setMinLoadTime(DateUtil.formateDate(minLoad.getDate()) + " " + minLoad.getMinTime());
                tradeFeatureDTO.setAveLoad(BigDecimalFunctions.listAvg(aveLoad));
                tradeFeatureDTO.setAveElectricity(BigDecimalFunctions.listAvg(energy));
                tradeFeatureDTO.setTotalPower(BigDecimalFunctions.listSum(energy));
            }
            tradeFeatureDTOS.add(tradeFeatureDTO);
        }
        return tradeFeatureDTOS;
    }

    @Override
    public List<IndustryLoadMaxFeatureDTO> getDayIndustryFeature(String cityId, Date startDate, Date endDate, String dataType, String periodType) {

        List<String> tradeIds = this.getTradeIds(dataType);
        //查询行业负荷特性
        List<LoadFeatureIndustryDayHisServiceDO> loadFeatureList = this.getIndustryLoadFeatureDayByType(DateUtils.addDays(startDate, -1), endDate, cityId, periodType);
        List<IndustryCityLoadDayHisClctDO> tradeClctDOs = industryCityLoadDayHisClctService.findIndustryCityLoadDayHisClctDOS(cityId, tradeIds, startDate, endDate);
        Map<Date, List<IndustryCityLoadDayHisClctDO>> subIndustryMap = tradeClctDOs.stream().collect(Collectors.groupingBy(IndustryCityLoadDayHisClctDO::getDate));

        //查询去年行业负荷特性
        Date lastYearStart = DateUtils.addYears(startDate, -1);
        Date lastYearEnd = DateUtils.addYears(endDate, -1);
        List<LoadFeatureIndustryDayHisServiceDO> lastYearLoadFeatureList = this.getIndustryLoadFeatureDayByType(lastYearStart, lastYearEnd, cityId, periodType);
        List<IndustryCityLoadDayHisClctDO> lastYearSubIndustryDOs = industryCityLoadDayHisClctService.findIndustryCityLoadDayHisClctDOS(cityId, tradeIds, lastYearStart, lastYearEnd);
        Map<String, IndustryCityLoadDayHisClctDO> lastYearSubIndustryMap = lastYearSubIndustryDOs.stream().collect(Collectors.toMap(dto -> dto.getTradeCode() + DateUtil.formateDate(dto.getDate()), Function.identity(), (o, n) -> o));

        List<IndustryLoadMaxFeatureDTO> results = new ArrayList<>();
        List<Date> betweenDay = com.tsintergy.lf.serviceimpl.common.util.DateUtils.getListBetweenDay(startDate, endDate);
        for (Date date : betweenDay) {
            for (IndustryCityLoadDayHisClctDO industryCityLoadDayHisClctDO : subIndustryMap.getOrDefault(date, Collections.emptyList())) {
                LoadFeatureIndustryDayHisServiceDO dayHisStatsDO = this.findLoadFeatureByDate(loadFeatureList, date);
                if (dayHisStatsDO != null) {
                    IndustryLoadMaxFeatureDTO dto = this.processOneDay(dayHisStatsDO, industryCityLoadDayHisClctDO, loadFeatureList, lastYearLoadFeatureList, lastYearSubIndustryMap);
                    results.add(dto);
                }
            }
        }
        results.sort(Comparator.comparing(IndustryLoadMaxFeatureDTO::getDate).thenComparing(IndustryLoadMaxFeatureDTO::getTradeCode));
        return results;
    }

    private List<String> getTradeIds(String dataType) {
        return "0".equals(dataType) ? new ArrayList<>(INDUSTRY_ID_MAP.keySet()) : Arrays.asList("99AA", "9900");
    }

    @SneakyThrows
    private List<LoadFeatureIndustryDayHisServiceDO> getIndustryLoadFeatureDayByType(Date startDate, Date endDate, String cityId, String periodType) {
        List<LoadFeatureIndustryDayHisServiceDO> list;
        // 1: 调度负荷最大时刻   0: 行业负荷最大时刻
        if ("1".equals(periodType)) {
            List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisVOS = loadFeatureCityDayHisService.findLoadFeatureCityDayHisVOS(cityId, startDate, endDate, Constants.CALIBER_ID_BG_QW);
            Map<String, LoadFeatureCityDayHisDO> cityDayHisDOMap = loadFeatureCityDayHisVOS.stream().collect(Collectors.toMap(src -> DateUtil.formateDate(src.getDate()), Function.identity()));

            //查询行业负荷
            List<IndustryCityLoadDayHisClctDO> clctDOs = industryCityLoadDayHisClctService.findIndustryCityLoadDayHisClctDOS(cityId, Collections.singletonList(Constants.TOTAL_SOCIAL_TRADE_CODE), startDate, endDate);
            Map<String, IndustryCityLoadDayHisClctDO> industryCityDayHisDOMap = clctDOs.stream().collect(Collectors.toMap(src -> DateUtil.formateDate(src.getDate()), Function.identity()));

            //查询行业负荷特性
            List<LoadFeatureIndustryDayHisServiceDO> loadFeatureIndustryDayHisServiceDOS = loadFeatureIndustryDayHisService.getLoadFeatureIndustryDayHisServiceList(startDate, endDate, cityId, Constants.TOTAL_SOCIAL_TRADE_CODE);

            List<LoadFeatureIndustryDayHisServiceDO> dayHisStatsDOS = new ArrayList<>();
            for (LoadFeatureIndustryDayHisServiceDO dayHisStatsDO : loadFeatureIndustryDayHisServiceDOS) {
                LoadFeatureIndustryDayHisServiceDO featureDayHisStatsDO = new LoadFeatureIndustryDayHisServiceDO();
                featureDayHisStatsDO.setDate(dayHisStatsDO.getDate());
                featureDayHisStatsDO.setMaxTime(cityDayHisDOMap.get(DateUtil.formateDate(dayHisStatsDO.getDate())).getMaxTime());
                IndustryCityLoadDayHisClctDO industryCityLoadDayHisClctDO = industryCityDayHisDOMap.get(DateUtil.formateDate(dayHisStatsDO.getDate()));
                if (industryCityLoadDayHisClctDO != null) {
                    featureDayHisStatsDO.setMaxLoad(getFieldValueByTime(featureDayHisStatsDO.getMaxTime(), industryCityLoadDayHisClctDO));
                }
                dayHisStatsDOS.add(featureDayHisStatsDO);
            }
            list = dayHisStatsDOS;
        } else {
            list = loadFeatureIndustryDayHisService.getLoadFeatureIndustryDayHisServiceList(startDate, endDate, cityId, Constants.TOTAL_SOCIAL_TRADE_CODE);
        }
        return list;
    }

    @SneakyThrows
    private List<LoadFeatureIndustryMonthHisServiceDO> getIndustryLoadFeatureMonthByType(String cityId, String startYM, String endYM, String periodType) {
        List<LoadFeatureIndustryMonthHisServiceDO> list;
        // 1: 调度负荷最大时刻   0: 行业负荷最大时刻
        if ("1".equals(periodType)) {
            List<LoadFeatureCityMonthHisDO> loadFeatureCityMonthHisDOS = loadFeatureCityMonthHisService.getLoadFeatureCityMonthHisDOS(cityId, startYM, endYM, Constants.CALIBER_ID_BG_QW);
            Map<String, LoadFeatureCityMonthHisDO> cityMonthHisDOMap = loadFeatureCityMonthHisDOS.stream().collect(Collectors.toMap(src -> src.getYear() + "-" + src.getMonth(), Function.identity()));

            //查询行业负荷
            List<Date> dates = loadFeatureCityMonthHisDOS.stream().map(LoadFeatureCityMonthHisDO::getMaxDate).collect(Collectors.toList());
            List<IndustryCityLoadDayHisClctDO> clctDOs = industryCityLoadDayHisClctService.findIndustryCityLoadDayHisClctDOS(cityId,
                    Collections.singletonList(Constants.TOTAL_SOCIAL_TRADE_CODE), dates);
            Map<String, IndustryCityLoadDayHisClctDO> industryCityDayHisDOMap = clctDOs.stream().collect(Collectors.toMap(src -> DateUtils.date2String(src.getDate(), DateFormatType.YEAR_MONTH_STR), Function.identity()));

            //查询行业负荷特性
            List<LoadFeatureIndustryMonthHisServiceDO> industryMonthHisServiceList = loadFeatureIndustryMonthHisService.getLoadFeatureIndustryMonthHisServiceList(cityId, Constants.TOTAL_SOCIAL_TRADE_CODE, startYM, endYM);

            List<LoadFeatureIndustryMonthHisServiceDO> dayHisStatsDOS = new ArrayList<>();
            for (LoadFeatureIndustryMonthHisServiceDO monthHisServiceDO : industryMonthHisServiceList) {
                LoadFeatureIndustryMonthHisServiceDO featureDayHisStatsDO = new LoadFeatureIndustryMonthHisServiceDO();
                featureDayHisStatsDO.setMaxDate(monthHisServiceDO.getMaxDate());
                featureDayHisStatsDO.setMaxTime(cityMonthHisDOMap.get(monthHisServiceDO.getYear() + "-" + monthHisServiceDO.getMonth()).getMaxTime());
                IndustryCityLoadDayHisClctDO industryCityLoadDayHisClctDO = industryCityDayHisDOMap.get(DateUtils.date2String(monthHisServiceDO.getMaxDate(), DateFormatType.YEAR_MONTH_STR));
                if (industryCityLoadDayHisClctDO != null) {
                    featureDayHisStatsDO.setMaxLoad(getFieldValueByTime(featureDayHisStatsDO.getMaxTime(), industryCityLoadDayHisClctDO));
                }
                dayHisStatsDOS.add(featureDayHisStatsDO);
            }
            list = dayHisStatsDOS;
        } else {
            list = loadFeatureIndustryMonthHisService.getLoadFeatureIndustryMonthHisServiceList(cityId, Constants.TOTAL_SOCIAL_TRADE_CODE, startYM, endYM);
        }
        return list;
    }

    private LoadFeatureIndustryDayHisServiceDO findLoadFeatureByDate(List<LoadFeatureIndustryDayHisServiceDO> loadFeatureList, Date date) {
        return loadFeatureList.stream().filter(item -> item.getDate().getTime() == date.getTime()).findFirst().orElse(null);
    }

    @Override
    public List<IndustryLoadMaxFeatureDTO> getMaxMonthIndustryFeature(String tradeCode, String cityId, Date startDate, Date endDate, String dataType, String periodType) {
        if (StringUtils.isEmpty(tradeCode)) {
            tradeCode = Constants.TOTAL_SOCIAL_TRADE_CODE;
        }

        // 查询日期范围内最大月负荷
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM");
        String startDateStr = dateFormat.format(startDate);
        String endDateStr = dateFormat.format(endDate);
        List<LoadFeatureIndustryMonthHisServiceDO> loadFeatureList = this.getIndustryLoadFeatureMonthByType(cityId, startDateStr, endDateStr, periodType);
        Optional<LoadFeatureIndustryMonthHisServiceDO> maxLoadFeature = loadFeatureList.stream().max(Comparator.comparing(LoadFeatureIndustryMonthHisServiceDO::getMaxLoad));
        if (!maxLoadFeature.isPresent()) {
            return Collections.emptyList();
        }
        LoadFeatureIndustryMonthHisServiceDO maxFeatureMonth = maxLoadFeature.get();

        // 查询子行业负荷
        List<String> tradeIds = this.getSubIndustryTradeIds(tradeCode, dataType);
        List<IndustryCityLoadDayHisClctDO> subIndustryCityLoadDayHisClctDOS = industryCityLoadDayHisClctService.findIndustryCityLoadDayHisClctDOS(cityId, tradeIds, maxFeatureMonth.getMaxDate(), maxFeatureMonth.getMaxDate());
        if (CollectionUtils.isEmpty(subIndustryCityLoadDayHisClctDOS)) {
            return Collections.emptyList();
        }

        // 上月环比行业负荷
        Map<String, IndustryCityLoadDayHisClctDO> lastMonthIndustryLoadMap = new HashMap<>();
        Date lastMonth = DateUtils.addMonths(maxFeatureMonth.getMaxDate(), -1);
        List<LoadFeatureIndustryMonthHisServiceDO> lastMonthLoadFeatures = this.getIndustryLoadFeatureMonthByType(cityId, dateFormat.format(lastMonth), dateFormat.format(lastMonth), periodType);
        LoadFeatureIndustryMonthHisServiceDO lastMonthLoadFeature = new LoadFeatureIndustryMonthHisServiceDO();
        lastMonthLoadFeature.setMaxDate(new Timestamp(lastMonth.getTime()));
        if (CollectionUtils.isNotEmpty(lastMonthLoadFeatures)) {
            lastMonthLoadFeature = lastMonthLoadFeatures.get(0);
            List<IndustryCityLoadDayHisClctDO> lastMonthIndustryLoadDOS = industryCityLoadDayHisClctService.findIndustryCityLoadDayHisClctDOS(cityId, tradeIds, lastMonthLoadFeature.getMaxDate(), lastMonthLoadFeature.getMaxDate());
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(lastMonthIndustryLoadDOS)) {
                lastMonthIndustryLoadMap = lastMonthIndustryLoadDOS.stream().collect(Collectors.toMap(IndustryCityLoadDayHisClctDO::getTradeCode, Function.identity(), (o, n) -> o));
            }
        }

        // 去年同比行业负荷
        Map<String, IndustryCityLoadDayHisClctDO> lastYearLoadFeatureMap = new HashMap<>();
        Date lastYear = DateUtils.addYears(maxFeatureMonth.getMaxDate(), -1);
        List<LoadFeatureIndustryMonthHisServiceDO> lastYearLoadFeatures = this.getIndustryLoadFeatureMonthByType(cityId, dateFormat.format(lastYear), dateFormat.format(lastYear), periodType);
        LoadFeatureIndustryMonthHisServiceDO lastYearLoadFeature = new LoadFeatureIndustryMonthHisServiceDO();
        lastYearLoadFeature.setMaxDate(new Timestamp(lastYear.getTime()));
        if (CollectionUtils.isNotEmpty(lastYearLoadFeatures)) {
            lastYearLoadFeature = lastYearLoadFeatures.get(0);
            List<IndustryCityLoadDayHisClctDO> lastYearIndustryLoadDOS = industryCityLoadDayHisClctService.findIndustryCityLoadDayHisClctDOS(cityId, tradeIds, lastYearLoadFeature.getMaxDate(), lastYearLoadFeature.getMaxDate());
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(lastYearIndustryLoadDOS)) {
                lastYearLoadFeatureMap = lastYearIndustryLoadDOS.stream().collect(Collectors.toMap(IndustryCityLoadDayHisClctDO::getTradeCode, Function.identity(), (o, n) -> o));
            }
        }

        // 转化对象
        List<IndustryLoadMaxFeatureDTO> results = new ArrayList<>();
        for (IndustryCityLoadDayHisClctDO industryCityLoadDayHisClctDO : subIndustryCityLoadDayHisClctDOS) {
            IndustryLoadMaxFeatureDTO industryLoadMaxFeatureDTO = new IndustryLoadMaxFeatureDTO();
            industryLoadMaxFeatureDTO.setTradeCode(industryCityLoadDayHisClctDO.getTradeCode());
            industryLoadMaxFeatureDTO.setTradeName(industryCityLoadDayHisClctDO.getTradeCodeDsc());
            industryLoadMaxFeatureDTO.setOrderNo(ORDER_MAP.get(industryCityLoadDayHisClctDO.getTradeCode()));
            industryLoadMaxFeatureDTO.setDate(DateUtil.formateDate(maxFeatureMonth.getMaxDate()) + StringUtils.SPACE + maxFeatureMonth.getMaxTime());
            BigDecimal load = this.getFieldValueByTime(maxFeatureMonth.getMaxTime(), industryCityLoadDayHisClctDO);
            industryLoadMaxFeatureDTO.setLoad(BigDecimalFunctions.multiply(load, BigDecimal.TEN));
            this.setLoadRate(industryLoadMaxFeatureDTO, maxFeatureMonth.getMaxLoad(), cityId, maxFeatureMonth.getMaxDate(), maxFeatureMonth.getMaxTime());
            if (lastMonthIndustryLoadMap.get(industryCityLoadDayHisClctDO.getTradeCode()) != null) {
                this.calcHuanbiInfo(industryLoadMaxFeatureDTO, lastMonthIndustryLoadMap.get(industryCityLoadDayHisClctDO.getTradeCode()), lastMonthLoadFeature.getMaxTime(), maxFeatureMonth.getMaxLoad(), lastMonthLoadFeature.getMaxLoad());
            }
            if (lastYearLoadFeatureMap.get(industryCityLoadDayHisClctDO.getTradeCode()) != null) {
                industryLoadMaxFeatureDTO.setTongbiDate(lastYearLoadFeature.getMaxDate());
                industryLoadMaxFeatureDTO.setTongbiMaxTime(lastYearLoadFeature.getMaxTime());
                BigDecimal tongbiLoad = this.getFieldValueByTime(lastYearLoadFeature.getMaxTime(), lastYearLoadFeatureMap.get(industryCityLoadDayHisClctDO.getTradeCode()));
                tongbiLoad = BigDecimalFunctions.multiply(tongbiLoad, BigDecimal.TEN);
                industryLoadMaxFeatureDTO.setTongbiLoad(tongbiLoad);
                industryLoadMaxFeatureDTO.setTongbiGrowthRate(LoadCalUtil.calcRate(industryLoadMaxFeatureDTO.getLoad().subtract(tongbiLoad), tongbiLoad));
            }
            results.add(industryLoadMaxFeatureDTO);
        }
        // 查询系统调度
        if (Constants.TOTAL_SOCIAL_TRADE_CODE.equals(tradeCode)) {
            this.addSystemLoad(results, cityId, maxFeatureMonth.getMaxDate(), maxFeatureMonth.getMaxTime(), lastMonthLoadFeature.getMaxDate(),
                    lastMonthLoadFeature.getMaxTime(), lastYearLoadFeature.getMaxDate(), lastYearLoadFeature.getMaxTime());
        }
        this.sortResult(results, tradeCode, dataType);
        this.setSubTradeFeatureList(results, tradeCode, cityId, dataType);
        return results;
    }

    @Override
    public List<IndustryLoadMaxFeatureDTO> getMonthIndustryFeature(String cityId, Date startDate, Date endDate, String dataType, String periodType) {
        List<String> tradeIds = this.getTradeIds(dataType);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMM");
        String startDateStr = dateFormat.format(startDate);
        String endDateStr = dateFormat.format(endDate);
        List<LoadFeatureIndustryMonthHisServiceDO> monthHisStatsDOs = loadFeatureIndustryMonthHisService.
                getLoadFeatureIndustryMonthHisServiceList(cityId, Constants.TOTAL_SOCIAL_TRADE_CODE, startDateStr, endDateStr);

        List<Date> maxLoadDates = monthHisStatsDOs.stream().map(LoadFeatureIndustryMonthHisServiceDO::getMaxDate).collect(Collectors.toList());
        List<IndustryCityLoadDayHisClctDO> clctDOs = industryCityLoadDayHisClctService.findIndustryCityLoadDayHisClctDOS(cityId, tradeIds, maxLoadDates);
        Map<Date, List<IndustryCityLoadDayHisClctDO>> subIndustryMap = clctDOs.stream().collect(Collectors.groupingBy(IndustryCityLoadDayHisClctDO::getDate));

        List<IndustryLoadMaxFeatureDTO> results = new ArrayList<>();
        for (LoadFeatureIndustryMonthHisServiceDO monthHisStatsDO : monthHisStatsDOs) {
            processOneMonth(results, cityId, monthHisStatsDO, subIndustryMap, tradeIds, periodType);
        }
        results.sort(Comparator.comparing(IndustryLoadMaxFeatureDTO::getDate)
                .thenComparing(IndustryLoadMaxFeatureDTO::getTradeCode));
        return results;
    }

    @Override
    public List<IndustryLoadMaxFeatureDTO> getMaxDayIndustryFeature(String code, String cityId, Date startDate, Date endDate, String dataType, String periodType) {
        if (StringUtils.isEmpty(code)) {
            code = Constants.TOTAL_SOCIAL_TRADE_CODE;
        }

        //查询日期范围内最大日负荷
        List<LoadFeatureIndustryDayHisServiceDO> loadFeatureList = this.getIndustryLoadFeatureDayByType(startDate, endDate, cityId, periodType);
        Optional<LoadFeatureIndustryDayHisServiceDO> maxLoadFeature = loadFeatureList.stream().max(Comparator.comparing(LoadFeatureIndustryDayHisServiceDO::getMaxLoad));
        if (!maxLoadFeature.isPresent()) {
            return Collections.emptyList();
        }
        LoadFeatureIndustryDayHisServiceDO maxFeatureDay = maxLoadFeature.get();

        //查询子行业负荷
        List<String> tradeIds = this.getSubIndustryTradeIds(code, dataType);
        List<IndustryCityLoadDayHisClctDO> subIndustryCityLoadDayHisClctDOS = industryCityLoadDayHisClctService.findIndustryCityLoadDayHisClctDOS(cityId, tradeIds, maxFeatureDay.getDate(), maxFeatureDay.getDate());
        if (CollectionUtils.isEmpty(subIndustryCityLoadDayHisClctDOS)) {
            return Collections.emptyList();
        }

        //昨日环比行业负荷
        Map<String, IndustryCityLoadDayHisClctDO> yesterdayIndustryLoadMap = new HashMap<>();
        Date yesterday = DateUtils.addDays(maxFeatureDay.getDate(), -1);
        List<LoadFeatureIndustryDayHisServiceDO> yesterdayLoadFeatures = this.getIndustryLoadFeatureDayByType(yesterday, yesterday, cityId, periodType);
        LoadFeatureIndustryDayHisServiceDO yesterdayLoadFeature = new LoadFeatureIndustryDayHisServiceDO();
        yesterdayLoadFeature.setDate(yesterday);
        if (CollectionUtils.isNotEmpty(yesterdayLoadFeatures)) {
            yesterdayLoadFeature = yesterdayLoadFeatures.get(0);
            List<IndustryCityLoadDayHisClctDO> yesterdayIndustryLoadDOS = industryCityLoadDayHisClctService.findIndustryCityLoadDayHisClctDOS(cityId, tradeIds, yesterdayLoadFeature.getDate(), yesterdayLoadFeature.getDate());
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(yesterdayIndustryLoadDOS)) {
                yesterdayIndustryLoadMap = yesterdayIndustryLoadDOS.stream().collect(Collectors.toMap(IndustryCityLoadDayHisClctDO::getTradeCode, Function.identity(), (o, n) -> o));
            }
        }

        //去年同比行业负荷
        Map<String, IndustryCityLoadDayHisClctDO> lastYearLoadFeatureMap = new HashMap<>();
        Date lastYear = DateUtils.addYears(maxFeatureDay.getDate(), -1);
        List<LoadFeatureIndustryDayHisServiceDO> lastYearLoadFeatures = this.getIndustryLoadFeatureDayByType(lastYear, lastYear, cityId, Constants.TOTAL_SOCIAL_TRADE_CODE);
        LoadFeatureIndustryDayHisServiceDO lastYearLoadFeature = new LoadFeatureIndustryDayHisServiceDO();
        lastYearLoadFeature.setDate(lastYear);
        if (CollectionUtils.isNotEmpty(lastYearLoadFeatures)) {
            lastYearLoadFeature = lastYearLoadFeatures.get(0);
            List<IndustryCityLoadDayHisClctDO> lastYearIndustryLoadDOS = industryCityLoadDayHisClctService.findIndustryCityLoadDayHisClctDOS(cityId, tradeIds, lastYearLoadFeature.getDate(), lastYearLoadFeature.getDate());
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(lastYearIndustryLoadDOS)) {
                lastYearLoadFeatureMap = lastYearIndustryLoadDOS.stream().collect(Collectors.toMap(IndustryCityLoadDayHisClctDO::getTradeCode, Function.identity(), (o, n) -> o));
            }
        }

        //转化对象
        List<IndustryLoadMaxFeatureDTO> results = new ArrayList<>();
        for (IndustryCityLoadDayHisClctDO industryCityLoadDayHisClctDO : subIndustryCityLoadDayHisClctDOS) {
            IndustryLoadMaxFeatureDTO industryLoadMaxFeatureDTO = new IndustryLoadMaxFeatureDTO();
            industryLoadMaxFeatureDTO.setTradeCode(industryCityLoadDayHisClctDO.getTradeCode());
            industryLoadMaxFeatureDTO.setTradeName(industryCityLoadDayHisClctDO.getTradeCodeDsc());
            industryLoadMaxFeatureDTO.setOrderNo(ORDER_MAP.get(industryCityLoadDayHisClctDO.getTradeCode()));
            industryLoadMaxFeatureDTO.setDate(DateUtil.formateDate(maxFeatureDay.getDate()) + StringUtils.SPACE + maxFeatureDay.getMaxTime());
            BigDecimal load = this.getFieldValueByTime(maxFeatureDay.getMaxTime(), industryCityLoadDayHisClctDO);
            industryLoadMaxFeatureDTO.setLoad(BigDecimalFunctions.multiply(load, BigDecimal.TEN));
            this.setLoadRate(industryLoadMaxFeatureDTO, maxFeatureDay.getMaxLoad(), cityId, maxFeatureDay.getDate(), maxFeatureDay.getMaxTime());
            if (yesterdayIndustryLoadMap.get(industryCityLoadDayHisClctDO.getTradeCode()) != null) {
                this.calcHuanbiInfo(industryLoadMaxFeatureDTO, yesterdayIndustryLoadMap.get(industryCityLoadDayHisClctDO.getTradeCode()), yesterdayLoadFeature.getMaxTime(), maxFeatureDay.getMaxLoad(), yesterdayLoadFeature.getMaxLoad());
            }
            if (lastYearLoadFeatureMap.get(industryCityLoadDayHisClctDO.getTradeCode()) != null) {
                industryLoadMaxFeatureDTO.setTongbiDate(lastYearLoadFeature.getDate());
                industryLoadMaxFeatureDTO.setTongbiMaxTime(lastYearLoadFeature.getMaxTime());
                BigDecimal tongbiLoad = this.getFieldValueByTime(lastYearLoadFeature.getMaxTime(), lastYearLoadFeatureMap.get(industryCityLoadDayHisClctDO.getTradeCode()));
                tongbiLoad = BigDecimalFunctions.multiply(tongbiLoad, BigDecimal.TEN);
                industryLoadMaxFeatureDTO.setTongbiLoad(tongbiLoad);
                industryLoadMaxFeatureDTO.setTongbiGrowthRate(LoadCalUtil.calcRate(industryLoadMaxFeatureDTO.getLoad().subtract(tongbiLoad), tongbiLoad));
            }
            results.add(industryLoadMaxFeatureDTO);
        }
        //查询系统调度
        if (Constants.TOTAL_SOCIAL_TRADE_CODE.equals(code)) {
            this.addSystemLoad(results, cityId, maxFeatureDay.getDate(), maxFeatureDay.getMaxTime(), yesterdayLoadFeature.getDate(), yesterdayLoadFeature.getMaxTime(), lastYearLoadFeature.getDate(), lastYearLoadFeature.getMaxTime());
        }
        this.sortResult(results, code, dataType);
//        if (!Constants.TOTAL_SOCIAL_TRADE_CODE.equals(code)) {
//            this.reCalcRateBaseParentIndustry(results);
//        }
        this.setSubTradeFeatureList(results, code, cityId, dataType);
        return results;
    }

    @SneakyThrows
    private void addSystemLoad(List<IndustryLoadMaxFeatureDTO> results, String cityId, Date date, String maxTime, Date huanbiDate, String huanbiMaxTime, Date tongbiDate, String tongbiMaxTime) {
        List<Date> dates = Arrays.asList(date, tongbiDate, huanbiDate);
        List<LoadCityHisDO> cityDOsByCityIdInDates = loadCityHisService.findLoadCityDOsByCityIdInDates(cityId, dates, Constants.CALIBER_ID_BG_QW);
        if (CollectionUtils.isEmpty(cityDOsByCityIdInDates)) {
            return;
        }
        Map<Date, LoadCityHisDO> loadCityHisDOMap = cityDOsByCityIdInDates.stream().collect(Collectors.toMap(LoadCityHisDO::getDate, Function.identity()));
        IndustryLoadMaxFeatureDTO industryLoadMaxFeatureDTO = new IndustryLoadMaxFeatureDTO();
        industryLoadMaxFeatureDTO.setTradeName("调度负荷");
        industryLoadMaxFeatureDTO.setTradeCode("");
        industryLoadMaxFeatureDTO.setOrderNo(-1);
        industryLoadMaxFeatureDTO.setDate(String.valueOf(date));
        date = DateUtil.getFormatDate(date);
        tongbiDate = DateUtil.getFormatDate(tongbiDate);
        huanbiDate = DateUtil.getFormatDate(huanbiDate);
        if (loadCityHisDOMap.get(date) != null) {
            LoadCityHisDO today = loadCityHisDOMap.get(date);
            int todayPointIndex = ColumnUtil.getPointIndex(maxTime);
            industryLoadMaxFeatureDTO.setLoad(today.getloadList().get(todayPointIndex));
        }
        if (loadCityHisDOMap.get(tongbiDate) != null && tongbiMaxTime != null) {
            LoadCityHisDO tongbi = loadCityHisDOMap.get(tongbiDate);
            industryLoadMaxFeatureDTO.setTongbiLoad(tongbi.getloadList().get(ColumnUtil.getPointIndex(tongbiMaxTime)));
            industryLoadMaxFeatureDTO.setTongbiGrowthRate(LoadCalUtil.calcRate(industryLoadMaxFeatureDTO.getLoad().subtract(industryLoadMaxFeatureDTO.getTongbiLoad()), industryLoadMaxFeatureDTO.getTongbiLoad()));
        }
        if (loadCityHisDOMap.get(huanbiDate) != null && tongbiMaxTime != null) {
            LoadCityHisDO huanbi = loadCityHisDOMap.get(huanbiDate);
            industryLoadMaxFeatureDTO.setHuanbiLoad(huanbi.getloadList().get(ColumnUtil.getPointIndex(huanbiMaxTime)));
            industryLoadMaxFeatureDTO.setHuanbiGrowthRate(LoadCalUtil.calcRate(industryLoadMaxFeatureDTO.getLoad().subtract(industryLoadMaxFeatureDTO.getHuanbiLoad()), industryLoadMaxFeatureDTO.getHuanbiLoad()));
        }
        results.add(industryLoadMaxFeatureDTO);
    }

    private void calcHuanbiInfo(IndustryLoadMaxFeatureDTO industryLoadMaxFeatureDTO, IndustryCityLoadDayHisClctDO huanbi, String huanbiMaxTime, BigDecimal maxLoad, BigDecimal huanbiMaxLoad) {
        BigDecimal huanbiLoad = BigDecimalFunctions.multiply(this.getFieldValueByTime(huanbiMaxTime, huanbi), BigDecimal.TEN);
        industryLoadMaxFeatureDTO.setHuanbiDate(huanbi.getDate());
        industryLoadMaxFeatureDTO.setHuanbiMaxTime(huanbiMaxTime);
        industryLoadMaxFeatureDTO.setHuanbiLoad(huanbiLoad);
        industryLoadMaxFeatureDTO.setHuanbiGrowthRate(LoadCalUtil.calcRate(industryLoadMaxFeatureDTO.getLoad().subtract(huanbiLoad), huanbiLoad));
        industryLoadMaxFeatureDTO.setHuanbiChangeValue(BigDecimalFunctions.subtract(industryLoadMaxFeatureDTO.getLoad(), huanbiLoad));
        BigDecimal huanbiTotalChange = BigDecimalFunctions.multiply(BigDecimalFunctions.subtract(maxLoad, huanbiMaxLoad), BigDecimal.TEN);
        industryLoadMaxFeatureDTO.setHuanbiChangeContributionRate(LoadCalUtil.calcRate(industryLoadMaxFeatureDTO.getHuanbiChangeValue(), huanbiTotalChange));
        if (Constants.TOTAL_SOCIAL_TRADE_CODE.equals(industryLoadMaxFeatureDTO.getTradeCode())) {
            industryLoadMaxFeatureDTO.setHuanbiChangeContributionRate(BigDecimal.valueOf(100));
        }
    }

    private List<String> getSubIndustryTradeIds(String code, String dataType) {
        List<String> tradeIds;
        if (Constants.TOTAL_SOCIAL_TRADE_CODE.equals(code)) {
            tradeIds = "0".equals(dataType) ? new ArrayList<>(INDUSTRY_ID_MAP.keySet()) : new ArrayList<>(TRADE_ID_MAP.keySet());
            tradeIds.add(Constants.TOTAL_SOCIAL_TRADE_CODE);
        } else {
            List<IndustryBaseInitDO> baseIndustryInfo = industryBaseInitService.getIndustryBaseInitServiceListByTypes(Collections.singletonList(code));
            tradeIds = baseIndustryInfo.stream().map(IndustryBaseInitDO::getId).collect(Collectors.toList());
            tradeIds.add(code);
        }
        return tradeIds;
    }

    /**
     * 处理每个月的行业月负荷特性
     *
     * @param results
     * @param cityId
     * @param monthHisStatsDO
     * @param subIndustryMap
     * @param tradeIds
     * @param periodType
     */
    private void processOneMonth(List<IndustryLoadMaxFeatureDTO> results, String cityId,
                                 LoadFeatureIndustryMonthHisServiceDO monthHisStatsDO,
                                 Map<Date, List<IndustryCityLoadDayHisClctDO>> subIndustryMap,
                                 List<String> tradeIds, String periodType) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM");
        Date date = monthHisStatsDO.getMaxDate();
        // 上月环比行业负荷
        Date lastMonth = DateUtils.addMonths(date, -1);
        List<LoadFeatureIndustryMonthHisServiceDO> lastMonthLoadFeatureList = this.getIndustryLoadFeatureMonthByType(cityId, dateFormat.format(lastMonth), dateFormat.format(lastMonth), periodType);
        LoadFeatureIndustryMonthHisServiceDO lastMonthLoadFeature = CollectionUtils.isEmpty(lastMonthLoadFeatureList) ? null : lastMonthLoadFeatureList.get(0);
        Map<String, IndustryCityLoadDayHisClctDO> lastMonthSubIndustryMaps = new HashMap<>();
        if (lastMonthLoadFeature != null) {
            List<IndustryCityLoadDayHisClctDO> lastMonthSubIndustryDOs = industryCityLoadDayHisClctService.findIndustryCityLoadDayHisClctDOS(
                    cityId, tradeIds, Collections.singletonList(new Date(lastMonthLoadFeature.getMaxDate().getTime())));
            if (!CollectionUtils.isEmpty(lastMonthSubIndustryDOs)) {
                lastMonthSubIndustryMaps = lastMonthSubIndustryDOs.stream().collect(Collectors.toMap(IndustryCityLoadDayHisClctDO::getTradeCode, Function.identity(), (o, n) -> o));
            }
        }

        // 去年同比行业负荷
        Date lastYear = DateUtils.addYears(date, -1);
        List<LoadFeatureIndustryMonthHisServiceDO> lastYearLoadFeatureList = this.getIndustryLoadFeatureMonthByType(cityId, dateFormat.format(lastYear), dateFormat.format(lastYear), periodType);
        LoadFeatureIndustryMonthHisServiceDO lastYearLoadFeature = CollectionUtils.isEmpty(lastYearLoadFeatureList) ? null : lastYearLoadFeatureList.get(0);
        Map<String, IndustryCityLoadDayHisClctDO> lastYearSubIndustryMaps = new HashMap<>();
        if (lastYearLoadFeature != null) {
            List<IndustryCityLoadDayHisClctDO> lastYearSubIndustryDOs = industryCityLoadDayHisClctService.findIndustryCityLoadDayHisClctDOS(
                    cityId, tradeIds, Collections.singletonList(new Date(lastYearLoadFeature.getMaxDate().getTime())));
            if (!CollectionUtils.isEmpty(lastYearSubIndustryDOs)) {
                lastYearSubIndustryMaps = lastYearSubIndustryDOs.stream().collect(Collectors.toMap(IndustryCityLoadDayHisClctDO::getTradeCode, Function.identity(), (o, n) -> o));
            }
        }

        List<IndustryCityLoadDayHisClctDO> cityLoadDayHisClctDOS = subIndustryMap.get(new Date(date.getTime()));
        List<IndustryLoadMaxFeatureDTO> loadMaxFeatureDTOList = new ArrayList<>();
        for (IndustryCityLoadDayHisClctDO industryCityLoadDayHisClctDO : cityLoadDayHisClctDOS) {
            IndustryLoadMaxFeatureDTO industryLoadMaxFeatureDTO = new IndustryLoadMaxFeatureDTO();
            industryLoadMaxFeatureDTO.setTradeCode(industryCityLoadDayHisClctDO.getTradeCode());
            industryLoadMaxFeatureDTO.setTradeName(industryCityLoadDayHisClctDO.getTradeCodeDsc());
            industryLoadMaxFeatureDTO.setLoad(BigDecimalFunctions.multiply(this.getFieldValueByTime(monthHisStatsDO.getMaxTime(), industryCityLoadDayHisClctDO), BigDecimal.TEN));
            industryLoadMaxFeatureDTO.setDate(dateFormat.format(date));
            if (lastMonthLoadFeature != null && lastMonthLoadFeature.getMaxLoad() != null) {
                BigDecimal lastMonthMaxLoad = lastMonthLoadFeature.getMaxLoad();
                industryLoadMaxFeatureDTO.setAllTradeHuanbiGrowthRate(LoadCalUtil.calcRate(monthHisStatsDO.getMaxLoad().subtract(lastMonthMaxLoad), lastMonthMaxLoad));
                IndustryCityLoadDayHisClctDO huanbi = lastMonthSubIndustryMaps.get(industryLoadMaxFeatureDTO.getTradeCode());
                if (huanbi != null) {
                    BigDecimal huanbiLoad = BigDecimalFunctions.multiply(getFieldValueByTime(lastMonthLoadFeature.getMaxTime(), huanbi), BigDecimal.TEN);
                    industryLoadMaxFeatureDTO.setHuanbiGrowthRate(LoadCalUtil.calcRate(industryLoadMaxFeatureDTO.getLoad().subtract(huanbiLoad), huanbiLoad));
                    industryLoadMaxFeatureDTO.setHuanbiLoad(huanbiLoad);
                }
            }
            if (lastYearLoadFeature != null && lastYearLoadFeature.getMaxLoad() != null) {
                BigDecimal lastYearMaxLoad = lastYearLoadFeature.getMaxLoad();
                industryLoadMaxFeatureDTO.setAllTradeTongbiGrowthRate(LoadCalUtil.calcRate(monthHisStatsDO.getMaxLoad().subtract(lastYearMaxLoad), lastYearMaxLoad));
                IndustryCityLoadDayHisClctDO tongbi = lastYearSubIndustryMaps.get(industryLoadMaxFeatureDTO.getTradeCode());
                if (tongbi != null) {
                    BigDecimal tongbiLoad = BigDecimalFunctions.multiply(getFieldValueByTime(lastYearLoadFeature.getMaxTime(), tongbi), BigDecimal.TEN);
                    industryLoadMaxFeatureDTO.setTongbiGrowthRate(LoadCalUtil.calcRate(industryLoadMaxFeatureDTO.getLoad().subtract(tongbiLoad), tongbiLoad));
                    industryLoadMaxFeatureDTO.setTongbiLoad(tongbiLoad);
                }
            }
            loadMaxFeatureDTOList.add(industryLoadMaxFeatureDTO);
        }
        results.addAll(loadMaxFeatureDTOList);
    }

    /**
     * 处理每天行业负荷特性
     *
     * @param dayHisStatsDO
     * @param industryCityLoadDayHisClctDO
     * @param loadFeatureList
     * @param lastYearLoadFeatureList
     * @param lastYearSubIndustryMap
     * @return
     */
    private IndustryLoadMaxFeatureDTO processOneDay(LoadFeatureIndustryDayHisServiceDO dayHisStatsDO, IndustryCityLoadDayHisClctDO industryCityLoadDayHisClctDO, List<LoadFeatureIndustryDayHisServiceDO> loadFeatureList, List<LoadFeatureIndustryDayHisServiceDO> lastYearLoadFeatureList, Map<String, IndustryCityLoadDayHisClctDO> lastYearSubIndustryMap) {
        IndustryLoadMaxFeatureDTO industryLoadMaxFeatureDTO = new IndustryLoadMaxFeatureDTO();
        industryLoadMaxFeatureDTO.setTradeCode(industryCityLoadDayHisClctDO.getTradeCode());
        industryLoadMaxFeatureDTO.setTradeName(industryCityLoadDayHisClctDO.getTradeCodeDsc());
        industryLoadMaxFeatureDTO.setLoad(BigDecimalFunctions.multiply(getFieldValueByTime(dayHisStatsDO.getMaxTime(), industryCityLoadDayHisClctDO), BigDecimal.TEN));
        industryLoadMaxFeatureDTO.setDate(DateUtils.date2String(dayHisStatsDO.getDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR));

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date yesterday = DateUtils.addDays(industryCityLoadDayHisClctDO.getDate(), -1);
        Date lastYear = DateUtils.addYears(industryCityLoadDayHisClctDO.getDate(), -1);

        // 昨日环比数据处理
        Map<String, LoadFeatureIndustryDayHisServiceDO> yesterdayLoadFeatureMap = loadFeatureList.stream().collect(Collectors.toMap(obj -> DateUtils.date2String(obj.getDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR), obj -> obj, (existing, replacement) -> existing));
        LoadFeatureIndustryDayHisServiceDO huanbiFeature = yesterdayLoadFeatureMap.get(dateFormat.format(yesterday));
        if (huanbiFeature != null) {
            industryLoadMaxFeatureDTO.setAllTradeHuanbiGrowthRate(LoadCalUtil.calcRate(dayHisStatsDO.getMaxLoad().subtract(huanbiFeature.getMaxLoad()), huanbiFeature.getMaxLoad()));
            IndustryCityLoadDayHisClctDO subTradeHuanbi = lastYearSubIndustryMap.get(industryLoadMaxFeatureDTO.getTradeCode() + DateUtils.date2String(huanbiFeature.getDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR));
            if (subTradeHuanbi != null) {
                BigDecimal huanbiLoad = BigDecimalFunctions.multiply(getFieldValueByTime(huanbiFeature.getMaxTime(), subTradeHuanbi), BigDecimal.TEN);
                industryLoadMaxFeatureDTO.setHuanbiGrowthRate(LoadCalUtil.calcRate(industryLoadMaxFeatureDTO.getLoad().subtract(huanbiLoad), huanbiLoad));
                industryLoadMaxFeatureDTO.setHuanbiLoad(huanbiLoad);
            }
        }

        // 去年同比数据处理
        Map<String, LoadFeatureIndustryDayHisServiceDO> lastYearLoadFeatureMap = lastYearLoadFeatureList.stream().collect(Collectors.toMap(obj -> DateUtils.date2String(obj.getDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR), obj -> obj, (existing, replacement) -> existing));
        LoadFeatureIndustryDayHisServiceDO tongbiFeature = lastYearLoadFeatureMap.get(dateFormat.format(lastYear));
        if (tongbiFeature != null && MapUtils.isNotEmpty(lastYearSubIndustryMap)) {
            industryLoadMaxFeatureDTO.setAllTradeTongbiGrowthRate(LoadCalUtil.calcRate(dayHisStatsDO.getMaxLoad().subtract(tongbiFeature.getMaxLoad()), tongbiFeature.getMaxLoad()));
            IndustryCityLoadDayHisClctDO subTradeTonbi = lastYearSubIndustryMap.get(industryLoadMaxFeatureDTO.getTradeCode() + DateUtils.date2String(tongbiFeature.getDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR));
            if (subTradeTonbi != null) {
                BigDecimal tongbiLoad = BigDecimalFunctions.multiply(getFieldValueByTime(tongbiFeature.getMaxTime(), subTradeTonbi), BigDecimal.TEN);
                industryLoadMaxFeatureDTO.setTongbiGrowthRate(LoadCalUtil.calcRate(industryLoadMaxFeatureDTO.getLoad().subtract(tongbiLoad), tongbiLoad));
                industryLoadMaxFeatureDTO.setTongbiLoad(tongbiLoad);
            }
        }
        return industryLoadMaxFeatureDTO;
    }

    /**
     * 通过时间获取对应字段值
     *
     * @param time
     * @param industryCityLoadDayHisClctDO
     * @return
     */
    private BigDecimal getFieldValueByTime(String time, IndustryCityLoadDayHisClctDO industryCityLoadDayHisClctDO) {
        String timeStr = time.replace(":", "");
        String methodName = "getT" + timeStr;
        Method method;
        BigDecimal fieldValue = BigDecimal.valueOf(0.0);
        try {
            method = industryCityLoadDayHisClctDO.getClass().getMethod(methodName);
            fieldValue = (BigDecimal) method.invoke(industryCityLoadDayHisClctDO);
        } catch (Exception e) {
            log.error("获取字段值异常", e);
        }
        return fieldValue;
    }

//    private void reCalcRateBaseParentIndustry(List<IndustryLoadMaxFeatureDTO> results) {
//        if (org.springframework.util.CollectionUtils.isEmpty(results) || results.size() < 2) {
//            return;
//        }
//        IndustryLoadMaxFeatureDTO parentIndustry = results.get(0);
//        parentIndustry.setHuanbiChangeContributionRate(BigDecimalFunctions.HUNDREND);
//        for (int i = 1; i < results.size(); i++) {
//            IndustryLoadMaxFeatureDTO subLoadMaxFeatureDTO = results.get(i);
//            subLoadMaxFeatureDTO.setHuanbiChangeContributionRate(LoadCalUtil.calcRate(subLoadMaxFeatureDTO.getHuanbiChangeValue(), parentIndustry.getHuanbiChangeValue()));
//            subLoadMaxFeatureDTO.setLoadRate(LoadCalUtil.calcRate(subLoadMaxFeatureDTO.getLoad(), parentIndustry.getLoad()));
//        }
//    }

    /**
     * 设置子行业数据
     *
     * @param results
     * @param tradeCode
     * @param cityId
     * @param dataType
     */
    @SneakyThrows
    private void setSubTradeFeatureList(List<IndustryLoadMaxFeatureDTO> results, String tradeCode, String cityId, String dataType) {
        if (!Constants.TOTAL_SOCIAL_TRADE_CODE.equals(tradeCode) || !"1".equals(dataType)) {
            return;
        }
        //查询所有行业下的子行业
        Map<String, List<String>> tradeInfoMap = industryBaseInitService.getIndustryBaseInitServiceListByTypes(new ArrayList<>(TRADE_ID_MAP.keySet())).stream()
                .collect(Collectors.groupingBy(IndustryBaseInitDO::getParentId, Collectors.mapping(IndustryBaseInitDO::getId, Collectors.toList())));
        List<String> tradeIds = tradeInfoMap.values().stream().flatMap(List::stream).collect(Collectors.toList());

        IndustryLoadMaxFeatureDTO loadMaxFeatureDTO = results.get(1);
        String[] dateStr = loadMaxFeatureDTO.getDate().split(" ");
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        Date today = formatter.parse(dateStr[0]);
        List<Date> dates = new ArrayList<>();
        dates.add(today);
        dates.add(loadMaxFeatureDTO.getHuanbiDate());
        dates.add(loadMaxFeatureDTO.getTongbiDate());
        Map<Date, List<IndustryCityLoadDayHisClctDO>> dataListGroupByDate = industryCityLoadDayHisClctService.findIndustryCityLoadDayHisClctDOS(cityId, tradeIds, dates)
                .stream().collect(Collectors.groupingBy(src -> DateUtil.getFormatDate(src.getDate())));

        for (IndustryLoadMaxFeatureDTO industryDTO : results) {
            if (!TRADE_ID_MAP.containsKey(industryDTO.getTradeCode())) {
                continue;
            }
            BigDecimal allTradeLoad = BigDecimalFunctions.divide(industryDTO.getLoad(), industryDTO.getLoad());
            List<IndustryLoadMaxFeatureDTO> subTrades = new ArrayList<>();

            List<IndustryCityLoadDayHisClctDO> todayIndustryLoadDOS = dataListGroupByDate.get(today).stream().filter(src -> shouldInclude(src, tradeInfoMap, industryDTO)).collect(Collectors.toList());
            List<IndustryCityLoadDayHisClctDO> lastYearIndustryLoadDOS = dataListGroupByDate.getOrDefault(DateUtil.getFormatDate(loadMaxFeatureDTO.getHuanbiDate()), Collections.emptyList()).stream().filter(src -> shouldInclude(src, tradeInfoMap, industryDTO)).collect(Collectors.toList());
            List<IndustryCityLoadDayHisClctDO> tongbiIndustryLoadDOS = dataListGroupByDate.getOrDefault(loadMaxFeatureDTO.getTongbiDate(), Collections.emptyList()).stream().filter(src -> shouldInclude(src, tradeInfoMap, industryDTO)).collect(Collectors.toList());
            lastYearIndustryLoadDOS.addAll(tongbiIndustryLoadDOS);

            Map<String, IndustryCityLoadDayHisClctDO> loadFeatureMap = new HashMap<>();
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(lastYearIndustryLoadDOS)) {
                loadFeatureMap = lastYearIndustryLoadDOS.stream().collect(Collectors.toMap(dto -> dto.getTradeCode() + DateUtil.getFormatDate(dto.getDate()), Function.identity(), (o, n) -> o));
            }
            for (IndustryCityLoadDayHisClctDO todayIndustryLoadDO : todayIndustryLoadDOS) {
                IndustryLoadMaxFeatureDTO industryLoadMaxFeatureDTO = new IndustryLoadMaxFeatureDTO();
                industryLoadMaxFeatureDTO.setTradeCode(todayIndustryLoadDO.getTradeCode());
                industryLoadMaxFeatureDTO.setTradeName(todayIndustryLoadDO.getTradeCodeDsc());
                BigDecimal load = this.getFieldValueByTime(dateStr[1], todayIndustryLoadDO);
                industryLoadMaxFeatureDTO.setLoad(BigDecimalFunctions.multiply(load, BigDecimal.TEN));

                industryLoadMaxFeatureDTO.setLoadRate(LoadCalUtil.calcRate(industryLoadMaxFeatureDTO.getLoadRate(), allTradeLoad));
                IndustryCityLoadDayHisClctDO huanbi = loadFeatureMap.get(todayIndustryLoadDO.getTradeCode() + DateUtil.getFormatDate(industryDTO.getHuanbiDate()));
                if (huanbi != null && industryLoadMaxFeatureDTO.getLoad() != null) {
                    BigDecimal huanbiLoad = BigDecimalFunctions.multiply(this.getFieldValueByTime(industryDTO.getHuanbiMaxTime(), huanbi), BigDecimal.TEN);
                    industryLoadMaxFeatureDTO.setHuanbiLoad(huanbiLoad);
                    industryLoadMaxFeatureDTO.setHuanbiGrowthRate(LoadCalUtil.calcRate(industryLoadMaxFeatureDTO.getLoad().subtract(huanbiLoad), huanbiLoad));
                    industryLoadMaxFeatureDTO.setHuanbiChangeValue(BigDecimalFunctions.subtract(industryLoadMaxFeatureDTO.getLoad(), huanbiLoad));
                    industryLoadMaxFeatureDTO.setHuanbiChangeContributionRate(BigDecimalFunctions.multiply(BigDecimalFunctions.divide(industryLoadMaxFeatureDTO.getHuanbiChangeValue(), industryDTO.getHuanbiChangeValue()), industryDTO.getHuanbiChangeContributionRate()));
                }
                IndustryCityLoadDayHisClctDO tongbi = loadFeatureMap.get(todayIndustryLoadDO.getTradeCode() + DateUtil.getFormatDate(industryDTO.getTongbiDate()));
                if (tongbi != null) {
                    BigDecimal tongbiLoad = this.getFieldValueByTime(industryDTO.getTongbiMaxTime(), tongbi);
                    tongbiLoad = BigDecimalFunctions.multiply(tongbiLoad, BigDecimal.TEN);
                    industryLoadMaxFeatureDTO.setTongbiLoad(tongbiLoad);
                    industryLoadMaxFeatureDTO.setTongbiGrowthRate(LoadCalUtil.calcRate(industryLoadMaxFeatureDTO.getLoad().subtract(tongbiLoad), tongbiLoad));
                }
                subTrades.add(industryLoadMaxFeatureDTO);
                subTrades.sort(Comparator.comparing(IndustryLoadMaxFeatureDTO::getTradeCode));
            }
            industryDTO.setSubIndustryFeatureList(subTrades);
        }
    }

    /**
     * 计算负荷占比
     *
     * @param maxFeatureDTO
     * @param maxLoad
     * @param cityId
     * @param date
     * @param maxTime
     */
    @SneakyThrows
    private void setLoadRate(IndustryLoadMaxFeatureDTO maxFeatureDTO, BigDecimal maxLoad, String cityId, Date date, String maxTime) {
        BigDecimal load = maxFeatureDTO.getLoad();
        maxLoad = BigDecimalFunctions.multiply(maxLoad, BigDecimal.TEN);
        //行业负荷占比:行业/产业负荷占系统全行业负荷占比
        if (!Constants.TOTAL_SOCIAL_TRADE_CODE.equals(maxFeatureDTO.getTradeCode())) {
            maxFeatureDTO.setLoadRate(LoadCalUtil.calcRate(load, maxLoad));
        } else {
            //系统全行业占比:系统全行业负荷占调度负荷占比
            LoadCityHisDO loadCityHisDO = loadCityHisService.getLoadCityHisDO(cityId, Constants.CALIBER_ID_BG_QW, date);
            int todayPointIndex = ColumnUtil.getPointIndex(maxTime);
            BigDecimal schedulingLoad = loadCityHisDO.getloadList().get(todayPointIndex);
            maxFeatureDTO.setLoadRate(LoadCalUtil.calcRate(load, schedulingLoad));
        }
    }

    /**
     * 结果排序
     *
     * @param results
     * @param code
     * @param dataType
     */
    private void sortResult(List<IndustryLoadMaxFeatureDTO> results, String code, String dataType) {
        Comparator<IndustryLoadMaxFeatureDTO> comparator = Comparator.comparingInt(
                dto -> {
                    Integer orderNo = dto.getOrderNo();
                    return (orderNo != null) ? orderNo : Integer.MAX_VALUE;
                });
        results.sort(comparator);
    }
}