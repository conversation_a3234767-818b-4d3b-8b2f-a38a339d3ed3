/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.weather.impl;

import com.tsintergy.lf.serviceapi.base.weather.api.WeatherStationFcBasicService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherStationFcBasicDO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherStationFcBasicDAO;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/5/31 16:37
 * @Version: 1.0.0
 */
@Service("weatherStationFcBasicService")
public class WeatherStationFcBasicServiceImpl implements WeatherStationFcBasicService {

    @Autowired
    WeatherStationFcBasicDAO weatherStationFcBasicDAO;
    @Override
    public List<WeatherStationFcBasicDO> getWeatherStationHisBasicDO(String cityId, String stationId, Date startDate,
        Date endDate, Integer type) {
        return null;
    }

    @Override
    public void doSaveOrUpdate(WeatherStationFcBasicDO weatherStationFcBasicDO) {
        weatherStationFcBasicDAO.saveOrUpdate(weatherStationFcBasicDO);
    }

    @Override
    public void doSaveOrUpdateList(Map<String, WeatherStationFcBasicDO> result) throws Exception {

    }
}