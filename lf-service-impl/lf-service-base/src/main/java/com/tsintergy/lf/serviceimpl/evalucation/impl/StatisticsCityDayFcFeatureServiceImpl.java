package com.tsintergy.lf.serviceimpl.evalucation.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsintergy.lf.serviceapi.base.evalucation.api.StatisticsCityDayFcFeatureService;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.StatisticsCityDayFcFeatureDO;
import com.tsintergy.lf.serviceimpl.evalucation.dao.StatisticsCityDayFcFeatureDAO;
import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2023-11-30
 * @since 1.0.0
 */
@Service("statisticsCityDayFcFeatureService")
public class StatisticsCityDayFcFeatureServiceImpl implements StatisticsCityDayFcFeatureService {

    @Autowired
    private StatisticsCityDayFcFeatureDAO statisticsCityDayFcFeatureDAO;

    @Override
    public List<StatisticsCityDayFcFeatureDO> getStatisticsCityDayFcFeatureDOReportList(String cityId, String caliberId,
        Date startDate, Date endDate) throws Exception {
        List<StatisticsCityDayFcFeatureDO> all = statisticsCityDayFcFeatureDAO.findAll(
            JpaWrappers.<StatisticsCityDayFcFeatureDO>lambdaQuery()
                .eq(StatisticsCityDayFcFeatureDO::getCityId, cityId)
                .eq(StatisticsCityDayFcFeatureDO::getCaliberId, caliberId)
                .eq(StatisticsCityDayFcFeatureDO::getReport, true)
                .ge(StatisticsCityDayFcFeatureDO::getDate, startDate)
                .le(StatisticsCityDayFcFeatureDO::getDate, endDate));
        return all;
    }

    @Override
    public List<StatisticsCityDayFcFeatureDO> getStatisticsCityDayFcFeatureDOReportList(String cityId, String caliberId,
        Date startDate, Date endDate, String algorithmId) throws Exception {
        List<StatisticsCityDayFcFeatureDO> all = statisticsCityDayFcFeatureDAO.findAll(
            JpaWrappers.<StatisticsCityDayFcFeatureDO>lambdaQuery()
                .eq(StatisticsCityDayFcFeatureDO::getCityId, cityId)
                .eq(StatisticsCityDayFcFeatureDO::getCaliberId, caliberId)
                .eq(algorithmId != null, StatisticsCityDayFcFeatureDO::getAlgorithmId, algorithmId)
                .ge(StatisticsCityDayFcFeatureDO::getDate, startDate)
                .le(StatisticsCityDayFcFeatureDO::getDate, endDate));
        return all;
    }
}
