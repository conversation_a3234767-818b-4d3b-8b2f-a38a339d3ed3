/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.bgd.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsieframework.core.base.service.BaseFacadeServiceImpl;
import com.tsintergy.lf.serviceapi.base.bgd.api.StatisticsCityQuarterFcFeatureService;
import com.tsintergy.lf.serviceapi.base.bgd.pojo.StatisticsCityQuarterFcFeatureServiceDO;
import com.tsintergy.lf.serviceimpl.bgd.dao.StatisticsCityQuarterFcFeatureServiceDAO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/11/17 21:23
 * @Version: 1.0.0
 */

@Service("statisticsCityQuarterFcFeatureServiceImpl")
public class StatisticsCityQuarterFcFeatureServiceImpl extends BaseFacadeServiceImpl implements
        StatisticsCityQuarterFcFeatureService {

    @Autowired
    StatisticsCityQuarterFcFeatureServiceDAO statisticsCityQuarterFcFeatureServiceDAO;
    @Override
    public List<StatisticsCityQuarterFcFeatureServiceDO> findStatisticsCityQuarterFcFeature(String cityId, String caliberId,
                                                                                            String algorithmId, String year, String quarter) {
        return  statisticsCityQuarterFcFeatureServiceDAO.findAll(JpaWrappers.<StatisticsCityQuarterFcFeatureServiceDO>lambdaQuery()
            .eq(!StringUtils.isEmpty(cityId),StatisticsCityQuarterFcFeatureServiceDO::getCityId,cityId)
            .eq(!StringUtils.isEmpty(caliberId),StatisticsCityQuarterFcFeatureServiceDO::getCaliberId,caliberId)
            .eq(!StringUtils.isEmpty(algorithmId),StatisticsCityQuarterFcFeatureServiceDO::getAlgorithmId,algorithmId)
            .eq(!StringUtils.isEmpty(year),StatisticsCityQuarterFcFeatureServiceDO::getYear,year)
            .eq(!StringUtils.isEmpty(quarter),StatisticsCityQuarterFcFeatureServiceDO::getQuarter,quarter)
        );
    }

    @Override
    public void saveOrUpdateList(List<StatisticsCityQuarterFcFeatureServiceDO> quarterDOList) {
        statisticsCityQuarterFcFeatureServiceDAO.saveOrUpdateBatchByTemplate(quarterDOList, 100);
    }

}