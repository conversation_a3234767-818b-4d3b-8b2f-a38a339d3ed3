/**
 * Copyright (C), 2015‐2020, 北京清能互联科技有限公司
 * Author:  wangchen
 * Date:  2020/4/19 17:18
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceimpl.ultra.dao;


import com.tsieframework.core.base.dao.jpa.BaseJpaDAO;
import com.tsieframework.core.component.datasource.dynamic.annotation.DataSource;
import com.tsintergy.lf.serviceapi.base.ultra.pojo.LoadFeatureCityDayUltraFcDO;
import org.springframework.stereotype.Repository;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2020/4/19
 * @since 1.0.0
 */
@Repository
@DataSource("ultra")
public interface LoadFeatureCityDayUltraFcDAO extends BaseJpaDAO<LoadFeatureCityDayUltraFcDO, String> {

}
