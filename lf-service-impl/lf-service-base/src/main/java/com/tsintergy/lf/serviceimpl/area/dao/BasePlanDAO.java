/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.area.dao;

import com.tsieframework.core.base.dao.hibernate.BaseAbstractDAO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.dao.hibernate.QueryOp;
import com.tsintergy.lf.serviceapi.base.area.pojo.BaseAreaDO;
import com.tsintergy.lf.serviceapi.base.area.pojo.BasePlanDO;
import java.util.List;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/8/4 9:39
 * @Version: 1.0.0
 */
@Component
public class BasePlanDAO extends BaseAbstractDAO<BasePlanDO> {


    public BasePlanDO getDefaultPlan()  {
        DBQueryParamBuilder param = DBQueryParamBuilder.create().queryDataOnly();
        param.where(QueryOp.StringEqualTo, "valid", "1");
        List<BasePlanDO> datas = query(param.build()).getDatas();
        if (CollectionUtils.isEmpty(datas)){
            return null;
        }
        return datas.get(0);
    }

    public BasePlanDO selectByName(String name) {
        DBQueryParamBuilder param = DBQueryParamBuilder.create().queryDataOnly();
        param.where(QueryOp.StringEqualTo, "name", name);
        List<BasePlanDO> datas = query(param.build()).getDatas();
        if (datas != null && datas.size() > 0) {
            return datas.get(0);
        }
        return null;
    }
}