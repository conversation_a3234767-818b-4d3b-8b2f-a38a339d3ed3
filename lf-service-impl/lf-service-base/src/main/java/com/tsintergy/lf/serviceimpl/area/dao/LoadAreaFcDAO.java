package com.tsintergy.lf.serviceimpl.area.dao;


import com.tsieframework.core.base.dao.hibernate.BaseAbstractDAO;
import com.tsieframework.core.base.dao.hibernate.BaseDAO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsintergy.lf.serviceapi.base.area.pojo.LoadAreaFcDO;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 * @version $Id: LoadCityFcDAO.java, v 0.1 2018-01-31 10:50:22 tao Exp $$
 */
@Component
public class LoadAreaFcDAO extends BaseAbstractDAO<LoadAreaFcDO> {

    /**
     * 查询历史负荷数据
     *
     * @param areaId    城市ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param caliberId 口径ID
     * @return
     * @throws Exception
     */
    public List<LoadAreaFcDO> getLoadCityFcDO(String areaId, Date startDate, Date endDate, String caliberId) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (null != startDate) {
            param.getQueryConditions().put("_dnl_date", new java.sql.Date(startDate.getTime()));
        }
        if (null != endDate) {
            param.getQueryConditions().put("_dnm_date", new java.sql.Date(endDate.getTime()));
        }
        if (null != areaId) {
            param.getQueryConditions().put("_ne_areId", areaId);
        }
        if (null != caliberId) {
            param.getQueryConditions().put("_ne_caliberId", caliberId);
        }
        param.setOrderby("date");
        param.setDesc("0");
        List<LoadAreaFcDO> loadCityFcDOS = this.query(param).getDatas();
        return loadCityFcDOS;
    }

    /**
     * get entities by ids and type in a period
     *
     * @param areaIds
     * @param startDate
     * @param endDate
     * @return
     * @throws Exception
     */
    public List<LoadAreaFcDO> getLoadCityFcDOSByCityIds(List<String> areaIds, Date startDate, Date endDate, String caliberId) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (null != startDate) {
            param.getQueryConditions().put("_dnl_date", new java.sql.Date(startDate.getTime()));
        }
        if (null != endDate) {
            param.getQueryConditions().put("_dnm_date", new java.sql.Date(endDate.getTime()));
        }
        if (null != areaIds) {
            param.getQueryConditions().put("_sin_areId", areaIds);
        }
        if (null != caliberId) {
            param.getQueryConditions().put("_ne_caliberId", caliberId);
        }
        param.setOrderby("areaId,date");
        param.setDesc("0,0");
        List<LoadAreaFcDO> loadCityFcDOS = this.query(param).getDatas();
        return loadCityFcDOS;
    }

    /**
     * 查询历史负荷数据
     *
     * @param areaId    城市ID
     * @param date      日期
     * @param caliberId 口径ID
     * @return
     * @throws Exception
     */
    public LoadAreaFcDO getLoadCityFcDOByOneDate(String areaId, Date date, String caliberId) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        param.getQueryConditions().put("_ne_date", new java.sql.Date(date.getTime()));
        param.getQueryConditions().put("_ne_areaId", areaId);
        param.getQueryConditions().put("_ne_caliberId", caliberId);
        List<LoadAreaFcDO> loadCityFcDOS = this.query(param).getDatas();
        if (loadCityFcDOS != null && loadCityFcDOS.size() > 0) {
            return loadCityFcDOS.get(0);
        }
        return null;
    }

    /**
     * 查询历史负荷数据
     *
     * @param areaId    城市ID
     * @param dates     日期列表
     * @param caliberId 口径ID
     * @return
     * @throws Exception
     */
    public List<LoadAreaFcDO> getLoadCityFcDOSByDates(String areaId, List<Date> dates, String caliberId) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (null != dates) {
            List<java.sql.Date> sqlDates = new ArrayList<java.sql.Date>();
            for (Date date : dates) {
                sqlDates.add(new java.sql.Date(date.getTime()));
            }
            param.getQueryConditions().put("_din_date", sqlDates);
        }
        if (null != areaId) {
            param.getQueryConditions().put("_se_areId", areaId);
        }
        if (null != caliberId) {
            param.getQueryConditions().put("_se_caliberId", caliberId);
        }
        param.setOrderby("date");
        param.setDesc("0");
        List<LoadAreaFcDO> loadCityFcDOS = this.query(param).getDatas();
        return loadCityFcDOS;
    }
    /**
     * 查询历史负荷数据
     *
     * @param date      日期
     * @param caliberId 口径ID
     * @return
     * @throws Exception
     */
    public List<LoadAreaFcDO> getLoadCityFcDOWithOutCityId( Date date, String caliberId) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        param.getQueryConditions().put("_ne_date", new java.sql.Date(date.getTime()));
        param.getQueryConditions().put("_ne_caliberId", caliberId);
        List<LoadAreaFcDO> loadCityFcDOS = this.query(param).getDatas();
        if (loadCityFcDOS != null && loadCityFcDOS.size() > 0) {
            return loadCityFcDOS;
        }
        return null;
    }
}