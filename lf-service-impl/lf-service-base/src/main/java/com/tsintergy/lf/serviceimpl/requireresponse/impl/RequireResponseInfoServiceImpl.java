/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.requireresponse.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.requireresponse.api.RequireResponseInfoService;
import com.tsintergy.lf.serviceapi.base.requireresponse.pojo.RequireResponseInfoDO;
import com.tsintergy.lf.serviceimpl.requireresponse.dao.RequireResponseInfoDAO;
import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/8/23 14:59
 * @Version: 1.0.0
 */

@Service("requireResponseInfoService")
public class RequireResponseInfoServiceImpl implements RequireResponseInfoService {

    @Autowired
    private RequireResponseInfoDAO requireResponseInfoDAO;
    @Override
    public List<RequireResponseInfoDO> getRequireResponseInfoByYear(String year) {
        Date startDate = DateUtil.getYearFirst(Integer.parseInt(year));
        Date endDate = DateUtil.getYearLast(Integer.parseInt(year));
        List<RequireResponseInfoDO> requireResponseInfoDOS = requireResponseInfoDAO
            .findAll(JpaWrappers.<RequireResponseInfoDO>lambdaQuery()
                .ge(RequireResponseInfoDO::getDate, startDate)
                .le(RequireResponseInfoDO::getDate, endDate));
        return requireResponseInfoDOS;
    }
}