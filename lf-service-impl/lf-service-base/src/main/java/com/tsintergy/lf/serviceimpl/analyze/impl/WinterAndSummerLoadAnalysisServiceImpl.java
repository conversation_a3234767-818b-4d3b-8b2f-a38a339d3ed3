/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.analyze.impl;

import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.analyze.api.WinterAndSummerLoadAnalysisService;
import com.tsintergy.lf.serviceapi.base.analyze.dto.*;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityDayHisService;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadHisDTO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityDayHisDO;
import com.tsintergy.lf.serviceapi.base.requireresponse.api.RequireResponseInfoService;
import com.tsintergy.lf.serviceapi.base.requireresponse.pojo.RequireResponseInfoDO;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityDayHisService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayHisDO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/8/22 15:49
 * @Version: 1.0.0
 */
@Service("winterAndSummerLoadAnalysisService")
public class WinterAndSummerLoadAnalysisServiceImpl implements WinterAndSummerLoadAnalysisService {


    @Autowired
    private LoadFeatureCityDayHisService loadFeatureCityDayHisService;

    @Autowired
    private WeatherFeatureCityDayHisService weatherFeatureCityDayHisService;

    @Autowired
    private LoadCityHisService loadCityHisService;

    @Autowired
    private RequireResponseInfoService requireResponseInfoService;
    @Override
    public List<LoadComparisonDTO> getLoadComparisonDTO(String analyzeType,String year, String caliberId, String cityId) {

        try {

            List<LoadComparisonDTO> result = new ArrayList<>();
            List<Date> dateList = getDateListByType(analyzeType,year);

            LoadFeatureCityDayHisDO maxLoadFeatureDoByDate = getMaxLoadFeatureDoByDate(dateList, cityId, caliberId);


            //去年
             String lastYear = getLastYear(year);

            LoadFeatureCityDayHisDO lastYearMaxLoadFeatureDoByDate = getMaxLoadFeatureDoByDate(getDateListByType(analyzeType,lastYear), cityId, caliberId);

            mergeResult(year,maxLoadFeatureDoByDate,lastYearMaxLoadFeatureDoByDate,result);

            //前年
            String beforeLastYear = getLastYear(lastYear);


            LoadFeatureCityDayHisDO beforeLastYearMaxLoadFeatureDoByDate = getMaxLoadFeatureDoByDate(getDateListByType(analyzeType,beforeLastYear), cityId, caliberId);

            mergeResult(lastYear,lastYearMaxLoadFeatureDoByDate,beforeLastYearMaxLoadFeatureDoByDate,result);
            //3年前
            String beforeLastYear2 = getLastYear(beforeLastYear);

            LoadFeatureCityDayHisDO beforeLastYearMaxLoadFeatureDoByDate2 = getMaxLoadFeatureDoByDate(getDateListByType(analyzeType,beforeLastYear2), cityId, caliberId);

            mergeResult(beforeLastYear,beforeLastYearMaxLoadFeatureDoByDate,beforeLastYearMaxLoadFeatureDoByDate2,result);

            //4年前
            String beforeLastYear3 = getLastYear(beforeLastYear2);

            LoadFeatureCityDayHisDO beforeLastYearMaxLoadFeatureDoByDate3 = getMaxLoadFeatureDoByDate(getDateListByType(analyzeType,beforeLastYear3), cityId, caliberId);

            mergeResult(beforeLastYear2,beforeLastYearMaxLoadFeatureDoByDate2,beforeLastYearMaxLoadFeatureDoByDate3,result);

            //5年前
            String beforeLastYear4 = getLastYear(beforeLastYear3);

            LoadFeatureCityDayHisDO beforeLastYearMaxLoadFeatureDoByDate4 = getMaxLoadFeatureDoByDate(getDateListByType(analyzeType,beforeLastYear4), cityId, caliberId);

            mergeResult(beforeLastYear3,beforeLastYearMaxLoadFeatureDoByDate3,beforeLastYearMaxLoadFeatureDoByDate4,result);

            if (beforeLastYearMaxLoadFeatureDoByDate4 != null){
                LoadComparisonDTO loadComparisonDTO = new LoadComparisonDTO();
                loadComparisonDTO.setMaxLoad(beforeLastYearMaxLoadFeatureDoByDate4.getMaxLoad());
                loadComparisonDTO.setDate(beforeLastYearMaxLoadFeatureDoByDate4.getDate());
                loadComparisonDTO.setYear(beforeLastYear4);
                result.add(loadComparisonDTO);
            }

            return result;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }



    @Override
    public List<LoadComparisonCurveDTO> getLoadComparisonCurve(String analyzeType, String year, String caliberId,
        String cityId) {
        try {
            List<LoadComparisonCurveDTO> result = new ArrayList<>();

            List<Date> dateList = getDateListByType(analyzeType, year);
            mergeWeatherFeature(year, cityId, dateList,result);
            mergeLoadFeature(year,cityId,caliberId,dateList,result);


            String lastYear = getLastYear(year);
            List<Date> lastDateList = getDateListByType(analyzeType, lastYear);
            mergeWeatherFeature(lastYear,cityId,lastDateList,result);
            mergeLoadFeature(lastYear,cityId,caliberId,lastDateList,result);

            return result;

        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }



    @Override
    public LoadStatisticsDTO getLoadStatistics(String analyzeType, String year, String caliberId, String cityId) {

        try {
            LoadStatisticsDTO result = new LoadStatisticsDTO();
            List<Date> dateList = getDateListByType(analyzeType, year);
            LoadFeatureCityDayHisDO maxLoadFeatureDoByDate = getMaxLoadFeatureDoByDate(dateList, cityId, caliberId);

            List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisDOS = loadFeatureCityDayHisService
                .findLoadFeatureCityDayHisDOS(cityId, dateList.get(0), dateList.get(dateList.size()-1), caliberId);
            BigDecimal maxLoad = null;
            int count = 0;
            if (maxLoadFeatureDoByDate != null){
                result.setMaxLoad(maxLoadFeatureDoByDate.getMaxLoad());
                String lastYear = getLastYear(year);
                List<Date> lastDateList = getDateListByType(analyzeType, lastYear);
                LoadFeatureCityDayHisDO lastYearMaxLoadFeature = getMaxLoadFeatureDoByDate(lastDateList, cityId, caliberId);
                if (lastYearMaxLoadFeature != null){
                    //同比增长率
                    BigDecimal loadGrowthRate = getLoadGrowthRate(maxLoadFeatureDoByDate.getMaxLoad(),
                        lastYearMaxLoadFeature.getMaxLoad());
                    result.setLoadGrowth(loadGrowthRate);
                    if (!CollectionUtils.isEmpty(loadFeatureCityDayHisDOS)){
                        loadFeatureCityDayHisDOS = loadFeatureCityDayHisDOS.stream().sorted(Comparator.comparing(t->t.getMaxLoad())).collect(
                            Collectors.toList());
                        maxLoad = lastYearMaxLoadFeature.getMaxLoad();

                        for (LoadFeatureCityDayHisDO loadFeatureCityDayHisDO : loadFeatureCityDayHisDOS) {
                            if (loadFeatureCityDayHisDO.getMaxLoad().compareTo(maxLoad)>0){
                                count++;
                                maxLoad = loadFeatureCityDayHisDO.getMaxLoad();
                            }
                        }
                    }
                }
                result.setInnovationsHighCount(count);
                result.setLastDate(lastYearMaxLoadFeature.getDate());
                if (count>0){
                    result.setLastDate(maxLoadFeatureDoByDate.getDate());
                }
            }
            return result;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public List<LoadHisDTO> getLoadStatisticsCurve(String analyzeType, String year, String caliberId, String cityId) {
        try {
            List<LoadHisDTO> result = new ArrayList<>();
            LoadHisDTO yearLoad = new LoadHisDTO();
            List<Date> dateList = getDateListByType(analyzeType, year);
            LoadFeatureCityDayHisDO maxLoadFeatureDoByDate = getMaxLoadFeatureDoByDate(dateList, cityId, caliberId);
            if (maxLoadFeatureDoByDate != null){
                yearLoad.setDate(maxLoadFeatureDoByDate.getDate());
                yearLoad.setData(loadCityHisService.findLoadCityHisDO(maxLoadFeatureDoByDate.getDate(),cityId,caliberId));
                result.add(yearLoad);
            }
            LoadHisDTO lastYearLoad = new LoadHisDTO();
            String lastYear = getLastYear(year);
            List<Date> lastDateList = getDateListByType(analyzeType, lastYear);
            LoadFeatureCityDayHisDO lastYearMaxLoadFeature = getMaxLoadFeatureDoByDate(lastDateList, cityId, caliberId);
            if (lastYearMaxLoadFeature != null){
                lastYearLoad.setDate(lastYearMaxLoadFeature.getDate());
                lastYearLoad.setData(loadCityHisService.findLoadCityHisDO(lastYearMaxLoadFeature.getDate(),cityId,caliberId));
                result.add(lastYearLoad);
            }

            return result;
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }

    @Override
    public List<RequireResponseDTO> getRequireResponse(String analyzeType, String year, String caliberId,
        String cityId) {
        List<RequireResponseInfoDO> requireResponseInfoByYear = requireResponseInfoService
            .getRequireResponseInfoByYear(year);
        return requireResponseInfoByYear.stream().map(t->{
            RequireResponseDTO requireResponseDTO = new RequireResponseDTO();
            BeanUtils.copyProperties(t,requireResponseDTO);
            return requireResponseDTO;
        }).collect(Collectors.toList());
    }


    /**
     * 根据分析类型获取日期集合
     *
     * @param analyzeType 0 度夏 1 度冬
     * @return
     */
    private List<Date> getDateListByType(String analyzeType, String year) {
        if (analyzeType.equals("0")) {
            Date startDate = DateUtil.getDate(year + "-06-01", "yyyy-MM-dd");
            Date endDate = DateUtil.getDate(year + "-09-30", "yyyy-MM-dd");
            return DateUtil.getListBetweenDay(startDate, endDate);
        } else if (analyzeType.equals("1")) {
            Date startDate = DateUtil.getDate(year + "-11-01", "yyyy-MM-dd");
            int endYear = Integer.parseInt(year) + 1;
            String endYearStr = String.valueOf(endYear);
            Date endDate;
            if (DateUtil.isLeapYear(endYear)) {
                endDate = DateUtil.getDate(endYearStr + "-02-29", "yyyy-MM-dd");
            } else {
                endDate = DateUtil.getDate(endYearStr + "-02-28", "yyyy-MM-dd");
            }
            return DateUtil.getListBetweenDay(startDate, endDate);
        }
        return null;
    }

    /**
     * 根据本年得到上一年
     * @param year
     */
    private String getLastYear(String year) {
        //获取本年第一天
        Date yearStat = DateUtil.getYearStat(year);
        //获取去年最后一天
        Date moveDay = DateUtil.getMoveDay(yearStat, -1);
        String lastYear = DateUtil.getYearByDate(moveDay);
        return lastYear;
    }


    /***
     * 功能描述:
     * 获取日期内的最大负荷当天的实体<br>
     * @param dateList
     * @param cityId
     * @param caliberId
     * @Return:
     * @Version: 1.0.0
     * @Author:<EMAIL>
     * @Date: 2022/8/22 18:31
     */
    private LoadFeatureCityDayHisDO getMaxLoadFeatureDoByDate(List<Date> dateList, String cityId, String caliberId) throws Exception {
        List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisDOS = loadFeatureCityDayHisService
            .findLoadFeatureCityDayHisDOS(cityId, dateList.get(0), dateList.get(dateList.size()-1), caliberId);
        if (CollectionUtils.isEmpty(loadFeatureCityDayHisDOS)){
            return null;
        }

        loadFeatureCityDayHisDOS = loadFeatureCityDayHisDOS.stream().sorted(Comparator.comparing(t->t.getMaxLoad())).collect(
            Collectors.toList());


        return loadFeatureCityDayHisDOS.get(loadFeatureCityDayHisDOS.size()-1);
    }


    /**
     * 负荷增长率
     * @param maxLoad
     * @param maxLoad1
     * @return
     */
    private BigDecimal getLoadGrowthRate(BigDecimal maxLoad, BigDecimal maxLoad1) {
        if (maxLoad == null || maxLoad1 == null){
            return null;
        }
        return (maxLoad.subtract(maxLoad1).abs()).divide(maxLoad1, RoundingMode.HALF_UP);
    }


    private void mergeResult(String year ,LoadFeatureCityDayHisDO yearFeature,
        LoadFeatureCityDayHisDO lastYearFeature, List<LoadComparisonDTO> result) {
        LoadComparisonDTO loadComparisonDTO = new LoadComparisonDTO();
        BigDecimal yearMaxLoad = null;
        loadComparisonDTO.setYear(year);
        if (yearFeature!=null){
            loadComparisonDTO.setMaxLoad(yearFeature.getMaxLoad());
            loadComparisonDTO.setDate(yearFeature.getDate());
            yearMaxLoad = yearFeature.getMaxLoad();
        }

        if (lastYearFeature!=null){
            loadComparisonDTO.setLoadGrowthRate(getLoadGrowthRate(yearMaxLoad,lastYearFeature.getMaxLoad()));
        }
        result.add(loadComparisonDTO);
    }



    private void mergeLoadFeature(String year, String cityId, String caliberId,List<Date> dateList,List<LoadComparisonCurveDTO> result) throws Exception {
        LoadComparisonCurveDTO loadComparisonCurveDTO = new LoadComparisonCurveDTO();
        loadComparisonCurveDTO.setYear(year);
        loadComparisonCurveDTO.setType("0");
        List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisDOS = loadFeatureCityDayHisService
            .findLoadFeatureCityDayHisDOS(cityId, dateList.get(0), dateList.get(dateList.size() - 1), caliberId);
        if (!CollectionUtils.isEmpty(loadFeatureCityDayHisDOS)){
            List<LoadDateValueDTO> yearWeatherFeatureDto = loadFeatureCityDayHisDOS.stream()
                .map(t -> new LoadDateValueDTO(t.getDate(), t.getMaxLoad())).collect(
                    Collectors.toList());
            loadComparisonCurveDTO.setList(yearWeatherFeatureDto);
        }

        result.add(loadComparisonCurveDTO);
    }

    private void mergeWeatherFeature(String year, String cityId, List<Date> dateList,List<LoadComparisonCurveDTO> result) throws Exception {
        LoadComparisonCurveDTO loadComparisonCurveDTO = new LoadComparisonCurveDTO();
        loadComparisonCurveDTO.setYear(year);
        loadComparisonCurveDTO.setType("1");
        List<WeatherFeatureCityDayHisDO> yearWeatherFeature = weatherFeatureCityDayHisService
            .findWeatherFeatureBySearchData(cityId, dateList.get(0), dateList.get(dateList.size() - 1), null);

        if (!CollectionUtils.isEmpty(yearWeatherFeature)){
            List<LoadDateValueDTO> yearWeatherFeatureDto = yearWeatherFeature.stream()
                .map(t -> new LoadDateValueDTO(t.getDate(), t.getHighestTemperature())).collect(
                    Collectors.toList());
            loadComparisonCurveDTO.setList(yearWeatherFeatureDto);
        }
        result.add(loadComparisonCurveDTO);
    }


}
