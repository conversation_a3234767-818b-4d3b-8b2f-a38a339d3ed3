package com.tsintergy.lf.serviceimpl.ultra.impl;

import com.tsieframework.core.base.dao.type.DataPointListMeta;
import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsintergy.aif.algorithm.serviceapi.base.pojo.Load;
import com.tsintergy.aif.algorithm.serviceapi.base.util.DateUtil;
import com.tsintergy.aif.tool.core.utils.data.ColumnUtil;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.LoadCalUtil;
import com.tsintergy.lf.core.util.UltraSystemUtils;
import com.tsintergy.lf.serviceapi.base.ultra.api.LoadCityHisUltraBasicService;
import com.tsintergy.lf.serviceapi.base.ultra.load.pojo.LoadCityFcUltraDO;
import com.tsintergy.lf.serviceapi.base.ultra.load.api.LoadCityFcUltraService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHis288DO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.ultra.load.pojo.NesdBasicPeriodDO;
import com.tsintergy.lf.serviceapi.base.ultra.api.AccuracyEvaluateService;
import com.tsintergy.lf.serviceapi.base.ultra.api.LoadFeatureCityFcUltraService;
import com.tsintergy.lf.serviceapi.base.ultra.dto.LoadFeatureUltraFcDayDTO;
import com.tsintergy.lf.serviceapi.base.ultra.dto.LoadUltraFcAccuracyDTO;
import com.tsintergy.lf.serviceapi.base.ultra.pojo.LoadCityHisUltraBasicDO;
import com.tsintergy.lf.serviceapi.base.ultra.pojo.LoadFeatureCityDayUltraFcDO;
import com.tsintergy.lf.serviceimpl.load.dao.LoadCityHis288DAO;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * @Description
 * @Date 2023/1/9 16:38
 * <AUTHOR>
 **/
@Service
public class AccuracyEvaluateServiceImpl implements AccuracyEvaluateService {

    @Autowired
    LoadCityFcUltraService loadCityFcUltraService;

    @Autowired
    LoadCityHisService loadCityHisService;

    @Autowired
    LoadFeatureCityFcUltraService loadFeatureCityFcUltraService;

    @Override
    public List<LoadFeatureUltraFcDayDTO> findFeatureStatisDTOS(Date startDate, Date endDate, String caliberId, String cityId, Integer tDelta, Integer sort, String algorithmId) throws Exception {
        List<LoadFeatureCityDayUltraFcDO> loadFeatureCityDayUltraFcDOS =
                loadFeatureCityFcUltraService
                    .findLoadFeatureCityDayUltraFcDOS(cityId, startDate, endDate, caliberId, algorithmId, tDelta, Constants.ULTRA_FORECAST_1);
        if (CollectionUtils.isEmpty(loadFeatureCityDayUltraFcDOS)) {
            return new ArrayList<>();
        }
        Map<String, LoadFeatureCityDayUltraFcDO> map = loadFeatureCityDayUltraFcDOS.stream().collect(
                Collectors.toMap(e -> e.getDateTime().getTime() + e.getCityId() + e.getCaliberId() + e.getType(), e -> e, (oldv, curv) -> curv)
        );
        List<LoadFeatureUltraFcDayDTO> result = new ArrayList<>();
        for (LoadFeatureCityDayUltraFcDO loadFeatureCityDayUltraFcDO : map.values()) {
            LoadFeatureUltraFcDayDTO dayDTO = new LoadFeatureUltraFcDayDTO();
            dayDTO.setDate(DateUtil.getStrDate(loadFeatureCityDayUltraFcDO.getDateTime(), "yyyy-MM-dd"));
            BeanUtils.copyProperties(loadFeatureCityDayUltraFcDO, dayDTO);
            dayDTO.setMinAccuracy(dayDTO.getMinAccuracy().multiply(new BigDecimal(100)));
            dayDTO.setMaxAccuracy(dayDTO.getMaxAccuracy().multiply(new BigDecimal(100)));
            dayDTO.setMaxLoadAccuracy(dayDTO.getMaxLoadAccuracy().multiply(new BigDecimal(100)));
            dayDTO.setMinLoadAccuracy(dayDTO.getMinLoadAccuracy().multiply(new BigDecimal(100)));
            dayDTO.setAvgAccuracy(dayDTO.getAvgAccuracy().multiply(new BigDecimal(100)));
            result.add(dayDTO);
        }
        result = result.stream().sorted(Comparator.comparing(LoadFeatureUltraFcDayDTO::getDate)).collect(Collectors.toList());
        if (sort != null && sort == 2) {
            Collections.reverse(result);
        }
        //计算最后一行 平均行数据
        List<BigDecimal> maxAccuracys = result.stream().map(LoadFeatureUltraFcDayDTO::getMaxLoadAccuracy).filter(Objects::nonNull).collect(Collectors.toList());
        BigDecimal maxLoadAccuracyAvg = CollectionUtils.isEmpty(maxAccuracys) ? null : maxAccuracys.stream().reduce(BigDecimal.ZERO, BigDecimal::add).divide(BigDecimal.valueOf(maxAccuracys.size()), 4, BigDecimal.ROUND_HALF_UP);
        List<BigDecimal> minLoadAccuracys = result.stream().map(LoadFeatureUltraFcDayDTO::getMinLoadAccuracy).filter(Objects::nonNull).collect(Collectors.toList());
        BigDecimal minLoadAccuracyAvg = CollectionUtils.isEmpty(minLoadAccuracys) ? null : minLoadAccuracys.stream().reduce(BigDecimal.ZERO, BigDecimal::add).divide(BigDecimal.valueOf(minLoadAccuracys.size()), 4, BigDecimal.ROUND_HALF_UP);
        List<BigDecimal> maxAccuracyAvgs = result.stream().map(LoadFeatureUltraFcDayDTO::getMaxAccuracy).filter(Objects::nonNull).collect(Collectors.toList());
        BigDecimal maxAccuracyAvg = CollectionUtils.isEmpty(maxAccuracyAvgs) ? null : maxAccuracyAvgs.stream().reduce(BigDecimal.ZERO, BigDecimal::add).divide(BigDecimal.valueOf(maxAccuracyAvgs.size()), 4, BigDecimal.ROUND_HALF_UP);
        List<BigDecimal> minAccuracyAvgs = result.stream().map(LoadFeatureUltraFcDayDTO::getMinAccuracy).filter(Objects::nonNull).collect(Collectors.toList());
        BigDecimal minAccuracyAvg = CollectionUtils.isEmpty(minAccuracyAvgs) ? null : minAccuracyAvgs.stream().reduce(BigDecimal.ZERO, BigDecimal::add).divide(BigDecimal.valueOf(minAccuracyAvgs.size()), 4, BigDecimal.ROUND_HALF_UP);
        List<BigDecimal> avgAccuracyAvgs = result.stream().map(LoadFeatureUltraFcDayDTO::getAvgAccuracy).filter(Objects::nonNull).collect(Collectors.toList());
        BigDecimal avgAccuracyAvg = CollectionUtils.isEmpty(avgAccuracyAvgs) ? null : avgAccuracyAvgs.stream().reduce(BigDecimal.ZERO, BigDecimal::add).divide(BigDecimal.valueOf(avgAccuracyAvgs.size()), 4, BigDecimal.ROUND_HALF_UP);
        LoadFeatureUltraFcDayDTO dayDTO = new LoadFeatureUltraFcDayDTO();
        dayDTO.setDate("平均");
        dayDTO.setMaxLoadAccuracy(maxLoadAccuracyAvg);
        dayDTO.setMinLoadAccuracy(minLoadAccuracyAvg);
        dayDTO.setMaxAccuracy(maxAccuracyAvg);
        dayDTO.setMinAccuracy(minAccuracyAvg);
        dayDTO.setAvgAccuracy(avgAccuracyAvg);
        result.add(dayDTO);
        return result;
    }


    @Autowired
    LoadCityHis288DAO loadCityHis288DAO;

    @Autowired
    LoadCityHisUltraBasicService loadCityHisUltraBasicService;

    @Override
    public List<LoadUltraFcAccuracyDTO> findDayTimeAccuracy(Date date, String cityId, String caliberId, Integer tDelta, Integer sort, String algorithmId) throws Exception {
        //十五分钟间隔 96点预测 or 五分钟间隔 288点数据
        //十五分钟间隔 96点预测 or 五分钟间隔 288点数据
        LoadCityFcUltraDO loadCityFcDO = loadCityFcUltraService.getLoadCityFcDO(date, cityId, caliberId, algorithmId, tDelta, Constants.ULTRA_FORECAST_1);
        LoadCityHisUltraBasicDO loadCityHisDO = loadCityHisUltraBasicService.getLoadCityHisDO(cityId, caliberId, date, tDelta);
        return getLoadUltraFcAccuracyDTOList(loadCityFcDO, loadCityHisDO, tDelta, sort);
    }

    private List<LoadUltraFcAccuracyDTO> getLoadUltraFcAccuracyDTOList(NesdBasicPeriodDO fcDO, LoadCityHisUltraBasicDO hisDO, Integer tDelta, Integer sort) throws Exception {
        List<LoadUltraFcAccuracyDTO> resultList = new ArrayList<>();
        List<BigDecimal> fcBigDecimals = null;
        if (!Objects.isNull(fcDO)) {
            fcBigDecimals = fcDO.getTvData();
        }
        List<BigDecimal> hisBigDecimals = null;
        if (!Objects.isNull(hisDO)) {
            hisBigDecimals = hisDO.getTvData();
        }
        if (CollectionUtils.isEmpty(fcBigDecimals) || CollectionUtils.isEmpty(hisBigDecimals)) {
            TsieExceptionUtils.newBusinessException("检查历史或预测数据");
        }
        if (!CollectionUtils.isEmpty(fcBigDecimals)) {
            List<String> columns = ColumnUtil.getColumns(tDelta == DataPointListMeta.MINUTE_5 ? 288 : 96, UltraSystemUtils
                .startWithZero(), false);
            for (int i = 0; i < fcBigDecimals.size(); i++) {
                LoadUltraFcAccuracyDTO dto = new LoadUltraFcAccuracyDTO();
                dto.setMomentTime(columns.get(i).substring(0, 2) + ":" + columns.get(i).substring(2, 4));
                if (!CollectionUtils.isEmpty(hisBigDecimals)) {
                    if (i < hisBigDecimals.size()) {
                        dto.setHisLoad(hisBigDecimals.get(i));
                    }
                }
                dto.setFcLoad(fcBigDecimals.get(i));
                if (hisBigDecimals != null && i < hisBigDecimals.size()){
                    dto.setAccuracy(LoadCalUtil.calcMaxMinAccuracy(hisBigDecimals.get(i), fcBigDecimals.get(i)).multiply(new BigDecimal(100)));
                }
                resultList.add(dto);
            }
            if (sort != null && sort == 2) {
                Collections.reverse(resultList);
            }
        }

        return resultList;
    }
}
