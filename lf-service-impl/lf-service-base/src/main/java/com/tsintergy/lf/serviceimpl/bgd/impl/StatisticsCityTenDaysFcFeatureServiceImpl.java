package com.tsintergy.lf.serviceimpl.bgd.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsieframework.core.base.service.BaseFacadeServiceImpl;
import com.tsintergy.lf.serviceapi.base.bgd.api.StatisticsCityTenDaysFcFeatureService;
import com.tsintergy.lf.serviceapi.base.bgd.pojo.StatisticsCityTenDaysFcFeatureServiceDO;
import com.tsintergy.lf.serviceimpl.bgd.dao.StatisticsCityTenDaysFcFeatureServiceDAO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/11/17 21:23
 * @Version: 1.0.0
 */

@Service("statisticsCityTenDaysFcFeatureService")
public class StatisticsCityTenDaysFcFeatureServiceImpl extends BaseFacadeServiceImpl implements
        StatisticsCityTenDaysFcFeatureService {

    @Autowired
    StatisticsCityTenDaysFcFeatureServiceDAO statisticsCityTenDaysFcFeatureServiceDAO;


    @Override
    public List<StatisticsCityTenDaysFcFeatureServiceDO> findStatisticsCityTenDaysFcFeatures(String cityId, String caliberId,
        String algorithmId, String startYm, String endYm) {
        List<StatisticsCityTenDaysFcFeatureServiceDO> cityTenDaysFcFeatureServiceDOS = statisticsCityTenDaysFcFeatureServiceDAO
            .findAll(JpaWrappers.<StatisticsCityTenDaysFcFeatureServiceDO>lambdaQuery()
                .eq(!StringUtils.isEmpty(cityId), StatisticsCityTenDaysFcFeatureServiceDO::getCityId, cityId)
                .eq(!StringUtils.isEmpty(caliberId), StatisticsCityTenDaysFcFeatureServiceDO::getCaliberId, caliberId)
                .eq(!StringUtils.isEmpty(algorithmId), StatisticsCityTenDaysFcFeatureServiceDO::getAlgorithmId,
                    algorithmId)
                .ge(!StringUtils.isEmpty(startYm), StatisticsCityTenDaysFcFeatureServiceDO::getYear,
                    startYm.substring(0, 4))
                .le(!StringUtils.isEmpty(endYm), StatisticsCityTenDaysFcFeatureServiceDO::getYear,
                    endYm.substring(0, 4)));
        List<StatisticsCityTenDaysFcFeatureServiceDO> result = new ArrayList<>();
        for (StatisticsCityTenDaysFcFeatureServiceDO cityTenDaysFcFeatureServiceDO : cityTenDaysFcFeatureServiceDOS) {
            if((cityTenDaysFcFeatureServiceDO.getYear()+"-"+cityTenDaysFcFeatureServiceDO.getMonth()).compareTo(startYm) > -1 && (cityTenDaysFcFeatureServiceDO.getYear()+"-"+cityTenDaysFcFeatureServiceDO.getMonth()).compareTo(endYm) < 1) {
                result.add(cityTenDaysFcFeatureServiceDO);
            }
        }
        return result ;
    }

    @Override
    public List<StatisticsCityTenDaysFcFeatureServiceDO> findStatisticsCityTenDaysFcFeature(String cityId, String caliberId,
                                                                                            String algorithmId, String year, String month,String type) {
        return  statisticsCityTenDaysFcFeatureServiceDAO.findAll(JpaWrappers.<StatisticsCityTenDaysFcFeatureServiceDO>lambdaQuery()
                .eq(!StringUtils.isEmpty(cityId),StatisticsCityTenDaysFcFeatureServiceDO::getCityId,cityId)
                .eq(!StringUtils.isEmpty(caliberId),StatisticsCityTenDaysFcFeatureServiceDO::getCaliberId,caliberId)
                .eq(!StringUtils.isEmpty(algorithmId),StatisticsCityTenDaysFcFeatureServiceDO::getAlgorithmId,algorithmId)
                .eq(!StringUtils.isEmpty(year),StatisticsCityTenDaysFcFeatureServiceDO::getYear,year)
                .eq(!StringUtils.isEmpty(month),StatisticsCityTenDaysFcFeatureServiceDO::getMonth,month)
                .eq(!StringUtils.isEmpty(type),StatisticsCityTenDaysFcFeatureServiceDO::getMonth,type)
        );
    }


}