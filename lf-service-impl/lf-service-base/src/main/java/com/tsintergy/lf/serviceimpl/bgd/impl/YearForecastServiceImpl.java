package com.tsintergy.lf.serviceimpl.bgd.impl;


import com.tsieframework.core.base.service.BaseFacadeServiceImpl;
import com.tsieframework.util.compatible.BigDecimalUtils;
import com.tsintergy.lf.core.constants.AlgorithmConstants;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.enums.MonthTypeEnum;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.core.util.LoadCalUtil;
import com.tsintergy.lf.serviceapi.base.bgd.api.StatisticsCityMonthFcFeatureService;
import com.tsintergy.lf.serviceapi.base.bgd.api.YearForecastService;
import com.tsintergy.lf.serviceapi.base.bgd.dto.MonthWeatherResultDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.MonthWeatherSettingDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.SeasonReportDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.YearLoadFeatureDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.YearWeatherFeatureDTO;
import com.tsintergy.lf.serviceapi.base.bgd.pojo.StatisticsCityMonthFcFeatureServiceDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityMonthFcService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityMonthHisService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityMonthFcDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityMonthHisDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCitySeasonFcDO;
import com.tsintergy.lf.serviceapi.base.system.api.ReportSystemService;
import com.tsintergy.lf.serviceapi.base.system.pojo.ReportSystemInitDO;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityDayFcService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityDayHisService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityMonthHisService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityYearHisService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayFcDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayHisDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityMonthHisDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityYearHisDO;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("yearForecastService")
public class YearForecastServiceImpl extends BaseFacadeServiceImpl implements YearForecastService {

    @Autowired
    private LoadFeatureCityMonthFcService loadFeatureCityMonthFcService;

    @Autowired
    private LoadFeatureCityMonthHisService loadFeatureCityMonthHisService;


    @Autowired
    private WeatherFeatureCityDayFcService weatherFeatureCityDayFcService;

    @Autowired
    private WeatherFeatureCityDayHisService weatherFeatureCityDayHisService;

    @Autowired
    private WeatherFeatureCityMonthHisService weatherFeatureCityMonthHisService;

    @Autowired
    WeatherFeatureCityYearHisService weatherFeatureCityYearHisService;

    @Autowired
    private StatisticsCityMonthFcFeatureService statisticsCityMonthFcFeatureService;

    @Autowired
    private ReportSystemService reportSystemService;

    @Override
    public List<YearLoadFeatureDTO> getYearLoadFc(String cityId, String caliberId, String year, String algorithmId,
        String startStr, String endStr) throws Exception {
        List<YearLoadFeatureDTO> result = new ArrayList<>();
        List<String> targetYearList = DateUtil.getTargetYearList(startStr, endStr);
        String startYear = startStr.split("-")[0];
        String endYear = endStr.split("-")[0];
        List<String> yearList = new ArrayList<>();
        if (startYear.equals(endYear)) {
            yearList.add(startYear);
        } else {
            yearList.add(startYear);
            yearList.add(endYear);
        }
        //List<LoadFeatureCitySeasonFcDO> seasonReportFeatures = new ArrayList<>();
        List<LoadFeatureCitySeasonFcDO> seasonReportFc = new ArrayList<>();
        List<LoadFeatureCityMonthHisDO> loadFeatureCityMonthHisDOList = new ArrayList<>();
        // 上报
        //Map<String, LoadFeatureCitySeasonFcDO> reportFeatureMap = new HashMap<>();
        // 预测
        Map<String, LoadFeatureCitySeasonFcDO> fcFeatureMap = new HashMap<>();
        Map<String, LoadFeatureCityMonthHisDO> hisFeatureMap = new HashMap<>();

        for (String s : yearList) {
            seasonReportFc.addAll(loadFeatureCityMonthFcService.getSeasonFeatures(
                s.split("-")[0], null, cityId, caliberId, null));
            loadFeatureCityMonthHisDOList.addAll(loadFeatureCityMonthHisService.getLoadFeatureCityMonthHisDOLists(
                cityId, String.valueOf(Integer.valueOf(s.split("-")[0]) - 1), null, caliberId));
        }
        if (CollectionUtils.isNotEmpty(seasonReportFc)) {
            if (algorithmId.equals("0")) {
                fcFeatureMap = seasonReportFc.stream()
                    .filter(t -> t.getReport() != null && Boolean.TRUE.equals(t.getReport()))
                    .collect(Collectors.toMap(t -> t.getYear() + "-" + t.getMonth(), t -> t));
            } else {
                fcFeatureMap = seasonReportFc.stream()
                    .filter(t -> t.getAlgorithmId().equals(algorithmId))
                    .collect(Collectors.toMap(t -> t.getYear() + "-" + t.getMonth(), t -> t));
            }
        }
        if (CollectionUtils.isNotEmpty(loadFeatureCityMonthHisDOList)) {
            hisFeatureMap = loadFeatureCityMonthHisDOList.stream().distinct()
                .collect(Collectors.toMap(t -> t.getYear() + "-" + t.getMonth(), t -> t));
        }
        for (String s : targetYearList) {
            String sYear = s.split("-")[0];
            String sMonth = s.split("-")[1];
            String key = sYear + "-" + sMonth;
            YearLoadFeatureDTO yearLoadFeatureDTO = new YearLoadFeatureDTO();
            yearLoadFeatureDTO.setMonth(key);
            for (String s1 : fcFeatureMap.keySet()) {
                // 预测
                if (key.equals(s1)) {
                    LoadFeatureCitySeasonFcDO loadFeatureCitySeasonFcDO = fcFeatureMap.get(s1);
                    yearLoadFeatureDTO.setMaxLoad(loadFeatureCitySeasonFcDO.getMaxLoad());
                    yearLoadFeatureDTO.setMinLoad(loadFeatureCitySeasonFcDO.getMinLoad());
                    yearLoadFeatureDTO.setEnergy(loadFeatureCitySeasonFcDO.getEnergy());
                    yearLoadFeatureDTO.setNoonTimeLoad(loadFeatureCitySeasonFcDO.getNoontimeLoad());
                    yearLoadFeatureDTO.setExtremeMaxLoad(loadFeatureCitySeasonFcDO.getExtremeMaxLoad());
                    yearLoadFeatureDTO.setExtremeMinLoad(loadFeatureCitySeasonFcDO.getExtremeMinLoad());
                    yearLoadFeatureDTO.setExtremeNoonTimeLoad(loadFeatureCitySeasonFcDO.getExtremeNoontimeLoad());
                    yearLoadFeatureDTO.setExtremeMonthEnergy(loadFeatureCitySeasonFcDO.getExtremeEnergy());
                }
            }
            key = String.valueOf(Integer.valueOf(key.split("-")[0]) - 1) + "-" + key.split("-")[1];
            for (String s1 : hisFeatureMap.keySet()) {
                // 实际
                if (key.equals(s1)) {
                    LoadFeatureCityMonthHisDO loadFeatureCityMonthHisDO = hisFeatureMap.get(s1);
                    if (loadFeatureCityMonthHisDO != null) {
                        yearLoadFeatureDTO.setLastMaxLoad(loadFeatureCityMonthHisDO.getMaxLoad());
                        yearLoadFeatureDTO.setLastMinLoad(loadFeatureCityMonthHisDO.getMinLoad());
                        yearLoadFeatureDTO.setLastEnergy(loadFeatureCityMonthHisDO.getEnergy());
                        yearLoadFeatureDTO.setLastYearNoonTimeLoad(loadFeatureCityMonthHisDO.getNoontimeLoad());
                    }
                }
            }
            result.add(yearLoadFeatureDTO);
        }
        return result;
    }

    @Override
    public void save(List<SeasonReportDTO> monthReportDTOS) throws Exception {
        SeasonReportDTO seasonReportDTO = monthReportDTOS.get(0);
        String caliberId = seasonReportDTO.getCaliberId();
        String cityId = seasonReportDTO.getCityId();
        String algorithmId = seasonReportDTO.getAlgorithmId();
        for (SeasonReportDTO monthReportDTO : monthReportDTOS) {
            String date = monthReportDTO.getDate();
            String[] split = date.split(Constants.SEPARATOR_BROKEN_LINE);
            String year = split[0];
            String month = split[1];
            LoadFeatureCityMonthFcDO loadFeatureCityMonthFcDO = new LoadFeatureCityMonthFcDO();
            loadFeatureCityMonthFcDO.setCityId(cityId);
            loadFeatureCityMonthFcDO.setYear(year);
            loadFeatureCityMonthFcDO.setCaliberId(caliberId);
            loadFeatureCityMonthFcDO.setMonth(month);
            loadFeatureCityMonthFcDO.setMaxLoad(monthReportDTO.getMaxLoad());
            loadFeatureCityMonthFcDO.setAlgorithmId(algorithmId);
            loadFeatureCityMonthFcDO.setMinLoad(monthReportDTO.getMinLoad());
            loadFeatureCityMonthFcDO.setEnergy(monthReportDTO.getEnergy());
            loadFeatureCityMonthFcService.saveOrUpdate(loadFeatureCityMonthFcDO);
        }
    }

    @Override
    public List<YearLoadFeatureDTO> getYearLoadComparison(String cityId, String caliberId, String year,
        String algorithmId, String startStr, String endStr) throws Exception {
        List<YearLoadFeatureDTO> result = new ArrayList<>();
        List<String> targetYearList = DateUtil.getTargetYearList(startStr, endStr);
        String startYear = startStr.split("-")[0];
        String endYear = endStr.split("-")[0];
        List<String> yearList = new ArrayList<>();
        if (startYear.equals(endYear)) {
            yearList.add(startYear);
        } else {
            yearList.add(startYear);
            yearList.add(endYear);
        }
        List<LoadFeatureCitySeasonFcDO> seasonReportFc = new ArrayList<>();
        List<LoadFeatureCityMonthHisDO> loadFeatureCityMonthHisDOList = new ArrayList<>();
        // 预测
        Map<String, LoadFeatureCitySeasonFcDO> fcFeatureMap = new HashMap<>();
        Map<String, LoadFeatureCityMonthHisDO> hisFeatureMap = new HashMap<>();
        for (String s : yearList) {
            seasonReportFc.addAll(loadFeatureCityMonthFcService.getSeasonFeatures(
                s.split("-")[0], null, cityId, caliberId, null));
            loadFeatureCityMonthHisDOList.addAll(loadFeatureCityMonthHisService.getLoadFeatureCityMonthHisDOLists(
                cityId, s.split("-")[0], null, caliberId));
        }
        if (CollectionUtils.isNotEmpty(seasonReportFc)) {
            if (algorithmId.equals("0")) {
                fcFeatureMap = seasonReportFc.stream()
                    .filter(t -> t.getReport() != null && Boolean.TRUE.equals(t.getReport()))
                    .collect(Collectors.toMap(t -> t.getYear() + "-" + t.getMonth(), t -> t));
            } else {
                fcFeatureMap = seasonReportFc.stream()
                    .filter(t -> t.getAlgorithmId().equals(algorithmId))
                    .collect(Collectors.toMap(t -> t.getYear() + "-" + t.getMonth(), t -> t));
            }
        }
        if (CollectionUtils.isNotEmpty(loadFeatureCityMonthHisDOList)) {
            hisFeatureMap = loadFeatureCityMonthHisDOList.stream().distinct()
                .collect(Collectors.toMap(t -> t.getYear() + "-" + t.getMonth(), t -> t));
        }
        for (String s : targetYearList) {
            String sYear = s.split("-")[0];
            String sMonth = s.split("-")[1];
            String key = sYear + "-" + sMonth;
            YearLoadFeatureDTO yearLoadFeatureDTO = new YearLoadFeatureDTO();
            yearLoadFeatureDTO.setMonth(key);
            for (String s1 : hisFeatureMap.keySet()) {
                // 实际
                if (key.equals(s1)) {
                    LoadFeatureCityMonthHisDO loadFeatureCityMonthHisDO = hisFeatureMap.get(s1);
                    yearLoadFeatureDTO.setLastMaxLoad(loadFeatureCityMonthHisDO.getMaxLoad());
                    yearLoadFeatureDTO.setLastMinLoad(loadFeatureCityMonthHisDO.getMinLoad());
                    yearLoadFeatureDTO.setLastEnergy(loadFeatureCityMonthHisDO.getEnergy());
                    yearLoadFeatureDTO.setLastYearNoonTimeLoad(loadFeatureCityMonthHisDO.getNoontimeLoad());
                }
            }
            for (String s1 : fcFeatureMap.keySet()) {
                // 预测
                if (key.equals(s1)) {
                    LoadFeatureCitySeasonFcDO loadFeatureCitySeasonFcDO = fcFeatureMap.get(s1);
                    yearLoadFeatureDTO.setMaxLoad(loadFeatureCitySeasonFcDO.getMaxLoad());
                    yearLoadFeatureDTO.setMinLoad(loadFeatureCitySeasonFcDO.getMinLoad());
                    yearLoadFeatureDTO.setEnergy(loadFeatureCitySeasonFcDO.getEnergy());
                    yearLoadFeatureDTO.setNoonTimeLoad(loadFeatureCitySeasonFcDO.getNoontimeLoad());
                    yearLoadFeatureDTO.setExtremeMaxLoad(loadFeatureCitySeasonFcDO.getExtremeMaxLoad());
                    yearLoadFeatureDTO.setExtremeMinLoad(loadFeatureCitySeasonFcDO.getExtremeMinLoad());
                    yearLoadFeatureDTO.setExtremeNoonTimeLoad(loadFeatureCitySeasonFcDO.getExtremeNoontimeLoad());
                    yearLoadFeatureDTO.setExtremeMonthEnergy(loadFeatureCitySeasonFcDO.getExtremeEnergy());
                }
            }
            result.add(yearLoadFeatureDTO);
        }
        for (YearLoadFeatureDTO yearLoadFeatureDTO : result) {
            yearLoadFeatureDTO.setMaxLoadAccuracy(
                LoadCalUtil.getAccuracy(yearLoadFeatureDTO.getLastMaxLoad(), yearLoadFeatureDTO.getMaxLoad()));
            yearLoadFeatureDTO.setMinLoadAccuracy(
                LoadCalUtil.getAccuracy(yearLoadFeatureDTO.getLastMinLoad(), yearLoadFeatureDTO.getMinLoad()));
            yearLoadFeatureDTO.setEnergyAccuracy(
                LoadCalUtil.getAccuracy(yearLoadFeatureDTO.getLastEnergy(), yearLoadFeatureDTO.getEnergy()));
            yearLoadFeatureDTO.setNoonTimeLoadAccuracy(
                LoadCalUtil.getAccuracy(yearLoadFeatureDTO.getLastYearNoonTimeLoad(),
                    yearLoadFeatureDTO.getNoonTimeLoad()));
        }
        return result;
    }

    private void getYearLoadFeaturesDTO(String algorithmId, List<YearLoadFeatureDTO> result,
        Map<String, LoadFeatureCitySeasonFcDO> fcFeatureMap,
        Map<String, LoadFeatureCityMonthHisDO> hisFeatureMap,
        Map<String, LoadFeatureCitySeasonFcDO> reportMap,
        Map<String, StatisticsCityMonthFcFeatureServiceDO> accuracyMap,
        Map<String, StatisticsCityMonthFcFeatureServiceDO> reportAccuracyMap) {
        for (MonthTypeEnum value : MonthTypeEnum.values()) {
            YearLoadFeatureDTO yearLoadFeatureDTO = new YearLoadFeatureDTO();
            String month = value.getType();
            yearLoadFeatureDTO.setMonth(month);
            LoadFeatureCityMonthHisDO loadFeatureCityMonthHisDO = hisFeatureMap.get(month);

            LoadFeatureCitySeasonFcDO loadFeatureCityMonthFcDO = fcFeatureMap.get(month);
            if (loadFeatureCityMonthFcDO != null) {
                yearLoadFeatureDTO.setMaxLoad(loadFeatureCityMonthFcDO.getMaxLoad());
                yearLoadFeatureDTO.setMinLoad(loadFeatureCityMonthFcDO.getMinLoad());
                yearLoadFeatureDTO.setEnergy(loadFeatureCityMonthFcDO.getEnergy());
            } else {
                if (AlgorithmConstants.MD_ALGORITHM_ID.equals(algorithmId)) {
                    LoadFeatureCitySeasonFcDO reportVo = reportMap.get(month);
                    if (reportVo != null) {
                        yearLoadFeatureDTO.setMaxLoad(reportVo.getMaxLoad());
                        yearLoadFeatureDTO.setMinLoad(reportVo.getMinLoad());
                        yearLoadFeatureDTO.setEnergy(reportVo.getEnergy());
                    }
                }
            }
            if (loadFeatureCityMonthHisDO != null) {
                yearLoadFeatureDTO.setLastMaxLoad(loadFeatureCityMonthHisDO.getMaxLoad());
                yearLoadFeatureDTO.setLastMinLoad(loadFeatureCityMonthHisDO.getMinLoad());
                yearLoadFeatureDTO.setLastEnergy(loadFeatureCityMonthHisDO.getEnergy());
            }
            if (accuracyMap != null) {
                StatisticsCityMonthFcFeatureServiceDO statisticsCityMonthFcFeatureServiceDO = accuracyMap.get(month);
                if (statisticsCityMonthFcFeatureServiceDO != null) {
                    yearLoadFeatureDTO.setMaxLoadAccuracy(statisticsCityMonthFcFeatureServiceDO.getMaxLoadAccuracy());
                    yearLoadFeatureDTO.setMinLoadAccuracy(statisticsCityMonthFcFeatureServiceDO.getMinLoadAccuracy());
                    yearLoadFeatureDTO.setEnergyAccuracy(statisticsCityMonthFcFeatureServiceDO.getEnergyAccuracy());
                } else {
                    if (AlgorithmConstants.MD_ALGORITHM_ID.equals(algorithmId) && reportAccuracyMap != null) {
                        StatisticsCityMonthFcFeatureServiceDO reportVO = reportAccuracyMap.get(month);
                        if (reportVO != null) {
                            yearLoadFeatureDTO.setMaxLoadAccuracy(reportVO.getMaxLoadAccuracy());
                            yearLoadFeatureDTO.setMinLoadAccuracy(reportVO.getMinLoadAccuracy());
                            yearLoadFeatureDTO.setEnergyAccuracy(reportVO.getEnergyAccuracy());
                        }
                    }
                }
            }
            result.add(yearLoadFeatureDTO);
        }
    }

    private void getYearLoadFeatureDTO(String algorithmId, List<YearLoadFeatureDTO> result,
        Map<String, LoadFeatureCitySeasonFcDO> fcFeatureMap,
        Map<String, LoadFeatureCityMonthHisDO> hisFeatureMap,
        Map<String, LoadFeatureCitySeasonFcDO> reportMap,
        Map<String, StatisticsCityMonthFcFeatureServiceDO> accuracyMap,
        Map<String, StatisticsCityMonthFcFeatureServiceDO> reportAccuracyMap)
        throws Exception {
        for (MonthTypeEnum value : MonthTypeEnum.values()) {
            YearLoadFeatureDTO yearLoadFeatureDTO = new YearLoadFeatureDTO();
            String month = value.getType();
            yearLoadFeatureDTO.setMonth(month);
            LoadFeatureCityMonthHisDO loadFeatureCityMonthHisDO = hisFeatureMap.get(month);

            LoadFeatureCitySeasonFcDO loadFeatureCityMonthFcDO = fcFeatureMap.get(month);
            if (loadFeatureCityMonthFcDO != null) {
                yearLoadFeatureDTO.setMaxLoad(loadFeatureCityMonthFcDO.getMaxLoad());
                yearLoadFeatureDTO.setMinLoad(loadFeatureCityMonthFcDO.getMinLoad());
                yearLoadFeatureDTO.setEnergy(loadFeatureCityMonthFcDO.getEnergy());
            } else {
                if (AlgorithmConstants.MD_ALGORITHM_ID.equals(algorithmId)) {
                    LoadFeatureCitySeasonFcDO reportVo = reportMap.get(month);
                    if (reportVo != null) {
                        yearLoadFeatureDTO.setMaxLoad(reportVo.getMaxLoad());
                        yearLoadFeatureDTO.setMinLoad(reportVo.getMinLoad());
                        yearLoadFeatureDTO.setEnergy(reportVo.getEnergy());
                    }
                }
            }
            if (loadFeatureCityMonthHisDO != null) {
                yearLoadFeatureDTO.setLastMaxLoad(loadFeatureCityMonthHisDO.getMaxLoad());
                yearLoadFeatureDTO.setLastMinLoad(loadFeatureCityMonthHisDO.getMinLoad());
                yearLoadFeatureDTO.setLastEnergy(loadFeatureCityMonthHisDO.getEnergy());
            }
            if (accuracyMap != null) {
                StatisticsCityMonthFcFeatureServiceDO statisticsCityMonthFcFeatureServiceDO = accuracyMap.get(month);
                if (statisticsCityMonthFcFeatureServiceDO != null) {
                    yearLoadFeatureDTO.setMaxLoadAccuracy(statisticsCityMonthFcFeatureServiceDO.getMaxLoadAccuracy());
                    yearLoadFeatureDTO.setMinLoadAccuracy(statisticsCityMonthFcFeatureServiceDO.getMinLoadAccuracy());
                    yearLoadFeatureDTO.setEnergyAccuracy(statisticsCityMonthFcFeatureServiceDO.getEnergyAccuracy());
                } else {
                    if (AlgorithmConstants.MD_ALGORITHM_ID.equals(algorithmId) && reportAccuracyMap != null) {
                        StatisticsCityMonthFcFeatureServiceDO reportVO = reportAccuracyMap.get(month);
                        if (reportVO != null) {
                            yearLoadFeatureDTO.setMaxLoadAccuracy(reportVO.getMaxLoadAccuracy());
                            yearLoadFeatureDTO.setMinLoadAccuracy(reportVO.getMinLoadAccuracy());
                            yearLoadFeatureDTO.setEnergyAccuracy(reportVO.getEnergyAccuracy());
                        }
                    }
                }
            }
            BigDecimal accuracy = LoadCalUtil.getAccuracy(yearLoadFeatureDTO.getLastMaxLoad(),
                yearLoadFeatureDTO.getMaxLoad());
            BigDecimal accuracy1 = LoadCalUtil.getAccuracy(yearLoadFeatureDTO.getLastMinLoad(),
                yearLoadFeatureDTO.getMinLoad());
            BigDecimal accuracy2 = LoadCalUtil.getAccuracy(yearLoadFeatureDTO.getLastEnergy(),
                yearLoadFeatureDTO.getEnergy());
            yearLoadFeatureDTO.setMaxLoadAccuracy(accuracy);
            yearLoadFeatureDTO.setMinLoadAccuracy(accuracy1);
            yearLoadFeatureDTO.setEnergyAccuracy(accuracy2);
            result.add(yearLoadFeatureDTO);
        }
    }

    @Override
    public List<YearWeatherFeatureDTO> getYearWeatherComparison(String cityId, String year, String startStr,
        String endStr) {
        List<YearWeatherFeatureDTO> result = new ArrayList<>();
        try {
            List<String> targetYearList = DateUtil.getTargetYearList(startStr, endStr);
            for (String s : targetYearList) {
                String sYear = s.split("-")[0];
                String sMonth = s.split("-")[1];
                YearWeatherFeatureDTO yearWeatherFeatureDTO = new YearWeatherFeatureDTO();
                //String month = value.getType();
                Date startDate = DateUtil.getFirstDayDateOfMonth(DateUtil.getDate(sYear + sMonth, "yyyyMM"));
                Date endDate = DateUtil.getLastDayOfMonth(DateUtil.getDate(sYear + sMonth, "yyyyMM"));
                yearWeatherFeatureDTO.setMonth(s);
                List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisDOS = weatherFeatureCityDayHisService.listWeatherFeatureCityDayHisDO(
                    cityId, startDate, endDate);
                List<WeatherFeatureCityDayFcDO> weatherFeatureCityDayFcDOList = weatherFeatureCityDayFcService.getWeatherFeatureCityDayFcDOList(
                    cityId, startDate, endDate);
                if (CollectionUtils.isNotEmpty(weatherFeatureCityDayHisDOS)) {
                    yearWeatherFeatureDTO.setHisHighTem(
                        weatherFeatureCityDayHisDOS.stream().filter(t -> t.getHighestTemperature() != null)
                            .max(Comparator.comparing(WeatherFeatureCityDayHisDO::getHighestTemperature)).get()
                            .getHighestTemperature());
                    yearWeatherFeatureDTO.setHisLowestTem(
                        weatherFeatureCityDayHisDOS.stream().filter(t -> t.getLowestTemperature() != null)
                            .min(Comparator.comparing(WeatherFeatureCityDayHisDO::getHighestTemperature)).get()
                            .getHighestTemperature());
                    if (yearWeatherFeatureDTO.getHisHighTem() != null
                        && yearWeatherFeatureDTO.getHisLowestTem() != null) {
                        yearWeatherFeatureDTO.setHisAveTem(BigDecimalUtils.avg(yearWeatherFeatureDTO.getHisHighTem(),
                            yearWeatherFeatureDTO.getHisLowestTem(), 2, true));
                    }
                }
                if (CollectionUtils.isNotEmpty(weatherFeatureCityDayFcDOList)) {
                    yearWeatherFeatureDTO.setFcHighTem(
                        weatherFeatureCityDayFcDOList.stream().filter(t -> t.getHighestTemperature() != null)
                            .max(Comparator.comparing(WeatherFeatureCityDayFcDO::getHighestTemperature)).get()
                            .getHighestTemperature());
                    yearWeatherFeatureDTO.setFcLowestTem(
                        weatherFeatureCityDayFcDOList.stream().filter(t -> t.getLowestTemperature() != null)
                            .min(Comparator.comparing(WeatherFeatureCityDayFcDO::getHighestTemperature)).get()
                            .getHighestTemperature());
                    if (yearWeatherFeatureDTO.getFcHighTem() != null
                        && yearWeatherFeatureDTO.getFcLowestTem() != null) {
                        yearWeatherFeatureDTO.setFcAveTem(BigDecimalUtils.avg(yearWeatherFeatureDTO.getFcHighTem(),
                            yearWeatherFeatureDTO.getFcLowestTem(), 2, true));
                    }
                }
                result.add(yearWeatherFeatureDTO);
            }
            for (MonthTypeEnum value : MonthTypeEnum.values()) {
            }
            return result;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<MonthWeatherResultDTO> getWeatherSetting(MonthWeatherSettingDTO monthWeatherSettingDTO)
        throws Exception {
        List<MonthWeatherResultDTO> result = new ArrayList<>();
        String cityId = monthWeatherSettingDTO.getCityId();
        Integer type = monthWeatherSettingDTO.getType();
        List<String> hisCity = monthWeatherSettingDTO.getHisYearDate();
        String startDate1 = monthWeatherSettingDTO.getStartDate();
        String endDate1 = monthWeatherSettingDTO.getEndDate();
        List<WeatherFeatureCityYearHisDO> res = new ArrayList<>();
        List<WeatherFeatureCityMonthHisDO> weatherFeatureCityMonthHisStat = weatherFeatureCityMonthHisService.findWeatherFeatureCityMonthHisStats(
            cityId, hisCity.get(0), hisCity.get(hisCity.size() - 1));
        Map<String, List<WeatherFeatureCityMonthHisDO>> collect3 = weatherFeatureCityMonthHisStat.stream()
            .collect(Collectors.groupingBy(WeatherFeatureCityMonthHisDO::getMonth));
        List<String> targetMonthList = new ArrayList<>();
        List<String> targetYearList = DateUtil.getTargetYearList(startDate1, endDate1);
        for (String s : targetYearList) {
            targetMonthList.add(s.split("-")[1]);
        }
        for (String s : targetYearList) {
            List<WeatherFeatureCityYearHisDO> weatherFeatureCityTenDaysDTO = weatherFeatureCityYearHisService.findWeatherFeatureCityTenDaysDTO(
                cityId, s.split("-")[0], s.split("-")[1]);
            res.addAll(weatherFeatureCityYearHisService.findWeatherFeatureCityTenDaysDTO(
                cityId, s.split("-")[0], s.split("-")[1]));
        }
        Map<String, List<WeatherFeatureCityYearHisDO>> collect = res.stream()
            .collect(Collectors.groupingBy(t -> t.getYear() + "-" + t.getMonth()));
        if (type == 0) {
            for (String s2 : targetYearList) {
                String s = s2.split("-")[1];
                MonthWeatherResultDTO monthWeatherResultDTO = new MonthWeatherResultDTO();
                monthWeatherResultDTO.setDateTime(s2);
                for (String s1 : collect3.keySet()) {
                    if (s.equals(s1)) {
                        List<WeatherFeatureCityMonthHisDO> weatherFeatureCityMonthHisDOS = collect3.get(s1);
                        // 最高温度取平均
                        List<BigDecimal> maxList = weatherFeatureCityMonthHisDOS.stream()
                            .map(WeatherFeatureCityMonthHisDO::getHighestTemperature).collect(Collectors.toList());
                        // 最低温度取平均
                        List<BigDecimal> minList = weatherFeatureCityMonthHisDOS.stream()
                            .map(WeatherFeatureCityMonthHisDO::getLowestTemperature).collect(Collectors.toList());
                        //平均温度取平均
                        List<BigDecimal> aveList = weatherFeatureCityMonthHisDOS.stream()
                            .map(WeatherFeatureCityMonthHisDO::getAveTemperature).collect(Collectors.toList());
                        // 极端最高温度取平均
                        BigDecimal maxList1 =
                            com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(maxList)
                                .compareTo(Constants.normalTem) > 0
                                ? com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(
                                maxList).add(Constants.extremeTem)
                                : com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(maxList)
                                    .subtract(Constants.extremeTem);
                        // 极端最低温度取平均
                        BigDecimal minList1 =
                            com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(maxList)
                                .compareTo(Constants.normalTem) > 0
                                ? com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(
                                minList).add(Constants.extremeTem)
                                : com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(minList)
                                    .subtract(Constants.extremeTem);
                        // 极端平均温度取平均
                        BigDecimal aveList1 =
                            com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(maxList)
                                .compareTo(Constants.normalTem) > 0
                                ? com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(
                                aveList).add(Constants.extremeTem)
                                : com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(aveList)
                                    .subtract(Constants.extremeTem);
                        monthWeatherResultDTO.setMaxTem(
                            com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(maxList));
                        monthWeatherResultDTO.setMinTem(
                            com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(minList));
                        monthWeatherResultDTO.setAveTem(
                            com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(aveList));
                        monthWeatherResultDTO.setHisMaxTem(
                            com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(maxList));
                        monthWeatherResultDTO.setHisMinTem(
                            com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(minList));
                        monthWeatherResultDTO.setHisAveTem(
                            com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(aveList));
                        monthWeatherResultDTO.setExtremeMaxTem(maxList1);
                        monthWeatherResultDTO.setExtremeMinTem(minList1);
                        monthWeatherResultDTO.setExtremeAveTem(aveList1);
                        result.add(monthWeatherResultDTO);
                    }
                }
            }
        } else {
            for (String s2 : targetYearList) {
                String s = s2.split("-")[1];
                // 取气象年度特性表中数据
                MonthWeatherResultDTO monthWeatherResultDTO = new MonthWeatherResultDTO();
                monthWeatherResultDTO.setDateTime(s2);
                for (String s1 : collect.keySet()) {
                    if (s2.equals(s1)) {
                        List<WeatherFeatureCityYearHisDO> weatherFeatureCityYearHisDOS = collect.get(s1);
                        WeatherFeatureCityYearHisDO weatherFeatureCityYearHisDO = weatherFeatureCityYearHisDOS.get(0);
                        monthWeatherResultDTO.setMaxTem(weatherFeatureCityYearHisDO.getMaxTem());
                        monthWeatherResultDTO.setMinTem(weatherFeatureCityYearHisDO.getMinTem());
                        monthWeatherResultDTO.setAveTem(weatherFeatureCityYearHisDO.getAveTem());
                        monthWeatherResultDTO.setExtremeMaxTem(weatherFeatureCityYearHisDO.getExtremeMaxTem());
                        monthWeatherResultDTO.setExtremeMinTem(weatherFeatureCityYearHisDO.getExtremeMinTem());
                        monthWeatherResultDTO.setExtremeAveTem(weatherFeatureCityYearHisDO.getExtremeAveTem());
                    }
                }
                for (String s1 : collect3.keySet()) {
                    if (s.equals(s1)) {
                        List<WeatherFeatureCityMonthHisDO> weatherFeatureCityMonthHisDOS = collect3.get(s1);
                        // 最高温度取平均
                        List<BigDecimal> maxList = weatherFeatureCityMonthHisDOS.stream()
                            .map(WeatherFeatureCityMonthHisDO::getHighestTemperature).collect(Collectors.toList());
                        // 最低温度取平均
                        List<BigDecimal> minList = weatherFeatureCityMonthHisDOS.stream()
                            .map(WeatherFeatureCityMonthHisDO::getLowestTemperature).collect(Collectors.toList());
                        //平均温度取平均
                        List<BigDecimal> aveList = weatherFeatureCityMonthHisDOS.stream()
                            .map(WeatherFeatureCityMonthHisDO::getAveTemperature).collect(Collectors.toList());
                        monthWeatherResultDTO.setHisMaxTem(
                            com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(maxList));
                        monthWeatherResultDTO.setHisMinTem(
                            com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(minList));
                        monthWeatherResultDTO.setHisAveTem(
                            com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(aveList));
                        //result.add(monthWeatherResultDTO);
                    }
                }
                result.add(monthWeatherResultDTO);
            }
        }
        return result;
    }

    @Override
    public void saveWeatherSetting(List<MonthWeatherResultDTO> monthWeatherResultDTOS) throws Exception {
        String vaule = "";
        if (CollectionUtils.isNotEmpty(monthWeatherResultDTOS)) {
            List<String> hisYearDate = monthWeatherResultDTOS.get(0).getHisYearDate();
            String season = monthWeatherResultDTOS.get(0).getSeason();
            for (String s : hisYearDate) {
                vaule = vaule + s + ",";
            }
            String substring = vaule.substring(0, vaule.length() - 1);
            ReportSystemInitDO reportSystemInitDO = new ReportSystemInitDO();
            reportSystemInitDO.setField(Constants.YEAR_DEFAULT);
            reportSystemInitDO.setName("年度历年平均年份");
            reportSystemInitDO.setValue(substring);
            reportSystemInitDO.setCityId("1");
            reportSystemService.doSaveOrUpdate(reportSystemInitDO);
            for (MonthWeatherResultDTO monthWeatherResultDTO : monthWeatherResultDTOS) {
                WeatherFeatureCityYearHisDO weatherFeatureCityYearHisDO = new WeatherFeatureCityYearHisDO();
                weatherFeatureCityYearHisDO.setCityId(monthWeatherResultDTO.getCityId());
                weatherFeatureCityYearHisDO.setYear(monthWeatherResultDTO.getDateTime().split("-")[0]);
                weatherFeatureCityYearHisDO.setMonth(monthWeatherResultDTO.getDateTime().split("-")[1]);
                if (monthWeatherResultDTO.getType() == 0) {
                    weatherFeatureCityYearHisDO.setHighestTemperature(monthWeatherResultDTO.getMaxTem());
                    weatherFeatureCityYearHisDO.setLowestTemperature(monthWeatherResultDTO.getMinTem());
                    weatherFeatureCityYearHisDO.setAveTemperature(monthWeatherResultDTO.getAveTem());
                    weatherFeatureCityYearHisDO.setExtremeHighestTemperature(monthWeatherResultDTO.getExtremeMaxTem());
                    weatherFeatureCityYearHisDO.setExtremeLowestTemperature(monthWeatherResultDTO.getExtremeMinTem());
                    weatherFeatureCityYearHisDO.setExtremeAveTemperature(monthWeatherResultDTO.getExtremeAveTem());
                    weatherFeatureCityYearHisDO.setFlag(false);
                } else {
                    weatherFeatureCityYearHisDO.setAveTem(monthWeatherResultDTO.getAveTem());
                    weatherFeatureCityYearHisDO.setMaxTem(monthWeatherResultDTO.getMaxTem());
                    weatherFeatureCityYearHisDO.setMinTem(monthWeatherResultDTO.getMinTem());
                    weatherFeatureCityYearHisDO.setExtremeAveTem(monthWeatherResultDTO.getExtremeAveTem());
                    weatherFeatureCityYearHisDO.setExtremeMaxTem(monthWeatherResultDTO.getExtremeMaxTem());
                    weatherFeatureCityYearHisDO.setExtremeMinTem(monthWeatherResultDTO.getExtremeMinTem());
                    weatherFeatureCityYearHisDO.setFlag(true);
                }
                weatherFeatureCityYearHisService.doSaveOrUpdate(weatherFeatureCityYearHisDO);
            }
        }
    }

}
