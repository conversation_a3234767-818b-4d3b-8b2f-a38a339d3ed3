package com.tsintergy.lf.serviceimpl.weather.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsieframework.util.compatible.StringUtils;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityYearHisService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityYearHisDO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherFeatureCityYearHisDAO;
import java.sql.Timestamp;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2023-06-16
 * @since 1.0.0
 */
@Service("weatherFeatureCityYearHisService")
public class WeatherFeatureCityYearHisServiceImpl extends BaseServiceImpl implements
    WeatherFeatureCityYearHisService {

    @Autowired
    private WeatherFeatureCityYearHisDAO weatherFeatureCityYearHisDAO;

    @Override
    public List<WeatherFeatureCityYearHisDO> findWeatherFeatureCityTenDaysDTO(String cityId, String year,
        List<String> month) {
        return weatherFeatureCityYearHisDAO.findAll(JpaWrappers.<WeatherFeatureCityYearHisDO>lambdaQuery()
            .eq(!StringUtils.isEmpty(cityId), WeatherFeatureCityYearHisDO::getCityId, cityId)
            .eq(!StringUtils.isEmpty(year), WeatherFeatureCityYearHisDO::getYear, year)
            .in(!CollectionUtils.isEmpty(month), WeatherFeatureCityYearHisDO::getMonth, month)
        );
    }

    @Override
    public List<WeatherFeatureCityYearHisDO> findWeatherFeatureCityTenDaysDTO(String cityId, String year,
        String month) {
        return weatherFeatureCityYearHisDAO.findAll(JpaWrappers.<WeatherFeatureCityYearHisDO>lambdaQuery()
            .eq(!StringUtils.isEmpty(cityId), WeatherFeatureCityYearHisDO::getCityId, cityId)
            .eq(!StringUtils.isEmpty(year), WeatherFeatureCityYearHisDO::getYear, year)
            .eq(!StringUtils.isEmpty(month), WeatherFeatureCityYearHisDO::getMonth, month)
        );
    }

    @Override
    public void doSaveOrUpdate(WeatherFeatureCityYearHisDO weatherFeatureCityYearHisDO) {
        List<WeatherFeatureCityYearHisDO> all = weatherFeatureCityYearHisDAO.findAll(
            JpaWrappers.<WeatherFeatureCityYearHisDO>lambdaQuery()
                .eq(!StringUtils.isEmpty(weatherFeatureCityYearHisDO.getCityId()),
                    WeatherFeatureCityYearHisDO::getCityId, weatherFeatureCityYearHisDO.getCityId())
                .eq(!StringUtils.isEmpty(weatherFeatureCityYearHisDO.getYear()), WeatherFeatureCityYearHisDO::getYear,
                    weatherFeatureCityYearHisDO.getYear())
                .eq(!StringUtils.isEmpty(weatherFeatureCityYearHisDO.getMonth()), WeatherFeatureCityYearHisDO::getMonth,
                    weatherFeatureCityYearHisDO.getMonth())
        );
        if (CollectionUtils.isEmpty(all)) {
            weatherFeatureCityYearHisDAO.save(weatherFeatureCityYearHisDO);
        } else {
            weatherFeatureCityYearHisDO.setId(all.get(0).getId());
            if (weatherFeatureCityYearHisDO.getHighestTemperature() == null) {
                weatherFeatureCityYearHisDO.setHighestTemperature(all.get(0).getHighestTemperature());
            }
            if (weatherFeatureCityYearHisDO.getLowestTemperature() == null) {
                weatherFeatureCityYearHisDO.setLowestTemperature(all.get(0).getLowestTemperature());
            }
            if (weatherFeatureCityYearHisDO.getAveTemperature() == null) {
                weatherFeatureCityYearHisDO.setAveTemperature(all.get(0).getAveTemperature());
            }
            if (weatherFeatureCityYearHisDO.getExtremeHighestTemperature() == null) {
                weatherFeatureCityYearHisDO.setExtremeHighestTemperature(all.get(0).getExtremeHighestTemperature());
            }
            if (weatherFeatureCityYearHisDO.getExtremeLowestTemperature() == null) {
                weatherFeatureCityYearHisDO.setExtremeLowestTemperature(all.get(0).getExtremeLowestTemperature());
            }
            if (weatherFeatureCityYearHisDO.getExtremeAveTemperature() == null) {
                weatherFeatureCityYearHisDO.setExtremeAveTemperature(all.get(0).getExtremeAveTemperature());
            }
            if (weatherFeatureCityYearHisDO.getMaxTem() == null) {
                weatherFeatureCityYearHisDO.setMaxTem(all.get(0).getMaxTem());
            }
            if (weatherFeatureCityYearHisDO.getMinTem() == null) {
                weatherFeatureCityYearHisDO.setMinTem(all.get(0).getMinTem());
            }
            if (weatherFeatureCityYearHisDO.getAveTem() == null) {
                weatherFeatureCityYearHisDO.setAveTem(all.get(0).getAveTem());
            }
            if (weatherFeatureCityYearHisDO.getExtremeMaxTem() == null) {
                weatherFeatureCityYearHisDO.setExtremeMaxTem(all.get(0).getExtremeMaxTem());
            }
            if (weatherFeatureCityYearHisDO.getExtremeMinTem() == null) {
                weatherFeatureCityYearHisDO.setExtremeMinTem(all.get(0).getExtremeMinTem());
            }
            if (weatherFeatureCityYearHisDO.getExtremeAveTem() == null) {
                weatherFeatureCityYearHisDO.setExtremeAveTem(all.get(0).getExtremeAveTem());
            }
            weatherFeatureCityYearHisDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            weatherFeatureCityYearHisDAO.saveOrUpdateByTemplate(weatherFeatureCityYearHisDO);
        }
    }
}
