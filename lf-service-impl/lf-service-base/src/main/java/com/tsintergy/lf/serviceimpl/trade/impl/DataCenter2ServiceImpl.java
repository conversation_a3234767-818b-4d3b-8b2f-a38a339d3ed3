/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.trade.impl;

import com.tsintergy.lf.serviceapi.base.trade.api.DataCenter2Service;
import com.tsintergy.lf.serviceapi.base.trade.pojo.DataCenter2;
import com.tsintergy.lf.serviceimpl.trade.dao.DataCenter2DAO;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/8/29 15:19
 * @Version: 1.0.0
 */
@Service("dataCenter2Service")
public class DataCenter2ServiceImpl implements DataCenter2Service {

    @Autowired
    DataCenter2DAO dataCenter2DAO;
    @Override
    public void save(List<DataCenter2> list) {
        dataCenter2DAO.saveOrUpdateBatch(list);
    }
}