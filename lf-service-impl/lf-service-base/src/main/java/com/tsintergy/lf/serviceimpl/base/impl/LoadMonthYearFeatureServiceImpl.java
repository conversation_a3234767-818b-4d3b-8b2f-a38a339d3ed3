package com.tsintergy.lf.serviceimpl.base.impl;

import com.tsieframework.core.base.math.BigDecimalFunctions;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.base.api.LoadMonthYearFeatureService;
import com.tsintergy.lf.serviceapi.base.base.dto.LoadMonthFeatureDTO;
import com.tsintergy.lf.serviceapi.base.bgd.api.LoadFeatureCityTenDaysHisService;
import com.tsintergy.lf.serviceapi.base.bgd.pojo.LoadFeatureCityTenDaysHisServiceDO;
import com.tsintergy.lf.serviceapi.base.load.api.*;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadFeatureFcDTO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityDayHisDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityMonthHisDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityQuarterHisDO;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityQuarterHisService;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2023-10-23
 * @since 1.0.0
 */
@Service("loadMonthYearFeatureService")
public class LoadMonthYearFeatureServiceImpl implements LoadMonthYearFeatureService {

    @Autowired
    LoadCityHisService loadCityHisService;

    @Autowired
    LoadFeatureCityDayHisService loadFeatureCityDayHisService;

    @Autowired
    LoadFeatureCityMonthHisService loadFeatureCityMonthHisService;

    @Autowired
    LoadFeatureCityTenDaysHisService loadFeatureCityTenDaysHisService;
    @Autowired
    LoadFeatureStatService loadFeatureStatService;
    @Autowired
    private LoadFeatureCityQuarterHisService loadFeatureCityQuarterHisService;
    @Autowired
    private WeatherFeatureCityQuarterHisService weatherFeatureCityQuarterHisService;

    public static <T> Function<T, String> groupByQuarter(Function<T, Date> dateExtractor) {
        return obj -> {
            Date date = dateExtractor.apply(obj);
            java.util.Calendar calendar = java.util.Calendar.getInstance();
            calendar.setTime(date);
            int year = calendar.get(java.util.Calendar.YEAR);
            int month = calendar.get(java.util.Calendar.MONTH) + 1;
            int quarter;
            if (month <= 3) {
                quarter = 1;
            } else if (month <= 6) {
                quarter = 2;
            } else if (month <= 9) {
                quarter = 3;
            } else {
                quarter = 4;
            }
            return year + "-" + quarter;
        };
    }

    @Override
    public List<LoadMonthFeatureDTO> getHisMonthFeature(String startDate, String endDate, String cityId,
                                                        String caliberId) throws Exception {
        List<LoadMonthFeatureDTO> result = new ArrayList<>();
        List<String> targetYearList = DateUtil.getTargetYearList(startDate, endDate);
        Date date = DateUtil.getDate(startDate, "yyyy-MM");
        Date date1 = DateUtil.getDate(endDate, "yyyy-MM");
        Date first = DateUtil.getFirstDayOfMonth(date);
        Date last = DateUtil.getLastDayOfMonth(date1);
        Map<String, List<LoadFeatureCityMonthHisDO>> collect = new HashMap<>();
        Map<String, List<LoadFeatureCityDayHisDO>> collect1 = new HashMap<>();
        Map<String, List<LoadCityHisDO>> collect2 = new HashMap<>();
        List<LoadFeatureCityMonthHisDO> loadFeatureCityMonthHisDOS = loadFeatureCityMonthHisService.getLoadFeatureCityMonthHisDOS(
                cityId, startDate, endDate, caliberId);
        List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisVOS = loadFeatureCityDayHisService.findLoadFeatureCityDayHisVOS(
                cityId, first, last, caliberId);
        List<LoadCityHisDO> loadCityHisDOS = loadCityHisService.getLoadCityHisDOS(cityId, caliberId, first, last);
        if (!CollectionUtils.isEmpty(loadFeatureCityMonthHisDOS)) {
            collect = loadFeatureCityMonthHisDOS.stream()
                    .collect(Collectors.groupingBy(dp -> dp.getYear() + "-" + Integer.valueOf(dp.getMonth())));
        }
        if (!CollectionUtils.isEmpty(loadFeatureCityDayHisVOS)) {
            collect1 = loadFeatureCityDayHisVOS.stream()
                    .collect(Collectors.groupingBy(dp -> DateUtil.getDateToStrFORMAT(dp.getDate(), "yyyy-MM")));
        }
        if (!CollectionUtils.isEmpty(loadCityHisDOS)) {
            collect2 = loadCityHisDOS.stream()
                    .collect(Collectors.groupingBy(dp -> DateUtil.getDateToStrFORMAT(dp.getDate(), "yyyy-MM")));
        }
        for (String yearMonth : targetYearList) {
            String year = yearMonth.split("-")[0];
            String month = yearMonth.split("-")[1];
            String key = year + "-" + Integer.valueOf(month);
            String key1 = year + "-" + month;
            List<LoadFeatureCityMonthHisDO> loadFeatureMonth = collect.get(key);
            List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisDOS = collect1.get(key1);
            List<LoadCityHisDO> loadCityHisDOS1 = collect2.get(key1);
            if (!CollectionUtils.isEmpty(loadFeatureMonth) || !CollectionUtils.isEmpty(loadFeatureCityDayHisDOS)) {
                LoadMonthFeatureDTO loadMonthFeatureDTO = new LoadMonthFeatureDTO();
                loadMonthFeatureDTO.setDateStr(year + "年" + Integer.valueOf(month) + "月");
                if (!CollectionUtils.isEmpty(loadFeatureMonth)) {
                    LoadFeatureCityMonthHisDO loadFeatureCityMonthHisDO = loadFeatureMonth.get(0);
                    loadMonthFeatureDTO.setMaxLoad(loadFeatureCityMonthHisDO.getMaxLoad());
                    //loadMonthFeatureDTO.setMaxLoadTime(loadFeatureCityMonthHisDO.getMaxTime());
                    loadMonthFeatureDTO.setMinLoad(loadFeatureCityMonthHisDO.getMinLoad());
                    //loadMonthFeatureDTO.setMinLoadTime(loadFeatureCityMonthHisDO.getMinTime());
                    loadMonthFeatureDTO.setNoontimeMinLoad(loadFeatureCityMonthHisDO.getNoontimeLoad());
                    loadMonthFeatureDTO.setAveLoad(loadFeatureCityMonthHisDO.getAveLoad());
                    loadMonthFeatureDTO.setEnergy(loadFeatureCityMonthHisDO.getEnergy());
                }
                calcLoadMonthFeatureDTO(loadCityHisDOS1, loadMonthFeatureDTO, loadFeatureCityDayHisDOS);
                result.add(loadMonthFeatureDTO);
            }
        }
        return result;
    }

    @Override
    public List<LoadMonthFeatureDTO> getHisTenDaysFeature(String startDate, String endDate, String cityId, String caliberId) throws Exception {
        List<LoadMonthFeatureDTO> result = new ArrayList<>();
        List<String> targetTenDaysList = DateUtil.getTargetTenDaysList(startDate, endDate);
        Date date = DateUtil.getDate(startDate, "yyyy-MM");
        Date date1 = DateUtil.getDate(endDate, "yyyy-MM");
        Date first = DateUtil.getFirstDayOfMonth(date);
        Date last = DateUtil.getLastDayOfMonth(date1);
        Map<String, List<LoadFeatureCityTenDaysHisServiceDO>> collect = new HashMap<>();
        Map<String, List<LoadFeatureCityDayHisDO>> collect1 = new HashMap<>();
        Map<String, List<LoadCityHisDO>> collect2 = new HashMap<>();
        List<LoadFeatureCityTenDaysHisServiceDO> loadFeatureCityTenDaysHisDOS = loadFeatureCityTenDaysHisService.findByRangeCondition(
                cityId, caliberId, startDate, endDate);
        List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisVOS = loadFeatureCityDayHisService.findLoadFeatureCityDayHisVOS(
                cityId, first, last, caliberId);
        List<LoadCityHisDO> loadCityHisDOS = loadCityHisService.getLoadCityHisDOS(cityId, caliberId, first, last);
        if (!CollectionUtils.isEmpty(loadFeatureCityTenDaysHisDOS)) {
            collect = loadFeatureCityTenDaysHisDOS.stream()
                    .collect(Collectors.groupingBy(dp -> dp.getYear() + "-" + dp.getMonth() + "-" + dp.getType()));
        }
        if (!CollectionUtils.isEmpty(loadFeatureCityDayHisVOS)) {
            collect1 = loadFeatureCityDayHisVOS.stream()
                    .collect(Collectors.groupingBy(groupByTenDays(LoadFeatureCityDayHisDO::getDate)));
        }
        if (!CollectionUtils.isEmpty(loadCityHisDOS)) {
            collect2 = loadCityHisDOS.stream()
                    .collect(Collectors.groupingBy(groupByTenDays(LoadCityHisDO::getDate)));
        }
        for (String tenDays : targetTenDaysList) {
            String[] tenDaysSplit = tenDays.split("-");
            String year = tenDaysSplit[0];
            String month = tenDaysSplit[1];
            String type = tenDaysSplit[2];
            String key = year + "-" + month + "-" + type;
            String key1 = year + "-" + month + "-" + type;
            List<LoadFeatureCityTenDaysHisServiceDO> loadFeatureTenDays = collect.get(key);
            List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisDOS = collect1.get(key1);
            List<LoadCityHisDO> loadCityHisDOS1 = collect2.get(key1);
            if (!CollectionUtils.isEmpty(loadFeatureTenDays) || !CollectionUtils.isEmpty(loadFeatureCityDayHisDOS)) {
                LoadMonthFeatureDTO loadMonthFeatureDTO = new LoadMonthFeatureDTO();
                loadMonthFeatureDTO.setDateStr(year + "年" + Integer.valueOf(month) + "月" + type);
                if (!CollectionUtils.isEmpty(loadFeatureTenDays)) {
                    LoadFeatureCityTenDaysHisServiceDO tenDaysHisServiceDO = loadFeatureTenDays.get(0);
                    loadMonthFeatureDTO.setMaxLoad(tenDaysHisServiceDO.getMaxLoad());
                    loadMonthFeatureDTO.setMaxLoadTime(tenDaysHisServiceDO.getMaxTime());
                    loadMonthFeatureDTO.setMinLoad(tenDaysHisServiceDO.getMinLoad());
                    loadMonthFeatureDTO.setMinLoadTime(tenDaysHisServiceDO.getMinTime());
                    //loadMonthFeatureDTO.setNoontimeMinLoad(tenDaysHisServiceDO.getNoontimeLoad());
                    loadMonthFeatureDTO.setAveLoad(tenDaysHisServiceDO.getAveLoad());
                    loadMonthFeatureDTO.setEnergy(tenDaysHisServiceDO.getEnergy());
                }
                calcLoadMonthFeatureDTO(loadCityHisDOS1, loadMonthFeatureDTO, loadFeatureCityDayHisDOS);
                result.add(loadMonthFeatureDTO);
            }
        }
        return result;
    }

    @Override
    public List<LoadMonthFeatureDTO> getHisQuartersFeature(String startYQ, String endYQ, String cityId, String caliberId) throws Exception {
        List<LoadMonthFeatureDTO> result = new ArrayList<>();
        List<String> targetQuarterList = DateUtil.getTargetQuarterList(startYQ, endYQ);
        Date startDate = DateUtil.getFirstDayDateOfOfQuarter(Integer.parseInt(startYQ.substring(0, 4)), Integer.parseInt(startYQ.substring(5)));
        Date endDate = DateUtil.getFirstDayDateOfOfQuarter(Integer.parseInt(endYQ.substring(0, 4)), Integer.parseInt(endYQ.substring(5)));
        Map<String, List<LoadFeatureCityQuarterHisDO>> collect = new HashMap<>();
        Map<String, List<LoadFeatureCityDayHisDO>> collect1 = new HashMap<>();
        Map<String, List<LoadCityHisDO>> collect2 = new HashMap<>();
        List<LoadFeatureCityQuarterHisDO> quarterHisLoadDOS =
                loadFeatureCityQuarterHisService.getLoadFeatureCityQuarterHisVOS(cityId, startYQ, endYQ, caliberId);
        List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisVOS = loadFeatureCityDayHisService.findLoadFeatureCityDayHisVOS(
                cityId, startDate, endDate, caliberId);
        List<LoadCityHisDO> loadCityHisDOS = loadCityHisService.getLoadCityHisDOS(cityId, caliberId, startDate, endDate);
        if (!CollectionUtils.isEmpty(quarterHisLoadDOS)) {
            collect = quarterHisLoadDOS.stream()
                    .collect(Collectors.groupingBy(dp -> dp.getYear() + "-" + dp.getQuarter()));
        }
        if (!CollectionUtils.isEmpty(loadFeatureCityDayHisVOS)) {
            collect1 = loadFeatureCityDayHisVOS.stream()
                    .collect(Collectors.groupingBy(groupByQuarter(LoadFeatureCityDayHisDO::getDate)));
        }
        if (!CollectionUtils.isEmpty(loadCityHisDOS)) {
            collect2 = loadCityHisDOS.stream()
                    .collect(Collectors.groupingBy(groupByQuarter(LoadCityHisDO::getDate)));
        }
        for (String quarterStr : targetQuarterList) {
            String[] tenDaysSplit = quarterStr.split("-");
            String year = tenDaysSplit[0];
            String quarter = tenDaysSplit[1];
            String key = year + "-" + quarter;
            List<LoadFeatureCityQuarterHisDO> loadFeatureTenDays = collect.get(key);
            List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisDOS = collect1.get(key);
            List<LoadCityHisDO> loadCityHisDOS1 = collect2.get(key);
            if (!CollectionUtils.isEmpty(loadFeatureTenDays) || !CollectionUtils.isEmpty(loadFeatureCityDayHisDOS)) {
                LoadMonthFeatureDTO loadMonthFeatureDTO = new LoadMonthFeatureDTO();
                loadMonthFeatureDTO.setDateStr(year + "年" + quarter + "季度");
                if (!CollectionUtils.isEmpty(loadFeatureTenDays)) {
                    LoadFeatureCityQuarterHisDO quarterHisDO = loadFeatureTenDays.get(0);
                    loadMonthFeatureDTO.setMaxLoad(quarterHisDO.getMaxLoad());
                    loadMonthFeatureDTO.setMaxLoadTime(quarterHisDO.getMaxTime());
                    loadMonthFeatureDTO.setMinLoad(quarterHisDO.getMinLoad());
                    loadMonthFeatureDTO.setMinLoadTime(quarterHisDO.getMinTime());
                    //loadMonthFeatureDTO.setNoontimeMinLoad(quarterHisDO.getNoontimeLoad());
                    loadMonthFeatureDTO.setAveLoad(quarterHisDO.getAveLoad());
                    loadMonthFeatureDTO.setEnergy(quarterHisDO.getEnergy());
                }
                calcLoadMonthFeatureDTO(loadCityHisDOS1, loadMonthFeatureDTO, loadFeatureCityDayHisDOS);
                result.add(loadMonthFeatureDTO);
            }
        }
        return result;
    }

    public <T> Function<T, String> groupByTenDays(Function<T, Date> dateExtractor) {
        return obj -> {
            Date date = dateExtractor.apply(obj);
            int day = date.getDate();
            java.util.Calendar calendar = java.util.Calendar.getInstance();
            calendar.setTime(date);
            int year = calendar.get(java.util.Calendar.YEAR);
            int month = calendar.get(java.util.Calendar.MONTH) + 1;
            String monthStr;
            if (month < 10) {
                monthStr = "0" + month;
            } else {
                monthStr = String.valueOf(month);
            }
            if (day <= 10) {
                return year + "-" + monthStr + "-上旬";
            } else if (day <= 20) {
                return year + "-" + monthStr + "-中旬";
            } else {
                return year + "-" + monthStr + "-下旬";
            }
        };
    }

    /**
     * 实时计算未落库的特性指标
     *
     * @param loadCityHisDOS
     * @param loadMonthFeatureDTO
     * @param loadFeatureCityDayHisDOS
     * @return
     */
    @SneakyThrows
    private void calcLoadMonthFeatureDTO(List<LoadCityHisDO> loadCityHisDOS,
                                         LoadMonthFeatureDTO loadMonthFeatureDTO,
                                         List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisDOS) {
        List<BigDecimal> bigDecimals = new ArrayList<>();
        List<BigDecimal> bigDecimals1 = new ArrayList<>();
        List<BigDecimal> bigDecimals2 = new ArrayList<>();
        List<BigDecimal> bigDecimals3 = new ArrayList<>();
        List<BigDecimal> bigDecimals4 = new ArrayList<>();
        List<BigDecimal> bigDecimals5 = new ArrayList<>();
        List<BigDecimal> bigDecimals6 = new ArrayList<>();
        if (!CollectionUtils.isEmpty(loadCityHisDOS)) {
            List<BigDecimal> bigDecimals7 = new ArrayList<>();
            List<BigDecimal> bigDecimals8 = new ArrayList<>();
            LoadFeatureFcDTO loadFeatureFcDTO = null;
            LoadFeatureFcDTO loadFeatureFcDTO1 = null;
            LoadFeatureFcDTO loadFeatureFcDTO2 = null;
            LoadFeatureFcDTO loadFeatureFcDTO3 = null;
            LoadFeatureFcDTO loadFeatureFcDTO4 = null;
            LoadFeatureFcDTO loadFeatureFcDTO5 = null;
            LoadFeatureFcDTO loadFeatureFcDTO6 = null;
            LoadFeatureFcDTO loadFeatureFcDTO7 = null;
            LoadFeatureFcDTO loadFeatureFcDTO8 = null;
            LoadFeatureFcDTO loadFeatureFcDTO9 = null;
            for (LoadCityHisDO loadCityHisDO : loadCityHisDOS) {
                LoadFeatureFcDTO statisticsDayFeature = loadFeatureStatService.findStatisticsDayFeature(loadCityHisDO);
                bigDecimals7.add(statisticsDayFeature.getNoontimeLoad());
                bigDecimals8.add(statisticsDayFeature.getMaxNoontimeLoad());
                if (loadFeatureFcDTO == null) {
                    loadFeatureFcDTO = statisticsDayFeature;
                    loadFeatureFcDTO1 = statisticsDayFeature;
                    loadFeatureFcDTO2 = statisticsDayFeature;
                    loadFeatureFcDTO3 = statisticsDayFeature;
                    loadFeatureFcDTO4 = statisticsDayFeature;
                    loadFeatureFcDTO5 = statisticsDayFeature;
                    loadFeatureFcDTO6 = statisticsDayFeature;
                    loadFeatureFcDTO7 = statisticsDayFeature;
                    loadFeatureFcDTO8 = statisticsDayFeature;
                    loadFeatureFcDTO9 = statisticsDayFeature;
                }
                if (statisticsDayFeature.getMorningLoad() != null && statisticsDayFeature.getMorningLoad().compareTo(loadFeatureFcDTO.getMorningLoad()) > 0) {
                    loadFeatureFcDTO = statisticsDayFeature;
                }
                if (statisticsDayFeature.getNoontimeLoad() != null && statisticsDayFeature.getNoontimeLoad().compareTo(loadFeatureFcDTO1.getNoontimeLoad()) < 0) {
                    loadFeatureFcDTO1 = statisticsDayFeature;
                }
                if (statisticsDayFeature.getMaxNoontimeLoad() != null && statisticsDayFeature.getMaxNoontimeLoad().compareTo(loadFeatureFcDTO2.getMaxNoontimeLoad()) > 0) {
                    loadFeatureFcDTO2 = statisticsDayFeature;
                }
                if (statisticsDayFeature.getEveningLoad() != null && statisticsDayFeature.getEveningLoad().compareTo(loadFeatureFcDTO3.getEveningLoad()) > 0) {
                    loadFeatureFcDTO3 = statisticsDayFeature;
                }
                if (statisticsDayFeature.getDifferent() != null && statisticsDayFeature.getDifferent().compareTo(loadFeatureFcDTO4.getDifferent()) > 0) {
                    loadFeatureFcDTO4 = statisticsDayFeature;
                }
                if (statisticsDayFeature.getDifferent() != null && statisticsDayFeature.getDifferent().compareTo(loadFeatureFcDTO5.getDifferent()) < 0) {
                    loadFeatureFcDTO5 = statisticsDayFeature;
                }
                if (statisticsDayFeature.getLoadGradient() != null && statisticsDayFeature.getLoadGradient().compareTo(loadFeatureFcDTO6.getLoadGradient()) > 0) {
                    loadFeatureFcDTO6 = statisticsDayFeature;
                }
                if (statisticsDayFeature.getLoadGradient() != null && statisticsDayFeature.getLoadGradient().compareTo(loadFeatureFcDTO7.getLoadGradient()) < 0) {
                    loadFeatureFcDTO7 = statisticsDayFeature;
                }
                if (statisticsDayFeature.getMaxLoad() != null && statisticsDayFeature.getMaxLoad().compareTo(loadFeatureFcDTO8.getMaxLoad()) > 0) {
                    loadFeatureFcDTO8 = statisticsDayFeature;
                }
                if (statisticsDayFeature.getMinLoad() != null && statisticsDayFeature.getMinLoad().compareTo(loadFeatureFcDTO9.getMinLoad()) < 0) {
                    loadFeatureFcDTO9 = statisticsDayFeature;
                }
            }
            loadMonthFeatureDTO.setMorningMaxLoad(loadFeatureFcDTO.getMorningLoad());
            loadMonthFeatureDTO.setMorningLoadTime(DateUtil.getDateToStrFORMAT(loadFeatureFcDTO.getDate(), "yyyy-MM-dd") + " " + loadFeatureFcDTO.getMorningLoadTime());
            loadMonthFeatureDTO.setNoontimeMinLoad(loadFeatureFcDTO1.getNoontimeLoad());
            loadMonthFeatureDTO.setNoontimeMinLoadTime(DateUtil.getDateToStrFORMAT(loadFeatureFcDTO1.getDate(), "yyyy-MM-dd") + " " + loadFeatureFcDTO1.getNoontimeLoadTime());
            loadMonthFeatureDTO.setNoontimeMaxLoad(loadFeatureFcDTO2.getMaxNoontimeLoad());
            loadMonthFeatureDTO.setNoontimeMaxLoadTime(DateUtil.getDateToStrFORMAT(loadFeatureFcDTO2.getDate(), "yyyy-MM-dd") + " " + loadFeatureFcDTO2.getMaxNoontimeLoadTime());
            loadMonthFeatureDTO.setEveningMaxLoad(loadFeatureFcDTO3.getEveningLoad());
            loadMonthFeatureDTO.setEveningMaxLoadTime(DateUtil.getDateToStrFORMAT(loadFeatureFcDTO3.getDate(), "yyyy-MM-dd") + " " + loadFeatureFcDTO3.getEveningLoadTime());
            loadMonthFeatureDTO.setMaxDifference(loadFeatureFcDTO4.getDifferent());
            loadMonthFeatureDTO.setMaxDifferenceDay(DateUtil.getDateToStrFORMAT(loadFeatureFcDTO4.getDate(), "yyyy-MM-dd"));
            loadMonthFeatureDTO.setMinDifference(loadFeatureFcDTO5.getDifferent());
            loadMonthFeatureDTO.setMinDifferenceDay(DateUtil.getDateToStrFORMAT(loadFeatureFcDTO5.getDate(), "yyyy-MM-dd"));
            loadMonthFeatureDTO.setMaxLoadTime(DateUtil.getDateToStrFORMAT(loadFeatureFcDTO8.getDate(), "yyyy-MM-dd") + " " + loadFeatureFcDTO8.getMaxTime());
            loadMonthFeatureDTO.setMinLoadTime(DateUtil.getDateToStrFORMAT(loadFeatureFcDTO9.getDate(), "yyyy-MM-dd") + " " + loadFeatureFcDTO9.getMinTime());
            if (!CollectionUtils.isEmpty(bigDecimals7)) {
                BigDecimal bigDecimal = BigDecimalFunctions.listAvg(bigDecimals7);
                loadMonthFeatureDTO.setAveMinNoontimeLoad(bigDecimal);
            }
            if (!CollectionUtils.isEmpty(bigDecimals8)) {
                BigDecimal bigDecimal = BigDecimalFunctions.listAvg(bigDecimals8);
                loadMonthFeatureDTO.setAveMaxNoontimeLoad(bigDecimal);
            }
            loadMonthFeatureDTO.setMaxLoadRate(loadFeatureFcDTO6.getLoadGradient());
            loadMonthFeatureDTO.setMinLoadRate(loadFeatureFcDTO7.getLoadGradient());
            loadMonthFeatureDTO.setMaxLoadRateDay(DateUtil.getDateToStrFORMAT(loadFeatureFcDTO6.getDate(), "yyyy-MM-dd"));
            loadMonthFeatureDTO.setMinLoadRateDay(DateUtil.getDateToStrFORMAT(loadFeatureFcDTO7.getDate(), "yyyy-MM-dd"));
        }
        if (!CollectionUtils.isEmpty(loadFeatureCityDayHisDOS)) {
            for (LoadFeatureCityDayHisDO loadFeatureCityDayHisDO : loadFeatureCityDayHisDOS) {
                bigDecimals.add(loadFeatureCityDayHisDO.getDifferent());
                bigDecimals1.add(loadFeatureCityDayHisDO.getGradient());
                bigDecimals2.add(loadFeatureCityDayHisDO.getMaxLoad());
                bigDecimals3.add(loadFeatureCityDayHisDO.getMinLoad());
                bigDecimals4.add(loadFeatureCityDayHisDO.getMorningPeak());
                bigDecimals5.add(loadFeatureCityDayHisDO.getEveningPeak());
                bigDecimals6.add(loadFeatureCityDayHisDO.getLoadGradient());
            }
            if (!CollectionUtils.isEmpty(bigDecimals)) {
                BigDecimal bigDecimal = BigDecimalFunctions.listAvg(bigDecimals);
                loadMonthFeatureDTO.setAveDifference(bigDecimal);
            }
            if (!CollectionUtils.isEmpty(bigDecimals1)) {
                BigDecimal bigDecimal = BigDecimalFunctions.listAvg(bigDecimals1);
                loadMonthFeatureDTO.setAveDifferenceRate(bigDecimal);
            }
            if (!CollectionUtils.isEmpty(bigDecimals2)) {
                BigDecimal bigDecimal = BigDecimalFunctions.listAvg(bigDecimals2);
                loadMonthFeatureDTO.setAveMaxLoad(bigDecimal);
            }
            if (!CollectionUtils.isEmpty(bigDecimals3)) {
                BigDecimal bigDecimal = BigDecimalFunctions.listAvg(bigDecimals3);
                loadMonthFeatureDTO.setAveMinLoad(bigDecimal);
            }
            if (!CollectionUtils.isEmpty(bigDecimals4)) {
                BigDecimal bigDecimal = BigDecimalFunctions.listAvg(bigDecimals4);
                loadMonthFeatureDTO.setAveMorningMaxLoad(bigDecimal);
            }
            if (!CollectionUtils.isEmpty(bigDecimals5)) {
                BigDecimal bigDecimal = BigDecimalFunctions.listAvg(bigDecimals5);
                loadMonthFeatureDTO.setAveMaxEveningLoad(bigDecimal);
            }
            if (!CollectionUtils.isEmpty(bigDecimals6)) {
                BigDecimal bigDecimal = BigDecimalFunctions.listAvg(bigDecimals6);
                loadMonthFeatureDTO.setAveLoadRate(bigDecimal);
            }
        }
    }
}
