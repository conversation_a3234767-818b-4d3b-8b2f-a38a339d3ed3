/**
 * Copyright (C), 2015‐2022, 北京清能互联科技有限公司 Author:  <EMAIL> Date:  2022/5/26 17:20 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceimpl.load.dao;

import com.tsieframework.core.base.dao.hibernate.BaseAbstractDAO;
import com.tsieframework.core.base.dao.hibernate.BaseDAO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureAcFcDO;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * Description: 空调预测负荷特性 <br>
 *
 * <AUTHOR>
 * @create 2022/5/26
 * @since 1.0.0
 */
public class LoadFeatureAcFcDAO extends BaseAbstractDAO<LoadFeatureAcFcDO> {

    /**
     * 查询预测负荷特性
     *
     * @param cityId 城市id
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param caliberId 口径id
     * @param algorithmId 算法id
     * @return 算法特性集合
     */
    public List<LoadFeatureAcFcDO> getLoadFeatureCityDayFcDO(String cityId, Date startDate, Date endDate,
                                                             String caliberId, String algorithmId) {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (null != startDate) {
            param.getQueryConditions().put("_dnl_date", new java.sql.Date(startDate.getTime()));
        }
        if (null != endDate) {
            param.getQueryConditions().put("_dnm_date", new java.sql.Date(endDate.getTime()));
        }
        if (StringUtils.isNotBlank(cityId)) {
            param.getQueryConditions().put("_ne_cityId", cityId);
        }
        if (null != caliberId) {
            param.getQueryConditions().put("_ne_caliberId", caliberId);
        }
        if (null != algorithmId) {
            param.getQueryConditions().put("_ne_algorithmId", algorithmId);
        }
        param.setOrderby("date");
        param.setDesc("0");
        return this.query(param).getDatas();
    }
}

