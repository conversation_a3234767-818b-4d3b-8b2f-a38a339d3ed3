/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.trade.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsintergy.lf.serviceapi.base.trade.api.TradeLoadHisStatService;
import com.tsintergy.lf.serviceapi.base.trade.pojo.TradeLoadHisStatDO;
import com.tsintergy.lf.serviceimpl.trade.dao.TradeLoadHisBasicDAO;
import com.tsintergy.lf.serviceimpl.trade.dao.TradeLoadHisStatDAO;
import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/8/29 15:19
 * @Version: 1.0.0
 */

@Service("tradeLoadHisStatService")
public class TradeLoadHisStatServiceImpl implements TradeLoadHisStatService {

    @Autowired
    TradeLoadHisStatDAO tradeLoadHisStatDAO;
    @Override
    public void save(List<TradeLoadHisStatDO> list) {
        tradeLoadHisStatDAO.saveOrUpdateBatch(list);
    }

    @Override
    public List<TradeLoadHisStatDO> findTradeLoadHisBasicDO(String tradeCode, Date startDate, Date endDate) {
        return tradeLoadHisStatDAO.findAll(JpaWrappers.<TradeLoadHisStatDO>lambdaQuery()
            .eq(!StringUtils.isEmpty(tradeCode), TradeLoadHisStatDO::getTradeCode,tradeCode)
            .le(endDate!=null, TradeLoadHisStatDO::getDateTime,endDate)
            .ge(startDate!=null, TradeLoadHisStatDO::getDateTime,startDate)
        );
    }
}