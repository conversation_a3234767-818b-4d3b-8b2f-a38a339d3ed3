package com.tsintergy.lf.serviceimpl.bgd.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsintergy.lf.serviceapi.base.bgd.api.LoadFeatureCityTenDaysFcService;
import com.tsintergy.lf.serviceapi.base.bgd.pojo.LoadFeatureCityTenDaysFcServiceDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCitySeasonFcDO;
import com.tsintergy.lf.serviceimpl.bgd.dao.LoadFeatureCitySeasonFcDAO;
import com.tsintergy.lf.serviceimpl.bgd.dao.LoadFeatureCityTenDaysFcServiceDAO;
import java.sql.Timestamp;
import java.util.List;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * Description: TODO <br>
 *
 * <AUTHOR>
 * @create 2023-02-16
 * @since 1.0.0
 */
@Service("loadFeatureCityTenDaysFcService")
public class LoadFeatureCityTenDaysFcServiceImpl extends BaseServiceImpl implements LoadFeatureCityTenDaysFcService {

    @Autowired
    LoadFeatureCityTenDaysFcServiceDAO loadFeatureCityTenDaysFcServiceDAO;

    @Autowired
    LoadFeatureCitySeasonFcDAO loadFeatureCitySeasonFcDAO;

    @Override
    public List<LoadFeatureCityTenDaysFcServiceDO> findLoadFeatureCityTenDaysFcServiceDOs(String cityId,
        String caliberId, String algorithmId, String year, String month, String type) {
        return loadFeatureCityTenDaysFcServiceDAO.findAll(JpaWrappers.<LoadFeatureCityTenDaysFcServiceDO>lambdaQuery()
            .eq(!StringUtils.isEmpty(year), LoadFeatureCityTenDaysFcServiceDO::getYear, year)
            .eq(!StringUtils.isEmpty(month), LoadFeatureCityTenDaysFcServiceDO::getMonth, month)
            .eq(!StringUtils.isEmpty(type), LoadFeatureCityTenDaysFcServiceDO::getType, type)
            .eq(!StringUtils.isEmpty(cityId), LoadFeatureCityTenDaysFcServiceDO::getCityId, cityId)
            .eq(!StringUtils.isEmpty(caliberId), LoadFeatureCityTenDaysFcServiceDO::getCaliberId, caliberId)
            .eq(!StringUtils.isEmpty(algorithmId), LoadFeatureCityTenDaysFcServiceDO::getAlgorithmId, algorithmId)
        );
    }

    @Override
    public List<LoadFeatureCityTenDaysFcServiceDO> findLoadFeatureCityTenDaysFcServiceDOs(String cityId, String caliberId, String algorithmId, String year, List<String> monthList) {
        return loadFeatureCityTenDaysFcServiceDAO.findAll(JpaWrappers.<LoadFeatureCityTenDaysFcServiceDO>lambdaQuery()
            .eq(!StringUtils.isEmpty(year), LoadFeatureCityTenDaysFcServiceDO::getYear, year)
            .in(!CollectionUtils.isEmpty(monthList), LoadFeatureCityTenDaysFcServiceDO::getMonth, monthList)
            .eq(!StringUtils.isEmpty(cityId), LoadFeatureCityTenDaysFcServiceDO::getCityId, cityId)
            .eq(!StringUtils.isEmpty(caliberId), LoadFeatureCityTenDaysFcServiceDO::getCaliberId, caliberId)
            .eq(!StringUtils.isEmpty(algorithmId), LoadFeatureCityTenDaysFcServiceDO::getAlgorithmId, algorithmId)
        );
    }

    @Override
    public List<LoadFeatureCityTenDaysFcServiceDO> getMonthReportVO(String cityId, String caliberId, String year,
        String month) {
        return loadFeatureCityTenDaysFcServiceDAO
            .findAll(JpaWrappers.<LoadFeatureCityTenDaysFcServiceDO>lambdaQuery()
                .eq(!StringUtils.isEmpty(year), LoadFeatureCityTenDaysFcServiceDO::getYear, year)
                .eq(!StringUtils.isEmpty(month), LoadFeatureCityTenDaysFcServiceDO::getMonth, month)
                .eq(!StringUtils.isEmpty(cityId), LoadFeatureCityTenDaysFcServiceDO::getCityId, cityId)
                .eq(!StringUtils.isEmpty(caliberId), LoadFeatureCityTenDaysFcServiceDO::getCaliberId, caliberId)
                .eq(LoadFeatureCityTenDaysFcServiceDO::getReport,true)
            );
    }

    @Override
    public List<LoadFeatureCityTenDaysFcServiceDO> getMonthReportVO(List<String> cityIds, String caliberId, String year,
        String month) {
        return loadFeatureCityTenDaysFcServiceDAO
            .findAll(JpaWrappers.<LoadFeatureCityTenDaysFcServiceDO>lambdaQuery()
                .eq(!StringUtils.isEmpty(year), LoadFeatureCityTenDaysFcServiceDO::getYear, year)
                .eq(!StringUtils.isEmpty(month), LoadFeatureCityTenDaysFcServiceDO::getMonth, month)
                .in(!CollectionUtils.isEmpty(cityIds), LoadFeatureCityTenDaysFcServiceDO::getCityId, cityIds)
                .eq(!StringUtils.isEmpty(caliberId), LoadFeatureCityTenDaysFcServiceDO::getCaliberId, caliberId)
                .eq(LoadFeatureCityTenDaysFcServiceDO::getReport,true)
            );
    }

    @Override
    public List<LoadFeatureCityTenDaysFcServiceDO> getMonthForecastVO(String cityId, String caliberId, String year,
        String month, String algorithmId) {
        return loadFeatureCityTenDaysFcServiceDAO
            .findAll(JpaWrappers.<LoadFeatureCityTenDaysFcServiceDO>lambdaQuery()
                .eq(!StringUtils.isEmpty(year), LoadFeatureCityTenDaysFcServiceDO::getYear, year)
                .eq(!StringUtils.isEmpty(month), LoadFeatureCityTenDaysFcServiceDO::getMonth, month)
                .eq(!StringUtils.isEmpty(cityId), LoadFeatureCityTenDaysFcServiceDO::getCityId, cityId)
                .eq(!StringUtils.isEmpty(caliberId), LoadFeatureCityTenDaysFcServiceDO::getCaliberId, caliberId)
                .eq(!StringUtils.isEmpty(algorithmId), LoadFeatureCityTenDaysFcServiceDO::getAlgorithmId, algorithmId)
            );
    }

    @Override
    public List<LoadFeatureCitySeasonFcDO> getSeasonForecastVO(String cityId, String caliberId, String year,
        String month, String algorithmId, String season) {
        return loadFeatureCitySeasonFcDAO
            .findAll(JpaWrappers.<LoadFeatureCitySeasonFcDO>lambdaQuery()
                .eq(!StringUtils.isEmpty(year), LoadFeatureCitySeasonFcDO::getYear, year)
                .eq(!StringUtils.isEmpty(month), LoadFeatureCitySeasonFcDO::getMonth, month)
                .eq(!StringUtils.isEmpty(cityId), LoadFeatureCitySeasonFcDO::getCityId, cityId)
                .eq(!StringUtils.isEmpty(caliberId), LoadFeatureCitySeasonFcDO::getCaliberId, caliberId)
                .eq(!StringUtils.isEmpty(algorithmId), LoadFeatureCitySeasonFcDO::getAlgorithmId, algorithmId)
                .eq(!StringUtils.isEmpty(season), LoadFeatureCitySeasonFcDO::getSeason, season)
            );
    }

    @Override
    public void doSaveOrUpdate(LoadFeatureCityTenDaysFcServiceDO fcVO) {
        if (fcVO == null) {
            return;
        }
        List<LoadFeatureCityTenDaysFcServiceDO> oldVoList =
            this.findLoadFeatureCityTenDaysFcServiceDOs(fcVO.getCityId(),
                fcVO.getCaliberId(), fcVO.getAlgorithmId(), fcVO.getYear(),
                fcVO.getMonth(), fcVO.getType());
        if (CollectionUtils.isEmpty(oldVoList)) {
            fcVO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            loadFeatureCityTenDaysFcServiceDAO
                .save(fcVO);
            return;
        }
        LoadFeatureCityTenDaysFcServiceDO oldVO = oldVoList.get(0);
        String id = oldVO.getId();
        fcVO.setCreatetime(oldVO.getCreatetime());
        BeanUtils.copyProperties(fcVO, oldVO);
        oldVO.setId(id);
        oldVO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
        loadFeatureCityTenDaysFcServiceDAO.saveOrUpdateByTemplate(oldVO);
    }

}
