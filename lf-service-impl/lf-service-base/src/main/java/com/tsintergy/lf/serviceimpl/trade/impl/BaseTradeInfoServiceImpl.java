/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.trade.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsintergy.lf.serviceapi.base.trade.api.BaseTradeInfoService;
import com.tsintergy.lf.serviceapi.base.trade.pojo.BaseTradeInfoDO;
import com.tsintergy.lf.serviceimpl.trade.dao.BaseTradeInfoDAO;
import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/8/29 15:19
 * @Version: 1.0.0
 */

@Service("baseTradeInfoService")
public class BaseTradeInfoServiceImpl implements BaseTradeInfoService {

    @Autowired
    BaseTradeInfoDAO baseTradeInfoDAO;
    @Override
    public void save(List<BaseTradeInfoDO> list) {
        baseTradeInfoDAO.saveOrUpdateBatch(list);
    }

    @Override
    public List<BaseTradeInfoDO> findBaseTradeInfo(String name ,String code, String pCode,Integer level) {
        return baseTradeInfoDAO.findAll(JpaWrappers.<BaseTradeInfoDO>lambdaQuery()
            .eq(!StringUtils.isEmpty(code), BaseTradeInfoDO::getCode,code)
            .eq(pCode!=null, BaseTradeInfoDO::getParent_code,pCode)
            .eq(level!=null, BaseTradeInfoDO::getLevel,level)
            .like(!StringUtils.isEmpty(name),BaseTradeInfoDO::getName,name)
            .orderByAsc(BaseTradeInfoDO::getOrderNo));
    }
}