package com.tsintergy.lf.serviceimpl.load.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.math.BigDecimalFunctions;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.aif.tool.core.feature.load.CityLoadFeature;
import com.tsintergy.aif.tool.core.feature.load.Load;
import com.tsintergy.aif.tool.core.feature.stat.LoadFeatureStat;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.base.api.HolidayService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityUserAddHisService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureUserAddDayHisService;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadUserAddAndFeatureDTO;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadUserAddDTO;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadUserAddTypicalDTO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityUserAddHisDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureUserAddDayHisDO;
import com.tsintergy.lf.serviceimpl.load.dao.LoadCityUserAddHisDAO;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class LoadCityUserAddHisServiceImpl implements LoadCityUserAddHisService {

    @Autowired
    private LoadCityUserAddHisDAO loadCityUserAddHisDAO;

    @Autowired
    private LoadFeatureUserAddDayHisService loadFeatureUserAddDayHisService;

    @Autowired
    HolidayService holidayService;

    @Override
    public List<LoadCityUserAddHisDO> findByDate(String cityId, String caliberId, Date startDate, Date endDate) {
        return loadCityUserAddHisDAO.findAll(JpaWrappers.<LoadCityUserAddHisDO>lambdaQuery()
                .eq(StringUtils.isNotBlank(cityId), LoadCityUserAddHisDO::getCityId,cityId)
                .eq(StringUtils.isNotBlank(caliberId),LoadCityUserAddHisDO::getCaliberId,caliberId)
                .ge(startDate!= null,LoadCityUserAddHisDO::getDate,startDate)
                .le(endDate!= null,LoadCityUserAddHisDO::getDate,endDate));
    }

    @Override
    public List<BigDecimal> getLoad(String cityId, String caliberId, Date startDate, Date endDate) {
        List<Date> dateList = DateUtil.getListBetweenDay(startDate, endDate);
        List<BigDecimal> loadList = new ArrayList<>();
        List<LoadCityUserAddHisDO> loadCityUserAddHisDOS = findByDate(cityId, caliberId, startDate, endDate);
        if (CollUtil.isEmpty(loadCityUserAddHisDOS)){
            return loadList;
        }
        Map<java.sql.Date, List<LoadCityUserAddHisDO>> collect = loadCityUserAddHisDOS.stream().collect(Collectors.groupingBy(LoadCityUserAddHisDO::getDate));
        for (Date date : dateList) {
            List<LoadCityUserAddHisDO> loadCityUserAddHisDOList = collect.get(date);
            if (CollUtil.isNotEmpty(loadCityUserAddHisDOList)){
                loadList.addAll(BigDecimalFunctions.listScale(loadCityUserAddHisDOList.get(0).getLoadList(),0));
            }else {
                loadList.addAll(ColumnUtil.getZeroOrNullList(96,null));
            }

        }
        return loadList;
    }

    @Override
    public LoadUserAddDTO getUserLoadContrast(String cityId, String caliberId, Date date) {
        LoadUserAddDTO loadUserAddDTO = new LoadUserAddDTO();
        loadUserAddDTO.setDate(date);
        Date sameDate = DateUtils.addDays(date, -7);
        loadUserAddDTO.setLastDate(sameDate);
        List<LoadCityUserAddHisDO> loadCityUserAddHisDOS = findByDate(cityId, caliberId, date, date);
        if (CollUtil.isNotEmpty(loadCityUserAddHisDOS)){
            loadUserAddDTO.setLoad(loadCityUserAddHisDOS.get(0).getLoadList());
        }
        List<LoadCityUserAddHisDO> loadCityUserAddHisDOList = findByDate(cityId, caliberId, sameDate, sameDate);
        if (CollUtil.isNotEmpty(loadCityUserAddHisDOList)){
            loadUserAddDTO.setLastLoad(loadCityUserAddHisDOList.get(0).getLoadList());
        }
        return loadUserAddDTO;
    }

    @Override
    public List<LoadUserAddAndFeatureDTO> getUserLoadAndFeature(String cityId, String caliberId, Date startDate, Date endDate) {
        List<LoadUserAddAndFeatureDTO> loadUserAddAndFeatureDTOS = new ArrayList<>();
        List<Date> dateList = DateUtil.getListBetweenDay(startDate, endDate);
        List<LoadCityUserAddHisDO> loadCityUserAddHisDOS = findByDate(cityId, caliberId, startDate, endDate);
        Map<java.sql.Date, List<LoadCityUserAddHisDO>> loadMap =  new HashMap<>();
        if (CollUtil.isNotEmpty(loadCityUserAddHisDOS)){
            loadMap= loadCityUserAddHisDOS.stream().collect(Collectors.groupingBy(LoadCityUserAddHisDO::getDate));
        }
        List<LoadFeatureUserAddDayHisDO> loadFeatureUserAddDayHisDOS = loadFeatureUserAddDayHisService.findByDate(cityId, caliberId, startDate, endDate);
        Map<java.sql.Date, List<LoadFeatureUserAddDayHisDO>> featureMap = new HashMap<>();
        if (CollUtil.isNotEmpty(loadFeatureUserAddDayHisDOS)){
            featureMap= loadFeatureUserAddDayHisDOS.stream().collect(Collectors.groupingBy(LoadFeatureUserAddDayHisDO::getDate));
        }
        for (Date date : dateList) {
            LoadUserAddAndFeatureDTO loadUserAddAndFeatureDTO = new LoadUserAddAndFeatureDTO();
            loadUserAddAndFeatureDTO.setDate(com.tsintergy.aif.tool.core.utils.date.DateUtil.date2String(date, DateFormatType.SIMPLE_DATE_FORMAT_STR));
            List<LoadFeatureUserAddDayHisDO> loadFeatureUserAddDayHisDOList = featureMap.get(date);
            if (CollUtil.isNotEmpty(loadFeatureUserAddDayHisDOList)){
                BeanUtils.copyProperties(loadFeatureUserAddDayHisDOList.get(0),loadUserAddAndFeatureDTO);
            }
            List<LoadCityUserAddHisDO> loadCityUserAddHisDOList = loadMap.get(date);
            if (CollUtil.isNotEmpty(loadCityUserAddHisDOList)){
                loadUserAddAndFeatureDTO.setLoad(loadCityUserAddHisDOList.get(0).getLoadList());
            }
            loadUserAddAndFeatureDTO.setLoadGradient(loadUserAddAndFeatureDTO.getLoadGradient()!= null?
                    BigDecimalFunctions.multiply(loadUserAddAndFeatureDTO.getLoadGradient(),BigDecimal.valueOf(100)):null);
            loadUserAddAndFeatureDTO.setGradient(loadUserAddAndFeatureDTO.getGradient()!= null?
                    BigDecimalFunctions.multiply(loadUserAddAndFeatureDTO.getGradient(),BigDecimal.valueOf(100)):null);
            if (CollUtil.isNotEmpty(loadFeatureUserAddDayHisDOList)||CollUtil.isNotEmpty(loadCityUserAddHisDOList)){
                loadUserAddAndFeatureDTOS.add(loadUserAddAndFeatureDTO);
            }
        }

        return loadUserAddAndFeatureDTOS;
    }

    @SneakyThrows
    @Override
    public LoadUserAddTypicalDTO getUserTypicalLoad(String cityId, String caliberId, Date startDate, Date endDate) {
        LoadUserAddTypicalDTO loadUserAddTypicalDTO = new LoadUserAddTypicalDTO();
        List<BigDecimal> workLoad = new ArrayList<>();
         List<BigDecimal> restLoad= new ArrayList<>();
         List<BigDecimal> holidayLoad= new ArrayList<>();
        List<LoadCityUserAddHisDO> loadCityUserAddHisDOS = findByDate(cityId, caliberId, startDate, endDate);
        Integer holidayNum = 0;
        Integer workNum = 0;
        Integer restNum = 0;
        for (LoadCityUserAddHisDO loadCityUserAddHisDO : loadCityUserAddHisDOS) {
            //判断是否是节假日
            Boolean holiday = holidayService.isHoliday(loadCityUserAddHisDO.getDate());
            if (holiday){
                if (CollUtil.isNotEmpty(holidayLoad)){
                    holidayLoad= BigDecimalFunctions.listAdd(loadCityUserAddHisDO.getLoadList(),holidayLoad);
                }else {
                    holidayLoad= loadCityUserAddHisDO.getLoadList();
                }
                holidayNum++;
                continue;
            }
            boolean workingDay = isWorkingDay(loadCityUserAddHisDO.getDate());
            if (workingDay){
                if (CollUtil.isNotEmpty(workLoad)){
                    workLoad= BigDecimalFunctions.listAdd(loadCityUserAddHisDO.getLoadList(),workLoad);
                }else {
                    workLoad= loadCityUserAddHisDO.getLoadList();
                }
                workNum++;
            }else {
                if (CollUtil.isNotEmpty(restLoad)){
                    restLoad= BigDecimalFunctions.listAdd(loadCityUserAddHisDO.getLoadList(),restLoad);
                }else {
                    restLoad= loadCityUserAddHisDO.getLoadList();
                }
                restNum++;
            }

        }
        loadUserAddTypicalDTO.setHolidayLoad(CollUtil.isNotEmpty(holidayLoad)&&holidayNum!=0?BigDecimalFunctions.listDivideValue(
                holidayLoad,BigDecimal.valueOf(holidayNum)):holidayLoad);
        loadUserAddTypicalDTO.setWorkLoad(CollUtil.isNotEmpty(workLoad)&&workNum!=0?BigDecimalFunctions.listDivideValue(
                workLoad,BigDecimal.valueOf(workNum)):workLoad);
        loadUserAddTypicalDTO.setRestLoad(CollUtil.isNotEmpty(restLoad)&&restNum!=0?BigDecimalFunctions.listDivideValue(
                restLoad,BigDecimal.valueOf(restNum)):restLoad);
        return loadUserAddTypicalDTO;
    }

    @SneakyThrows
    @Override
    public List<LoadUserAddAndFeatureDTO> getTypicalLoadAndFeature(String cityId, String caliberId, Date startDate, Date endDate) {
        List<LoadUserAddAndFeatureDTO> loadUserAddAndFeatureDTOS = new ArrayList<>();
        LoadUserAddTypicalDTO userTypicalLoad = getUserTypicalLoad(cityId, caliberId, startDate, endDate);
        if (CollUtil.isNotEmpty(userTypicalLoad.getWorkLoad())){
            LoadUserAddAndFeatureDTO loadUserAddAndFeatureDTO = new LoadUserAddAndFeatureDTO();
            loadUserAddAndFeatureDTO.setDate("工作日");
            loadUserAddAndFeatureDTO.setLoad(userTypicalLoad.getWorkLoad());
            LoadCityUserAddHisDO loadCityUserAddHisDO = new LoadCityUserAddHisDO();
            BasePeriodUtils.setAllFiled(loadCityUserAddHisDO,ColumnUtil.listToMap(userTypicalLoad.getWorkLoad(), Constants.LOAD_CURVE_START_WITH_ZERO));
            LoadFeatureStat loadFeatureStat = new LoadFeatureStat();
            CityLoadFeature cityLoadFeature = loadFeatureStat.statCityDayFeature((Load) loadCityUserAddHisDO, null);
            BeanUtils.copyProperties(cityLoadFeature,loadUserAddAndFeatureDTO);
            loadUserAddAndFeatureDTOS.add(loadUserAddAndFeatureDTO);
        }
        if (CollUtil.isNotEmpty(userTypicalLoad.getRestLoad())){
            LoadUserAddAndFeatureDTO loadUserAddAndFeatureDTO = new LoadUserAddAndFeatureDTO();
            loadUserAddAndFeatureDTO.setDate("休息日");
            loadUserAddAndFeatureDTO.setLoad(userTypicalLoad.getRestLoad());
            LoadCityUserAddHisDO loadCityUserAddHisDO = new LoadCityUserAddHisDO();
            BasePeriodUtils.setAllFiled(loadCityUserAddHisDO,ColumnUtil.listToMap(userTypicalLoad.getRestLoad(), Constants.LOAD_CURVE_START_WITH_ZERO));
            LoadFeatureStat loadFeatureStat = new LoadFeatureStat();
            CityLoadFeature cityLoadFeature = loadFeatureStat.statCityDayFeature((Load) loadCityUserAddHisDO, null);
            BeanUtils.copyProperties(cityLoadFeature,loadUserAddAndFeatureDTO);
            loadUserAddAndFeatureDTOS.add(loadUserAddAndFeatureDTO);
        }
        if (CollUtil.isNotEmpty(userTypicalLoad.getHolidayLoad())){
            LoadUserAddAndFeatureDTO loadUserAddAndFeatureDTO = new LoadUserAddAndFeatureDTO();
            loadUserAddAndFeatureDTO.setDate("节假日");
            loadUserAddAndFeatureDTO.setLoad(userTypicalLoad.getHolidayLoad());
            LoadCityUserAddHisDO loadCityUserAddHisDO = new LoadCityUserAddHisDO();
            BasePeriodUtils.setAllFiled(loadCityUserAddHisDO,ColumnUtil.listToMap(userTypicalLoad.getHolidayLoad(), Constants.LOAD_CURVE_START_WITH_ZERO));
            LoadFeatureStat loadFeatureStat = new LoadFeatureStat();
            CityLoadFeature cityLoadFeature = loadFeatureStat.statCityDayFeature((Load) loadCityUserAddHisDO, null);
            BeanUtils.copyProperties(cityLoadFeature,loadUserAddAndFeatureDTO);
            loadUserAddAndFeatureDTOS.add(loadUserAddAndFeatureDTO);
        }
        return loadUserAddAndFeatureDTOS;
    }

    /**
     * 判断是否是工作日
     * @param date
     * @return
     */
    public  boolean isWorkingDay(Date date) {
        // 将Date对象转换为Calendar对象
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
        return dayOfWeek != Calendar.SATURDAY && dayOfWeek != Calendar.SUNDAY;
    }

}
