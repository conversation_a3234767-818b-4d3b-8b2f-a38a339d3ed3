/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.weather.dao;

import com.tsieframework.core.base.dao.hibernate.BaseAbstractDAO;
import com.tsieframework.core.base.dao.hibernate.BaseDAO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.dao.hibernate.QueryOp;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherStationHisBasicDO;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/5/20 15:41
 * @Version: 1.0.0
 */
@Component
public class WeatherStationHisBasicDAO extends BaseAbstractDAO<WeatherStationHisBasicDO> {


    public WeatherStationHisBasicDO getWeatherStationHisBasicDO(String stationId, Date date, Integer type){
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create().queryDataOnly();
        dbQueryParamBuilder.where(QueryOp.DateEqualTo,"date",date)
            .where(QueryOp.StringEqualTo,"stationId",stationId)
            .where(QueryOp.NumberEqualTo,"type",type);
        List<WeatherStationHisBasicDO> resultList = this.query(dbQueryParamBuilder.build()).getDatas();
        return CollectionUtils.isEmpty(resultList) ? null : resultList.get(0);
    }

    public List<WeatherStationHisBasicDO> getWeatherStationHisBasicDO(String cityId,String stationId, Date startDate,Date endDate,Integer type){
        DBQueryParam param = DBQueryParamBuilder.create().build();
        DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (null != startDate) {
            param.getQueryConditions().put("_dnl_date", new java.sql.Date(startDate.getTime()));
        }
        if (null != endDate) {
            param.getQueryConditions().put("_dnm_date", new java.sql.Date(endDate.getTime()));
        }
        if (null != type) {
            param.getQueryConditions().put("_ne_type", type);
        }
        if (null != cityId) {
            param.getQueryConditions().put("_ne_cityId", cityId);
        }
        if (null != stationId) {
            param.getQueryConditions().put("_ne_stationId", stationId);
        }
        List<WeatherStationHisBasicDO> datas = this.query(param).getDatas();
        return datas;
    }
}