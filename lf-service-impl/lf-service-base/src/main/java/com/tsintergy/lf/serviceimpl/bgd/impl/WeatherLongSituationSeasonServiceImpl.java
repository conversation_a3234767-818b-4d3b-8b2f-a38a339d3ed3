package com.tsintergy.lf.serviceimpl.bgd.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsieframework.core.base.service.BaseFacadeServiceImpl;
import com.tsintergy.lf.serviceapi.base.bgd.api.WeatherLongSeasonSituationService;
import com.tsintergy.lf.serviceapi.base.bgd.pojo.WeatherLongSeasonSituationDO;
import com.tsintergy.lf.serviceimpl.bgd.dao.WeatherLongSeasonSituationDAO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service("weatherLongSeasonSituationService")
public class WeatherLongSituationSeasonServiceImpl extends BaseFacadeServiceImpl implements WeatherLongSeasonSituationService {

    @Autowired
    private WeatherLongSeasonSituationDAO weatherLongSeasonSituationDAO;

    @Override
    public WeatherLongSeasonSituationDO getWeatherLongSeasonSituationDO(String cityId, String year, Integer dateType) {
        return weatherLongSeasonSituationDAO.findOne(JpaWrappers.<WeatherLongSeasonSituationDO>lambdaQuery()
        .eq(WeatherLongSeasonSituationDO::getCityId,cityId)
        .eq(WeatherLongSeasonSituationDO::getYear,year)
        .eq(WeatherLongSeasonSituationDO::getSeason,String.valueOf(dateType)));
    }
}
