package com.tsintergy.lf.serviceimpl.ultra.dao;

import com.tsieframework.core.base.dao.jpa.BaseJpaDAO;
import com.tsieframework.core.component.datasource.dynamic.annotation.DataSource;
import com.tsintergy.lf.serviceapi.base.ultra.pojo.LoadCityFcUltraBatchDO;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository
@DataSource("ultra")
public interface LoadCityFcUltraBatchDAO extends BaseJpaDAO<LoadCityFcUltraBatchDO, String> {

}