package com.tsintergy.lf.serviceimpl.evalucation.dao;


import com.alibaba.fastjson.JSONObject;
import com.tsieframework.core.base.SpringContextManager;
import com.tsieframework.core.base.dao.hibernate.BaseAbstractDAO;
import com.tsieframework.core.base.dao.hibernate.BaseDAO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.util.compatible.BigDecimalUtils;
import com.tsieframework.util.compatible.StringUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.LoadCalUtil;
import com.tsintergy.lf.serviceapi.base.base.pojo.BaseLoadFcCityDO;
import com.tsintergy.lf.serviceapi.base.check.pojo.SettingReportDO;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.AccuracyLoadCityFcBatchDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.DeviationLoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.DispersionLoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.StatisticsCityDayFcBatchDO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcBatchDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceimpl.forecast.impl.ForecastResultStatServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;

@Slf4j
@Component
public class StatisticsCityDayFcBatchDAO extends BaseAbstractDAO<StatisticsCityDayFcBatchDO> {

    /**
     *      功能描述: <br>  获取预测结果统计数据
     *
     * @param cityId    城市ID
     * @param caliberId 口径ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return  @since: 1.0.0     @Author:zhaoml     @Date: 2018/4/24 13:21   
     */
    public List<StatisticsCityDayFcBatchDO> getStatisticsCityDayFcBatchDOs(String cityId, String caliberId,
                                                                           List<String> algorithmIds, List<Integer> batchIds,
                                                                           Date startDate, Date endDate) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);

        if (null != startDate) {
            param.getQueryConditions().put("_dnl_date", new java.sql.Date(startDate.getTime()));
        }
        if (null != endDate) {
            param.getQueryConditions().put("_dnm_date", new java.sql.Date(endDate.getTime()));
        }
        param.getQueryConditions().put("_sin_algorithmId", algorithmIds);
        param.getQueryConditions().put("_nin_batchId", batchIds);
        if (StringUtils.isNotBlank(cityId)) {
            param.getQueryConditions().put("_ne_cityId", cityId);
        }
        if (StringUtils.isNotBlank(caliberId)) {
            param.getQueryConditions().put("_ne_caliberId", caliberId);
        }
        param.setDesc("date");
        return query(param).getDatas();
    }


    /**
     *      功能描述: <br>  获取预测结果统计
     *
     * @param cityId      城市ID
     * @param caliberId   口径ID
     * @param algorithmId 算法ID
     * @param date        日期
     * @return  @since: 1.0.0     @Author:zhaoml     @Date: 2018/4/24 13:23   
     */
    public StatisticsCityDayFcBatchDO getStatisticsCityDayFcBatchDO(String cityId, String caliberId, String algorithmId,
                                                                    Date date, Integer batchId) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (null != date) {
            param.getQueryConditions().put("_de_date", new java.sql.Date(date.getTime()));
        }
        if (StringUtils.isNotBlank(algorithmId)) {
            param.getQueryConditions().put("_ne_algorithmId", algorithmId);
        }
        if (StringUtils.isNotBlank(cityId)) {
            param.getQueryConditions().put("_ne_cityId", cityId);
        }
        if (batchId != null) {
            param.getQueryConditions().put("_ne_batchId", batchId);
        }
        if (StringUtils.isNotBlank(caliberId)) {
            param.getQueryConditions().put("_ne_caliberId", caliberId);
        }
        List<StatisticsCityDayFcBatchDO> datas = query(param).getDatas();
        if (datas.size() > 0) {
            return datas.get(0);
        }
        return null;
    }

    /**
     * 保存或更新
     */
    public StatisticsCityDayFcBatchDO doSaveOrUpdateStatisticsCityDayFcBatchDO(
            StatisticsCityDayFcBatchDO statisticsCityDayFcVO)
            throws Exception {
        StatisticsCityDayFcBatchDO oldVO = getStatisticsCityDayFcBatchDO(statisticsCityDayFcVO.getCityId(),
                statisticsCityDayFcVO.getCaliberId(), statisticsCityDayFcVO.getAlgorithmId(),
                statisticsCityDayFcVO.getDate(), statisticsCityDayFcVO.getBatchId());
        if (oldVO != null) {
            String id = oldVO.getId();
            BeanUtils.copyProperties(statisticsCityDayFcVO, oldVO, "createtime");
            oldVO.setId(id);
            oldVO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            return updateAndFlush(oldVO);
        } else {
            return createAndFlush(statisticsCityDayFcVO);
        }
    }

    /**
     * 保存或更新
     */
    public List<StatisticsCityDayFcBatchDO> doSaveOrUpdateStatisticsCityDayFcBatchDOs(
            List<StatisticsCityDayFcBatchDO> statisticsCityDayFcVOS) throws Exception {
        List<StatisticsCityDayFcBatchDO> vos = new ArrayList<StatisticsCityDayFcBatchDO>();
        for (StatisticsCityDayFcBatchDO statisticsCityDayFcVO : statisticsCityDayFcVOS) {
            try {
                vos.add(this.doSaveOrUpdateStatisticsCityDayFcBatchDO(statisticsCityDayFcVO));
            } catch (Exception e) {
                log.error("保存预测统计结果出错了", e);
            }
        }
        return vos;
    }

    /**
     *      功能描述: <br>  获取预测结果统计数据
     *
     * @param cityIds     城市ID列表
     * @param algorithmId 算法ID
     * @param startDate   开始日期
     * @param endDate     结束日期
     * @return  @since: 1.0.0     @Author:zhaoml     @Date: 2018/4/24 13:25   
     */
    public List<StatisticsCityDayFcBatchDO> getStatisticsCityDayFcBatchDOs(List<String> cityIds, String caliberId,
                                                                           String algorithmId, Date startDate, Date endDate, Boolean isReport) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (null != startDate) {
            param.getQueryConditions().put("_dnl_date", new java.sql.Date(startDate.getTime()));
        }
        if (null != endDate) {
            param.getQueryConditions().put("_dnm_date", new java.sql.Date(endDate.getTime()));
        }
        if (StringUtils.isNotBlank(algorithmId)) {
            param.getQueryConditions().put("_ne_algorithmId", algorithmId);
        }
        if (null != cityIds && cityIds.size() > 0) {
            param.getQueryConditions().put("_sin_cityId", cityIds);
        }
        if (null != isReport) {
            param.getQueryConditions().put("_ne_report", isReport);
        }
        if (StringUtils.isNotBlank(caliberId)) {
            param.getQueryConditions().put("_ne_caliberId", caliberId);
        }
        param.setDesc("date");
        return query(param)
                .getDatas();
    }

    /**
     * 获取上报预测结果统计
     *
     * @param cityId    城市ID
     * @param caliberId 口径ID
     * @param date      日期
     */
    public StatisticsCityDayFcBatchDO getReportStatisticsCityDayFcBatchDO(String cityId, String caliberId, Date date)
            throws Exception {

        if (cityId == null) {
            throw TsieExceptionUtils.newBusinessException("城市ID不可为空");
        }
        if (caliberId == null) {
            throw TsieExceptionUtils.newBusinessException("口径ID不可为空");
        }
        if (date == null) {
            throw TsieExceptionUtils.newBusinessException("日期不可为空");
        }
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        param.getQueryConditions().put("_de_date", new java.sql.Date(date.getTime()));
        param.getQueryConditions().put("_se_cityId", cityId);
        param.getQueryConditions().put("_se_caliberId", caliberId);
        param.getQueryConditions().put("_ne_report", true);
        List<StatisticsCityDayFcBatchDO> datas = query(param)
                .getDatas();
        if (datas.size() > 0) {
            return datas.get(0);
        }
        return null;
    }

    /**
     * 统计一天预测结果情况
     *
     * @param accuracyLoadCityFcDO 准确率
     * @param standardAccuracy     考核标准准确率
     */
    public StatisticsCityDayFcBatchDO statistics(List<LoadCityHisDO> loadCityHisDOS, LoadCityFcBatchDO loadCityFcDO,
                                                 BaseLoadFcCityDO accuracyLoadCityFcDO, BigDecimal standardAccuracy, JSONObject denominatorMap) throws Exception {

        if (accuracyLoadCityFcDO == null) {
            log.error("统计预测结果有误：缺失准确率");
            return null;
        }

        String cityId = accuracyLoadCityFcDO.getCityId();
        String algorithmId = accuracyLoadCityFcDO.getAlgorithmId();
        String caliberId = accuracyLoadCityFcDO.getCaliberId();
        java.sql.Date date = accuracyLoadCityFcDO.getDate();


        StatisticsCityDayFcBatchDO statisticsCityDayFcDO = new StatisticsCityDayFcBatchDO();
        statisticsCityDayFcDO.setCityId(cityId);
        statisticsCityDayFcDO.setAlgorithmId(algorithmId);
        statisticsCityDayFcDO.setCaliberId(caliberId);
        statisticsCityDayFcDO.setAlgoForeTime(DateUtils.date2String(new Date(loadCityFcDO.getCreatetime().getTime()),
                DateFormatType.DATE_FORMAT_STR));
        statisticsCityDayFcDO.setDate(date);
        statisticsCityDayFcDO.setBatchId(loadCityFcDO.getBatchId());
        statisticsCityDayFcDO.setReport(accuracyLoadCityFcDO.getReport());
        statisticsCityDayFcDO.setCreatetime(loadCityFcDO.getCreatetime());

        Map<String, LoadCityHisDO> loadCityHisVOMap = new HashMap<>();
        if (loadCityHisDOS != null) {
            for (LoadCityHisDO loadCityHisDO : loadCityHisDOS) {
                String key =
                        loadCityHisDO.getCityId() + "-" + loadCityHisDO.getCaliberId() + "-" + loadCityHisDO.getDate()
                                .getTime();
                loadCityHisVOMap.put(key, loadCityHisDO);
            }
        }

        /**
         * 预测数据的key
         */
        BigDecimal dayAccuracy = null;
        if (loadCityFcDO != null) {
            String fcKey =
                    loadCityFcDO.getCityId() + "-" + loadCityFcDO.getCaliberId() + "-" + loadCityFcDO.getDate().getTime();
            LoadCityHisDO loadCityHisDO = loadCityHisVOMap.get(fcKey);
            //获取单日的平均准确率
            String hisDOCityId = loadCityHisDO.getCityId();
            if (denominatorMap != null && denominatorMap.containsKey(hisDOCityId)) {
                BigDecimal bigDecimal = null;
                if (denominatorMap.get(hisDOCityId) != null && !"".equals(denominatorMap.get(hisDOCityId))) {
                    Object o = denominatorMap.get(hisDOCityId);
                    bigDecimal = new BigDecimal(o.toString());
                    dayAccuracy = LoadCalUtil.getDayAccuracy(loadCityHisDO, loadCityFcDO, Constants.LOAD_CURVE_POINT_NUM, bigDecimal);
                } else {
                    dayAccuracy = LoadCalUtil.getDayAccuracy(loadCityHisDO, loadCityFcDO, Constants.LOAD_CURVE_POINT_NUM, null);
                }
            } else {
                dayAccuracy = LoadCalUtil.getDayAccuracy(loadCityHisDO, loadCityFcDO, Constants.LOAD_CURVE_POINT_NUM, null);
            }

            statisticsCityDayFcDO.setAccuracy(dayAccuracy);

            //计算偏差率
            DeviationLoadCityFcDO deviationLoadCityFcDO = SpringContextManager.getApplicationContext().getBean(ForecastResultStatServiceImpl.class).calculateDeviation(loadCityHisDO, loadCityFcDO);
            List<BigDecimal> list = BasePeriodUtils.toList(deviationLoadCityFcDO, Constants.LOAD_CURVE_POINT_NUM,
                    Constants.LOAD_CURVE_START_WITH_ZERO);
            List<BigDecimal> bigDecimals = new ArrayList<>();
            for (BigDecimal bigDecimal : list) {
                if (bigDecimal == null) {
                    log.error("城市id为: " + deviationLoadCityFcDO.getCityId() + ",口径id为: "
                            + deviationLoadCityFcDO.getCaliberId() + ",日期为: " + deviationLoadCityFcDO.getDate() + "的偏差率统计错误，请检查相关数据");
                    continue;
                }
                bigDecimals.add(bigDecimal.abs());
            }
            BigDecimal decimal = BigDecimalUtils.avgList(bigDecimals, 4, false).setScale(4, BigDecimal.ROUND_HALF_UP);
            statisticsCityDayFcDO.setDeviation(decimal);

            //计算离散率
            DispersionLoadCityFcDO dispersionLoadCityFcDO = SpringContextManager.getApplicationContext().getBean(DispersionLoadCityFcDAO.class).calculateDispersion(accuracyLoadCityFcDO);
            statisticsCityDayFcDO.setDispersion(LoadCalUtil.getStandardDeviation(BigDecimalUtils.avgList(BasePeriodUtils.toList(
                    dispersionLoadCityFcDO, Constants.LOAD_CURVE_POINT_NUM,
                    Constants.LOAD_CURVE_START_WITH_ZERO), 4, false)));
        }


        //如果准确率大于标准准确率 则合格率为1 为合格 否则为不合格 为0
        if (dayAccuracy != null && standardAccuracy != null) {
            if (dayAccuracy.compareTo(standardAccuracy) >= 0) {
                statisticsCityDayFcDO.setPass(new BigDecimal(1));
            } else {
                statisticsCityDayFcDO.setPass(new BigDecimal(0));
            }
        }
        statisticsCityDayFcDO.setStandardAccuracy(standardAccuracy);
        return statisticsCityDayFcDO;
    }


    /**
     * 统计一天预测结果情况
     *
     * @param settingReportDOS 考核设置
     */
    public List<StatisticsCityDayFcBatchDO> statistics(List<LoadCityHisDO> loadCityHisDOS,
                                                       List<LoadCityFcBatchDO> loadCityFcDOS, List<SettingReportDO> settingReportDOS, JSONObject denominatorMap) throws Exception {
        // list to map
        Map<String, BaseLoadFcCityDO> accuracyLoadCityFcVOMap = new HashMap<String, BaseLoadFcCityDO>();
        List<AccuracyLoadCityFcBatchDTO> accuracyLoadCityFcDOS = this
                .calculateAccuracyBatch(loadCityHisDOS, loadCityFcDOS);

        if (accuracyLoadCityFcDOS != null) {
            for (AccuracyLoadCityFcBatchDTO accuracyLoadCityFcDO : accuracyLoadCityFcDOS) {
                String key = accuracyLoadCityFcDO.getCityId() + "-" + accuracyLoadCityFcDO.getCaliberId() + "-"
                        + accuracyLoadCityFcDO
                        .getAlgorithmId() + "-" + accuracyLoadCityFcDO.getDate().getTime() + "-" + accuracyLoadCityFcDO.getBatchId();
                accuracyLoadCityFcVOMap.put(key, accuracyLoadCityFcDO);
            }
        }

        Map<String, LoadCityFcBatchDO> loadCityFcVOMap = new HashMap<>();
        if (loadCityFcDOS != null) {
            for (LoadCityFcBatchDO loadCityFcDO : loadCityFcDOS) {
                String key =
                        loadCityFcDO.getCityId() + "-" + loadCityFcDO.getCaliberId() + "-" + loadCityFcDO.getAlgorithmId()
                                + "-" + loadCityFcDO.getDate().getTime() + "-" + loadCityFcDO.getBatchId();
                loadCityFcVOMap.put(key, loadCityFcDO);
            }
        }

        // Map<城市ID,考核准确率>
        Map<String, BigDecimal> standardAccuracyMap = new HashMap<String, BigDecimal>();
        if (settingReportDOS != null) {
            for (SettingReportDO settingReportDO : settingReportDOS) {
                standardAccuracyMap.put(settingReportDO.getCityId(), settingReportDO.getStandardAccuracy());
            }
        }

        List<StatisticsCityDayFcBatchDO> statisticsCityDayFcDOS = new ArrayList<StatisticsCityDayFcBatchDO>();
        for (String key : accuracyLoadCityFcVOMap.keySet()) {
            try {
                BigDecimal standardAccuracy = standardAccuracyMap.get(key.substring(0, key.indexOf("-")));
                StatisticsCityDayFcBatchDO statisticsCityDayFcDO = statistics(loadCityHisDOS, loadCityFcVOMap.get(key),
                        accuracyLoadCityFcVOMap.get(key), standardAccuracy, denominatorMap);
                if (statisticsCityDayFcDO != null) {
                    statisticsCityDayFcDOS.add(statisticsCityDayFcDO);
                }
            } catch (Exception e) {
                log.error("统计预测结果出错了", e);
            }
        }

        return statisticsCityDayFcDOS;

    }

    /**
     * 计算预测准确率
     *
     * @param loadCityHisDOS 历史数据
     * @param loadCityFcDOS  预测数据
     */
    public List<AccuracyLoadCityFcBatchDTO> calculateAccuracyBatch(List<LoadCityHisDO> loadCityHisDOS,
                                                                   List<LoadCityFcBatchDO> loadCityFcDOS) throws Exception {

        Map<String, LoadCityHisDO> loadCityHisVOMap = new HashMap<String, LoadCityHisDO>();
        for (LoadCityHisDO loadCityHisDO : loadCityHisDOS) {
            String key = loadCityHisDO.getCityId() + "-" + loadCityHisDO.getCaliberId() + "-" + loadCityHisDO.getDate()
                    .getTime();
            loadCityHisVOMap.put(key, loadCityHisDO);
        }

        Map<String, List<LoadCityFcBatchDO>> loadCityFcVOMap = new HashMap();
        for (LoadCityFcBatchDO loadCityFcDO : loadCityFcDOS) {
            String key =
                    loadCityFcDO.getCityId() + "-" + loadCityFcDO.getCaliberId() + "-" + loadCityFcDO.getDate().getTime();
            if (loadCityFcVOMap.get(key) == null) {
                loadCityFcVOMap.put(key, new ArrayList<>());
            }
            loadCityFcVOMap.get(key).add(loadCityFcDO);
        }

        List<AccuracyLoadCityFcBatchDTO> accuracyLoadCityFcDOS = new ArrayList();
        for (String key : loadCityHisVOMap.keySet()) {
            if (loadCityFcVOMap.get(key) != null) {
                for (LoadCityFcBatchDO loadCityFcDO : loadCityFcVOMap.get(key)) {
                    try {
                        accuracyLoadCityFcDOS.add(this.calculateAccuracyBatchOne(loadCityHisVOMap.get(key), loadCityFcDO));
                    } catch (Exception e) {
                        log.error("统计准确率出错了", e);
                    }
                }
            }
        }

        return accuracyLoadCityFcDOS;
    }

    /**
     * 计算预测准确率
     *
     * @param loadCityHisVO 历史数据
     * @param loadCityFcDO  预测数据
     */
    public AccuracyLoadCityFcBatchDTO calculateAccuracyBatchOne(LoadCityHisDO loadCityHisVO, LoadCityFcBatchDO loadCityFcDO)
            throws Exception {

        if (!loadCityHisVO.getCityId().equals(loadCityFcDO.getCityId())) {
            log.error("历史数据和预测数据的城市不同，不可以计算");
            return null;
        }

        if (!loadCityHisVO.getDate().equals(loadCityFcDO.getDate())) {
            log.error("历史数据和预测数据的日期不同，不可以计算");
            return null;
        }

        Map<String, BigDecimal> hisDataMap = BasePeriodUtils
                .toMap(loadCityHisVO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
        Map<String, BigDecimal> fcDataMap = BasePeriodUtils
                .toMap(loadCityFcDO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
        Map<String, BigDecimal> accuracyMap = new HashMap<String, BigDecimal>();

        for (String column : hisDataMap.keySet()) {
            accuracyMap.put(column, LoadCalUtil.getAccuracy(hisDataMap.get(column), fcDataMap.get(column)));
        }

        AccuracyLoadCityFcBatchDTO accuracyLoadCityFcVO = new AccuracyLoadCityFcBatchDTO();
        accuracyLoadCityFcVO.setAlgorithmId(loadCityFcDO.getAlgorithmId());
        accuracyLoadCityFcVO.setCityId(loadCityFcDO.getCityId());
        accuracyLoadCityFcVO.setDate(loadCityFcDO.getDate());
        accuracyLoadCityFcVO.setBatchId(loadCityFcDO.getBatchId());
        accuracyLoadCityFcVO.setCaliberId(loadCityFcDO.getCaliberId());
        accuracyLoadCityFcVO.setReport(loadCityFcDO.getReport());
        BasePeriodUtils.setAllFiled(accuracyLoadCityFcVO, accuracyMap);

        return accuracyLoadCityFcVO;
    }
}