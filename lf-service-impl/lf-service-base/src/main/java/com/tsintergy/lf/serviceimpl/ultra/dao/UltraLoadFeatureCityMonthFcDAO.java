/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司 Author:  wangchen Date:  2019/3/11 15:01 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceimpl.ultra.dao;

import com.tsieframework.core.base.dao.jpa.BaseJpaDAO;
import com.tsieframework.core.component.datasource.dynamic.annotation.DataSource;
import com.tsintergy.lf.serviceapi.base.ultra.pojo.UltraLoadFeatureCityMonthFcDO;
import org.springframework.stereotype.Component;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2020/7/6
 * @since 1.0.0
 */
@Component
@DataSource("ultra")
public interface UltraLoadFeatureCityMonthFcDAO extends BaseJpaDAO<UltraLoadFeatureCityMonthFcDO, String> {

}