/**
 * Copyright(C),2015‐2020,北京清能互联科技有限公司
 * Author:yangjin
 * Date:2020/6/1610:01
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceimpl.weather.impl;

import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.dao.hibernate.QueryOp;
import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcModifyService;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherCityFcModifyDTO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcModifyDO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherCityFcModifyDAO;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 *Description: <br>
 *
 *<AUTHOR>
 *@create 2020/6/16
 *@since 1.0.0
 */
@Service("weatherCityFcModifyService")
public class WeatherCityFcModifyServiceImpl extends BaseServiceImpl implements WeatherCityFcModifyService {

    @Autowired
    private WeatherCityFcModifyDAO weatherCityFcModifyDAO;
    

    @Override
    public void doInsertOrUpdateData(WeatherCityFcModifyDTO dto) throws Exception{
        Map<String, BigDecimal> decimalMap = ColumnUtil.listToMap(dto.getValues(), Constants.LOAD_CURVE_START_WITH_ZERO);
        List<WeatherCityFcModifyDO> modifyWeatherByDateAndType = findModifyWeatherByDateAndType(dto.getDate(), dto.getType(),dto.getCityId());
        if(CollectionUtils.isEmpty(modifyWeatherByDateAndType)){
            WeatherCityFcModifyDO WeatherCityFcModifyDO = new WeatherCityFcModifyDO();
            WeatherCityFcModifyDO.setId(UUID.randomUUID().toString().replace("-","").toLowerCase());
            WeatherCityFcModifyDO.setCityId(dto.getCityId());
            WeatherCityFcModifyDO.setType(Integer.valueOf(dto.getType()));
            WeatherCityFcModifyDO.setDate(new java.sql.Date(new SimpleDateFormat("yyyy-MM-dd").parse(dto.getDate()).getTime()));
            WeatherCityFcModifyDO.setCreatetime(new Timestamp(System.currentTimeMillis()));
            BasePeriodUtils.setAllFiled(WeatherCityFcModifyDO,decimalMap);
            weatherCityFcModifyDAO.createAndFlush(WeatherCityFcModifyDO);
        }else {
            WeatherCityFcModifyDO WeatherCityFcModifyDO = modifyWeatherByDateAndType.get(0);
            BasePeriodUtils.setAllFiled(WeatherCityFcModifyDO,decimalMap);
            WeatherCityFcModifyDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            weatherCityFcModifyDAO.updateAndFlush(WeatherCityFcModifyDO);
        }

    }

    @Override
    public List<WeatherCityFcModifyDO> findModifyWeatherByDateAndType(String date,String type,String cityId) throws Exception{
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create();
        if (StringUtils.isNotBlank(cityId)) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo, "cityId", cityId);
        }
        if (type != null) {
            dbQueryParamBuilder.where(QueryOp.NumberEqualTo, "type", Integer.valueOf(type));
        }
        if (date != null) {
            dbQueryParamBuilder.where(QueryOp.DateEqualTo, "date", new java.sql.Date(simpleDateFormat.parse(date).getTime()));
        }

        List<WeatherCityFcModifyDO> datas = weatherCityFcModifyDAO.query(dbQueryParamBuilder.build()).getDatas();
        return datas;
    }

}