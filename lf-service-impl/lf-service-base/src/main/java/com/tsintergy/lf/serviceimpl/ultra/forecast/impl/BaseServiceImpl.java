//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.tsintergy.lf.serviceimpl.ultra.forecast.impl;

import com.tsieframework.core.base.SpringContextManager;
import com.tsieframework.core.base.dao.DAO;
import com.tsieframework.core.base.service.BaseService;
import javax.persistence.EntityManager;
import org.hibernate.Session;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;

public abstract class BaseServiceImpl implements BaseService, InitializingBean {
    private ApplicationContext applicationContext;


    @Autowired
    private EntityManager entityManager;
    public BaseServiceImpl() {
    }

    public <T extends DAO> T getDAO(Class<T> daoClass) throws Exception {
        return this.applicationContext.getBean(daoClass);
    }

    public void afterPropertiesSet() throws Exception {
        this.applicationContext = SpringContextManager.getApplicationContext();
    }


    public Session getCurrentSession() {
        return (Session)this.entityManager.unwrap(Session.class);
    }

}
