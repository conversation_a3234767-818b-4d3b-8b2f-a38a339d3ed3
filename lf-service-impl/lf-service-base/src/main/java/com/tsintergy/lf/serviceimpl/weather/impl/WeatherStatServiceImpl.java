/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.weather.impl;

import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.lf.core.constants.CityConstants;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.core.util.DataUtil;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.core.util.PeriodDataUtil;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherEnum;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcMeteoService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityHisService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherStatService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 *Description:  <br>
 *
 *@Author: <EMAIL>
 *@Date: 2022/6/29 17:29
 *@Version: 1.0.0
 */
@Slf4j
@Service
public class WeatherStatServiceImpl implements WeatherStatService {

    @Autowired
    WeatherCityHisService weatherCityHisService;

    @Autowired
    WeatherCityFcService weatherCityFcService;

    @Autowired
    private WeatherCityFcMeteoService weatherCityFcMeteoService;


    @Autowired
    CityService cityService;

    @Override
    public void statPorviceWeather(Date start, Date end) throws Exception {
        statPorviceHisWeather(start, end);
        statPorviceFcWeather(start, end);
        weatherCityFcMeteoService.statMeteoProvinceFcWeather(start, end);
    }


    @Override
    public void statPorviceHisWeather(Date start, Date end) throws Exception {
        List<WeatherCityHisDO> weatherCityHisDOs = weatherCityHisService.findWeatherCityHisDOs(null, null, start, end);
        if (!CollectionUtils.isEmpty(weatherCityHisDOs)) {
            List<WeatherCityHisDO> weatherCityHisDOS = new ArrayList<>();
//            Map<java.sql.Date, List<WeatherCityHisDO>> dateWeatherMap = weatherCityHisDOs.stream()
//                .collect(Collectors.groupingBy(WeatherCityHisDO::getDate));
            Map<String, List<WeatherCityHisDO>> dateWeatherMap = weatherCityHisDOs.stream()
                .collect(Collectors.groupingBy(t-> DateUtil.formateDate(t.getDate())+Constants.SEPARATOR_PUNCTUATION+t.getType()));
            Map<String, String> cityMap = cityService.findAllCitys().stream()
                .collect(Collectors.toMap(CityDO::getCity, CityDO::getId));
            dateWeatherMap.forEach((key,weatherDOS) -> {
                if (!CollectionUtils.isEmpty(weatherDOS)) {
                    try {
                        List<BigDecimal> resultValues = new ArrayList<>();
                        for (WeatherCityHisDO weatherCityHisDO : weatherDOS) {
                            if (weatherCityHisDO.getCityId().equals(cityMap.get("武汉"))) {
                                List<BigDecimal> weatherValues = BasePeriodUtils
                                    .toList(weatherCityHisDO, Constants.WEATHER_CURVE_POINT_NUM,true);
                                weatherValues.add(weatherCityHisDO.getT2400());
                                //log.info("武汉原始数据：" + weatherValues.toString());
                                List<BigDecimal> values = DataUtil.multiply(weatherValues,new BigDecimal(0.33333333));
                                //log.info("武汉*0.33333333后的结果：" + values.toString());
                                resultValues = DataUtil.listAdd(resultValues,values);
                            } else if (weatherCityHisDO.getCityId().equals(cityMap.get("黄冈"))) {
                                List<BigDecimal> weatherValues = BasePeriodUtils.toList(weatherCityHisDO,Constants.WEATHER_CURVE_POINT_NUM,true);
                                weatherValues.add(weatherCityHisDO.getT2400());
                                //log.info("黄冈原始数据：" + weatherValues.toString());
                                List<BigDecimal> values = DataUtil.multiply(weatherValues,new BigDecimal(0.15896188));
                                //log.info("黄冈*0.15896188后的结果：" + values.toString());
                                resultValues = DataUtil.listAdd(resultValues,values);
                            } else if (weatherCityHisDO.getCityId().equals(cityMap.get("襄阳"))) {
                                List<BigDecimal> weatherValues = BasePeriodUtils.toList(weatherCityHisDO,Constants.WEATHER_CURVE_POINT_NUM,true);
                                weatherValues.add(weatherCityHisDO.getT2400());
                                //log.info("襄阳原始数据：" + weatherValues.toString());
                                List<BigDecimal> values = DataUtil.multiply(weatherValues,new BigDecimal(0.14220059));
                                //log.info("襄阳*0.14220059后的结果：" + values.toString());
                                resultValues = DataUtil.listAdd(resultValues,values);
                            } else if (weatherCityHisDO.getCityId().equals(cityMap.get("荆州"))) {
                                List<BigDecimal> weatherValues = BasePeriodUtils.toList(weatherCityHisDO,Constants.WEATHER_CURVE_POINT_NUM,true);
                                weatherValues.add(weatherCityHisDO.getT2400());
                                //log.info("荆州原始数据：" + weatherValues.toString());
                                List<BigDecimal> values = DataUtil.multiply(weatherValues,new BigDecimal(0.14138956));
                                //log.info("荆州*0.14138956后的结果：" + values.toString());
                                resultValues = DataUtil.listAdd(resultValues,values);
                            } else if (weatherCityHisDO.getCityId().equals(cityMap.get("孝感"))) {
                                List<BigDecimal> weatherValues = BasePeriodUtils.toList(weatherCityHisDO,Constants.WEATHER_CURVE_POINT_NUM,true);
                                weatherValues.add(weatherCityHisDO.getT2400());
                                //log.info("孝感原始数据：" + weatherValues.toString());
                                List<BigDecimal> values = DataUtil.multiply(weatherValues,new BigDecimal(0.1154366));
                                //log.info("孝感*0.1154366后的结果：" + values.toString());
                                resultValues = DataUtil.listAdd(resultValues,values);
                            } else if (weatherCityHisDO.getCityId().equals(cityMap.get("宜昌"))) {
                                List<BigDecimal> weatherValues = BasePeriodUtils.toList(weatherCityHisDO,Constants.WEATHER_CURVE_POINT_NUM,true);
                                weatherValues.add(weatherCityHisDO.getT2400());
                                //log.info("宜昌原始数据：" + weatherValues.toString());
                                List<BigDecimal> values = DataUtil.multiply(weatherValues,new BigDecimal(0.10867802));
                                //log.info("宜昌*0.10867802后的结果：" + values.toString());
                                resultValues = DataUtil.listAdd(resultValues,values);
                            }
                        }

                        if (!CollectionUtils.isEmpty(resultValues)) {
                            log.info("湖北最后的结果：" + resultValues.toString());
                            WeatherCityHisDO weatherCityHisDO = new WeatherCityHisDO();
                            String[] split = key.split(Constants.SEPARATOR_PUNCTUATION);
                            weatherCityHisDO.setType(Integer.valueOf(split[1]));
                            weatherCityHisDO.setDate(new java.sql.Date(DateUtil.getDate(split[0],"yyyy-MM-dd").getTime()));
                            weatherCityHisDO.setCityId(CityConstants.PROVINCE_ID);

                            Map<String, BigDecimal> decimalMap = ColumnUtil
                                .listToMap(resultValues.subList(0,96), true);
                            BasePeriodUtils.setAllFiled(weatherCityHisDO,decimalMap);
                            weatherCityHisDO.setT2400(resultValues.get(96));
                            weatherCityHisDOS.add(weatherCityHisDO);

                        }
                    } catch (Exception e) {
                        log.error("装配省调气象异常",e);
                    }
                }
            });

            if (!CollectionUtils.isEmpty(weatherCityHisDOS)) {
                saveHisBatch(weatherCityHisDOS);
            }
        }

    }

    @Override
    public void statPorviceFcWeather(Date start, Date end) throws Exception {
        List<WeatherCityFcDO> weatherCityFcDOs = weatherCityFcService.findWeatherCityFcDOs(null, null, start, end);
        if (!CollectionUtils.isEmpty(weatherCityFcDOs)) {
            List<WeatherCityFcDO> weatherCityFcDOS = new ArrayList<>();
            Map<String, List<WeatherCityFcDO>> dateWeatherMap = weatherCityFcDOs.stream()
                .collect(Collectors.groupingBy(t-> DateUtil.formateDate(t.getDate())+Constants.SEPARATOR_PUNCTUATION+t.getType()));
            Map<String, String> cityMap = cityService.findAllCitys().stream()
                .collect(Collectors.toMap(CityDO::getCity, CityDO::getId));
            dateWeatherMap.forEach((key,weatherDOS) -> {
                if (!CollectionUtils.isEmpty(weatherDOS)) {
                    try {
                        List<BigDecimal> resultValues = new ArrayList<>();
                        for (WeatherCityFcDO weatherCityHisDO : weatherDOS) {
                            if (weatherCityHisDO.getCityId().equals(cityMap.get("武汉"))) {
                                List<BigDecimal> weatherValues = BasePeriodUtils
                                    .toList(weatherCityHisDO, Constants.WEATHER_CURVE_POINT_NUM,true);
                                weatherValues.add(weatherCityHisDO.getT2400());
                                //log.info("武汉原始数据：" + weatherValues.toString());
                                List<BigDecimal> values = DataUtil.multiply(weatherValues,new BigDecimal(0.33333333));
                                //log.info("武汉*0.33333333后的结果：" + values.toString());
                                resultValues = DataUtil.listAdd(resultValues,values);
                            } else if (weatherCityHisDO.getCityId().equals(cityMap.get("黄冈"))) {
                                List<BigDecimal> weatherValues = BasePeriodUtils.toList(weatherCityHisDO,Constants.WEATHER_CURVE_POINT_NUM,true);
                                weatherValues.add(weatherCityHisDO.getT2400());
                                //log.info("黄冈原始数据：" + weatherValues.toString());
                                List<BigDecimal> values = DataUtil.multiply(weatherValues,new BigDecimal(0.15896188));
                                //log.info("黄冈*0.15896188后的结果：" + values.toString());
                                resultValues = DataUtil.listAdd(resultValues,values);
                            } else if (weatherCityHisDO.getCityId().equals(cityMap.get("襄阳"))) {
                                List<BigDecimal> weatherValues = BasePeriodUtils.toList(weatherCityHisDO,Constants.WEATHER_CURVE_POINT_NUM,true);
                                weatherValues.add(weatherCityHisDO.getT2400());
                                //log.info("襄阳原始数据：" + weatherValues.toString());
                                List<BigDecimal> values = DataUtil.multiply(weatherValues,new BigDecimal(0.14220059));
                                //log.info("襄阳*0.14220059后的结果：" + values.toString());
                                resultValues = DataUtil.listAdd(resultValues,values);
                            } else if (weatherCityHisDO.getCityId().equals(cityMap.get("荆州"))) {
                                List<BigDecimal> weatherValues = BasePeriodUtils.toList(weatherCityHisDO,Constants.WEATHER_CURVE_POINT_NUM,true);
                                weatherValues.add(weatherCityHisDO.getT2400());
                                //log.info("荆州原始数据：" + weatherValues.toString());
                                List<BigDecimal> values = DataUtil.multiply(weatherValues,new BigDecimal(0.14138956));
                                //log.info("荆州*0.14138956后的结果：" + values.toString());
                                resultValues = DataUtil.listAdd(resultValues,values);
                            } else if (weatherCityHisDO.getCityId().equals(cityMap.get("孝感"))) {
                                List<BigDecimal> weatherValues = BasePeriodUtils.toList(weatherCityHisDO,Constants.WEATHER_CURVE_POINT_NUM,true);
                                weatherValues.add(weatherCityHisDO.getT2400());
                                //log.info("孝感原始数据：" + weatherValues.toString());
                                List<BigDecimal> values = DataUtil.multiply(weatherValues,new BigDecimal(0.1154366));
                                //log.info("孝感*0.1154366后的结果：" + values.toString());
                                resultValues = DataUtil.listAdd(resultValues,values);
                            } else if (weatherCityHisDO.getCityId().equals(cityMap.get("宜昌"))) {
                                List<BigDecimal> weatherValues = BasePeriodUtils.toList(weatherCityHisDO,Constants.WEATHER_CURVE_POINT_NUM,true);
                                weatherValues.add(weatherCityHisDO.getT2400());
                                //log.info("宜昌原始数据：" + weatherValues.toString());
                                List<BigDecimal> values = DataUtil.multiply(weatherValues,new BigDecimal(0.10867802));
                                //log.info("宜昌*0.10867802后的结果：" + values.toString());
                                resultValues = DataUtil.listAdd(resultValues,values);
                            }
                        }

                        if (!CollectionUtils.isEmpty(resultValues)) {
                            //log.info("湖北最后的结果：" + resultValues.toString());
                            WeatherCityFcDO weatherCityFcDO = new WeatherCityFcDO();
                            weatherCityFcDO.setType(WeatherEnum.TEMPERATURE.getType());
                            String[] split = key.split(Constants.SEPARATOR_PUNCTUATION);
                            weatherCityFcDO.setType(Integer.valueOf(split[1]));
                            weatherCityFcDO.setDate(new java.sql.Date(DateUtil.getDate(split[0],"yyyy-MM-dd").getTime()));
                            weatherCityFcDO.setCityId(CityConstants.PROVINCE_ID);
                            Map<String, BigDecimal> decimalMap = ColumnUtil
                                .listToMap(resultValues.subList(0,96), true);
                            BasePeriodUtils.setAllFiled(weatherCityFcDO,decimalMap);
                            weatherCityFcDO.setT2400(resultValues.get(96));
                            weatherCityFcDOS.add(weatherCityFcDO);

                        }
                    } catch (Exception e) {
                        log.error("装配省调气象异常",e);
                    }
                }
            });

            if (!CollectionUtils.isEmpty(weatherCityFcDOS)) {
                saveFcBatch(weatherCityFcDOS);
            }
        }

    }

    private void saveHisBatch(List<WeatherCityHisDO> values) {
        List<WeatherCityHisDO> updateD0List = new ArrayList<>();
        int size = 100;
        int loop = 1;
        for (int i = 0; i < values.size(); i++) {
            try {
                PeriodDataUtil.do24To96VO(values.get(i));
                updateD0List.add(values.get(i));
                if (i >= loop * size - 1) {
                    log.info("第{}次批量入库开始....,时间：{}", (i + 1) / 100,
                        DateUtils.date2String(new Date(), DateFormatType.DATE_FORMAT_STR));
                    weatherCityHisService.doSaveOrUpdateBatch(updateD0List);
                    log.info("第{}次批量入库完成....,时间：{}", (i + 1) / 100,
                        DateUtils.date2String(new Date(), DateFormatType.DATE_FORMAT_STR));
                    updateD0List.clear();
                    loop = loop + 1;
                }
            } catch (Exception e) {
                log.error("批量保存历史气象异常", e);
            }
        }
        if (!CollectionUtils.isEmpty(updateD0List)) {
            weatherCityHisService.doSaveOrUpdateBatch(updateD0List);
        }
        log.info("批量入库完成....,时间：{}", DateUtils.date2String(new Date(), DateFormatType.DATE_FORMAT_STR));
    }

    private void saveFcBatch(List<WeatherCityFcDO> values) {
        List<WeatherCityFcDO> updateD0List = new ArrayList<>();
        int size = 100;
        int loop = 1;
        for (int i = 0; i < values.size(); i++) {
            try {
                PeriodDataUtil.do24To96VO(values.get(i));
                updateD0List.add(values.get(i));
                if (i >= loop * size - 1) {
                    log.info("预测气象第{}次批量入库开始....,时间：{}", (i + 1) / 100,
                        DateUtils.date2String(new Date(), DateFormatType.DATE_FORMAT_STR));
                    weatherCityFcService.doInsertOrUpdateBatch(updateD0List);
                    log.info("预测气象第{}次批量入库完成....,时间：{}", (i + 1) / 100,
                        DateUtils.date2String(new Date(), DateFormatType.DATE_FORMAT_STR));
                    updateD0List.clear();
                    loop = loop + 1;
                }
            } catch (Exception e) {
                log.error("批量保存预测气象异常", e);
            }
        }
        if (!CollectionUtils.isEmpty(updateD0List)) {
            weatherCityFcService.doInsertOrUpdateBatch(updateD0List);
        }
        log.info("批量入库预测气象完成....,时间：{}", DateUtils.date2String(new Date(), DateFormatType.DATE_FORMAT_STR));
    }
}