
package com.tsintergy.lf.serviceimpl.weather.impl;

import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.dao.hibernate.QueryOp;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsieframework.util.compatible.StringUtils;
import com.tsintergy.lf.serviceapi.base.weather.api.StatisticsAccuracyWeatherCityMonthHisService;
import com.tsintergy.lf.serviceapi.base.weather.api.StatisticsAccuracyWeatherCityYearHisService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.StatisticsAccuracyWeatherCityMonthHisDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.StatisticsAccuracyWeatherCityYearHisDO;
import com.tsintergy.lf.serviceimpl.weather.dao.StatisticsAccuracyWeatherCityYearHisDAO;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;



/**
 * <AUTHOR>
 */

@Service("statisticsAccuracyWeatherCityYearHisService")
public class StatisticsAccuracyWeatherCityYearHisServiceImpl extends BaseServiceImpl implements StatisticsAccuracyWeatherCityYearHisService {

    private static final Logger logger = LoggerFactory.getLogger(StatisticsAccuracyWeatherCityYearHisServiceImpl.class);

    @Autowired
    private StatisticsAccuracyWeatherCityYearHisDAO statisticsAccuracyWeatherCityYearHisDAO;

    @Autowired
    private StatisticsAccuracyWeatherCityMonthHisService statisticsAccuracyWeatherCityMonthHisService;

    @Override
    public List<StatisticsAccuracyWeatherCityYearHisDO> findByCityAndType(String cityId, Integer type, String startDate,
                                                                          String endDate) throws Exception {
        return statisticsAccuracyWeatherCityYearHisDAO.find(cityId, type, startDate, endDate);
    }

    @Override
    public void doStatAccuracyWeatherCityYear(String cityId, Date startDate, Date endDate, Integer type) throws Exception {
        String startYearMonth = DateUtils.date2String(startDate, DateFormatType.YEAR_MONTH_STR);
        String endYearMonth = DateUtils.date2String(endDate, DateFormatType.YEAR_MONTH_STR);
        List<StatisticsAccuracyWeatherCityMonthHisDO> statisticsAccuracyWeatherCityMonthHisVOS = null;
        statisticsAccuracyWeatherCityMonthHisVOS = statisticsAccuracyWeatherCityMonthHisService.findByCityAndType(cityId, type, startYearMonth, endYearMonth);

        if (!CollectionUtils.isEmpty(statisticsAccuracyWeatherCityMonthHisVOS)) {
            statisticsAccuracyWeatherCityMonthHisVOS.forEach(statisticsAccuracyWeatherCityMonthHisVO -> {
                try {
                    StatisticsAccuracyWeatherCityYearHisDO accYear = new StatisticsAccuracyWeatherCityYearHisDO();
                    accYear.setCityId(statisticsAccuracyWeatherCityMonthHisVO.getCityId());
                    accYear.setType(statisticsAccuracyWeatherCityMonthHisVO.getType());
                    accYear.setYear(statisticsAccuracyWeatherCityMonthHisVO.getYear());
                    String methodName = "setMonth" + statisticsAccuracyWeatherCityMonthHisVO.getMonth();
                    Method method = StatisticsAccuracyWeatherCityYearHisDO.class.getMethod(methodName, BigDecimal.class);
                    method.invoke(accYear, statisticsAccuracyWeatherCityMonthHisVO.getAvg());
                    accYear.setAvg(calcAvg(accYear));

                    List<StatisticsAccuracyWeatherCityYearHisDO> yearAccVos = this.findWeatherAccuracyYear(accYear.getCityId(), accYear.getType(), accYear.getYear());

                    if (!CollectionUtils.isEmpty(yearAccVos)) {
                        StatisticsAccuracyWeatherCityYearHisDO old = yearAccVos.get(0);
                        method.invoke(old, statisticsAccuracyWeatherCityMonthHisVO.getAvg());
                        old.setAvg(calcAvg(old));
                        this.getDefaultDao().update(old);
                    } else {
                        this.getDefaultDao().create(accYear);
                    }
                } catch (Exception e) {
                    logger.error("统计气象准确率异常...", e);
                }
            });
        }


    }

    private BigDecimal calcAvg(StatisticsAccuracyWeatherCityYearHisDO accYear) {
        List<BigDecimal> accuracyList = new ArrayList<>();
        try {
            for (int i = 1; i < 13; i++) {
                String methodName = "getMonth" + i;
                if (i < 10) {
                    methodName = "getMonth" + 0 + i;
                }
                Method method = StatisticsAccuracyWeatherCityYearHisDO.class.getMethod(methodName,null);
                BigDecimal accuracy = (BigDecimal) method.invoke(accYear,null);
                if (accuracy != null) {
                    accuracyList.add(accuracy);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
        if (!CollectionUtils.isEmpty(accuracyList)) {
            return com.tsieframework.core.base.vo.util.BasePeriodUtils.getMaxMinAvg(accuracyList,4).get("avg");
        }
        return null;
    }

    private List<StatisticsAccuracyWeatherCityYearHisDO> findWeatherAccuracyYear(String cityId, Integer type, String year) throws Exception {
        DBQueryParamBuilder dbQueryParamBuilder = DBQueryParamBuilder.create();
        if (StringUtils.isNotBlank(cityId)) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo,"cityId",cityId);
        }
        if (StringUtils.isNotBlank(year)) {
            dbQueryParamBuilder.where(QueryOp.StringEqualTo,"year",year);
        }
        if (type != null) {
            dbQueryParamBuilder.where(QueryOp.NumberEqualTo,"type",type);
        }
        return this.getDefaultDao().query(dbQueryParamBuilder.build()).getDatas();
    }

    private StatisticsAccuracyWeatherCityYearHisDAO getDefaultDao() throws Exception {
        return statisticsAccuracyWeatherCityYearHisDAO;
    }


}
