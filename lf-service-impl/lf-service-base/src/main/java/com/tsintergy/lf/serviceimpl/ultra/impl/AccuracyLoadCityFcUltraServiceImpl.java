package com.tsintergy.lf.serviceimpl.ultra.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;

import com.tsintergy.lf.serviceapi.base.ultra.api.AccuracyLoadCityFcUltraService;
import com.tsintergy.lf.serviceapi.base.ultra.pojo.AccuracyLoadCityFcUltraDO;
import com.tsintergy.lf.serviceimpl.ultra.dao.AccuracyLoadCityFcUltraServiceDAO;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * @Description
 * @Date 2023/1/31 16:11
 * <AUTHOR>
 **/
@Service
public class AccuracyLoadCityFcUltraServiceImpl implements AccuracyLoadCityFcUltraService {

    @Autowired
    AccuracyLoadCityFcUltraServiceDAO accuracyLoadCityFcUltraServiceDAO;

    @Override
    public List<AccuracyLoadCityFcUltraDO> findShortMultipointFc(Date startDate, Date endDate, String cityId,
        String caliberId, Integer timeSpan, String algorithmId, Integer multipointType) {
        return accuracyLoadCityFcUltraServiceDAO.findAll(
                JpaWrappers.<AccuracyLoadCityFcUltraDO>query()
                        .eq(!StringUtils.isEmpty(cityId), "cityId", cityId)
                        .eq(!StringUtils.isEmpty(caliberId), "caliberId", caliberId)
                        .eq(!StringUtils.isEmpty(algorithmId), "algorithmId", algorithmId)
                        .eq(!Objects.isNull(multipointType), "multipointType", multipointType)
                        .eq(!Objects.isNull(timeSpan), "tvMeta.delta", timeSpan)
                        .ge(!Objects.isNull(startDate), "dateTime", startDate)
                        .le(!Objects.isNull(endDate), "dateTime", endDate)
        );
    }

    @Override
    public void doSaveOrUpdateList(List<AccuracyLoadCityFcUltraDO> accuracyLoadCityFcDOS) {
        accuracyLoadCityFcUltraServiceDAO.saveOrUpdateBatchByTemplate(accuracyLoadCityFcDOS);
    }

}
