package com.tsintergy.lf.serviceimpl.bgd.impl;


import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.service.BaseFacadeServiceImpl;
import com.tsintergy.lf.core.enums.TenDaysTypeEnum;
import com.tsintergy.lf.core.enums.WeatherColumnEnum;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.bgd.api.MonthPageService;
import com.tsintergy.lf.serviceapi.base.bgd.api.WeatherSituationService;
import com.tsintergy.lf.serviceapi.base.bgd.dto.TempCompareDTO;
import com.tsintergy.lf.serviceapi.base.bgd.pojo.WeatherSituationDO;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherEnum;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityDayFcService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityDayHisService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayFcDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayHisDO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.CriteriaBuilder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 月度页面所涉及接口实现
 */
@Service("monthPageService")
public class MonthPageServiceImpl extends BaseFacadeServiceImpl implements MonthPageService {

    @Autowired
    private WeatherFeatureCityDayFcService weatherFeatureCityDayFcService;

    @Autowired
    private WeatherFeatureCityDayHisService weatherFeatureCityDayHisService;

    @Autowired
    private WeatherSituationService weatherSituationService;

    @Override
    public String getWeatherInfo(String cityId, Date date) {
        String monthDate = DateUtil.getMonthDate(date);
        String yearByDate = DateUtil.getYearByDate(date);
        WeatherSituationDO weatherSituationDO = weatherSituationService.getWeatherSituationDO(cityId, yearByDate, monthDate);
        if (weatherSituationDO != null) {
            return weatherSituationDO.getSituation();
        }
        return null;
    }

    @Override
    public List<TempCompareDTO> getTempCompareDTOList(String cityId, Date date, Integer type) throws Exception {
        List<TempCompareDTO> tempCompareDTOList = new ArrayList<>(16);
        Date firstDayDateOfMonth = DateUtil.getFirstDayDateOfMonth(date);
        Date lastDayOfMonth = DateUtil.getLastDayOfMonth(date);
        List<WeatherFeatureCityDayFcDO> weatherFeatureCityDayFcDOList = weatherFeatureCityDayFcService.getWeatherFeatureCityDayFcDOList(cityId, firstDayDateOfMonth, lastDayOfMonth);
        List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisDOS = weatherFeatureCityDayHisService.listWeatherFeatureCityDayHisDO(cityId, firstDayDateOfMonth, lastDayOfMonth);
        for (TenDaysTypeEnum tenDaysTypeEnum : TenDaysTypeEnum.values()){
            TempCompareDTO tempCompareDTO = new TempCompareDTO();
            tempCompareDTO.setType(tenDaysTypeEnum.getName());
            List<Date> dates = genTenDayList(firstDayDateOfMonth, tenDaysTypeEnum.getType());
            if (WeatherColumnEnum.HIGHESTTEMPERATURE.getId().equals(type)){
                Optional<WeatherFeatureCityDayFcDO> maxFc = weatherFeatureCityDayFcDOList
                        .stream()
                        .filter(t -> dates.contains(t.getDate()))
                        .max(Comparator.comparing(WeatherFeatureCityDayFcDO::getHighestTemperature));
                maxFc.ifPresent(weatherFeatureCityDayFcDO -> tempCompareDTO.setFcTemp(weatherFeatureCityDayFcDO.getHighestTemperature()));
                Optional<WeatherFeatureCityDayHisDO> maxHis = weatherFeatureCityDayHisDOS
                        .stream()
                        .filter(t -> dates.contains(t.getDate()))
                        .max(Comparator.comparing(WeatherFeatureCityDayHisDO::getHighestTemperature));
                maxHis.ifPresent(weatherFeatureCityDayHisDO -> tempCompareDTO.setHisTemp(weatherFeatureCityDayHisDO.getHighestTemperature()));
                tempCompareDTOList.add(tempCompareDTO);
            }else if (WeatherColumnEnum.LOWESTTEMPERATURE.getId().equals(type)){
                Optional<WeatherFeatureCityDayFcDO> maxFc = weatherFeatureCityDayFcDOList
                        .stream()
                        .filter(t -> dates.contains(t.getDate()))
                        .max(Comparator.comparing(WeatherFeatureCityDayFcDO::getLowestTemperature));
                maxFc.ifPresent(weatherFeatureCityDayFcDO -> tempCompareDTO.setFcTemp(weatherFeatureCityDayFcDO.getLowestTemperature()));
                Optional<WeatherFeatureCityDayHisDO> maxHis = weatherFeatureCityDayHisDOS
                        .stream()
                        .filter(t -> dates.contains(t.getDate()))
                        .max(Comparator.comparing(WeatherFeatureCityDayHisDO::getLowestTemperature));
                maxHis.ifPresent(weatherFeatureCityDayHisDO -> tempCompareDTO.setHisTemp(weatherFeatureCityDayHisDO.getLowestTemperature()));
                tempCompareDTOList.add(tempCompareDTO);
            }else if (WeatherColumnEnum.AVETEMPERATURE.getId().equals(type)){
                Optional<WeatherFeatureCityDayFcDO> maxFc = weatherFeatureCityDayFcDOList
                        .stream()
                        .filter(t -> dates.contains(t.getDate()))
                        .max(Comparator.comparing(WeatherFeatureCityDayFcDO::getAveTemperature));
                maxFc.ifPresent(weatherFeatureCityDayFcDO -> tempCompareDTO.setFcTemp(weatherFeatureCityDayFcDO.getAveTemperature()));
                Optional<WeatherFeatureCityDayHisDO> maxHis = weatherFeatureCityDayHisDOS
                        .stream()
                        .filter(t -> dates.contains(t.getDate()))
                        .max(Comparator.comparing(WeatherFeatureCityDayHisDO::getAveTemperature));
                maxHis.ifPresent(weatherFeatureCityDayHisDO -> tempCompareDTO.setHisTemp(weatherFeatureCityDayHisDO.getAveTemperature()));
                tempCompareDTOList.add(tempCompareDTO);
            }
        }
        return tempCompareDTOList;
    }

    /**
     * 根据分旬类型获取当前旬日期范围
     * @param firstDayDateOfMonth
     * @param type
     * @return
     */
    private List<Date> genTenDayList(Date firstDayDateOfMonth,String type){
        Date endDate = null;
        if (TenDaysTypeEnum.First.getType().equals(type)){
            endDate = DateUtils.addDays(firstDayDateOfMonth, 9);
        }else if (TenDaysTypeEnum.Second.getType().equals(type)){
            firstDayDateOfMonth = DateUtils.addDays(firstDayDateOfMonth, 10);
            endDate = DateUtils.addDays(firstDayDateOfMonth, 9);
        }else {
            firstDayDateOfMonth = DateUtils.addDays(firstDayDateOfMonth, 20);
            endDate = DateUtil.getLastDayOfMonth(firstDayDateOfMonth);
        }
        return DateUtil.getListBetweenDay(firstDayDateOfMonth,endDate);
    }
}