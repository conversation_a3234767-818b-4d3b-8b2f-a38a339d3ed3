/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.trade.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsintergy.lf.serviceapi.base.trade.api.TradeLoadHisBasicService;
import com.tsintergy.lf.serviceapi.base.trade.pojo.TradeLoadHisClctDO;
import com.tsintergy.lf.serviceimpl.trade.dao.TradeLoadHisBasicDAO;
import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/8/29 15:19
 * @Version: 1.0.0
 */

@Service("tradeLoadHisBasicService")
public class TradeLoadHisBasicServiceImpl implements TradeLoadHisBasicService {

    @Autowired
    TradeLoadHisBasicDAO tradeLoadHisBasicDAO;
    @Override
    public void save(List<TradeLoadHisClctDO> list) {
        tradeLoadHisBasicDAO.saveOrUpdateBatch(list);
    }

    @Override
    public List<TradeLoadHisClctDO> findTradeLoadHisBasicDO(String tradeCode, Date startDate, Date endDate) {
        return tradeLoadHisBasicDAO.findAll(JpaWrappers.<TradeLoadHisClctDO>lambdaQuery()
            .eq(!StringUtils.isEmpty(tradeCode), TradeLoadHisClctDO::getTradeCode,tradeCode)
            .le(endDate!=null, TradeLoadHisClctDO::getDataDate,endDate)
            .ge(startDate!=null, TradeLoadHisClctDO::getDataDate,startDate)
        );
    }
}