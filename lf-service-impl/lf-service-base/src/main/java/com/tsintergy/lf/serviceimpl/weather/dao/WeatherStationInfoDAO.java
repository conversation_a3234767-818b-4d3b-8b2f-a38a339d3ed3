/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.weather.dao;

import com.tsieframework.core.base.dao.hibernate.BaseAbstractDAO;
import com.tsieframework.core.base.dao.hibernate.BaseDAO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherStationHisBasicDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherStationInfoDO;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/5/31 10:41
 * @Version: 1.0.0
 */
@Component
public class WeatherStationInfoDAO extends BaseAbstractDAO<WeatherStationInfoDO> {


    public List<WeatherStationInfoDO> getWeatherStationInfo(String cityId,String stationId){
        DBQueryParam param = DBQueryParamBuilder.create().build();
        DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (null != stationId) {
            param.getQueryConditions().put("_ne_stationId", stationId);
        }
        if (null != cityId) {
            param.getQueryConditions().put("_ne_cityId", stationId);
        }
        List<WeatherStationInfoDO> datas = this.query(param).getDatas();
        return datas;
    }
}