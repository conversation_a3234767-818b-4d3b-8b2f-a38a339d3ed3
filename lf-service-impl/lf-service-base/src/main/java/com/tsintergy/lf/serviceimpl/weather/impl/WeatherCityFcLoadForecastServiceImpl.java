/**
 *   
 *  Copyright (C), 2015‐2020, 北京清能互联科技有限公司  
 *  Author:  EDZ  
 *  Date:  2020/10/12 14:06  
 *  History:  
 *  <author> <time> <version> <desc> 
 */
package com.tsintergy.lf.serviceimpl.weather.impl;

import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.util.compatible.BigDecimalUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.evalucation.api.BatchDataFilterService;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherEnum;
import com.tsintergy.lf.serviceapi.base.system.api.SettingBatchInitService;
import com.tsintergy.lf.serviceapi.base.system.pojo.SettingBatchInitDO;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcLoadForecastService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcService;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherDTO;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherFeatureDTO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcLoadForecastDO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherCityFcDAO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherCityFcLoadForecastDAO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 *   
 *  Description:  <br> 
 *  
 *  <AUTHOR>  
 *  @create 2020/10/12  
 *  @since 1.0.0  
 */
@Slf4j
@Service("weatherCityFcLoadForecastService")
public class WeatherCityFcLoadForecastServiceImpl implements WeatherCityFcLoadForecastService {

    private static final Integer FIRST_INDEX = 0;
    @Autowired
    WeatherCityFcDAO weatherCityFcDAO;

    @Autowired
    WeatherCityFcService weatherCityFcService;

    @Autowired
    WeatherCityFcLoadForecastService weatherCityFcLoadForecastService;
    @Autowired
    private WeatherCityFcLoadForecastDAO weatherCityFcLoadForecastDAO;
    @Autowired
    private CityService cityService;
    @Autowired
    private SettingBatchInitService settingBatchInitService;

    @Autowired
    private BatchDataFilterService batchDataFilterService;

    /**
     * 查询预测使用气象时间
     *
     * @param cityId
     * @param type
     * @param algorithmId
     * @param date
     * @return
     * @throws Exception
     */
    @Override
    public Date findWeatherFcTime(String cityId, Integer type, List<String> algorithmId, Date date, String caliberId) throws Exception {

        Date weatherFcTime = null;
        List<WeatherCityFcLoadForecastDO> weatherCityFcLoadForecastDOList = weatherCityFcLoadForecastDAO.findWeatherInfoFcLoadForecast(cityId, type, algorithmId, date, caliberId);
        List<Date> list = new ArrayList<>();
        for (WeatherCityFcLoadForecastDO weatherCityFcLoadForecastDO : weatherCityFcLoadForecastDOList) {
            Date dateTime = null;
            if (weatherCityFcLoadForecastDO.getUpdatetime() != null) {
                dateTime = weatherCityFcLoadForecastDO.getUpdatetime();
            } else {
                dateTime = weatherCityFcLoadForecastDO.getCreatetime();
            }

            if (dateTime != null) {
                list.add(dateTime);
            }
        }
        if (!CollectionUtils.isEmpty(list)) {
            weatherFcTime = Collections.min(list);
        }

        return weatherFcTime;
    }

    @Override
    public List<WeatherDTO> findWeatherFc(String cityId, Integer type, List<String> algorithmId, Date date, String caliberId) throws Exception {
        List<WeatherDTO> list = new ArrayList<>();
        List<WeatherCityFcLoadForecastDO> weatherInfoFcLoadForecast = weatherCityFcLoadForecastDAO.findWeatherInfoFcLoadForecast(cityId, type, algorithmId, date, caliberId);
        for (WeatherCityFcLoadForecastDO weatherCityFcLoadForecastDO : weatherInfoFcLoadForecast) {
            WeatherDTO weatherDTO = new WeatherDTO();
            weatherDTO.setCity(weatherCityFcLoadForecastDO.getCityId());
            weatherDTO.setType(weatherCityFcLoadForecastDO.getType());
            weatherDTO.setDate(weatherCityFcLoadForecastDO.getDate());
            weatherDTO.setAlgorithm(AlgorithmEnum.findById(weatherCityFcLoadForecastDO.getAlgorithmId()).getDescription());

            weatherDTO.setData(BasePeriodUtils
                    .toList(weatherCityFcLoadForecastDO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO));
            list.add(weatherDTO);
        }
        return list;
    }

    @Override
    public List<WeatherFeatureDTO> findWeatherFeature(String cityId, Integer type, List<String> algorithmIds, Date date, List<WeatherFeatureDTO> weatherFeatureList, String caliberId) throws Exception {

        if (weatherFeatureList == null) {
            throw new BusinessException("T100", "气象特性列表为空");
        }
        for (String algorithmId : algorithmIds) {
            List<WeatherCityFcLoadForecastDO> weatherCityFcLoadForecastDOList = weatherCityFcLoadForecastDAO.findWeatherInfoFcLoadForecast(cityId, type, algorithmId, date, caliberId);
            if (!CollectionUtils.isEmpty(weatherCityFcLoadForecastDOList)) {
                WeatherFeatureDTO weatherFeatureDTO = new WeatherFeatureDTO("");
                for (WeatherCityFcLoadForecastDO weatherCityFcLoadForecastDO : weatherCityFcLoadForecastDOList) {
                    List<BigDecimal> weatherList = BasePeriodUtils.toList(weatherCityFcLoadForecastDO, Constants.WEATHER_CURVE_POINT_NUM,
                            Constants.WEATHER_CURVE_START_WITH_ZERO);
                    Map<String, BigDecimal> maxMixAvg = BasePeriodUtils.getMaxMinAvg(weatherList, 4);


                    if (WeatherEnum.HUMIDITY.value().equals(weatherCityFcLoadForecastDO.getType())) {
                        weatherFeatureDTO.setAveHumidity(maxMixAvg.get("avg"));
                    } else if (WeatherEnum.TEMPERATURE.value().equals(weatherCityFcLoadForecastDO.getType())) {
                        weatherFeatureDTO.setHighestTemperature(maxMixAvg.get("max"));
                        weatherFeatureDTO.setLowestTemperature(maxMixAvg.get("min"));
                    } else if (WeatherEnum.RAINFALL.value().equals(weatherCityFcLoadForecastDO.getType())) {
                        List<BigDecimal> bigDecimals = BasePeriodUtils
                                .toList(weatherCityFcLoadForecastDO, Constants.WEATHER_CURVE_POINT_NUM_24, Constants.WEATHER_CURVE_START_WITH_ZERO);
                        bigDecimals.set(FIRST_INDEX, BigDecimal.ZERO);
                        if (weatherCityFcLoadForecastDO.getT2400() != null) {
                            bigDecimals.add(weatherCityFcLoadForecastDO.getT2400());
                            weatherFeatureDTO.setRainfall(BigDecimalUtils.addAllValue(bigDecimals));
                        } else {
                            java.sql.Date srcDate = weatherCityFcLoadForecastDO.getDate();
                            java.sql.Date tomorrowDate = new java.sql.Date(
                                    DateUtils.addDays(new Date(srcDate.getTime()), 1).getTime());
                            WeatherCityFcDO tomorrowData = weatherCityFcService
                                    .findWeatherCityFcDO(weatherCityFcLoadForecastDO.getCityId(), WeatherEnum.RAINFALL.getType(), tomorrowDate);
                            BigDecimal T0000 = null;
                            if (tomorrowData != null) {
                                T0000 = tomorrowData.getT0000();
                            }
                            bigDecimals.add(T0000);
                            weatherFeatureDTO.setRainfall(BigDecimalUtils.addAllValue(bigDecimals));
                        }
                    }

                }
                weatherFeatureDTO.setWeatherName(
                        AlgorithmEnum.findById(weatherCityFcLoadForecastDOList.get(0).getAlgorithmId()).getDescription());
                weatherFeatureList.add(weatherFeatureDTO);
            }
        }

        return weatherFeatureList;
    }


    /**
     * 插入或者更新预测时气象数据信息
     *
     * @param cityId
     * @param date
     * @param
     * @param systemAlgorithmId
     */
    @Override
    public void insertOrUpdateWeatherInfo(String cityId, Date date, String systemAlgorithmId, String caliberId, Integer batchId) {
        try {
            String weatherDataCityId = cityService.findWeatherCityId(cityId);
            List<WeatherCityFcDO> weatherCityFcDOS = weatherCityFcService
                    .findWeatherCityFcDOs(weatherDataCityId, date, date);
            for (WeatherCityFcDO weatherCityFcDO : weatherCityFcDOS) {
                WeatherCityFcLoadForecastDO weatherCityFcLoadForecastDO = new WeatherCityFcLoadForecastDO(
                        weatherDataCityId, systemAlgorithmId, weatherCityFcDO.getType(), date, caliberId);
                BasePeriodUtils.setAllFiled(weatherCityFcLoadForecastDO,
                        BasePeriodUtils.toMap(weatherCityFcDO, 96, Constants.LOAD_CURVE_START_WITH_ZERO));
                weatherCityFcLoadForecastDO.setBatchId(batchId);
                Timestamp dateTime = weatherCityFcDO.getUpdatetime() == null ? weatherCityFcDO.getCreatetime()
                        : weatherCityFcDO.getUpdatetime();
                weatherCityFcLoadForecastDO.setCreatetime(dateTime);
                weatherCityFcLoadForecastDO.setUpdatetime(dateTime);
                weatherCityFcLoadForecastService.doSaveOrUpdate(weatherCityFcLoadForecastDO);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public List<WeatherCityFcLoadForecastDO> findWeatherInfoFcLoadForecast(String cityId, Integer type, String algorithmId, Date date, String caliberId) throws Exception {
        return weatherCityFcLoadForecastDAO.findWeatherInfoFcLoadForecast(cityId, type, algorithmId, date, caliberId);
    }

    @Override
    public List<WeatherCityFcLoadForecastDO> findWeatherInfoFcLoadForecastByBatchId(String cityId, Integer type,
                                                                                    String algorithmId, Date date, String caliberId, Integer batchId) throws Exception {
        return weatherCityFcLoadForecastDAO
                .findWeatherInfoFcLoadForecast(cityId, type, algorithmId, date, date, caliberId, batchId);
    }

    @Override
    public List<WeatherCityFcLoadForecastDO> findWeatherInfoFcLoadForecast(String cityId, Integer type,
                                                                           String algorithmId, Date startDate, Date endDate, String caliberId) throws Exception {
        return weatherCityFcLoadForecastDAO
                .findWeatherInfoFcLoadForecast(cityId, type, algorithmId, startDate, endDate, caliberId, null);
    }

    @Override
    public List<WeatherCityFcLoadForecastDO> findWeatherInfoFcLoadForecastBatch(String cityId, Integer type,
                                                                                List<String> algorithmIds, List<Integer> batchIds, Date startDate, Date endDate, String caliberId)
            throws Exception {
        return weatherCityFcLoadForecastDAO.findWeatherInfoFcLoadForecastBatch(cityId, type, algorithmIds, batchIds, startDate, endDate, caliberId);
    }

    @Override
    public void doCreateAndFlush(WeatherCityFcLoadForecastDO weatherCityFcLoadForecastDO) {
        weatherCityFcLoadForecastDAO.createAndFlush(weatherCityFcLoadForecastDO);
    }

    @Override
    public void doSaveOrUpdate(WeatherCityFcLoadForecastDO weatherCityFcLoadForecastDO) {
        weatherCityFcLoadForecastDAO.saveOrUpdateEntityByTemplate(weatherCityFcLoadForecastDO);
    }

    @Override
    public void doDelete(String id) throws Exception {
        weatherCityFcLoadForecastDAO.deleteByPk(id);
    }

    @Override
    public List<WeatherCityFcLoadForecastDO> findWeatherFcLoadForecastBatchByType(String cityId, String algorithmId, String caliberId, String batchId,
                                                                                  Integer day, Integer type, Date startDate, Date endDate) throws Exception {
        List<WeatherCityFcLoadForecastDO> weather = weatherCityFcLoadForecastDAO.findWeatherInfoFcLoadForecastBatch(cityId, type, Arrays.asList(algorithmId), null, startDate, endDate, caliberId);
        return batchDataFilterService.filterWeatherForecastDOByBatchId(weather, cityId, batchId, day);
    }
}