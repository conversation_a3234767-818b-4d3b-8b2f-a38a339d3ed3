/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.bgd.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsieframework.core.base.service.BaseFacadeServiceImpl;
import com.tsintergy.lf.serviceapi.base.bgd.api.StatisticsCityMonthFcFeatureService;
import com.tsintergy.lf.serviceapi.base.bgd.pojo.StatisticsCityMonthFcFeatureServiceDO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.StatisticsCityDayFcFeatureDO;
import com.tsintergy.lf.serviceimpl.bgd.dao.StatisticsCityMonthFcFeatureServiceDAO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/11/17 21:23
 * @Version: 1.0.0
 */

@Service("statisticsCityMonthFcFeatureService")
public class StatisticsCityMonthFcFeatureServiceImpl extends BaseFacadeServiceImpl implements
        StatisticsCityMonthFcFeatureService {

    @Autowired
    StatisticsCityMonthFcFeatureServiceDAO statisticsCityMonthFcFeatureServiceDAO;
    @Override
    public List<StatisticsCityMonthFcFeatureServiceDO> findStatisticsCityMonthFcFeature(String cityId, String caliberId,
                                                                                        String algorithmId, String year, String month) {
        return  statisticsCityMonthFcFeatureServiceDAO.findAll(JpaWrappers.<StatisticsCityMonthFcFeatureServiceDO>lambdaQuery()
            .eq(!StringUtils.isEmpty(cityId),StatisticsCityMonthFcFeatureServiceDO::getCityId,cityId)
            .eq(!StringUtils.isEmpty(caliberId),StatisticsCityMonthFcFeatureServiceDO::getCaliberId,caliberId)
            .eq(!StringUtils.isEmpty(algorithmId),StatisticsCityMonthFcFeatureServiceDO::getAlgorithmId,algorithmId)
            .eq(!StringUtils.isEmpty(year),StatisticsCityMonthFcFeatureServiceDO::getYear,year)
            .eq(!StringUtils.isEmpty(month),StatisticsCityMonthFcFeatureServiceDO::getMonth,month)
        );
    }

    @Override
    public List<StatisticsCityMonthFcFeatureServiceDO> findStatisticsCityMonthFcSeasonFeature(String cityId,
        String caliberId, String algorithmId, String year, List<String> months) {

        return  statisticsCityMonthFcFeatureServiceDAO.findAll(JpaWrappers.<StatisticsCityMonthFcFeatureServiceDO>lambdaQuery()
            .eq(!StringUtils.isEmpty(cityId),StatisticsCityMonthFcFeatureServiceDO::getCityId,cityId)
            .eq(!StringUtils.isEmpty(caliberId),StatisticsCityMonthFcFeatureServiceDO::getCaliberId,caliberId)
            .eq(!StringUtils.isEmpty(algorithmId),StatisticsCityMonthFcFeatureServiceDO::getAlgorithmId,algorithmId)
            .eq(!StringUtils.isEmpty(year),StatisticsCityMonthFcFeatureServiceDO::getYear,year)
            .in(!CollectionUtils.isEmpty(months),StatisticsCityMonthFcFeatureServiceDO::getMonth,months)
        );
    }

    @Override
    public List<StatisticsCityMonthFcFeatureServiceDO> findStatisticsCityMonthFcFeatures(String cityId, String caliberId,
        String algorithmId, String startYm, String endYm) {
        List<StatisticsCityMonthFcFeatureServiceDO> cityMonthFcFeatureServiceDOS = statisticsCityMonthFcFeatureServiceDAO
            .findAll(JpaWrappers.<StatisticsCityMonthFcFeatureServiceDO>lambdaQuery()
                .eq(!StringUtils.isEmpty(cityId), StatisticsCityMonthFcFeatureServiceDO::getCityId, cityId)
                .eq(!StringUtils.isEmpty(caliberId), StatisticsCityMonthFcFeatureServiceDO::getCaliberId, caliberId)
                .eq(!StringUtils.isEmpty(algorithmId), StatisticsCityMonthFcFeatureServiceDO::getAlgorithmId,
                    algorithmId)
                .ge(!StringUtils.isEmpty(startYm), StatisticsCityMonthFcFeatureServiceDO::getYear,
                    startYm.substring(0, 4))
                .le(!StringUtils.isEmpty(endYm), StatisticsCityMonthFcFeatureServiceDO::getMonth,
                    endYm.substring(0, 4)));
        List<StatisticsCityMonthFcFeatureServiceDO> result = new ArrayList<>();
        for (StatisticsCityMonthFcFeatureServiceDO cityMonthFcFeatureServiceDO : cityMonthFcFeatureServiceDOS) {
            if((cityMonthFcFeatureServiceDO.getYear()+"-"+cityMonthFcFeatureServiceDO.getMonth()).compareTo(startYm) > -1 && (cityMonthFcFeatureServiceDO.getYear()+"-"+cityMonthFcFeatureServiceDO.getMonth()).compareTo(endYm) < 1) {
                result.add(cityMonthFcFeatureServiceDO);
            }
        }
        return result;
    }

    @Override
    public List<StatisticsCityMonthFcFeatureServiceDO> findReportStatisticsMonthFcFeatureYM(String cityId,
        String caliberId, String startYm, String endYm) {
        List<StatisticsCityMonthFcFeatureServiceDO> cityMonthFcFeatureServiceDOS = statisticsCityMonthFcFeatureServiceDAO
            .findAll(JpaWrappers.<StatisticsCityMonthFcFeatureServiceDO>lambdaQuery()
                .eq(!StringUtils.isEmpty(cityId), StatisticsCityMonthFcFeatureServiceDO::getCityId, cityId)
                .eq(!StringUtils.isEmpty(caliberId), StatisticsCityMonthFcFeatureServiceDO::getCaliberId, caliberId)
                .eq(StatisticsCityMonthFcFeatureServiceDO::getReport, true)
                .ge(!StringUtils.isEmpty(startYm), StatisticsCityMonthFcFeatureServiceDO::getYear,
                    startYm.substring(0, 4))
                .le(!StringUtils.isEmpty(endYm), StatisticsCityMonthFcFeatureServiceDO::getMonth,
                    endYm.substring(0, 4)));
        List<StatisticsCityMonthFcFeatureServiceDO> result = new ArrayList<>();
        for (StatisticsCityMonthFcFeatureServiceDO cityMonthFcFeatureServiceDO : cityMonthFcFeatureServiceDOS) {
            if((cityMonthFcFeatureServiceDO.getYear()+"-"+cityMonthFcFeatureServiceDO.getMonth()).compareTo(startYm) > -1 && (cityMonthFcFeatureServiceDO.getYear()+"-"+cityMonthFcFeatureServiceDO.getMonth()).compareTo(endYm) < 1) {
                result.add(cityMonthFcFeatureServiceDO);
            }
        }
        return result;
    }

    @Override
    public List<StatisticsCityMonthFcFeatureServiceDO> queryByYearAndMonths(String year, List<String> monthList) {
        return statisticsCityMonthFcFeatureServiceDAO.findAll(
                JpaWrappers.<StatisticsCityMonthFcFeatureServiceDO>lambdaQuery()
                        .eq(!StringUtils.isEmpty(year), StatisticsCityMonthFcFeatureServiceDO::getYear, year)
                        .in(!CollectionUtils.isEmpty(monthList), StatisticsCityMonthFcFeatureServiceDO::getMonth, monthList)
        );
    }

    @Override
    public void saveOrUpdateList(List<StatisticsCityMonthFcFeatureServiceDO> result) {
        for (StatisticsCityMonthFcFeatureServiceDO fcDO : result) {
            String cityId = fcDO.getCityId();
            String caliberId = fcDO.getCaliberId();
            String algorithmId = fcDO.getAlgorithmId();
            String year = fcDO.getYear();
            String month = fcDO.getMonth();
            if (!StringUtils.isEmpty(cityId)&&
                    !StringUtils.isEmpty(caliberId)&&
                    !StringUtils.isEmpty(algorithmId)&&
                    !StringUtils.isEmpty(year)&&
                    !StringUtils.isEmpty(month)){
                statisticsCityMonthFcFeatureServiceDAO.delete(
                        JpaWrappers.<StatisticsCityMonthFcFeatureServiceDO>lambdaUpdate()
                                .eq(StatisticsCityMonthFcFeatureServiceDO::getCityId, cityId)
                                .eq(StatisticsCityMonthFcFeatureServiceDO::getCaliberId, caliberId)
                                .eq(StatisticsCityMonthFcFeatureServiceDO::getAlgorithmId, algorithmId)
                                .eq(StatisticsCityMonthFcFeatureServiceDO::getYear, year)
                                .eq(StatisticsCityMonthFcFeatureServiceDO::getMonth, month)
                );
                statisticsCityMonthFcFeatureServiceDAO.saveOrUpdateBatch(Collections.singletonList(fcDO));
            }
        }
    }

    @Override
    public List<StatisticsCityMonthFcFeatureServiceDO> queryAllCityByAlgoIdAndYmAndCaliberId(String caliberId, List<String> algoIdList, String year, String month) {
        return statisticsCityMonthFcFeatureServiceDAO.findAll(
                JpaWrappers.<StatisticsCityMonthFcFeatureServiceDO>lambdaQuery()
                        .eq(!StringUtils.isEmpty(caliberId), StatisticsCityMonthFcFeatureServiceDO::getCaliberId, caliberId)
                        .eq(!StringUtils.isEmpty(year), StatisticsCityMonthFcFeatureServiceDO::getYear, year)
                        .eq(!StringUtils.isEmpty(month), StatisticsCityMonthFcFeatureServiceDO::getMonth, month)
                        .in(!CollectionUtils.isEmpty(algoIdList), StatisticsCityMonthFcFeatureServiceDO::getAlgorithmId, algoIdList)
        );
    }

    @Override
    public List<StatisticsCityMonthFcFeatureServiceDO> findReportStatisticsMonthFcFeature(String cityId, String caliberId, String year, String month) {
        return statisticsCityMonthFcFeatureServiceDAO.findAll(
                JpaWrappers.<StatisticsCityMonthFcFeatureServiceDO>lambdaQuery()
                        .eq(!StringUtils.isEmpty(caliberId), StatisticsCityMonthFcFeatureServiceDO::getCaliberId, caliberId)
                        .eq(!StringUtils.isEmpty(year), StatisticsCityMonthFcFeatureServiceDO::getYear, year)
                        .eq(!StringUtils.isEmpty(month), StatisticsCityMonthFcFeatureServiceDO::getMonth, month)
                        .eq(StatisticsCityMonthFcFeatureServiceDO::getReport, true)
        );    }
}
