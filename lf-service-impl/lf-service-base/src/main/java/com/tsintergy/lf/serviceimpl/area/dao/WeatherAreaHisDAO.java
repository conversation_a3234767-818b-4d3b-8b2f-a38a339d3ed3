package com.tsintergy.lf.serviceimpl.area.dao;

import com.tsieframework.core.base.dao.hibernate.BaseAbstractDAO;
import com.tsieframework.core.base.dao.hibernate.BaseDAO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.dao.jpa.BaseJpaDAO;
import com.tsintergy.lf.serviceapi.base.area.pojo.WeatherAreaHisDO;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/***
 * 功能描述:<br>
 * @Version: 1.0.0
 * @Author:<EMAIL>
 * @Date: 2022/8/3 17:21
 */
@Component
public interface WeatherAreaHisDAO extends BaseJpaDAO<WeatherAreaHisDO,String> {

}