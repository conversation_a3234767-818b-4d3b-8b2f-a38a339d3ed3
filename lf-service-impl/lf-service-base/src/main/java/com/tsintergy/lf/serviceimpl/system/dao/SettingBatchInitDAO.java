package com.tsintergy.lf.serviceimpl.system.dao;


import com.tsieframework.core.base.dao.hibernate.*;
import com.tsintergy.lf.serviceapi.base.system.pojo.SettingBatchInitDO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class SettingBatchInitDAO extends BaseAbstractDAO<SettingBatchInitDO> {

    public List<SettingBatchInitDO> getBatchList() {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        List<SettingBatchInitDO> settingSystemVOs = this.query(param).getDatas();
        return settingSystemVOs;
    }

    public SettingBatchInitDO getBatchByCondition(String batchName, String cityId) {
        DBQueryParamBuilder builder = DBQueryParamBuilder.create().queryDataOnly();
        builder.where(QueryOp.StringEqualTo, "name", batchName);
        builder.where(QueryOp.StringEqualTo, "cityId", cityId);
        List<SettingBatchInitDO> datas = this.query(builder.build()).getDatas();
        if (CollectionUtils.isNotEmpty(datas)) {
            return datas.get(0);
        }
        return null;
    }

    public List<SettingBatchInitDO> getListByCityId(String cityId) {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (cityId != null) {
            param.getQueryConditions().put("_se_cityId", cityId);
        }
        List<SettingBatchInitDO> settingSystemVOs = this.query(param).getDatas();
        return settingSystemVOs;
    }

    public SettingBatchInitDO getBatchById(String id) {
        DBQueryParamBuilder builder = DBQueryParamBuilder.create().queryDataOnly();
        builder.where(QueryOp.StringEqualTo, "id", id);
        List<SettingBatchInitDO> datas = this.query(builder.build()).getDatas();
        if (CollectionUtils.isNotEmpty(datas)) {
            return datas.get(0);
        }
        return null;
    }
}