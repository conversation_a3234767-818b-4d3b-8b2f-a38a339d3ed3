package com.tsintergy.lf.serviceimpl.weather.impl;

import com.tsintergy.lf.core.enums.WeatherSourceEnum;
import com.tsintergy.lf.serviceapi.base.forecast.api.AlgorithmService;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.AlgorithmDO;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityHisAdapterService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityHisMeteoService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityDayHisMeteoService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityDayHisService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisMeteoDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayHisDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayHisMeteoDO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherCityHisDAO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service("weatherCityHisAdapterService")
public class WeatherCityHisAdapterServiceImpl implements WeatherCityHisAdapterService {

    @Autowired
    private WeatherCityHisDAO weatherCityHisDAO;

    @Autowired
    private WeatherCityHisMeteoService weatherCityHisMeteoService;

    @Autowired
    private AlgorithmService algorithmService;

    @Autowired
    private WeatherFeatureCityDayHisService weatherFeatureCityDayHisService;

    @Autowired
    WeatherFeatureCityDayHisMeteoService weatherFeatureCityDayHisMeteoService;

    @Autowired
    private WeatherFeatureStatServiceImpl weatherFeatureStatService;

    @Override
    public List<WeatherCityHisDO> findHisWeather(String cityId, String algorithmId, Integer type, Date start, Date end) throws Exception {
        if (StringUtils.isEmpty(algorithmId)) {
            return weatherCityHisDAO.findWeatherCityHisDO(cityId, type, start, end);
        }
        AlgorithmDO algorithmDO = algorithmService.getAlgorithmDOById(algorithmId);
        if (algorithmDO == null || algorithmDO.getHisWeatherSource().equals(WeatherSourceEnum.HIS.name())) {
            return weatherCityHisDAO.findWeatherCityHisDO(cityId, type, start, end);
        }
        if (algorithmDO.getHisWeatherSource().equalsIgnoreCase(WeatherSourceEnum.METEO.name())) {
            List<WeatherCityHisMeteoDO> HisMeteoDOS = weatherCityHisMeteoService.getListByCondition(cityId, type, start, end);
            if (CollectionUtils.isNotEmpty(HisMeteoDOS)) {
                return HisMeteoDOS.stream().map(
                        src -> {
                            WeatherCityHisDO weatherCityHisDO = new WeatherCityHisDO();
                            BeanUtils.copyProperties(src, weatherCityHisDO);
                            return weatherCityHisDO;
                        }).collect(Collectors.toList());
            }
        }
        return weatherCityHisDAO.findWeatherCityHisDO(cityId, type, start, end);
    }

    @Override
    public List<WeatherCityHisDO> findWeatherCityHisDOS(String sourceType, String cityId, Integer type, Date start, Date end) throws Exception {
        if (WeatherSourceEnum.METEO.name().equals(sourceType)) {
            List<WeatherCityHisMeteoDO> HisMeteoDOS = weatherCityHisMeteoService.getListByCondition(cityId, type, start, end);
            if (CollectionUtils.isNotEmpty(HisMeteoDOS)) {
                return HisMeteoDOS.stream().map(
                        src -> {
                            WeatherCityHisDO weatherCityHisDO = new WeatherCityHisDO();
                            BeanUtils.copyProperties(src, weatherCityHisDO);
                            return weatherCityHisDO;
                        }).sorted(Comparator.comparing(WeatherCityHisDO::getDate)).collect(Collectors.toList());
            }
        }
        return weatherCityHisDAO.findWeatherCityHisDO(cityId, type, start, end);
    }

    @Override
    public List<WeatherCityHisDO> findHisWeather(String sourceType, String cityId, Date start, Date endDate) throws Exception {
        return this.findWeatherCityHisDOS(sourceType, cityId, null, start, endDate);
    }

    @Override
    public List<WeatherFeatureCityDayHisDO> findWeatherFeatureCityDayHisDO(String sourceType, String cityId, Date startDate, Date endDate) throws Exception {
        if (WeatherSourceEnum.METEO.name().equals(sourceType)) {
            List<WeatherFeatureCityDayHisMeteoDO> weatherFeatureCityDayHisMeteoDO = weatherFeatureCityDayHisMeteoService.findWeatherFeatureCityDayHisMeteoDO(cityId, startDate, endDate);
            return  weatherFeatureCityDayHisMeteoDO.stream().map(item->{
                WeatherFeatureCityDayHisDO entity = new WeatherFeatureCityDayHisDO();
                BeanUtils.copyProperties(item, entity);
                return entity;
            }).sorted(Comparator.comparing(WeatherFeatureCityDayHisDO::getDate)).collect(Collectors.toList());
            }
        return weatherFeatureCityDayHisService.listWeatherFeatureCityDayHisDO(cityId, startDate, endDate);
    }

    @Override
    public List<WeatherFeatureCityDayHisDO> findWeatherFeatureByAlgorithmId(String algorithmId, String cityId, Date startDate, Date endDate) throws Exception {
        if (StringUtils.isEmpty(algorithmId)) {
            return weatherFeatureCityDayHisService.listWeatherFeatureCityDayHisDO(cityId, startDate, endDate);
        }
        AlgorithmDO algorithmDO = algorithmService.getAlgorithmDOById(algorithmId);
        if (algorithmDO == null || algorithmDO.getHisWeatherSource().equals(WeatherSourceEnum.HIS.name())) {
            return weatherFeatureCityDayHisService.listWeatherFeatureCityDayHisDO(cityId, startDate, endDate);
        }

        if (algorithmDO.getHisWeatherSource().equalsIgnoreCase(WeatherSourceEnum.METEO.name())) {
            List<WeatherFeatureCityDayHisMeteoDO> weatherFeatureCityDayHisMeteoDO = weatherFeatureCityDayHisMeteoService.findWeatherFeatureCityDayHisMeteoDO(cityId, startDate, endDate);
            return  weatherFeatureCityDayHisMeteoDO.stream().map(item->{
                WeatherFeatureCityDayHisDO entity = new WeatherFeatureCityDayHisDO();
                BeanUtils.copyProperties(item, entity);
                return entity;
            }).sorted(Comparator.comparing(WeatherFeatureCityDayHisDO::getDate)).collect(Collectors.toList());
        }
        return weatherFeatureCityDayHisService.listWeatherFeatureCityDayHisDO(cityId, startDate, endDate);
    }
}
