/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.bgd.impl;

import com.alibaba.nacos.common.utils.MapUtils;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.math.BigDecimalFunctions;
import com.tsieframework.core.base.service.BaseFacadeServiceImpl;
import com.tsintergy.lf.core.constants.AlgorithmConstants;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.enums.SeasonAlgorithmModelEnum;
import com.tsintergy.lf.core.enums.SeasonAlgorithmModelEnums;
import com.tsintergy.lf.core.enums.WeatherColumnEnum;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.core.util.LoadCalUtil;
import com.tsintergy.lf.core.util.QuarterUtil;
import com.tsintergy.lf.serviceapi.base.bgd.api.*;
import com.tsintergy.lf.serviceapi.base.bgd.dto.*;
import com.tsintergy.lf.serviceapi.base.bgd.pojo.LoadFeatureCityTenDaysFcServiceDO;
import com.tsintergy.lf.serviceapi.base.bgd.pojo.StatisticsCityMonthFcFeatureServiceDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityMonthFcService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityMonthHisService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityMonthHisDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCitySeasonFcDO;
import com.tsintergy.lf.serviceapi.base.system.api.ReportSystemService;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceapi.base.system.pojo.ReportSystemInitDO;
import com.tsintergy.lf.serviceapi.base.weather.api.*;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayFcDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayHisDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityMonthHisDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityMonthMdHisDO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.Map.Entry;
import java.util.stream.Collectors;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/11/19 14:14
 * @Version: 1.0.0
/**
 * Description: TODO <br>
 *
 * <AUTHOR>
 * @create 2023-02-17
 * @since 1.0.0
 */
@Service("seasonForecastModelService")
public class SeasonForecastModelServiceImpl extends BaseFacadeServiceImpl implements SeasonForecastModelService {

    @Autowired
    private StatisticsCityQuarterFcFeatureService statisticsCityQuarterFcFeatureService;

    @Autowired
    private StatisticsCityTenDaysFcFeatureService statisticsCityTenDaysFcFeatureService;

    @Autowired
    private StatisticsCityMonthFcFeatureService statisticsCityMonthFcFeatureService;

    @Autowired
    private WeatherFeatureCityDayHisService weatherFeatureCityDayHisService;

    @Autowired
    private WeatherFeatureCityDayFcService weatherFeatureCityDayFcService;

    @Autowired
    private SettingSystemService settingSystemService;

    @Autowired
    private LoadFeatureCityMonthFcService loadFeatureCityMonthFcService;

    @Autowired
    private LoadFeatureCityMonthHisService loadFeatureCityMonthHisService;

    @Autowired
    private LoadFeatureCityTenDaysFcService loadFeatureCityTenDaysFcService;

    @Autowired
    private WeatherFeatureCityTenDaysHisService weatherFeatureCityTenDaysHisService;

    @Autowired
    private WeatherFeatureCityMonthHisService weatherFeatureCityMonthHisService;

    @Autowired
    private WeatherFeatureCityMonthMdHisService weatherFeatureCityMonthMdHisService;

    @Autowired
    private ReportSystemService reportSystemService;

    @Override
    public List<TenDaysOfMonthAccuracyDTO> getHistoryAccuracy(String cityId, String caliberId, Date date,
        String year, String season) throws Exception {
        List<LoadFeatureCityMonthHisDO> hisDOList;
        List<LoadFeatureCitySeasonFcDO> fcDOList;
        if (!StringUtils.isEmpty(season)) {
            //夏季
            //预测
            fcDOList = loadFeatureCityMonthFcService.getSummerSeasonFeature(year, cityId, caliberId, null);
            // 夏季月实际
            hisDOList = loadFeatureCityMonthHisService.getSummerMonthsFeature(year, cityId, caliberId);
        } else {
            //冬季
            //实际
            hisDOList = loadFeatureCityMonthHisService
                    .getWinterMonthsFeatures(year, cityId, caliberId);
            //预测
            fcDOList = loadFeatureCityMonthFcService.getWinterSeasonFeatures(year, cityId, caliberId, null);
        }
        List<TenDaysOfMonthAccuracyDTO> result = new ArrayList<>();
        // 预测数据
        /*List<LoadFeatureCityMonthFcDO> fcDOList = loadFeatureCityMonthFcService.findLoadFeatureMonthList(
            monthList, year, cityId, caliberId, null);*/
        List<LoadFeatureCitySeasonFcDO> custom = new ArrayList<>();

        Map<String, List<LoadFeatureCitySeasonFcDO>> collect = fcDOList.stream()
            .collect(Collectors.groupingBy(LoadFeatureCitySeasonFcDO::getAlgorithmId));
        int count = 1;
        List<LoadFeatureCitySeasonFcDO> loadFeatureCitySeasonFcDOS = collect.get(
            SeasonAlgorithmModelEnums.REPORT.getId());
        if (CollectionUtils.isEmpty(loadFeatureCitySeasonFcDOS)) {
            List<LoadFeatureCitySeasonFcDO> loadFeatureFc = collect.get(
                SeasonAlgorithmModelEnums.HUBEI_CUSTOM.getId());
            if (!CollectionUtils.isEmpty(loadFeatureFc)) {
                for (LoadFeatureCitySeasonFcDO loadFeatureFcDO : loadFeatureFc) {
                    loadFeatureFcDO.setAlgorithmId("0");
                    custom.add(loadFeatureFcDO);
                }
                collect.put("0", custom);
            }
        }
        for (SeasonAlgorithmModelEnums value : SeasonAlgorithmModelEnums.values()) {
            if (!MapUtils.isEmpty(collect)) {
                Set<String> stringsKey = collect.keySet();
                for (String s : stringsKey) {
                    if (value.getId().equals(s)) {
                        BigDecimal fcMax = null;
                        BigDecimal fcMin = null;
                        LoadFeatureCitySeasonFcDO fcEnergy = null;
                        BigDecimal hisMaxLoad = null;
                        BigDecimal hisMinLoad = null;
                        LoadFeatureCityMonthHisDO hisMaxEnergy = null;
                        Optional<LoadFeatureCitySeasonFcDO> max = collect.get(s).stream()
                            .max(Comparator.comparing(LoadFeatureCitySeasonFcDO::getMaxLoad));
                        if (max.isPresent()) {
                            fcMax = max.get().getMaxLoad();
                        }
                        Optional<LoadFeatureCitySeasonFcDO> min = collect.get(s).stream()
                            .min(Comparator.comparing(LoadFeatureCitySeasonFcDO::getMinLoad));
                        if (min.isPresent()) {
                            fcMin = min.get().getMinLoad();
                        }
                        BigDecimal reduceEnergy = collect.get(s).stream().filter(t -> t.getEnergy() != null)
                            .map(LoadFeatureCitySeasonFcDO::getEnergy).reduce(BigDecimal.ZERO, BigDecimal::add);
                        BigDecimal reduce1 = collect.get(s).stream().filter(t -> t.getNoontimeLoad() != null)
                            .map(LoadFeatureCitySeasonFcDO::getNoontimeLoad).reduce(BigDecimal.ZERO, BigDecimal::add);

                        Optional<LoadFeatureCityMonthHisDO> hisMax = hisDOList.stream()
                            .max(Comparator.comparing(LoadFeatureCityMonthHisDO::getMaxLoad));
                        if (hisMax.isPresent()) {
                            hisMaxLoad = hisMax.get().getMaxLoad();
                        }
                        Optional<LoadFeatureCityMonthHisDO> hisMin = hisDOList.stream()
                            .min(Comparator.comparing(LoadFeatureCityMonthHisDO::getMinLoad));
                        if (hisMin.isPresent()) {
                            hisMinLoad = hisMin.get().getMinLoad();
                        }
                        BigDecimal reduceHisEnergy = hisDOList.stream()
                            .filter(t -> t.getEnergy() != null).map(LoadFeatureCityMonthHisDO::getEnergy)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                        BigDecimal reduce = hisDOList.stream()
                            .filter(t -> t.getNoontimeLoad() != null).map(LoadFeatureCityMonthHisDO::getNoontimeLoad)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                        BigDecimal accuracy = null;
                        if (hisMaxLoad != null && fcMax != null) {
                            accuracy = LoadCalUtil.getAccuracy(hisMaxLoad, fcMax);
                        }
                        BigDecimal accuracy1 = null;
                        if (hisMinLoad != null && fcMin != null) {
                            accuracy1 = LoadCalUtil.getAccuracy(hisMinLoad, fcMin);
                        }
                        BigDecimal accuracy2 = null;
                        if (reduceHisEnergy != null && reduceEnergy != null) {
                            accuracy2 = LoadCalUtil.getAccuracy(reduceHisEnergy, reduceEnergy);
                        }
                        BigDecimal accuracy3 = null;
                        if (reduce != null && reduce1 != null) {
                            accuracy3 = LoadCalUtil.getAccuracy(reduce, reduce1);
                        }
                        if (reduce.compareTo(new BigDecimal("0")) == 0 || reduce1.compareTo(new BigDecimal("0")) == 0) {
                            accuracy3 = null;
                        }
                        TenDaysOfMonthAccuracyDTO tenDaysOfMonthAccuracyDTO = new TenDaysOfMonthAccuracyDTO();
                        tenDaysOfMonthAccuracyDTO.setAlgorithmName(value.getName());
                        tenDaysOfMonthAccuracyDTO.setMaxLoad(accuracy);
                        tenDaysOfMonthAccuracyDTO.setMinLoad(accuracy1);
                        tenDaysOfMonthAccuracyDTO.setMonthEnergy(accuracy2);
                        tenDaysOfMonthAccuracyDTO.setNoontimeLoad(accuracy3);
                        result.add(tenDaysOfMonthAccuracyDTO);
                    }
                }
            }
        }
        return result;
    }

    @Override
    public List<TempCompareDTO> getTempCompareDTOList(String cityId, String year, String season, Integer type, String startStr, String endStr) throws Exception {
        List<String> monthList = new ArrayList<>(4);
        List<String> targetYearList = DateUtil.getTargetYearList(startStr, endStr);
        List<String> res = new ArrayList<>();
        for (String s : targetYearList) {
            res.add(s.split("-")[1]);
        }
        monthList = res;
        Map<String,TempCompareDTO> tempMap = new HashMap<>(4);
        String nextYear = String.valueOf(Integer.parseInt(year) + 1);
        for (String s : targetYearList) {
            TempCompareDTO tempCompareDTO = new TempCompareDTO();
            tempCompareDTO.setType(s.split("-")[0] + "年" + s.split("-")[1]);
            tempMap.put(s.split("-")[1], tempCompareDTO);
        }
        /*for (int i = 0; i < monthList.size(); i++) {
            TempCompareDTO tempCompareDTO = new TempCompareDTO();
            if (StringUtils.isEmpty(season) && i > 1){
                tempCompareDTO.setType(nextYear + "年" + Integer.parseInt(monthList.get(i)) + "月");
            }else {
                tempCompareDTO.setType(year + "年" + Integer.parseInt(monthList.get(i)) + "月");
            }
            tempMap.put(monthList.get(i),tempCompareDTO);
        }*/
        String lastDayOfMonth = DateUtil.getLastDayOfMonth(endStr.split("-")[0], monthList.get(monthList.size() - 1));
        String firstDayOfMonth = DateUtil.getFirstDayOfMonth(startStr.split("-")[0], monthList.get(0));
        /*if (StringUtils.isEmpty(season)){
            lastDayOfMonth = DateUtil.getLastDayOfMonth(String.valueOf(Integer.parseInt(year) + 1), monthList.get(monthList.size() - 1));
        }*/
        Date end = DateUtils.string2Date(lastDayOfMonth, DateFormatType.SIMPLE_DATE_FORMAT_STR);
        Date start = DateUtils.string2Date(firstDayOfMonth, DateFormatType.SIMPLE_DATE_FORMAT_STR);
        List<WeatherFeatureCityDayFcDO> weatherFeatureCityDayFcDOList = weatherFeatureCityDayFcService.getWeatherFeatureCityDayFcDOList(cityId, start, end);
        List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisList = weatherFeatureCityDayHisService.listWeatherFeatureCityDayHisDO(cityId, start, end);
        for (int i=0;i<monthList.size();i++){
            String yearStr = year;
            if (StringUtils.isEmpty(season) && i>1){
                yearStr = nextYear;
            }
            List<String> dateList = genDateList(targetYearList.get(i));
            TempCompareDTO tempCompareDTO = tempMap.get(monthList.get(i));
            if (WeatherColumnEnum.HIGHESTTEMPERATURE.getId().equals(type)){
                Optional<WeatherFeatureCityDayFcDO> maxFcTemp = weatherFeatureCityDayFcDOList
                    .stream()
                    .filter(t -> dateList.contains(DateUtil.formateDate(t.getDate())))
                    .max(Comparator.comparing(WeatherFeatureCityDayFcDO::getHighestTemperature));
                maxFcTemp.ifPresent(weatherFeatureCityDayFcDO -> tempCompareDTO.setFcTemp(weatherFeatureCityDayFcDO.getHighestTemperature()));
                Optional<WeatherFeatureCityDayHisDO> maxHisTemp = weatherFeatureCityDayHisList
                    .stream()
                    .filter(t -> dateList.contains(DateUtil.formateDate(t.getDate())))
                    .max(Comparator.comparing(WeatherFeatureCityDayHisDO::getHighestTemperature));
                maxHisTemp.ifPresent(WeatherFeatureCityDayHisDO -> tempCompareDTO.setHisTemp(WeatherFeatureCityDayHisDO.getHighestTemperature()));
            }else if (WeatherColumnEnum.LOWESTTEMPERATURE.getId().equals(type)){
                Optional<WeatherFeatureCityDayFcDO> lowFcTemp = weatherFeatureCityDayFcDOList
                    .stream()
                    .filter(t -> dateList.contains(DateUtil.formateDate(t.getDate())))
                    .max(Comparator.comparing(WeatherFeatureCityDayFcDO::getLowestTemperature));
                lowFcTemp.ifPresent(weatherFeatureCityDayFcDO -> tempCompareDTO.setFcTemp(weatherFeatureCityDayFcDO.getLowestTemperature()));
                Optional<WeatherFeatureCityDayHisDO> lowHisTemp = weatherFeatureCityDayHisList
                    .stream()
                    .filter(t -> dateList.contains(DateUtil.formateDate(t.getDate())))
                    .max(Comparator.comparing(WeatherFeatureCityDayHisDO::getLowestTemperature));
                lowHisTemp.ifPresent(WeatherFeatureCityDayHisDO -> tempCompareDTO.setHisTemp(WeatherFeatureCityDayHisDO.getLowestTemperature()));
            }else if (WeatherColumnEnum.AVETEMPERATURE.getId().equals(type)){
                Optional<WeatherFeatureCityDayFcDO> avgFcTemp = weatherFeatureCityDayFcDOList
                    .stream()
                    .filter(t -> dateList.contains(DateUtil.formateDate(t.getDate())))
                    .max(Comparator.comparing(WeatherFeatureCityDayFcDO::getAveTemperature));
                avgFcTemp.ifPresent(weatherFeatureCityDayFcDO -> tempCompareDTO.setFcTemp(weatherFeatureCityDayFcDO.getAveTemperature()));
                Optional<WeatherFeatureCityDayHisDO> avgHisTemp = weatherFeatureCityDayHisList
                    .stream()
                    .filter(t -> dateList.contains(DateUtil.formateDate(t.getDate())))
                    .max(Comparator.comparing(WeatherFeatureCityDayHisDO::getAveTemperature));
                avgHisTemp.ifPresent(WeatherFeatureCityDayHisDO -> tempCompareDTO.setHisTemp(WeatherFeatureCityDayHisDO.getAveTemperature()));
            }
            tempMap.put(monthList.get(i),tempCompareDTO);
        }
        return new ArrayList<>(tempMap.values());
    }

    @Override
    public List<FeaturesDTO> getFeatureList(String cityId, String year, String season, String algorithmId)
        throws Exception {
        //year = String.valueOf(Integer.parseInt(year) - 1);
        String nextYear = String.valueOf(Integer.parseInt(year) + 1);
        String lastYear = String.valueOf(Integer.parseInt(year) - 1);
        List<FeaturesDTO> featureDTOList = new ArrayList<>(16);
        String caliberId = Constants.CALIBER_ID_BG_DS;
        if (String.valueOf(Constants.PROVINCE_TYPE).equals(cityId)){
            caliberId = Constants.CALIBER_ID_BG_QW;
        }
        List<String> monthList = new ArrayList<>(4);
        monthList = Arrays.asList(Constants.SUMMER_MONTHS);
        if (StringUtils.isEmpty(season)){
            monthList = Arrays.asList(Constants.WINTER_MONTHS);
        }
        Map<String, LoadFeatureCitySeasonFcDO> monthFcDOMap;
        Map<String, LoadFeatureCityMonthHisDO> cityMonthHisDOMap;
        if (!StringUtils.isEmpty(season)) {
            // 重新预测
            if (algorithmId.equals("0")) {
                monthFcDOMap = loadFeatureCityMonthFcService.getSummerSeasonReportFeature(year, cityId, caliberId)
                    .stream()
                    .collect(Collectors.toMap(t -> t.getMonth(), t -> t));
            } else {
                monthFcDOMap = loadFeatureCityMonthFcService.getSummerSeasonFeature(year, cityId, caliberId,
                    algorithmId).stream().collect(
                    Collectors.toMap(t -> t.getMonth(), t -> t));
            }
            cityMonthHisDOMap = loadFeatureCityMonthHisService
                .getSummerMonthsFeature(lastYear, cityId, caliberId).stream().collect(
                    Collectors.toMap(t -> t.getMonth(), t -> t));
        } else {
            // 重新预测
            if (algorithmId.equals("0")) {
                monthFcDOMap = loadFeatureCityMonthFcService
                    .getWinterSeasonReportFeatures(year, cityId, caliberId).stream()
                    .collect(
                        Collectors.toMap(t -> t.getMonth(), t -> t));
            } else {
                monthFcDOMap = loadFeatureCityMonthFcService
                    .getWinterSeasonFeatures(year, cityId, caliberId, algorithmId).stream().collect(
                        Collectors.toMap(t -> t.getMonth(), t -> t));
            }
            cityMonthHisDOMap = loadFeatureCityMonthHisService
                .getWinterMonthsFeatures(lastYear, cityId, caliberId).stream().collect(
                    Collectors.toMap(t -> t.getMonth(), t -> t));
        }
        for (String s : monthList) {
            FeaturesDTO featureDTO = new FeaturesDTO();
            LoadFeatureCityMonthHisDO loadFeatureCityMonthHisDO = new LoadFeatureCityMonthHisDO();
            LoadFeatureCitySeasonFcDO loadFeatureCitySeasonFcDO = new LoadFeatureCitySeasonFcDO();
            if (MapUtils.isNotEmpty(cityMonthHisDOMap)) {
                loadFeatureCityMonthHisDO = cityMonthHisDOMap.get(s);
            }
            if (MapUtils.isNotEmpty(monthFcDOMap)) {
                loadFeatureCitySeasonFcDO = monthFcDOMap.get(s);
            }
            BigDecimal accuracyMaxLoad = LoadCalUtil.getAccuracy(loadFeatureCityMonthHisDO.getMaxLoad(),
                loadFeatureCitySeasonFcDO.getMaxLoad());
            BigDecimal accuracyMinLoad = LoadCalUtil.getAccuracy(loadFeatureCityMonthHisDO.getMinLoad(),
                loadFeatureCitySeasonFcDO.getMinLoad());
            BigDecimal accuracyEnergy = LoadCalUtil.getAccuracy(loadFeatureCityMonthHisDO.getMinLoad(),
                loadFeatureCitySeasonFcDO.getMinLoad());
            featureDTO.setMaxLoad(accuracyMaxLoad);
            featureDTO.setMinLoad(accuracyMinLoad);
            featureDTO.setMonthEnergy(accuracyEnergy);
            featureDTO.setType(s);
            featureDTOList.add(featureDTO);
        }
        List<BigDecimal> maxLoadList = new ArrayList<>(4);
        List<BigDecimal> minLoadList = new ArrayList<>(4);
        List<BigDecimal> energyList = new ArrayList<>(4);
        List<FeaturesDTO> collect = new ArrayList<>(6);
        maxLoadList = featureDTOList.stream().map(FeaturesDTO::getMaxLoad).collect(Collectors.toList());
        minLoadList = featureDTOList.stream().map(FeaturesDTO::getMinLoad).collect(Collectors.toList());
        energyList = featureDTOList.stream().map(FeaturesDTO::getMonthEnergy).collect(Collectors.toList());
        collect = featureDTOList.stream()
            .sorted(Comparator.comparing(FeaturesDTO::getType))
            .collect(Collectors.toList());
        FeaturesDTO featureDTO = new FeaturesDTO();
        featureDTO.setType("平均");
        if (!CollectionUtils.isEmpty(maxLoadList)){
            featureDTO.setMaxLoad(BigDecimalFunctions.listAvg(maxLoadList));
        }
        if (!CollectionUtils.isEmpty(minLoadList)){
            featureDTO.setMinLoad(BigDecimalFunctions.listAvg(minLoadList));
        }
        if (!CollectionUtils.isEmpty(energyList)){
            featureDTO.setMonthEnergy(BigDecimalFunctions.listAvg(energyList));
        }
        collect.add(featureDTO);
        return collect;
    }

    private FeatureDTO mergeFeatureDTO(
        Map<String, StatisticsCityMonthFcFeatureServiceDO> accuracyMap,
        SeasonAlgorithmModelEnums weatherLoadForecast,String year,String season) {
        FeatureDTO tenDaysOfMonthAccuracyDTO = new FeatureDTO();
//        StatisticsCityQuarterFcFeatureServiceDO statisticsCityQuarterFcFeatureServiceDO = cityQuarterFcFeatureServiceDOMap
//                .get(weatherLoadForecast.getId());
        List<String> monthsBySeason = QuarterUtil.getMonthsBySeason(season);
        List<StatisticsCityMonthFcFeatureServiceDO> accuracyDOList = new ArrayList<>(4);
        String monthDefaultAlgorithmId = settingSystemService.getMonthDefaultAlgorithmId();
        String nextYear = String.valueOf(Integer.parseInt(year) + 1 );
        for (int i = 0; i < monthsBySeason.size(); i++) {
            StatisticsCityMonthFcFeatureServiceDO statisticsCityMonthFcFeatureServiceDO = accuracyMap.get(year + Constants.SEPARATOR_BROKEN_LINE + monthsBySeason.get(i) + Constants.SEPARATOR_BROKEN_LINE + weatherLoadForecast.getId());
            if (weatherLoadForecast.getId().equals(SeasonAlgorithmModelEnums.REPORT.getId()) && statisticsCityMonthFcFeatureServiceDO == null){
                statisticsCityMonthFcFeatureServiceDO = accuracyMap.get(year + Constants.SEPARATOR_BROKEN_LINE + monthsBySeason.get(i) + Constants.SEPARATOR_BROKEN_LINE + monthDefaultAlgorithmId);
            }
            if (StringUtils.isEmpty(season) && i>1){
                statisticsCityMonthFcFeatureServiceDO = accuracyMap.get(nextYear + Constants.SEPARATOR_BROKEN_LINE + monthsBySeason.get(i) + Constants.SEPARATOR_BROKEN_LINE + weatherLoadForecast.getId());
                if (weatherLoadForecast.getId().equals(SeasonAlgorithmModelEnums.REPORT.getId()) && statisticsCityMonthFcFeatureServiceDO == null){
                    statisticsCityMonthFcFeatureServiceDO = accuracyMap.get(nextYear + Constants.SEPARATOR_BROKEN_LINE + monthsBySeason.get(i) + Constants.SEPARATOR_BROKEN_LINE + monthDefaultAlgorithmId);
                }
            }
            if (statisticsCityMonthFcFeatureServiceDO != null){
                accuracyDOList.add(statisticsCityMonthFcFeatureServiceDO);
            }
        }
//        if (statisticsCityQuarterFcFeatureServiceDO == null && weatherLoadForecast.getId().equals(SeasonAlgorithmModelEnums.REPORT.getId())){
//            String monthDefaultAlgorithmId = settingSystemService.getMonthDefaultAlgorithmId();
//            statisticsCityQuarterFcFeatureServiceDO = cityQuarterFcFeatureServiceDOMap
//                    .get(monthDefaultAlgorithmId);
//        }
//        if (statisticsCityQuarterFcFeatureServiceDO != null) {
//            tenDaysOfMonthAccuracyDTO.setMaxLoad(statisticsCityQuarterFcFeatureServiceDO.getMaxLoadAccuracy().multiply(new BigDecimal(100)));
//            tenDaysOfMonthAccuracyDTO.setMinLoad(statisticsCityQuarterFcFeatureServiceDO.getMinLoadAccuracy().multiply(new BigDecimal(100)));
//            tenDaysOfMonthAccuracyDTO.setMonthEnergy(statisticsCityQuarterFcFeatureServiceDO.getEnergyAccuracy().multiply(new BigDecimal(100)));
//        }
        List<BigDecimal> maxList = new ArrayList<>(4);
        List<BigDecimal> minList = new ArrayList<>(4);
        List<BigDecimal> energyList = new ArrayList<>(4);
        if (!CollectionUtils.isEmpty(accuracyDOList)){
            for (StatisticsCityMonthFcFeatureServiceDO serviceDO : accuracyDOList){
                maxList.add(serviceDO.getMaxLoadAccuracy());
                minList.add(serviceDO.getMinLoadAccuracy());
                energyList.add(serviceDO.getEnergyAccuracy());
            }
        }
        if (!CollectionUtils.isEmpty(maxList)){
            tenDaysOfMonthAccuracyDTO.setMaxLoad(BigDecimalFunctions.listAvg(maxList).multiply(new BigDecimal(100)));
        }
        if (!CollectionUtils.isEmpty(minList)){
            tenDaysOfMonthAccuracyDTO.setMinLoad(BigDecimalFunctions.listAvg(minList).multiply(new BigDecimal(100)));
        }
        if (!CollectionUtils.isEmpty(energyList)){
            tenDaysOfMonthAccuracyDTO.setMonthEnergy(BigDecimalFunctions.listAvg(energyList).multiply(new BigDecimal(100)));
        }
        return tenDaysOfMonthAccuracyDTO;
    }

    private List<String> genDateList(String yearMonth) {
        String sYear = yearMonth.split("-")[0];
        String sMonth = yearMonth.split("-")[1];
        String firstDayOfMonth = DateUtil.getFirstDayOfMonth(sYear, sMonth);
        String lastDayOfMonth = DateUtil.getLastDayOfMonth(sYear, sMonth);
        return DateUtil.getListBetweenDay(firstDayOfMonth, lastDayOfMonth);
    }

    @Override
    public List<TenDaysOfMonthFeatureDTO> getMonthLoadFc(String cityId, String caliberId, Date date, String season, String algorithmId) {
        String defaultAlgorithmId = settingSystemService.getMonthDefaultAlgorithmId();
        List<TenDaysOfMonthFeatureDTO> result = new ArrayList<>();
        String year = DateUtil.getYearByDate(date);
        String nextYear = String.valueOf(Integer.parseInt(year) + 1);
        String lastYear = String.valueOf(Integer.parseInt(year) - 1);
        try {
            Map<String, LoadFeatureCitySeasonFcDO> monthFcDOMap;
            Map<String, LoadFeatureCitySeasonFcDO> monthFcReportDOMap;
            Map<String, LoadFeatureCityMonthHisDO> cityMonthHisDOMap;
            if (!StringUtils.isEmpty(season)) {
                // 重新预测
                monthFcDOMap = loadFeatureCityMonthFcService.getSummerSeasonFeature(year, cityId, caliberId, algorithmId).stream().collect(
                    Collectors.toMap(t -> t.getAlgorithmId() + Constants.SEPARATOR_BROKEN_LINE + t.getMonth(), t -> t));
                // 默认上报
                monthFcReportDOMap = loadFeatureCityMonthFcService.getSummerSeasonFeature(year, cityId, caliberId, defaultAlgorithmId).stream().collect(
                    Collectors.toMap(t -> t.getAlgorithmId() + Constants.SEPARATOR_BROKEN_LINE + t.getMonth(), t -> t));
                cityMonthHisDOMap = loadFeatureCityMonthHisService
                    .getSummerMonthsFeature(lastYear, cityId, caliberId).stream().collect(
                        Collectors.toMap(t -> t.getMonth(), t -> t));
            } else {
                // 重新预测
                monthFcDOMap = loadFeatureCityMonthFcService
                    .getWinterSeasonFeatures(year, cityId, caliberId, algorithmId).stream().collect(
                        Collectors.toMap(t -> t.getAlgorithmId() + Constants.SEPARATOR_BROKEN_LINE + t.getMonth(), t -> t));
                // 默认上报
                monthFcReportDOMap = loadFeatureCityMonthFcService
                    .getWinterSeasonFeatures(year, cityId, caliberId, defaultAlgorithmId).stream().collect(
                        Collectors.toMap(t -> t.getAlgorithmId() + Constants.SEPARATOR_BROKEN_LINE + t.getMonth(), t -> t));
                cityMonthHisDOMap = loadFeatureCityMonthHisService
                    .getWinterMonthsFeatures(lastYear, cityId, caliberId).stream().collect(
                        Collectors.toMap(t -> t.getMonth(), t -> t));
            }

            //获取冬季或夏季对应月份
            List<String> months = QuarterUtil.getMonthsBySeason(season);

            for (SeasonAlgorithmModelEnums seasonAlgorithmModelEnums : SeasonAlgorithmModelEnums.values()) {
                if (algorithmId.equals(seasonAlgorithmModelEnums.getId())) {
                    String algorithmModelEnumsName = seasonAlgorithmModelEnums.getName();
                    String algorithmIds = seasonAlgorithmModelEnums.getId();
                    for (int i = 0; i < months.size(); i++) {
                        TenDaysOfMonthFeatureDTO tenDaysOfMonthFeatureDTO = new TenDaysOfMonthFeatureDTO();
                        tenDaysOfMonthFeatureDTO.setAlgorithmName(algorithmModelEnumsName);
                        if (!StringUtils.isEmpty(season)) {
                            tenDaysOfMonthFeatureDTO.setType(year + "年" + months.get(i) + "月");
                        } else if ("01".equals(months.get(i)) || "02".equals(months.get(i))) {
                            tenDaysOfMonthFeatureDTO.setType(nextYear + "年" + months.get(i) + "月");
                        } else {
                            tenDaysOfMonthFeatureDTO.setType(year + "年" + months.get(i) + "月");
                        }
                        LoadFeatureCitySeasonFcDO loadFeatureCityMonthFcDO = monthFcDOMap.get(algorithmIds + Constants.SEPARATOR_BROKEN_LINE + months.get(i));
                        if (loadFeatureCityMonthFcDO != null) {
                            tenDaysOfMonthFeatureDTO.setMaxLoad(loadFeatureCityMonthFcDO.getMaxLoad());
                            tenDaysOfMonthFeatureDTO.setMinLoad(loadFeatureCityMonthFcDO.getMinLoad());
                            tenDaysOfMonthFeatureDTO.setMonthEnergy(loadFeatureCityMonthFcDO.getEnergy());
                        }
                        LoadFeatureCityMonthHisDO loadFeatureCityMonthHisDO = cityMonthHisDOMap.get(months.get(i));
                        if (loadFeatureCityMonthHisDO != null) {
                            tenDaysOfMonthFeatureDTO.setLastYearMaxLoad(loadFeatureCityMonthHisDO.getMaxLoad());
                            tenDaysOfMonthFeatureDTO.setLastYearMinLoad(loadFeatureCityMonthHisDO.getMinLoad());
                            tenDaysOfMonthFeatureDTO.setLastYearEnergy(loadFeatureCityMonthHisDO.getEnergy());
                        }
                        result.add(tenDaysOfMonthFeatureDTO);
                    }
                }
            }
            if (!CollectionUtils.isEmpty(result)) {
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(result) && result.get(0).getMaxLoad() == null) {
                    List<TenDaysOfMonthFeatureDTO> result1 = new ArrayList<>();
                    for (String seasonMonth : months) {
                        TenDaysOfMonthFeatureDTO tenDaysOfMonthFeatureDTO = new TenDaysOfMonthFeatureDTO();
                        if ("01".equals(seasonMonth) || "02".equals(seasonMonth)) {
                            tenDaysOfMonthFeatureDTO.setType(nextYear + "年" + seasonMonth + "月");
                            //year = nextYear;
                        } else {
                            tenDaysOfMonthFeatureDTO.setType(year + "年" + seasonMonth + "月");
                        }
                        LoadFeatureCitySeasonFcDO loadFeatureCityMonthFcDO = monthFcReportDOMap.get(
                            AlgorithmConstants.COMMON_ALGORITHM_TYPE + Constants.SEPARATOR_BROKEN_LINE + seasonMonth);
                        LoadFeatureCityMonthHisDO loadFeatureCityMonthHisDO = cityMonthHisDOMap.get(seasonMonth);
                        if (loadFeatureCityMonthHisDO != null) {
                            tenDaysOfMonthFeatureDTO.setLastYearMaxLoad(loadFeatureCityMonthHisDO.getMaxLoad());
                            tenDaysOfMonthFeatureDTO.setLastYearEnergy(loadFeatureCityMonthHisDO.getEnergy());
                        }
                        tenDaysOfMonthFeatureDTO.setAlgorithmName("校核上报");
                        if (loadFeatureCityMonthFcDO != null) {
                            tenDaysOfMonthFeatureDTO.setMaxLoad(loadFeatureCityMonthFcDO.getMaxLoad());
                            tenDaysOfMonthFeatureDTO.setMinLoad(loadFeatureCityMonthFcDO.getMinLoad());
                            tenDaysOfMonthFeatureDTO.setMonthEnergy(loadFeatureCityMonthFcDO.getEnergy());
                        } else {
                            LoadFeatureCitySeasonFcDO cityMonthFcDO = monthFcReportDOMap.get(defaultAlgorithmId + Constants.SEPARATOR_BROKEN_LINE + seasonMonth);
                            if (cityMonthFcDO != null) {
                                tenDaysOfMonthFeatureDTO.setMaxLoad(cityMonthFcDO.getMaxLoad());
                                tenDaysOfMonthFeatureDTO.setMinLoad(cityMonthFcDO.getMinLoad());
                                tenDaysOfMonthFeatureDTO.setMonthEnergy(cityMonthFcDO.getEnergy());
                            }
                        }
                        result1.add(tenDaysOfMonthFeatureDTO);
                    }
                    return result1;
                }
            }
            /*List<TenDaysOfMonthFeatureDTO> featureDTOList = genRandomTreeResult(season, cityId, caliberId, date, cityMonthHisDOMap);
            if (!CollectionUtils.isEmpty(featureDTOList) && CollectionUtils.isEmpty(result)) {
                result.addAll(featureDTOList);
            }*/
            return result;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public List<TenDaysOfMonthFeatureDTO> getMonthLoadFcCompare(String cityId, String caliberId, Date date,
        String season, String algorithmId, String startStr, String endStr) throws Exception {
        List<TenDaysOfMonthFeatureDTO> result = new ArrayList<>();
        String year = startStr.split("-")[0];
        String nextYear = String.valueOf(Integer.parseInt(year) + 1);
        String lastYear = String.valueOf(Integer.parseInt(year) - 1);
        Map<String, LoadFeatureCitySeasonFcDO> fcCollect = new HashMap<>();;
        List<LoadFeatureCityMonthHisDO> hisList = new ArrayList<>();
        List<LoadFeatureCitySeasonFcDO> fcList = new ArrayList<>();
        List<String> yearList = new ArrayList<>();
        String startYear = startStr.split("-")[0];
        String endYear = endStr.split("-")[0];
        if (startYear.equals(endYear)) {
            yearList.add(startYear);
        } else {
            yearList.add(startYear);
            yearList.add(endYear);
        }
        if (StringUtils.isEmpty(season)) {
            // 度冬
            for (String s : yearList) {
                List<LoadFeatureCitySeasonFcDO> seasonReportFeatures = loadFeatureCityMonthFcService.getSeasonReportFeatures(
                    s, null, cityId, caliberId, "4");
                List<LoadFeatureCitySeasonFcDO> seasonFeaturesNew = loadFeatureCityMonthFcService.getSeasonFeaturesNew(
                    s, null, cityId, caliberId, "", "4");
                if (!CollectionUtils.isEmpty(seasonFeaturesNew)) {
                    fcList.addAll(seasonFeaturesNew);
                }
                if (!CollectionUtils.isEmpty(
                    loadFeatureCityMonthHisService.getLoadFeatureCityMonthHisDOLists(cityId, lastYear, null,
                        caliberId))) {
                    hisList.addAll(
                        loadFeatureCityMonthHisService.getLoadFeatureCityMonthHisDOLists(cityId,
                            s, null, caliberId));
                }
            }
        } else {
            List<LoadFeatureCitySeasonFcDO> seasonReportFeatures = loadFeatureCityMonthFcService.getSeasonReportFeatures(
                startYear, null, cityId, caliberId, "2");
            List<LoadFeatureCitySeasonFcDO> seasonFeaturesNew = loadFeatureCityMonthFcService.getSeasonFeaturesNew(
                startYear, null, cityId, caliberId, "", "2");
            if (!CollectionUtils.isEmpty(seasonFeaturesNew)) {
                fcList.addAll(seasonFeaturesNew);
            }
            if (!CollectionUtils.isEmpty(
                loadFeatureCityMonthHisService.getLoadFeatureCityMonthHisDOLists(cityId, lastYear, null,
                    caliberId))) {
                hisList.addAll(
                    loadFeatureCityMonthHisService.getLoadFeatureCityMonthHisDOLists(cityId,
                        year, null, caliberId));
            }
        }
        if (algorithmId.equals("0")) {
            fcCollect = fcList.stream()
                .filter(t -> t.getReport() != null && Boolean.TRUE.equals(t.getReport()))
                .collect(Collectors.toMap(t -> t.getYear() + "-" + t.getMonth(), t -> t));
        } else {
            String finalAlgorithmId = algorithmId;
            fcCollect = fcList.stream()
                .filter(t -> t.getAlgorithmId().equals(finalAlgorithmId))
                .collect(Collectors.toMap(t -> t.getYear() + "-" + t.getMonth(), t -> t));
        }
        Map<String, LoadFeatureCityMonthHisDO> hisCollect = hisList.stream()
            .collect(Collectors.toMap(t -> t.getYear() + "-" + t.getMonth(), t -> t));
        List<String> targetMonthList = DateUtil.getTargetYearList(startStr, endStr);
        for (String s : targetMonthList) {
            TenDaysOfMonthFeatureDTO tenDaysOfMonthFeatureDTO = new TenDaysOfMonthFeatureDTO();
            tenDaysOfMonthFeatureDTO.setMonth(s);
            if (MapUtils.isNotEmpty(fcCollect)) {
                for (String s1 : fcCollect.keySet()) {
                    if (s.equals(s1)) {
                        LoadFeatureCitySeasonFcDO loadFeatureCitySeasonFcDO = fcCollect.get(s1);
                        tenDaysOfMonthFeatureDTO.setMaxLoad(loadFeatureCitySeasonFcDO.getMaxLoad());
                        tenDaysOfMonthFeatureDTO.setMinLoad(loadFeatureCitySeasonFcDO.getMinLoad());
                        tenDaysOfMonthFeatureDTO.setMonthEnergy(loadFeatureCitySeasonFcDO.getEnergy());
                        tenDaysOfMonthFeatureDTO.setNoonTimeLoad(loadFeatureCitySeasonFcDO.getNoontimeLoad());
                    }
                }
            }
            if (MapUtils.isNotEmpty(hisCollect)) {
                for (String s1 : hisCollect.keySet()) {
                    if (s.equals(s1)) {
                        LoadFeatureCityMonthHisDO loadFeatureCityMonthHisDO = hisCollect.get(s1);
                        tenDaysOfMonthFeatureDTO.setLastYearMaxLoad(loadFeatureCityMonthHisDO.getMaxLoad());
                        tenDaysOfMonthFeatureDTO.setLastYearMinLoad(loadFeatureCityMonthHisDO.getMinLoad());
                        tenDaysOfMonthFeatureDTO.setLastYearEnergy(loadFeatureCityMonthHisDO.getEnergy());
                        tenDaysOfMonthFeatureDTO.setLastYearNoonTimeLoad(loadFeatureCityMonthHisDO.getNoontimeLoad());
                    }
                }
            }
            result.add(tenDaysOfMonthFeatureDTO);
        }
        for (TenDaysOfMonthFeatureDTO tenDaysOfMonthFeatureDTO : result) {
            tenDaysOfMonthFeatureDTO.setMaxLoadAccuracy(
                LoadCalUtil.getAccuracy(tenDaysOfMonthFeatureDTO.getLastYearMaxLoad(),
                    tenDaysOfMonthFeatureDTO.getMaxLoad()));
            tenDaysOfMonthFeatureDTO.setMinLoadAccuracy(
                LoadCalUtil.getAccuracy(tenDaysOfMonthFeatureDTO.getLastYearMinLoad(),
                    tenDaysOfMonthFeatureDTO.getMinLoad()));
            tenDaysOfMonthFeatureDTO.setEnergyAccuracy(
                LoadCalUtil.getAccuracy(tenDaysOfMonthFeatureDTO.getLastYearEnergy(),
                    tenDaysOfMonthFeatureDTO.getMonthEnergy()));
            tenDaysOfMonthFeatureDTO.setNoonTimeLoadAccuracy(
                LoadCalUtil.getAccuracy(tenDaysOfMonthFeatureDTO.getLastYearNoonTimeLoad(),
                    tenDaysOfMonthFeatureDTO.getNoonTimeLoad()));
        }
        return result;
    }

    @Override
    public List<TenDaysOfMonthFeatureDTO> getMonthReportList(String cityId, String caliberId, Date date,
        String algorithmName, String season, String startStr, String endStr) throws Exception {
        List<TenDaysOfMonthFeatureDTO> result = new ArrayList<>();
        String year = DateUtil.getYearByDate(date);
        String nextYear = String.valueOf(Integer.parseInt(year) + 1);
        String lastYear = String.valueOf(Integer.parseInt(year) - 1);
        Map<String, LoadFeatureCitySeasonFcDO> fcCollect = new HashMap<>();
        String algorithmId = null;
        List<LoadFeatureCityMonthHisDO> hisList = new ArrayList<>();
        List<LoadFeatureCitySeasonFcDO> fcList = new ArrayList<>();
        List<String> yearList = new ArrayList<>();
        String startYear = startStr.split("-")[0];
        String endYear = endStr.split("-")[0];
        yearList.add(startYear);
        yearList.add(endYear);
        if (StringUtils.isEmpty(season)) {
            // 度冬
            for (String s : yearList) {
                if (!CollectionUtils.isEmpty(loadFeatureCityMonthFcService.getSeasonFeaturesNew(
                    s, null, cityId, caliberId, "", "4"))) {
                    List<LoadFeatureCitySeasonFcDO> seasonFeaturesNew = loadFeatureCityMonthFcService.getSeasonFeaturesNew(
                        s, null, cityId, caliberId, "", "4");
                    fcList.addAll(loadFeatureCityMonthFcService.getSeasonFeaturesNew(
                        s, null, cityId, caliberId, "", "4"));
                }
                if (!CollectionUtils.isEmpty(
                    loadFeatureCityMonthHisService.getLoadFeatureCityMonthHisDOLists(cityId, lastYear, null,
                        caliberId))) {
                    hisList.addAll(
                        loadFeatureCityMonthHisService.getLoadFeatureCityMonthHisDOLists(cityId,
                            String.valueOf(Integer.valueOf(s) - 1), null, caliberId));
                }
            }
        } else {
            if (!CollectionUtils.isEmpty(loadFeatureCityMonthFcService.getSeasonFeaturesNew(
                startYear, null, cityId, caliberId, "", "2"))) {
                fcList.addAll(loadFeatureCityMonthFcService.getSeasonFeaturesNew(
                    startYear, null, cityId, caliberId, "", "2"));
            }
            if (!CollectionUtils.isEmpty(
                loadFeatureCityMonthHisService.getLoadFeatureCityMonthHisDOLists(cityId, lastYear, null,
                    caliberId))) {
                hisList.addAll(
                    loadFeatureCityMonthHisService.getLoadFeatureCityMonthHisDOLists(cityId,
                        String.valueOf(Integer.valueOf(startYear) - 1), null, caliberId));
            }
        }
        for (SeasonAlgorithmModelEnum value : SeasonAlgorithmModelEnum.values()) {
            if (algorithmName.equals(value.getName())) {
                algorithmId = value.getId();
            }
        }
        if (algorithmId.equals("0")) {
            fcCollect = fcList.stream()
                .filter(t -> t.getReport() != null && Boolean.TRUE.equals(t.getReport()))
                .collect(Collectors.toMap(t -> t.getYear() + "-" + t.getMonth(), t -> t));
        } else {
            String finalAlgorithmId = algorithmId;
            fcCollect = fcList.stream()
                .filter(t -> t.getAlgorithmId().equals(finalAlgorithmId))
                .collect(Collectors.toMap(t -> t.getYear() + "-" + t.getMonth(), t -> t));
        }
        Map<String, LoadFeatureCityMonthHisDO> hisCollect = hisList.stream()
            .collect(Collectors.toMap(t -> t.getYear() + "-" + t.getMonth(), t -> t));
        List<String> targetMonthList = DateUtil.getTargetYearList(startStr, endStr);
        for (String s : targetMonthList) {
            TenDaysOfMonthFeatureDTO tenDaysOfMonthFeatureDTO = new TenDaysOfMonthFeatureDTO();
            tenDaysOfMonthFeatureDTO.setMonth(s);
            if (MapUtils.isNotEmpty(fcCollect)) {
                for (String s1 : fcCollect.keySet()) {
                    if (s.equals(s1)) {
                        LoadFeatureCitySeasonFcDO loadFeatureCitySeasonFcDO = fcCollect.get(s1);
                        tenDaysOfMonthFeatureDTO.setMaxLoad(loadFeatureCitySeasonFcDO.getMaxLoad());
                        tenDaysOfMonthFeatureDTO.setMinLoad(loadFeatureCitySeasonFcDO.getMinLoad());
                        tenDaysOfMonthFeatureDTO.setMonthEnergy(loadFeatureCitySeasonFcDO.getEnergy());
                        tenDaysOfMonthFeatureDTO.setNoonTimeLoad(loadFeatureCitySeasonFcDO.getNoontimeLoad());
                        tenDaysOfMonthFeatureDTO.setExtremeMaxLoad(loadFeatureCitySeasonFcDO.getExtremeMaxLoad());
                        tenDaysOfMonthFeatureDTO.setExtremeMinLoad(loadFeatureCitySeasonFcDO.getExtremeMinLoad());
                        tenDaysOfMonthFeatureDTO.setExtremeMonthEnergy(loadFeatureCitySeasonFcDO.getExtremeEnergy());
                        tenDaysOfMonthFeatureDTO.setExtremeNoonTimeLoad(loadFeatureCitySeasonFcDO.getExtremeNoontimeLoad());
                    }
                }
            }
            s = String.valueOf(Integer.valueOf(s.split("-")[0]) -1) + "-" + s.split("-")[1];
            if (MapUtils.isNotEmpty(hisCollect)) {
                for (String s1 : hisCollect.keySet()) {
                    if (s.equals(s1)) {
                        LoadFeatureCityMonthHisDO loadFeatureCityMonthHisDO = hisCollect.get(s1);
                        tenDaysOfMonthFeatureDTO.setLastYearMaxLoad(loadFeatureCityMonthHisDO.getMaxLoad());
                        tenDaysOfMonthFeatureDTO.setLastYearMinLoad(loadFeatureCityMonthHisDO.getMinLoad());
                        tenDaysOfMonthFeatureDTO.setLastYearEnergy(loadFeatureCityMonthHisDO.getEnergy());
                        tenDaysOfMonthFeatureDTO.setLastYearNoonTimeLoad(loadFeatureCityMonthHisDO.getNoontimeLoad());
                    }
                }
            }
            result.add(tenDaysOfMonthFeatureDTO);
        }
        return result;
    }

    @Override
    public void report(List<SeasonReportDTO> monthReportDTOS) throws Exception {
        if (CollectionUtils.isEmpty(monthReportDTOS)) {
            return;
        }
        SeasonReportDTO seasonReportDTO = monthReportDTOS.get(0);
        String sYear = seasonReportDTO.getDate().split(Constants.SEPARATOR_BROKEN_LINE)[0];
        String cityId = seasonReportDTO.getCityId();
        String caliberId = getCaliberId(seasonReportDTO.getCityId());
        String season = "";
        if ("1".equals(seasonReportDTO.getSeason())) {
            season = "2";
        } else if ("2".equals(seasonReportDTO.getSeason())) {
            season = "4";
        } else {
            season = "5";
        }
        for (SeasonReportDTO monthReportDTO : monthReportDTOS) {
            if (monthReportDTO.getDate() == null || monthReportDTO.getDate().split("-").length == 1) {
                continue;
            }
            String date = monthReportDTO.getDate();
            String[] split = date.split(Constants.SEPARATOR_BROKEN_LINE);
            String year = split[0];
            String month = split[1];
            List<LoadFeatureCitySeasonFcDO> seasonReportFeatures = new ArrayList<>();
            if ("2".equals(season)) {
                // 度夏
                seasonReportFeatures = loadFeatureCityMonthFcService.getSummerSeasonReportFeature(
                    year, month, cityId, caliberId);
            } else if ("4".equals(season)) {
                // 度冬
                seasonReportFeatures = loadFeatureCityMonthFcService.getSeasonFeatures(
                    year, month, cityId, caliberId, null, "4");
            } else {
                // 年度
                seasonReportFeatures = loadFeatureCityMonthFcService.getSeasonReportFeatures(
                    year, month, cityId, caliberId);
            }
            /*LoadFeatureCityMonthFcDO monthReportVO = loadFeatureCityMonthFcService
                    .getMonthReportVO(cityId, caliberId, year, month);*/
            if (!CollectionUtils.isEmpty(seasonReportFeatures)) {
                seasonReportFeatures.get(0).setReport(false);
                loadFeatureCityMonthFcService.saveOrUpdate(seasonReportFeatures.get(0));
            }
            LoadFeatureCitySeasonFcDO loadFeatureCityMonthFcDO = new LoadFeatureCitySeasonFcDO();
            loadFeatureCityMonthFcDO.setCityId(cityId);
            loadFeatureCityMonthFcDO.setYear(year);
            loadFeatureCityMonthFcDO.setSeason(season);
            loadFeatureCityMonthFcDO.setCaliberId(caliberId);
            loadFeatureCityMonthFcDO.setReport(true);
            loadFeatureCityMonthFcDO.setAlgorithmId(AlgorithmConstants.MD_ALGORITHM_ID);
            loadFeatureCityMonthFcDO.setMonth(month);
            loadFeatureCityMonthFcDO.setMaxLoad(monthReportDTO.getMaxLoad());
            loadFeatureCityMonthFcDO.setMinLoad(monthReportDTO.getMinLoad());
            loadFeatureCityMonthFcDO.setEnergy(monthReportDTO.getEnergy());
            loadFeatureCityMonthFcDO.setExtremeMaxLoad(monthReportDTO.getExtremeMaxLoad());
            loadFeatureCityMonthFcDO.setExtremeMinLoad(monthReportDTO.getExtremeMinLoad());
            loadFeatureCityMonthFcDO.setExtremeNoontimeLoad(monthReportDTO.getExtremeNoonTimeLoad());
            loadFeatureCityMonthFcDO.setExtremeEnergy(monthReportDTO.getExtremeEnergy());
            loadFeatureCityMonthFcDO.setNoontimeLoad(monthReportDTO.getNoonTimeLoad());
            loadFeatureCityMonthFcDO.setReportTime(new Timestamp(System.currentTimeMillis()));
            loadFeatureCityMonthFcService.saveOrUpdate(loadFeatureCityMonthFcDO);
        }
    }

    @Override
    public String getReportTime(String cityId, String caliberId, Date date, String season) throws Exception {
        List<String> monthList = Arrays.asList(Constants.WINTER_MONTHS);
        if ("2".equals(season)) {
            monthList = Arrays.asList(Constants.SUMMER_MONTHS);
        } else if ("5".equals(season)){
            monthList = DateUtil.getMonthList();
        }
        Date nextMonthDate = DateUtils.addMonths(date, 0);
        String year = DateUtil.getYearByDate(nextMonthDate);
        String month = DateUtil.getMonthDate(nextMonthDate);
        /*Map<String, List<LoadFeatureCityMonthFcDO>> collect = loadFeatureCityMonthFcService
            .getMonthReportsVO(cityId, caliberId, year, null).stream()
            .collect(Collectors.groupingBy(t -> t.getMonth()));*/
        Map<String, List<LoadFeatureCitySeasonFcDO>> collect = loadFeatureCityMonthFcService.getSeasonReportFeatures(
                year, null, cityId, caliberId, season).stream()
            .collect(Collectors.groupingBy(t -> t.getMonth()));
        if (!MapUtils.isEmpty(collect)){
            for (Entry<String, List<LoadFeatureCitySeasonFcDO>> stringListEntry : collect.entrySet()) {
                for (String s : monthList) {
                    if (s.equals(stringListEntry.getKey())) {
                        LoadFeatureCitySeasonFcDO loadFeatureCityMonthFcDO = stringListEntry.getValue().get(0);
                        if (loadFeatureCityMonthFcDO != null && loadFeatureCityMonthFcDO.getReportTime() != null) {
                            return DateUtil.getDateToStrFORMAT(loadFeatureCityMonthFcDO.getReportTime(), "yyyy-MM-dd HH:mm");
                        }
                    }
                }
            }
        }
        return "暂未上报";
    }

    @Override
    public List<MonthWeatherResultDTO> getWeatherSetting(MonthWeatherSettingDTO monthWeatherSettingDTO)
        throws Exception {
        List<MonthWeatherResultDTO> result = new ArrayList<>();
        String cityId = monthWeatherSettingDTO.getCityId();
        Integer type = monthWeatherSettingDTO.getType();
        List<String> hisCity = monthWeatherSettingDTO.getHisYearDate();
        String startDate1 = monthWeatherSettingDTO.getStartDate().substring(0, 7);
        String endDate1 = monthWeatherSettingDTO.getEndDate().substring(0, 7);
        List<WeatherFeatureCityMonthMdHisDO> res = new ArrayList<>();
        List<WeatherFeatureCityMonthHisDO> weatherFeatureCityMonthHisStat = weatherFeatureCityMonthHisService.findWeatherFeatureCityMonthHisStats(
            cityId, hisCity.get(0), hisCity.get(hisCity.size() - 1));
        Map<String, List<WeatherFeatureCityMonthHisDO>> collect3 = weatherFeatureCityMonthHisStat.stream()
            .collect(Collectors.groupingBy(WeatherFeatureCityMonthHisDO::getMonth));
        List<String> targetMonthList = new ArrayList<>();
        List<String> targetYearList = DateUtil.getTargetYearList(startDate1, endDate1);
        for (String s : targetYearList) {
            targetMonthList.add(s.split("-")[1]);
        }
        for (String s : targetYearList) {
            res.addAll(weatherFeatureCityMonthMdHisService.findWeatherFeatureCityTenDaysDTO(
                cityId, s.split("-")[0], s.split("-")[1]));
        }
        Map<String, List<WeatherFeatureCityMonthMdHisDO>> collect = res.stream()
            .collect(Collectors.groupingBy(t -> t.getYear() + "-" + t.getMonth()));
        if (type == 0) {
            for (String s2 : targetYearList) {
                String s = s2.split("-")[1];
                MonthWeatherResultDTO monthWeatherResultDTO = new MonthWeatherResultDTO();
                monthWeatherResultDTO.setDateTime(s2);
                for (String s1 : collect3.keySet()) {
                    if (s.equals(s1)) {
                        List<WeatherFeatureCityMonthHisDO> weatherFeatureCityMonthHisDOS = collect3.get(s1);
                        // 最高温度取平均
                        List<BigDecimal> maxList = weatherFeatureCityMonthHisDOS.stream()
                            .map(WeatherFeatureCityMonthHisDO::getHighestTemperature).collect(Collectors.toList());
                        // 最低温度取平均
                        List<BigDecimal> minList = weatherFeatureCityMonthHisDOS.stream()
                            .map(WeatherFeatureCityMonthHisDO::getLowestTemperature).collect(Collectors.toList());
                        //平均温度取平均
                        List<BigDecimal> aveList = weatherFeatureCityMonthHisDOS.stream()
                            .map(WeatherFeatureCityMonthHisDO::getAveTemperature).collect(Collectors.toList());
                        // 极端最高温度取平均
                        BigDecimal maxList1 =
                            com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(maxList)
                                .compareTo(Constants.normalTem) > 0
                                ? com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(
                                maxList).add(Constants.extremeTem)
                                : com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(maxList)
                                    .subtract(Constants.extremeTem);
                        // 极端最低温度取平均
                        BigDecimal minList1 =
                            com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(maxList)
                                .compareTo(Constants.normalTem) > 0
                                ? com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(
                                minList).add(Constants.extremeTem)
                                : com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(minList)
                                    .subtract(Constants.extremeTem);
                        // 极端平均温度取平均
                        BigDecimal aveList1 =
                            com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(maxList)
                                .compareTo(Constants.normalTem) > 0
                                ? com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(
                                aveList).add(Constants.extremeTem)
                                : com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(aveList)
                                    .subtract(Constants.extremeTem);
                        monthWeatherResultDTO.setMaxTem(
                            com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(maxList));
                        monthWeatherResultDTO.setMinTem(
                            com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(minList));
                        monthWeatherResultDTO.setAveTem(
                            com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(aveList));
                        monthWeatherResultDTO.setHisMaxTem(
                            com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(maxList));
                        monthWeatherResultDTO.setHisMinTem(
                            com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(minList));
                        monthWeatherResultDTO.setHisAveTem(
                            com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(aveList));
                        monthWeatherResultDTO.setExtremeMaxTem(maxList1);
                        monthWeatherResultDTO.setExtremeMinTem(minList1);
                        monthWeatherResultDTO.setExtremeAveTem(aveList1);
                        result.add(monthWeatherResultDTO);
                    }
                }
            }
        } else {
            for (String s2 : targetYearList) {
                String s = s2.split("-")[1];
                // 取气象年度特性表中数据
                MonthWeatherResultDTO monthWeatherResultDTO = new MonthWeatherResultDTO();
                monthWeatherResultDTO.setDateTime(s2);
                for (String s1 : collect.keySet()) {
                    if (s2.equals(s1)) {
                        List<WeatherFeatureCityMonthMdHisDO> weatherFeatureCityMonthMdHisDOS = collect.get(s1);
                        WeatherFeatureCityMonthMdHisDO weatherFeatureCityYearHisDO = weatherFeatureCityMonthMdHisDOS.get(
                            0);
                        monthWeatherResultDTO.setMaxTem(weatherFeatureCityYearHisDO.getMaxTem());
                        monthWeatherResultDTO.setMinTem(weatherFeatureCityYearHisDO.getMinTem());
                        monthWeatherResultDTO.setAveTem(weatherFeatureCityYearHisDO.getAveTem());
                        monthWeatherResultDTO.setExtremeMaxTem(weatherFeatureCityYearHisDO.getExtremeMaxTem());
                        monthWeatherResultDTO.setExtremeMinTem(weatherFeatureCityYearHisDO.getExtremeMinTem());
                        monthWeatherResultDTO.setExtremeAveTem(weatherFeatureCityYearHisDO.getExtremeAveTem());
                    }
                }
                for (String s1 : collect3.keySet()) {
                    if (s.equals(s1)) {
                        List<WeatherFeatureCityMonthHisDO> weatherFeatureCityMonthHisDOS = collect3.get(s1);
                        // 最高温度取平均
                        List<BigDecimal> maxList = weatherFeatureCityMonthHisDOS.stream()
                            .map(WeatherFeatureCityMonthHisDO::getHighestTemperature).collect(Collectors.toList());
                        // 最低温度取平均
                        List<BigDecimal> minList = weatherFeatureCityMonthHisDOS.stream()
                            .map(WeatherFeatureCityMonthHisDO::getLowestTemperature).collect(Collectors.toList());
                        //平均温度取平均
                        List<BigDecimal> aveList = weatherFeatureCityMonthHisDOS.stream()
                            .map(WeatherFeatureCityMonthHisDO::getAveTemperature).collect(Collectors.toList());
                        monthWeatherResultDTO.setHisMaxTem(
                            com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(maxList));
                        monthWeatherResultDTO.setHisMinTem(
                            com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(minList));
                        monthWeatherResultDTO.setHisAveTem(
                            com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(aveList));
                        result.add(monthWeatherResultDTO);
                    }
                }
            }
        }
        return result;
    }

    @Override
    public void saveWeatherSetting(List<MonthWeatherResultDTO> monthWeatherResultDTOS) throws Exception {
        String vaule = "";
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(monthWeatherResultDTOS)) {
            List<String> hisYearDate = monthWeatherResultDTOS.get(0).getHisYearDate();
            String season = monthWeatherResultDTOS.get(0).getSeason();
            for (String s : hisYearDate) {
                vaule = vaule + s + ",";
            }
            String substring = vaule.substring(0, vaule.length() - 1);
            ReportSystemInitDO reportSystemInitDO = new ReportSystemInitDO();
            if (season.equals("1")) {
                // 度夏
                reportSystemInitDO.setField(Constants.SUMMER_DEFAULT);
                reportSystemInitDO.setName("夏季历年平均年份");
            } else {
                // 度冬
                reportSystemInitDO.setField(Constants.WINTER_DEFAULT);
                reportSystemInitDO.setName("冬季历年平均年份");
            }
            reportSystemInitDO.setValue(substring);
            reportSystemInitDO.setCityId("1");
            reportSystemService.doSaveOrUpdate(reportSystemInitDO);
            for (MonthWeatherResultDTO monthWeatherResultDTO : monthWeatherResultDTOS) {
                WeatherFeatureCityMonthMdHisDO weatherFeatureCityYearHisDO = new WeatherFeatureCityMonthMdHisDO();
                weatherFeatureCityYearHisDO.setCityId(monthWeatherResultDTO.getCityId());
                weatherFeatureCityYearHisDO.setYear(monthWeatherResultDTO.getDateTime().split("-")[0]);
                weatherFeatureCityYearHisDO.setMonth(monthWeatherResultDTO.getDateTime().split("-")[1]);
                weatherFeatureCityYearHisDO.setHighestTemperature(monthWeatherResultDTO.getMaxTem());
                weatherFeatureCityYearHisDO.setLowestTemperature(monthWeatherResultDTO.getMinTem());
                weatherFeatureCityYearHisDO.setAveTemperature(monthWeatherResultDTO.getAveTem());
                weatherFeatureCityYearHisDO.setExtremeHighestTemperature(monthWeatherResultDTO.getExtremeMaxTem());
                weatherFeatureCityYearHisDO.setExtremeLowestTemperature(monthWeatherResultDTO.getExtremeMinTem());
                weatherFeatureCityYearHisDO.setExtremeAveTemperature(monthWeatherResultDTO.getExtremeAveTem());
                weatherFeatureCityYearHisDO.setType("1");
                if (monthWeatherResultDTO.getType() == 0) {
                    // 历史
                    weatherFeatureCityYearHisDO.setHighestTemperature(monthWeatherResultDTO.getMaxTem());
                    weatherFeatureCityYearHisDO.setLowestTemperature(monthWeatherResultDTO.getMinTem());
                    weatherFeatureCityYearHisDO.setAveTemperature(monthWeatherResultDTO.getAveTem());
                    weatherFeatureCityYearHisDO.setExtremeHighestTemperature(monthWeatherResultDTO.getExtremeMaxTem());
                    weatherFeatureCityYearHisDO.setExtremeLowestTemperature(monthWeatherResultDTO.getExtremeMinTem());
                    weatherFeatureCityYearHisDO.setExtremeAveTemperature(monthWeatherResultDTO.getExtremeAveTem());
                    weatherFeatureCityYearHisDO.setType("1");
                } else {
                    // 人工
                    weatherFeatureCityYearHisDO.setAveTem(monthWeatherResultDTO.getAveTem());
                    weatherFeatureCityYearHisDO.setMaxTem(monthWeatherResultDTO.getMaxTem());
                    weatherFeatureCityYearHisDO.setMinTem(monthWeatherResultDTO.getMinTem());
                    weatherFeatureCityYearHisDO.setExtremeAveTem(monthWeatherResultDTO.getExtremeAveTem());
                    weatherFeatureCityYearHisDO.setExtremeMaxTem(monthWeatherResultDTO.getExtremeMaxTem());
                    weatherFeatureCityYearHisDO.setExtremeMinTem(monthWeatherResultDTO.getExtremeMinTem());
                    weatherFeatureCityYearHisDO.setType("2");
                }
                weatherFeatureCityMonthMdHisService.doSaveOrUpdate(weatherFeatureCityYearHisDO);
            }
        }
    }

    @Override
    public List<MonthWeatherResultDTO> getWeatherInfo(String startStr, String endStr, String cityId) throws Exception {
        List<MonthWeatherResultDTO> result = new ArrayList<>();
        String startYear = startStr.split("-")[0];
        String endYear = endStr.split("-")[0];
        List<String> targetMonthList = DateUtil.getTargetYearList(startStr, endStr);
        List<WeatherFeatureCityMonthMdHisDO> weatherFeatureCityTenDaysDTO = new ArrayList<>();
        List<WeatherFeatureCityMonthHisDO> weatherFeatureCityHis = new ArrayList<>();
        Map<String, List<WeatherFeatureCityMonthMdHisDO>> collect = new HashMap<>();
        Map<String, List<WeatherFeatureCityMonthHisDO>> collectHis = new HashMap<>();
        if (startYear.equals(endYear)) {
            // 度夏
            for (String s : targetMonthList) {
                s = s.split("-")[1];
                if (weatherFeatureCityMonthHisService.getWeatherFeatureCityMonthHisDO(cityId,
                    String.valueOf(Integer.valueOf(startYear) - 1), s) != null) {
                    weatherFeatureCityHis.add(weatherFeatureCityMonthHisService.getWeatherFeatureCityMonthHisDO(cityId,
                        String.valueOf(Integer.valueOf(startYear) - 1), s));
                }
            }
        } else {
            // 度冬
            for (String s : targetMonthList) {
                s = s.split("-")[1];
                if (Integer.valueOf(s) > 6) {
                    if (weatherFeatureCityMonthHisService.getWeatherFeatureCityMonthHisDO(cityId,
                        String.valueOf(Integer.valueOf(startYear) - 1), s) != null) {
                        weatherFeatureCityHis.add(weatherFeatureCityMonthHisService.getWeatherFeatureCityMonthHisDO(cityId,
                            String.valueOf(Integer.valueOf(startYear) - 1), s));
                    }
                } else {
                    if (weatherFeatureCityMonthHisService.getWeatherFeatureCityMonthHisDO(cityId,
                        String.valueOf(Integer.valueOf(endYear) - 1), s) != null) {
                        weatherFeatureCityHis.add(weatherFeatureCityMonthHisService.getWeatherFeatureCityMonthHisDO(cityId,
                            String.valueOf(Integer.valueOf(endYear) - 1), s));
                    }
                }
            }
        }
        for (String s : targetMonthList) {
            if (!CollectionUtils.isEmpty(weatherFeatureCityMonthMdHisService.findWeatherFeatureCityTenDaysDTO(
                cityId, s.split("-")[0], s.split("-")[1]))) {
                weatherFeatureCityTenDaysDTO.addAll(weatherFeatureCityMonthMdHisService.findWeatherFeatureCityTenDaysDTO(
                        cityId, s.split("-")[0], s.split("-")[1]));
            }
        }
        if (!CollectionUtils.isEmpty(weatherFeatureCityTenDaysDTO)) {
             collect = weatherFeatureCityTenDaysDTO.stream()
                .collect(Collectors.groupingBy(t -> t.getMonth()));
        }
        if (!CollectionUtils.isEmpty(weatherFeatureCityHis)) {
            collectHis = weatherFeatureCityHis.stream()
                .collect(Collectors.groupingBy(t -> t.getMonth()));
        }
        for (String s : targetMonthList) {
            MonthWeatherResultDTO monthWeatherResultDTO = new MonthWeatherResultDTO();
            monthWeatherResultDTO.setDateTime(s);
            for (String s1 : collect.keySet()) {
                if (s.split("-")[1].equals(s1)) {
                    List<WeatherFeatureCityMonthMdHisDO> weatherFeatureCityMonthMdHisDOS = collect.get(s1);
                    WeatherFeatureCityMonthMdHisDO weatherFeatureCityMonthMdHisDO = weatherFeatureCityMonthMdHisDOS.get(
                        0);
                    if ("1".equals(weatherFeatureCityMonthMdHisDO.getType())) {
                        // 历史
                        monthWeatherResultDTO.setMaxTem(weatherFeatureCityMonthMdHisDO.getHighestTemperature());
                        monthWeatherResultDTO.setMinTem(weatherFeatureCityMonthMdHisDO.getLowestTemperature());
                        monthWeatherResultDTO.setAveTem(weatherFeatureCityMonthMdHisDO.getAveTemperature());
                    } else {
                        monthWeatherResultDTO.setMaxTem(weatherFeatureCityMonthMdHisDO.getMaxTem());
                        monthWeatherResultDTO.setMinTem(weatherFeatureCityMonthMdHisDO.getMinTem());
                        monthWeatherResultDTO.setAveTem(weatherFeatureCityMonthMdHisDO.getAveTem());
                    }
                }
            }
            for (String s1 : collectHis.keySet()) {
                if (s.split("-")[1].equals(s1)) {
                    List<WeatherFeatureCityMonthHisDO> weatherFeatureCityMonthHisDOS = collectHis.get(s1);
                    WeatherFeatureCityMonthHisDO weatherFeatureCityMonthHisDO = weatherFeatureCityMonthHisDOS.get(0);
                    monthWeatherResultDTO.setHisMaxTem(weatherFeatureCityMonthHisDO.getHighestTemperature());
                    monthWeatherResultDTO.setHisMinTem(weatherFeatureCityMonthHisDO.getLowestTemperature());
                    monthWeatherResultDTO.setHisAveTem(weatherFeatureCityMonthHisDO.getAveTemperature());
                }
            }
            result.add(monthWeatherResultDTO);
        }
        return result;
    }

    private List<TenDaysOfMonthFeatureDTO> genRandomTreeResult(String season, String cityId, String caliberId, Date date, Map<String, LoadFeatureCityMonthHisDO> cityMonthHisDOMap) {
        String year = DateUtil.getYearByDate(date);
        String nextYear = String.valueOf(Integer.parseInt(year) + 1);
        String lastYear = String.valueOf(Integer.parseInt(year) - 1);
        List<TenDaysOfMonthFeatureDTO> randomTreeList = new ArrayList<>();
        List<LoadFeatureCityTenDaysFcServiceDO> featureCityDayFcDOList = new ArrayList<>(16);
        List<String> months = QuarterUtil.getMonthsBySeason(season);
        List<String> monthList = new ArrayList<>();
        List<String> nextMonthList = new ArrayList<>();
        monthList.add(months.get(0));
        monthList.add(months.get(1));
        if (StringUtils.isEmpty(season)) {
            nextMonthList.add(months.get(2));
            nextMonthList.add(months.get(3));
            List<LoadFeatureCityTenDaysFcServiceDO> monthFeatureList = loadFeatureCityTenDaysFcService.findLoadFeatureCityTenDaysFcServiceDOs(cityId, caliberId, SeasonAlgorithmModelEnums.RANDOM_TREE.getId(), year, monthList);
            List<LoadFeatureCityTenDaysFcServiceDO> nextMonthFeatureList = loadFeatureCityTenDaysFcService.findLoadFeatureCityTenDaysFcServiceDOs(cityId, caliberId, SeasonAlgorithmModelEnums.RANDOM_TREE.getId(), nextYear, nextMonthList);
            if (!CollectionUtils.isEmpty(monthFeatureList)) {
                featureCityDayFcDOList.addAll(monthFeatureList);
            }
            if (!CollectionUtils.isEmpty(nextMonthFeatureList)) {
                featureCityDayFcDOList.addAll(nextMonthFeatureList);
            }
        } else {
            monthList.add(months.get(2));
            monthList.add(months.get(3));
            List<LoadFeatureCityTenDaysFcServiceDO> monthFeatureList = loadFeatureCityTenDaysFcService.findLoadFeatureCityTenDaysFcServiceDOs(cityId, caliberId, SeasonAlgorithmModelEnums.RANDOM_TREE.getId(), year, monthList);
            if (!CollectionUtils.isEmpty(monthFeatureList)) {
                featureCityDayFcDOList.addAll(monthFeatureList);
            }
        }

        if (!CollectionUtils.isEmpty(featureCityDayFcDOList)) {
            Map<String, List<LoadFeatureCityTenDaysFcServiceDO>> monthFeatureMap = featureCityDayFcDOList
                .stream()
                .collect(Collectors.groupingBy(LoadFeatureCityTenDaysFcServiceDO::getMonth));
            for (String month : months) {
                TenDaysOfMonthFeatureDTO tenDaysOfMonthFeatureDTO = new TenDaysOfMonthFeatureDTO();
                tenDaysOfMonthFeatureDTO.setAlgorithmName(SeasonAlgorithmModelEnums.RANDOM_TREE.getName());
                if ("01".equals(month) || "02".equals(month)) {
                    tenDaysOfMonthFeatureDTO.setType(nextYear + "年" + month + "月");
                    year = nextYear;
                } else {
                    tenDaysOfMonthFeatureDTO.setType(year + "年" + month + "月");
                }
                List<LoadFeatureCityTenDaysFcServiceDO> serviceDOList = monthFeatureMap.get(month);
                if (!CollectionUtils.isEmpty(serviceDOList)) {
                    Optional<LoadFeatureCityTenDaysFcServiceDO> maxLoad = serviceDOList.stream()
                        .max(Comparator.comparing(LoadFeatureCityTenDaysFcServiceDO::getMaxLoad));
                    if (maxLoad.isPresent()) {
                        LoadFeatureCityTenDaysFcServiceDO serviceDO = maxLoad.get();
                        tenDaysOfMonthFeatureDTO.setMaxLoad(serviceDO.getMaxLoad());
                    }
                    Optional<LoadFeatureCityTenDaysFcServiceDO> minLoad = serviceDOList.stream()
                        .min(Comparator.comparing(LoadFeatureCityTenDaysFcServiceDO::getMinLoad));
                    if (minLoad.isPresent()) {
                        LoadFeatureCityTenDaysFcServiceDO serviceDO = minLoad.get();
                        tenDaysOfMonthFeatureDTO.setMinLoad(serviceDO.getMinLoad());
                    }
                    BigDecimal sum = serviceDOList.stream().map(LoadFeatureCityTenDaysFcServiceDO::getEnergy).reduce(BigDecimal.ZERO, BigDecimal::add);
                    tenDaysOfMonthFeatureDTO.setMonthEnergy(sum);
                }
                LoadFeatureCityMonthHisDO loadFeatureCityMonthHisDO = cityMonthHisDOMap.get(month);
                if (loadFeatureCityMonthHisDO != null) {
                    tenDaysOfMonthFeatureDTO.setLastYearMaxLoad(loadFeatureCityMonthHisDO.getMaxLoad());
                    tenDaysOfMonthFeatureDTO.setLastYearMinLoad(loadFeatureCityMonthHisDO.getMinLoad());
                    tenDaysOfMonthFeatureDTO.setLastYearEnergy(loadFeatureCityMonthHisDO.getEnergy());
                }
                randomTreeList.add(tenDaysOfMonthFeatureDTO);
            }
        }
        return randomTreeList;
    }

    private String getCaliberId(String cityId){
        if (Constants.PROVINCE_TYPE == Integer.parseInt(cityId)){
            return Constants.CALIBER_ID_BG_QW;
        }else{
            return Constants.CALIBER_ID_BG_DS;
        }
    }

}
