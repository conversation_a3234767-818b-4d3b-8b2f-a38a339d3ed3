
package com.tsintergy.lf.serviceimpl.ultra.impl;


import com.tsieframework.core.base.dao.type.DataPointListMeta;
import com.tsieframework.core.base.math.BigDecimalFunctions;
import com.tsieframework.core.base.service.BaseFacadeServiceImpl;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.core.util.PeriodDataUtil;
import com.tsintergy.lf.core.util.UltraSystemUtils;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherEnum;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureStatService;
import com.tsintergy.lf.serviceapi.base.ultra.api.AccuracyLoadCityFcUltraService;
import com.tsintergy.lf.serviceapi.base.ultra.api.LoadCityHisUltraBasicService;
import com.tsintergy.lf.serviceapi.base.ultra.api.LoadFeatureCityFcUltraService;
import com.tsintergy.lf.serviceapi.base.ultra.api.UltraLoadAccuracyDayService;
import com.tsintergy.lf.serviceapi.base.ultra.dto.*;
import com.tsintergy.lf.serviceapi.base.ultra.enums.UltraMultipointFifteenEnum;
import com.tsintergy.lf.serviceapi.base.ultra.load.api.LoadCityFcUltraService;
import com.tsintergy.lf.serviceapi.base.ultra.load.pojo.LoadCityFcUltraDO;
import com.tsintergy.lf.serviceapi.base.ultra.pojo.AccuracyLoadCityFcUltraDO;
import com.tsintergy.lf.serviceapi.base.ultra.pojo.LoadCityHisUltraBasicDO;
import com.tsintergy.lf.serviceapi.base.ultra.pojo.LoadFeatureCityDayUltraFcDO;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityHisService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service("ultraLoadAccuracyDayService")
public class LoadAccuracyDayServiceImpl extends BaseFacadeServiceImpl implements UltraLoadAccuracyDayService {

    private final Logger logger = LoggerFactory.getLogger(LoadAccuracyDayServiceImpl.class);

    @Autowired
    private LoadFeatureCityFcUltraService loadFeatureCityFcUltraService;

    @Autowired
    private LoadFeatureStatService loadFeatureStatService;

    @Autowired
    private AccuracyLoadCityFcUltraService accuracyLoadCityFcService;

    @Autowired
    private LoadCityHisUltraBasicService loadCityHisService;

    @Autowired
    private LoadCityFcUltraService loadCityFcService;

    @Autowired
    private WeatherCityHisService weatherCityHisService;

    @Autowired
    private WeatherCityFcService weatherCityFcService;

    @Override
    public List<AccuracyUltraDayDTO> getLoadHistory(AccuracyUltraData data) throws Exception {
        //全时段查询
        List<LoadFeatureCityDayUltraFcDO> ultraFcDOS;
        Integer multipointType;
        if (DataPointListMeta.MINUTE_15 == data.getTimeSpan()) {
            multipointType = Constants.ULTRA_FORECAST_1;
        } else {
            //查询30分钟间隔数据
            data.setTimeSpan(DataPointListMeta.MINUTE_15);
            multipointType = UltraMultipointFifteenEnum.ULTRA_FORECAST_2.getType();
        }
        if (data.getStartTime() == null) {
            //改为实时计算
            List<String> columns;
            if (DataPointListMeta.MINUTE_15 == data.getTimeSpan()) {
                columns = ColumnUtil
                        .getColumns(DataPointListMeta.POINTS_96, UltraSystemUtils.startWithZero(), false);
            } else {
                columns = ColumnUtil.getColumns(DataPointListMeta.POINTS_288, UltraSystemUtils.startWithZero(), false);
            }
            ultraFcDOS = loadFeatureStatService
                    .calculateUltraFcLoadFeatureCityDay(data.getCityId(), data.getStartDate(), data.getEndDate(),
                            data.getCaliberId(), data.getAlgorithmId(), columns.get(0), columns.get(columns.size() - 1),
                            multipointType, data.getTimeSpan());
        }
        //分时段查询 需要重新计算；
        else {
            ultraFcDOS = loadFeatureStatService
                    .calculateUltraFcLoadFeatureCityDay(data.getCityId(), data.getStartDate(), data.getEndDate(),
                            data.getCaliberId(), data.getAlgorithmId(), data.getStartTime(), data.getEndTime(),
                            multipointType, data.getTimeSpan());
        }
        return process(ultraFcDOS);
    }


    @Override
    public TimeAccuracyDTO getTimeAccuracy(AccuracyUltraData data) {
        TimeAccuracyDTO result = new TimeAccuracyDTO();
        Integer multipointType;
        if (DataPointListMeta.MINUTE_15 == data.getTimeSpan()) {
            multipointType = Constants.ULTRA_FORECAST_1;
        } else {
            //查询30分钟间隔数据
            data.setTimeSpan(DataPointListMeta.MINUTE_15);
            multipointType = UltraMultipointFifteenEnum.ULTRA_FORECAST_2.getType();
        }
        List<BigDecimal> loadHis = loadCityHisService
            .findLoadCityHisDO(data.getCityId(), data.getDate(), data.getDate(), data.getCaliberId(),
                data.getTimeSpan());
        LoadCityFcUltraDO loadFc = loadCityFcService
            .findLoadFc(data.getCityId(), data.getCaliberId(), data.getAlgorithmId(), data.getDate(),
                data.getTimeSpan(), null, multipointType);
        List<AccuracyLoadCityFcUltraDO> accuracyLoadCityFcDOS = accuracyLoadCityFcService
            .findShortMultipointFc(data.getDate(), data.getDate(), data.getCityId(), data.getCaliberId(),
                data.getTimeSpan(), data.getAlgorithmId(), multipointType);
        if (CollectionUtils.isEmpty(accuracyLoadCityFcDOS)) {
            return null;
        }
        if (data.getStartTime() == null) {
            result.setHis(loadHis);
            result.setFc(loadFc.getTvData());
            result.setAccuracy(accuracyLoadCityFcDOS.get(0).getTvData());
            result.setDeviation(BigDecimalFunctions.listSubtract(loadFc.getTvData(), loadHis));
        } else {
            result.setHis(captureByTime(loadHis, data.getStartTime(), data.getEndTime()));
            result.setFc(captureByTime(loadFc.getTvData(), data.getStartTime(), data.getEndTime()));
            result.setAccuracy(
                captureByTime(accuracyLoadCityFcDOS.get(0).getTvData(), data.getStartTime(), data.getEndTime()));
            result.setDeviation(BigDecimalFunctions.listSubtract(result.getFc(), result.getHis()));
        }
        return result;
    }

    @Override
    public AccuracyLowDetailsDayDTO getAccuracyLowDetails(AccuracyUltraData data) throws Exception {
        AccuracyLowDetailsDayDTO result = new AccuracyLowDetailsDayDTO();
        Integer multipointType;
        if (DataPointListMeta.MINUTE_15 == data.getTimeSpan()) {
            multipointType = Constants.ULTRA_FORECAST_1;
        } else {
            //查询30分钟间隔数据
            data.setTimeSpan(DataPointListMeta.MINUTE_15);
            multipointType = UltraMultipointFifteenEnum.ULTRA_FORECAST_2.getType();
        }
        int pointSize =
            DataPointListMeta.MINUTE_5 == data.getTimeSpan() ? DataPointListMeta.POINTS_288
                : DataPointListMeta.POINTS_96;
        List<String> columns = ColumnUtil.getColumns(pointSize, UltraSystemUtils.startWithZero(), false);
        int index = columns.indexOf(data.getTime());
        List<BigDecimal> weatherHis = this.weatherCityHisService
            .find96WeatherCityHisValue(data.getDate(), data.getCityId(), WeatherEnum.TEMPERATURE.getType());
        List<BigDecimal> weatherFc = this.weatherCityFcService
            .find96WeatherCityFcValue(data.getDate(), data.getCityId(), WeatherEnum.TEMPERATURE.getType());
        if (DataPointListMeta.MINUTE_5 == data.getTimeSpan()) {
            weatherHis= PeriodDataUtil.data96to288(weatherHis, UltraSystemUtils.startWithZero());
            weatherFc= PeriodDataUtil.data96to288(weatherFc, UltraSystemUtils.startWithZero());
        }
        List<BigDecimal> loadHis = loadCityHisService
            .findLoadCityHisDO(data.getCityId(), data.getDate(), data.getDate(), data.getCaliberId(),
                data.getTimeSpan());
        LoadCityFcUltraDO loadFc = loadCityFcService
            .findLoadFc(data.getCityId(), data.getCaliberId(), data.getAlgorithmId(), data.getDate(),
                data.getTimeSpan(), null, multipointType);
        result.setDateTime(data.getDate());
        result.setHisLoad(loadHis.get(index));
        result.setFcLoad(loadFc.getTvData().get(index));
        result.setDeviationLoad(BigDecimalFunctions.subtract(result.getFcLoad(), result.getHisLoad()));
        result.setHisTemp(weatherHis.get(index));
        result.setFcTemp(weatherFc.get(index));
        result.setDeviationTemp(BigDecimalFunctions.subtract(result.getFcTemp(), result.getHisTemp()));
        return result;
    }


    @Override
    public MultiPeriodAccuracyDTO getMultiPeriodAccuracy(AccuracyUltraData data) throws Exception {
        List<MultiPeriodAccuracyDTO> resultList = new ArrayList<>();
        List<AccuracyLoadCityFcUltraDO> multipointFc = this.accuracyLoadCityFcService
            .findShortMultipointFc(data.getDate(), data.getDate(), data.getCityId(), data.getCaliberId(),
                data.getTimeSpan(), data.getAlgorithmId(), null);
        List<LoadCityFcUltraDO> loadCityFcDO = this.loadCityFcService
            .getLoadCityFcDO(data.getCityId(), data.getCaliberId(), data.getAlgorithmId(), data.getDate(),
                data.getDate(), data.getTimeSpan(), null);
        Map<Integer, AccuracyLoadCityFcUltraDO> collectAccuracy = multipointFc.stream()
            .collect(Collectors.toMap(AccuracyLoadCityFcUltraDO::getMultipointType, Function.identity()));
        Map<Integer, LoadCityFcUltraDO> collectFc = loadCityFcDO.stream()
            .collect(Collectors.toMap(LoadCityFcUltraDO::getMultipointType, Function.identity()));
        List<LoadCityHisUltraBasicDO> loadUltraCityHisDOS = this.loadCityHisService
            .getLoadCityHisDOS(data.getCityId(), data.getCaliberId(), data.getDate(), data.getDate(),
                data.getTimeSpan());
        if (CollectionUtils.isEmpty(loadUltraCityHisDOS)) {
            return null;
        }
        List<MultiPeriodAccuracyUnitDTO> accuracyList = new ArrayList<>();
        List<MultiPeriodLoadUnitDTO> deviationList = new ArrayList<>();
        List<MultiPeriodLoadUnitDTO> fcList = new ArrayList<>();


        for (Map.Entry<Integer, LoadCityFcUltraDO> map : collectFc.entrySet()) {
            MultiPeriodLoadUnitDTO fcUnitDTO = new MultiPeriodLoadUnitDTO();
            fcUnitDTO.setType(map.getKey());
            fcUnitDTO.setValue(map.getValue().getTvData());
            MultiPeriodAccuracyUnitDTO accuracyUnitDTO = new MultiPeriodAccuracyUnitDTO();
            accuracyUnitDTO.setType(map.getKey());
            AccuracyLoadCityFcUltraDO accuracyLoadCityFcDO = collectAccuracy.get(map.getKey());
            if (accuracyLoadCityFcDO == null) {
                continue;
            }
            accuracyUnitDTO.setValue(accuracyLoadCityFcDO.getTvData());
            MultiPeriodLoadUnitDTO deviationUnitDTO = new MultiPeriodLoadUnitDTO();
            deviationUnitDTO.setType(map.getKey());
            deviationUnitDTO
                .setValue(BigDecimalFunctions.listSubtract(fcUnitDTO.getValue(), loadUltraCityHisDOS.get(0).getTvData()));
            fcList.add(fcUnitDTO);
            accuracyList.add(accuracyUnitDTO);
            deviationList.add(deviationUnitDTO);
        }


        for(LoadCityHisUltraBasicDO loadCityHisUltraBasicDO:loadUltraCityHisDOS){
            MultiPeriodLoadUnitDTO fcUnitDTO = new MultiPeriodLoadUnitDTO();
            fcUnitDTO.setType(0);
            fcUnitDTO.setValue(loadCityHisUltraBasicDO.getTvData());
            fcList.add(fcUnitDTO);
        }

        MultiPeriodAccuracyDTO result = new MultiPeriodAccuracyDTO();
        result.setAccuracyList(accuracyList);
        result.setDeviationList(deviationList);
        result.setFcList(fcList);
        return result;
    }

    /**
     * 结果对象转换
     *
     * @param ultraFcDOS 数据库查询的准确率特性数据
     */
    private List<AccuracyUltraDayDTO> process(List<LoadFeatureCityDayUltraFcDO> ultraFcDOS) {
        List<AccuracyUltraDayDTO> resultList = new ArrayList<>();
        for (LoadFeatureCityDayUltraFcDO src : ultraFcDOS) {
            AccuracyUltraDayDTO result = new AccuracyUltraDayDTO();
            BeanUtils.copyProperties(src, result);
            resultList.add(result);
        }
        return resultList;
    }

    private List<BigDecimal> captureByTime(List<BigDecimal> src, String startTime, String endTime) {
        int pointSize = src.size();
        List<String> columns = ColumnUtil.getColumns(pointSize, UltraSystemUtils.startWithZero(), false);
        return src.subList(columns.indexOf(startTime), columns.indexOf(endTime) + 1);
    }

}
