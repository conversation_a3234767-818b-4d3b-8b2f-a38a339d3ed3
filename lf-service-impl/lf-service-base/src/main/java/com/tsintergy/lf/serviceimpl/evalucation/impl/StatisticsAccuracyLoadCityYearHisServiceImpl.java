/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.evalucation.impl;

import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.evalucation.api.StatisticsAccuracyLoadCityYearHisService;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.MultipleAccuracyDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.SingleAccuraryDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.StatisticsAccuracyLoadCityMonthHisDO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.StatisticsAccuracyLoadCityYearHisDO;
import com.tsintergy.lf.serviceimpl.evalucation.dao.StatisticsAccuracyLoadCityMonthHisDAO;
import com.tsintergy.lf.serviceimpl.evalucation.dao.StatisticsAccuracyLoadCityYearHisDAO;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/3/31 15:14
 * @Version: 1.0.0
 */
@Service("statisticsAccuracyLoadCityYearHisService")
public class StatisticsAccuracyLoadCityYearHisServiceImpl implements StatisticsAccuracyLoadCityYearHisService {

    @Autowired
    private StatisticsAccuracyLoadCityYearHisDAO statisticsAccuracyLoadCityYearHisDAO;

    @Autowired
    private StatisticsAccuracyLoadCityMonthHisDAO statisticsAccuracyLoadCityMonthHisDAO;

    @Override
    public List<MultipleAccuracyDTO> getAvgAccuracyLoadCityYearHisDOs(Map<String, String> cityMap, String caliberId,
        String startYear, String endYear) throws Exception {
        List<MultipleAccuracyDTO> accuracyDTOList = new ArrayList<>();
        for(Map.Entry<String,String> city : cityMap.entrySet()){
            List<StatisticsAccuracyLoadCityYearHisDO> datas = statisticsAccuracyLoadCityYearHisDAO.getStatisticsAccuracyLoadCityMonthHisDOs(city.getKey(), caliberId, startYear, endYear);
            if(CollectionUtils.isEmpty(datas)){
                continue;
            }

            MultipleAccuracyDTO dto = new MultipleAccuracyDTO();
            BigDecimal avgAccuracy = new BigDecimal("0.0000");
            for(StatisticsAccuracyLoadCityYearHisDO hisDO : datas){
                avgAccuracy = avgAccuracy.add(hisDO.getAccuracy());
            }
            dto.setCityId(city.getKey());
            dto.setName(city.getValue());
            dto.setAveAccuracy(avgAccuracy.divide(new BigDecimal(datas.size()),4));
            accuracyDTOList.add(dto);
        }

        return accuracyDTOList;
    }

    /**
     * 统计年准确率
     */
    @Override
    public List<StatisticsAccuracyLoadCityYearHisDO> getReportYearAccuracy(List<CityDO> cityVOS, String caliberId,
        String startY, String endY) throws Exception {

        List<StatisticsAccuracyLoadCityYearHisDO> cityYearHisDOList = new ArrayList<>();
        for (CityDO city : cityVOS) {
            int startYear = Integer.valueOf(startY);
            int endYear = Integer.valueOf(endY);
            for (int year = startYear; year <= endYear; year++) {
                StatisticsAccuracyLoadCityYearHisDO statisticsAccuracyLoadCityYearHisDO = new StatisticsAccuracyLoadCityYearHisDO();
                List<StatisticsAccuracyLoadCityMonthHisDO> accuracyMonth = statisticsAccuracyLoadCityMonthHisDAO
                    .findAccuracyMonth(String.valueOf(year), null, city.getId(), caliberId);
                if (CollectionUtils.isEmpty(accuracyMonth)) {
                    continue;
                }

                BigDecimal avgAccuracy = getAvgAccuracy(accuracyMonth);
                statisticsAccuracyLoadCityYearHisDO.setCityId(city.getId());
                statisticsAccuracyLoadCityYearHisDO.setAccuracy(avgAccuracy);
                statisticsAccuracyLoadCityYearHisDO.setYear(String.valueOf(year));
                statisticsAccuracyLoadCityYearHisDO.setCaliberId(caliberId);
                cityYearHisDOList.add(statisticsAccuracyLoadCityYearHisDO);
            }
        }
        return cityYearHisDOList;
    }
    /**
     * 获取城市年准确率列表
     * @param cityName
     * @param cityId
     * @param caliberId
     * @param startYear
     * @param endYear
     * @return
     * @throws Exception
     */
    @Override
    public List<SingleAccuraryDTO> getStatisticsAccuracyLoadCityYearHisDOs(String cityName, String cityId, String caliberId, String startYear, String endYear) throws Exception {
        List<SingleAccuraryDTO> singleAccuraryDTOList = new ArrayList<>();
        List<StatisticsAccuracyLoadCityYearHisDO> datas = statisticsAccuracyLoadCityYearHisDAO.getStatisticsAccuracyLoadCityMonthHisDOs(cityId, caliberId, startYear, endYear);
        //数据为空时直接返回空列表
        if(CollectionUtils.isEmpty(datas)){
            return singleAccuraryDTOList;
        }

        for(StatisticsAccuracyLoadCityYearHisDO hisDO :datas){
            SingleAccuraryDTO singleAccuraryDTO = new SingleAccuraryDTO();
            singleAccuraryDTO.setDatetime(hisDO.getYear());
            singleAccuraryDTO.setAccuracy(hisDO.getAccuracy());
            singleAccuraryDTO.setCityName(cityName);
            singleAccuraryDTOList.add(singleAccuraryDTO);
        }
        return singleAccuraryDTOList;
    }

    @Override
    public void doSaveOrUpdate(List<StatisticsAccuracyLoadCityYearHisDO> reportAccuracyYearDOS) throws Exception {
        if (CollectionUtils.isEmpty(reportAccuracyYearDOS) || reportAccuracyYearDOS.size() < 1) {
            return;
        }
        for (StatisticsAccuracyLoadCityYearHisDO accuracyYearVO : reportAccuracyYearDOS) {
            List<StatisticsAccuracyLoadCityYearHisDO> month = statisticsAccuracyLoadCityYearHisDAO
                .findAccuracyMonth(accuracyYearVO.getYear(), accuracyYearVO.getCityId(), accuracyYearVO.getCaliberId());
            if (!CollectionUtils.isEmpty(month) && month.size() > 0) {
                StatisticsAccuracyLoadCityYearHisDO monthVO = month.get(0);
                BeanUtils.copyProperties(accuracyYearVO, monthVO, "id");
                monthVO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
                statisticsAccuracyLoadCityYearHisDAO.updateAndFlush(monthVO);
            } else {
                statisticsAccuracyLoadCityYearHisDAO.createAndFlush(accuracyYearVO);
            }
        }
    }

    private BigDecimal getAvgAccuracy(List<StatisticsAccuracyLoadCityMonthHisDO> dayAccuracyList) {

        BigDecimal avgAccuracy = new BigDecimal("0.0000");
        if (CollectionUtils.isEmpty(dayAccuracyList)) {
            return avgAccuracy;
        }
        for (StatisticsAccuracyLoadCityMonthHisDO statisticsCityDayFcDO : dayAccuracyList) {
            avgAccuracy = avgAccuracy.add(statisticsCityDayFcDO.getAccuracy());
        }

        return avgAccuracy.divide(new BigDecimal(dayAccuracyList.size()), 4);
    }
}