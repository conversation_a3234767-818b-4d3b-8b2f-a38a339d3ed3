package com.tsintergy.lf.serviceimpl.bgd.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsieframework.core.base.service.BaseFacadeServiceImpl;
import com.tsintergy.lf.serviceapi.base.bgd.api.IndustryBaseInitService;
import com.tsintergy.lf.serviceapi.base.bgd.pojo.IndustryBaseInitDO;
import com.tsintergy.lf.serviceimpl.bgd.dao.IndustryBaseInitDAO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Description: TODO <br>
 *
 * <AUTHOR>
 * @create 2023-02-16
 * @since 1.0.0
 */
@Service("industryBaseInitService")
public class IndustryBaseInitServiceImpl extends BaseFacadeServiceImpl implements IndustryBaseInitService {

    @Autowired
    IndustryBaseInitDAO industryBaseInitDAO;

    @Override
    public List<IndustryBaseInitDO> getIndustryBaseInitServiceListByTypes(List<String> types) {
        return industryBaseInitDAO.findAll(JpaWrappers.<IndustryBaseInitDO>lambdaQuery()
                .in(IndustryBaseInitDO::getType, types));
    }

    @Override
    public List<IndustryBaseInitDO> getIndustryBaseInitServiceListById(String parentId) {
        return industryBaseInitDAO.findAll(JpaWrappers.<IndustryBaseInitDO>lambdaQuery()
            .eq(IndustryBaseInitDO::getParentId,parentId));
    }

    @Override
    public List<IndustryBaseInitDO> getIndustryBaseInitServiceListByName(String name) {
        return industryBaseInitDAO.findAll(JpaWrappers.<IndustryBaseInitDO>lambdaQuery().eq(IndustryBaseInitDO::getName, name));
    }

    @Override
    public List<IndustryBaseInitDO> getIndustryBaseInitServiceListByTypes() {
        return industryBaseInitDAO.findAll();
    }

    @Override
    public List<IndustryBaseInitDO> getIndustryBaseInitServiceListByLevel(String level) {
        return industryBaseInitDAO.findAll(JpaWrappers.<IndustryBaseInitDO>lambdaQuery().eq(IndustryBaseInitDO::getLevel, level));
    }
}
