package com.tsintergy.lf.serviceimpl.weather.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsieframework.util.compatible.StringUtils;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityTenDaysHisService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityTenDaysHisDO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherFeatureCityTenDaysHisDAO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2023-06-13
 * @since 1.0.0
 */
@Service("weatherFeatureCityTenDaysHisService")
public class WeatherFeatureCityTenDaysHisServiceImpl extends BaseServiceImpl implements
    WeatherFeatureCityTenDaysHisService {

    @Autowired
    private WeatherFeatureCityTenDaysHisDAO weatherFeatureCityTenDaysHisDAO;

    @Override
    public List<WeatherFeatureCityTenDaysHisDO> findWeatherFeatureCityTenDaysDTO(String cityId, String year,
                                                                                 String month, String type) {
        return weatherFeatureCityTenDaysHisDAO.findAll(JpaWrappers.<WeatherFeatureCityTenDaysHisDO>lambdaQuery()
                .eq(!StringUtils.isEmpty(cityId), WeatherFeatureCityTenDaysHisDO::getCityId, cityId)
                .eq(!StringUtils.isEmpty(year), WeatherFeatureCityTenDaysHisDO::getYear, year)
                .eq(!StringUtils.isEmpty(month), WeatherFeatureCityTenDaysHisDO::getMonth, month)
                .eq(!StringUtils.isEmpty(type), WeatherFeatureCityTenDaysHisDO::getType, type)
        );
    }

    @Override
    public List<WeatherFeatureCityTenDaysHisDO> findByRangeCondition(String cityId, String startYM, String endYM) {
        List<WeatherFeatureCityTenDaysHisDO> weatherFeatureCityTenDaysHisDOS = weatherFeatureCityTenDaysHisDAO.findAll(JpaWrappers.<WeatherFeatureCityTenDaysHisDO>lambdaQuery()
                .ge(!org.springframework.util.StringUtils.isEmpty(startYM), WeatherFeatureCityTenDaysHisDO::getYear, startYM.substring(0, 4))
                .le(!org.springframework.util.StringUtils.isEmpty(endYM), WeatherFeatureCityTenDaysHisDO::getYear, endYM.substring(0, 4))
                .eq(!org.springframework.util.StringUtils.isEmpty(cityId), WeatherFeatureCityTenDaysHisDO::getCityId, cityId)
        );

        List<WeatherFeatureCityTenDaysHisDO> featureCityTenDaysHisDOS = new ArrayList<>();
        for (WeatherFeatureCityTenDaysHisDO loadFeatureCityMonthHisVO : weatherFeatureCityTenDaysHisDOS) {
            if ((loadFeatureCityMonthHisVO.getYear() + "-" + loadFeatureCityMonthHisVO.getMonth()).compareTo(startYM) > -1
                    && (loadFeatureCityMonthHisVO.getYear() + "-" + loadFeatureCityMonthHisVO.getMonth()).compareTo(endYM) < 1) {
                featureCityTenDaysHisDOS.add(loadFeatureCityMonthHisVO);
            }
        }
        return featureCityTenDaysHisDOS;
    }

    @Override
    public void doSaveOrUpdate(WeatherFeatureCityTenDaysHisDO weatherFeatureCityTenDaysHisDO) {
        List<WeatherFeatureCityTenDaysHisDO> result = weatherFeatureCityTenDaysHisDAO.findAll(
                JpaWrappers.<WeatherFeatureCityTenDaysHisDO>lambdaQuery()
                        .eq(!StringUtils.isEmpty(weatherFeatureCityTenDaysHisDO.getCityId()),
                                WeatherFeatureCityTenDaysHisDO::getCityId, weatherFeatureCityTenDaysHisDO.getCityId())
                        .eq(!StringUtils.isEmpty(weatherFeatureCityTenDaysHisDO.getYear()),
                                WeatherFeatureCityTenDaysHisDO::getYear, weatherFeatureCityTenDaysHisDO.getYear())
                        .eq(!StringUtils.isEmpty(weatherFeatureCityTenDaysHisDO.getMonth()),
                                WeatherFeatureCityTenDaysHisDO::getMonth, weatherFeatureCityTenDaysHisDO.getMonth())
                        .eq(!StringUtils.isEmpty(weatherFeatureCityTenDaysHisDO.getType()),
                                WeatherFeatureCityTenDaysHisDO::getType, weatherFeatureCityTenDaysHisDO.getType())
                        .eq(weatherFeatureCityTenDaysHisDO.getStatus(), WeatherFeatureCityTenDaysHisDO::getStatus,
                                weatherFeatureCityTenDaysHisDO.getStatus()));

        if (CollectionUtils.isEmpty(result)) {
            weatherFeatureCityTenDaysHisDAO.save(weatherFeatureCityTenDaysHisDO);
        } else {
            String id = result.get(0).getId();
            weatherFeatureCityTenDaysHisDO.setId(id);
            weatherFeatureCityTenDaysHisDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            weatherFeatureCityTenDaysHisDAO.saveOrUpdateByTemplate(weatherFeatureCityTenDaysHisDO);
        }
    }
}
