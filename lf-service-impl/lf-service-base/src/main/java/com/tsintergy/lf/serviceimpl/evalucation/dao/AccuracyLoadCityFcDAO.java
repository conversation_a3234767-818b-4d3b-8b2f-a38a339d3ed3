package com.tsintergy.lf.serviceimpl.evalucation.dao;

import com.tsieframework.core.base.dao.hibernate.BaseAbstractDAO;
import com.tsieframework.core.base.dao.hibernate.BaseDAO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.util.compatible.BigDecimalUtils;
import com.tsieframework.util.compatible.StringUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.AccuracyLoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.core.util.LoadCalUtil;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 * @version $Id: AccuracyLoadCityFcDAO.java, v 0.1 2018-01-31 10:15:01 tao Exp $$
 */

@Component
public class AccuracyLoadCityFcDAO extends BaseAbstractDAO<AccuracyLoadCityFcDO> {

    private static final Logger logger = LoggerFactory.getLogger(AccuracyLoadCityFcDAO.class);
    /**
     * 计算预测准确率
     *
     * @param loadCityHisDO 历史数据
     * @param loadCityFcDO  预测数据
     * @return
     */
    public AccuracyLoadCityFcDO calculateAccuracy(LoadCityHisDO loadCityHisDO, LoadCityFcDO loadCityFcDO) throws Exception {

        if (!loadCityHisDO.getCityId().equals(loadCityFcDO.getCityId())) {
            logger.error("历史数据和预测数据的城市不同，不可以计算");
            return null;
        }

        if (!loadCityHisDO.getDate().equals(loadCityFcDO.getDate())) {
            logger.error("历史数据和预测数据的日期不同，不可以计算");
            return null;
        }

        Map<String, BigDecimal> hisDataMap = BasePeriodUtils.toMap(loadCityHisDO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
        Map<String, BigDecimal> fcDataMap = BasePeriodUtils.toMap(loadCityFcDO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
        Map<String, BigDecimal> accuracyMap = new HashMap<String, BigDecimal>();

        for (String column : hisDataMap.keySet()) {
            accuracyMap.put(column, LoadCalUtil.getAccuracy(hisDataMap.get(column), fcDataMap.get(column)));
        }

        AccuracyLoadCityFcDO accuracyLoadCityFcDO = new AccuracyLoadCityFcDO();
        accuracyLoadCityFcDO.setAlgorithmId(loadCityFcDO.getAlgorithmId());
        accuracyLoadCityFcDO.setCityId(loadCityFcDO.getCityId());
        accuracyLoadCityFcDO.setDate(loadCityFcDO.getDate());
        accuracyLoadCityFcDO.setCaliberId(loadCityFcDO.getCaliberId());
        accuracyLoadCityFcDO.setReport(loadCityFcDO.getReport());
        BasePeriodUtils.setAllFiled(accuracyLoadCityFcDO, accuracyMap);

        return accuracyLoadCityFcDO;
    }

   

    /**
     * 计算指定时段内的平均准确率
     *
     * @param accuracyLoadCityFcDOS 准确率列表
     * @param startPeriod           开始时段
     * @param endPeriod             结束时段
     * @return
     */
    public BigDecimal calculateAvgAccuracy(List<AccuracyLoadCityFcDO> accuracyLoadCityFcDOS, String startPeriod, String endPeriod) {
        List<BigDecimal> datas = new ArrayList<BigDecimal>();
        if (accuracyLoadCityFcDOS != null) {
            for (AccuracyLoadCityFcDO accuracyLoadCityFcDO : accuracyLoadCityFcDOS) {
                Map<String, BigDecimal> map = BasePeriodUtils.toMap(accuracyLoadCityFcDO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
                for (String column : map.keySet()) {
                    if (column.replace("t", "").compareTo(startPeriod) > -1 && column.replace("t", "").compareTo(endPeriod) < 1) {
                        datas.add(map.get(column));
                    }
                }
            }
        }

        BigDecimal avg = BigDecimalUtils.avgList(datas, 4, false);
        return avg;
    }

        /**
     * 计算指定时段内的平均准确率
     *
     * @param accuracyLoadCityFcDO 准确率
     * @param startPeriod          开始时段
     * @param endPeriod            结束时段
     * @return
     */
    public BigDecimal calculateAvgAccuracy(AccuracyLoadCityFcDO accuracyLoadCityFcDO, String startPeriod, String endPeriod) {
        List<AccuracyLoadCityFcDO> list = new ArrayList<AccuracyLoadCityFcDO>();
        list.add(accuracyLoadCityFcDO);
        return calculateAvgAccuracy(list, startPeriod, endPeriod);
    }



    /**
     *    
     *  功能描述: <br> 
     * 获取地区负荷预测准确率
     *
     * @param cityIds     地区ID列表
     * @param caliberId   口径ID
     * @param algorithmId 算法
     * @param startDate   开始时间
     * @param endDate     结束时间
     * @return 地区负荷预测准确率列表
     *  @since: 1.0.0   
     *  @Author:zhaoml   
     *  @Date: 2018/4/23 14:23   
     */
    public List<AccuracyLoadCityFcDO> getAccuracyLoadCityFcDOs(List<String> cityIds, String caliberId, String algorithmId, Date startDate, Date endDate, Boolean isReport) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (null != startDate) {
            param.getQueryConditions().put("_dnl_date", new java.sql.Date(startDate.getTime()));
        }
        if (null != endDate) {
            param.getQueryConditions().put("_dnm_date", new java.sql.Date(endDate.getTime()));
        }
        if (null != algorithmId) {
            param.getQueryConditions().put("_ne_algorithmId", algorithmId);
        }
        if (null != cityIds) {
            param.getQueryConditions().put("_sin_cityId", cityIds);
        }
        if (null != caliberId) {
            param.getQueryConditions().put("_se_caliberId", caliberId);
        }
        if (null != isReport) {
            param.getQueryConditions().put("_ne_report", isReport);
        }
        param.setOrderby("date");
        param.setDesc("0");
        List<AccuracyLoadCityFcDO> accuracyLoadCityFcDOList = this.query(param).getDatas();
        return accuracyLoadCityFcDOList;
    }

   

    /**
     * 计算预测准确率
     *
     * @param loadCityHisVOs 历史数据
     * @param loadCityFcDOS  预测数据
     * @return
     */
    public List<AccuracyLoadCityFcDO> calculateAccuracy(List<LoadCityHisDO> loadCityHisVOs, List<LoadCityFcDO> loadCityFcDOS) throws Exception {

        Map<String, LoadCityHisDO> loadCityHisVOMap = new HashMap<String, LoadCityHisDO>();
        for (LoadCityHisDO loadCityHisVO : loadCityHisVOs) {
            String key = loadCityHisVO.getCityId() + "-" + loadCityHisVO.getCaliberId() + "-" + loadCityHisVO.getDate().getTime();
            loadCityHisVOMap.put(key, loadCityHisVO);
        }

        Map<String, List<LoadCityFcDO>> loadCityFcVOMap = new HashMap<String, List<LoadCityFcDO>>();
        for (LoadCityFcDO loadCityFcDO : loadCityFcDOS) {
            String key = loadCityFcDO.getCityId() + "-" + loadCityFcDO.getCaliberId() + "-" + loadCityFcDO.getDate().getTime();
            if (loadCityFcVOMap.get(key) == null) {
                loadCityFcVOMap.put(key, new ArrayList<LoadCityFcDO>());
            }
            loadCityFcVOMap.get(key).add(loadCityFcDO);
        }

        List<AccuracyLoadCityFcDO> accuracyLoadCityFcVOs = new ArrayList<AccuracyLoadCityFcDO>();
        for (String key : loadCityHisVOMap.keySet()) {
            if (loadCityFcVOMap.get(key) != null) {
                for (LoadCityFcDO loadCityFcDO : loadCityFcVOMap.get(key)) {
                    try {
                        accuracyLoadCityFcVOs.add(this.calculateAccuracy(loadCityHisVOMap.get(key), loadCityFcDO));
                    } catch (Exception e) {
                        logger.error("统计准确率出错了", e);
                    }
                }
            }
        }

        return accuracyLoadCityFcVOs;
    }



    /**
     *    
     *  功能描述: <br> 
     * 获取地区负荷预测准确率
     *
     * @param cityId      地区ID
     * @param caliberId   口径ID
     * @param algorithmId 算法
     * @param startDate   开始时间
     * @param endDate     结束时间
     * @return 地区负荷预测准确率列表
     *  @Author:zhaoml   
     *  @Date: 2018/4/23 14:15   
     */
    public List<AccuracyLoadCityFcDO> getAccuracyLoadCityFcDOs(String cityId, String caliberId, String algorithmId, Date startDate, Date endDate, Boolean isReport) throws Exception {

        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        if (null != startDate) {
            param.getQueryConditions().put("_dnl_date", new java.sql.Date(startDate.getTime()));
        }
        if (null != endDate) {
            param.getQueryConditions().put("_dnm_date", new java.sql.Date(endDate.getTime()));
        }
        if (StringUtils.isNotBlank(algorithmId)) {
            param.getQueryConditions().put("_ne_algorithmId", algorithmId);
        }
        if (StringUtils.isNotBlank(cityId)) {
            param.getQueryConditions().put("_ne_cityId", cityId);
        }
        if (StringUtils.isNotBlank(caliberId)) {
            param.getQueryConditions().put("_ne_caliberId", caliberId);
        }
        if (null != isReport) {
            param.getQueryConditions().put("_ne_report", isReport);
        }
        param.setDesc("date");
        return query(param).getDatas();
    }



    public List<AccuracyLoadCityFcDO> getAccuracyLoadCityFcDO(String cityId, String caliberId, String algorithmId, Date date) {
        DBQueryParam param = DBQueryParamBuilder.create().build();
        param.setPageSize("0");
        param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        param.getQueryConditions().put("_de_date", new java.sql.Date(date.getTime()));
        param.getQueryConditions().put("_ne_cityId", cityId);
        param.getQueryConditions().put("_ne_algorithmId", algorithmId);
        param.getQueryConditions().put("_ne_caliberId", caliberId);
        return  this.query(param).getDatas();
    }


    
}