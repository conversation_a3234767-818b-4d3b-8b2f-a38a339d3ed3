/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.area.impl;

import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.math.BigDecimalFunctions;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.lf.core.constants.AlgorithmConstants;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.algorithm.api.AreaForecastAlgorithmService;
import com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum;
import com.tsintergy.lf.serviceapi.base.area.api.BaseAreaService;
import com.tsintergy.lf.serviceapi.base.area.api.BasePlanService;
import com.tsintergy.lf.serviceapi.base.area.api.LoadAreaFcService;
import com.tsintergy.lf.serviceapi.base.area.pojo.BasePlanDO;
import com.tsintergy.lf.serviceapi.base.area.pojo.LoadAreaFcDO;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcService;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceimpl.area.dao.LoadAreaFcDAO;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/8/3 17:13
 * @Version: 1.0.0
 */

@Service("loadAreaFcService")
public class LoadAreaFcServiceImpl implements LoadAreaFcService {



    @Autowired
    private LoadAreaFcDAO loadAreaFcDAO;

    @Autowired
    private LoadCityFcService loadCityFcService;

    @Autowired
    private BasePlanService basePlanService;

    @Autowired
    private BaseAreaService baseAreaService;


    @Override
    public List<LoadAreaFcDO> findLoadCityFcDOS(String areaId, Date startDate, Date endDate, String caliberId)
        throws Exception {
        return loadAreaFcDAO.getLoadCityFcDO(areaId, startDate, endDate, caliberId);
    }

    @Override
    public void doInsertOrUpdate(LoadAreaFcDO loadAreaFcDO) throws Exception {
        LoadAreaFcDO LoadCityFcDOTemp = loadAreaFcDAO
            .getLoadCityFcDOByOneDate(loadAreaFcDO.getAreaId(), loadAreaFcDO.getDate(), loadAreaFcDO.getCaliberId());
        if (LoadCityFcDOTemp == null) {
            loadAreaFcDAO.createAndFlush(loadAreaFcDO);
            return;
        }
        loadAreaFcDAO.getSession().flush();
        loadAreaFcDAO.getSession().clear();
        loadAreaFcDO.setId(LoadCityFcDOTemp.getId());
        loadAreaFcDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
        loadAreaFcDAO.updateAndFlush(loadAreaFcDO);
    }

    @Override
    public void doInsertOrUpdateBatch(List<LoadAreaFcDO> loadAreaFcDOS) throws Exception {
        for (LoadAreaFcDO loadAreaFcDO : loadAreaFcDOS) {
            this.doInsertOrUpdate(loadAreaFcDO);
        }
    }

    @Override
    public List<BigDecimal> findLoadCityFcDO(String areaId, Date startDate, Date endDate, String caliberId)
        throws Exception {
        List<LoadAreaFcDO> powerLoadFcCityClctDOS = null;
        try {
            powerLoadFcCityClctDOS = this.findLoadCityFcDOS(areaId, startDate, endDate, caliberId);
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException("01C20180003");
        }
        List<BigDecimal> result = new ArrayList<>();
        List nullList = new ArrayList() {
            {
                for (int i = 0; i < 96; i++) {
                    add(null);
                }
            }
        };
        if (!CollectionUtils.isEmpty(powerLoadFcCityClctDOS) && powerLoadFcCityClctDOS.size() > 0) {
            Map<Date, LoadAreaFcDO> mapData = powerLoadFcCityClctDOS.stream()
                .collect(Collectors.toMap(LoadAreaFcDO::getDate, Function.identity(), (o, n) -> n));
            List<Date> listDate = DateUtil.getListBetweenDay(startDate, endDate);
            for (Date date : listDate) {
                LoadAreaFcDO loadAreaFcDO = mapData.get(date);
                if (loadAreaFcDO == null) {
                    result.addAll(nullList);
                } else {
                    result.addAll(BasePeriodUtils
                        .toList(loadAreaFcDO, Constants.LOAD_CURVE_POINT_NUM,
                            Constants.LOAD_CURVE_START_WITH_ZERO));
                }
            }
        }
        return result;
    }

    @Override
    public void doStatAllAreaFcLoad(Date startDate, Date end, List<String> defaultAreaIds, String caliberId,String netLossCoefficient)
        throws Exception {
        List<Date> listBetweenDay = DateUtil.getListBetweenDay(startDate, end);

        Map<String, LoadAreaFcDO> collect = findLoadCityFcDOS(null, startDate, end, caliberId).stream()
            .collect(Collectors.toMap(t -> {
                return t.getAreaId() + DateUtil.getDateToStr(t.getDate());
            }, t -> t));

        for (Date date : listBetweenDay) {
            List<List<BigDecimal>> loadCityHisValueSum = new ArrayList<>();
            for (String areaId : defaultAreaIds) {
                LoadAreaFcDO loadAreaFcDO = collect.get(areaId + DateUtil.getDateToStr(date));
                if (loadAreaFcDO == null){
                    continue;
                }
                loadCityHisValueSum.add(BasePeriodUtils
                    .toList(loadAreaFcDO, com.tsintergy.lf.core.constants.Constants.LOAD_CURVE_POINT_NUM,
                        com.tsintergy.lf.core.constants.Constants.LOAD_CURVE_START_WITH_ZERO));
            }
            if (CollectionUtils.isEmpty(loadCityHisValueSum)){
                continue;
            }
            List<BigDecimal> zeroList = ColumnUtil.getZeroOrNullList(96, BigDecimal.ZERO);
            for (List<BigDecimal> bigDecimals : loadCityHisValueSum) {
                zeroList = BigDecimalFunctions.listAdd(zeroList, bigDecimals);
            }

            //用每个点的数据除以网损得到每个点的预测负荷
            List<BigDecimal> loadFcValues = BigDecimalFunctions
                .listDivideValue(zeroList, new BigDecimal(netLossCoefficient));
            LoadCityFcDO loadCityFcDO =new LoadCityFcDO();
            Map<String, BigDecimal> valueMap = ColumnUtil.listToMap(loadFcValues, false);
            BasePeriodUtils.setAllFiled(loadCityFcDO,valueMap);

            loadCityFcDO.setCityId(String.valueOf(com.tsintergy.lf.core.constants.Constants.PROVINCE_TYPE));
            loadCityFcDO.setCaliberId(caliberId);
            loadCityFcDO.setAlgorithmId(AlgorithmEnum.REGION_FORECAST.getId());
            loadCityFcDO.setDate(new java.sql.Date(date.getTime()));
            loadCityFcService.doSaveOrUpdateLoadCityFcDO96(loadCityFcDO);
        }
    }

    @Override
    public void doAreaForecast(Date startDate, Date end, String caliberId) throws Exception {
        //默认方案
        BasePlanDO defaultPlan = basePlanService.getDefaultPlan();
        //当前预测模式 1系统分区预测 2 地市上报累加
        List<String> defaultAreaIds = basePlanService.getDefaultAreaIds();

        List<Date> listBetweenDay = DateUtil.getListBetweenDay(startDate, end);
        Map<String, LoadCityFcDO> collect = loadCityFcService
            .findFcByAlgorithmId(null, caliberId, AlgorithmConstants.MD_ALGORITHM_ID, startDate, end)
            .stream()
            .collect(Collectors.toMap(t -> {
                return t.getCityId() + DateUtil.getDateToStr(t.getDate());
            }, t -> t));
        for (Date date : listBetweenDay) {
            for (String areaId : defaultAreaIds) {
                String cityId = baseAreaService.getCityIdsByAreaId(areaId).get(0);
                LoadCityFcDO loadCityFcDO = collect.get(cityId + DateUtil.getDateToStr(date));
                if (loadCityFcDO == null) {
                    continue;
                }
                Map<String, BigDecimal> valueMap = ColumnUtil.listToMap(BasePeriodUtils
                        .toList(loadCityFcDO, com.tsintergy.lf.core.constants.Constants.LOAD_CURVE_POINT_NUM,
                            com.tsintergy.lf.core.constants.Constants.LOAD_CURVE_START_WITH_ZERO),
                    com.tsintergy.lf.core.constants.Constants.LOAD_CURVE_START_WITH_ZERO);
                LoadAreaFcDO loadAreaFcDO = new LoadAreaFcDO();
                BasePeriodUtils.setAllFiled(loadAreaFcDO, valueMap);
                loadAreaFcDO.setAreaId(areaId);
                loadAreaFcDO.setDate(new java.sql.Date(date.getTime()));
                loadAreaFcDO.setCaliberId(caliberId);
                this.doInsertOrUpdate(loadAreaFcDO);
            }
        }
    }
}