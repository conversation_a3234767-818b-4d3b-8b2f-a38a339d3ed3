/**
 * Copyright(C),2015-2018,北京清能互联科技有限公司
 * Author:   jxm
 * Date:    2018/11/2710:40
 * History:
 * <author><time><version><desc>
 */
package com.tsintergy.lf.serviceimpl.common.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * Description:<br>
 *
 * <AUTHOR>
 * @create2018/11/27
 * @since1.0.0
 */
public class DateUtils {

    /**
     * yyyy-MM-dd HH:mm:ss
     */
    public final static String DATE_FORMAT1 = "yyyy-MM-dd HH:mm:ss";
    /**
     * yyyy-MM-dd
     */
    public final static String DATE_FORMAT2 = "yyyy-MM-dd";
    /**
     * YYYYMMDD
     */
    public final static String DATE_FORMAT4 = "yyyyMMdd";
    /**
     * YYYYMMddHHmmss
     */
    public final static String DATE_FORMAT5 = "yyyyMMddHHmmss";
    /**
     * YYYYMMddHHmmssSSS
     */
    public final static String DATE_FORMAT6 = "yyyyMMddHHmmssSSS";
    /**
     * yyyy-MM
     */
    public final static String DATE_FORMAT7 = "yyyy-MM";

    /**
     * YYYYMMdd HH:mm:ss
     */
    public final static String DATE_FORMAT8 = "yyyyMMdd HH:mm:ss";

    /**
     * yyyyMM
     */
    public final static String DATE_FORMAT9 = "yyyyMM";
    /**
     * yyyyMM
     */
    public final static String DATE_FORMAT10 = "MMdd";

    public static String getDateToStr(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT2);
        return sdf.format(date);
    }

    /**
     *      * 根据日期 找到对应日期的 星期
     *     
     */
    public static String getDayOfWeekByDate(Date date) {
        String dayOfweek = "-1";
        try {
            SimpleDateFormat formatter = new SimpleDateFormat("E");
            String str = formatter.format(date);
            dayOfweek = str;

        } catch (Exception e) {
           e.printStackTrace();
        }
        return dayOfweek;
    }


    /**
     * 取得两个日期之间的所有日期集合，包含起始日期和结束日期
     *
     * @param startdate 起始日期
     * @param enddate   结束日期
     * @return 日期集合
     */
    public static List<Date> getListBetweenDay(Date startdate, Date enddate) {
        List<Date> list = new ArrayList<Date>();
        Calendar startcal = Calendar.getInstance();
        startcal.setTime(startdate);
        for (Date date = startdate;
             date.before(enddate) || date.equals(enddate); ) {
            list.add(date);
            startcal.add(Calendar.DAY_OF_MONTH, 1);
            date = startcal.getTime();
        }
        return list;
    }


    public static Date getFirstDayDateOfMonth(final Date date) {

        final Calendar cal = Calendar.getInstance();

        cal.setTime(date);

        final int last = cal.getActualMinimum(Calendar.DAY_OF_MONTH);

        cal.set(Calendar.DAY_OF_MONTH, last);

        return cal.getTime();

    }

    public static Date getLastDayOfMonth(final Date date) {

        final Calendar cal = Calendar.getInstance();

        cal.setTime(date);

        final int last = cal.getActualMaximum(Calendar.DAY_OF_MONTH);

        cal.set(Calendar.DAY_OF_MONTH, last);

        return cal.getTime();

    }


    public static String getDateToStrFORMAT(Date date, String format) {
        if (date != null) {
            SimpleDateFormat sdf = new SimpleDateFormat(format);
            return sdf.format(date);
        }
        return null;
    }

    public static String getNowDate() {
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT1);
        return sdf.format(new Date());
    }

    public static Date getDateFromString(String dateStr, String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        Date date = null;
        try {
            date = sdf.parse(dateStr);
        } catch (Exception e) {
            return null;
        }

        return date;
    }

    /**
     * @param date 日期
     * @param day  天数
     * @return 时间相加天数或相减天数
     */
    public static Date getSubDate(Date date, Integer day) {
        if (date != null && day != null) {
            Calendar c = Calendar.getInstance();
            c.setTime(date);
            c.add(Calendar.DATE, day);
            return c.getTime();
        }
        return null;
    }

    public static Date getSubMonth(Date date, Integer day) {
        if (date != null && day != null) {
            Calendar c = Calendar.getInstance();
            c.setTime(date);
            c.add(Calendar.MONTH, day);
            return c.getTime();
        }
        return null;
    }

    /**
     * 清空小时分钟秒
     */
    public static Date clearHHmmss(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 返回d1与d2之间的天数： D1 - D2
     *
     * @param d1 被减时间
     * @param d2 减时间
     * @return 两个时间相差的分钟数(忽略小时分钟秒)
     */
    public static int getDaysDifferMinutes(Date d1, Date d2) {
        double millisPerMinutes = org.apache.commons.lang3.time.DateUtils.MILLIS_PER_MINUTE;
        double differ = (d1.getTime() - d2.getTime()) / millisPerMinutes;
        //logger.info("the diff of double is:" + differ);
        return (int) (differ);
    }

    /**
     * 返回d1与d2之间的天数： D1 - D2
     *
     * @param d1：被减数
     * @param d2：减数
     * @return 如果按时间拍列的话，d1在d2的前面，返回负数；否则返回两者之间相差的天数
     */
    public static int getDaysDiffer2(Date d1, Date d2) {
        Calendar d1Calendar = Calendar.getInstance();
        d1Calendar.setTime(d1);
        d1Calendar.set(Calendar.HOUR_OF_DAY, 0);
        d1Calendar.set(Calendar.MINUTE, 0);
        d1Calendar.set(Calendar.SECOND, 0);
        d1Calendar.set(Calendar.MILLISECOND, 0);

        Calendar d2Calendar = Calendar.getInstance();
        d2Calendar.setTime(d2);
        d2Calendar.set(Calendar.HOUR_OF_DAY, 0);
        d2Calendar.set(Calendar.MINUTE, 0);
        d2Calendar.set(Calendar.SECOND, 0);
        d2Calendar.set(Calendar.MILLISECOND, 0);

        if (d1Calendar.after(d2Calendar)) {//if equal, return false
            int dayNum = 0;
            while (true) {
                d1Calendar.add(Calendar.DAY_OF_YEAR, -1);
                if (d1Calendar.before(d2Calendar))    //if equal, return false
                    break;
                dayNum++;
            }
            return dayNum;
        } else if (d1Calendar.equals(d2Calendar)) {
            return 0;
        } else {
            return (-1 * getDaysDiffer2(d2, d1));
        }
    }


    /**
     * 两日期之间相差天数
     *
     * @param begin_date
     * @param end_date
     * @return
     */
    public static int getIntervalDayNum(Date begin_date, Date end_date) {
        int day = 0;
        try {
            Calendar begin = Calendar.getInstance();
            begin.setTime(begin_date);
            begin.set(Calendar.HOUR_OF_DAY, 0);
            begin.set(Calendar.MINUTE, 0);
            begin.set(Calendar.SECOND, 0);
            begin.set(Calendar.MILLISECOND, 0);

            Calendar end = Calendar.getInstance();
            end.setTime(end_date);
            end.set(Calendar.HOUR_OF_DAY, 0);
            end.set(Calendar.MINUTE, 0);
            end.set(Calendar.SECOND, 0);
            end.set(Calendar.MILLISECOND, 0);
            day = (int) ((end.getTimeInMillis() - begin.getTimeInMillis()) / (24 * 60 * 60 * 1000));
        } catch (Exception e) {
            System.out.println("getIntervalDayNum failed! " + e.getMessage());
            return -1;
        }
        return day;
    }


    /**
     * @param minDate 开始日期 yyyy-MM-dd HH:mm:ss
     * @param maxDate 终止日期yyyy-MM-dd HH:mm:ss
     * @return List<String>
     * @throws Exception
     * @fun 获得两个日期之间的所有月份
     */
    public static List<String> getMonthBetween(String minDate, String maxDate) throws Exception {
        // 转换格式yyyy-MM
        minDate = minDate.substring(0, 7);
        maxDate = maxDate.substring(0, 7);

        ArrayList<String> result = new ArrayList<String>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");// 格式化为年月

        Calendar min = Calendar.getInstance();
        Calendar max = Calendar.getInstance();

        min.setTime(sdf.parse(minDate));
        min.set(min.get(Calendar.YEAR), min.get(Calendar.MONTH), 1);

        max.setTime(sdf.parse(maxDate));
        max.set(max.get(Calendar.YEAR), max.get(Calendar.MONTH), 2);

        Calendar curr = min;
        while (curr.before(max)) {
            result.add(sdf.format(curr.getTime()));
            curr.add(Calendar.MONTH, 1);
        }
        return result;
    }


    /*public static void main(String argc[]) {
        Date subDate = DateUtils.getSubDate(new Date(), -1);

        Date date1 = DateUtils.getDateFromString("2017-01-03", DATE_FORMAT2);
        Date date2 = DateUtils.getDateFromString("2017-01-01", DATE_FORMAT2);
        int days = DateUtils.getDaysDiffer2(date1, date2);
        System.out.println(days);
    }*/

    /**
     * 从日期中取出 hh
     *
     * @param date
     * @return
     */
    public static String getStrHourFromDate(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("HHmmss");
        String strDateFormat = sdf.format(date);
        return strDateFormat.substring(0, 2);
    }

    /**
     * 返回两日期之间：milliseconds
     *
     * @return
     */
    public static Long getIntervalMillisecondNum(Date begin_date, Date end_date) {
        long minute = 0;
        try {
            minute = end_date.getTime() - begin_date.getTime();
        } catch (Exception e) {
            System.out.println("getDateToMinutes failed! " + e.getMessage());
            return (long) -1;
        }
        //输出结果 相差的milliseconds
        return minute;
    }

    /**
     * 判断日期是否为同一天
     *
     * @return
     */
    public static boolean isSameDay(Date day1, Date day2) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String ds1 = sdf.format(day1);
        String ds2 = sdf.format(day2);
        if (ds1.equals(ds2)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 将字符串转换成日期
     *
     * @param dateStr:yyyy-MM-dd HH:mm:ss
     * @return
     */
    public static Date getStrToDatePARSE(String dateStr) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = null;
        try {
            date = format.parse(dateStr);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return date;
    }

    /**
     * 判断一个日期是否是周末
     *
     * @param bdate
     * @return
     * @throws ParseException
     */
    public static boolean isWeekend(Date bdate) throws ParseException {
        Calendar cal = Calendar.getInstance();
        cal.setTime(bdate);
        if (cal.get(Calendar.DAY_OF_WEEK) == Calendar.SATURDAY || cal.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY) {
            return true;
        } else {
            return false;
        }
    }


    /**
     * 根据日期获取 星期 （2019-05-06 ——> 星期一）
     *
     * @param
     * @return
     */
    public static String dateToWeek(Date date) {
        String[] weekDays = {"星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"};
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        //一周的第几天
        int w = cal.get(Calendar.DAY_OF_WEEK) - 1;
        if (w < 0) {
            w = 0;
        }
        return weekDays[w];
    }


    /**
     * 1 第一季度 2 第二季度 3 第三季度 4 第四季度
     *
     * @param date
     * @return
     */
    public static int getSeason(Date date) {
        int season = 0;
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        int month = c.get(Calendar.MONTH);
        switch (month) {
            case Calendar.JANUARY:
            case Calendar.FEBRUARY:
            case Calendar.MARCH:
                season = 1;
                break;
            case Calendar.APRIL:
            case Calendar.MAY:
            case Calendar.JUNE:
                season = 2;
                break;
            case Calendar.JULY:
            case Calendar.AUGUST:
            case Calendar.SEPTEMBER:
                season = 3;
                break;
            case Calendar.OCTOBER:
            case Calendar.NOVEMBER:
            case Calendar.DECEMBER:
                season = 4;
                break;
            default:
                break;
        }
        return season;
    }

    public static Date getFormatDate(Date date) {
        return getDateFromString(getDateToStrFORMAT(date, DATE_FORMAT2), DATE_FORMAT2);
    }


    /**
     * 获取上个月最后一天
     *
     * @param date
     * @return
     */
    public static Date lastDayOfLastMonth(Date date) {
        Calendar endTime = Calendar.getInstance();
        endTime.setTime(date);
        endTime.add(Calendar.MONTH, -1);
        endTime.set(Calendar.DATE, endTime.getActualMaximum(Calendar.DAY_OF_MONTH));
        return endTime.getTime();
    }
}

