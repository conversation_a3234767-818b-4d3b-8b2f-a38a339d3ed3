package com.tsintergy.lf.serviceimpl.bgd.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsieframework.core.base.service.BaseFacadeServiceImpl;
import com.tsintergy.lf.serviceapi.base.bgd.api.WeatherSituationService;
import com.tsintergy.lf.serviceapi.base.bgd.pojo.WeatherSituationDO;
import com.tsintergy.lf.serviceimpl.bgd.dao.WeatherSituationDAO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service("weatherSituationService")
public class WeatherSituationServiceImpl extends BaseFacadeServiceImpl implements WeatherSituationService {

    @Autowired
    private WeatherSituationDAO weatherSituationDAO;

    @Override
    public WeatherSituationDO getWeatherSituationDO(String cityId, String year, String month) {
        return weatherSituationDAO.findOne(JpaWrappers.<WeatherSituationDO>lambdaQuery()
        .eq(WeatherSituationDO::getYear,year)
        .eq(WeatherSituationDO::getCityId,cityId)
        .eq(WeatherSituationDO::getMonth,month));
    }
}
