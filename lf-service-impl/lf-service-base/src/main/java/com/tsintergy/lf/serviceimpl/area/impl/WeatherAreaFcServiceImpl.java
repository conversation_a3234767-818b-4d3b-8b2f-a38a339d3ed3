/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.area.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.area.api.BaseAreaService;
import com.tsintergy.lf.serviceapi.base.area.api.WeatherAreaFcService;
import com.tsintergy.lf.serviceapi.base.area.api.WeatherAreaHisService;
import com.tsintergy.lf.serviceapi.base.area.pojo.WeatherAreaFcDO;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherNewEnum;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.BaseWeatherDO;
import com.tsintergy.lf.serviceimpl.area.dao.WeatherAreaFcDAO;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/8/3 17:26
 * @Version: 1.0.0
 */
@Service("weatherAreaFcService")
@Slf4j
public class WeatherAreaFcServiceImpl implements WeatherAreaFcService {


    @Autowired
    CityService cityService;
    
    @Autowired
    WeatherAreaFcDAO weatherAreaFcDAO;

    @Autowired
    private WeatherCityFcService weatherCityFcService;

    @Autowired
    private WeatherAreaHisService weatherAreaHisService;


    @Autowired
    private BaseAreaService baseAreaService;
    @Override
    public List<WeatherAreaFcDO> findWeatherCityFcDOs(String areaId, Integer type, Date startDate, Date endDate)
        throws Exception {
        try {
            return weatherAreaFcDAO.findAll(JpaWrappers.<WeatherAreaFcDO>lambdaQuery()
                .eq(areaId != null, WeatherAreaFcDO::getAreaId, areaId)
                .eq(type != null, WeatherAreaFcDO::getType, type)
                .le(endDate != null, WeatherAreaFcDO::getDate, endDate)
                .ge(startDate != null, WeatherAreaFcDO::getDate, startDate)
            );
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("", e.getMessage(), e);
        }
    }

    @Override
    public void doInsertOrUpdate(WeatherAreaFcDO weatherAreaFcDO) throws Exception {
        List<WeatherAreaFcDO> weatherAreaFcDOS = findWeatherCityFcDOs(weatherAreaFcDO.getAreaId(), weatherAreaFcDO.getType(), weatherAreaFcDO.getDate(),
                weatherAreaFcDO.getDate());
        if (CollectionUtils.isEmpty(weatherAreaFcDOS)) {
            weatherAreaFcDAO.save(weatherAreaFcDO);
            return;
        }
        weatherAreaFcDO.setId(weatherAreaFcDOS.get(0).getId());
        weatherAreaFcDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
        weatherAreaFcDAO.saveOrUpdateByTemplate(weatherAreaFcDO);
    }

    @Override
    public void doInsertOrUpdateList(List<WeatherAreaFcDO> weatherAreaFcDOS) throws Exception {
        for (WeatherAreaFcDO weatherAreaFcDO : weatherAreaFcDOS) {
            this.doInsertOrUpdate(weatherAreaFcDO);
        }
    }

    @Override
    public void deleteByAreasIds(List<String> areaIds) throws Exception {
        weatherAreaFcDAO.delete(JpaWrappers.<WeatherAreaFcDO>lambdaQuery()
            .in(!CollectionUtils.isEmpty(areaIds),WeatherAreaFcDO::getAreaId,areaIds));
    }

    @Override
    public void statAreaFcWeather(String areaId, Date startDate, Date endDate) throws Exception {
        List<Date> listBetweenDay = DateUtil.getListBetweenDay(startDate, endDate);

        long start = System.currentTimeMillis();

        Map<String, BaseWeatherDO> collect = weatherCityFcService
            .findWeatherCityFcDOs(null, null, startDate, endDate).stream()
            .collect(Collectors.toMap(t -> {
                return t.getCityId() + DateUtil.getDateToStr(t.getDate()) + t.getType();
            }, t -> t));
        List<String> cityIds = baseAreaService.getCityIdsByAreaId(areaId);
        long select = System.currentTimeMillis();
        log.info("查询时间:----"+(select-start));

        List<WeatherAreaFcDO> weatherAreaFcDOS = new ArrayList<>();
        for (Date date : listBetweenDay) {
            WeatherNewEnum[] values = WeatherNewEnum.values();
            for (WeatherNewEnum value : values) {
                Map<String, BigDecimal> aveAreaWeatherValueMap = weatherAreaHisService
                    .getAveAreaWeatherValueMap(cityIds, collect,date,value.getType());
                if (!CollectionUtils.isEmpty(aveAreaWeatherValueMap)){
                    WeatherAreaFcDO weatherAreaHisDO = new WeatherAreaFcDO();
                    BasePeriodUtils.setAllFiled(weatherAreaHisDO, aveAreaWeatherValueMap);
                    weatherAreaHisDO.setAreaId(areaId);
                    weatherAreaHisDO.setDate(new java.sql.Date(date.getTime()));
                    weatherAreaHisDO.setType(value.getType());
                    weatherAreaFcDOS.add(weatherAreaHisDO);
                }
            }
        }

        log.info("准备数据时间:----"+(System.currentTimeMillis()-select));

        updateAreaWeather(weatherAreaFcDOS,areaId,startDate,endDate);
    }

    private void updateAreaWeather(List<WeatherAreaFcDO> weatherAreaFcDOS,String areaId, Date startDate, Date endDate) {
        if (CollectionUtils.isEmpty(weatherAreaFcDOS)){
            return;
        }
        long update = System.currentTimeMillis();
        weatherAreaFcDAO.delete(JpaWrappers.<WeatherAreaFcDO>lambdaQuery()
            .eq(areaId!=null,WeatherAreaFcDO::getAreaId,areaId)
            .le(endDate != null, WeatherAreaFcDO::getDate, endDate)
            .ge(startDate != null, WeatherAreaFcDO::getDate, startDate));

        weatherAreaFcDAO.saveOrUpdateBatch(weatherAreaFcDOS);

        log.info("删除插入时间:----"+(System.currentTimeMillis()-update));

    }
}
