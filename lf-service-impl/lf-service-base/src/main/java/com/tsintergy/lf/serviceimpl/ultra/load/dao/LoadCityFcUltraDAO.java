package com.tsintergy.lf.serviceimpl.ultra.load.dao;

import com.tsieframework.core.base.dao.jpa.BaseJpaDAO;
import com.tsieframework.core.component.datasource.dynamic.annotation.DataSource;
import com.tsintergy.lf.serviceapi.base.ultra.load.pojo.LoadCityFcUltraDO;
import org.springframework.stereotype.Repository;


@Repository
@DataSource("ultra")
public interface LoadCityFcUltraDAO extends BaseJpaDAO<LoadCityFcUltraDO, String> {

}