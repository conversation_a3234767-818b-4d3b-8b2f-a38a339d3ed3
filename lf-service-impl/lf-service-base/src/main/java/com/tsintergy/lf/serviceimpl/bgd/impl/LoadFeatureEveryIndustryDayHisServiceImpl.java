package com.tsintergy.lf.serviceimpl.bgd.impl;

import com.tsieframework.core.base.dao.jpa.query.JpaWrappers;
import com.tsieframework.core.base.math.BigDecimalFunctions;
import com.tsieframework.core.base.service.BaseFacadeServiceImpl;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.util.compatible.BigDecimalUtils;
import com.tsintergy.aif.algorithm.serviceapi.base.pojo.Load;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.serviceapi.base.bgd.api.LoadFeatureEveryIndustryDayHisService;
import com.tsintergy.lf.serviceapi.base.bgd.pojo.LoadFeatureEveryIndustryDayHisServiceDO;
import com.tsintergy.lf.serviceapi.base.trade.pojo.TradeLoadFeatureDayHisDO;
import com.tsintergy.lf.serviceimpl.bgd.dao.LoadFeatureEveryIndustryDayHisServiceDAO;
import com.tsintergy.lf.serviceimpl.system.dao.SettingSystemDAO;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * Description: TODO <br>
 *
 * <AUTHOR>
 * @create 2023-02-16
 * @since 1.0.0
 */
@Service("loadFeatureEveryIndustryDayHisService")
public class LoadFeatureEveryIndustryDayHisServiceImpl extends BaseFacadeServiceImpl implements
    LoadFeatureEveryIndustryDayHisService {

    @Autowired
    LoadFeatureEveryIndustryDayHisServiceDAO loadFeatureEveryIndustryDayHisServiceDAO;

    @Autowired
    SettingSystemDAO settingSystemDAO;

    @Override
    public List<LoadFeatureEveryIndustryDayHisServiceDO> getLoadFeatureEveryIndustryDayHisServiceList(String cityId, String id,
        Date startDate, Date endDate) {
        return loadFeatureEveryIndustryDayHisServiceDAO.findAll(JpaWrappers.<LoadFeatureEveryIndustryDayHisServiceDO>lambdaQuery()
            .eq(LoadFeatureEveryIndustryDayHisServiceDO::getCityId,cityId)
            .eq(LoadFeatureEveryIndustryDayHisServiceDO::getEveryIndustryId,id)
            .ge(LoadFeatureEveryIndustryDayHisServiceDO::getDate,startDate)
            .le(LoadFeatureEveryIndustryDayHisServiceDO::getDate,endDate));
    }

    @Override
    public List<LoadFeatureEveryIndustryDayHisServiceDO> getLoadFeatureEveryIndustryDayHisServiceList(String cityId,
        Date startDate, Date endDate) {
        return loadFeatureEveryIndustryDayHisServiceDAO.findAll(JpaWrappers.<LoadFeatureEveryIndustryDayHisServiceDO>lambdaQuery()
            .eq(LoadFeatureEveryIndustryDayHisServiceDO::getCityId,cityId)
            .ge(LoadFeatureEveryIndustryDayHisServiceDO::getDate,startDate)
            .le(LoadFeatureEveryIndustryDayHisServiceDO::getDate,endDate));
    }

    @Override
    public List<LoadFeatureEveryIndustryDayHisServiceDO> getLoadFeatureEveryIndustryDayHisServiceLists(String cityId,
        Date startDate, Date endDate, List<String> industryList) {
        return loadFeatureEveryIndustryDayHisServiceDAO.findAll(JpaWrappers.<LoadFeatureEveryIndustryDayHisServiceDO>lambdaQuery()
            .eq(LoadFeatureEveryIndustryDayHisServiceDO::getCityId,cityId)
            .ge(LoadFeatureEveryIndustryDayHisServiceDO::getDate,startDate)
            .le(LoadFeatureEveryIndustryDayHisServiceDO::getDate,endDate)
            .in(!CollectionUtils.isEmpty(industryList),LoadFeatureEveryIndustryDayHisServiceDO::getEveryIndustryId,industryList)
        );

    }

    @Override
    public List<LoadFeatureEveryIndustryDayHisServiceDO> getLoadFeatureEveryIndustryDayHisServiceTypeList(String cityId,
        Date startDate, Date endDate, List<String> type) {
        return loadFeatureEveryIndustryDayHisServiceDAO.findAll(JpaWrappers.<LoadFeatureEveryIndustryDayHisServiceDO>lambdaQuery()
            .eq(LoadFeatureEveryIndustryDayHisServiceDO::getCityId,cityId)
            .ge(LoadFeatureEveryIndustryDayHisServiceDO::getDate,startDate)
            .le(LoadFeatureEveryIndustryDayHisServiceDO::getDate,endDate)
            .in(!CollectionUtils.isEmpty(type),LoadFeatureEveryIndustryDayHisServiceDO::getType,type)
        );
    }

    @Override
    public LoadFeatureEveryIndustryDayHisServiceDO doStatLoadFeatureCityDayTests(boolean startWithZero, Load load,
        Date date) throws Exception {
        List<String> peakTimes = settingSystemDAO.getPeakTimes();
        List<String> troughTimes = settingSystemDAO.getTroughTimes();

        List<String> morningPeak = settingSystemDAO.getMorningPeak();
        List<String> noontimePeak = settingSystemDAO.getNoontimePeak();
        List<String> eveningPeak = settingSystemDAO.getEveningPeak();
        List<String> troughPeak = settingSystemDAO.getTroughPeak();
        LoadFeatureEveryIndustryDayHisServiceDO baseLoadFeatureDayDO = statisticsDayFeatures(startWithZero,load, peakTimes, troughTimes,date,
            morningPeak,noontimePeak,eveningPeak,troughPeak);

        return baseLoadFeatureDayDO;
    }

    public LoadFeatureEveryIndustryDayHisServiceDO statisticsDayFeatures(boolean startWithZero,Load loadCityVO, List<String> peakTimes, List<String> troughTimes,Date date,
        List<String> morningPeak,List<String> noontimePeak, List<String> eveningPeak,List<String> troughPeak)
        throws Exception {

        List<BigDecimal> peaks = new ArrayList<BigDecimal>();
        List<BigDecimal> troughs = new ArrayList<BigDecimal>();

        List<BigDecimal> morningPeaks = new ArrayList<BigDecimal>();
        List<BigDecimal> noontimePeaks = new ArrayList<BigDecimal>();
        List<BigDecimal> eveningPeaks = new ArrayList<BigDecimal>();
        List<BigDecimal> troughPeaks = new ArrayList<BigDecimal>();

        List<BigDecimal> loadList = loadCityVO.getloadList();

        Map<String, BigDecimal> loadMap = ColumnUtil.listToMap(loadList,startWithZero);
        Map<String, BigDecimal> maxMixAvg = BasePeriodUtils.getMaxMinAvg(loadList, 4);

        String maxTime = null; // 最大负荷发生时刻
        String minTime = null; // 最小负荷发生时刻

        for (String column : loadMap.keySet()) {
            BigDecimal load = loadMap.get(column);
            if (null != load) {
                column = column.substring(1);
                if (load.compareTo(maxMixAvg.get("max")) == 0) {
                    maxTime = column;
                }
                if (load.compareTo(maxMixAvg.get("min")) == 0) {
                    minTime = column;
                }
                if (peakTimes != null && peakTimes.contains(column)) {
                    peaks.add(load);
                }
                if (troughTimes != null && troughTimes.contains(column)) {
                    troughs.add(load);
                }
                if (morningPeak != null && morningPeak.contains(column)) {
                    morningPeaks .add(load);
                }
                if (noontimePeak != null && noontimePeak.contains(column)) {
                    noontimePeaks.add(load);
                }
                if (eveningPeak != null && eveningPeak.contains(column)) {
                    eveningPeaks.add(load);
                }
                if (troughPeak != null && troughPeak.contains(column)) {
                    troughPeaks.add(load);
                }
            }
        }

        LoadFeatureEveryIndustryDayHisServiceDO baseLoadEveryIndustryFeatureDayDO = new LoadFeatureEveryIndustryDayHisServiceDO();

        if (maxTime != null) {
            baseLoadEveryIndustryFeatureDayDO.setMaxTime(new StringBuffer(maxTime).insert(2, ":").toString());
        }
        if (minTime != null) {
            baseLoadEveryIndustryFeatureDayDO.setMinTime(new StringBuffer(minTime).insert(2, ":").toString());
        }

        baseLoadEveryIndustryFeatureDayDO.setDate(new java.sql.Date(date.getTime()));
        // 最大负荷
        baseLoadEveryIndustryFeatureDayDO.setMaxLoad(maxMixAvg.get("max"));
        // 最小负荷
        baseLoadEveryIndustryFeatureDayDO.setMinLoad(maxMixAvg.get("min"));
        // 平均负荷
        baseLoadEveryIndustryFeatureDayDO.setAveLoad(maxMixAvg.get("avg"));
        // 峰谷差 = 日最大负荷 – 日最小负荷
        baseLoadEveryIndustryFeatureDayDO.setDifferent(BigDecimalUtils.sub(baseLoadEveryIndustryFeatureDayDO.getMaxLoad(), baseLoadEveryIndustryFeatureDayDO.getMinLoad()));
        if(baseLoadEveryIndustryFeatureDayDO.getMaxLoad() != null && baseLoadEveryIndustryFeatureDayDO.getMaxLoad().intValue() != 0){
            // 峰谷差率 = （日最大负荷 – 日最小负荷）/日最大负荷
            baseLoadEveryIndustryFeatureDayDO.setGradient(BigDecimalUtils.divide(baseLoadEveryIndustryFeatureDayDO.getDifferent(), baseLoadEveryIndustryFeatureDayDO.getMaxLoad(), 4));
            // 负荷率 = 日平均负荷/日最大负荷
            baseLoadEveryIndustryFeatureDayDO.setLoadGradient(BigDecimalUtils.divide(baseLoadEveryIndustryFeatureDayDO.getAveLoad(), baseLoadEveryIndustryFeatureDayDO.getMaxLoad(), 4));
        }
        // 尖峰平均负荷 = average(尖峰时段负荷）。尖峰时段后台可配，默认值为5:30~7:00，10:00~12:00，18：00~20:00
        baseLoadEveryIndustryFeatureDayDO.setPeak(BigDecimalUtils.avgList(peaks, 4, true));
        // 低谷平均负荷 = average（低谷时段负荷）。低谷时段可配，默认值为2:00~5:00，12:00~14:00，22:00~0:00
        baseLoadEveryIndustryFeatureDayDO.setTrough(BigDecimalUtils.avgList(troughs, 4, false));
        baseLoadEveryIndustryFeatureDayDO.setMorningPeak(BigDecimalUtils.avgList(morningPeaks, 4, false));
        baseLoadEveryIndustryFeatureDayDO.setEveningPeak(BigDecimalUtils.avgList(eveningPeaks, 4, false));
        baseLoadEveryIndustryFeatureDayDO.setNoontimePeak(BigDecimalUtils.avgList(noontimePeaks, 4, false));
        baseLoadEveryIndustryFeatureDayDO.setTroughMax(BigDecimalUtils.avgList(troughPeaks, 4, false));
        // 日电量 = 96点负荷之和/4 、24点负荷之和
        int size = loadList.size();
        BigDecimal energy = null;
        if (size == Constants.LOAD_CURVE_POINT_NUM){
            energy = BigDecimalUtils.divide(BigDecimalUtils.addAllValue(loadList), new BigDecimal(4), 4);
        }
        else if(size == Constants.LOAD_CURVE_POINT_NUM_24){
            energy = BigDecimalFunctions.listSum(loadList);
        }

        baseLoadEveryIndustryFeatureDayDO.setEnergy(energy);

        return baseLoadEveryIndustryFeatureDayDO;
    }

    @Override
    public void saveOrUpdate(TradeLoadFeatureDayHisDO tradeLoadFeatureDayHisDO) {

    }
}
