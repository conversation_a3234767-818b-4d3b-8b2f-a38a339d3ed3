package com.tsintergy.lf.serviceimpl.common.util;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.vo.BasePeriod96VO;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisClctDO;
import com.tsintergy.lf.serviceimpl.common.dto.ComprehensiveUnitDTO;
import com.tsintergy.lf.serviceimpl.common.dto.VerticalData;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;

/**
 * 纵数据导入监听类
 *
 * <AUTHOR>
 */
public class VerticalDataListener extends AnalysisEventListener<VerticalData> {

    List<ComprehensiveUnitDTO> result;

    private List<VerticalData> list = new ArrayList();

    private String cityId;

    private String caliberId;

    private Integer type;

    private String algorithmId;

    private Boolean startWithZero;

    private DateFormatType dateFormatType;

    public VerticalDataListener(String cityId, String caliberId, Integer type, String algorithmId,
        Boolean startWithZero,
        DateFormatType dateFormatType,
        List<ComprehensiveUnitDTO> result) {
        this.cityId = cityId;
        this.caliberId = caliberId;
        this.type = type;
        this.algorithmId = algorithmId;
        this.startWithZero = startWithZero;
        this.result = result;
        this.dateFormatType = dateFormatType;
    }

    /**
     * 逐行解析数据
     */
    @Override
    public void invoke(VerticalData data, AnalysisContext context) {
        list.add(data);
    }

    /**
     * 逐行的返回头信息
     */
    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {

    }


    /**
     * 所有数据解析完成后的回调函数
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Map<String, List<VerticalData>> mapByDate = list.stream()
            .collect(Collectors.groupingBy(src -> (src.getDateTime().split(" ")[0])));
        for (Map.Entry<String, List<VerticalData>> mapData : mapByDate.entrySet()) {
            List<VerticalData> value = mapData.getValue();
            ComprehensiveUnitDTO unitDTO = new ComprehensiveUnitDTO();
            unitDTO.setCaliberId(caliberId);
            unitDTO.setCityId(cityId);
            unitDTO.setAlgorithmId(algorithmId);
            unitDTO.setType(type);
            unitDTO.setCreatetime(new Timestamp(System.currentTimeMillis()));
            unitDTO.setDate(new java.sql.Date(DateUtil.getDate(mapData.getKey(), null).getTime()));
            //装载96点or24点数据
            for (VerticalData one : value) {
                String s = one.getDateTime().split(" ")[1].replace(":", "");
                String methodName = "setT" + s;
                Method method = null;
                try {
                    method = BasePeriod96VO.class.getMethod(methodName, BigDecimal.class);
                    method.invoke(unitDTO, new BigDecimal(one.getValue()));
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            String substring = value.get(0).getDateTime().substring(0, 10);
            //依据起始点装载2400点or0000点
            if (startWithZero) {
                //用明天的0000点装载2400点
                Date yesterday = DateUtils
                    .addDays(DateUtils.string2Date(substring, dateFormatType), 1);
                List<VerticalData> verticalData = mapByDate
                    .get(DateUtils.date2String(yesterday, dateFormatType));
                if (CollectionUtils.isNotEmpty(verticalData)) {
                    List<VerticalData> filterData = verticalData.stream()
                        .filter(src -> "0000".equals(src.getDateTime().split(" ")[1].replace(":", "")))
                        .collect(Collectors.toList());
                    unitDTO.setT2400(new BigDecimal(filterData.get(0).getValue()));
                }
            } else {
                //用昨天的2400点装载0000点
                Date yesterday = DateUtils
                    .addDays(DateUtils.string2Date(substring, dateFormatType), -1);
                List<VerticalData> verticalData = mapByDate
                    .get(DateUtils.date2String(yesterday, dateFormatType));
                if (CollectionUtils.isNotEmpty(verticalData)) {
                    List<VerticalData> filterData = verticalData.stream()
                        .filter(src -> "2400".equals(src.getDateTime().split(" ")[1].replace(":", "")))
                        .collect(Collectors.toList());
                    unitDTO.setT0000(new BigDecimal(filterData.get(0).getValue()));
                }
            }
            LoadCityHisClctDO clctDO = new LoadCityHisClctDO();
            BeanUtils.copyProperties(unitDTO, clctDO);
            result.add(unitDTO);
        }
    }

//
//    public static void main(String[] args) {
//        File file = new File("E:\\临时\\2020年全口径.xlsx");
//        List<ComprehensiveUnitDTO> result = new ArrayList<>();
//        EasyExcel.read(file, VerticalData.class,
//            new VerticalDataListener("1", "1", 1, "1", true, DateFormatType.SIMPLE_DATE_FORMAT_STR, result))
//            .sheet()
//            .headRowNumber(0).doRead();
//        System.out.println("1");
//    }


}
