
package com.tsintergy.lf.serviceimpl.weather.impl;

import com.google.common.collect.Lists;
import com.tsieframework.core.base.dao.DataPackage;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.enums.TyphoonTypeEnum;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.api.WeatherTyphoonDefinitionService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.base.pojo.WeatherTyphoonDefinitionDO;
import com.tsintergy.lf.serviceapi.base.forecast.dto.SearchDTO;
import com.tsintergy.lf.serviceapi.base.typhoon.api.TyphoonHisService;
import com.tsintergy.lf.serviceapi.base.typhoon.dto.TyphoonWeatherFeatureCityDTO;
import com.tsintergy.lf.serviceapi.base.typhoon.pojo.TyphoonHisDO;
import com.tsintergy.lf.serviceapi.base.typhoon.pojo.WeatherTyphoonProvinceHisDO;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityHisService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityDayHisService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherTyphoonRateService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayHisDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherTyphoonRateDO;
import com.tsintergy.lf.serviceimpl.typhoon.dao.WeatherTyphoonProvinceHisDAO;
import com.tsintergy.lf.serviceimpl.weather.dao.WeatherFeatureCityDayHisDAO;
import java.beans.BeanInfo;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.io.Serializable;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @version $Id: WeatherFeatureCityDayHisServiceImpl.java, v 0.1 2018-01-31 11:00:39 tao Exp $$
 */

@Service("weatherFeatureCityDayHisService")
@Transactional
public class WeatherFeatureCityDayHisServiceImpl extends BaseServiceImpl implements WeatherFeatureCityDayHisService {

    private static final Logger logger = LogManager.getLogger(WeatherFeatureCityDayHisServiceImpl.class);

    @Autowired
    CityService cityService;

    @Autowired
    WeatherTyphoonDefinitionService weatherTyphoonDefinitionService;


    @Autowired
    TyphoonHisService typhoonHisService;

    @Autowired
    WeatherTyphoonRateService weatherTyphoonRateService;

    @Autowired
    WeatherCityHisService weatherCityHisService;


    @Autowired
    WeatherTyphoonProvinceHisDAO weatherTyphoonProvinceHisDAO;

    @Autowired
    WeatherFeatureCityDayHisDAO weatherFeatureCityDayHisDAO;

    
  

    @Override
    public DataPackage queryWeatherFeatureCityHisDO(DBQueryParam param) throws Exception {
        try {
            return weatherFeatureCityDayHisDAO.query(param);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("",e.getMessage(), e);
        }
    }

    @Override
    public WeatherFeatureCityDayHisDO doCreate(WeatherFeatureCityDayHisDO vo) throws Exception {
        try {
            return (WeatherFeatureCityDayHisDO) weatherFeatureCityDayHisDAO.create(vo);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("",e.getMessage(), e);
        }
    }

    @Override
    public void doRemoveWeatherFeatureCityHisDO(WeatherFeatureCityDayHisDO vo) throws Exception {
        try {
            weatherFeatureCityDayHisDAO.remove(vo);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("",e.getMessage(), e);
        }
    }

    @Override
    public void doRemoveWeatherFeatureCityHisDOByPK(Serializable pk) throws Exception {
        try {
            weatherFeatureCityDayHisDAO.removeByPk(pk);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("",e.getMessage(), e);
        }
    }

    @Override
    public WeatherFeatureCityDayHisDO doUpdateWeatherFeatureCityHisVO(WeatherFeatureCityDayHisDO vo) throws Exception {
        try {
            return (WeatherFeatureCityDayHisDO) weatherFeatureCityDayHisDAO.update(vo);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("",e.getMessage(), e);
        }
    }

    @Override
    public WeatherFeatureCityDayHisDO findWeatherFeatureCityHisVOByPk(Serializable pk) throws Exception {
        try {
            return (WeatherFeatureCityDayHisDO) weatherFeatureCityDayHisDAO.findByPk(pk);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new BusinessException("",e.getMessage(), e);
        }
    }

    @Override
    public WeatherFeatureCityDayHisDO findWeatherFeatureCityHisVOByDate(String cityId, Date date) throws Exception {
        List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisVOS = weatherFeatureCityDayHisDAO
                .findWeatherFeatureCityHisVOs(cityId, date);
        if (weatherFeatureCityDayHisVOS.size() < 1) {
            return null;
        }
        return weatherFeatureCityDayHisVOS.get(0);
    }


    @Override
    public List<WeatherFeatureCityDayHisDO> findWeatherFeatureCityDayHisDO(List<String> cityIds, Date startDate,
                                                                           Date endDate) throws Exception {
        return weatherFeatureCityDayHisDAO.listWeatherFeatureCityDayHisDO(cityIds, startDate, endDate);
    }

    @Override
    public List<WeatherFeatureCityDayHisDO> listWeatherFeatureCityDayHisDO(String cityId, Date startDate, Date endDate)
            throws Exception {
        cityId = cityService.findWeatherCityId(cityId);
        return weatherFeatureCityDayHisDAO.getWeatherFeatureCityDayHisDOs(cityId, startDate, endDate);
    }

    /**
     * 功能描述: <br> 获取气象的类型
     *
     * @param cityId    城市id
     * @param startDate 开始日期
     * @param endDate   结束日期
     * <AUTHOR>
     * @since 1.0.0
     */
    @Override
    public List<TyphoonWeatherFeatureCityDTO> getTyphoonFeatureType(Date startDate, Date endDate, String... cityId)
            throws BusinessException {
        List<TyphoonWeatherFeatureCityDTO> typhoonWeatherFeatureCityDTOS = new ArrayList<>();
        try {
            //查询占比
            List<WeatherTyphoonDefinitionDO> weatherTyphoonDefinitionVOS = weatherTyphoonDefinitionService
                    .getWeatherTyphoonVO();
            if (CollectionUtils.isEmpty(weatherTyphoonDefinitionVOS)) {
                return null;
            }
            WeatherTyphoonDefinitionDO weatherTyphoonDefinitionVO = weatherTyphoonDefinitionVOS.get(0);
            //查询台风表
            List<TyphoonHisDO> typhoonHisVOS = typhoonHisService.listTyphoonHisVO(startDate, endDate);
            //将台风数据转map集合
            Map<Date, List<TyphoonHisDO>> typhoonHisMap = typhoonHisVOS.stream()
                    .collect(Collectors.groupingBy(TyphoonHisDO::getNodeTime));
            List<String> typhoonDateStrs = new ArrayList<>();
            for (Date typhoonDate : typhoonHisMap.keySet()) {
                String typhoonDateStr = DateUtils.date2String(typhoonDate, DateFormatType.SIMPLE_DATE_FORMAT_STR);
                typhoonDateStrs.add(typhoonDateStr);
            }
            List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisVOS = null;
            if (!StringUtils.isEmpty(cityId) && cityId.length == 1) {
                CityDO cityVO = cityService.findCityById(cityId[0]);
                //查询省的气象
                if (cityVO.getType() == 1) {
                    //如果是省 则调查省的气象
                    List<WeatherTyphoonProvinceHisDO> weatherCityHisVOS = weatherTyphoonProvinceHisDAO
                            .getListWeatherProvinceHisVOs(startDate, endDate);
                    //先查出省的最高温度  省的最大降雨量  省的最大风速
                    Map<Date, List<WeatherTyphoonProvinceHisDO>> weatherMap = weatherCityHisVOS.stream()
                            .collect(Collectors.groupingBy(WeatherTyphoonProvinceHisDO::getDate));
                    for (Date date : weatherMap.keySet()) {
                        boolean isOk = false;
                        List<WeatherTyphoonProvinceHisDO> weatherCityHisVOList = weatherMap.get(date);
                        for (WeatherTyphoonProvinceHisDO weatherCityHisVO : weatherCityHisVOList) {
                            TyphoonWeatherFeatureCityDTO weatherFeatureCityDTO = new TyphoonWeatherFeatureCityDTO();
                            weatherFeatureCityDTO.setCityId(cityId[0]);
                            weatherFeatureCityDTO.setDate(date);
                            weatherFeatureCityDTO.setTyphoonType(TyphoonTypeEnum.NORMAL.getType());
                            String str = DateUtils.date2String(date, DateFormatType.SIMPLE_DATE_FORMAT_STR);
                            if (typhoonDateStrs.contains(str)) {
                                weatherFeatureCityDTO.setTyphoonType(TyphoonTypeEnum.TYPHOON.getType());
                                typhoonWeatherFeatureCityDTOS.add(weatherFeatureCityDTO);
                                isOk = true;
                                break;
                            }
                            if (isOk == false) {
                                Map<String, BigDecimal> maxMinAvg = BasePeriodUtils.getMaxMinAvg(
                                        BasePeriodUtils.toList(weatherCityHisVO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO),
                                        4);
                                //温度
                                if (weatherCityHisVO.getType() == 2) {
                                    BigDecimal loadMax = maxMinAvg.get("max");
                                    if (loadMax.compareTo(new BigDecimal(0)) > 0) {
                                        weatherFeatureCityDTO
                                                .setTyphoonType(TyphoonTypeEnum.HIGH_TEMPERATURE.getType());
                                        typhoonWeatherFeatureCityDTOS.add(weatherFeatureCityDTO);
                                        break;
                                    }
                                }
                                //降雨量
                                if (weatherCityHisVO.getType() == 3) {
                                    BigDecimal rainMax = maxMinAvg.get("max");
                                    if (rainMax.compareTo(new BigDecimal(0)) > 0) {
                                        weatherFeatureCityDTO.setTyphoonType(TyphoonTypeEnum.STRONG_RAINFALL.getType());
                                        typhoonWeatherFeatureCityDTOS.add(weatherFeatureCityDTO);
                                        break;
                                    }
                                }
                                //风速
                                if (weatherCityHisVO.getType() == 4) {
                                    BigDecimal windMax = maxMinAvg.get("max");
                                    if (windMax.compareTo(new BigDecimal(0)) > 0) {
                                        weatherFeatureCityDTO.setTyphoonType(TyphoonTypeEnum.STRONG_WIND.getType());
                                        typhoonWeatherFeatureCityDTOS.add(weatherFeatureCityDTO);
                                        break;
                                    }
                                } else {
                                    weatherFeatureCityDTO.setTyphoonType(TyphoonTypeEnum.NORMAL.getType());
                                }
                            }
                            typhoonWeatherFeatureCityDTOS.add(weatherFeatureCityDTO);
                        }
                    }
                    return typhoonWeatherFeatureCityDTOS;
                } else {
                    //查询的是地市id
                    weatherFeatureCityDayHisVOS = this.listWeatherFeatureCityDayHisDO(cityId[0], startDate, endDate);
                }
            } else {
                if (StringUtils.isEmpty(cityId)) {
                    //如果城市id传的是空  则查询所有
                    weatherFeatureCityDayHisVOS = this.listWeatherFeatureCityDayHisDO(null, startDate, endDate);
                } else {
                    List<String> cityIds = new ArrayList<>();
                    for (String id : cityId) {
                        cityIds.add(id);
                    }
                    this.findWeatherFeatureCityDayHisDO(cityIds, startDate, endDate);
                }
            }
            //将历史特性转map集合
            Map<Date, List<WeatherFeatureCityDayHisDO>> weatherFeatureMap = weatherFeatureCityDayHisVOS.stream()
                    .collect(Collectors.groupingBy(WeatherFeatureCityDayHisDO::getDate));
            for (Date key : weatherFeatureMap.keySet()) {
                String ymd = DateUtils.date2String(key, DateFormatType.SIMPLE_DATE_FORMAT_STR);
                List<WeatherFeatureCityDayHisDO> hisVOS = weatherFeatureMap.get(key);
                for (WeatherFeatureCityDayHisDO hisVO : hisVOS) {
                    TyphoonWeatherFeatureCityDTO typhoonWeatherFeatureCityDTO = new TyphoonWeatherFeatureCityDTO();
                    typhoonWeatherFeatureCityDTO.setCityId(hisVO.getCityId());
                    typhoonWeatherFeatureCityDTO.setDate(hisVO.getDate());
                    typhoonWeatherFeatureCityDTO.setTyphoonType(TyphoonTypeEnum.NORMAL.getType());
                    //转成yyyyMMdd类型
                    if (typhoonDateStrs.contains(ymd)) {
                        //台风类型
                        typhoonWeatherFeatureCityDTO.setTyphoonType(TyphoonTypeEnum.TYPHOON.getType());
                    } else if (hisVO.getHighestTemperature() != null) {
                        if (hisVO.getHighestTemperature()
                                .compareTo(new BigDecimal(weatherTyphoonDefinitionVO.getTemperature())) >= 0) {
                            //高温类型
                            typhoonWeatherFeatureCityDTO.setTyphoonType(TyphoonTypeEnum.HIGH_TEMPERATURE.getType());
                        }
                    } else if (hisVO.getRainfall() != null) {
                        if (hisVO.getRainfall().compareTo(new BigDecimal(weatherTyphoonDefinitionVO.getRainfall()))
                                >= 0) {
                            //强降雨类型
                            typhoonWeatherFeatureCityDTO.setTyphoonType(TyphoonTypeEnum.STRONG_RAINFALL.getType());
                        }
                    } else if (hisVO.getMaxWinds() != null) {
                        if (hisVO.getMaxWinds().compareTo(new BigDecimal(weatherTyphoonDefinitionVO.getWind())) >= 0) {
                            //大风类型
                            typhoonWeatherFeatureCityDTO.setTyphoonType(TyphoonTypeEnum.STRONG_WIND.getType());
                        }
                    } else {
                        typhoonWeatherFeatureCityDTO.setTyphoonType(TyphoonTypeEnum.NORMAL.getType());
                    }
                    typhoonWeatherFeatureCityDTOS.add(typhoonWeatherFeatureCityDTO);
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw new BusinessException("",e.getMessage(), e);
        }
        return typhoonWeatherFeatureCityDTOS;
    }

    @Override
    public List<WeatherFeatureCityDayHisDO> listWeatherFeatureCityDayHisDO(List<Date> dates, String cityId)
            throws Exception {
        return weatherFeatureCityDayHisDAO.listWeatherFeatureCityDayHisDO(dates, cityId);
    }

    /**
     * 功能描述: <br> 获取省的气象(温度  降雨  大风)
     *
     * <AUTHOR>
     * @since 1.0.0
     */
    @Override
    public List<WeatherTyphoonProvinceHisDO> doSaveProvinceWeather(Date startDate, Date endDate)
            throws BusinessException {
        try {
            Map<String, WeatherTyphoonRateDO> rateMap = weatherTyphoonRateService.findTyphoonRateMap();
            //计算后的省的气象数据
            List<WeatherTyphoonProvinceHisDO> result = Lists.newArrayList();
            List<WeatherCityHisDO> weatherCityHisVOS =weatherCityHisService
                    .findWeatherCityHisDOs(null, null, startDate, endDate);
            //通过日期和type分类
            Map<String, List<WeatherCityHisDO>> weatherMap = new HashMap<>();
            for (WeatherCityHisDO weatherCityHisVO : weatherCityHisVOS) {
                String key = weatherCityHisVO.getDate() + "-" + weatherCityHisVO.getType();
                if (weatherMap.get(key) == null) {
                    weatherMap.put(key, new ArrayList<>());
                }
                weatherMap.get(key).add(weatherCityHisVO);
            }
            for (String key : weatherMap.keySet()) {
                //同一天 同一个类型的
                List<WeatherCityHisDO> weatherCityHisVOList = weatherMap.get(key);
                //省的96点数据
                Map<String, BigDecimal> provinceMap = new HashMap<>();
                WeatherTyphoonProvinceHisDO province = new WeatherTyphoonProvinceHisDO();
                for (WeatherCityHisDO weatherCityHisVO : weatherCityHisVOList) {

                    province.setDate(weatherCityHisVO.getDate());
                    province.setType(weatherCityHisVO.getType());

//                    cityHisVO.setDate(weatherCityHisVO.getDate());
//                    cityHisVO.setType(weatherCityHisVO.getType());
                    Map<String, BigDecimal> columnMap = BasePeriodUtils.toMap(weatherCityHisVO, 96, Constants.LOAD_CURVE_START_WITH_ZERO);
                    for (String column : columnMap.keySet()) {
                        //算出每个字段的时刻值
                        WeatherTyphoonRateDO weatherTyphoonRate = rateMap.get(weatherCityHisVO.getCityId());
                        BigDecimal rate = new BigDecimal(0);
                        if (weatherTyphoonRate != null) {
                            rate = weatherTyphoonRate.getRate();
                        }
                        if (provinceMap.get(column) == null) {
                            provinceMap.put(column, columnMap.get(column) == null ? new BigDecimal(0.00)
                                    : columnMap.get(column).multiply(rate));
                        }
                        provinceMap.get(column).add(columnMap.get(column) == null ? new BigDecimal(0.00)
                                : columnMap.get(column).multiply(rate));
                    }
                }
                BasePeriodUtils.setAllFiled(province, provinceMap);
                result.add(province);
            }
            //入库操作(先删除 在入库)
            weatherTyphoonProvinceHisDAO.removeWeatherProvinceHisVOs(startDate, endDate);
            weatherTyphoonProvinceHisDAO.saveWeatherProvinceHisVOList(result);
            return result;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw new BusinessException("",e.getMessage(), e);
        }
    }

    @Override
    public List<WeatherTyphoonProvinceHisDO> getProvinceWeatherByDates(List<Date> dates) throws Exception {
        return weatherTyphoonProvinceHisDAO.getListWeatherProvinceHisVOs(dates);
    }

    @Override
    public List<WeatherCityHisDO> getProvinceWeatherNew(Date startDate, Date endDate) throws Exception {
        Map<String, WeatherTyphoonRateDO> rateMap = weatherTyphoonRateService.findTyphoonRateMap();
        List<WeatherCityHisDO> weatherCityHisVOs =weatherCityHisService
                .findWeatherCityHisDOs(null, null, startDate, endDate);
        Map<Integer, List<WeatherCityHisDO>> typeMap = weatherCityHisVOs.stream()
                .collect(Collectors.groupingBy(WeatherCityHisDO::getType));
        List<WeatherCityHisDO> result = Lists.newArrayList();
        typeMap.forEach((k, v) -> {
            Map<java.sql.Date, List<WeatherCityHisDO>> dateMap = v.stream()
                    .collect(Collectors.groupingBy(WeatherCityHisDO::getDate));
            dateMap.forEach((date, vos) -> {
                try {
                    WeatherCityHisDO temp = mergeColPointsAvg(vos, rateMap);
                    temp.setType(k);
                    temp.setDate(date);
                    result.add(temp);
                } catch (Exception e) {

                }
            });
        });
        return result;
    }

    @Override
    public List<WeatherTyphoonProvinceHisDO> doSaveProvinceWeatherNew(Date startDate, Date endDate) throws Exception {
        Map<String, WeatherTyphoonRateDO> rateMap = weatherTyphoonRateService.findTyphoonRateMap();
        List<WeatherCityHisDO> weatherCityHisVOs =weatherCityHisService
                .findWeatherCityHisDOs(null, null, startDate, endDate);
        Map<Integer, List<WeatherCityHisDO>> typeMap = weatherCityHisVOs.stream()
                .collect(Collectors.groupingBy(WeatherCityHisDO::getType));
        List<WeatherTyphoonProvinceHisDO> result = Lists.newArrayList();
        typeMap.forEach((k, v) -> {
            Map<java.sql.Date, List<WeatherCityHisDO>> dateMap = v.stream().parallel()
                    .collect(Collectors.groupingBy(WeatherCityHisDO::getDate));
            dateMap.forEach((date, vos) -> {
                try {
                    WeatherTyphoonProvinceHisDO temp = mergeProvinceColPointsAvg(vos, rateMap);
                    temp.setType(k);
                    temp.setDate(date);
                    result.add(temp);
                } catch (Exception e) {

                }
            });
        });
        //入库操作(先删除 在入库)
        weatherTyphoonProvinceHisDAO.removeWeatherProvinceHisVOs(startDate, endDate);
        weatherTyphoonProvinceHisDAO.saveWeatherProvinceHisVOList(result);
        return result;
    }


    private WeatherCityHisDO mergeColPointsAvg(List<WeatherCityHisDO> weatherCityHisVOS,
                                               Map<String, WeatherTyphoonRateDO> rateMap) throws Exception {
        BeanInfo beanInfo = Introspector.getBeanInfo(WeatherCityHisDO.class);
        PropertyDescriptor[] pds = beanInfo.getPropertyDescriptors();
        WeatherCityHisDO result = new WeatherCityHisDO();
        for (PropertyDescriptor pd : pds) {
            if (pd.getName().startsWith("t") && !pd.getName().equalsIgnoreCase("type")) {
                Method method = pd.getReadMethod();
                BigDecimal sum = BigDecimal.ZERO;
                weatherCityHisVOS.forEach(vo -> {
                    BigDecimal bigDecimal = rateMap.get(vo.getCityId()).getRate();
                    try {
                        Object value = method.invoke(vo, null);
                        sum.add(bigDecimal.multiply(value == null ? BigDecimal.ZERO : (BigDecimal) value));
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });
                pd.getWriteMethod().invoke(result,
                        sum.divide(BigDecimal.valueOf(weatherCityHisVOS.size()), 4, BigDecimal.ROUND_HALF_UP));
            }
        }
        return result;
    }

    private WeatherTyphoonProvinceHisDO mergeProvinceColPointsAvg(List<WeatherCityHisDO> weatherCityHisVOS,
                                                                  Map<String, WeatherTyphoonRateDO> rateMap) throws Exception {
        BeanInfo beanInfo = Introspector.getBeanInfo(WeatherCityHisDO.class);
        PropertyDescriptor[] pds = beanInfo.getPropertyDescriptors();
        WeatherCityHisDO result = new WeatherCityHisDO();
        for (PropertyDescriptor pd : pds) {
            if (pd.getName().startsWith("t") && !pd.getName().equalsIgnoreCase("type")) {
                Method method = pd.getReadMethod();
                BigDecimal sum = BigDecimal.ZERO;
                weatherCityHisVOS.forEach(vo -> {
                    BigDecimal bigDecimal = rateMap.get(vo.getCityId()).getRate();
                    try {
                        Object value = method.invoke(vo, null);
                        sum.add(bigDecimal.multiply(value == null ? BigDecimal.ZERO : (BigDecimal) value));
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });
                pd.getWriteMethod().invoke(result,
                        sum.divide(BigDecimal.valueOf(weatherCityHisVOS.size()), 4, BigDecimal.ROUND_HALF_UP));
            }
        }
        WeatherTyphoonProvinceHisDO provinceVo = new WeatherTyphoonProvinceHisDO();
        BeanUtils.copyProperties(result, provinceVo);
        return provinceVo;
    }


    @Override
    public List<WeatherFeatureCityDayHisDO> findWeatherFeatureBySearchData(String cityId, Date startDate, Date endDate, List<SearchDTO> searchDTOS) throws Exception {
        return weatherFeatureCityDayHisDAO.findWeatherFeature(cityId, startDate, endDate, searchDTOS);
    }
}

