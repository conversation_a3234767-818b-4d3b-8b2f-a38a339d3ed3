package com.tsintergy.lf.serviceimpl.load.impl;

import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.util.compatible.BigDecimalUtils;
import com.tsintergy.lf.core.constants.AlgorithmConstants;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.check.api.SettingCheckService;
import com.tsintergy.lf.serviceapi.base.check.pojo.SettingCheckDO;
import com.tsintergy.lf.serviceapi.base.evalucation.api.StatisticsCityDayFcService;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.StatisticsCityDayFcDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityDayFcService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityDayHisService;
import com.tsintergy.lf.serviceapi.base.load.api.PeakAndTroughAssessmentService;
import com.tsintergy.lf.serviceapi.base.load.dto.AccuracyDistributionDTO;
import com.tsintergy.lf.serviceapi.base.load.dto.AccuracyEstimationDTO;
import com.tsintergy.lf.serviceapi.base.load.dto.AccuracyEstimationsDTO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityDayFcDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityDayHisDO;
import java.math.BigDecimal;
import java.util.Date;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("peakAndTroughAssessmentService")
public class PeakAndTroughAssessmentServiceImpl implements PeakAndTroughAssessmentService {

    @Autowired
    StatisticsCityDayFcService statisticsCityDayFcService;//日准确率
    @Autowired
    LoadFeatureCityDayFcService loadFeatureCityDayFcService;//负荷预测值
    @Autowired
    LoadFeatureCityDayHisService loadFeatureCityDayHisService;//负荷实际值
    @Autowired
    SettingCheckService settingCheckService;//查询免考
    private final BigDecimal HUNDRED = new BigDecimal(100);

    @Override
    public AccuracyEstimationsDTO getAccuracyStatistics(String cityId, String caliberId, String startDate, String endDate, String dateType) throws Exception {
        Map<String, Date> resultDate = getDate(dateType, startDate, endDate);
        Date start = resultDate.get("start");
        Date end = resultDate.get("end");
        AccuracyEstimationsDTO accuracyEstimationsDTO = new AccuracyEstimationsDTO();
        List<String> cityIds = new ArrayList<>();
        cityIds.add(cityId);
        java.sql.Date sqlStart =  new java.sql.Date(start.getTime());
        java.sql.Date sqlEnd =  new java.sql.Date(end.getTime());
        //日准确率
        List<StatisticsCityDayFcDO> dayAccuracyList = statisticsCityDayFcService.getDayAccuracyList(cityId, caliberId, null, start, end, true);
        Map<Date, List<StatisticsCityDayFcDO>> statisticsCityDayFcDOCollect = null;
        if (CollectionUtils.isNotEmpty(dayAccuracyList)){
           statisticsCityDayFcDOCollect = dayAccuracyList.stream().collect(Collectors.groupingBy(StatisticsCityDayFcDO::getDate));
        }
        //负荷预测值
        List<LoadFeatureCityDayFcDO> loadFeatureCityDayFcDOList= loadFeatureCityDayFcService.findLoadFeatureCityDayFcList(cityIds, sqlStart, sqlEnd, caliberId);
        Map<java.sql.Date, List<LoadFeatureCityDayFcDO>> loadFeatureCityDayFcDOCollect = null;
        if (CollectionUtils.isNotEmpty(loadFeatureCityDayFcDOList)){
            loadFeatureCityDayFcDOCollect = loadFeatureCityDayFcDOList.stream().collect(Collectors.groupingBy(LoadFeatureCityDayFcDO::getDate));

        }
        //负荷实际值
        List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisDOS = loadFeatureCityDayHisService.findLoadFeatureCityDayHisDOS(cityId, start, end, caliberId);
        Map<java.sql.Date, List<LoadFeatureCityDayHisDO>> loadFeatureCityDayHisDOCollect = null;
        if (CollectionUtils.isNotEmpty(loadFeatureCityDayHisDOS)){
           loadFeatureCityDayHisDOCollect = loadFeatureCityDayHisDOS.stream().collect(Collectors.groupingBy(LoadFeatureCityDayHisDO::getDate));
        }
        List<AccuracyEstimationDTO> accuracyEstimationDTOS = new ArrayList<>();
        while (start.getTime()<=end.getTime()){
            AccuracyEstimationDTO accuracyEstimationDTO = new AccuracyEstimationDTO();
            if (statisticsCityDayFcDOCollect!=null){
                List<StatisticsCityDayFcDO> statisticsCityDayFcDOS = statisticsCityDayFcDOCollect.get(start);
                if (CollectionUtils.isNotEmpty(statisticsCityDayFcDOS)){
                    StatisticsCityDayFcDO   statisticsCityDayFcDO = statisticsCityDayFcDOS.get(0);
                    //日准确率
                    accuracyEstimationDTO.setDailyAccuracy(statisticsCityDayFcDO.getAccuracy());
                }
            }

            //预测负荷实体
            LoadFeatureCityDayFcDO loadFeatureCityDayFcDO=new LoadFeatureCityDayFcDO();
            if (loadFeatureCityDayFcDOCollect!=null){
                List<LoadFeatureCityDayFcDO> LoadFeatureCityDayFcDOs = loadFeatureCityDayFcDOCollect.get(start);
                if (CollectionUtils.isNotEmpty(LoadFeatureCityDayFcDOs)){
                    loadFeatureCityDayFcDO = LoadFeatureCityDayFcDOs.get(0);
                    //日最大负荷预测值
                    accuracyEstimationDTO.setDailyMaximumLoadFc(loadFeatureCityDayFcDO.getMaxLoad());
                    //最小负荷预测值
                    accuracyEstimationDTO.setDailyMinimumLoadFc(loadFeatureCityDayFcDO.getMinLoad());
                }
            }

            //实际负荷实体
            LoadFeatureCityDayHisDO loadFeatureCityDayHisDO=new LoadFeatureCityDayHisDO();
            if (loadFeatureCityDayHisDOCollect!=null){
                List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisDOs = loadFeatureCityDayHisDOCollect.get(start);
                if (CollectionUtils.isNotEmpty(loadFeatureCityDayHisDOs)){
                    loadFeatureCityDayHisDO = loadFeatureCityDayHisDOs.get(0);
                    //最大负荷实际值
                    accuracyEstimationDTO.setActualValueOfDailyMaximumLad(loadFeatureCityDayHisDO.getMaxLoad());
                    //最小负荷实际值
                    accuracyEstimationDTO.setActualValueOfDailyMinimumLad(loadFeatureCityDayHisDO.getMinLoad());
                }
            }

            //日期
            accuracyEstimationDTO.setDate(DateUtil.formateDate(start));
            //天数+1
            start = DateUtils.addDays(start, 1);
            if (accuracyEstimationDTO.getActualValueOfDailyMaximumLad()==null&&accuracyEstimationDTO.getActualValueOfDailyMinimumLad()==null&&accuracyEstimationDTO.getDailyMaximumLoadFc()==null&&accuracyEstimationDTO.getDailyMinimumLoadFc()==null&&accuracyEstimationDTO.getDailyAccuracy()==null){
                continue;
            }


            //最大准确率
            accuracyEstimationDTO.setDailyMaximumLoadAccuracy(getDailyLoadAccuracy(loadFeatureCityDayFcDO.getMaxLoad(),loadFeatureCityDayHisDO.getMaxLoad()));
            //最小准确率
            accuracyEstimationDTO.setDailyMinimumLoadAccuracy(getDailyLoadAccuracy(loadFeatureCityDayFcDO.getMinLoad(),loadFeatureCityDayHisDO.getMinLoad()));
            //最大最小准确率
            accuracyEstimationDTO.setDailyMaximumAndMinimumAccuracy(DailyMaximumAndMinimumAccuracy(getDailyLoadAccuracy(loadFeatureCityDayFcDO.getMaxLoad(),loadFeatureCityDayHisDO.getMaxLoad()),getDailyLoadAccuracy(loadFeatureCityDayFcDO.getMinLoad(),loadFeatureCityDayHisDO.getMinLoad())));
            accuracyEstimationDTOS.add(accuracyEstimationDTO);
        }
        if (CollectionUtils.isEmpty(accuracyEstimationDTOS)) {
            return null;
        }

        //返回的数据无需去除免考数据
        List<AccuracyEstimationDTO> accuracyEstimationDTOSAll = accuracyEstimationDTOS;
        //免考时间范围
        List<Date> examinationDate = getNoExaminationList(cityIds, resultDate.get("start"), resultDate.get("end"));
        boolean noExamination = CollectionUtils.isNotEmpty(examinationDate);
        //统一免考过滤
        if (CollectionUtils.isNotEmpty(accuracyEstimationDTOS)){
            accuracyEstimationDTOS = accuracyEstimationDTOS.stream().filter(t->{
                Date date = DateUtil.getDate(t.getDate(), null);
                return noExamination?!(examinationDate.contains(date)):true;
            }).collect(Collectors.toList());
        }

        if (CollectionUtils.isNotEmpty(accuracyEstimationDTOS)){
            //设置各准确率平均值
            setAccuracyEstimationsDTOAvg(accuracyEstimationDTOS,accuracyEstimationsDTO);
        }
        accuracyEstimationsDTO.setAccuracyEstimation(accuracyEstimationDTOSAll);
        return accuracyEstimationsDTO;
    }




    @Override
    public List<AccuracyDistributionDTO> getAccuracyDistribution(String accuracyType, String caliberId, String cityId, String dateType, String startDate, String endDate) throws Exception {
        AccuracyEstimationsDTO accuracyStatistics = getAccuracyStatistics(cityId, caliberId, startDate, endDate, dateType);
        if (accuracyStatistics==null){
            return null;
        }
        List<AccuracyEstimationDTO> accuracyEstimation = accuracyStatistics.getAccuracyEstimation();
        if (CollectionUtils.isEmpty(accuracyEstimation)){
            return null;
        }
        List<AccuracyDistributionDTO> accuracyDistributionDTOS = new ArrayList<>();
        //过滤后的准确率集合
        List<BigDecimal> collect = null;
        if (("0").equals(accuracyType)){
             collect = accuracyEstimation.stream().filter(t->t.getDailyAccuracy()!=null).map(t -> t.getDailyAccuracy()).collect(Collectors.toList());
        }else if (("1").equals(accuracyType)){
             collect = accuracyEstimation.stream().filter(t->t.getDailyMaximumLoadAccuracy()!=null).map(t -> t.getDailyMaximumLoadAccuracy()).collect(Collectors.toList());
        }else if (("2").equals(accuracyType)){
             collect = accuracyEstimation.stream().filter(t->t.getDailyMinimumLoadAccuracy()!=null).map(t -> t.getDailyMinimumLoadAccuracy()).collect(Collectors.toList());
        }else if(("3").equals(accuracyType)){
            collect = accuracyEstimation.stream().filter(t->t.getDailyMaximumAndMinimumAccuracy()!=null).map(t -> t.getDailyMaximumAndMinimumAccuracy()).collect(Collectors.toList());
        }
        List<AccuracyDistributionDTO>  accuracyDistributionDTOList = getAccuracyDistributionDTOList(collect);
        accuracyDistributionDTOS.addAll(accuracyDistributionDTOList);
        return accuracyDistributionDTOS.stream().distinct().collect(Collectors.toList());
    }

    /**
     *获取时间查询条件
     * @param dateType
     * @param startDate
     * @param endDate
     * @return
     */
    @Override
    public Map<String,Date> getDate(String dateType,String startDate,String endDate){
        Map<String,Date> result = new HashMap<>();
        Date start = null;
        Date end = null;
        //查询条件为月
        if (("1").equals(dateType)){
            Date date = DateUtil.getDate(startDate, "yyyy-MM");
            start = DateUtil.getFirstDayOfMonth(date);
            end = DateUtil.getLastDayOfMonth(date);

        }else if(("0").equals(dateType)){
            start= DateUtil.getDate(startDate,null);
            end= DateUtil.getDate(endDate,null);
        }
        result.put("start",start);
        result.put("end",end);
        return result;
    }
    @Getter
    enum AccuracyRange{

        RANGE1("98","100"),
        RANGE2  ("95","98"),
        RANGE3("90","95"),
        RANGE4("80","90"),
        RANGE5("0","80");
        private String max;
        private String min;
        AccuracyRange(String min, String max) {
            this.min=min;
            this.max=max;
        }

    }


    /**
     * 设置各准确率平均值
     * @param accuracyEstimationDTOS 数据集
     * @param accuracyEstimationsDTO 返回实体
     */
    private void setAccuracyEstimationsDTOAvg(List<AccuracyEstimationDTO> accuracyEstimationDTOS,AccuracyEstimationsDTO accuracyEstimationsDTO ){
        //日准确率平均值
        List<BigDecimal> dailyAccuracyCollect = accuracyEstimationDTOS.stream().filter(t -> t.getDailyAccuracy() != null).map(t -> t.getDailyAccuracy()).collect(Collectors.toList());
        accuracyEstimationsDTO.setDailyAccuracyAvg(getAccuracyAvg(dailyAccuracyCollect));
        //最大负荷准确率平均值
        List<BigDecimal> dailyMaximumLoadAccuracyCollect = accuracyEstimationDTOS.stream().filter(t -> t.getDailyMaximumLoadAccuracy() != null).map(t -> t.getDailyMaximumLoadAccuracy()).collect(Collectors.toList());
        accuracyEstimationsDTO.setDailyMaximumLoadAccuracyAvg(getAccuracyAvg(dailyMaximumLoadAccuracyCollect));
        //最小负荷准确率平均值
        List<BigDecimal> dailyMinimumLoadAccuracyCollect = accuracyEstimationDTOS.stream().filter(t -> t.getDailyMinimumLoadAccuracy() != null).map(t -> t.getDailyMinimumLoadAccuracy()).collect(Collectors.toList());
        accuracyEstimationsDTO.setDailyMinimumLoadAccuracyAvg(getAccuracyAvg(dailyMinimumLoadAccuracyCollect));
        //最大最小准确率平均值
        List<BigDecimal> dailyMaximumAndMinimumAccuracyCollect = accuracyEstimationDTOS.stream().filter(t -> t.getDailyMaximumAndMinimumAccuracy() != null).map(t -> t.getDailyMaximumAndMinimumAccuracy()).collect(Collectors.toList());
        accuracyEstimationsDTO.setDailyMaximumAndMinimumAccuracyAvg(getAccuracyAvg(dailyMaximumAndMinimumAccuracyCollect));
    }

    /**
     * 求准确率平均值
     * @param collect 准确率集合
     * @return
     */
    private BigDecimal getAccuracyAvg(List<BigDecimal> collect){
        if(CollectionUtils.isNotEmpty(collect)){
            return BigDecimalUtils.avgList(collect, 4, false);
        }
        return null;
    }



    /**
     * 获取免考时间段
     * @param cityIds
     * @param startDate
     * @param endDate
     * @return
     */
    private List<Date> getNoExaminationList(List<String>cityIds, Date startDate, Date endDate) throws Exception {
        //免考返回数据
        List<SettingCheckDO> settingCheckVOs = settingCheckService.getSettingCheckVOs(cityIds, startDate,endDate, AlgorithmConstants.CHECK_STATUS);
        //免考时间范围
        List<Date> examinationDate = new ArrayList();
        settingCheckVOs.forEach(t->{
            java.sql.Date examinationStartDate = t.getStartDate();
            java.sql.Date examinationEndDate = t.getEndDate();
            List<Date> listBetweenDay = DateUtil.getListBetweenDay(examinationStartDate, examinationEndDate);
            examinationDate.addAll(listBetweenDay);
        });
        return examinationDate;
    }
    private List<AccuracyDistributionDTO> getAccuracyDistributionDTOList(List<BigDecimal> list){
        List<AccuracyDistributionDTO> accuracyDistributionDTOS= new ArrayList<>();
        for (AccuracyRange value : AccuracyRange.values()) {
            AccuracyDistributionDTO accuracyDistributionDTO;
                if (("80").equals(value.getMin())){
                 accuracyDistributionDTO = getAccuracyDistributionDTO(list, new BigDecimal(value.max).divide(HUNDRED,2,BigDecimal.ROUND_HALF_UP), new BigDecimal(value.min).divide(HUNDRED,2,BigDecimal.ROUND_HALF_UP), false, false);
            } else if(("80").equals(value.getMax())){
                 accuracyDistributionDTO = getAccuracyDistributionDTO(list, new BigDecimal(value.max).divide(HUNDRED,2,BigDecimal.ROUND_HALF_UP), new BigDecimal(value.min).divide(HUNDRED,2,BigDecimal.ROUND_HALF_UP), false, true);
            }else{
                accuracyDistributionDTO = getAccuracyDistributionDTO(list, new BigDecimal(value.max).divide(HUNDRED,2,BigDecimal.ROUND_HALF_UP), new BigDecimal(value.min).divide(HUNDRED,2,BigDecimal.ROUND_HALF_UP), true, false);
            }
            accuracyDistributionDTOS.add(accuracyDistributionDTO);
        }
        return accuracyDistributionDTOS;
    }

    /**
     * 获取准确率分布对象数据
     * @param list 准确率集合
     * @param max 上限
     * @param min 下限
     * @param minIsEq 前闭后开
     * @param maxIsEq 前开后闭
     * @return
     */
    private AccuracyDistributionDTO getAccuracyDistributionDTO(List<BigDecimal> list,BigDecimal max,BigDecimal min,boolean minIsEq,boolean maxIsEq) {
        AccuracyDistributionDTO accuracyDistributionDTO = new AccuracyDistributionDTO();
        int length = 0;
        StringBuffer range=new StringBuffer();
        if (minIsEq){
            length = list.stream().filter(t -> t.compareTo(min) == 1 && t.compareTo(max) == -1 || t.compareTo(min) == 0).toArray().length;
            range.append("["+min.multiply(HUNDRED)+"%,"+max.multiply(HUNDRED)+"%)");
        }else if (maxIsEq){
            length= list.stream().filter(t -> t.compareTo(min) == 1 &&t.compareTo(max)==-1|| t.compareTo(max) == 0).toArray().length;
            range.append("("+min.multiply(HUNDRED)+"%,"+max.multiply(HUNDRED)+"%]");
        }else{
            length= list.stream().filter(t -> t.compareTo(min) == 1&&t.compareTo(max) ==-1).toArray().length;
            range.append("("+min.multiply(HUNDRED)+"%,"+max.multiply(HUNDRED)+"%)");
        }
        accuracyDistributionDTO.setAccuracyRange(range.toString());
        accuracyDistributionDTO.setStatisticalValue(length);
        accuracyDistributionDTO.setProportion(new BigDecimal(length).multiply(HUNDRED).divide(new BigDecimal(list.size()),2,BigDecimal.ROUND_HALF_UP).setScale(2)+"");
        return accuracyDistributionDTO;
    }



    /**
     *
     * @param fc 预测
     * @param value 实际
     * @return
     */
    private BigDecimal getDailyLoadAccuracy(BigDecimal fc,BigDecimal value){
        if (fc==null||value==null){
            return null;
        }
        //做差
        BigDecimal subtract = fc.subtract(value);
        //求绝对值
        BigDecimal abs = subtract.abs();
        //相除
        BigDecimal divide = abs.divide(value,4, BigDecimal.ROUND_HALF_UP);
        BigDecimal result = BigDecimal.ONE.subtract(divide);
        return result.abs();
    }


    /**
     *最大最小准确率
     * @param max 最大
     * @param min 最小
     * @return
     */
    private BigDecimal DailyMaximumAndMinimumAccuracy(BigDecimal max,BigDecimal min){
        if (max==null||min==null){
            return null;
        }
        return  max.add(min).divide(BigDecimal.valueOf(BigDecimal.ROUND_CEILING),4,BigDecimal.ROUND_HALF_UP);
    }
}