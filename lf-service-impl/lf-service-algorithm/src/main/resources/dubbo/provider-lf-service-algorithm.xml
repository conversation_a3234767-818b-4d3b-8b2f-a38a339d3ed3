<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns:dubbo="http://dubbo.apache.org/schema/dubbo"
  xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
	http://dubbo.apache.org/schema/dubbo http://dubbo.apache.org/schema/dubbo/dubbo.xsd" >

  <dubbo:service group="forecastable"   ref="forecastable" interface="com.tsintergy.lf.serviceapi.algorithm.api.CustomizationForecastService" timeout="50000"/>
  <dubbo:service group="typhoonForecast"   ref="typhoonForecast" interface="com.tsintergy.lf.serviceapi.algorithm.api.CustomizationForecastService" timeout="50000"/>
  <dubbo:service group="analysisForecastService"   ref="analysisForecastService" interface="com.tsintergy.lf.serviceapi.algorithm.api.CustomizationForecastService" timeout="50000"/>
  <dubbo:service group="shortForecastService"   ref="shortForecastService" interface="com.tsintergy.lf.serviceapi.algorithm.api.CustomizationForecastService" timeout="50000"/>
  <dubbo:service group="holidayForecastService"   ref="holidayForecastService" interface="com.tsintergy.lf.serviceapi.algorithm.api.CustomizationForecastService" timeout="50000"/>
  <dubbo:service group="preProcessService"   ref="preProcessService" interface="com.tsintergy.lf.serviceapi.algorithm.api.CustomizationForecastService" timeout="50000"/>
  <dubbo:service group="sensitivityForecastService"   ref="sensitivityForecastService" interface="com.tsintergy.lf.serviceapi.algorithm.api.CustomizationForecastService" timeout="50000"/>
  <dubbo:service group="similarDayForecast"   ref="similarDayForecast" interface="com.tsintergy.lf.serviceapi.algorithm.api.CustomizationForecastService" timeout="50000"/>
  <dubbo:service group="forecastDataService"   ref="forecastDataService" interface="com.tsintergy.lf.serviceapi.base.forecast.api.ForecastDataService" timeout="50000"/>

  <!--    <dubbo:service group="${tsie.security.service-group}" ref="security-domainHibernateService" interface="com.tsieframework.core.base.service.hibernate.CommonService" timeout="100000"/>-->

  <!--    <dubbo:reference id="algorithmService" interface="com.tsintergy.lf.serviceapi.base.forecast.api.AlgorithmService"-->
  <!--      check="false" timeout="50000"/>-->
  <!--    <dubbo:reference id="loadCityHisService" interface="com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService"-->
  <!--      check="false" timeout="50000"/>-->
  <!--    <dubbo:reference id="weatherFeatureCityDayFcService"-->
  <!--      interface="com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityDayFcService"-->
  <!--      check="false" timeout="50000"/>-->
  <!--    <dubbo:reference id="loadCityHisClctService"-->
  <!--      check="false" interface="com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisClctService" timeout="50000"/>-->
  <!--    <dubbo:reference id="weatherCityHisService"-->
  <!--      check="false" interface="com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityHisService" timeout="50000"/>-->
  <!--    <dubbo:reference id="cityService" interface="com.tsintergy.lf.serviceapi.base.base.api.CityService" check="false" timeout="50000"/>-->
  <!--    <dubbo:reference id="dataCheckInfoService"-->
  <!--      check="false"   interface="com.tsintergy.lf.serviceapi.base.datamanage.api.DataCheckInfoService" timeout="50000"/>-->
  <!--    <dubbo:reference id="typhoonArchiveService"-->
  <!--      check="false" interface="com.tsintergy.lf.serviceapi.base.typhoon.api.TyphoonArchiveService" timeout="50000"/>-->
  <!--    <dubbo:reference id="weatherCityFcService"-->
  <!--      check="false" interface="com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcService" timeout="50000"/>-->
  <!--    <dubbo:reference id="holidayService" interface="com.tsintergy.lf.serviceapi.base.base.api.HolidayService"-->
  <!--      check="false" timeout="50000"/>-->
  <!--    <dubbo:reference id="loadCityFcService" interface="com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcService"-->
  <!--      check="false" timeout="50000"/>-->
  <!--    <dubbo:reference id="weatherFeatureCityDayHisService"-->
  <!--      interface="com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityDayHisService"-->
  <!--      check="false" timeout="50000"/>-->
  <!--    <dubbo:reference id="statisticsSynthesizeWeatherCityDayHisService"-->
  <!--      interface="com.tsintergy.lf.serviceapi.base.weather.api.StatisticsSynthesizeWeatherCityDayHisService"-->
  <!--      check="false" timeout="50000"/>-->
  <!--    <dubbo:reference id="statisticsSynthesizeWeatherCityDayFcService"-->
  <!--      interface="com.tsintergy.lf.serviceapi.base.weather.api.StatisticsSynthesizeWeatherCityDayFcService"-->
  <!--      check="false" timeout="50000"/>-->
  <!--    <dubbo:reference id="weatherCityFcModifyService"-->
  <!--      check="false" interface="com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcModifyService" timeout="50000"/>-->
  <!--    <dubbo:reference id="settingSystemService"-->
  <!--      check="false" interface="com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService" timeout="50000"/>-->

</beans>
