package com.tsintergy.lf.serviceimpl.impl;

import com.tsieframework.cloud.security.serviceapi.system.api.RedisService;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsintergy.aif.algorithm.serviceapi.base.util.ALgorithmSupport;
import com.tsintergy.lf.core.constants.AlgorithmConstants;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.algorithm.api.Executor;
import com.tsintergy.lf.serviceapi.algorithm.api.SimilarDayQueryService;
import com.tsintergy.lf.serviceapi.algorithm.dto.HolidayInfo;
import com.tsintergy.lf.serviceapi.algorithm.dto.Param;
import com.tsintergy.lf.serviceapi.algorithm.dto.Result;
import com.tsintergy.lf.serviceapi.algorithm.dto.SimilarDayParam;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.api.HolidayService;
import com.tsintergy.lf.serviceapi.base.base.pojo.HolidayDO;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcService;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherEnum;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityHisService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisDO;
import com.tsintergy.lf.serviceimpl.factory.AlgorithmDetailFactory;
import com.tsintergy.lf.serviceimpl.factory.ForecastMethodFactory;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2023-05-05
 * @since 1.0.0
 */
@Slf4j
@Service
public class SimilarDayServiceImpl<E extends Param, T extends Result> implements SimilarDayQueryService<E, T> {

    @Autowired
    private LoadCityHisService loadCityHisService;

    @Autowired
    private WeatherCityHisService weatherCityHisService;

    @Autowired
    private WeatherCityFcService weatherCityFcService;

    @Autowired
    private CityService cityService;

    @Autowired
    private LoadCityFcService loadCityFcService;

    @Autowired
    private HolidayService holidayService;

    @Autowired
    public RedisService redisService;

    private static final DateFormatType DATE_PATTERN = DateFormatType.SIMPLE_DATE_FORMAT_STR;

    @Override
    public void forecast(E param) throws Exception {
        Map<String, Object> dataMap = new HashMap<>();
        mergeData(param, dataMap);
        execute(param, dataMap);
    }


    /**
     * 1.匹配执行器；2.装载算法参数，例如in&out文件路径等
     * @param param
     * @param dataMap
     */
    @SneakyThrows
    public void execute(E param, Map<String, Object> dataMap) {
        //step1：匹配算法执行器
        Executor<E, T> executor = ForecastMethodFactory.create(param);
        //step2：装载算法执行路径 模板名称
        param.setAlgorithmDetail(AlgorithmDetailFactory.create(param));
        //step3： 执行预处理；个别算法in&out文件特殊，可以在此处处理
        executor.preprocess(param, dataMap);
        //step4：执行器调用算法命令
        executor.execute(param);
    }




    @Override
    public void mergeData(E param, Map<String, Object> srcMap) throws Exception {
        SimilarDayParam similarDayParam = (SimilarDayParam) param;
        String cityId = similarDayParam.getCityId();
        String caliberId = similarDayParam.getCaliberId();
        String algoId = similarDayParam.getAlgoId();
        String weatherCityId = cityService.findWeatherCityId(cityId);
        Date targetDate = similarDayParam.getTargetDate();
        List<Date> chooseDateList = similarDayParam.getChooseDateList();
        List<Date> dateList = null;
        //获取时间范围内的所有日期
        dateList = DateUtil.getListBetweenDay(similarDayParam.getStartDate(),
            similarDayParam.getEndDate());
        for (Date date : chooseDateList) {
            if (!dateList.contains(date)){
                dateList.add(date);
            }
        }
        if ("0".equals(similarDayParam.getTargetDataType())&&!dateList.contains(targetDate)){
            //添加当天历史数据
            dateList.add(similarDayParam.getTargetDate());
        }

        Collections.sort(dateList);
        //节假日与调休日
        mergeHolidayAndAdjustedWorkdayData(srcMap);
        //获取城市历史负荷数据
        List<LoadCityHisDO> loadData =
            new ArrayList<>(loadCityHisService.findLoadCityDOsByCityIdInDates(cityId, dateList, caliberId));
        List<Integer> typeList = Arrays.asList(
            WeatherEnum.HUMIDITY.getType(),
            WeatherEnum.TEMPERATURE.getType(),
            WeatherEnum.EFFECTIVE_TEMPERATURE.getType(),
            WeatherEnum.RAINFALL.getType(),
            WeatherEnum.WINDSPEED.getType());
        List<WeatherCityHisDO> weatherCityHisDOList = new ArrayList<>();
        for (Integer integer : typeList) {
            List<WeatherCityHisDO> weatherByDates = weatherCityHisService.findWeatherByDates(weatherCityId, integer,
                dateList);
            weatherCityHisDOList.addAll(weatherByDates);
        }
        //List<WeatherCityHisDO> weatherCityHisDOList = weatherCityHisService.queryWeatherCityHisDOAllTypeList(weatherCityId, typeList, dateList);
        //获取历史气象数据 湿度
        List<WeatherCityHisDO> humidData =
            weatherCityHisDOList.stream()
                .filter(weatherCityHisDO -> weatherCityHisDO.getType()
                    .equals(WeatherEnum.HUMIDITY.getType())).collect(Collectors.toList());
        //获取历史气象数据 温度
        List<WeatherCityHisDO> tempData =
            weatherCityHisDOList.stream()
                .filter(weatherCityHisDO -> weatherCityHisDO.getType()
                    .equals(WeatherEnum.TEMPERATURE.getType())).collect(Collectors.toList());
        //获取历史气象数据 体感温度
        List<WeatherCityHisDO> apparentData =
            weatherCityHisDOList.stream()
                .filter(weatherCityHisDO -> weatherCityHisDO.getType()
                    .equals(WeatherEnum.EFFECTIVE_TEMPERATURE.getType())).collect(Collectors.toList());
        //获取历史气象数据 降雨
        List<WeatherCityHisDO> rainData =
            weatherCityHisDOList.stream()
                .filter(weatherCityHisDO -> weatherCityHisDO.getType()
                    .equals(WeatherEnum.RAINFALL.getType())).collect(Collectors.toList());
        //获取历史气象数据 风速
        List<WeatherCityHisDO> windData =
            weatherCityHisDOList.stream()
                .filter(weatherCityHisDO -> weatherCityHisDO.getType()
                    .equals(WeatherEnum.WINDSPEED.getType())).collect(Collectors.toList());

        //目标日期数据为预测数据
        if ("1".equals(similarDayParam.getTargetDataType())){
            //预测负荷数据
            LoadCityFcDO loadCityFcDO = loadCityFcService.getLoadCityFcDO(targetDate, cityId, caliberId, algoId);
            LoadCityHisDO loadCityHisDO = new LoadCityHisDO();
            BeanUtils.copyProperties(loadCityFcDO, loadCityHisDO);
            loadData.add(loadCityHisDO);
            List<WeatherCityFcDO> weatherCityFcDOList = weatherCityFcService.queryWeatherCityHisDOAllTypeList(weatherCityId, typeList, Collections.singletonList(targetDate));
            //获取历史气象数据 湿度
            List<WeatherCityFcDO> humidFc = weatherCityFcDOList.stream()
                .filter(weatherCityFc -> weatherCityFc.getType()
                    .equals(WeatherEnum.HUMIDITY.getType())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(humidFc)){
                WeatherCityFcDO weatherCityFcDO = humidFc.get(0);
                if (!Objects.isNull(weatherCityFcDO)){
                    WeatherCityHisDO weatherCityHisDO = new WeatherCityHisDO();
                    BeanUtils.copyProperties(weatherCityFcDO, weatherCityHisDO);
                    humidData.add(weatherCityHisDO);
                }
            }

            //获取历史气象数据 温度
            List<WeatherCityFcDO> tempFc =
                weatherCityFcDOList.stream()
                    .filter(weatherCityFcDO -> weatherCityFcDO.getType()
                        .equals(WeatherEnum.TEMPERATURE.getType())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(tempFc)){
                WeatherCityFcDO weatherCityFcDO = tempFc.get(0);
                if (!Objects.isNull(weatherCityFcDO)){
                    WeatherCityHisDO weatherCityHisDO = new WeatherCityHisDO();
                    BeanUtils.copyProperties(weatherCityFcDO, weatherCityHisDO);
                    tempData.add(weatherCityHisDO);
                }
            }
            //获取历史气象数据 体感温度
            List<WeatherCityFcDO> apparentFc =
                weatherCityFcDOList.stream()
                    .filter(weatherCityFcDO -> weatherCityFcDO.getType()
                        .equals(WeatherEnum.EFFECTIVE_TEMPERATURE.getType())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(apparentFc)){
                WeatherCityFcDO weatherCityFcDO = apparentFc.get(0);
                if (!Objects.isNull(weatherCityFcDO)){
                    WeatherCityHisDO weatherCityHisDO = new WeatherCityHisDO();
                    BeanUtils.copyProperties(weatherCityFcDO, weatherCityHisDO);
                    apparentData.add(weatherCityHisDO);
                }
            }
            //获取历史气象数据 降雨
            List<WeatherCityFcDO> rainFc =
                weatherCityFcDOList.stream()
                    .filter(weatherCityFcDO -> weatherCityFcDO.getType()
                        .equals(WeatherEnum.RAINFALL.getType())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(rainFc)){
                WeatherCityFcDO weatherCityFcDO = rainFc.get(0);
                if (!Objects.isNull(weatherCityFcDO)){
                    WeatherCityHisDO weatherCityHisDO = new WeatherCityHisDO();
                    BeanUtils.copyProperties(weatherCityFcDO, weatherCityHisDO);
                    rainData.add(weatherCityHisDO);
                }
            }
            //获取历史气象数据 风速
            List<WeatherCityFcDO> winFc =
                weatherCityFcDOList.stream()
                    .filter(weatherCityFcDO -> weatherCityFcDO.getType()
                        .equals(WeatherEnum.WINDSPEED.getType())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(winFc)){
                WeatherCityFcDO weatherCityFcDO = winFc.get(0);
                if (!Objects.isNull(weatherCityFcDO)){
                    WeatherCityHisDO weatherCityHisDO = new WeatherCityHisDO();
                    BeanUtils.copyProperties(weatherCityFcDO, weatherCityHisDO);
                    windData.add(weatherCityHisDO);
                }
            }
        }

        if (!CollectionUtils.isEmpty(chooseDateList)){
            Collections.sort(chooseDateList);
            List<String> consecutiveDays = chooseDateList.stream()
                .map(date -> DateFormatUtils.format(date, DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR.getValue()))
                .collect(Collectors.toList());
            srcMap.put(AlgorithmConstants.CUSTOMIZED_DATE, consecutiveDays);
        }

        List<String> loadList = loadData.stream()
            .map(loadCityHisDO -> DateFormatUtils.format(loadCityHisDO.getDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR.getValue()))
            .collect(Collectors.toList());
        List<String> tempList = tempData.stream()
            .map(weatherCityHisDO -> DateFormatUtils.format(weatherCityHisDO.getDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR.getValue()))
            .collect(Collectors.toList());
        List<String> rainList = rainData.stream()
            .map(weatherCityHisDO -> DateFormatUtils.format(weatherCityHisDO.getDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR.getValue()))
            .collect(Collectors.toList());
        List<String> humidList = humidData.stream()
            .map(weatherCityHisDO -> DateFormatUtils.format(weatherCityHisDO.getDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR.getValue()))
            .collect(Collectors.toList());
        List<String> apparentList = apparentData.stream()
            .map(weatherCityHisDO -> DateFormatUtils.format(weatherCityHisDO.getDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR.getValue()))
            .collect(Collectors.toList());
        List<String> windList = windData.stream()
            .map(weatherCityHisDO -> DateFormatUtils.format(weatherCityHisDO.getDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR.getValue()))
            .collect(Collectors.toList());

        if ("1".equals(similarDayParam.getTargetDataType())&&!dateList.contains(targetDate)){
            dateList.add(targetDate);
        }
        for (Date date : dateList) {
            String dateStr = DateFormatUtils.format(date, DateFormatType.SIMPLE_DATE_FORMAT_STR.getValue());
            if (!loadList.contains(dateStr)){
                LoadCityHisDO loadCityHisDO = new LoadCityHisDO();
                loadCityHisDO.setDate(new java.sql.Date(date.getTime()));
                loadData.add(loadCityHisDO);
            }
            if (!tempList.contains(dateStr)){
                WeatherCityHisDO weatherCityHisDO = new WeatherCityHisDO();
                weatherCityHisDO.setDate(new java.sql.Date(date.getTime()));
                tempData.add(weatherCityHisDO);
            }
            if (!rainList.contains(dateStr)){
                WeatherCityHisDO weatherCityHisDO = new WeatherCityHisDO();
                weatherCityHisDO.setDate(new java.sql.Date(date.getTime()));
                rainData.add(weatherCityHisDO);
            }
            if (!humidList.contains(dateStr)){
                WeatherCityHisDO weatherCityHisDO = new WeatherCityHisDO();
                weatherCityHisDO.setDate(new java.sql.Date(date.getTime()));
                humidData.add(weatherCityHisDO);
            }
            if (!apparentList.contains(dateStr)){
                WeatherCityHisDO weatherCityHisDO = new WeatherCityHisDO();
                weatherCityHisDO.setDate(new java.sql.Date(date.getTime()));
                apparentData.add(weatherCityHisDO);
            }
            if (!windList.contains(dateStr)){
                WeatherCityHisDO weatherCityHisDO = new WeatherCityHisDO();
                weatherCityHisDO.setDate(new java.sql.Date(date.getTime()));
                windData.add(weatherCityHisDO);
            }
        }

        loadData.sort(Comparator.comparing(LoadCityHisDO::getDate));
        tempData.sort(Comparator.comparing(WeatherCityHisDO::getDate));
        rainData.sort(Comparator.comparing(WeatherCityHisDO::getDate));
        humidData.sort(Comparator.comparing(WeatherCityHisDO::getDate));
        apparentData.sort(Comparator.comparing(WeatherCityHisDO::getDate));
        windData.sort(Comparator.comparing(WeatherCityHisDO::getDate));
        srcMap.put(AlgorithmConstants.HISTORY_LOAD, loadData);
        srcMap.put(AlgorithmConstants.TEMPERATURE, tempData);
        srcMap.put(AlgorithmConstants.PRECIPITATION, rainData);
        srcMap.put(AlgorithmConstants.HUMIDITY, humidData);
        srcMap.put(AlgorithmConstants.APPARENT, apparentData);
        srcMap.put(AlgorithmConstants.WIND, windData);

    }


    void mergeHolidayAndAdjustedWorkdayData(Map<String, Object> datas) throws Exception {
        List<HolidayDO> holidayVOS = holidayService.getAllHolidayVOS();
        holidayVOS.sort(Comparator.comparing(HolidayDO::getDate, Comparator.nullsFirst(Comparator.naturalOrder())));
        Map<String, Object> holidayInfo = this.getHolidayInfo(holidayVOS);
        datas.put(AlgorithmConstants.HOLIDAY_INFO, holidayInfo.get(AlgorithmConstants.HOLIDAY_INFO));
        datas.put(AlgorithmConstants.ADJUSTED_WORKDAY, holidayInfo.get(AlgorithmConstants.OFFDATES));
    }

    /**
     * 功能描述: <br> 构造算法所需的节假日数据的形式
     *
     * @param holidayVOS 节假日
     * <AUTHOR>
     * @since 1.0.0
     */
    public Map<String, Object> getHolidayInfo(List<HolidayDO> holidayVOS) throws Exception {
        Map<String, Object> mapData = new HashMap<>();
        //节假日信息
        List<HolidayInfo> holidayInfos = new ArrayList<>();
        //调休日期
        List<String> offDates = new ArrayList<>();
        holidayVOS.sort(Comparator.comparing(HolidayDO::getStartDate,
            Comparator.nullsFirst(Comparator.naturalOrder())));
        for (HolidayDO holidayVO : holidayVOS) {
            String offDate = holidayVO.getOffDates();
            if (StringUtils.isNotBlank(offDate)) {
                String[] dateStr = offDate.split(",");
                for (String str : dateStr) {
                    offDates.add(DateUtils.formateString(str, DateFormatType.SIMPLE_DATE_FORMAT_STR,
                        DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR));
                }
            }
            Date isHolidayDate = holidayVO.getDate();
            Date startDate = holidayVO.getStartDate();
            Date endDate = holidayVO.getEndDate();
            List<Date> dateList = DateUtil.getListBetweenDay(startDate, endDate);
            for (Date date : dateList) {
                HolidayInfo info = new HolidayInfo();
                info.setDate(new java.sql.Date(date.getTime()));
                //数据库存储 101 102 103....  算法需传  1 2 3 ....
                info.setHolidayType(holidayVO.getCode() % 100);
                //是否为节假日当天
                if (date.equals(isHolidayDate)) {
                    info.setIsHolidayDay(1);
                } else {
                    info.setIsHolidayDay(0);
                }
                //该日前休假的天数
                Integer dayAhead = DateUtil.getDayCountBetweenDay(DateUtils.date2String(holidayVO.getStartDate(),
                    DATE_PATTERN), DateUtils.date2String(date, DATE_PATTERN), "yyyy-MM-dd");
                //该日后休假的天数
                Integer dayAfter = DateUtil.getDayCountBetweenDay(DateUtils.date2String(date, DATE_PATTERN),
                    DateUtils.date2String(holidayVO.getEndDate(), DATE_PATTERN), "yyyy-MM-dd");
                info.setDaysAhead(dayAhead);
                info.setDaysAfter(dayAfter);
                holidayInfos.add(info);
            }
        }
        mapData.put(AlgorithmConstants.HOLIDAY_INFO, holidayInfos);
        mapData.put(AlgorithmConstants.OFFDATES, offDates);
        return mapData;
    }
}
