package com.tsintergy.lf.serviceimpl.impl;

import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.aif.algorithm.serviceapi.base.dto.AlgorithmEnum;
import com.tsintergy.aif.algorithm.serviceapi.base.dto.shortforecast.ShortForecastMetaData;
import com.tsintergy.aif.algorithm.serviceapi.base.dto.shortforecast.ShortForecastResult;
import com.tsintergy.aif.algorithm.serviceimpl.client.support.ComprehensiveInputHelper;
import com.tsintergy.lf.core.constants.CityConstants;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.constants.SystemConstant;
import com.tsintergy.lf.core.enums.WeatherSourceEnum;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.serviceapi.algorithm.api.ComprehensiveAlgorithmService;
import com.tsintergy.lf.serviceapi.algorithm.dto.ComprehensiveAlgorithmParam;
import com.tsintergy.lf.serviceapi.base.base.api.CaliberService;
import com.tsintergy.lf.serviceapi.base.base.api.HolidayService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CaliberDO;
import com.tsintergy.lf.serviceapi.base.forecast.api.*;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.AlgorithmDO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.AlgorithmParamDO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.CityExtraAlgorithmRunConfigDO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.recall.api.LoadCityFcRecallService;
import com.tsintergy.lf.serviceapi.base.recall.pojo.LoadCityFcRecallDO;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceimpl.client.ComprehensiveAlgorithmClient;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2023-06-20
 * @since 1.0.0
 */
@Slf4j
@Service("comprehensiveAlgorithmService")
public class ComprehensiveAlgorithmServiceImpl implements ComprehensiveAlgorithmService {

    @Autowired
    private LoadCityFcBatchService loadCityFcBatchService;
    @Autowired
    private ComprehensiveAlgorithmClient comprehensiveAlgorithmClient;
    @Autowired
    private CaliberService caliberService;
    @Autowired
    private AlgorithmParamService algorithmParamService;
    @Autowired
    private AlgorithmService algorithmService;
    @Autowired
    private LoadCityFcService loadCityFcService;
    @Autowired
    private LoadCityFcRecallService loadCityFcRecallService;

    @Autowired
    private SettingSystemService systemService;

    @Autowired
    private HolidayService holidayService;


    @Autowired
    private CityExtraAlgorithmRunConfigService cityExtraAlgorithmRunConfigService;


    @Override
    public void doForecast(Date startDate, Date endDate, String caliberId, String cityId, Integer pointNum,
                                     Integer fcDayWeatherType, Integer type, Integer apparentType,Boolean recall) {
        String gurId = "30";
        CaliberDO caliberDO = caliberService.findCaliberDOByPk(caliberId);
        ComprehensiveAlgorithmParam comprehensiveAlgorithmParam = new ComprehensiveAlgorithmParam(caliberDO.getName(), startDate, endDate, new String[]{caliberId});
        comprehensiveAlgorithmParam.setCaliberName(caliberDO.getName());
        comprehensiveAlgorithmParam.setCityId(Constants.PROVINCE_ID);
        comprehensiveAlgorithmParam.setCaliberId(caliberDO.getId());
        if (pointNum != null) {
            comprehensiveAlgorithmParam.setPoint(pointNum);
        }
        if (fcDayWeatherType != null) {
            comprehensiveAlgorithmParam.setFcDayWeatherType(fcDayWeatherType);
        }
        if (type != null) {
            comprehensiveAlgorithmParam.setFcType(type);
        }
        if (recall!=null){
            comprehensiveAlgorithmParam.setRecall(recall);
        }
        try {
            //算法参数列表
            List<AlgorithmParamDO> algorithmParam = algorithmParamService.getAlgorithmParamByAlgorithmIdNoCache(
                AlgorithmEnum.NEW_COMPREHENSIVE_MODEL.getId());
            Map<String, String> paramMap = algorithmParam.stream().collect(
                Collectors.toMap(AlgorithmParamDO::getParamEn, AlgorithmParamDO::getDefaultValue));
            mergeAlgorithmParam(paramMap,comprehensiveAlgorithmParam);
            //使用的城市id 用逗号隔开
            String userCity = cityId;
            if (Constants.PROVINCE_ID.equals(cityId)) {
                userCity = paramMap.get("UserCity");
                comprehensiveAlgorithmParam.setAlgorithmEnums(Arrays.asList(AlgorithmEnum.NEW_COMPREHENSIVE_MODEL));
                comprehensiveAlgorithmParam.setAlgorithmEnum((AlgorithmEnum.NEW_COMPREHENSIVE_MODEL));
            }
            comprehensiveAlgorithmParam.setUserCityList(Arrays.asList(userCity.split(",")));
            comprehensiveAlgorithmParam.setCityId(cityId);
            String[] distinguish = {cityId};
            comprehensiveAlgorithmParam.setDistinguishParams(distinguish);
            ShortForecastResult forecastResult = comprehensiveAlgorithmClient.invoke(comprehensiveAlgorithmParam);
            if (BooleanUtils.isNotTrue(recall)){
                this.saveResult(forecastResult, cityId, caliberId, gurId, null);
                //判断是否需要使用多气象源再次运行
                this.reRunWithMultiWeatherSource(comprehensiveAlgorithmParam, gurId);
            }else {
                this.saveRecallResult(forecastResult, cityId, caliberId, gurId, null);
            }

        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    @Override
    @SneakyThrows
    public void saveResult(ShortForecastResult forecastResult, String cityId, String caliberId, String gurId, Date startDate) {
        List<ShortForecastMetaData> forecastResultList = forecastResult.getShortForecastMetaData();
        List<Date> holidays = holidayService.getAllHolidays();
        for (ShortForecastMetaData shortForecastMetaData : forecastResultList) {
            Date date = shortForecastMetaData.getDate();
            List<BigDecimal> value = shortForecastMetaData.getValue();
            Map<String, BigDecimal> valueMap = ColumnUtil
                    .listToMap(value, Constants.LOAD_CURVE_START_WITH_ZERO);
            LoadCityFcDO loadCityFcDO = new LoadCityFcDO();
            BasePeriodUtils.setAllFiled(loadCityFcDO, valueMap);
            loadCityFcDO.setAlgorithmId(gurId);
            loadCityFcDO.setDate(new java.sql.Date(date.getTime()));
            loadCityFcDO.setCityId(cityId);
            loadCityFcDO.setCaliberId(caliberId);
            List<LoadCityFcDO> reportLoadFc = loadCityFcService.findReportLoadFc(cityId, caliberId, date, date);
            // 考虑节假日和非节假日的默认上报
            String systemAlgorithmId = "";
            String[] split = null;
            if (holidays.contains(date)) {
                // 查询节假日默认上报算法
                split = systemService.findByFieldId(SystemConstant.HOLIDAY_ALGORITHM).getValue()
                        .split(Constants.SEPARATOR_PUNCTUATION);
            } else {
                // 查询正常日默认上报算法
                split = systemService.findByFieldId(SystemConstant.NORMAL_ALGORITHM).getValue()
                        .split(Constants.SEPARATOR_PUNCTUATION);
            }
            if (cityId.equals(CityConstants.PROVINCE_ID)) {
                systemAlgorithmId = split[0];
            } else {
                systemAlgorithmId = split[1];
            }
            if (gurId.equals(systemAlgorithmId)) {
                if (reportLoadFc.size() < 1) {
                    // 没有任何上报过
                    loadCityFcDO.setReport(true);
                    loadCityFcDO.setReportTime(new Timestamp(System.currentTimeMillis()));
                    loadCityFcDO.setSucceed(true);
                } else {
                    if (reportLoadFc.size() > 0 && reportLoadFc.get(0).getAlgorithmId()
                            .equals(systemAlgorithmId)) {
                        // 自己上报过
                        loadCityFcDO.setReport(true);
                        loadCityFcDO.setReportTime(new Timestamp(System.currentTimeMillis()));
                        loadCityFcDO.setSucceed(true);
                    } else if (reportLoadFc.size() > 0 && !reportLoadFc.get(0).getAlgorithmId()
                            .equals(systemAlgorithmId) && !reportLoadFc.get(0).getAlgorithmId()
                            .equals("0")) {
                        // 除人工和自己算法上报之外的上报（取消之前的上报，然后上报自己）
                        this.cancelReport(reportLoadFc);
                        loadCityFcDO.setReport(true);
                        loadCityFcDO.setReportTime(new Timestamp(System.currentTimeMillis()));
                        loadCityFcDO.setSucceed(true);
                    }
                }
            }
            //算法结果中的date必须在startDate之后才保存
            if (startDate != null && date.before(startDate)) {
                continue;
            }
            loadCityFcService.doSaveOrUpdateLoadCityFcDO96(loadCityFcDO);
            loadCityFcBatchService.doSave(loadCityFcDO);
        }
    }

    @Override
    public void saveRecallResult(ShortForecastResult forecastResult, String cityId, String caliberId, String gurId, Date startDate) throws Exception {
        List<ShortForecastMetaData> forecastResultList = forecastResult.getShortForecastMetaData();
        for (ShortForecastMetaData shortForecastMetaData : forecastResultList) {
            Date date = shortForecastMetaData.getDate();
            List<BigDecimal> value = shortForecastMetaData.getValue();
            Map<String, BigDecimal> valueMap = ColumnUtil
                    .listToMap(value, Constants.LOAD_CURVE_START_WITH_ZERO);
            LoadCityFcRecallDO loadCityFcDO = new LoadCityFcRecallDO();
            BasePeriodUtils.setAllFiled(loadCityFcDO, valueMap);
            loadCityFcDO.setAlgorithmId(gurId);
            loadCityFcDO.setDate(new java.sql.Date(date.getTime()));
            loadCityFcDO.setCityId(cityId);
            loadCityFcDO.setCaliberId(caliberId);
            loadCityFcRecallService.doSaveOrUpdateLoadCityFcDO96(loadCityFcDO);
        }
    }

    @SneakyThrows
    private void reRunWithMultiWeatherSource(ComprehensiveAlgorithmParam param, String gruId) {
        AlgorithmDO algorithmDO = algorithmService.getAlgorithmDOById(gruId);
        CityExtraAlgorithmRunConfigDO extraRunConfigDO = cityExtraAlgorithmRunConfigService.findExtraRunConfigDOByType(algorithmDO.getCode());
        if (extraRunConfigDO == null) {
            return;
        }
        List<String> cityIds = Arrays.asList(extraRunConfigDO.getCityIds().split(","));
        if (cityIds.contains(param.getCityId())) {
            log.info("城市{},算法{}使用多气象源再次运行", param.getCityName(), gruId);
            param.setFcWeatherSource(WeatherSourceEnum.METEO.name());
            ShortForecastResult forecastResult = comprehensiveAlgorithmClient.invoke(param);
            saveResult(forecastResult, param.getCityId(), param.getCaliberId(), extraRunConfigDO.getAlgorithmId(), null);
        }
    }

    private void cancelReport(List<LoadCityFcDO> reportLoadFc) throws Exception {
        for (LoadCityFcDO loadCityFcDO : reportLoadFc) {
            loadCityFcDO.setRecommend(false);
            loadCityFcDO.setReport(false);
            loadCityFcDO.setSucceed(false);
            loadCityFcService.doSaveOrUpdateLoadCityFcDO96(loadCityFcDO);
            loadCityFcBatchService.doSave(loadCityFcDO);
        }
    }
    /**
     * 算法参数赋值
     * @param paramMap
     * @param param
     */
    private void mergeAlgorithmParam(Map<String, String> paramMap, ComprehensiveAlgorithmParam param) {
        param.setUseGPU(paramMap.get(ComprehensiveInputHelper.USE_GPU));
        param.setGPUCardMemory(paramMap.get(ComprehensiveInputHelper.GPU_CARD_MEMORY));
        param.setCPUThreads(paramMap.get(ComprehensiveInputHelper.CPU_THREADS));
        param.setTrainBeginDay(paramMap.get(ComprehensiveInputHelper.TRAIN_BEGIN_DAY));
        param.setWindowDate(paramMap.get(ComprehensiveInputHelper.WINDOW_DATE));
        param.setWindowTmp(paramMap.get(ComprehensiveInputHelper.WINDOW_TMP));
        param.setWindowLoad(paramMap.get(ComprehensiveInputHelper.WINDOW_LOAD));
        param.setBatchSize(paramMap.get(ComprehensiveInputHelper.BATCH_SIZE));
        param.setDecayInterval(paramMap.get(ComprehensiveInputHelper.DECAY_INTERVAL));
        param.setDecayRatio(paramMap.get(ComprehensiveInputHelper.DECAY_RATIO));
        param.setTrainingRounds(paramMap.get(ComprehensiveInputHelper.TRAINING_ROUNDS));
        param.setTrainingLr(paramMap.get(ComprehensiveInputHelper.TRAINING_LR));
        param.setFinetuningNumFreeLayer(paramMap.get(ComprehensiveInputHelper.FINETUNING_NUM_FREE_LAYER));
        param.setFinetuningRounds(paramMap.get(ComprehensiveInputHelper.FINETUNING_ROUNDS));
        param.setFinetuningNumSyn(paramMap.get(ComprehensiveInputHelper.FINETUNING_NUM_SYN));
        param.setFinetuningLr(paramMap.get(ComprehensiveInputHelper.FINETUNING_LR));
        param.setWindowWidth(paramMap.get(ComprehensiveInputHelper.WINDOW_WIDTH));
        param.setIsHumidity(paramMap.get(ComprehensiveInputHelper.IS_HUMIDITY));
        param.setIsPrecipitation(paramMap.get(ComprehensiveInputHelper.IS_PRECIPITATION));
        param.setIsWind(paramMap.get(ComprehensiveInputHelper.IS_WIND));
        param.setIsIrradiance(paramMap.get(ComprehensiveInputHelper.IS_IRRADIANCE));
        param.setIsIndustryLoad(paramMap.get(ComprehensiveInputHelper.IS_INDUSTRY_LOAD));
        param.setIsCityLoad(paramMap.get(ComprehensiveInputHelper.IS_CITY_LOAD));
        param.setIsFeltTemperature(paramMap.get(ComprehensiveInputHelper.IS_FELT_TEMPERATURE));
        param.setIsHeatIndex(paramMap.get(ComprehensiveInputHelper.IS_HEAT_INDEX));

    }
}
