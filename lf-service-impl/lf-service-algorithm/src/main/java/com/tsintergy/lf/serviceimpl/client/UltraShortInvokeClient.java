package com.tsintergy.lf.serviceimpl.client;

import cn.hutool.core.collection.CollectionUtil;
import com.tsieframework.core.base.dao.type.DataPointListMeta;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.aif.algorithm.serviceapi.base.dto.DateValuesDTO;
import com.tsintergy.aif.algorithm.serviceapi.base.dto.DateVariableDTO;
import com.tsintergy.aif.algorithm.serviceapi.base.dto.ultraShort.UltraShortBasicData;
import com.tsintergy.aif.algorithm.serviceapi.base.dto.ultraShort.UltraShortParam;
import com.tsintergy.aif.algorithm.serviceimpl.client.facade.impl.AbstractUltraShortForecastInvokeClient;
import com.tsintergy.aif.tool.core.enums.WeatherEnum;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.PeriodDataUtil;
import com.tsintergy.lf.core.util.UltraSystemUtils;
import com.tsintergy.lf.core.util.VslfDateUtil;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcService;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityHisService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisDO;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.joda.time.base.BasePeriod;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Description
 * @Date 2023/2/8 14:18
 * <AUTHOR>
 **/
@Slf4j
@Component
public class UltraShortInvokeClient extends AbstractUltraShortForecastInvokeClient<UltraShortParam> {

    public static final String NULL_ENUM = "<NULL>";

    @Autowired
    LoadCityHisService loadCityHisService;

    @Autowired
    LoadCityFcService loadCityFcService;

    @Autowired
    WeatherCityFcService weatherCityFcService;

    @Autowired
    WeatherCityHisService weatherCityHisService;

    @Autowired
    CityService cityService;

    @SneakyThrows
    @Override
    protected UltraShortBasicData generateBasicData(UltraShortParam param) {
        String cityId = param.getCityId();
        String caliberId = param.getCaliberId();
        Integer timeUnit = param.getTimeUnit();
        Date startDate = param.getStartDate();
        Integer pointLen = param.getPointLen();
        Integer startTime = param.getStartTime();
        UltraShortBasicData basicData = new UltraShortBasicData();
        Date tomorrow = DateUtils.addDays(startDate, 1);
        //异常数据时间
        List<DateVariableDTO> badTimes = new ArrayList<>();
        basicData.setBadTimes(badTimes);
        //历史负荷数据 预测日期前的数据
        List<LoadCityHisDO> loadCityHisDOS =
            loadCityHisService.getLoadCityHisDOS(cityId, caliberId, null, tomorrow);
        //填入历史负荷
        if (!CollectionUtil.isEmpty(loadCityHisDOS)) {
            List<DateValuesDTO> loadList = new ArrayList<>();
            for (LoadCityHisDO loadCityHisDO : loadCityHisDOS) {
                DateValuesDTO dateValuesDTO = new DateValuesDTO();
                Date dateTime = loadCityHisDO.getDate();
                dateValuesDTO.setDate(date2Str(dateTime));
                List<BigDecimal> tvData = BasePeriodUtils.toList(loadCityHisDO, Constants.LOAD_CURVE_POINT_NUM,
                    Constants.LOAD_CURVE_START_WITH_ZERO);
                if (CollectionUtil.isEmpty(tvData)) {
                    continue;
                }
                dateValuesDTO.setValues(listBig2Str(tvData));
                loadList.add(dateValuesDTO);
            }

            //获取前三天日期
            List<Date> dateList = VslfDateUtil.getListBetweenDay(DateUtils.addDays(startDate, -10), tomorrow);
            //判断是否有预测日期前三天的数据 如果没有就用预测数据填入
            List<String> dateStrCollect = loadList.stream().map(DateValuesDTO::getDate).collect(Collectors.toList());
            //判断前三天对应日期是否有数据，如果没有从预测数据中取并填入，前三天是不能缺数据的
            for (Date date : dateList) {
                String dateStr = date2Str(date);
                if (!dateStrCollect.contains(dateStr)) {
                    List<BigDecimal> tvData = null;
                    LoadCityFcDO loadCityFcDO =
                        loadCityFcService.getLoadCityFcDO(date, cityId, caliberId, param.getAlgorithmEnum().getId());
                    if (!Objects.isNull(loadCityFcDO)){
                        tvData = BasePeriodUtils.toList(loadCityFcDO, Constants.LOAD_CURVE_POINT_NUM,
                            Constants.LOAD_CURVE_START_WITH_ZERO);
                    }else {
                        try {
                            //如果预测数据还没有就拿去年数据
                            LoadCityHisDO loadCityHisDO =
                                loadCityHisService.getLoadCityHisDO(cityId, caliberId, DateUtils.addYears(date, -1));
                            if (!Objects.isNull(loadCityHisDO)){
                                tvData =  BasePeriodUtils.toList(loadCityHisDO, Constants.LOAD_CURVE_POINT_NUM,
                                    Constants.LOAD_CURVE_START_WITH_ZERO);
                            }else {
                                //如果去年历史也没有就用去年预测来填补
                                LoadCityFcDO loadCityFcLastYearDO =
                                    loadCityFcService.getLoadCityFcDO(DateUtils.addYears(date, -1), cityId, caliberId, param.getAlgorithmEnum().getId());
                                if (!Objects.isNull(loadCityFcLastYearDO)){
                                    tvData = BasePeriodUtils.toList(loadCityFcLastYearDO, Constants.LOAD_CURVE_POINT_NUM,
                                        Constants.LOAD_CURVE_START_WITH_ZERO);
                                }
                            }
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    }
                    if (!CollectionUtil.isEmpty(tvData)){
                        if (!CollectionUtil.isEmpty(tvData)) {
                            DateValuesDTO dateValuesDTO = new DateValuesDTO();
                            dateValuesDTO.setDate(dateStr);
                            dateValuesDTO.setValues(listBig2Str(tvData));
                            loadList.add(dateValuesDTO);
                        }
                    }
                }
            }
            //替换预测点数据
            loadList.forEach(iterm -> {
                if (iterm.getDate().equals(date2Str(startDate))||iterm.getDate().equals(date2Str(tomorrow))){
                    List<String> valueList = iterm.getValueList();
                    //广西屏蔽使用日前预测补充当日空点逻辑；
//                    LoadCityFcDO loadCityFcDO =
//                        loadCityFcService.getLoadCityFcDO(str2Date(iterm.getDate()), cityId, caliberId, param.getAlgorithmEnum().getId(),
//                            timeUnit, Constants.ULTRA_FORECAST_1);
//                    if (!Objects.isNull(loadCityFcDO)){
//                        List<BigDecimal> tvData = loadCityFcDO.getTvData();
//                        //预测点数据替换 以及 空缺数据填入
//                        putData(tvData, valueList, iterm);
//                    }
                    //今年预测数据缺失时刻点就拿去年实际填补
                    try {
                        LoadCityHisDO loadCityHisLastYearDO =
                            loadCityHisService.getLoadCityHisDO(cityId, caliberId, DateUtils.addYears(str2Date(iterm.getDate()), -1));
                        if (!Objects.isNull(loadCityHisLastYearDO)){

                            List<BigDecimal> tvData = BasePeriodUtils.toList(loadCityHisLastYearDO, Constants.LOAD_CURVE_POINT_NUM,
                                Constants.LOAD_CURVE_START_WITH_ZERO);
                            putData(tvData, valueList, iterm);
                        }
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                    //要是去年历史还没有就拿去年预测填补
                    LoadCityFcDO loadCityFcLastYearDO =
                        loadCityFcService.getLoadCityFcDO(DateUtils.addYears(str2Date(iterm.getDate()), -1), cityId, caliberId, param.getAlgorithmEnum().getId());
                    if (!Objects.isNull(loadCityFcLastYearDO)){
                        List<BigDecimal> tvData = BasePeriodUtils.toList(loadCityFcLastYearDO, Constants.LOAD_CURVE_POINT_NUM,
                            Constants.LOAD_CURVE_START_WITH_ZERO);
                        //预测点数据替换 以及 空缺数据填入
                        putData(tvData, valueList, iterm);
                    }
                }
            });
            basicData.setLoadList(
                loadList.stream().sorted(Comparator.comparing(iterm -> str2Date(iterm.getDate())))
                    .collect(Collectors.toList())
            );
            String weatherCityId = cityService.findWeatherCityId(cityId);
            //查询历史气象数据
            List<WeatherCityHisDO> weatherCityHisDOS =  weatherCityHisService.findWeatherCityHisDOs(weatherCityId, WeatherEnum.TEMPERATURE.getType(), null,
                new java.sql.Date(tomorrow.getTime()));

            //填入历史温度气象数据
            if (!CollectionUtil.isEmpty(weatherCityHisDOS)) {
                List<DateValuesDTO> weatherList = new ArrayList<>();
                for (WeatherCityHisDO weatherCityHisDO : weatherCityHisDOS) {
                    java.sql.Date dateTime = weatherCityHisDO.getDate();
                    List<BigDecimal> tvData = BasePeriodUtils.toList(weatherCityHisDO, Constants.WEATHER_CURVE_POINT_NUM,
                        Constants.WEATHER_CURVE_START_WITH_ZERO);
                    if (CollectionUtil.isEmpty(tvData)) {
                        continue;
                    }
                    weatherList.add(genDateValuesDTO(date2Str(dateTime), timeUnit, tvData));
                }
                //判断是否有预测日期前三天的数据 如果没有就用预测数据填入
                List<String> dateStrList = weatherList.stream().map(DateValuesDTO::getDate)
                    .collect(Collectors.toList());
                //判断前三天加上预测当天对应日期是否有数据
                for (Date date : dateList) {
                    String dateStr = date2Str(date);
                    if (!dateStrList.contains(dateStr)) {
                        try {
                            List<BigDecimal> tvData = weatherCityFcService.find96WeatherCityFcValue(date, weatherCityId,
                                WeatherEnum.TEMPERATURE.getType());
                            if (CollectionUtil.isEmpty(tvData)){
                                //如果预测数据还没有就拿去年数据
                                tvData = weatherCityHisService.find96WeatherCityHisValue(
                                    DateUtils.addYears(date, -1), weatherCityId, WeatherEnum.TEMPERATURE.getType()
                                );
                                //如果去年历史数据缺失就拿去年预测数据填补
                                if (CollectionUtil.isEmpty(tvData)){
                                    tvData = weatherCityFcService.find96WeatherCityFcValue(DateUtils.addYears(date, -1), weatherCityId,
                                        WeatherEnum.TEMPERATURE.getType());
                                }
                            }
                            weatherList.add(genDateValuesDTO(dateStr, timeUnit, tvData));
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    }
                }

                //替换预测点气象数据
                weatherList.forEach(iterm -> {
                    if (iterm.getDate().equals(date2Str(startDate))||iterm.getDate().equals(date2Str(tomorrow))){
                        List<String> valueList = iterm.getValueList();
                        List<BigDecimal> tvData = null;
                        List<BigDecimal> tvData288 = null;
                        try {
                            tvData = weatherCityFcService.find96WeatherCityFcValue(str2Date(iterm.getDate()), weatherCityId, WeatherEnum.TEMPERATURE.getType());
                            if (!CollectionUtil.isEmpty(tvData)) {
                                tvData288 = PeriodDataUtil.data96to288(tvData, UltraSystemUtils.startWithZero());
                                putData(DataPointListMeta.MINUTE_15 == timeUnit?tvData:tvData288, valueList, iterm);
                            }
                            //如果预测数据还没有就拿去年数据
                            tvData = weatherCityHisService.find96WeatherCityHisValue(
                                DateUtils.addYears(str2Date(iterm.getDate()), -1), weatherCityId, WeatherEnum.TEMPERATURE.getType()
                            );
                            if (!CollectionUtil.isEmpty(tvData)) {
                                tvData288 = PeriodDataUtil.data96to288(tvData, UltraSystemUtils.startWithZero());
                                putData(DataPointListMeta.MINUTE_15 == timeUnit?tvData:tvData288, valueList, iterm);
                            }
                            tvData = weatherCityFcService.find96WeatherCityFcValue(
                                DateUtils.addYears(str2Date(iterm.getDate()), -1), weatherCityId, WeatherEnum.TEMPERATURE.getType()
                            );
                            if (!CollectionUtil.isEmpty(tvData)) {
                                tvData288 = PeriodDataUtil.data96to288(tvData, UltraSystemUtils.startWithZero());
                                putData(DataPointListMeta.MINUTE_15 == timeUnit?tvData:tvData288, valueList, iterm);
                            }
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    }
                });
                basicData.setTempList(
                    weatherList.stream().sorted(Comparator.comparing(iterm -> str2Date(iterm.getDate())))
                        .collect(Collectors.toList()));
                return basicData;
            }
            throw new RuntimeException("查询历史气象数据为空！");
        }
        throw new RuntimeException("查询历史负荷数据为空！");
    }

    //将BigDecimal集合转为String集合
    public List<String> listBig2Str(List<BigDecimal> tvData) {
        return tvData.stream()
            .map(data -> Objects.isNull(data)?NULL_ENUM:data.toString())
            .collect(Collectors.toList());
    }

    //日期转为yyyy-MM-dd类型字符串
    public String date2Str(Date date) {
        return DateFormatUtils.format(date, DateFormatType.SIMPLE_DATE_FORMAT_STR.getValue());
    }

    //字符串转为日期
    public Date str2Date(String dateStr) {
        return VslfDateUtil.getDateFromString(dateStr, DateFormatType.SIMPLE_DATE_FORMAT_STR.getValue());
    }


    //判断生成数据集合
    public DateValuesDTO genDateValuesDTO(String dateStr, Integer timeSpan, List<BigDecimal> tvData) {
        DateValuesDTO dateValuesDTO = new DateValuesDTO();
        dateValuesDTO.setDate(dateStr);
        if (DataPointListMeta.MINUTE_15 == timeSpan) {
            dateValuesDTO.setValues(listBig2Str(tvData));
        } else if (DataPointListMeta.MINUTE_5 == timeSpan) {
            dateValuesDTO.setValues(listBig2Str(tvData.size() == DataPointListMeta.POINTS_96?PeriodDataUtil.data96to288(tvData, UltraSystemUtils.startWithZero()):tvData));
        }
        return dateValuesDTO;
    }

    //填充空缺数据
    public void putData(List<BigDecimal> tvData, List<String> valueList, DateValuesDTO iterm){
        if (!CollectionUtil.isEmpty(tvData)){
            for (int i = 0; i < tvData.size(); i++) {
                if (i < valueList.size()){
                    String s = valueList.get(i);
                    BigDecimal point = tvData.get(i);
                    if (NULL_ENUM.equals(s)) {
                        if (!Objects.isNull(point)) {
                            valueList.set(i, point.toString());
                        }
                    }
                }
            }
            iterm.setValues(valueList);
        }
    }
}
