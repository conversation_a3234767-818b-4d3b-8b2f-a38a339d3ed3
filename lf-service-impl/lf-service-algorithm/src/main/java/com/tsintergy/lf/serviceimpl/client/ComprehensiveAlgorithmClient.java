package com.tsintergy.lf.serviceimpl.client;

import com.tsintergy.aif.algorithm.serviceapi.base.dto.comprehensive.ComprehensiveBasicData;
import com.tsintergy.aif.algorithm.serviceapi.base.pojo.Load;
import com.tsintergy.aif.algorithm.serviceapi.base.util.DateUtil;
import com.tsintergy.aif.algorithm.serviceimpl.client.facade.impl.AbstractComprehensiveInvokeClient;
import com.tsintergy.lf.serviceapi.algorithm.dto.ComprehensiveAlgorithmParam;
import com.tsintergy.lf.serviceapi.algorithm.dto.WeatherDTOS;
import com.tsintergy.lf.serviceapi.base.base.api.HolidayService;
import com.tsintergy.lf.serviceapi.base.base.pojo.HolidayDO;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcService;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.recall.api.LoadCityFcRecallService;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceimpl.support.AlgorithmDataSupport;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;

/**
 *
 * @description: 
 * <AUTHOR>
 * @date 2025/05/14 14:31
 * @version: 1.0
 */ 
public class ComprehensiveAlgorithmClient extends AbstractComprehensiveInvokeClient<ComprehensiveAlgorithmParam> {


    @Autowired
    LoadCityHisService loadCityHisService;
    @Autowired
    LoadCityFcService loadCityFcService;
    @Autowired
    LoadCityFcRecallService loadCityFcRecallService;

    @Autowired
    AlgorithmDataSupport algorithmDataSupport;


    @Autowired
    HolidayService holidayService;

    @Autowired
    private SettingSystemService settingSystemService;

    @Override
    protected ComprehensiveBasicData generateBasicData(ComprehensiveAlgorithmParam param) {
        Date beginDate = param.getBeginDate();
        Date lastDate = param.getEndDate();

        //气象开始时间
        String trainBeginDay = param.getTrainBeginDay();
        Date trainBeginDate = DateUtil.getDate(trainBeginDay, "yyyy-MM-dd");
        //气象数据源
        // 区域id 代替城市id 查实际数据
        List<LoadCityHisDO> loadCityHisDOS = null;
        List<? extends Load> loadCityFcDOS = null;
        ComprehensiveBasicData comprehensiveBasicData = null;
        try {
            loadCityHisDOS = loadCityHisService.getLoadCityHisDOS(param.getCityId(),param.getCaliberId(),trainBeginDate,lastDate);
            if (BooleanUtils.isTrue(param.getRecall())){
                loadCityFcDOS =  loadCityFcRecallService.getLoadCityFc(param.getCityId(),param.getCaliberId(),beginDate,lastDate,"10");
            }else {
                loadCityFcDOS = loadCityFcService.listLoadCityFc(param.getCityId(),param.getCaliberId(),beginDate,lastDate,"10");
            }
            //封装区域预测和实际气象
            WeatherDTOS areaWeatherDTO = algorithmDataSupport
                    .getWeatherDTO(trainBeginDate, lastDate, param.getUserCityList(), param.getFinetuningLr(), param.getRecall(),
                            param.getFcWeatherSource(), param.getFcDayWeatherType(),param.getIsPrecipitation());
            List<HolidayDO> holidayDOS = holidayService.findHolidayVOS(null, null);
            comprehensiveBasicData = new ComprehensiveBasicData(loadCityHisDOS,loadCityFcDOS, areaWeatherDTO.getHisHumiditys(),
                areaWeatherDTO.getFcHumiditys(), areaWeatherDTO.getHisTemperatures(), areaWeatherDTO.getFcTemperatures(),
                areaWeatherDTO.getHisPrecipitations(), areaWeatherDTO.getFcPrecipitations(), areaWeatherDTO.getHisWinds(),
                areaWeatherDTO.getFcWinds(),null,null,
                holidayDOS);
            comprehensiveBasicData.setDateNotInclude(settingSystemService.getNotIncludedDateList());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return comprehensiveBasicData;
    }

}
