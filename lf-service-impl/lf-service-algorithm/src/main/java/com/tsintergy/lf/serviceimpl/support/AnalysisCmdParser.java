/**
 * Copyright (C), 2015‐2021, 北京清能互联科技有限公司 Author:  wang<PERSON>@tsintergy.com Date:  2021/12/8 9:51 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceimpl.support;

import com.tsieframework.util.compatible.StringUtils;
import com.tsintergy.algorithm.alginv.core.console.ConsoleCmd;
import com.tsintergy.algorithm.alginv.core.console.ConsoleCmdOption;
import com.tsintergy.algorithm.alginv.core.console.ConsoleCmdParser;
import java.util.List;
import org.springframework.stereotype.Component;

/**
 * Description:  <br> 平台组封装路径中间有空格 /p: inputPath 此项目中不能有空格  /p:inputPath
 *
 * <AUTHOR>
 * @create 2021/12/8
 * @since 1.0.0
 */
@Component
public class AnalysisCmdParser implements ConsoleCmdParser {


    /**
     * 自定义拼接cmd命令
     * @param execPath 执行文件路径
     * @param optionList cmd命令列表
     * @return
     */
    @Override
    public ConsoleCmd parse(String execPath, List<ConsoleCmdOption> optionList) {
        StringBuilder sb = new StringBuilder();
        for (ConsoleCmdOption option : optionList) {
            //拼接参数。过滤掉他自己拼接的带参数前缀的cmd语句
            if (StringUtils.isEmpty(option.getName())) {
                if (option.isHasValue()) {
                    sb.append(option.getValue());
                    sb.append(" ");
                }
            }
        }
        return ConsoleCmd.of(sb.toString());
    }
}