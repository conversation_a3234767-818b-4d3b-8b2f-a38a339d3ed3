/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.impl;

import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.aif.algorithm.serviceapi.base.dto.AlgorithmEnum;
import com.tsintergy.aif.algorithm.serviceapi.base.dto.ResultList;
import com.tsintergy.aif.algorithm.serviceapi.base.dto.shortforecast.ShortForecastMetaData;
import com.tsintergy.aif.algorithm.serviceapi.base.dto.shortforecast.ShortForecastResult;
import com.tsintergy.aif.algorithm.serviceimpl.client.facade.impl.AbstractShortForecastInvokeClient;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.serviceapi.algorithm.api.AreaForecastAlgorithmService;
import com.tsintergy.lf.serviceapi.algorithm.dto.AreaForecastAlgorithmParam;
import com.tsintergy.lf.serviceapi.base.area.api.LoadAreaFcService;
import com.tsintergy.lf.serviceapi.base.area.pojo.BaseAreaDO;
import com.tsintergy.lf.serviceapi.base.area.pojo.LoadAreaFcDO;
import com.tsintergy.lf.serviceapi.base.forecast.api.AlgorithmService;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.AlgorithmDO;
import com.tsintergy.lf.serviceimpl.area.dao.BaseAreaDAO;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/8/9 14:28
 * @Version: 1.0.0
 */
@Service("areaForecastAlgorithmService")
public class AreaForecastAlgorithmServiceImpl extends BaseServiceImpl implements AreaForecastAlgorithmService {


    @Autowired
    AbstractShortForecastInvokeClient abstractShortForecastInvokeClient;


    @Autowired
    private LoadAreaFcService loadAreaFcService;
    @Autowired
    private BaseAreaDAO baseAreaDAO;

    @Autowired
    private AlgorithmService algorithmService;

    @Override
    public void doStatAreaForecast(String areaId, String caliberId,Integer pointNum, List<String> algorithmIds, Integer type, Integer fcDayWeatherType,
        Date startDate, Date endDate, Integer forecastType) {
        List<AlgorithmEnum> algorithmEnums = getAlgorithmEnums(algorithmIds);
        Map<String, String> collect = algorithmService.getAllAlgorithms().stream()
            .collect(Collectors.toMap(t -> t.getCode(), t -> t.getId()));
        BaseAreaDO baseAreaDO = baseAreaDAO.findById(areaId);
        String areaName = baseAreaDO.getName();
        AreaForecastAlgorithmParam areaForecastAlgorithmParam = new AreaForecastAlgorithmParam(areaName,startDate,endDate,new String[]{areaId,caliberId}
        ,forecastType,pointNum,type,fcDayWeatherType);
        areaForecastAlgorithmParam.setAreaId(areaId);
        areaForecastAlgorithmParam.setCaliberId(caliberId);
        areaForecastAlgorithmParam.setAlgorithmEnums(algorithmEnums);
        try {
            ResultList<ShortForecastResult> resultList = abstractShortForecastInvokeClient.invoke(areaForecastAlgorithmParam);

            List<ShortForecastResult> forecastResultList = resultList.getForecastResultList();

            for (ShortForecastResult shortForecastResult : forecastResultList) {
                AlgorithmEnum algorithmEnum = shortForecastResult.getAlgorithmEnum();
                String algorithmEnumId = algorithmEnum.getId();
                List<ShortForecastMetaData> shortForecastMetaData = shortForecastResult.getShortForecastMetaData();
                for (ShortForecastMetaData shortForecastMetaDatum : shortForecastMetaData) {
                    Date date = shortForecastMetaDatum.getDate();
                    List<BigDecimal> value = shortForecastMetaDatum.getValue();
                    Map<String, BigDecimal> valueMap = ColumnUtil
                        .listToMap(value, Constants.LOAD_CURVE_START_WITH_ZERO);
                    LoadAreaFcDO loadAreaFcDO = new LoadAreaFcDO();
                    BasePeriodUtils.setAllFiled(loadAreaFcDO,valueMap);
                    loadAreaFcDO.setAlgorithmId(collect.get(algorithmEnumId));
                    loadAreaFcDO.setDate(new java.sql.Date(date.getTime()));
                    loadAreaFcDO.setAreaId(areaId);
                    loadAreaFcDO.setCaliberId(caliberId);
                    loadAreaFcService.doInsertOrUpdate(loadAreaFcDO);
                }
            }

        }catch (Exception e){
            e.printStackTrace();
        }

    }

    private List<AlgorithmEnum> getAlgorithmEnums(List<String> algorithmIds) {
        Map<String, AlgorithmDO> collect = algorithmService.getAllAlgorithms().stream()
            .collect(Collectors.toMap(t -> t.getId(), t -> t));
        if (CollectionUtils.isEmpty(algorithmIds)){
            return null;
        }
        List<AlgorithmEnum> enums = new ArrayList<>();
            algorithmIds.forEach(e -> {
                AlgorithmDO algorithmDO = collect.get(e);
                enums.add(AlgorithmEnum.findById(algorithmDO.getCode()));
            });
        return enums;
    }

}