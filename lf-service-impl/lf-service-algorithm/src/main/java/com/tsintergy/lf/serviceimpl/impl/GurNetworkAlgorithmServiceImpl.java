package com.tsintergy.lf.serviceimpl.impl;

import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.aif.algorithm.serviceapi.base.dto.AlgorithmEnum;
import com.tsintergy.aif.algorithm.serviceapi.base.dto.shortforecast.ShortForecastMetaData;
import com.tsintergy.aif.algorithm.serviceapi.base.dto.shortforecast.ShortForecastResult;
import com.tsintergy.aif.algorithm.serviceimpl.client.support.GurNetworkInputHelper;
import com.tsintergy.lf.core.constants.CityConstants;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.constants.SystemConstant;
import com.tsintergy.lf.core.enums.WeatherSourceEnum;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.serviceapi.algorithm.api.GurNetworkAlgorithmService;
import com.tsintergy.lf.serviceapi.algorithm.dto.GurNetworkAlgorithmParam;
import com.tsintergy.lf.serviceapi.base.base.api.CaliberService;
import com.tsintergy.lf.serviceapi.base.base.api.HolidayService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CaliberDO;
import com.tsintergy.lf.serviceapi.base.forecast.api.*;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.AlgorithmDO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.AlgorithmParamDO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.CityExtraAlgorithmRunConfigDO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.recall.api.LoadCityFcRecallService;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2023-06-20
 * @since 1.0.0
 */
@Slf4j
@Service("gurNetworkAlgorithmService")
public class GurNetworkAlgorithmServiceImpl implements GurNetworkAlgorithmService {

    @Autowired
    private LoadCityFcBatchService loadCityFcBatchService;
    @Autowired
    private GurNetworkAlgorithmClient gurNetworkAlgorithmClient;
    @Autowired
    private CaliberService caliberService;
    @Autowired
    private AlgorithmParamService algorithmParamService;
    @Autowired
    private AlgorithmService algorithmService;
    @Autowired
    private LoadCityFcService loadCityFcService;

    @Autowired
    private SettingSystemService systemService;

    @Autowired
    private HolidayService holidayService;

    @Autowired
    private LoadCityFcRecallService loadCityFcRecallService;

    @Autowired
    private CityExtraAlgorithmRunConfigService cityExtraAlgorithmRunConfigService;


    @Override
    public void doGurNetworkForecast(Date startDate, Date endDate, String caliberId, String cityId, Integer pointNum,
                                     Integer weather, Integer type, Integer apparentType) {
        String gurId = Constants.GUR_ID;
        CaliberDO caliberDO = caliberService.findCaliberDOByPk(caliberId);
        GurNetworkAlgorithmParam gurNetworkAlgorithmParam = new GurNetworkAlgorithmParam(caliberDO.getName(), startDate, endDate, new String[]{caliberId});
        gurNetworkAlgorithmParam.setCaliberName(caliberDO.getName());
        gurNetworkAlgorithmParam.setCityId(Constants.PROVINCE_ID);
        gurNetworkAlgorithmParam.setCaliberId(caliberDO.getId());
        if (pointNum != null) {
            gurNetworkAlgorithmParam.setPoint(pointNum);
        }
        if (weather != null) {
            gurNetworkAlgorithmParam.setFcDayWeatherType(weather);
        }
        if (type != null) {
            gurNetworkAlgorithmParam.setFcType(type);
        }
        try {
            //算法参数列表
            List<AlgorithmParamDO> algorithmParam = algorithmParamService.getAlgorithmParamByAlgorithmIdNoCache(
                AlgorithmEnum.GUR_NETWORK.getId());
            Map<String, String> paramMap = algorithmParam.stream().collect(
                Collectors.toMap(AlgorithmParamDO::getParamEn, AlgorithmParamDO::getDefaultValue));
            mergeAlgorithmParam(paramMap,gurNetworkAlgorithmParam);
            //使用的城市id 用逗号隔开
            String userCity = cityId;
            gurNetworkAlgorithmParam.setAlgorithmEnums(Arrays.asList(AlgorithmEnum.GUR_NETWORK_CITY));
            gurNetworkAlgorithmParam.setAlgorithmEnum((AlgorithmEnum.GUR_NETWORK_CITY));
            if (Constants.PROVINCE_ID.equals(cityId)) {
                userCity = paramMap.get("UserCity");
                gurNetworkAlgorithmParam.setAlgorithmEnums(Arrays.asList(AlgorithmEnum.GUR_NETWORK));
                gurNetworkAlgorithmParam.setAlgorithmEnum((AlgorithmEnum.GUR_NETWORK));
            }
            gurNetworkAlgorithmParam.setUserCityList(Arrays.asList(userCity.split(",")));
            gurNetworkAlgorithmParam.setCityId(cityId);
            String[] distinguish = {cityId};
            gurNetworkAlgorithmParam.setDistinguishParams(distinguish);
            if (apparentType != null) {
                // 体感温度
                gurId = Constants.GUR_APPARENT_ID;
                gurNetworkAlgorithmParam.setFinetuningLr("0.001");
                gurNetworkAlgorithmParam.setAlgorithmEnums(Arrays.asList(AlgorithmEnum.GUR_NETWORK_APPARENT));
                gurNetworkAlgorithmParam.setAlgorithmEnum((AlgorithmEnum.GUR_NETWORK_APPARENT));

            }
            ShortForecastResult forecastResult = gurNetworkAlgorithmClient.invoke(gurNetworkAlgorithmParam);
            this.saveResult(forecastResult, cityId, caliberId, gurId, null);
            //判断是否需要使用多气象源再次运行
            this.reRunWithMultiWeatherSource(gurNetworkAlgorithmParam, gurId);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    @Override
    @SneakyThrows
    public void saveResult(ShortForecastResult forecastResult, String cityId, String caliberId, String gurId, Date startDate) {
        List<ShortForecastMetaData> forecastResultList = forecastResult.getShortForecastMetaData();
        List<Date> holidays = holidayService.getAllHolidays();
        for (ShortForecastMetaData shortForecastMetaData : forecastResultList) {
            Date date = shortForecastMetaData.getDate();
            List<BigDecimal> value = shortForecastMetaData.getValue();
            Map<String, BigDecimal> valueMap = ColumnUtil
                    .listToMap(value, Constants.LOAD_CURVE_START_WITH_ZERO);
            LoadCityFcDO loadCityFcDO = new LoadCityFcDO();
            BasePeriodUtils.setAllFiled(loadCityFcDO, valueMap);
            loadCityFcDO.setAlgorithmId(gurId);
            loadCityFcDO.setDate(new java.sql.Date(date.getTime()));
            loadCityFcDO.setCityId(cityId);
            loadCityFcDO.setCaliberId(caliberId);
            List<LoadCityFcDO> reportLoadFc = loadCityFcService.findReportLoadFc(cityId, caliberId, date, date);
            // 考虑节假日和非节假日的默认上报
            String systemAlgorithmId = "";
            String[] split = null;
            if (holidays.contains(date)) {
                // 查询节假日默认上报算法
                split = systemService.findByFieldId(SystemConstant.HOLIDAY_ALGORITHM).getValue()
                        .split(Constants.SEPARATOR_PUNCTUATION);
            } else {
                // 查询正常日默认上报算法
                split = systemService.findByFieldId(SystemConstant.NORMAL_ALGORITHM).getValue()
                        .split(Constants.SEPARATOR_PUNCTUATION);
            }
            if (cityId.equals(CityConstants.PROVINCE_ID)) {
                systemAlgorithmId = split[0];
            } else {
                systemAlgorithmId = split[1];
            }
            if (gurId.equals(systemAlgorithmId)) {
                if (reportLoadFc.size() < 1) {
                    // 没有任何上报过
                    loadCityFcDO.setReport(true);
                    loadCityFcDO.setReportTime(new Timestamp(System.currentTimeMillis()));
                    loadCityFcDO.setSucceed(true);
                } else {
                    if (reportLoadFc.size() > 0 && reportLoadFc.get(0).getAlgorithmId()
                            .equals(systemAlgorithmId)) {
                        // 自己上报过
                        loadCityFcDO.setReport(true);
                        loadCityFcDO.setReportTime(new Timestamp(System.currentTimeMillis()));
                        loadCityFcDO.setSucceed(true);
                    } else if (reportLoadFc.size() > 0 && !reportLoadFc.get(0).getAlgorithmId()
                            .equals(systemAlgorithmId) && !reportLoadFc.get(0).getAlgorithmId()
                            .equals("0")) {
                        // 除人工和自己算法上报之外的上报（取消之前的上报，然后上报自己）
                        this.cancelReport(reportLoadFc);
                        loadCityFcDO.setReport(true);
                        loadCityFcDO.setReportTime(new Timestamp(System.currentTimeMillis()));
                        loadCityFcDO.setSucceed(true);
                    }
                }
            }
            //算法结果中的date必须在startDate之后才保存
            if (startDate != null && date.before(startDate)) {
                continue;
            }
            loadCityFcService.doSaveOrUpdateLoadCityFcDO96(loadCityFcDO);
            loadCityFcBatchService.doSave(loadCityFcDO);
        }
    }

    @SneakyThrows
    private void reRunWithMultiWeatherSource(GurNetworkAlgorithmParam param, String gruId) {
        AlgorithmDO algorithmDO = algorithmService.getAlgorithmDOById(gruId);
        CityExtraAlgorithmRunConfigDO extraRunConfigDO = cityExtraAlgorithmRunConfigService.findExtraRunConfigDOByType(algorithmDO.getCode());
        if (extraRunConfigDO == null) {
            return;
        }
        List<String> cityIds = Arrays.asList(extraRunConfigDO.getCityIds().split(","));
        if (cityIds.contains(param.getCityId())) {
            log.info("城市{},算法{}使用多气象源再次运行", param.getCityName(), gruId);
            param.setFcWeatherSource(WeatherSourceEnum.METEO.name());
            ShortForecastResult forecastResult = gurNetworkAlgorithmClient.invoke(param);
            saveResult(forecastResult, param.getCityId(), param.getCaliberId(), extraRunConfigDO.getAlgorithmId(), null);
        }
    }

    private void cancelReport(List<LoadCityFcDO> reportLoadFc) throws Exception {
        for (LoadCityFcDO loadCityFcDO : reportLoadFc) {
            loadCityFcDO.setRecommend(false);
            loadCityFcDO.setReport(false);
            loadCityFcDO.setSucceed(false);
            loadCityFcService.doSaveOrUpdateLoadCityFcDO96(loadCityFcDO);
            loadCityFcBatchService.doSave(loadCityFcDO);
        }
    }
    /**
     * 算法参数赋值
     * @param paramMap
     * @param gurNetworkAlgorithmParam
     */
    private void mergeAlgorithmParam(Map<String, String> paramMap, GurNetworkAlgorithmParam gurNetworkAlgorithmParam) {
        gurNetworkAlgorithmParam.setUseGPU(paramMap.get(GurNetworkInputHelper.USE_GPU));
        gurNetworkAlgorithmParam.setGPUCardMemory(paramMap.get(GurNetworkInputHelper.GPU_CARD_MEMORY));
        gurNetworkAlgorithmParam.setCPUThreads(paramMap.get(GurNetworkInputHelper.CPU_THREADS));
        gurNetworkAlgorithmParam.setTrainBeginDay(paramMap.get(GurNetworkInputHelper.TRAIN_BEGIN_DAY));
        gurNetworkAlgorithmParam.setWindowDate(paramMap.get(GurNetworkInputHelper.WINDOW_DATE));
        gurNetworkAlgorithmParam.setWindowTmp(paramMap.get(GurNetworkInputHelper.WINDOW_TMP));
        gurNetworkAlgorithmParam.setWindowLoad(paramMap.get(GurNetworkInputHelper.WINDOW_LOAD));
        gurNetworkAlgorithmParam.setBatchSize(paramMap.get(GurNetworkInputHelper.BATCH_SIZE));
        gurNetworkAlgorithmParam.setDecayInterval(paramMap.get(GurNetworkInputHelper.DECAY_INTERVAL));
        gurNetworkAlgorithmParam.setDecayRatio(paramMap.get(GurNetworkInputHelper.DECAY_RATIO));
        gurNetworkAlgorithmParam.setTrainingRounds(paramMap.get(GurNetworkInputHelper.TRAINING_ROUNDS));
        gurNetworkAlgorithmParam.setTrainingLr(paramMap.get(GurNetworkInputHelper.TRAINING_LR));
        gurNetworkAlgorithmParam.setFinetuningNumFreeLayer(paramMap.get(GurNetworkInputHelper.FINETUNING_NUM_FREE_LAYER));
        gurNetworkAlgorithmParam.setFinetuningRounds(paramMap.get(GurNetworkInputHelper.FINETUNING_ROUNDS));
        gurNetworkAlgorithmParam.setFinetuningNumSyn(paramMap.get(GurNetworkInputHelper.FINETUNING_NUM_SYN));
        gurNetworkAlgorithmParam.setFinetuningLr(paramMap.get(GurNetworkInputHelper.FINETUNING_LR));
        gurNetworkAlgorithmParam.setWindowWidth(paramMap.get(GurNetworkInputHelper.WINDOW_WIDTH));
    }
}
