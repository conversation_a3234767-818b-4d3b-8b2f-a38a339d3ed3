/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.impl;

import com.tsieframework.core.base.service.BaseServiceImpl;
import com.tsintergy.aif.algorithm.serviceapi.base.dto.AlgorithmEnum;
import com.tsintergy.aif.algorithm.serviceapi.base.dto.ResultList;
import com.tsintergy.aif.algorithm.serviceapi.base.dto.longforecast.LongForecastMetaData;
import com.tsintergy.aif.algorithm.serviceapi.base.dto.longforecast.LongForecastResult;
import com.tsintergy.aif.algorithm.serviceapi.base.dto.monthTendayForecast.MonthTendayForecastAlgorithmParam;
import com.tsintergy.aif.algorithm.serviceapi.base.dto.monthTendayForecast.MonthTendayForecastMetaData;
import com.tsintergy.aif.algorithm.serviceapi.base.dto.monthTendayForecast.MonthTendayForecastParam;
import com.tsintergy.aif.algorithm.serviceapi.base.dto.monthTendayForecast.MonthTendayForecastResult;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.algorithm.api.LongForecastAlgorithmService;
import com.tsintergy.lf.serviceapi.algorithm.api.MonthTendayForecastAlgorithmService;
import com.tsintergy.lf.serviceapi.algorithm.dto.LongForecastAlgorithmParam;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityFcLongService;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadHisDataDTO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityFcLongDO;
import com.tsintergy.lf.serviceimpl.common.util.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/5/13 16:26
 * @Version: 1.0.0
 */
@Service("monthTendayForecastAlgorithmService")
public class MonthTendayForecastAlgorithmServiceImpl extends BaseServiceImpl implements MonthTendayForecastAlgorithmService {


    @Autowired
    MonthTendayForecastInvokeClient monthTendayForecastInvokeClient;

    @Autowired
    CityService cityService;

    @Autowired
    LoadCityFcLongService loadCityFcLongService;

    @Override
    public void forecast(String caliberId, String cityId, Date startDate, Date endDate) throws Exception {

        try {
            long start = System.currentTimeMillis();

            CityDO cityDO = cityService.findCityById(cityId);
            MonthTendayForecastParam monthTendayForecastParam = new MonthTendayForecastParam(cityDO.getCity(),startDate,endDate,null, null, null, null);
            monthTendayForecastParam.setCaliberId(caliberId);
            monthTendayForecastParam.setCityId(cityId);
            String[] distinguish = {cityId,caliberId};
            monthTendayForecastParam.setDistinguishParams(distinguish);

            List<String> hisColumnNameList = Arrays.asList("MAX_LOAD", "MIN_LOAD", "AVE_LOAD", "ENERGY");
            String factorNameStr = "day_mean_风速_xun_mean  day_max_风速_xun_max  day_max_风速_xun_mean  day_min_风速_xun_mean  day_min_风速_xun_min  day_mean_降雨_xun_mean  day_mean_湿度_xun_mean  day_max_湿度_xun_max  day_max_湿度_xun_mean  day_min_湿度_xun_mean  day_min_湿度_xun_min  day_mean_温度_xun_mean  day_max_温度_xun_max  day_max_温度_xun_mean  day_min_温度_xun_mean  day_min_温度_xun_min";
            List<String> factorNameList = Arrays.asList(factorNameStr.split("  "));
            List<String> yearFactorNameList = Arrays.asList("gdp", "fst_crop_add_val", "sct_consum_pdt_retail_tol_amt");
            List<List<String>> tradeNameList = Arrays.asList(hisColumnNameList, factorNameList, yearFactorNameList);
            monthTendayForecastParam.setTradeNameLists(tradeNameList);

            MonthTendayForecastResult tendayForecastResult = monthTendayForecastInvokeClient.invoke(monthTendayForecastParam);

            List<MonthTendayForecastMetaData> tendayForecastMetaDataList = tendayForecastResult.getMonthTendayForecastMetaDatas();
            for (MonthTendayForecastMetaData tendayForecastMetaData : tendayForecastMetaDataList) {
                LoadCityFcLongDO loadCityFcLongDO = new LoadCityFcLongDO();
                loadCityFcLongDO.setMaxLoad(tendayForecastMetaData.getValue().get(0));
                loadCityFcLongDO.setMinLoad(tendayForecastMetaData.getValue().get(1));
                loadCityFcLongDO.setMeanLoad(tendayForecastMetaData.getValue().get(2));
                loadCityFcLongDO.setEleLoad(tendayForecastMetaData.getValue().get(3));

                loadCityFcLongDO.setDate(new java.sql.Date(DateUtils.getDateFromString(tendayForecastMetaData.getDate(), DateUtils.DATE_FORMAT2).getTime()));
                loadCityFcLongDO.setCityId(cityId);
                loadCityFcLongDO.setCaliberId(caliberId);
                loadCityFcLongDO.setAlgorithmId(AlgorithmEnum.MONTH_TENDAY_FORECAST.getType());
                loadCityFcLongService.saveOrUpdate(loadCityFcLongDO);
            }

            long end = System.currentTimeMillis();
            System.out.println(end-start);
        } catch (Exception e) {
           throw e;
        }
    }
}