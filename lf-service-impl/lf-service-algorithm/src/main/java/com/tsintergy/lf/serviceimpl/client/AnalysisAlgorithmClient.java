/**
 * Copyright (C), 2015‐2021, 北京清能互联科技有限公司 Author:  wang<PERSON>@tsintergy.com Date:  2021/12/8 9:54 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceimpl.client;

import com.tsintergy.algorithm.alginv.core.AlginvConfigConstants;
import com.tsintergy.algorithm.alginv.core.configure.properties.AlginvProperties;
import com.tsintergy.algorithm.alginv.core.console.ConsoleCmd;
import com.tsintergy.algorithm.alginv.core.invoker.utils.PathUtils;
import com.tsintergy.algorithm.alginv.serviceapi.client.api.AlgorithmQueryFacadeService;
import com.tsintergy.algorithm.alginv.serviceimpl.client.AlgorithmClient;
import com.tsintergy.algorithm.alginv.serviceimpl.client.invoker.ClientAlgorithmInvoker;
import com.tsintergy.algorithm.alginv.serviceimpl.client.parameter.ClientInvokeParameters;
import com.tsintergy.lf.serviceimpl.support.AnalysisCmdParser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Description:  <br> aif项目的算法客户端，重写不兼容的parseCmd方法
 *
 * <AUTHOR>
 * @create 2021/12/8
 * @since 1.0.0
 */
@Component
public class AnalysisAlgorithmClient extends AlgorithmClient {

    @Autowired
    private  AlginvProperties properties;

    @Autowired
    private AnalysisCmdParser analysisCmdParser;

    public AnalysisAlgorithmClient(
        ClientAlgorithmInvoker clientAlgorithmInvoker,
        AlginvProperties properties,
        AlgorithmQueryFacadeService algorithmQueryFacadeService) {
        super(clientAlgorithmInvoker, properties, algorithmQueryFacadeService);
    }


    @Override
    protected ConsoleCmd parseCmd(ClientInvokeParameters clientParameters) {
        return analysisCmdParser.parse(
            PathUtils.resolvePath(AlginvConfigConstants.VAR_EXE_BASE_DIR, clientParameters.getAlgorithmExeName()),
            clientParameters.getOptions());
    }

}