package com.tsintergy.lf.serviceimpl.executor.analysis;

import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.util.edom.efile.ETable;
import com.tsintergy.algorithm.alginv.core.configure.properties.AlginvProperties;
import com.tsintergy.algorithm.alginv.serviceimpl.client.AlgorithmClient;
import com.tsintergy.algorithm.alginv.serviceimpl.client.parameter.ClientInvokeParameters;
import com.tsintergy.algorithm.alginv.serviceimpl.client.parameter.SimpleClientInvokeParametersBuilder;
import com.tsintergy.lf.core.constants.AlgorithmConstants;
import com.tsintergy.lf.serviceapi.algorithm.dto.SimilarDateBean;
import com.tsintergy.lf.serviceapi.algorithm.dto.SimilarDayParam;
import com.tsintergy.lf.serviceapi.algorithm.dto.SimilarResult;
import com.tsintergy.lf.serviceimpl.executor.AbstractBaseExecutor;
import com.tsintergy.lf.serviceimpl.support.ALgorithmSupport;
import com.tsintergy.lf.serviceimpl.support.FilePathSupport;
import com.tsintergy.lf.serviceimpl.support.FreeMarkerConfig;
import com.tsintergy.lf.serviceimpl.support.FreemarkUtils;
import freemarker.template.Configuration;
import freemarker.template.Template;
import java.io.File;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.io.Writer;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2023-05-05
 * @since 1.0.0
 */
@Slf4j
@Component("similarDayQueryExecutor")
public class SimilarDayQueryExecutor extends AbstractBaseExecutor<SimilarDayParam, SimilarResult> {

    /**
     * 控制参数
     * 搜索起始日
     */
    private static final String SEARCH_BEGIN_DAY = "SearchBeginDay";

    /**
     * 搜索结束日
     */
    private static final String SEARCH_END_DAY = "SearchEndDay";

    /**
     * 查找当日
     */
    private static final String SEARCH_DAY = "SearchDay";

    /**
     * 搜索起始时刻
     */
    private static final String SEARCH_BEGIN_POINT = "SearchBeginPoint";

    /**
     * 搜索结束时刻
     */
    private static final String SEARCH_END_POINT = "SearchEndPoint";

    /**
     * 日期类型
     */
    private static final String SEARCH_DATE_TYPE = "SearchDateType";

    /**
     * 连续天数
     */
    private static final String CONSECUTIVE_DAYS = "ConsecutiveDays";

    /**
     * 指标条件
     */
    private static final String INDICATOR_CONDITION_NAME = "IndicatorConditionName";

    /**
     * 指标条件最小值
     */
    private static final String INDICATOR_CONDITION_LOW = "IndicatorConditionLow";

    /**
     * 指标条件最大值
     */
    private static final String INDICATOR_CONDITION_UP = "IndicatorConditionUp";

    /**
     * 最大结果数量
     */
    private static final String MAX_RESULT_NUM = "MaxResultNum";

    /**
     * 排除相似度小于
     */
    private static final String EXCLUDE_SIMILARITY_LESS_THAN = "ExcludeSimilarityLessThan";

    /**
     * 特征选择参数
     * 日最高温度
     */
    private static final String DAY_MAX_TEMPERATURE = "DayMaxTemperature";

    /**
     * 日最低温度
     */
    private static final String DAY_MIN_TEMPERATURE = "DayMinTemperature";

    /**
     * 日平均温度
     */
    private static final String DAY_AVERAGE_TEMPERATURE = "DayAverageTemperature";

    /**
     * 累积平均温度
     */
    private static final String CUMULATIVE_AVERAGE_TEMPERATURE = "CumulativeAverageTemperature";

    /**
     * 日最高体感温度
     */
    private static final String DAY_MAX_APPARENT_TEMPERATURE = "DayMaxApparentTemperature";

    /**
     * 日最低体感温度
     */
    private static final String DAY_MIN_APPARENT_TEMPERATURE = "DayMinApparentTemperature";

    /**
     * 日平均体感温度
     */
    private static final String DAY_AVERAGE_APPARENT_TEMPERATURE = "DayAverageApparentTemperature";

    /**
     * 累积平均体感温度
     */
    private static final String CUMULATIVE_AVERAGE_APPARENT_TEMPERATURE = "CumulativeAverageApparentTemperature";

    /**
     * 日平均湿度
     */
    private static final String DAY_AVERAGE_HUMIDITY = "DayAverageHumidity";

    /**
     * 累积降水
     */
    private static final String CUMULATIVE_PRECIPITATION = "CumulativePrecipitation";

    /**
     * 日最高负荷
     */
    private static final String DAY_MAX_LOAD = "DayMaxLoad";

    /**
     * 日最低负荷
     */
    private static final String DAY_MIN_LOAD = "DayMinLoad";

    /**
     * 日平均负荷
     */
    private static final String DAY_AVERAGE_LOAD = "DayAverageLoad";

    /**
     * 峰谷差
     */
    private static final String PEAK_VALLEY_DIFFERENCE = "PeakValleyDifference";

    /**
     * 温度曲线
     */
    private static final String TEMPERATURE_CURVE = "TemperatureCurve";

    /**
     * 湿度曲线
     */
    private static final String HUMIDITY_CURVE = "HumidityCurve";

    /**
     * 降水曲线
     */
    private static final String PRECIPITATION_CURVE = "PrecipitationCurve";

    /**
     * 体感温度曲线
     */
    private static final String APPARENT_CURVE = "ApparentCurve";

    /**
     * 风速曲线
     */
    private static final String WIND_CURVE = "WindCurve";

    /**
     * 负荷曲线
     */
    private static final String DAY_LOAD_CURVE = "DayLoadCurve";

    @Autowired
    private AlginvProperties alginvProperties;

    @Autowired
    private AlgorithmClient algorithmClient;

    @Override
    public void execute(SimilarDayParam param) throws BusinessException, IOException, InterruptedException {
        SimpleClientInvokeParametersBuilder simpleClientInvokeParametersBuilder = ClientInvokeParameters
            .compatibleBuilder();
        String outPath = FilePathSupport.getInAbsoluteFile(param, param.getAlgorithmDetail().getOut());
        ClientInvokeParameters parameters =
            simpleClientInvokeParametersBuilder.caseId(ALgorithmSupport.getUUID())
                .actionId(ALgorithmSupport.getUUID())
                .algorithmExeName(FilePathSupport.getBasePath(param.getAlgorithmDetail().getRun()) + FilePathSupport
                    .getBasePath(param.getAlgorithmDetail().getExeName()))
                .caseInputDir(FilePathSupport.getInAbsoluteFile(param, param.getAlgorithmDetail().getIn()))
                .caseOutputDir(outPath)
                .algorithmType(param.getAlgorithmEnum().getType())
                .ignoreConsoleResult(true)
                //实时返回时设置成同步
                .async(param.getAlgorithmDetail().getAsync())
                .build();
        Map<String, String> map = new HashMap<>(16);
        //补充cmd命令：装载算法授权文件所在路径
        fillCmdOptio(param, simpleClientInvokeParametersBuilder);
        //装载算法回调需要的补充参数；
        fillBackParam(param, map);
        parameters.setParameters(map);
        ALgorithmSupport.mkdir(alginvProperties.getCaseBaseDir() + outPath);
        algorithmClient.invoke(parameters);
    }

    @Override
    public void preprocess(SimilarDayParam param, Map<String, Object> datas) throws BusinessException {
        Map<String, Object> map = new HashMap<>(16);
        try {
            //1.将ipml中查询的数据与file in模板中的标签一一对应后放入map
            writeData(param, datas, map);
            //2.把处理过的map数据写入file in文件
            writeFile(param, param.getAlgorithmDetail().getIn(), param.getAlgorithmDetail().getTemplateName(), map);
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException("算法输入文件写入错误", e);
        }
    }

    @Override
    @SuppressWarnings(value = "all")
    public void writeData(SimilarDayParam param, Map<String, Object> datas, Map<String, Object> map) throws Exception {
        //控制参数填充
        map.put(SEARCH_BEGIN_DAY,
            DateUtils.date2String(param.getStartDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR));
        map.put(SEARCH_END_DAY,
            DateUtils.date2String(param.getEndDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR));
        map.put(SEARCH_DAY, DateUtils.date2String(param.getTargetDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR));
        map.put(SEARCH_BEGIN_POINT, param.getStartTime());
        map.put(SEARCH_END_POINT, param.getEndTime());
        List<String> dateTypeList = param.getDateType();
        StringBuilder dateType = new StringBuilder();
        for (int i = 0; i < dateTypeList.size(); i++) {
            String s = dateTypeList.get(i);
            if (s.equals("workDay")){
                dateType.append("WeekDay");
            } else if (s.equals("restDay")){
                dateType.append("RestDay");
            } else if (s.equals("holiday")){
                dateType.append("Holiday");
            }
            if (i != (dateTypeList.size() - 1)){
                dateType.append("-");
            }
        }
        map.put(SEARCH_DATE_TYPE, dateType.toString());
        map.put(MAX_RESULT_NUM, param.getMaxResultCount());
        map.put(EXCLUDE_SIMILARITY_LESS_THAN, param.getExclude());

        //FeatureSelectParameter 参数填充
        defaultParam(datas, map, 0);
        String condition = param.getCondition();
        String method = param.getMethod();
        //气象变化
        if ("1".equals(condition)){
            if ("1".equals(method)){
                List<String> weatherFeatureList = param.getWeatherFeatureList();
                if (!CollectionUtils.isEmpty(weatherFeatureList)){
                    map.put(DAY_MAX_TEMPERATURE, weatherFeatureList.contains("highestTemp")? 1 : 0);
                    map.put(DAY_MIN_TEMPERATURE, weatherFeatureList.contains("lowestTemp")? 1 : 0);
                    map.put(DAY_AVERAGE_TEMPERATURE, weatherFeatureList.contains("avgTemp")? 1 : 0);
                    map.put(CUMULATIVE_AVERAGE_TEMPERATURE, weatherFeatureList.contains("sumAvgTemp")? 1 : 0);
                    map.put(DAY_MAX_APPARENT_TEMPERATURE, weatherFeatureList.contains("highestBodyTemp")? 1 : 0);
                    map.put(DAY_MIN_APPARENT_TEMPERATURE, weatherFeatureList.contains("lowestBodyTemp")? 1 : 0);
                    map.put(DAY_AVERAGE_APPARENT_TEMPERATURE, weatherFeatureList.contains("avgBodyTemp")? 1 : 0);
                    map.put(CUMULATIVE_AVERAGE_APPARENT_TEMPERATURE, weatherFeatureList.contains("sumAvgBodyTemp")? 1 : 0);
                    map.put(DAY_AVERAGE_HUMIDITY, weatherFeatureList.contains("avgHumidity")? 1 : 0);
                    map.put(CUMULATIVE_PRECIPITATION, weatherFeatureList.contains("sumRain")? 1 : 0);
                }
                if (StringUtils.isNotBlank(param.getChangeDays())){
                    map.put(CONSECUTIVE_DAYS, param.getChangeDays());
                }
                String weatherRange = param.getWeatherRange();
                if (!StringUtils.isBlank(weatherRange)){
                    if ("1".equals(weatherRange)){
                        map.put(INDICATOR_CONDITION_NAME, "DayMaxTemperature");
                    }else {
                        map.put(INDICATOR_CONDITION_NAME, "DayMinTemperature");
                    }
                    if (!Objects.isNull(param.getRangeStart()) && !Objects.isNull(param.getRangeEnd())){
                        map.put(INDICATOR_CONDITION_LOW, param.getRangeStart());
                        map.put(INDICATOR_CONDITION_UP, param.getRangeEnd());
                    }
                }
            }else {
                List<String> weatherCurveList = param.getWeatherCurveList();
                if (!CollectionUtils.isEmpty(weatherCurveList)){
                    map.put(TEMPERATURE_CURVE, weatherCurveList.contains("temperature")? 1 : 0);
                    map.put(HUMIDITY_CURVE, weatherCurveList.contains("humidity")? 1 : 0);
                    map.put(PRECIPITATION_CURVE, weatherCurveList.contains("rain")? 1 : 0);
                    map.put(WIND_CURVE, weatherCurveList.contains("windSpeed")? 1 : 0);
                    map.put(APPARENT_CURVE, weatherCurveList.contains("bodyTemp")? 1 : 0);
                }
            }
        } else if ("2".equals(condition)) {
            //负荷变化
            if ("1".equals(method)){
                List<String> loadList = param.getLoadList();
                if (!CollectionUtils.isEmpty(loadList)){
                    map.put(DAY_MAX_LOAD, loadList.contains("maxLoad")? 1 : 0);
                    map.put(DAY_MIN_LOAD, loadList.contains("minLoad")? 1 : 0);
                    map.put(DAY_AVERAGE_LOAD, loadList.contains("avgLoad")? 1 : 0);
                    map.put(PEAK_VALLEY_DIFFERENCE, loadList.contains("diff")? 1 : 0);
                }
                if (StringUtils.isNotBlank(param.getChangeDays())){
                    map.put(CONSECUTIVE_DAYS, param.getChangeDays());
                }
            }else {
                map.put(DAY_LOAD_CURVE, 1);
            }
        } else {
            //自定义 默认走负荷曲线
            List<String> dateList = (List<String>) datas.get(AlgorithmConstants.CUSTOMIZED_DATE);
            if (!CollectionUtils.isEmpty(dateList)){
                map.put(AlgorithmConstants.CUSTOMIZED_DATE, dateList);
            }
            map.put(DAY_LOAD_CURVE, 1);
        }
    }


    /**
     * step1.2 生成file in 文件
     *
     * @param param 算法参数
     * @param inPath in路径
     * @param template 算法template名
     * @param map 数据map
     */
    public void writeFile(SimilarDayParam param, String inPath, String template, Map<String, Object> map) throws Exception {
        String filePath;
        filePath = alginvProperties.getCaseBaseDir() + FilePathSupport.getInAbsoluteFile(param, inPath);

        FreeMarkerConfig freeMarkerConfig = new FreeMarkerConfig();
        // 设置模板所在的根路径
        String fmkpath = "freemarker" + "/" + template;
        //设置输出文件生成路径
        freeMarkerConfig.setOutPutFilePath(filePath);
        Configuration cfg = new Configuration();
        // 设置FreeMarker的模版文件位置
        cfg.setClassForTemplateLoading(FreemarkUtils.class, fmkpath);
        process(freeMarkerConfig, map,cfg);
    }

    public void process(FreeMarkerConfig freeMarkerConfig, Map<String, Object> outResult,Configuration cfg) throws Exception {
        Template template = null;
        String outFileName = null;
        //获取模板
        template = cfg.getTemplate("FILE_IN.e.ftl");
        outFileName = template.getName();
        outFileName = outFileName.replace(".ftl", "");
        buildTemplate(outResult, outFileName, template, freeMarkerConfig);
    }

    public void buildTemplate(Map root, String outFileName, Template template, FreeMarkerConfig freeMarkerConfig) throws Exception {
        String saveFile = freeMarkerConfig.getOutPutFilePath() + File.separator + outFileName;
        File newsDir = new File(freeMarkerConfig.getOutPutFilePath());
        if (!newsDir.exists()) {
            newsDir.mkdirs();
        }
        Writer out = null;
        try {
            // SYSTEM_ENCODING = "UTF-8";设置编码格式为GB2312
            out = new OutputStreamWriter(Files.newOutputStream(Paths.get(saveFile)), freeMarkerConfig.getEncoding());
            template.process(root, out);
            out.close();
        } catch (Exception e) {
            throw TsieExceptionUtils.newBusinessException("freemarker generate file fail", e);
        }
    }

    public void defaultParam(Map<String, Object> datas, Map<String, Object> map, Integer num){
        map.put(CONSECUTIVE_DAYS, 1);
        map.put(INDICATOR_CONDITION_NAME, "DayMaxTemperature");
        map.put(INDICATOR_CONDITION_LOW, "-100");
        map.put(INDICATOR_CONDITION_UP, "100");
        //气象变化 气象特性指标
        map.put(DAY_MAX_TEMPERATURE, num);
        map.put(DAY_MIN_TEMPERATURE, num);
        map.put(DAY_AVERAGE_TEMPERATURE, num);
        map.put(CUMULATIVE_AVERAGE_TEMPERATURE, num);
        map.put(DAY_MAX_APPARENT_TEMPERATURE, num);
        map.put(DAY_MIN_APPARENT_TEMPERATURE, num);
        map.put(DAY_AVERAGE_APPARENT_TEMPERATURE, num);
        map.put(CUMULATIVE_AVERAGE_APPARENT_TEMPERATURE, num);
        map.put(DAY_AVERAGE_HUMIDITY, num);
        map.put(CUMULATIVE_PRECIPITATION, num);
        //气象变化 气象特性曲线
        map.put(TEMPERATURE_CURVE, num);
        map.put(HUMIDITY_CURVE, num);
        map.put(PRECIPITATION_CURVE, num);
        map.put(WIND_CURVE, num);
        map.put(APPARENT_CURVE, num);
        //负荷变化 负荷特性指标
        map.put(DAY_MAX_LOAD, num);
        map.put(DAY_MIN_LOAD, num);
        map.put(DAY_AVERAGE_LOAD, num);
        map.put(PEAK_VALLEY_DIFFERENCE, num);
        //负荷变化 负荷曲线
        map.put(DAY_LOAD_CURVE, num);
        //自定义

        //历史数据
        List<String> list = new ArrayList<>();
        map.put(AlgorithmConstants.HOLIDAY_INFO, datas.get(AlgorithmConstants.HOLIDAY_INFO));
        map.put(AlgorithmConstants.ADJUSTED_WORKDAY, datas.get(AlgorithmConstants.ADJUSTED_WORKDAY));
        map.put(AlgorithmConstants.CUSTOMIZED_DATE, list);
        map.put(AlgorithmConstants.HISTORY_LOAD, datas.get(AlgorithmConstants.HISTORY_LOAD));
        map.put(AlgorithmConstants.HUMIDITY, datas.get(AlgorithmConstants.HUMIDITY));
        map.put(AlgorithmConstants.TEMPERATURE, datas.get(AlgorithmConstants.TEMPERATURE));
        map.put(AlgorithmConstants.APPARENT, datas.get(AlgorithmConstants.APPARENT));
        map.put(AlgorithmConstants.PRECIPITATION, datas.get(AlgorithmConstants.PRECIPITATION));
        map.put(AlgorithmConstants.WIND, datas.get(AlgorithmConstants.WIND));

    }

    /**
     * 解析E文件为result对象
     *
     * @param eTables E文件list
     */
    public static  SimilarResult parseSimilar(List<ETable> eTables) {
        SimilarResult result = new SimilarResult();
        for (ETable table : eTables) {
            List<SimilarDateBean> similarDateBeans = new ArrayList<>();
            List<Object[]> similarData = table.getDatas();
            for (Object[] similarDatum : similarData) {
                SimilarDateBean similarDateBean = new SimilarDateBean();
                similarDateBean
                    .setDate(DateUtils.string2Date((String) similarDatum[0], DateFormatType.SIMPLE_DATE_FORMAT_STR));
                similarDateBean.setDegree(new BigDecimal((String) similarDatum[1]));
                similarDateBeans.add(similarDateBean);
            }
            result.setSimilarDatas(similarDateBeans);
        }
        return result;
    }
}
