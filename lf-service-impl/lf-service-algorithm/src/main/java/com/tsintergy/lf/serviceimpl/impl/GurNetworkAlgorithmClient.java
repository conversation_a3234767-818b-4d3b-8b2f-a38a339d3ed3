package com.tsintergy.lf.serviceimpl.impl;

import com.tsintergy.aif.algorithm.serviceapi.base.dto.gurNetwork.GurNetworkBasicData;
import com.tsintergy.aif.algorithm.serviceapi.base.util.DateUtil;
import com.tsintergy.aif.algorithm.serviceimpl.client.facade.impl.AbstractGurNetworkInvokeClient;
import com.tsintergy.lf.serviceapi.algorithm.dto.GurNetworkAlgorithmParam;
import com.tsintergy.lf.serviceapi.algorithm.dto.WeatherDTOS;
import com.tsintergy.lf.serviceapi.base.base.api.HolidayService;
import com.tsintergy.lf.serviceapi.base.base.pojo.HolidayDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceimpl.support.AlgorithmDataSupport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2023-06-20
 * @since 1.0.0
 */
@Service("GurNetworkAlgorithmClient")
public class GurNetworkAlgorithmClient extends AbstractGurNetworkInvokeClient<GurNetworkAlgorithmParam> {


    @Autowired
    LoadCityHisService loadCityHisService;

    @Autowired
    AlgorithmDataSupport algorithmDataSupport;


    @Autowired
    HolidayService holidayService;

    @Autowired
    private SettingSystemService settingSystemService;

    @Override
    protected GurNetworkBasicData generateBasicData(GurNetworkAlgorithmParam param) {
        Date beginDate = param.getBeginDate();
        Date lastDate = param.getEndDate();

        //气象开始时间
        String trainBeginDay = param.getTrainBeginDay();
        Date trainBeginDate = DateUtil.getDate(trainBeginDay, "yyyy-MM-dd");
        //气象数据源
        // 区域id 代替城市id 查实际数据
        List<LoadCityHisDO> loadCityHisDOS = null;
        GurNetworkBasicData gurNetworkBasicData = null;
        try {
            loadCityHisDOS = loadCityHisService.getLoadCityHisDOS(param.getCityId(),param.getCaliberId(),trainBeginDate,lastDate);
            //封装区域预测和实际气象
            WeatherDTOS areaWeatherDTO = algorithmDataSupport
                    .getWeatherDTO(trainBeginDate, lastDate, param.getUserCityList(), param.getFinetuningLr(), param.getRecall(),
                            param.getFcWeatherSource(),param.getFcDayWeatherType(),null);
            List<HolidayDO> holidayDOS = holidayService.findHolidayVOS(null, null);
            gurNetworkBasicData = new GurNetworkBasicData(loadCityHisDOS, areaWeatherDTO.getHisHumiditys(),
                areaWeatherDTO.getFcHumiditys(), areaWeatherDTO.getHisTemperatures(), areaWeatherDTO.getFcTemperatures(),
                areaWeatherDTO.getHisPrecipitations(), areaWeatherDTO.getFcPrecipitations(), areaWeatherDTO.getHisWinds(),
                areaWeatherDTO.getFcWinds(),null,null,
                holidayDOS);
            gurNetworkBasicData.setDateNotInclude(settingSystemService.getNotIncludedDateList());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return gurNetworkBasicData;
    }

}
