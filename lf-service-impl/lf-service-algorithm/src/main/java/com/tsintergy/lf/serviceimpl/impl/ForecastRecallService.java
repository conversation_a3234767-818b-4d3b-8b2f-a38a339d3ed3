/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.impl;

import com.tsintergy.lf.core.constants.AlgorithmConstants;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.algorithm.api.CustomizationForecastService;
import com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum;
import com.tsintergy.lf.serviceapi.algorithm.dto.ForecastParam;
import com.tsintergy.lf.serviceapi.algorithm.dto.GeneralRecallResult;
import com.tsintergy.lf.serviceapi.algorithm.dto.GeneralResult;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Description: 预测回溯 <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/8/2 11:06
 * @Version: 1.0.0
 */

@Service("forecastRecallService")
public class ForecastRecallService extends AbstractForecastService<ForecastParam,GeneralRecallResult> {


    @Autowired
    private Forecastor forecastor;
    @Override
    public void forecast(ForecastParam param) throws Exception {
        param.setRecall(AlgorithmConstants.IS_RECALL);
        forecastor.forecast(param);
    }
}