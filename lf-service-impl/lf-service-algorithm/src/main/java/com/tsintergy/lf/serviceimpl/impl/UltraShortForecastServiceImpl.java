package com.tsintergy.lf.serviceimpl.impl;

import com.tsintergy.aif.algorithm.serviceapi.base.dto.ultraShort.UltraShortParam;
import com.tsintergy.aif.algorithm.serviceapi.base.dto.ultraShort.UltraShortResult;
import com.tsintergy.lf.serviceapi.algorithm.api.UltraGuangxiForecastService;
import com.tsintergy.lf.serviceimpl.client.UltraShortInvokeClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Description
 * @Date 2023/2/8 19:22
 * <AUTHOR>
 **/
@Service
public class UltraShortForecastServiceImpl implements UltraGuangxiForecastService {

    @Autowired
    UltraShortInvokeClient ultraGuangxiInvokeClient;

    @Override
    public UltraShortResult forecast(UltraShortParam ultraGuangxiParam) throws Exception {
        return ultraGuangxiInvokeClient.invoke(ultraGuangxiParam);
    }
}
