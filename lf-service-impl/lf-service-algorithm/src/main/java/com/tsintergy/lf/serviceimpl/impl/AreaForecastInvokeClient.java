/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.impl;

import com.tsintergy.aif.algorithm.serviceapi.base.dto.shortforecast.ShortForecastBasicData;
import com.tsintergy.aif.algorithm.serviceimpl.client.facade.impl.AbstractShortForecastInvokeClient;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.algorithm.dto.AreaForecastAlgorithmParam;
import com.tsintergy.lf.serviceapi.algorithm.dto.WeatherAreaDTO;
import com.tsintergy.lf.serviceapi.algorithm.dto.WeatherFeatureDTO;
import com.tsintergy.lf.serviceapi.base.area.api.LoadAreaHisService;
import com.tsintergy.lf.serviceapi.base.area.pojo.LoadAreaHisDO;
import com.tsintergy.lf.serviceapi.base.base.api.HolidayService;
import com.tsintergy.lf.serviceapi.base.base.pojo.HolidayDO;
import com.tsintergy.lf.serviceimpl.support.AlgorithmDataSupport;
import java.util.Date;
import java.util.List;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Description: 分区预测 <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/8/8 12:03
 * @Version: 1.0.0
 */
@Service("areaForecastInvokeClient")
public class AreaForecastInvokeClient extends AbstractShortForecastInvokeClient<AreaForecastAlgorithmParam> {

    @Autowired
    LoadAreaHisService loadAreaHisService;

    @Autowired
    AlgorithmDataSupport algorithmDataSupport;


    @Autowired
    HolidayService holidayService;

    @SneakyThrows
    @Override
    protected ShortForecastBasicData generateForecastBasicData(AreaForecastAlgorithmParam param) {

        Date beginDate = param.getBeginDate();
        Date startDate = DateUtil.getMoveDay(beginDate, -1200);
        Date lastDate = param.getEndDate();

        List<LoadAreaHisDO> loadCityHisDOS = loadAreaHisService
            .findLoadCityHisDOS(param.getAreaId(), startDate, lastDate, param.getCaliberId());


        //封装区域预测和实际气象
        WeatherAreaDTO areaWeatherDTO = algorithmDataSupport
            .getAreaWeatherDTO(startDate, lastDate, param.getAreaId());

        //封装区域预测和实际气象特性
        WeatherFeatureDTO areaWeatherFeatureDTO = algorithmDataSupport
            .getAreaWeatherFeatureDTO(startDate, lastDate, param.getAreaId());

        List<HolidayDO> holidayDOS = holidayService.findHolidayVOS(null,null);
        return new ShortForecastBasicData(loadCityHisDOS, areaWeatherDTO.getHisHumiditys(),
            areaWeatherDTO.getFcHumiditys(), areaWeatherDTO.getHisTemperatures(), areaWeatherDTO.getFcTemperatures(),
            areaWeatherDTO.getHisPrecipitations(), areaWeatherDTO.getFcPrecipitations(), areaWeatherDTO.getHisWinds(),
            areaWeatherDTO.getFcWinds(), areaWeatherFeatureDTO.getHisWeatherFeature(), areaWeatherFeatureDTO.getFcWeatherFeature(),
            holidayDOS);
    }
}