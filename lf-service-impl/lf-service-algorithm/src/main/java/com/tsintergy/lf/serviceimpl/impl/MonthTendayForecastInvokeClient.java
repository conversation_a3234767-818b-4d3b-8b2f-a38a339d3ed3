/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceimpl.impl;

import com.tsieframework.core.base.math.BigDecimalFunctions;
import com.tsintergy.aif.algorithm.serviceapi.base.dto.monthTendayForecast.MonthTendayForecastAlgorithmParam;
import com.tsintergy.aif.algorithm.serviceapi.base.dto.monthTendayForecast.MonthTendayForecastBasicData;
import com.tsintergy.aif.algorithm.serviceapi.base.dto.monthTendayForecast.MonthTendayForecastInputData;
import com.tsintergy.aif.algorithm.serviceapi.base.dto.monthTendayForecast.MonthTendayForecastParam;
import com.tsintergy.aif.algorithm.serviceapi.base.pojo.Value;
import com.tsintergy.aif.algorithm.serviceimpl.client.facade.impl.AbstractMonthTendayForecastInvokeClient;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.algorithm.dto.ValueDTO;
import com.tsintergy.lf.serviceapi.base.base.api.HolidayService;
import com.tsintergy.lf.serviceapi.base.base.pojo.HolidayDO;
import com.tsintergy.lf.serviceapi.base.forecast.api.LongForecastService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadFeatureFcDTO;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadHisDataDTO;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityDayHisService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayHisDO;
import com.tsintergy.lf.serviceimpl.support.AlgorithmDataSupport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description: 月度旬度中长期客户端 <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/5/13 14:41
 * @Version: 1.0.0
 */
@Service("monthTendayForecastInvokeClient")
public class MonthTendayForecastInvokeClient extends AbstractMonthTendayForecastInvokeClient<MonthTendayForecastParam> {

    @Autowired
    LongForecastService longForecastService;

    @Autowired
    LoadCityHisService loadCityHisService ;

    @Autowired
    AlgorithmDataSupport algorithmDataSupport;

    @Autowired
    WeatherFeatureCityDayHisService weatherFeatureCityDayHisService;

    @Autowired
    HolidayService holidayService;

    @Override
    protected MonthTendayForecastBasicData generateBasicData(MonthTendayForecastParam param) {
        MonthTendayForecastBasicData monthTendayForecastBasicData = new MonthTendayForecastBasicData(new ArrayList<>(), new ArrayList<>(), new ArrayList<>(), new ArrayList<>());
        Date beginDate = param.getBeginDate();
        Date lastDate = param.getEndDate();
        try {
            List<LoadHisDataDTO> loadHisDataDTOS = loadCityHisService
                    .findLoadCityVOsByCityId(DateUtil.getMoveDay(beginDate, -1200), lastDate, param.getCityId(),
                            param.getCaliberId());
            Map<Date, List<LoadHisDataDTO>> hisDateListMap = loadHisDataDTOS.stream().collect(Collectors.groupingBy(u -> DateUtil.getFirstDayInTenday(u.getDate()), Collectors.toList()));
            List<Value> hisTendayDataList = new ArrayList<>();
            hisDateListMap.forEach((k, v) -> {
                List<LoadFeatureFcDTO> hisDayLoadList = new ArrayList<>();
                for (LoadHisDataDTO hisDataDTO : v) {
                    LoadFeatureFcDTO loadFeatureFcDTO = new LoadFeatureFcDTO();
                    loadFeatureFcDTO.setMaxLoad(BigDecimalFunctions.listMax(hisDataDTO.getData()));
                    loadFeatureFcDTO.setMinLoad(BigDecimalFunctions.listMin(hisDataDTO.getData()));
                    loadFeatureFcDTO.setAveLoad(BigDecimalFunctions.listAvg(hisDataDTO.getData()));
                    loadFeatureFcDTO.setIntegralLoad(loadFeatureFcDTO.getAveLoad() != null ? BigDecimalFunctions.multiply(loadFeatureFcDTO.getAveLoad(), new BigDecimal(24)) : null);
                    hisDayLoadList.add(loadFeatureFcDTO);
                }

                ValueDTO valueDTO = new ValueDTO();
                valueDTO.setDate(new java.sql.Date(k.getTime()));
                List<BigDecimal> bigDecimals = new ArrayList<>();
                bigDecimals.add(BigDecimalFunctions.listMax(hisDayLoadList.stream().map(LoadFeatureFcDTO::getMaxLoad).collect(Collectors.toList())));
                bigDecimals.add(BigDecimalFunctions.listMin(hisDayLoadList.stream().map(LoadFeatureFcDTO::getMinLoad).collect(Collectors.toList())));
                bigDecimals.add(BigDecimalFunctions.listAvg(hisDayLoadList.stream().map(LoadFeatureFcDTO::getAveLoad).collect(Collectors.toList())));
                if (hisDayLoadList.stream().anyMatch(u -> u.getIntegralLoad()!=null)) {
                    bigDecimals.add(BigDecimalFunctions.listSum(hisDayLoadList.stream().map(LoadFeatureFcDTO::getIntegralLoad).collect(Collectors.toList())));
                }
                valueDTO.setValueList(bigDecimals);
                hisTendayDataList.add(valueDTO);
            });

            List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisStatsDOS = weatherFeatureCityDayHisService
                    .listWeatherFeatureCityDayHisDO(param.getCityId(), DateUtil.getMoveDay(beginDate, -1200), lastDate);
            Map<Date, List<WeatherFeatureCityDayHisDO>> weatherDateListMap = weatherFeatureCityDayHisStatsDOS.stream().collect(Collectors.groupingBy(u -> DateUtil.getFirstDayInTenday(u.getDate()), Collectors.toList()));
            List<Value> factorDataList = new ArrayList<>();
            weatherDateListMap.forEach((k, weatherFeatureList) -> {

                ValueDTO valueDTO = new ValueDTO();
                valueDTO.setDate(new java.sql.Date(k.getTime()));
                //顺序与MonthTendayForecastAlgorithmServiceImpl#forecast 中的factorNameList一致
                List<BigDecimal> bigDecimals = new ArrayList<>();
                bigDecimals.add(BigDecimalFunctions.listAvg(weatherFeatureList.stream().map(WeatherFeatureCityDayHisDO::getAveWinds).collect(Collectors.toList())));
                bigDecimals.add(BigDecimalFunctions.listMax(weatherFeatureList.stream().map(WeatherFeatureCityDayHisDO::getMaxWinds).collect(Collectors.toList())));
                bigDecimals.add(BigDecimalFunctions.listAvg(weatherFeatureList.stream().map(WeatherFeatureCityDayHisDO::getMaxWinds).collect(Collectors.toList())));
                bigDecimals.add(BigDecimalFunctions.listAvg(weatherFeatureList.stream().map(WeatherFeatureCityDayHisDO::getMinWinds).collect(Collectors.toList())));
                bigDecimals.add(BigDecimalFunctions.listMin(weatherFeatureList.stream().map(WeatherFeatureCityDayHisDO::getMinWinds).collect(Collectors.toList())));
                bigDecimals.add(BigDecimalFunctions.listAvg(weatherFeatureList.stream().map(WeatherFeatureCityDayHisDO::getRainfall).collect(Collectors.toList())));
                bigDecimals.add(BigDecimalFunctions.listAvg(weatherFeatureList.stream().map(WeatherFeatureCityDayHisDO::getAveHumidity).collect(Collectors.toList())));
                bigDecimals.add(BigDecimalFunctions.listMax(weatherFeatureList.stream().map(WeatherFeatureCityDayHisDO::getHighestHumidity).collect(Collectors.toList())));
                bigDecimals.add(BigDecimalFunctions.listAvg(weatherFeatureList.stream().map(WeatherFeatureCityDayHisDO::getHighestHumidity).collect(Collectors.toList())));
                bigDecimals.add(BigDecimalFunctions.listAvg(weatherFeatureList.stream().map(WeatherFeatureCityDayHisDO::getLowestHumidity).collect(Collectors.toList())));
                bigDecimals.add(BigDecimalFunctions.listMin(weatherFeatureList.stream().map(WeatherFeatureCityDayHisDO::getLowestHumidity).collect(Collectors.toList())));
                bigDecimals.add(BigDecimalFunctions.listAvg(weatherFeatureList.stream().map(WeatherFeatureCityDayHisDO::getAveTemperature).collect(Collectors.toList())));
                bigDecimals.add(BigDecimalFunctions.listMax(weatherFeatureList.stream().map(WeatherFeatureCityDayHisDO::getHighestTemperature).collect(Collectors.toList())));
                bigDecimals.add(BigDecimalFunctions.listAvg(weatherFeatureList.stream().map(WeatherFeatureCityDayHisDO::getHighestTemperature).collect(Collectors.toList())));
                bigDecimals.add(BigDecimalFunctions.listAvg(weatherFeatureList.stream().map(WeatherFeatureCityDayHisDO::getLowestTemperature).collect(Collectors.toList())));
                bigDecimals.add(BigDecimalFunctions.listMin(weatherFeatureList.stream().map(WeatherFeatureCityDayHisDO::getLowestTemperature).collect(Collectors.toList())));

                valueDTO.setValueList(bigDecimals);
                factorDataList.add(valueDTO);
            });

            List<HolidayDO> holidayDOS = holidayService.findHolidayVOS(null,null);
            monthTendayForecastBasicData.setHisDatas(hisTendayDataList.stream().sorted(Comparator.comparing(Value::getDate)).collect(Collectors.toList()));
            monthTendayForecastBasicData.setFactorValueList(factorDataList.stream().sorted(Comparator.comparing(Value::getDate)).collect(Collectors.toList()));
            monthTendayForecastBasicData.setHolidays(holidayDOS);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return monthTendayForecastBasicData;
    }

    @Override
    protected MonthTendayForecastAlgorithmParam generateAlgorithmParam(MonthTendayForecastParam param) {
        return super.generateAlgorithmParam(param);
    }

    @Override
    protected MonthTendayForecastInputData generateInputData(MonthTendayForecastParam param, MonthTendayForecastBasicData forecastBasicData) throws Exception {
        return super.generateInputData(param, forecastBasicData);
    }
}