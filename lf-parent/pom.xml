<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.tsintergy.lf</groupId>
        <artifactId>lf</artifactId>
        <version>${lf.version}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>lf-parent</artifactId>
    <packaging>pom</packaging>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <tsie-cloud-security.version>3.1.0-cas-SNAPSHOT</tsie-cloud-security.version>
        <tsie-emodel.version>3.1.0</tsie-emodel.version>
        <tsie-cloud-job.version>3.1.0</tsie-cloud-job.version>
        <lombok.version>1.18.0</lombok.version>
        <java.cas.client.version>3.5.0</java.cas.client.version>
        <shardingsphere.version>4.0.1</shardingsphere.version>
        <tsie.log4j.version>2.17.0</tsie.log4j.version>
        <tsie-alginv.version>3.2.0</tsie-alginv.version>

        <!-- jib插件打包专用配置，具体参考com.tsieframework:tsie-build的jib-maven-plugin插件配置 -->
        <docker-registry-default>192.168.14.130:5000</docker-registry-default>
        <docker-registry-to>192.168.14.130:5000</docker-registry-to>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- 集成tsie-cloud-security -->
            <dependency>
                <groupId>com.tsieframework.cloud.security</groupId>
                <artifactId>tsie-cloud-security-parent</artifactId>
                <version>${tsie-cloud-security.version}</version>
                <scope>import</scope>
                <type>pom</type>
            </dependency>

            <!-- 集成tsie-emodel -->
            <dependency>
                <groupId>com.tsintergy.emodel</groupId>
                <artifactId>emodel-parent</artifactId>
                <version>${tsie-emodel.version}</version>
                <scope>import</scope>
                <type>pom</type>
            </dependency>

            <dependency>
                <groupId>com.tsintergy.algorithm</groupId>
                <artifactId>tsie-alginv-parent</artifactId>
                <version>${tsie-alginv.version}</version>
                <scope>import</scope>
                <type>pom</type>
            </dependency>

            <!-- 集成tsie-job -->
            <dependency>
                <groupId>com.tsintergy.job</groupId>
                <artifactId>tsie-cloud-job-parent</artifactId>
                <version>${tsie-cloud-job.version}</version>
                <scope>import</scope>
                <type>pom</type>
            </dependency>

            <dependency>
                <groupId>com.tsintergy.lf</groupId>
                <artifactId>lf-core</artifactId>
                <version>${lf.version}</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>

            <dependency>
                <groupId>com.tsintergy.lf</groupId>
                <artifactId>lf-i18n</artifactId>
                <version>${lf.version}</version>
            </dependency>

            <dependency>
                <groupId>com.tsintergy.lf</groupId>
                <artifactId>lf-config</artifactId>
                <version>${lf.version}</version>
            </dependency>

            <dependency>
                <groupId>com.tsintergy.lf</groupId>
                <artifactId>lf-service-api</artifactId>
                <version>${lf.version}</version>
            </dependency>

            <dependency>
                <groupId>com.tsintergy.lf</groupId>
                <artifactId>lf-service-base</artifactId>
                <version>${lf.version}</version>
            </dependency>
            <dependency>
                <groupId>com.tsintergy.lf</groupId>
                <artifactId>lf-service-algorithm</artifactId>
                <version>${lf.version}</version>
            </dependency>

            <dependency>
                <groupId>com.tsintergy.lf</groupId>
                <artifactId>lf-web-controller</artifactId>
                <version>${lf.version}</version>
            </dependency>

            <dependency>
                <groupId>com.tsintergy.lf</groupId>
                <artifactId>lf-starter-web-run-rpc</artifactId>
                <version>${lf.version}</version>
            </dependency>

            <dependency>
                <groupId>com.tsintergy.lf</groupId>
                <artifactId>lf-starter-web-run-single</artifactId>
                <version>${lf.version}</version>
            </dependency>

            <dependency>
                <groupId>com.tsintergy.lf</groupId>
                <artifactId>lf-web-run-rpc</artifactId>
                <version>${lf.version}</version>
            </dependency>

            <dependency>
                <groupId>com.tsintergy.lf</groupId>
                <artifactId>lf-starter-service-impl</artifactId>
                <version>${lf.version}</version>
            </dependency>

            <dependency>
                <groupId>com.tsintergy.lf</groupId>
                <artifactId>lf-web-run-single</artifactId>
                <version>${lf.version}</version>
            </dependency>

            <dependency>
                <groupId>com.tsintergy.lf</groupId>
                <artifactId>lf-web-static</artifactId>
                <version>${lf.version}</version>
            </dependency>


            <!-- https://mvnrepository.com/artifact/com.github.junrar/junrar -->
            <dependency>
                <groupId>com.github.junrar</groupId>
                <artifactId>junrar</artifactId>
                <version>0.7</version>
            </dependency>
            <dependency>
                <groupId>org.apache.axis</groupId>
                <artifactId>axis</artifactId>
                <version>1.4</version>
            </dependency>
            <dependency>
                <groupId>axis</groupId>
                <artifactId>axis-jaxrpc</artifactId>
                <version>1.4</version>
            </dependency>
            <dependency>
                <groupId>axis</groupId>
                <artifactId>axis-wsdl4j</artifactId>
                <version>1.5.1</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>2.1.6</version>
                <exclusions>
                    <exclusion>
                        <artifactId>asm</artifactId>
                        <groupId>org.ow2.asm</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>3.17</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml-schemas</artifactId>
                <version>3.17</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>3.17</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-math3</artifactId>
                <version>3.6.1</version>
            </dependency>
            <dependency>
                <groupId>commons-beanutils</groupId>
                <artifactId>commons-beanutils</artifactId>
                <version>1.9.3</version>
            </dependency>
            <dependency>
                <groupId>commons-collections</groupId>
                <artifactId>commons-collections</artifactId>
                <version>3.2.2</version>
            </dependency>

            <dependency>
                <groupId>commons-discovery</groupId>
                <artifactId>commons-discovery</artifactId>
                <version>0.5</version>
            </dependency>
            <!--cas的客户端 -->
            <dependency>
                <groupId>org.jasig.cas.client</groupId>
                <artifactId>cas-client-core</artifactId>
                <version>${java.cas.client.version}</version>
            </dependency>
            <!--达梦8-->
            <dependency>
                <groupId>dm</groupId>
                <artifactId>jdbc-driver</artifactId>
                <version>8.0</version>
            </dependency>

<!--            达梦6-->
<!--            <dependency>-->
<!--                <groupId>dm</groupId>-->
<!--                <artifactId>jdbc-driver</artifactId>-->
<!--                <version>6.0</version>-->
<!--            </dependency>-->

<!--            <dependency>-->
<!--                <groupId>com.tsintergy</groupId>-->
<!--                <artifactId>dm6-wrapper</artifactId>-->
<!--                <version>1.0-SNAPSHOT</version>-->
<!--            </dependency>-->
            <dependency>
                <groupId>dm</groupId>
                <artifactId>dialect.hibernate</artifactId>
                <version>4.1</version>
            </dependency>
            <dependency>
                <groupId>com.tsieframework.jdbc</groupId>
                <artifactId>jdbc-wrapper</artifactId>
                <version>1.0.1</version>
            </dependency>

            <!--分库分表-->
            <dependency>
                <groupId>org.apache.shardingsphere</groupId>
                <artifactId>sharding-jdbc-core</artifactId>
                <version>${shardingsphere.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.shardingsphere</groupId>
                <artifactId>sharding-jdbc-spring-namespace</artifactId>
                <version>${shardingsphere.version}</version>
            </dependency>

            <!--jasypt处理配置文件数据库密码加密-->
            <dependency>
                <groupId>com.github.ulisesbocchio</groupId>
                <artifactId>jasypt-spring-boot-starter</artifactId>
                <version>2.0.0</version>
            </dependency>

            <!--log4j漏洞解决-->
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-api</artifactId>
                <version>${tsie.log4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-core</artifactId>
                <version>${tsie.log4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-api</artifactId>
                <version>${tsie.log4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-slf4j-impl</artifactId>
                <version>${tsie.log4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-jul</artifactId>
                <version>${tsie.log4j.version}</version>
            </dependency>


            <dependency>
                <artifactId>aif-algorithm-service-api</artifactId>
                <groupId>com.tsintergy.aif</groupId>
                <version>henan-1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <artifactId>aif-algorithm-service-client</artifactId>
                <groupId>com.tsintergy.aif</groupId>
                <version>henan-1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <artifactId>aif-algorithm-service-base</artifactId>
                <groupId>com.tsintergy.aif</groupId>
                <version>henan-1.0.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-compress</artifactId>
                <version>1.20</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>4.5.0</version>
            </dependency>

            <dependency>
                <groupId>xerces</groupId>
                <artifactId>xercesImpl</artifactId>
                <version>2.11.0</version>
            </dependency>

            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>2.6</version>
                <scope>compile</scope>
            </dependency>

            <dependency>
                <groupId>org.jboss.slf4j</groupId>
                <artifactId>slf4j-jboss-logging</artifactId>
                <version>1.2.1.Final</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>2.7.0</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

</project>