<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tsintergy.lf.jobhandler.collect.serviceimpl.dao.D5000LoadClctDAO">
  <resultMap id="BaseResultMap" type="com.tsintergy.lf.jobhandler.collect.pojo.D5000LoadClctDO">
    <result column="occur_time" jdbcType="TIMESTAMP" property="occurTime" />
    <result column="sload" jdbcType="DECIMAL" property="cur_097" />
  </resultMap>

  <select id="findLoadClctDOOrderByTimeAsc" resultMap="BaseResultMap" statementType="STATEMENT">
    select
    OCCUR_TIME,${column} sload
    from ${tableName}
    where   <![CDATA[  OCCUR_TIME >= ${startTime} and   OCCUR_TIME <= ${endTime}  ]]>
    order by OCCUR_TIME asc
  </select>

<!--  <select id="findLoadClctDOOrderByTimeAsc" resultMap="BaseResultMap" statementType="STATEMENT">-->
<!--    select-->
<!--    OCCUR_TIME,cur_097 sload-->
<!--    from Yc_hs_500017-->
<!--    where   <![CDATA[  OCCUR_TIME >= ${startTime} and   OCCUR_TIME <= ${endTime}  ]]>-->
<!--    order by OCCUR_TIME asc-->
<!--  </select>-->

  <select id="findSmallPowerLoadClctDO" resultMap="BaseResultMap" statementType="STATEMENT">
    select
    OCCUR_TIME,${column} sload
    from ${tableName}
    where   <![CDATA[  OCCUR_TIME >= ${startTime} and   OCCUR_TIME <= ${endTime}  ]]>
    order by OCCUR_TIME asc
  </select>

</mapper>