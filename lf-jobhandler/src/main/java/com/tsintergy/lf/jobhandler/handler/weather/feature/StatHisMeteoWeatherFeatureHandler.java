/**
 * Copyright (C), 2015‐2020, 北京清能互联科技有限公司 Author:  gss Date:  2020/3/5 15:40 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.jobhandler.handler.weather.feature;

import com.tsintergy.lf.jobhandler.common.AbstractBaseHandler;
import com.tsintergy.lf.jobhandler.common.ParamDate;
import com.tsintergy.lf.jobhandler.common.ParamDateUtil;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureStatMeteoService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureStatService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 统计多气象源历史日气象特性
 * 0 10 3 * * ?
 *
 * <AUTHOR>
 * @create 2020/3/5
 * @since 1.0.0
 */
@Component
@JobHandler("statHisMeteoWeatherFeatureHandler")
@Slf4j
public class StatHisMeteoWeatherFeatureHandler extends AbstractBaseHandler {

    @Autowired
    private WeatherFeatureStatMeteoService  weatherFeatureStatService;


    @Override
    public ReturnT<String> execute(String s) throws Exception {
        Date startDate = DateUtils.addDays(new Date(), -1);
        ParamDate paramDate = resolveJobParam(s, startDate, 0);
        statHisWeatherFeature(paramDate);
        return ReturnT.SUCCESS;
    }

    public void statHisWeatherFeature(ParamDate paramDate) throws Exception {
        statDayHisWeatherFeature(paramDate);
    }


    public void statDayHisWeatherFeature(ParamDate paramDate) throws Exception {
        XxlJobLogger.log("开始统计历史日气象特性并入库，开始时间为:{},结束日期为：{}", paramDate.getStartDate(), paramDate.getEndDate());
        weatherFeatureStatService.doStatWeatherFeatureCityDay(null, paramDate.getStartDate(), paramDate.getEndDate());
        XxlJobLogger.log("统计成功");
    }
}
