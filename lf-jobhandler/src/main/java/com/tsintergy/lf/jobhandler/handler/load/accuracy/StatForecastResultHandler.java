/**
 * Copyright (C), 2015‐2020, 北京清能互联科技有限公司 Author:  gss Date:  2020/3/5 15:40 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.jobhandler.handler.load.accuracy;

import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsintergy.lf.jobhandler.common.AbstractBaseHandler;
import com.tsintergy.lf.jobhandler.common.ParamDate;
import com.tsintergy.lf.serviceapi.base.forecast.api.ForecastResultStatService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 *
 * @description: 统计各个算法的预测结果（准确率，偏差值，离散度）
 * <AUTHOR>
 * @date 2025/04/10 15:27
 * @version: 1.0
 */
@Component
@JobHandler("statForecastResultHandler")
@Slf4j
public class StatForecastResultHandler extends AbstractBaseHandler {

    @Autowired
    private ForecastResultStatService forecastResultStatService;


    @Override
    public ReturnT<String> execute(String s) throws Exception {
        Date startDate = DateUtils.addDays(new Date(), -1);
        ParamDate paramDate = resolveJobParam(s, startDate, 0);
        startDate = paramDate.getStartDate();
        Date endDate = paramDate.getEndDate();
        try {
            XxlJobLogger.log("开始统计算法的预测结果，开始时间为:{},结束时间：{}", startDate, endDate);
            forecastResultStatService.statForecastResult(null, null, null, startDate, endDate);
            XxlJobLogger.log("统计算法的预测结果执行成功啦!");
        } catch (Exception e) {
            XxlJobLogger.log("执行异常，异常为{}", e);
            throw new BusinessException(e.getMessage(), e.toString());
        }
        return ReturnT.SUCCESS;
    }
}
