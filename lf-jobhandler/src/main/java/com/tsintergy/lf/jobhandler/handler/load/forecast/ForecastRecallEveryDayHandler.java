/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.jobhandler.handler.load.forecast;

import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsintergy.lf.core.constants.CityConstants;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.constants.SystemConstant;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.jobhandler.common.AbstractBaseHandler;
import com.tsintergy.lf.jobhandler.common.ParamDate;
import com.tsintergy.lf.jobhandler.task.AutoForecastRecallTask;
import com.tsintergy.lf.serviceapi.algorithm.api.ComprehensiveAlgorithmService;
import com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum;
import com.tsintergy.lf.serviceapi.base.base.api.CaliberService;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.api.HolidayService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CaliberDO;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.base.pojo.HolidayDO;
import com.tsintergy.lf.serviceapi.base.forecast.api.AutoForecastService;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceapi.base.system.pojo.SettingSystemDO;
import com.tsintergy.lf.serviceimpl.common.impl.CoreConfigInfo;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * Description: 每日预测回溯定时任务 <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/8/1 17:20
 * @Version: 1.0.0
 */

@Component
@JobHandler("forecastRecallEveryDayHandler")
@Slf4j
public class ForecastRecallEveryDayHandler extends AbstractBaseHandler {

    @Autowired
    SettingSystemService settingSystemService;
    @Autowired
    HolidayService holidayService;
    @Autowired
    private CityService cityService;
    @Autowired
    private CaliberService caliberService;
    @Autowired
    private ScheduledThreadPoolExecutor scheduledThreadPoolExecutor;
    @Autowired
    private CoreConfigInfo coreConfigInfo;
    @Autowired
    private AutoForecastService autoForecastService;
    @Autowired
    private ComprehensiveAlgorithmService comprehensiveAlgorithmService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        //默认用实际气象跑昨天的预测
        Date startDate = DateUtils.addDays(DateUtil.getFormatDate(new Date()), -1);
        Date endDate = startDate;
        List<CityDO> cityVOS = cityService.findAllCitys();
        List<CaliberDO> caliberVOS = caliberService.findAllCalibers();

        XxlJobLogger.log("普通正常日预测算法开始预测：城市数量为:{}", cityVOS.size());
        //添加预测批次，本次预测放在同一文件夹下面
        String uid = DateUtil.getUid();
        Integer forecastType = Integer.parseInt(coreConfigInfo.getRuntimeParam("implement.forecast.type"));
        Integer pointNum = Integer.parseInt(coreConfigInfo.getRuntimeParam("task.forecast.point"));

        List<AlgorithmEnum> enums = new ArrayList<>();
        enums.add(AlgorithmEnum.FORECAST_INNOVATION);

        if (StringUtils.isNotEmpty(s)) {
            String[] split = s.split(",");
            String startDateStr = split[0];
            String endDateStr = split[1];
            startDate = DateUtils.string2Date(startDateStr, DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);
            endDate = DateUtils.string2Date(endDateStr, DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);
        }

        List<Date> listBetweenDay = DateUtil.getListBetweenDay(startDate, endDate);

        ParamDate paramDate = null;
        //1 所有城市
        for (CityDO cityVO : cityVOS) {
            if (!cityVO.getId().equals(CityConstants.PROVINCE_ID)) {
                continue;
            }
            //2 所有口径
            for (CaliberDO caliberVO : caliberVOS) {
                if (!caliberVO.getId().equals(CityConstants.PROVINCE_ID) && !caliberVO.getId().equals(Constants.DEFAULT_CALIBER_ID)) {
                    continue;
                }
                for (Date date : listBetweenDay) {
                    XxlJobLogger.log("开始预测:城市id为:{}, 口径id为：{},开始时间为：{}，结束时间为：{}", cityVO.getId(), caliberVO.getId(), startDate, endDate);
                    scheduledThreadPoolExecutor.schedule(new AutoForecastRecallTask(forecastType, uid, cityVO.getId(),
                            caliberVO.getId(),
                            autoForecastService, date, date, enums, 0, 2, pointNum), 0, TimeUnit.MILLISECONDS);
                }
            }
        }
        return ReturnT.SUCCESS;
    }


    /**
     * 判断是否启动节假日算法
     *
     * @param srcDate
     * @return
     */
    private ParamDate judgeIfStartHoliday(String param, Date srcDate) throws Exception {
        Date date = DateUtil.getFormatDate(srcDate);
        ParamDate paramDate = new ParamDate();
        if (StringUtils.isNotEmpty(param)) {
            paramDate = resolveJobParam(param, null, null);
            return paramDate;
        }
        SettingSystemDO settingSystemDO = settingSystemService.findByFieldId(SystemConstant.HOLIDAY_DAY);
        //默认是节假日前一天开始预测
        Integer advanceDay = 1;
        if (settingSystemDO != null) {
            advanceDay = Integer.valueOf(settingSystemDO.getValue());
        }
        Boolean holidayFlag = false;
        List<HolidayDO> holidayVOS = holidayService.getAllHolidayVOS();
        Map<Date, HolidayDO> map = new HashMap<>();
        //把所有放假的日期转map
        for (HolidayDO vo : holidayVOS) {
            List<Date> dates = DateUtil.getListBetweenDay(new Date(vo.getStartDate().getTime()), new Date(vo.getEndDate().getTime()));
            for (Date between : dates) {
                map.put(between, vo);
            }
        }
        //例如前五天需要预测出节假日，2月11日是春节，2月8日预测2月11到2月18日，2月9日预测2月11日到2月18日
        for (int i = advanceDay; i >= 0; i--) {
            Date holidayFcDate = DateUtils.addDays(date, i);
            if (map.containsKey(holidayFcDate)) {
                holidayFlag = true;
                HolidayDO holidayDO = map.get(holidayFcDate);
                paramDate.setStartDate(holidayDO.getStartDate());
                paramDate.setEndDate(DateUtils.addDays(holidayDO.getEndDate(), 1));
                break;
            }
        }
        //需要启动预测
        if (holidayFlag) {
            Date startDate = paramDate.getStartDate();
            if (date.after(startDate) || date.equals(startDate)) {
                paramDate.setStartDate(DateUtils.addDays(date, 1));
            }
        }
        return paramDate;
    }
}