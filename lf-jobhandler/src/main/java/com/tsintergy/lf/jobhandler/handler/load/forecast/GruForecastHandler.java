package com.tsintergy.lf.jobhandler.handler.load.forecast;

import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsintergy.lf.core.constants.CityConstants;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.constants.SystemConstant;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.jobhandler.common.AbstractBaseHandler;
import com.tsintergy.lf.jobhandler.task.AutoGruForecastTask;
import com.tsintergy.lf.serviceapi.algorithm.api.GurNetworkAlgorithmService;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceapi.base.system.pojo.SettingSystemDO;
import com.tsintergy.lf.serviceimpl.forecast.impl.AlgorithmInvoker;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * @description: GRU算法定时预测任务
 * @date 2025/04/10 15:22
 * @version: 1.0
 */
@Component
@JobHandler("gruNetworkHandler")
@Slf4j
public class GruForecastHandler extends AbstractBaseHandler {

    @Autowired
    private GurNetworkAlgorithmService gurNetworkAlgorithmService;

    @Autowired
    private SettingSystemService settingSystemService;


    @Override
    public ReturnT<String> execute(String s) throws Exception {
        Date startDate = null;
        Date end = null;
        List<String> cityParamList = null;
        if (!StringUtils.isEmpty(s)) {
            String[] params = s.split(",");
            startDate = DateUtils.string2Date(params[0], DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);
            end = DateUtils.string2Date(params[1], DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);
            if (params.length == 3) {
                String cityIdsStr = params[2];
                String[] cityIds = cityIdsStr.split("-");
                cityParamList = Arrays.asList(cityIds);
            }
        } else {
            startDate = DateUtils.addDays(DateUtil.getFormatDate(new Date()), 1);
            //默认的是一次预测三天
            Integer days = 3;
            //查询用户自定义的正常日天数
            SettingSystemDO reportVOS = settingSystemService.findByFieldId(SystemConstant.FORECAST_DAY);
            if (reportVOS != null) {
                days = Integer.parseInt(reportVOS.getValue());
            }
            end = DateUtils.addDays(startDate, days - 1);
        }
        Date stopDate = DateUtils.string2Date("20250730",DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);
        if (startDate.compareTo(stopDate) > 0) {
            return ReturnT.FAIL;
        }
        AlgorithmInvoker instance = AlgorithmInvoker.getInstance();
        //gru算法预测
        instance.invoke(new AutoGruForecastTask(CityConstants.PROVINCE_ID, Constants.DEFAULT_CALIBER_ID, gurNetworkAlgorithmService,
                startDate, end, null));
        return ReturnT.SUCCESS;
    }
}
