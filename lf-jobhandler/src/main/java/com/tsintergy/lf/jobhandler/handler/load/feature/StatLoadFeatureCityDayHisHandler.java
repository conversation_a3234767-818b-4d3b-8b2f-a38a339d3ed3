/**
 * Copyright(C),2015‐2021,北京清能互联科技有限公司
 * Author:yangjin
 * Date:2021/1/2511:19
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.jobhandler.handler.load.feature;

import com.tsintergy.lf.jobhandler.common.AbstractBaseHandler;
import com.tsintergy.lf.jobhandler.common.ParamDate;
import com.tsintergy.lf.jobhandler.common.ParamDateUtil;
import com.tsintergy.lf.serviceapi.base.base.api.CaliberService;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureStatService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * Description: <br>
 * 0 40 2 * * ?
 * 统计历史负荷的日、周、月、季、年
 *
 * <AUTHOR>
 * @create 2021/1/25
 * @since 1.0.0
 */

@Component
@JobHandler("statLoadFeatureCityDayHisHandler")
public class StatLoadFeatureCityDayHisHandler extends AbstractBaseHandler {


    @Autowired
    private LoadFeatureStatService loadFeatureStatService;
    @Autowired
    CityService cityService;

    @Autowired
    CaliberService caliberService;


    @Override
    public ReturnT<String> execute(String param) throws Exception {
        // 1.解析参数
        Date startDate = DateUtils.addDays(new Date(), -1);
        ParamDate paramDate = resolveJobParam(param, startDate, 0);
        //2.重新统计历史负荷
        statHisLoadFeature(paramDate);
        return ReturnT.SUCCESS;
    }

    public void statHisLoadFeature(ParamDate paramDate) throws Exception {
        //日
        statDayHisLoadFeature(paramDate);
        //周
        statWeekHisLoadFeature(paramDate);
        //月
        statMonthHisLoadFeature(paramDate);
        //季
        statQuarterHisLoadFeature(paramDate);
        //年
        statYearHisLoadFeature(paramDate);

    }


    public void statDayHisLoadFeature(ParamDate paramDate) throws Exception {
        //统计日负荷特性
        XxlJobLogger.log("开始统计日负荷特性,开始日期：{}，结束日期：{}");
        loadFeatureStatService.doStatLoadFeatureCityDay(null, paramDate.getStartDate(), paramDate.getEndDate(),
                null);
        XxlJobLogger.log("统计成功");
    }


    public void statWeekHisLoadFeature(ParamDate paramDate) throws Exception {
        XxlJobLogger.log("开始统计周负荷特性,开始日期：{}，结束日期：{}");
        //统计周负荷特性
        ParamDate param = ParamDateUtil.getWeekParamDate(paramDate);
        loadFeatureStatService.doStatLoadFeatureCityWeek(null, param.getStartDate(), param.getEndDate(),
                null);
        XxlJobLogger.log("统计成功");
    }


    public void statMonthHisLoadFeature(ParamDate paramDate) throws Exception {
        XxlJobLogger.log("开始统计月负荷特性,开始日期：{}，结束日期：{}");
        //统计月负荷特性
        ParamDate param = ParamDateUtil.getMonthParamDate(paramDate);
        loadFeatureStatService.doStatLoadFeatureCityMonth(null, param.getStartDate(), param.getEndDate(),
                null);
        XxlJobLogger.log("统计成功");
    }


    public void statQuarterHisLoadFeature(ParamDate paramDate) throws Exception {
        XxlJobLogger.log("开始统计月季荷特性,开始日期：{}，结束日期：{}");
        //统计季负荷特性
        ParamDate param = ParamDateUtil.getQuarterParamDate(paramDate);
        loadFeatureStatService.doStatLoadFeatureCityQuarter(null, param.getStartDate(), param.getEndDate(),
                null);
        XxlJobLogger.log("统计成功");
    }


    public void statYearHisLoadFeature(ParamDate paramDate) throws Exception {
        XxlJobLogger.log("开始统计月年荷特性,开始日期：{}，结束日期：{}");
        //统计年负荷特性
        ParamDate param = ParamDateUtil.getYearParamDate(paramDate);
        loadFeatureStatService.doStatLoadFeatureCityYear(null, param.getStartDate(), param.getEndDate(),
                null);
        XxlJobLogger.log("统计成功");
    }
}
