/**
 * Copyright (C), 2015‐2020, 北京清能互联科技有限公司 Author:  gss Date:  2020/3/5 15:40 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.jobhandler.handler.load.accuracy;

import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsintergy.lf.jobhandler.common.AbstractBaseHandler;
import com.tsintergy.lf.jobhandler.common.ParamDate;
import com.tsintergy.lf.serviceapi.base.base.api.CaliberService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CaliberDO;
import com.tsintergy.lf.serviceapi.base.evalucation.api.AccuracyAssessService;
import com.tsintergy.lf.serviceapi.base.evalucation.api.AccuracyCompositeService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 *
 * @description: 统计各个算法的综合预测准确率
 * <AUTHOR>
 * @date 2025/04/10 15:26
 * @version: 1.0
 */
@Component
@JobHandler("StatForecastCompositeAccuracyHandler")
@Slf4j
public class StatForecastCompositeAccuracyHandler extends AbstractBaseHandler {

    @Autowired
    private AccuracyCompositeService accuracyCompositeService;

    @Autowired
    private CaliberService caliberService;

    @Autowired
    private AccuracyAssessService accuracyAssessService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        Date startDate = DateUtils.addDays(new Date(), -1);
        ParamDate paramDate = resolveJobParam(s, startDate, 0);
        startDate = paramDate.getStartDate();
        Date endDate = paramDate.getEndDate();
        try {
            List<CaliberDO> calibers = caliberService.findAllCalibers();
            for (CaliberDO caliber : calibers){
                XxlJobLogger.log("开始统计考核点准确率结果，开始时间为:{},结束时间：{}, 口径为{}", startDate, endDate, caliber.getName());
                accuracyAssessService.doCalculateAssessAccuracy(null, caliber.getId(), null, startDate, endDate);
                XxlJobLogger.log("开始统计考核点综合准确率结果，开始时间为:{},结束时间：{}, 口径为{}", startDate, endDate, caliber.getName());
                accuracyCompositeService.doCalculateCompositeAccuracy(null, caliber.getId(), null, startDate, endDate);
                XxlJobLogger.log("统计考核点准确率结果执行成功啦!");

            }
        } catch (Exception e) {
            XxlJobLogger.log("执行异常，异常为{}", e);
            throw new BusinessException(e.getMessage(), e.toString());
        }
        return ReturnT.SUCCESS;
    }


}
