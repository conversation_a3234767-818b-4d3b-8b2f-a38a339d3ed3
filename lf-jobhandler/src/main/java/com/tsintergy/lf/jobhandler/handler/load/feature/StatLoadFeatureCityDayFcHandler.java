/**
 * Copyright(C),2015‐2021,北京清能互联科技有限公司
 * Author:yangjin
 * Date:2021/1/259:45
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.jobhandler.handler.load.feature;

import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsintergy.lf.jobhandler.common.AbstractBaseHandler;
import com.tsintergy.lf.jobhandler.common.ParamDate;
import com.tsintergy.lf.jobhandler.common.ParamDateUtil;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureStatService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * Description: <br>
 * 统计日、周、月预测负荷特性
 * 0 50 2 * * ?
 *
 * <AUTHOR>
 * @create 2021/1/25
 * @since 1.0.0
 */
@JobHandler("statLoadFeatureCityDayFcHandler")
@Component
public class StatLoadFeatureCityDayFcHandler extends AbstractBaseHandler {

    @Autowired
    LoadFeatureStatService loadFeatureStatService;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        // 1.解析参数
        Date date = DateUtils.addDays(new Date(),-7);
        ParamDate paramDate = resolveJobParam(param, date, 17);
        //2.开始统计
        //日
        statDayFcLoadFeature(paramDate);
        paramDate = parseWeekNullParam(paramDate);
        //周
        statWeekFcLoadFeature(paramDate);

        return ReturnT.SUCCESS;
    }

    public void statDayFcLoadFeature(ParamDate paramDate) {
        XxlJobLogger.log("统计日预测负荷特性开始，开始时间:{} ,结束时间为:{}" + DateUtils.date2String(paramDate.getEndDate(), DateFormatType.SIMPLE_DATE_FORMAT_STR));
        loadFeatureStatService.doStatLoadFeatureCityDayFc(null, paramDate.getStartDate(), paramDate.getEndDate(), null);
        XxlJobLogger.log("统计成功");
    }


    public void statWeekFcLoadFeature(ParamDate paramDate) {
        XxlJobLogger.log("统计周预测负荷特性开始，开始时间:{}, 结束时间为:{}", paramDate.getStartDate(), paramDate.getEndDate());
        loadFeatureStatService.doStatLoadFeatureCityWeekFc(null, paramDate.getStartDate(), paramDate.getEndDate(), null);
        XxlJobLogger.log("统计成功");
    }

    ParamDate parseMonthNullParam(ParamDate paramDate) {
        paramDate.setEndDate(DateUtils.addDays(new Date(), -1));
        paramDate.setStartDate(ParamDateUtil.findMonthFirstDayByDate(paramDate.getEndDate()));
        return paramDate;
    }


    ParamDate parseWeekNullParam(ParamDate paramDate) {
        paramDate.setStartDate(ParamDateUtil.findWeekFirstDayByDate(DateUtils.addDays(new Date(), -1)));
        paramDate.setEndDate(DateUtils.addDays(new Date(), -1));
        return paramDate;
    }

}
