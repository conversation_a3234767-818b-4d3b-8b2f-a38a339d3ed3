-- ��Ŀ��ʼ��sql��Ĭ�����ɵ���¼�û�ͬ����ģʽ�¡�
CREATE TABLE "WEATHER_TYPHOON_RATE"
(
 "ID" VARCHAR(32) NOT NULL,
 "CITY_ID" VARCHAR(32) NOT NULL,
 "RATE" DEC(10,2) NOT NULL,
 "CREATE_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NULL,
 "UPDATE_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NULL
);

CREATE TABLE "WEATHER_TYPHOON_PROVINCE_HIS_BASIC"
(
 "ID" VARCHAR(32) NOT NULL,
 "DATE" DATE NOT NULL,
 "TYPE" TINYINT NOT NULL,
 "T0000" DEC(32,2) NULL,
 "T0015" DEC(32,2) NULL,
 "T0030" DEC(32,2) NULL,
 "T0045" DEC(32,2) NULL,
 "T0100" DEC(32,2) NULL,
 "T0115" DEC(32,2) NULL,
 "T0130" DEC(32,2) NULL,
 "T0145" DEC(32,2) NULL,
 "T0200" DEC(32,2) NULL,
 "T0215" DEC(32,2) NULL,
 "T0230" DEC(32,2) NULL,
 "T0245" DEC(32,2) NULL,
 "T0300" DEC(32,2) NULL,
 "T0315" DEC(32,2) NULL,
 "T0330" DEC(32,2) NULL,
 "T0345" DEC(32,2) NULL,
 "T0400" DEC(32,2) NULL,
 "T0415" DEC(32,2) NULL,
 "T0430" DEC(32,2) NULL,
 "T0445" DEC(32,2) NULL,
 "T0500" DEC(32,2) NULL,
 "T0515" DEC(32,2) NULL,
 "T0530" DEC(32,2) NULL,
 "T0545" DEC(32,2) NULL,
 "T0600" DEC(32,2) NULL,
 "T0615" DEC(32,2) NULL,
 "T0630" DEC(32,2) NULL,
 "T0645" DEC(32,2) NULL,
 "T0700" DEC(32,2) NULL,
 "T0715" DEC(32,2) NULL,
 "T0730" DEC(32,2) NULL,
 "T0745" DEC(32,2) NULL,
 "T0800" DEC(32,2) NULL,
 "T0815" DEC(32,2) NULL,
 "T0830" DEC(32,2) NULL,
 "T0845" DEC(32,2) NULL,
 "T0900" DEC(32,2) NULL,
 "T0915" DEC(32,2) NULL,
 "T0930" DEC(32,2) NULL,
 "T0945" DEC(32,2) NULL,
 "T1000" DEC(32,2) NULL,
 "T1015" DEC(32,2) NULL,
 "T1030" DEC(32,2) NULL,
 "T1045" DEC(32,2) NULL,
 "T1100" DEC(32,2) NULL,
 "T1115" DEC(32,2) NULL,
 "T1130" DEC(32,2) NULL,
 "T1145" DEC(32,2) NULL,
 "T1200" DEC(32,2) NULL,
 "T1215" DEC(32,2) NULL,
 "T1230" DEC(32,2) NULL,
 "T1245" DEC(32,2) NULL,
 "T1300" DEC(32,2) NULL,
 "T1315" DEC(32,2) NULL,
 "T1330" DEC(32,2) NULL,
 "T1345" DEC(32,2) NULL,
 "T1400" DEC(32,2) NULL,
 "T1415" DEC(32,2) NULL,
 "T1430" DEC(32,2) NULL,
 "T1445" DEC(32,2) NULL,
 "T1500" DEC(32,2) NULL,
 "T1515" DEC(32,2) NULL,
 "T1530" DEC(32,2) NULL,
 "T1545" DEC(32,2) NULL,
 "T1600" DEC(32,2) NULL,
 "T1615" DEC(32,2) NULL,
 "T1630" DEC(32,2) NULL,
 "T1645" DEC(32,2) NULL,
 "T1700" DEC(32,2) NULL,
 "T1715" DEC(32,2) NULL,
 "T1730" DEC(32,2) NULL,
 "T1745" DEC(32,2) NULL,
 "T1800" DEC(32,2) NULL,
 "T1815" DEC(32,2) NULL,
 "T1830" DEC(32,2) NULL,
 "T1845" DEC(32,2) NULL,
 "T1900" DEC(32,2) NULL,
 "T1915" DEC(32,2) NULL,
 "T1930" DEC(32,2) NULL,
 "T1945" DEC(32,2) NULL,
 "T2000" DEC(32,2) NULL,
 "T2015" DEC(32,2) NULL,
 "T2030" DEC(32,2) NULL,
 "T2045" DEC(32,2) NULL,
 "T2100" DEC(32,2) NULL,
 "T2115" DEC(32,2) NULL,
 "T2130" DEC(32,2) NULL,
 "T2145" DEC(32,2) NULL,
 "T2200" DEC(32,2) NULL,
 "T2215" DEC(32,2) NULL,
 "T2230" DEC(32,2) NULL,
 "T2245" DEC(32,2) NULL,
 "T2300" DEC(32,2) NULL,
 "T2315" DEC(32,2) NULL,
 "T2330" DEC(32,2) NULL,
 "T2345" DEC(32,2) NULL,
 "T2400" DEC(32,2) NULL,
 "CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NULL,
 "UPDATETIME" TIMESTAMP(6) NULL
);

CREATE TABLE "WEATHER_TYPHOON_HIS_CLCT"
(
 "ID" VARCHAR(100) NOT NULL,
 "TYID" VARCHAR(20) NOT NULL,
 "STATION" VARCHAR(14) NOT NULL,
 "NODE_TIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL,
 "RK_TIME" TIMESTAMP(6) NULL,
 "LONGITUDE" DEC(8,3) NULL,
 "LATITUDE" DEC(8,3) NULL,
 "WIND_POWER" DEC(8,3) NULL,
 "MAX_SPEED" DEC(8,3) NULL,
 "CENTER_PRESS" DEC(8,3) NULL,
 "MOVE_SPEED" DEC(8,3) NULL,
 "MOVE_DIRECTION" DEC(8,3) NULL,
 "SEVEN_AREA" DEC(8,3) NULL,
 "TEN_AREA" DEC(8,3) NULL,
 "TWELVE_AREA" DEC(8,3) NULL
);

CREATE TABLE "WEATHER_TYPHOON_FC_CLCT"
(
 "ID" VARCHAR(100) NOT NULL,
 "TY_ID" VARCHAR(100) NOT NULL,
 "STATION" VARCHAR(20) NOT NULL,
 "FORECAST_TIME" VARCHAR(100) NOT NULL,
 "COMING_TIME" VARCHAR(100) NOT NULL,
 "RK_TIME" TIMESTAMP(6) NULL,
 "LONGITUDE" DEC(8,3) NULL,
 "LATITUDE" DEC(8,3) NULL,
 "WIND_POWER" DEC(8,3) NULL,
 "MAX_SPEED" DEC(8,3) NULL,
 "CENTER_PRESS" DEC(8,3) NULL,
 "MOVE_SPEED" DEC(8,3) NULL,
 "MOVE_DIRECTION" DEC(8,3) NULL,
 "SEVEN_AREA" DEC(8,3) NULL,
 "TEN_AREA" DEC(8,3) NULL,
 "TWELVE_AREA" DEC(8,3) NULL
);

CREATE TABLE "WEATHER_TYPHOON_DEFINITION"
(
 "ID" VARCHAR(32) NOT NULL,
 "TEMPERATURE" DEC(10,2) DEFAULT 37.
 NOT NULL,
 "RAINFALL" DEC(10,2) DEFAULT 50.
 NOT NULL,
 "WIND" DEC(10,2) DEFAULT 17.2
 NOT NULL,
 "PREV_DAYS" INTEGER DEFAULT 2
 NOT NULL,
 "NEXT_DAYS" INTEGER DEFAULT 3
 NOT NULL,
 "CREATE_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NULL,
 "UPDATE_AT" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NULL
);

CREATE TABLE "WEATHER_TYPHOON_ARCHIVE_CLCT"
(
 "ID" VARCHAR(32) NOT NULL,
 "CN_NAME" VARCHAR(100) NULL,
 "EN_NAME" VARCHAR(100) NULL,
 "RK_TIME" VARCHAR(50) NULL,
 "DRPRK" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL,
 "AFFECT_AREA" BIT NULL,
 "LEVEL" BIT NULL,
 "ACTIVATE_TIME" TIMESTAMP(6) DEFAULT '1900-01-01 00:00:00'
 NOT NULL,
 "REPEAL_TIME" TIMESTAMP(6) DEFAULT '1900-01-01 00:00:00'
 NOT NULL,
 "LOGIN_TIME" TIMESTAMP(6) DEFAULT '1900-01-01 00:00:00'
 NOT NULL,
 "END_TIME" TIMESTAMP(6) DEFAULT '1900-01-01 00:00:00'
 NOT NULL,
 "CITY_ID" VARCHAR(32) NULL,
 "WIND" DEC(4,0) NULL,
 "FLAG" BIT NULL
);

CREATE TABLE "WEATHER_STATION_INFO"
(
 "ID" VARCHAR(50) NULL,
 "CITY_ID" VARCHAR(200) NULL,
 "STATION_ID" VARCHAR(200) NULL,
 "CREATETIME" TIMESTAMP(0) NULL,
 "UPDATETIME" TIMESTAMP(0) NULL
);

CREATE TABLE "WEATHER_STATION_HIS_BASIC"
(
 "ID" VARCHAR(32) NOT NULL,
 "DATE" DATE NOT NULL,
 "STATION_ID" VARCHAR(32) NOT NULL,
 "CITY_ID" VARCHAR(32) NOT NULL,
 "TYPE" TINYINT NOT NULL,
 "T0000" DEC(32,2) NULL,
 "T0015" DEC(32,2) NULL,
 "T0030" DEC(32,2) NULL,
 "T0045" DEC(32,2) NULL,
 "T0100" DEC(32,2) NULL,
 "T0115" DEC(32,2) NULL,
 "T0130" DEC(32,2) NULL,
 "T0145" DEC(32,2) NULL,
 "T0200" DEC(32,2) NULL,
 "T0215" DEC(32,2) NULL,
 "T0230" DEC(32,2) NULL,
 "T0245" DEC(32,2) NULL,
 "T0300" DEC(32,2) NULL,
 "T0315" DEC(32,2) NULL,
 "T0330" DEC(32,2) NULL,
 "T0345" DEC(32,2) NULL,
 "T0400" DEC(32,2) NULL,
 "T0415" DEC(32,2) NULL,
 "T0430" DEC(32,2) NULL,
 "T0445" DEC(32,2) NULL,
 "T0500" DEC(32,2) NULL,
 "T0515" DEC(32,2) NULL,
 "T0530" DEC(32,2) NULL,
 "T0545" DEC(32,2) NULL,
 "T0600" DEC(32,2) NULL,
 "T0615" DEC(32,2) NULL,
 "T0630" DEC(32,2) NULL,
 "T0645" DEC(32,2) NULL,
 "T0700" DEC(32,2) NULL,
 "T0715" DEC(32,2) NULL,
 "T0730" DEC(32,2) NULL,
 "T0745" DEC(32,2) NULL,
 "T0800" DEC(32,2) NULL,
 "T0815" DEC(32,2) NULL,
 "T0830" DEC(32,2) NULL,
 "T0845" DEC(32,2) NULL,
 "T0900" DEC(32,2) NULL,
 "T0915" DEC(32,2) NULL,
 "T0930" DEC(32,2) NULL,
 "T0945" DEC(32,2) NULL,
 "T1000" DEC(32,2) NULL,
 "T1015" DEC(32,2) NULL,
 "T1030" DEC(32,2) NULL,
 "T1045" DEC(32,2) NULL,
 "T1100" DEC(32,2) NULL,
 "T1115" DEC(32,2) NULL,
 "T1130" DEC(32,2) NULL,
 "T1145" DEC(32,2) NULL,
 "T1200" DEC(32,2) NULL,
 "T1215" DEC(32,2) NULL,
 "T1230" DEC(32,2) NULL,
 "T1245" DEC(32,2) NULL,
 "T1300" DEC(32,2) NULL,
 "T1315" DEC(32,2) NULL,
 "T1330" DEC(32,2) NULL,
 "T1345" DEC(32,2) NULL,
 "T1400" DEC(32,2) NULL,
 "T1415" DEC(32,2) NULL,
 "T1430" DEC(32,2) NULL,
 "T1445" DEC(32,2) NULL,
 "T1500" DEC(32,2) NULL,
 "T1515" DEC(32,2) NULL,
 "T1530" DEC(32,2) NULL,
 "T1545" DEC(32,2) NULL,
 "T1600" DEC(32,2) NULL,
 "T1615" DEC(32,2) NULL,
 "T1630" DEC(32,2) NULL,
 "T1645" DEC(32,2) NULL,
 "T1700" DEC(32,2) NULL,
 "T1715" DEC(32,2) NULL,
 "T1730" DEC(32,2) NULL,
 "T1745" DEC(32,2) NULL,
 "T1800" DEC(32,2) NULL,
 "T1815" DEC(32,2) NULL,
 "T1830" DEC(32,2) NULL,
 "T1845" DEC(32,2) NULL,
 "T1900" DEC(32,2) NULL,
 "T1915" DEC(32,2) NULL,
 "T1930" DEC(32,2) NULL,
 "T1945" DEC(32,2) NULL,
 "T2000" DEC(32,2) NULL,
 "T2015" DEC(32,2) NULL,
 "T2030" DEC(32,2) NULL,
 "T2045" DEC(32,2) NULL,
 "T2100" DEC(32,2) NULL,
 "T2115" DEC(32,2) NULL,
 "T2130" DEC(32,2) NULL,
 "T2145" DEC(32,2) NULL,
 "T2200" DEC(32,2) NULL,
 "T2215" DEC(32,2) NULL,
 "T2230" DEC(32,2) NULL,
 "T2245" DEC(32,2) NULL,
 "T2300" DEC(32,2) NULL,
 "T2315" DEC(32,2) NULL,
 "T2330" DEC(32,2) NULL,
 "T2345" DEC(32,2) NULL,
 "T2400" DEC(32,2) NULL,
 "CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NULL,
 "UPDATETIME" TIMESTAMP(6) NULL
);

CREATE TABLE "WEATHER_STATION_FC_BASIC"
(
 "ID" VARCHAR(32) NOT NULL,
 "DATE" DATE NOT NULL,
 "STATION_ID" VARCHAR(32) NOT NULL,
 "CITY_ID" VARCHAR(32) NULL,
 "TYPE" TINYINT NULL,
 "T0000" DEC(32,2) NULL,
 "T0015" DEC(32,2) NULL,
 "T0030" DEC(32,2) NULL,
 "T0045" DEC(32,2) NULL,
 "T0100" DEC(32,2) NULL,
 "T0115" DEC(32,2) NULL,
 "T0130" DEC(32,2) NULL,
 "T0145" DEC(32,2) NULL,
 "T0200" DEC(32,2) NULL,
 "T0215" DEC(32,2) NULL,
 "T0230" DEC(32,2) NULL,
 "T0245" DEC(32,2) NULL,
 "T0300" DEC(32,2) NULL,
 "T0315" DEC(32,2) NULL,
 "T0330" DEC(32,2) NULL,
 "T0345" DEC(32,2) NULL,
 "T0400" DEC(32,2) NULL,
 "T0415" DEC(32,2) NULL,
 "T0430" DEC(32,2) NULL,
 "T0445" DEC(32,2) NULL,
 "T0500" DEC(32,2) NULL,
 "T0515" DEC(32,2) NULL,
 "T0530" DEC(32,2) NULL,
 "T0545" DEC(32,2) NULL,
 "T0600" DEC(32,2) NULL,
 "T0615" DEC(32,2) NULL,
 "T0630" DEC(32,2) NULL,
 "T0645" DEC(32,2) NULL,
 "T0700" DEC(32,2) NULL,
 "T0715" DEC(32,2) NULL,
 "T0730" DEC(32,2) NULL,
 "T0745" DEC(32,2) NULL,
 "T0800" DEC(32,2) NULL,
 "T0815" DEC(32,2) NULL,
 "T0830" DEC(32,2) NULL,
 "T0845" DEC(32,2) NULL,
 "T0900" DEC(32,2) NULL,
 "T0915" DEC(32,2) NULL,
 "T0930" DEC(32,2) NULL,
 "T0945" DEC(32,2) NULL,
 "T1000" DEC(32,2) NULL,
 "T1015" DEC(32,2) NULL,
 "T1030" DEC(32,2) NULL,
 "T1045" DEC(32,2) NULL,
 "T1100" DEC(32,2) NULL,
 "T1115" DEC(32,2) NULL,
 "T1130" DEC(32,2) NULL,
 "T1145" DEC(32,2) NULL,
 "T1200" DEC(32,2) NULL,
 "T1215" DEC(32,2) NULL,
 "T1230" DEC(32,2) NULL,
 "T1245" DEC(32,2) NULL,
 "T1300" DEC(32,2) NULL,
 "T1315" DEC(32,2) NULL,
 "T1330" DEC(32,2) NULL,
 "T1345" DEC(32,2) NULL,
 "T1400" DEC(32,2) NULL,
 "T1415" DEC(32,2) NULL,
 "T1430" DEC(32,2) NULL,
 "T1445" DEC(32,2) NULL,
 "T1500" DEC(32,2) NULL,
 "T1515" DEC(32,2) NULL,
 "T1530" DEC(32,2) NULL,
 "T1545" DEC(32,2) NULL,
 "T1600" DEC(32,2) NULL,
 "T1615" DEC(32,2) NULL,
 "T1630" DEC(32,2) NULL,
 "T1645" DEC(32,2) NULL,
 "T1700" DEC(32,2) NULL,
 "T1715" DEC(32,2) NULL,
 "T1730" DEC(32,2) NULL,
 "T1745" DEC(32,2) NULL,
 "T1800" DEC(32,2) NULL,
 "T1815" DEC(32,2) NULL,
 "T1830" DEC(32,2) NULL,
 "T1845" DEC(32,2) NULL,
 "T1900" DEC(32,2) NULL,
 "T1915" DEC(32,2) NULL,
 "T1930" DEC(32,2) NULL,
 "T1945" DEC(32,2) NULL,
 "T2000" DEC(32,2) NULL,
 "T2015" DEC(32,2) NULL,
 "T2030" DEC(32,2) NULL,
 "T2045" DEC(32,2) NULL,
 "T2100" DEC(32,2) NULL,
 "T2115" DEC(32,2) NULL,
 "T2130" DEC(32,2) NULL,
 "T2145" DEC(32,2) NULL,
 "T2200" DEC(32,2) NULL,
 "T2215" DEC(32,2) NULL,
 "T2230" DEC(32,2) NULL,
 "T2245" DEC(32,2) NULL,
 "T2300" DEC(32,2) NULL,
 "T2315" DEC(32,2) NULL,
 "T2330" DEC(32,2) NULL,
 "T2345" DEC(32,2) NULL,
 "T2400" DEC(32,2) NULL,
 "CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NULL,
 "UPDATETIME" TIMESTAMP(6) NULL
);

CREATE TABLE "WEATHER_FEATURE_STATION_DAY_HIS_SERVICE"
(
 "ID" VARCHAR(32) NOT NULL,
 "DATE" DATE NOT NULL,
 "STATION_ID" VARCHAR(32) NOT NULL,
 "CITY_ID" VARCHAR(32) NOT NULL,
 "HIGHEST_TEMPERATURE" DEC(10,2) NULL,
 "LOWEST_TEMPERATURE" DEC(32,2) NULL,
 "AVE_TEMPERATURE" DEC(32,2) NULL,
 "HIGHEST_HUMIDITY" DEC(32,2) NULL,
 "LOWEST_HUMIDITY" DEC(32,2) NULL,
 "AVE_HUMIDITY" DEC(32,2) NULL,
 "MAX_WINDS" DEC(32,2) NULL,
 "MIN_WINDS" DEC(32,2) NULL,
 "AVE_WINDS" DEC(32,2) NULL,
 "HIGHEST_COMFORT" DEC(32,2) NULL,
 "LOWEST_COMFORT" DEC(32,2) NULL,
 "AVE_COMFORT" DEC(32,2) NULL,
 "HIGHEST_EFFECTIVE_TEMPERATURE" DEC(32,2) NULL,
 "LOWEST_EFFECTIVE_TEMPERATURE" DEC(32,2) NULL,
 "AVE_EFFECTIVE_TEMPERATURE" DEC(32,2) NULL,
 "MAX_COLDNESS" DEC(32,2) NULL,
 "MIN_COLDNESS" DEC(32,2) NULL,
 "AVE_COLDNESS" DEC(32,2) NULL,
 "MAX_TEMPERATURE_HUMIDITY" DEC(32,2) NULL,
 "MIN_TEMPERATURE_HUMIDITY" DEC(32,2) NULL,
 "AVE_TEMPERATURE_HUMIDITY" DEC(32,2) NULL,
 "RAINFALL" DEC(32,2) NULL,
 "CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NULL,
 "UPDATETIME" TIMESTAMP(6) NULL,
 "AVE_WINDDIRECTION" INTEGER DEFAULT NULL
 NULL
);

CREATE TABLE "WEATHER_FEATURE_STATION_DAY_FC_SERVICE"
(
 "ID" VARCHAR(32) NOT NULL,
 "DATE" DATE NOT NULL,
 "STATION_ID" VARCHAR(32) NOT NULL,
 "CITY_ID" VARCHAR(32) NOT NULL,
 "HIGHEST_TEMPERATURE" DEC(10,2) NULL,
 "LOWEST_TEMPERATURE" DEC(32,2) NULL,
 "AVE_TEMPERATURE" DEC(32,2) NULL,
 "HIGHEST_HUMIDITY" DEC(32,2) NULL,
 "LOWEST_HUMIDITY" DEC(32,2) NULL,
 "AVE_HUMIDITY" DEC(32,2) NULL,
 "MAX_WINDS" DEC(32,2) NULL,
 "MIN_WINDS" DEC(32,2) NULL,
 "AVE_WINDS" DEC(32,2) NULL,
 "HIGHEST_COMFORT" DEC(32,2) NULL,
 "LOWEST_COMFORT" DEC(32,2) NULL,
 "AVE_COMFORT" DEC(32,2) NULL,
 "HIGHEST_EFFECTIVE_TEMPERATURE" DEC(32,2) NULL,
 "LOWEST_EFFECTIVE_TEMPERATURE" DEC(32,2) NULL,
 "AVE_EFFECTIVE_TEMPERATURE" DEC(32,2) NULL,
 "MAX_COLDNESS" DEC(32,2) NULL,
 "MIN_COLDNESS" DEC(32,2) NULL,
 "AVE_COLDNESS" DEC(32,2) NULL,
 "MAX_TEMPERATURE_HUMIDITY" DEC(32,2) NULL,
 "MIN_TEMPERATURE_HUMIDITY" DEC(32,2) NULL,
 "AVE_TEMPERATURE_HUMIDITY" DEC(32,2) NULL,
 "RAINFALL" DEC(32,2) NULL,
 "CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NULL,
 "UPDATETIME" TIMESTAMP(6) NULL
);

CREATE TABLE "WEATHER_FEATURE_CITY_QUARTER_HIS_SERVICE"
(
 "ID" VARCHAR(32) NOT NULL,
 "CITY_ID" VARCHAR(32) NOT NULL,
 "YEAR" VARCHAR(4) NOT NULL,
 "QUARTER" VARCHAR(1) NOT NULL,
 "HIGHEST_TEMPERATURE" DEC(10,2) NULL,
 "LOWEST_TEMPERATURE" DEC(32,2) NULL,
 "AVE_TEMPERATURE" DEC(32,2) NULL,
 "HIGHEST_HUMIDITY" DEC(32,2) NULL,
 "LOWEST_HUMIDITY" DEC(32,2) NULL,
 "AVE_HUMIDITY" DEC(32,2) NULL,
 "MAX_WINDS" DEC(32,2) NULL,
 "MIN_WINDS" DEC(32,2) NULL,
 "AVE_WINDS" DEC(32,2) NULL,
 "HIGHEST_COMFORT" DEC(32,2) NULL,
 "LOWEST_COMFORT" DEC(32,2) NULL,
 "AVE_COMFORT" DEC(32,2) NULL,
 "HIGHEST_EFFECTIVE_TEMPERATURE" DEC(32,2) NULL,
 "LOWEST_EFFECTIVE_TEMPERATURE" DEC(32,2) NULL,
 "AVE_EFFECTIVE_TEMPERATURE" DEC(32,2) NULL,
 "MAX_COLDNESS" DEC(32,2) NULL,
 "MIN_COLDNESS" DEC(32,2) NULL,
 "AVE_COLDNESS" DEC(32,2) NULL,
 "MAX_TEMPERATURE_HUMIDITY" DEC(32,2) NULL,
 "MIN_TEMPERATURE_HUMIDITY" DEC(32,2) NULL,
 "AVE_TEMPERATURE_HUMIDITY" DEC(32,2) NULL,
 "RAINFALL" DEC(32,2) NULL,
 "CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NULL,
 "UPDATETIME" TIMESTAMP(6) NULL
);

CREATE TABLE "WEATHER_FEATURE_CITY_MONTH_HIS_SERVICE"
(
 "ID" VARCHAR(32) NOT NULL,
 "CITY_ID" VARCHAR(32) NOT NULL,
 "YEAR" VARCHAR(4) NOT NULL,
 "MONTH" VARCHAR(2) NOT NULL,
 "HIGHEST_TEMPERATURE" DEC(10,2) NULL,
 "LOWEST_TEMPERATURE" DEC(32,2) NULL,
 "AVE_TEMPERATURE" DEC(32,2) NULL,
 "HIGHEST_HUMIDITY" DEC(32,2) NULL,
 "LOWEST_HUMIDITY" DEC(32,2) NULL,
 "AVE_HUMIDITY" DEC(32,2) NULL,
 "MAX_WINDS" DEC(32,2) NULL,
 "MIN_WINDS" DEC(32,2) NULL,
 "AVE_WINDS" DEC(32,2) NULL,
 "HIGHEST_COMFORT" DEC(32,2) NULL,
 "LOWEST_COMFORT" DEC(32,2) NULL,
 "AVE_COMFORT" DEC(32,2) NULL,
 "HIGHEST_EFFECTIVE_TEMPERATURE" DEC(32,2) NULL,
 "LOWEST_EFFECTIVE_TEMPERATURE" DEC(32,2) NULL,
 "AVE_EFFECTIVE_TEMPERATURE" DEC(32,2) NULL,
 "MAX_COLDNESS" DEC(32,2) NULL,
 "MIN_COLDNESS" DEC(32,2) NULL,
 "AVE_COLDNESS" DEC(32,2) NULL,
 "MAX_TEMPERATURE_HUMIDITY" DEC(32,2) NULL,
 "MIN_TEMPERATURE_HUMIDITY" DEC(32,2) NULL,
 "AVE_TEMPERATURE_HUMIDITY" DEC(32,2) NULL,
 "RAINFALL" DEC(32,2) NULL,
 "CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NULL,
 "UPDATETIME" TIMESTAMP(6) NULL
);

CREATE TABLE "WEATHER_FEATURE_CITY_DAY_HIS_SERVICE"
(
 "ID" VARCHAR(32) NOT NULL,
 "DATE" DATE NOT NULL,
 "CITY_ID" VARCHAR(32) NOT NULL,
 "HIGHEST_TEMPERATURE" DEC(10,2) NULL,
 "LOWEST_TEMPERATURE" DEC(32,2) NULL,
 "AVE_TEMPERATURE" DEC(32,2) NULL,
 "HIGHEST_HUMIDITY" DEC(32,2) NULL,
 "LOWEST_HUMIDITY" DEC(32,2) NULL,
 "AVE_HUMIDITY" DEC(32,2) NULL,
 "MAX_WINDS" DEC(32,2) NULL,
 "MIN_WINDS" DEC(32,2) NULL,
 "AVE_WINDS" DEC(32,2) NULL,
 "HIGHEST_COMFORT" DEC(32,2) NULL,
 "LOWEST_COMFORT" DEC(32,2) NULL,
 "AVE_COMFORT" DEC(32,2) NULL,
 "HIGHEST_EFFECTIVE_TEMPERATURE" DEC(32,2) NULL,
 "LOWEST_EFFECTIVE_TEMPERATURE" DEC(32,2) NULL,
 "AVE_EFFECTIVE_TEMPERATURE" DEC(32,2) NULL,
 "MAX_COLDNESS" DEC(32,2) NULL,
 "MIN_COLDNESS" DEC(32,2) NULL,
 "AVE_COLDNESS" DEC(32,2) NULL,
 "MAX_TEMPERATURE_HUMIDITY" DEC(32,2) NULL,
 "MIN_TEMPERATURE_HUMIDITY" DEC(32,2) NULL,
 "AVE_TEMPERATURE_HUMIDITY" DEC(32,2) NULL,
 "RAINFALL" DEC(32,2) NULL,
 "CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NULL,
 "UPDATETIME" TIMESTAMP(6) NULL,
 "RECENTLY_RAINFALL" DEC(32,2) NULL,
 "AVE_WINDDIRECTION" INTEGER DEFAULT NULL
 NULL
);

CREATE TABLE "WEATHER_FEATURE_CITY_DAY_FC_SERVICE"
(
 "ID" VARCHAR(32) NOT NULL,
 "DATE" DATE NOT NULL,
 "CITY_ID" VARCHAR(32) NOT NULL,
 "HIGHEST_TEMPERATURE" DEC(10,2) NULL,
 "LOWEST_TEMPERATURE" DEC(32,2) NULL,
 "AVE_TEMPERATURE" DEC(32,2) NULL,
 "HIGHEST_HUMIDITY" DEC(32,2) NULL,
 "LOWEST_HUMIDITY" DEC(32,2) NULL,
 "AVE_HUMIDITY" DEC(32,2) NULL,
 "MAX_WINDS" DEC(32,2) NULL,
 "MIN_WINDS" DEC(32,2) NULL,
 "AVE_WINDS" DEC(32,2) NULL,
 "HIGHEST_COMFORT" DEC(32,2) NULL,
 "LOWEST_COMFORT" DEC(32,2) NULL,
 "AVE_COMFORT" DEC(32,2) NULL,
 "HIGHEST_EFFECTIVE_TEMPERATURE" DEC(32,2) NULL,
 "LOWEST_EFFECTIVE_TEMPERATURE" DEC(32,2) NULL,
 "AVE_EFFECTIVE_TEMPERATURE" DEC(32,2) NULL,
 "MAX_COLDNESS" DEC(32,2) NULL,
 "MIN_COLDNESS" DEC(32,2) NULL,
 "AVE_COLDNESS" DEC(32,2) NULL,
 "MAX_TEMPERATURE_HUMIDITY" DEC(32,2) NULL,
 "MIN_TEMPERATURE_HUMIDITY" DEC(32,2) NULL,
 "AVE_TEMPERATURE_HUMIDITY" DEC(32,2) NULL,
 "RAINFALL" DEC(32,2) NULL,
 "CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NULL,
 "UPDATETIME" TIMESTAMP(6) NULL
);

CREATE TABLE "WEATHER_CITY_HIS_BASIC"
(
 "ID" VARCHAR(32) NOT NULL,
 "DATE" DATE NOT NULL,
 "CITY_ID" VARCHAR(32) NOT NULL,
 "TYPE" TINYINT NULL,
 "T0000" DEC(32,2) NULL,
 "T0015" DEC(32,2) NULL,
 "T0030" DEC(32,2) NULL,
 "T0045" DEC(32,2) NULL,
 "T0100" DEC(32,2) NULL,
 "T0115" DEC(32,2) NULL,
 "T0130" DEC(32,2) NULL,
 "T0145" DEC(32,2) NULL,
 "T0200" DEC(32,2) NULL,
 "T0215" DEC(32,2) NULL,
 "T0230" DEC(32,2) NULL,
 "T0245" DEC(32,2) NULL,
 "T0300" DEC(32,2) NULL,
 "T0315" DEC(32,2) NULL,
 "T0330" DEC(32,2) NULL,
 "T0345" DEC(32,2) NULL,
 "T0400" DEC(32,2) NULL,
 "T0415" DEC(32,2) NULL,
 "T0430" DEC(32,2) NULL,
 "T0445" DEC(32,2) NULL,
 "T0500" DEC(32,2) NULL,
 "T0515" DEC(32,2) NULL,
 "T0530" DEC(32,2) NULL,
 "T0545" DEC(32,2) NULL,
 "T0600" DEC(32,2) NULL,
 "T0615" DEC(32,2) NULL,
 "T0630" DEC(32,2) NULL,
 "T0645" DEC(32,2) NULL,
 "T0700" DEC(32,2) NULL,
 "T0715" DEC(32,2) NULL,
 "T0730" DEC(32,2) NULL,
 "T0745" DEC(32,2) NULL,
 "T0800" DEC(32,2) NULL,
 "T0815" DEC(32,2) NULL,
 "T0830" DEC(32,2) NULL,
 "T0845" DEC(32,2) NULL,
 "T0900" DEC(32,2) NULL,
 "T0915" DEC(32,2) NULL,
 "T0930" DEC(32,2) NULL,
 "T0945" DEC(32,2) NULL,
 "T1000" DEC(32,2) NULL,
 "T1015" DEC(32,2) NULL,
 "T1030" DEC(32,2) NULL,
 "T1045" DEC(32,2) NULL,
 "T1100" DEC(32,2) NULL,
 "T1115" DEC(32,2) NULL,
 "T1130" DEC(32,2) NULL,
 "T1145" DEC(32,2) NULL,
 "T1200" DEC(32,2) NULL,
 "T1215" DEC(32,2) NULL,
 "T1230" DEC(32,2) NULL,
 "T1245" DEC(32,2) NULL,
 "T1300" DEC(32,2) NULL,
 "T1315" DEC(32,2) NULL,
 "T1330" DEC(32,2) NULL,
 "T1345" DEC(32,2) NULL,
 "T1400" DEC(32,2) NULL,
 "T1415" DEC(32,2) NULL,
 "T1430" DEC(32,2) NULL,
 "T1445" DEC(32,2) NULL,
 "T1500" DEC(32,2) NULL,
 "T1515" DEC(32,2) NULL,
 "T1530" DEC(32,2) NULL,
 "T1545" DEC(32,2) NULL,
 "T1600" DEC(32,2) NULL,
 "T1615" DEC(32,2) NULL,
 "T1630" DEC(32,2) NULL,
 "T1645" DEC(32,2) NULL,
 "T1700" DEC(32,2) NULL,
 "T1715" DEC(32,2) NULL,
 "T1730" DEC(32,2) NULL,
 "T1745" DEC(32,2) NULL,
 "T1800" DEC(32,2) NULL,
 "T1815" DEC(32,2) NULL,
 "T1830" DEC(32,2) NULL,
 "T1845" DEC(32,2) NULL,
 "T1900" DEC(32,2) NULL,
 "T1915" DEC(32,2) NULL,
 "T1930" DEC(32,2) NULL,
 "T1945" DEC(32,2) NULL,
 "T2000" DEC(32,2) NULL,
 "T2015" DEC(32,2) NULL,
 "T2030" DEC(32,2) NULL,
 "T2045" DEC(32,2) NULL,
 "T2100" DEC(32,2) NULL,
 "T2115" DEC(32,2) NULL,
 "T2130" DEC(32,2) NULL,
 "T2145" DEC(32,2) NULL,
 "T2200" DEC(32,2) NULL,
 "T2215" DEC(32,2) NULL,
 "T2230" DEC(32,2) NULL,
 "T2245" DEC(32,2) NULL,
 "T2300" DEC(32,2) NULL,
 "T2315" DEC(32,2) NULL,
 "T2330" DEC(32,2) NULL,
 "T2345" DEC(32,2) NULL,
 "T2400" DEC(32,2) NULL,
 "CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NULL,
 "UPDATETIME" TIMESTAMP(6) NULL
);

CREATE TABLE "WEATHER_CITY_FC_BASIC_MODIFY"
(
 "ID" VARCHAR(32) NOT NULL,
 "DATE" DATE NOT NULL,
 "CITY_ID" VARCHAR(32) NOT NULL,
 "TYPE" TINYINT NULL,
 "T0000" DEC(32,2) NULL,
 "T0015" DEC(32,2) NULL,
 "T0030" DEC(32,2) NULL,
 "T0045" DEC(32,2) NULL,
 "T0100" DEC(32,2) NULL,
 "T0115" DEC(32,2) NULL,
 "T0130" DEC(32,2) NULL,
 "T0145" DEC(32,2) NULL,
 "T0200" DEC(32,2) NULL,
 "T0215" DEC(32,2) NULL,
 "T0230" DEC(32,2) NULL,
 "T0245" DEC(32,2) NULL,
 "T0300" DEC(32,2) NULL,
 "T0315" DEC(32,2) NULL,
 "T0330" DEC(32,2) NULL,
 "T0345" DEC(32,2) NULL,
 "T0400" DEC(32,2) NULL,
 "T0415" DEC(32,2) NULL,
 "T0430" DEC(32,2) NOT NULL,
 "T0445" DEC(32,2) NULL,
 "T0500" DEC(32,2) NULL,
 "T0515" DEC(32,2) NULL,
 "T0530" DEC(32,2) NULL,
 "T0545" DEC(32,2) NULL,
 "T0600" DEC(32,2) NULL,
 "T0615" DEC(32,2) NULL,
 "T0630" DEC(32,2) NULL,
 "T0645" DEC(32,2) NULL,
 "T0700" DEC(32,2) NULL,
 "T0715" DEC(32,2) NULL,
 "T0730" DEC(32,2) NULL,
 "T0745" DEC(32,2) NULL,
 "T0800" DEC(32,2) NULL,
 "T0815" DEC(32,2) NULL,
 "T0830" DEC(32,2) NULL,
 "T0845" DEC(32,2) NULL,
 "T0900" DEC(32,2) NULL,
 "T0915" DEC(32,2) NULL,
 "T0930" DEC(32,2) NULL,
 "T0945" DEC(32,2) NULL,
 "T1000" DEC(32,2) NULL,
 "T1015" DEC(32,2) NULL,
 "T1030" DEC(32,2) NULL,
 "T1045" DEC(32,2) NULL,
 "T1100" DEC(32,2) NULL,
 "T1115" DEC(32,2) NULL,
 "T1130" DEC(32,2) NULL,
 "T1145" DEC(32,2) NULL,
 "T1200" DEC(32,2) NULL,
 "T1215" DEC(32,2) NULL,
 "T1230" DEC(32,2) NULL,
 "T1245" DEC(32,2) NULL,
 "T1300" DEC(32,2) NULL,
 "T1315" DEC(32,2) NULL,
 "T1330" DEC(32,2) NULL,
 "T1345" DEC(32,2) NULL,
 "T1400" DEC(32,2) NULL,
 "T1415" DEC(32,2) NULL,
 "T1430" DEC(32,2) NULL,
 "T1445" DEC(32,2) NULL,
 "T1500" DEC(32,2) NULL,
 "T1515" DEC(32,2) NULL,
 "T1530" DEC(32,2) NULL,
 "T1545" DEC(32,2) NULL,
 "T1600" DEC(32,2) NULL,
 "T1615" DEC(32,2) NULL,
 "T1630" DEC(32,2) NULL,
 "T1645" DEC(32,2) NULL,
 "T1700" DEC(32,2) NULL,
 "T1715" DEC(32,2) NULL,
 "T1730" DEC(32,2) NULL,
 "T1745" DEC(32,2) NULL,
 "T1800" DEC(32,2) NULL,
 "T1815" DEC(32,2) NULL,
 "T1830" DEC(32,2) NULL,
 "T1845" DEC(32,2) NULL,
 "T1900" DEC(32,2) NULL,
 "T1915" DEC(32,2) NULL,
 "T1930" DEC(32,2) NULL,
 "T1945" DEC(32,2) NULL,
 "T2000" DEC(32,2) NULL,
 "T2015" DEC(32,2) NULL,
 "T2030" DEC(32,2) NULL,
 "T2045" DEC(32,2) NULL,
 "T2100" DEC(32,2) NULL,
 "T2115" DEC(32,2) NULL,
 "T2130" DEC(32,2) NULL,
 "T2145" DEC(32,2) NULL,
 "T2200" DEC(32,2) NULL,
 "T2215" DEC(32,2) NULL,
 "T2230" DEC(32,2) NULL,
 "T2245" DEC(32,2) NULL,
 "T2300" DEC(32,2) NULL,
 "T2315" DEC(32,2) NULL,
 "T2330" DEC(32,2) NULL,
 "T2345" DEC(32,2) NULL,
 "T2400" DEC(32,2) NULL,
 "CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NULL,
 "UPDATETIME" TIMESTAMP(6) NULL
);

CREATE TABLE "WEATHER_CITY_FC_BASIC"
(
 "ID" VARCHAR(32) NOT NULL,
 "DATE" DATE NOT NULL,
 "CITY_ID" VARCHAR(32) NOT NULL,
 "TYPE" TINYINT NULL,
 "T0000" DEC(32,2) NULL,
 "T0015" DEC(32,2) NULL,
 "T0030" DEC(32,2) NULL,
 "T0045" DEC(32,2) NULL,
 "T0100" DEC(32,2) NULL,
 "T0115" DEC(32,2) NULL,
 "T0130" DEC(32,2) NULL,
 "T0145" DEC(32,2) NULL,
 "T0200" DEC(32,2) NULL,
 "T0215" DEC(32,2) NULL,
 "T0230" DEC(32,2) NULL,
 "T0245" DEC(32,2) NULL,
 "T0300" DEC(32,2) NULL,
 "T0315" DEC(32,2) NULL,
 "T0330" DEC(32,2) NULL,
 "T0345" DEC(32,2) NULL,
 "T0400" DEC(32,2) NULL,
 "T0415" DEC(32,2) NULL,
 "T0430" DEC(32,2) NULL,
 "T0445" DEC(32,2) NULL,
 "T0500" DEC(32,2) NULL,
 "T0515" DEC(32,2) NULL,
 "T0530" DEC(32,2) NULL,
 "T0545" DEC(32,2) NULL,
 "T0600" DEC(32,2) NULL,
 "T0615" DEC(32,2) NULL,
 "T0630" DEC(32,2) NULL,
 "T0645" DEC(32,2) NULL,
 "T0700" DEC(32,2) NULL,
 "T0715" DEC(32,2) NULL,
 "T0730" DEC(32,2) NULL,
 "T0745" DEC(32,2) NULL,
 "T0800" DEC(32,2) NULL,
 "T0815" DEC(32,2) NULL,
 "T0830" DEC(32,2) NULL,
 "T0845" DEC(32,2) NULL,
 "T0900" DEC(32,2) NULL,
 "T0915" DEC(32,2) NULL,
 "T0930" DEC(32,2) NULL,
 "T0945" DEC(32,2) NULL,
 "T1000" DEC(32,2) NULL,
 "T1015" DEC(32,2) NULL,
 "T1030" DEC(32,2) NULL,
 "T1045" DEC(32,2) NULL,
 "T1100" DEC(32,2) NULL,
 "T1115" DEC(32,2) NULL,
 "T1130" DEC(32,2) NULL,
 "T1145" DEC(32,2) NULL,
 "T1200" DEC(32,2) NULL,
 "T1215" DEC(32,2) NULL,
 "T1230" DEC(32,2) NULL,
 "T1245" DEC(32,2) NULL,
 "T1300" DEC(32,2) NULL,
 "T1315" DEC(32,2) NULL,
 "T1330" DEC(32,2) NULL,
 "T1345" DEC(32,2) NULL,
 "T1400" DEC(32,2) NULL,
 "T1415" DEC(32,2) NULL,
 "T1430" DEC(32,2) NULL,
 "T1445" DEC(32,2) NULL,
 "T1500" DEC(32,2) NULL,
 "T1515" DEC(32,2) NULL,
 "T1530" DEC(32,2) NULL,
 "T1545" DEC(32,2) NULL,
 "T1600" DEC(32,2) NULL,
 "T1615" DEC(32,2) NULL,
 "T1630" DEC(32,2) NULL,
 "T1645" DEC(32,2) NULL,
 "T1700" DEC(32,2) NULL,
 "T1715" DEC(32,2) NULL,
 "T1730" DEC(32,2) NULL,
 "T1745" DEC(32,2) NULL,
 "T1800" DEC(32,2) NULL,
 "T1815" DEC(32,2) NULL,
 "T1830" DEC(32,2) NULL,
 "T1845" DEC(32,2) NULL,
 "T1900" DEC(32,2) NULL,
 "T1915" DEC(32,2) NULL,
 "T1930" DEC(32,2) NULL,
 "T1945" DEC(32,2) NULL,
 "T2000" DEC(32,2) NULL,
 "T2015" DEC(32,2) NULL,
 "T2030" DEC(32,2) NULL,
 "T2045" DEC(32,2) NULL,
 "T2100" DEC(32,2) NULL,
 "T2115" DEC(32,2) NULL,
 "T2130" DEC(32,2) NULL,
 "T2145" DEC(32,2) NULL,
 "T2200" DEC(32,2) NULL,
 "T2215" DEC(32,2) NULL,
 "T2230" DEC(32,2) NULL,
 "T2245" DEC(32,2) NULL,
 "T2300" DEC(32,2) NULL,
 "T2315" DEC(32,2) NULL,
 "T2330" DEC(32,2) NULL,
 "T2345" DEC(32,2) NULL,
 "T2400" DEC(32,2) NULL,
 "CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NULL,
 "UPDATETIME" TIMESTAMP(6) NULL
);

CREATE TABLE "TSIE_USER_ROLE"
(
 "ID" VARCHAR(32) NOT NULL,
 "USERID" VARCHAR(32) NOT NULL,
 "ROLEID" VARCHAR(32) NOT NULL
);

CREATE TABLE "TSIE_USER_OAUTHBINDING"
(
 "ID" VARCHAR(255) NOT NULL,
 "USERID" VARCHAR(255) NULL,
 "OAUTHOPENID" VARCHAR(255) NULL,
 "OAUTHSERVERNAME" VARCHAR(255) NULL,
 "OAUTHSERVERCODE" VARCHAR(255) NULL,
 "OAUTHSERVERDESC" VARCHAR(255) NULL,
 "BINDINGTYPE" VARCHAR(255) NULL,
 "BINDINGDATE" TIMESTAMP(6) NULL
);

CREATE TABLE "TSIE_USER"
(
 "ID" VARCHAR(32) NOT NULL,
 "USERNAME" VARCHAR(32) NOT NULL,
 "ENABLE" BIGINT DEFAULT 1
 NOT NULL,
 "PASSWD" VARCHAR(64) NOT NULL,
 "NICKNAME" VARCHAR(20) NULL,
 "EMAIL" VARCHAR(50) NULL,
 "PHONENO" VARCHAR(32) DEFAULT NULL
 NULL,
 "PASSWD_CHANGE_PROMPT" INTEGER DEFAULT 0
 NULL,
 "EXPIRED_DATE" DATE NULL,
 "LOGIN_MODE" INTEGER NULL
);

CREATE TABLE "TSIE_UKEY"
(
 "ID" VARCHAR(255) NOT NULL,
 "USERID" VARCHAR(32) NULL,
 "USERNAME" VARCHAR(32) NULL,
 "UKEY" VARCHAR(32) NOT NULL,
 "SN" VARCHAR(32) NOT NULL,
 "CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL,
 "BINDINGTIME" TIMESTAMP(6) DEFAULT '1900-01-01 00:00:00'
 NOT NULL
);

CREATE TABLE "TSIE_ROLE"
(
 "ID" VARCHAR(32) NOT NULL,
 "ROLE" VARCHAR(40) NOT NULL,
 "DESCPT" VARCHAR(240) NOT NULL,
 "CATEGORY" VARCHAR(140) NOT NULL
);

CREATE TABLE "TSIE_RANDOMCODE_HISTORY"
(
 "ID" VARCHAR(32) NOT NULL,
 "IP" VARCHAR(32) NULL,
 "CONTACT_VALUE" VARCHAR(32) NOT NULL,
 "CREATE_TIME" TIMESTAMP(6) NOT NULL,
 "COUNT_TIME" INTEGER NULL
);

CREATE TABLE "TSIE_OPERATE_LOG"
(
 "ID" VARCHAR(32) NOT NULL,
 "USER_NAME" VARCHAR(20) NULL,
 "REMOTE_ADDR" VARCHAR(32) NULL,
 "LOG_EXCEPTION" VARCHAR(256) NULL,
 "TITLE" VARCHAR(30) NULL,
 "REQUEST_URI" VARCHAR(120) NULL,
 "LOG_TYPE" VARCHAR(32) NULL,
 "DESCRIPTION" VARCHAR(200) NULL,
 "CREATE_DATE" TIMESTAMP(6) NULL
);

CREATE TABLE "TSIE_MESSAGE"
(
 "ID" VARCHAR(32) NOT NULL,
 "TITLE" VARCHAR(100) NULL,
 "CONTENT" TEXT NULL,
 "MODULE_ID" VARCHAR(32) NULL,
 "MESSAGE_LEVEL" INTEGER NULL,
 "URL" VARCHAR(200) NULL,
 "CREATETIME" TIMESTAMP(6) NULL,
 "ENDTIME" TIMESTAMP(6) NULL,
 "DELETEFLAG" INTEGER DEFAULT 0
 NOT NULL
);

CREATE TABLE "TSIE_MENU_ROLE"
(
 "ID" VARCHAR(32) NOT NULL,
 "MENUID" VARCHAR(32) NOT NULL,
 "ROLEID" VARCHAR(32) NOT NULL
);

CREATE TABLE "TSIE_MENU"
(
 "ID" VARCHAR(32) NOT NULL,
 "NAME" VARCHAR(200) NOT NULL,
 "TYPE" INTEGER NOT NULL,
 "MENUPATH" VARCHAR(200) NULL,
 "PARENTID" VARCHAR(32) DEFAULT '0'
 NOT NULL,
 "DESCRIPTION" VARCHAR(200) NOT NULL,
 "TARGETTYPE" VARCHAR(32) NULL,
 "REL" VARCHAR(200) NULL,
 "SORT" INTEGER NULL,
 "PLATFORM" VARCHAR(32) DEFAULT NULL
);

CREATE TABLE "TSIE_LOGIN_FAIL_COUNT_RECOND"
(
 "ID" VARCHAR(255) NOT NULL,
 "USER_NAME" VARCHAR(64) NOT NULL,
 "IP" VARCHAR(32) NULL,
 "CREATE_TIME" TIMESTAMP(6) NULL,
 "COUNT_TIME" INTEGER DEFAULT 0
 NULL
);

CREATE TABLE "TSIE_GROUP_USER"
(
 "ID" VARCHAR(32) NOT NULL,
 "USERID" VARCHAR(32) NOT NULL,
 "GROUPID" VARCHAR(32) NOT NULL
);

CREATE TABLE "TSIE_GROUP_ROLE"
(
 "ID" VARCHAR(32) NOT NULL,
 "GROUPID" VARCHAR(32) NOT NULL,
 "ROLEID" VARCHAR(32) NOT NULL
);

CREATE TABLE "TSIE_GROUP"
(
 "ID" VARCHAR(32) NOT NULL,
 "GROUPNAME" VARCHAR(50) NOT NULL,
 "DESCRIPTION" VARCHAR(128) DEFAULT NULL
 NULL
);

CREATE TABLE "TSIE_DB_PARAMETER"
(
 "PARAM_ID" VARCHAR(32) NOT NULL,
 "PARAM_NAME" VARCHAR(64) NULL,
 "MANAGABLE" INTEGER NULL,
 "MNG_LEVEL" VARCHAR(16) NULL,
 "PARAM_TYPE" VARCHAR(16) NULL,
 "CREATE_DATE" TIMESTAMP(6) NULL,
 "STATUS" INTEGER NULL,
 "PARAM_VALUE" VARCHAR(32) NOT NULL,
 "DESCRIPTION" VARCHAR(256) NULL
);

CREATE TABLE "TSIE_COMMON_RANDOMCODE"
(
 "ID" VARCHAR(32) NOT NULL,
 "TYPE" BIGINT NULL,
 "PHONE" VARCHAR(11) NULL,
 "EMAIL" VARCHAR(32) NULL,
 "RANDOM_CODE" VARCHAR(6) NOT NULL,
 "CREATE_TIME" TIMESTAMP(6) NOT NULL,
 "VALID_TIME" TIMESTAMP(6) NULL,
 "STATUS" BIGINT NULL
);


 CREATE TABLE "LF_SHOW"."LF_SHOW"."TSIE_ORGANIZATION"(
"ID" VARCHAR(50) NOT NULL,
"GROUP_ID" VARCHAR(50),
"PARENT_ID" VARCHAR(50),
"ORDER_BY_ASC" INTEGER,
"ORG_TYPE" VARCHAR(50))
 STORAGE( INITIAL 1 , NEXT 1 , MINEXTENTS 1 , on "PRIMARY", FILLFACTOR 0 ) ;


 CREATE TABLE "LF_SHOW"."LF_SHOW"."TSIE_DB_DICTITEM"(
"ID" INTEGER NOT NULL,
"DICT_CODE" VARCHAR(50),
"DICT_NAME" VARCHAR(50),
"GROUP_CODE" VARCHAR(50),
"DESCRIPTION" VARCHAR(50),
"SORT_ORDER" TINYINT,
"STATUS" TINYINT,
"CREATE_DATE" TIMESTAMP(0),
"UPDATE_DATE" TIMESTAMP(0))
 STORAGE( INITIAL 1 , NEXT 1 , MINEXTENTS 1 , on "PRIMARY", FILLFACTOR 0 ) ;



CREATE TABLE "STATISTICS_WEATHER_CITY_DAY_FC_STAT"
(
 "ID" VARCHAR(32) NOT NULL,
 "CITY_ID" VARCHAR(32) NOT NULL,
 "TYPE" INTEGER NOT NULL,
 "DATE" DATE NULL,
 "ACCURACY" DEC(10,4) NULL,
 "PASS" DEC(10,4) NULL,
 "STANDARD_ACCURACY" DEC(10,4) NULL,
 "CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NULL,
 "UPDATETIME" TIMESTAMP(6) NULL
);

CREATE TABLE "STATISTICS_SYNTHESIZE_WEATHER_CITY_DAY_HIS"
(
 "ID" VARCHAR(32) NOT NULL,
 "DATE" DATE NOT NULL,
 "CITY_ID" VARCHAR(32) NOT NULL,
 "TYPE" TINYINT NOT NULL,
 "T0000" DEC(10,4) NULL,
 "T0015" DEC(10,4) NULL,
 "T0030" DEC(10,4) NULL,
 "T0045" DEC(10,4) NULL,
 "T0100" DEC(10,4) NULL,
 "T0115" DEC(10,4) NULL,
 "T0130" DEC(10,4) NULL,
 "T0145" DEC(10,4) NULL,
 "T0200" DEC(10,4) NULL,
 "T0215" DEC(10,4) NULL,
 "T0230" DEC(10,4) NULL,
 "T0245" DEC(10,4) NULL,
 "T0300" DEC(10,4) NULL,
 "T0315" DEC(10,4) NULL,
 "T0330" DEC(10,4) NULL,
 "T0345" DEC(10,4) NULL,
 "T0400" DEC(10,4) NULL,
 "T0415" DEC(10,4) NULL,
 "T0430" DEC(10,4) NULL,
 "T0445" DEC(10,4) NULL,
 "T0500" DEC(10,4) NULL,
 "T0515" DEC(10,4) NULL,
 "T0530" DEC(10,4) NULL,
 "T0545" DEC(10,4) NULL,
 "T0600" DEC(10,4) NULL,
 "T0615" DEC(10,4) NULL,
 "T0630" DEC(10,4) NULL,
 "T0645" DEC(10,4) NULL,
 "T0700" DEC(10,4) NULL,
 "T0715" DEC(10,4) NULL,
 "T0730" DEC(10,4) NULL,
 "T0745" DEC(10,4) NULL,
 "T0800" DEC(10,4) NULL,
 "T0815" DEC(10,4) NULL,
 "T0830" DEC(10,4) NULL,
 "T0845" DEC(10,4) NULL,
 "T0900" DEC(10,4) NULL,
 "T0915" DEC(10,4) NULL,
 "T0930" DEC(10,4) NULL,
 "T0945" DEC(10,4) NULL,
 "T1000" DEC(10,4) NULL,
 "T1015" DEC(10,4) NULL,
 "T1030" DEC(10,4) NULL,
 "T1045" DEC(10,4) NULL,
 "T1100" DEC(10,4) NULL,
 "T1115" DEC(10,4) NULL,
 "T1130" DEC(10,4) NULL,
 "T1145" DEC(10,4) NULL,
 "T1200" DEC(10,4) NULL,
 "T1215" DEC(10,4) NULL,
 "T1230" DEC(10,4) NULL,
 "T1245" DEC(10,4) NULL,
 "T1300" DEC(10,4) NULL,
 "T1315" DEC(10,4) NULL,
 "T1330" DEC(10,4) NULL,
 "T1345" DEC(10,4) NULL,
 "T1400" DEC(10,4) NULL,
 "T1415" DEC(10,4) NULL,
 "T1430" DEC(10,4) NULL,
 "T1445" DEC(10,4) NULL,
 "T1500" DEC(10,4) NULL,
 "T1515" DEC(10,4) NULL,
 "T1530" DEC(10,4) NULL,
 "T1545" DEC(10,4) NULL,
 "T1600" DEC(10,4) NULL,
 "T1615" DEC(10,4) NULL,
 "T1630" DEC(10,4) NULL,
 "T1645" DEC(10,4) NULL,
 "T1700" DEC(10,4) NULL,
 "T1715" DEC(10,4) NULL,
 "T1730" DEC(10,4) NULL,
 "T1745" DEC(10,4) NULL,
 "T1800" DEC(10,4) NULL,
 "T1815" DEC(10,4) NULL,
 "T1830" DEC(10,4) NULL,
 "T1845" DEC(10,4) NULL,
 "T1900" DEC(10,4) NULL,
 "T1915" DEC(10,4) NULL,
 "T1930" DEC(10,4) NULL,
 "T1945" DEC(10,4) NULL,
 "T2000" DEC(10,4) NULL,
 "T2015" DEC(10,4) NULL,
 "T2030" DEC(10,4) NULL,
 "T2045" DEC(10,4) NULL,
 "T2100" DEC(10,4) NULL,
 "T2115" DEC(10,4) NULL,
 "T2130" DEC(10,4) NULL,
 "T2145" DEC(10,4) NULL,
 "T2200" DEC(10,4) NULL,
 "T2215" DEC(10,4) NULL,
 "T2230" DEC(10,4) NULL,
 "T2245" DEC(10,4) NULL,
 "T2300" DEC(10,4) NULL,
 "T2315" DEC(10,4) NULL,
 "T2330" DEC(10,4) NULL,
 "T2345" DEC(10,4) NULL,
 "T2400" DEC(10,4) NULL,
 "CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NULL,
 "UPDATETIME" TIMESTAMP(6) NULL
);

CREATE TABLE "STATISTICS_SYNTHESIZE_WEATHER_CITY_DAY_FC"
(
 "ID" VARCHAR(32) NOT NULL,
 "DATE" DATE NOT NULL,
 "CITY_ID" VARCHAR(32) NOT NULL,
 "TYPE" TINYINT NOT NULL,
 "T0000" DEC(10,4) NULL,
 "T0015" DEC(10,4) NULL,
 "T0030" DEC(10,4) NULL,
 "T0045" DEC(10,4) NULL,
 "T0100" DEC(10,4) NULL,
 "T0115" DEC(10,4) NULL,
 "T0130" DEC(10,4) NULL,
 "T0145" DEC(10,4) NULL,
 "T0200" DEC(10,4) NULL,
 "T0215" DEC(10,4) NULL,
 "T0230" DEC(10,4) NULL,
 "T0245" DEC(10,4) NULL,
 "T0300" DEC(10,4) NULL,
 "T0315" DEC(10,4) NULL,
 "T0330" DEC(10,4) NULL,
 "T0345" DEC(10,4) NULL,
 "T0400" DEC(10,4) NULL,
 "T0415" DEC(10,4) NULL,
 "T0430" DEC(10,4) NULL,
 "T0445" DEC(10,4) NULL,
 "T0500" DEC(10,4) NULL,
 "T0515" DEC(10,4) NULL,
 "T0530" DEC(10,4) NULL,
 "T0545" DEC(10,4) NULL,
 "T0600" DEC(10,4) NULL,
 "T0615" DEC(10,4) NULL,
 "T0630" DEC(10,4) NULL,
 "T0645" DEC(10,4) NULL,
 "T0700" DEC(10,4) NULL,
 "T0715" DEC(10,4) NULL,
 "T0730" DEC(10,4) NULL,
 "T0745" DEC(10,4) NULL,
 "T0800" DEC(10,4) NULL,
 "T0815" DEC(10,4) NULL,
 "T0830" DEC(10,4) NULL,
 "T0845" DEC(10,4) NULL,
 "T0900" DEC(10,4) NULL,
 "T0915" DEC(10,4) NULL,
 "T0930" DEC(10,4) NULL,
 "T0945" DEC(10,4) NULL,
 "T1000" DEC(10,4) NULL,
 "T1015" DEC(10,4) NULL,
 "T1030" DEC(10,4) NULL,
 "T1045" DEC(10,4) NULL,
 "T1100" DEC(10,4) NULL,
 "T1115" DEC(10,4) NULL,
 "T1130" DEC(10,4) NULL,
 "T1145" DEC(10,4) NULL,
 "T1200" DEC(10,4) NULL,
 "T1215" DEC(10,4) NULL,
 "T1230" DEC(10,4) NULL,
 "T1245" DEC(10,4) NULL,
 "T1300" DEC(10,4) NULL,
 "T1315" DEC(10,4) NULL,
 "T1330" DEC(10,4) NULL,
 "T1345" DEC(10,4) NULL,
 "T1400" DEC(10,4) NULL,
 "T1415" DEC(10,4) NULL,
 "T1430" DEC(10,4) NULL,
 "T1445" DEC(10,4) NULL,
 "T1500" DEC(10,4) NULL,
 "T1515" DEC(10,4) NULL,
 "T1530" DEC(10,4) NULL,
 "T1545" DEC(10,4) NULL,
 "T1600" DEC(10,4) NULL,
 "T1615" DEC(10,4) NULL,
 "T1630" DEC(10,4) NULL,
 "T1645" DEC(10,4) NULL,
 "T1700" DEC(10,4) NULL,
 "T1715" DEC(10,4) NULL,
 "T1730" DEC(10,4) NULL,
 "T1745" DEC(10,4) NULL,
 "T1800" DEC(10,4) NULL,
 "T1815" DEC(10,4) NULL,
 "T1830" DEC(10,4) NULL,
 "T1845" DEC(10,4) NULL,
 "T1900" DEC(10,4) NULL,
 "T1915" DEC(10,4) NULL,
 "T1930" DEC(10,4) NULL,
 "T1945" DEC(10,4) NULL,
 "T2000" DEC(10,4) NULL,
 "T2015" DEC(10,4) NULL,
 "T2030" DEC(10,4) NULL,
 "T2045" DEC(10,4) NULL,
 "T2100" DEC(10,4) NULL,
 "T2115" DEC(10,4) NULL,
 "T2130" DEC(10,4) NULL,
 "T2145" DEC(10,4) NULL,
 "T2200" DEC(10,4) NULL,
 "T2215" DEC(10,4) NULL,
 "T2230" DEC(10,4) NULL,
 "T2245" DEC(10,4) NULL,
 "T2300" DEC(10,4) NULL,
 "T2315" DEC(10,4) NULL,
 "T2330" DEC(10,4) NULL,
 "T2345" DEC(10,4) NULL,
 "T2400" DEC(10,4) NULL,
 "CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NULL,
 "UPDATETIME" TIMESTAMP(6) NULL
);

CREATE TABLE "STATISTICS_CITY_DAY_FC_SERVICE"
(
 "ID" VARCHAR(32) NOT NULL,
 "CITY_ID" VARCHAR(32) NOT NULL,
 "CALIBER_ID" VARCHAR(32) NOT NULL,
 "DATE" DATE NULL,
 "ALGORITHM_ID" VARCHAR(32) NULL,
 "ACCURACY" DEC(10,4) NULL,
 "DEVIATION" DEC(10,4) NULL,
 "PASS" DEC(10,4) NULL,
 "DISPERSION" DEC(10,4) NULL,
 "STANDARD_ACCURACY" DEC(10,4) NULL,
 "REPORT" BIT DEFAULT 0
 NULL,
 "CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NULL,
 "UPDATETIME" TIMESTAMP(6) NULL
);

CREATE TABLE "STATISTICS_ACCURACY_WEATHER_CITY_YEAR_HIS"
(
 "ID" VARCHAR(32) NOT NULL,
 "YEAR" VARCHAR(4) NOT NULL,
 "CITY_ID" VARCHAR(32) NOT NULL,
 "TYPE" TINYINT NOT NULL,
 "MONTH01" DEC(10,4) NULL,
 "MONTH02" DEC(10,4) NULL,
 "MONTH03" DEC(10,4) NULL,
 "MONTH04" DEC(10,4) NULL,
 "MONTH05" DEC(10,4) NULL,
 "MONTH06" DEC(10,4) NULL,
 "MONTH07" DEC(10,4) NULL,
 "MONTH08" DEC(10,4) NULL,
 "MONTH09" DEC(10,4) NULL,
 "MONTH10" DEC(10,4) NULL,
 "MONTH11" DEC(10,4) NULL,
 "MONTH12" DEC(10,4) NULL,
 "AVG" DEC(10,4) NULL,
 "CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NULL,
 "UPDATETIME" TIMESTAMP(6) NULL
);

CREATE TABLE "STATISTICS_ACCURACY_WEATHER_CITY_MONTH_HIS"
(
 "ID" VARCHAR(32) NOT NULL,
 "YEAR" VARCHAR(4) NOT NULL,
 "MONTH" VARCHAR(2) NOT NULL,
 "CITY_ID" VARCHAR(32) NOT NULL,
 "TYPE" TINYINT NOT NULL,
 "DAY01" DEC(10,4) NULL,
 "DAY02" DEC(10,4) NULL,
 "DAY03" DEC(10,4) NULL,
 "DAY04" DEC(10,4) NULL,
 "DAY05" DEC(10,4) NULL,
 "DAY06" DEC(10,4) NULL,
 "DAY07" DEC(10,4) NULL,
 "DAY08" DEC(10,4) NULL,
 "DAY09" DEC(10,4) NULL,
 "DAY10" DEC(10,4) NULL,
 "DAY11" DEC(10,4) NULL,
 "DAY12" DEC(10,4) NULL,
 "DAY13" DEC(10,4) NULL,
 "DAY14" DEC(10,4) NULL,
 "DAY15" DEC(10,4) NULL,
 "DAY16" DEC(10,4) NULL,
 "DAY17" DEC(10,4) NULL,
 "DAY18" DEC(10,4) NULL,
 "DAY19" DEC(10,4) NULL,
 "DAY20" DEC(10,4) NULL,
 "DAY21" DEC(10,4) NULL,
 "DAY22" DEC(10,4) NULL,
 "DAY23" DEC(10,4) NULL,
 "DAY24" DEC(10,4) NULL,
 "DAY25" DEC(10,4) NULL,
 "DAY26" DEC(10,4) NULL,
 "DAY27" DEC(10,4) NULL,
 "DAY28" DEC(10,4) NULL,
 "DAY29" DEC(10,4) NULL,
 "DAY30" DEC(10,4) NULL,
 "DAY31" DEC(10,4) NULL,
 "AVG" DEC(10,4) NULL,
 "CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NULL,
 "UPDATETIME" TIMESTAMP(6) NULL
);

CREATE TABLE "STATISTICS_ACCURACY_WEATHER_CITY_DAY_HIS"
(
 "ID" VARCHAR(32) NOT NULL,
 "DATE" DATE NOT NULL,
 "CITY_ID" VARCHAR(32) NOT NULL,
 "TYPE" TINYINT NOT NULL,
 "T0000" DEC(10,4) NULL,
 "T0015" DEC(10,4) NULL,
 "T0030" DEC(10,4) NULL,
 "T0045" DEC(10,4) NULL,
 "T0100" DEC(10,4) NULL,
 "T0115" DEC(10,4) NULL,
 "T0130" DEC(10,4) NULL,
 "T0145" DEC(10,4) NULL,
 "T0200" DEC(10,4) NULL,
 "T0215" DEC(10,4) NULL,
 "T0230" DEC(10,4) NULL,
 "T0245" DEC(10,4) NULL,
 "T0300" DEC(10,4) NULL,
 "T0315" DEC(10,4) NULL,
 "T0330" DEC(10,4) NULL,
 "T0345" DEC(10,4) NULL,
 "T0400" DEC(10,4) NULL,
 "T0415" DEC(10,4) NULL,
 "T0430" DEC(10,4) NULL,
 "T0445" DEC(10,4) NULL,
 "T0500" DEC(10,4) NULL,
 "T0515" DEC(10,4) NULL,
 "T0530" DEC(10,4) NULL,
 "T0545" DEC(10,4) NULL,
 "T0600" DEC(10,4) NULL,
 "T0615" DEC(10,4) NULL,
 "T0630" DEC(10,4) NULL,
 "T0645" DEC(10,4) NULL,
 "T0700" DEC(10,4) NULL,
 "T0715" DEC(10,4) NULL,
 "T0730" DEC(10,4) NULL,
 "T0745" DEC(10,4) NULL,
 "T0800" DEC(10,4) NULL,
 "T0815" DEC(10,4) NULL,
 "T0830" DEC(10,4) NULL,
 "T0845" DEC(10,4) NULL,
 "T0900" DEC(10,4) NULL,
 "T0915" DEC(10,4) NULL,
 "T0930" DEC(10,4) NULL,
 "T0945" DEC(10,4) NULL,
 "T1000" DEC(10,4) NULL,
 "T1015" DEC(10,4) NULL,
 "T1030" DEC(10,4) NULL,
 "T1045" DEC(10,4) NULL,
 "T1100" DEC(10,4) NULL,
 "T1115" DEC(10,4) NULL,
 "T1130" DEC(10,4) NULL,
 "T1145" DEC(10,4) NULL,
 "T1200" DEC(10,4) NULL,
 "T1215" DEC(10,4) NULL,
 "T1230" DEC(10,4) NULL,
 "T1245" DEC(10,4) NULL,
 "T1300" DEC(10,4) NULL,
 "T1315" DEC(10,4) NULL,
 "T1330" DEC(10,4) NULL,
 "T1345" DEC(10,4) NULL,
 "T1400" DEC(10,4) NULL,
 "T1415" DEC(10,4) NULL,
 "T1430" DEC(10,4) NULL,
 "T1445" DEC(10,4) NULL,
 "T1500" DEC(10,4) NULL,
 "T1515" DEC(10,4) NULL,
 "T1530" DEC(10,4) NULL,
 "T1545" DEC(10,4) NULL,
 "T1600" DEC(10,4) NULL,
 "T1615" DEC(10,4) NULL,
 "T1630" DEC(10,4) NULL,
 "T1645" DEC(10,4) NULL,
 "T1700" DEC(10,4) NULL,
 "T1715" DEC(10,4) NULL,
 "T1730" DEC(10,4) NULL,
 "T1745" DEC(10,4) NULL,
 "T1800" DEC(10,4) NULL,
 "T1815" DEC(10,4) NULL,
 "T1830" DEC(10,4) NULL,
 "T1845" DEC(10,4) NULL,
 "T1900" DEC(10,4) NULL,
 "T1915" DEC(10,4) NULL,
 "T1930" DEC(10,4) NULL,
 "T1945" DEC(10,4) NULL,
 "T2000" DEC(10,4) NULL,
 "T2015" DEC(10,4) NULL,
 "T2030" DEC(10,4) NULL,
 "T2045" DEC(10,4) NULL,
 "T2100" DEC(10,4) NULL,
 "T2115" DEC(10,4) NULL,
 "T2130" DEC(10,4) NULL,
 "T2145" DEC(10,4) NULL,
 "T2200" DEC(10,4) NULL,
 "T2215" DEC(10,4) NULL,
 "T2230" DEC(10,4) NULL,
 "T2245" DEC(10,4) NULL,
 "T2300" DEC(10,4) NULL,
 "T2315" DEC(10,4) NULL,
 "T2330" DEC(10,4) NULL,
 "T2345" DEC(10,4) NULL,
 "T2400" DEC(10,4) NULL,
 "AVG" DEC(10,4) NULL,
 "CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NULL,
 "UPDATETIME" TIMESTAMP(6) NULL
);

CREATE TABLE "SIMILAR_DAY_BASIC"
(
 "ID" VARCHAR(32) NOT NULL,
 "CITY_ID" VARCHAR(32) NOT NULL,
 "CALIBER_ID" VARCHAR(32) NOT NULL,
 "DATE" DATE NOT NULL,
 "SIMILAR_DAY" DATE NOT NULL,
 "DEGREE" DEC(10,4) NULL,
 "CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NULL,
 "UPDATETIME" TIMESTAMP(6) NULL
);

CREATE TABLE "SETTING_SYSTEM_INIT"
(
 "ID" VARCHAR(32) NOT NULL,
 "FIELD" VARCHAR(32) NOT NULL,
 "VALUE" VARCHAR(255) NULL,
 "NAME" VARCHAR(32) NULL,
 "DESCRIPTION" VARCHAR(32) NULL
);

CREATE TABLE "SETTING_REPORT_INIT"
(
 "ID" VARCHAR(32) NOT NULL,
 "CITY_ID" VARCHAR(32) NULL,
 "STANDARD_ACCURACY" DEC(10,4) NULL,
 "REPORT_TIME" VARCHAR(5) NULL
);

CREATE TABLE "SETTING_CHECK_PRECISION"
(
 "ID" VARCHAR(32) NOT NULL,
 "CITY_ID" VARCHAR(32) NULL,
 "YEAR" VARCHAR(4) NULL,
 "JANUARY" DEC(10,4) NULL,
 "FEBRUARY" DEC(10,4) NULL,
 "MARCH" DEC(10,4) NULL,
 "APRIL" DEC(10,4) NULL,
 "MAY" DEC(10,4) NULL,
 "JUNE" DEC(10,4) NULL,
 "JULY" DEC(10,4) NULL,
 "AUGUST" DEC(10,4) NULL,
 "SEPTEMBER" DEC(10,4) NULL,
 "OCTOBER" DEC(10,4) NULL,
 "NOVEMBER" DEC(10,4) NULL,
 "DECEMBER" DEC(10,4) NULL,
 "CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL,
 "UPDATETIME" TIMESTAMP(6) NULL
);

CREATE TABLE "SETTING_CHECK_BASIC"
(
 "ID" VARCHAR(32) NOT NULL,
 "CITY_ID" VARCHAR(32) NOT NULL,
 "CREATE_BY" VARCHAR(32) NULL,
 "SUBMIT_TIME" TIMESTAMP(6) NULL,
 "ENDTIME" TIMESTAMP(6) NULL,
 "START_PERIOD" VARCHAR(5) NULL,
 "END_PERIOD" VARCHAR(5) NULL,
 "START_DATE" DATE NULL,
 "END_DATE" DATE NULL,
 "REASON" VARCHAR(200) NULL,
 "STATUS" SMALLINT NULL,
 "OPINION" VARCHAR(200) NULL,
 "CHECK_TIME" VARCHAR(3900) NULL
);

CREATE TABLE "REPORT_LOAD_YEAR"
(
 "ID" VARCHAR(32) NOT NULL,
 "CITY_ID" VARCHAR(32) NOT NULL,
 "YEAR" VARCHAR(4) NOT NULL,
 "MONTH" VARCHAR(2) NULL,
 "REQ_MAX_LOAD" DEC(32,4) NULL,
 "REQ_MIN_LOAD" DEC(32,4) NULL,
 "REQ_ENERGY" DEC(32,4) NULL,
 "PEAK_LOAD" DEC(32,4) NULL,
 "PEAK_ENERGY" DEC(32,4) NULL,
 "PRO_LOAD" DEC(32,4) NULL,
 "PRO_ENERGY" DEC(32,4) NULL,
 "REPORT" BIT NULL,
 "REPORT_TIME" TIMESTAMP(6) NULL,
 "CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NULL,
 "UPDATETIME" TIMESTAMP(6) NULL
);

CREATE TABLE "REPORT_LOAD_WEEK"
(
 "ID" VARCHAR(32) NOT NULL,
 "CITY_ID" VARCHAR(32) NOT NULL,
 "YEAR" VARCHAR(4) NOT NULL,
 "WEEK" VARCHAR(2) NOT NULL,
 "DATE" DATE NOT NULL,
 "REQ_MAX_LOAD" DEC(32,4) NULL,
 "REQ_MIN_LOAD" DEC(32,4) NULL,
 "REQ_ENERGY" DEC(32,4) NULL,
 "PEAK_LOAD" DEC(32,4) NULL,
 "PEAK_ENERGY" DEC(32,4) NULL,
 "PRO_LOAD" DEC(32,4) NULL,
 "PRO_ENERGY" DEC(32,4) NULL,
 "REPORT" BIT NULL,
 "REPORT_TIME" TIMESTAMP(6) NULL,
 "CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NULL,
 "UPDATETIME" TIMESTAMP(6) NULL
);

CREATE TABLE "REPORT_LOAD_QUARTER"
(
 "ID" VARCHAR(32) NOT NULL,
 "CITY_ID" VARCHAR(32) NOT NULL,
 "YEAR" VARCHAR(4) NOT NULL,
 "QUARTER" VARCHAR(2) NOT NULL,
 "MONTH" VARCHAR(2) NOT NULL,
 "REQ_MAX_LOAD" DEC(32,4) NULL,
 "REQ_MIN_LOAD" DEC(32,4) NULL,
 "REQ_ENERGY" DEC(32,4) NULL,
 "PEAK_LOAD" DEC(32,4) NULL,
 "PEAK_ENERGY" DEC(32,4) NULL,
 "PRO_LOAD" DEC(32,4) NULL,
 "PRO_ENERGY" DEC(32,4) NULL,
 "REPORT" BIT NULL,
 "REPORT_TIME" TIMESTAMP(6) NULL,
 "CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NULL,
 "UPDATETIME" TIMESTAMP(6) NULL
);

CREATE TABLE "REPORT_LOAD_MONTH"
(
 "ID" VARCHAR(32) NOT NULL,
 "CITY_ID" VARCHAR(32) NOT NULL,
 "YEAR" VARCHAR(4) NOT NULL,
 "MONTH" VARCHAR(2) NOT NULL,
 "REQ_MAX_LOAD" DEC(32,4) NULL,
 "REQ_MIN_LOAD" DEC(32,4) NULL,
 "REQ_ENERGY" DEC(32,4) NULL,
 "PEAK_LOAD" DEC(32,4) NULL,
 "PEAK_ENERGY" DEC(32,4) NULL,
 "PRO_LOAD" DEC(32,4) NULL,
 "PRO_ENERGY" DEC(32,4) NULL,
 "REPORT" BIT NULL,
 "REPORT_TIME" TIMESTAMP(6) NULL,
 "CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NULL,
 "UPDATETIME" TIMESTAMP(6) NULL
);

CREATE TABLE "REPORT_LOAD_HIS_MONTH_ENERGY"
(
 "ID" VARCHAR(32) NOT NULL,
 "CITY_ID" VARCHAR(32) NOT NULL,
 "DATE" DATE NOT NULL,
 "HIS_ENERGY" DECIMAL(32,4) NULL,
 "REPORT_TIME" TIMESTAMP(0) NULL,
 "CREATETIME" TIMESTAMP(0) NULL,
 "UPDATETIME" TIMESTAMP(0) NULL
);

CREATE TABLE "REPORT_LOAD_DAY"
(
 "ID" VARCHAR(32) NOT NULL,
 "CITY_ID" VARCHAR(32) NOT NULL,
 "DATE" DATE NOT NULL,
 "REQ_MAX_LOAD" DEC(32,4) NULL,
 "REQ_MIN_LOAD" DEC(32,4) NULL,
 "REQ_ENERGY" DEC(32,4) NULL,
 "PEAK_LOAD" DEC(32,4) NULL,
 "PEAK_ENERGY" DEC(32,4) NULL,
 "PRO_LOAD" DEC(32,4) NULL,
 "PRO_ENERGY" DEC(32,4) NULL,
 "REPORT_TIME" TIMESTAMP(6) NULL,
 "REPORT" BIT NULL,
 "CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NULL,
 "UPDATETIME" TIMESTAMP(6) NULL
);

CREATE TABLE "REPORT_ACCURACY_WEEK_STAT"
(
 "ID" VARCHAR(32) NOT NULL,
 "CITY_ID" VARCHAR(32) NOT NULL,
 "YEAR" VARCHAR(4) NOT NULL,
 "WEEK" VARCHAR(4) NOT NULL,
 "DATE" DATE NOT NULL,
 "STANDARD_ACCURACY" DEC(10,4) NULL,
 "MAX_ACCURACY" DEC(10,4) NULL,
 "MIN_ACCURACY" DEC(10,4) NULL,
 "DAY_MAX_AVG" DEC(10,4) NULL,
 "DAY_MIN_AVG" DEC(10,4) NULL,
 "WEEK_MAX_AVG" DEC(10,4) NULL,
 "WEEK_MIN_AVG" DEC(10,4) NULL,
 "POINT_AVG" DEC(10,4) NULL,
 "ENERGY_AVG" DEC(10,4) NULL,
 "CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NULL,
 "UPDATETIME" TIMESTAMP(6) NULL
);

CREATE TABLE "REPORT_ACCURACY_SYNTHESIZE_MONTH_STAT"
(
 "ID" VARCHAR(32) NOT NULL,
 "YEAR" VARCHAR(4) NOT NULL,
 "MONTH" VARCHAR(4) NOT NULL,
 "CITY_ID" VARCHAR(32) NOT NULL,
 "STANDARD_ACCURACY" DEC(10,4) NULL,
 "SYNTHESIZE_ACCURACY" DEC(10,4) NULL,
 "POINT_ACCURACY" DEC(10,4) NULL,
 "MIN_SYNTHESIZE_ACCURACY" DEC(10,4) NULL,
 "MAX_SYNTHESIZE_ACCURACY" DEC(10,4) NULL,
 "ENERGY_SYNTHESIZE_ACCURACY" DEC(10,4) NULL,
 "EVALUATE" INTEGER NULL,
 "CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NULL,
 "UPDATETIME" TIMESTAMP(6) NULL
);

CREATE TABLE "REPORT_ACCURACY_MONTH_STAT"
(
 "ID" VARCHAR(32) NOT NULL,
 "CITY_ID" VARCHAR(320) NOT NULL,
 "YEAR" VARCHAR(4) NOT NULL,
 "MONTH" VARCHAR(4) NOT NULL,
 "STANDARD_ACCURACY" DEC(10,4) NULL,
 "MAX_ACCURACY" DEC(10,4) NULL,
 "MIN_ACCURACY" DEC(10,4) NULL,
 "DAY_MAX_AVG" DEC(10,4) NULL,
 "DAY_MIN_AVG" DEC(10,4) NULL,
 "WEEK_MAX_AVG" DEC(10,4) NULL,
 "WEEK_MIN_AVG" DEC(10,4) NULL,
 "POINT_AVG" DEC(10,4) NULL,
 "ENERGY_AVG" DEC(10,4) NULL,
 "CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NULL,
 "UPDATETIME" TIMESTAMP(6) NULL
);

CREATE TABLE "REPORT_ACCURACY_DAY_STAT"
(
 "ID" VARCHAR(32) NOT NULL,
 "CITY_ID" VARCHAR(32) NOT NULL,
 "DATE" DATE NOT NULL,
 "STANDARD_ACCURACY" DEC(10,4) NULL,
 "POINT_ACCURACY" DEC(10,4) NULL,
 "MAX_ACCURACY" DEC(10,4) NULL,
 "MIN_ACCURACY" DEC(10,4) NULL,
 "CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NULL,
 "UPDATETIME" TIMESTAMP(6) NULL
);

CREATE TABLE "PASS_LOAD_CITY_FC_SERVICE"
(
 "ID" VARCHAR(32) NOT NULL,
 "DATE" DATE NOT NULL,
 "CITY_ID" VARCHAR(32) NOT NULL,
 "CALIBER_ID" VARCHAR(32) NOT NULL,
 "ALGORITHM_ID" VARCHAR(32) NOT NULL,
 "T0000" DEC(10,4) NULL,
 "T0015" DEC(10,4) NULL,
 "T0030" DEC(10,4) NULL,
 "T0045" DEC(10,4) NULL,
 "T0100" DEC(10,4) NULL,
 "T0115" DEC(10,4) NULL,
 "T0130" DEC(10,4) NULL,
 "T0145" DEC(10,4) NULL,
 "T0200" DEC(10,4) NULL,
 "T0215" DEC(10,4) NULL,
 "T0230" DEC(10,4) NULL,
 "T0245" DEC(10,4) NULL,
 "T0300" DEC(10,4) NULL,
 "T0315" DEC(10,4) NULL,
 "T0330" DEC(10,4) NULL,
 "T0345" DEC(10,4) NULL,
 "T0400" DEC(10,4) NULL,
 "T0415" DEC(10,4) NULL,
 "T0430" DEC(10,4) NULL,
 "T0445" DEC(10,4) NULL,
 "T0500" DEC(10,4) NULL,
 "T0515" DEC(10,4) NULL,
 "T0530" DEC(10,4) NULL,
 "T0545" DEC(10,4) NULL,
 "T0600" DEC(10,4) NULL,
 "T0615" DEC(10,4) NULL,
 "T0630" DEC(10,4) NULL,
 "T0645" DEC(10,4) NULL,
 "T0700" DEC(10,4) NULL,
 "T0715" DEC(10,4) NULL,
 "T0730" DEC(10,4) NULL,
 "T0745" DEC(10,4) NULL,
 "T0800" DEC(10,4) NULL,
 "T0815" DEC(10,4) NULL,
 "T0830" DEC(10,4) NULL,
 "T0845" DEC(10,4) NULL,
 "T0900" DEC(10,4) NULL,
 "T0915" DEC(10,4) NULL,
 "T0930" DEC(10,4) NULL,
 "T0945" DEC(10,4) NULL,
 "T1000" DEC(10,4) NULL,
 "T1015" DEC(10,4) NULL,
 "T1030" DEC(10,4) NULL,
 "T1045" DEC(10,4) NULL,
 "T1100" DEC(10,4) NULL,
 "T1115" DEC(10,4) NULL,
 "T1130" DEC(10,4) NULL,
 "T1145" DEC(10,4) NULL,
 "T1200" DEC(10,4) NULL,
 "T1215" DEC(10,4) NULL,
 "T1230" DEC(10,4) NULL,
 "T1245" DEC(10,4) NULL,
 "T1300" DEC(10,4) NULL,
 "T1315" DEC(10,4) NULL,
 "T1330" DEC(10,4) NULL,
 "T1345" DEC(10,4) NULL,
 "T1400" DEC(10,4) NULL,
 "T1415" DEC(10,4) NULL,
 "T1430" DEC(10,4) NULL,
 "T1445" DEC(10,4) NULL,
 "T1500" DEC(10,4) NULL,
 "T1515" DEC(10,4) NULL,
 "T1530" DEC(10,4) NULL,
 "T1545" DEC(10,4) NULL,
 "T1600" DEC(10,4) NULL,
 "T1615" DEC(10,4) NULL,
 "T1630" DEC(10,4) NULL,
 "T1645" DEC(10,4) NULL,
 "T1700" DEC(10,4) NULL,
 "T1715" DEC(10,4) NULL,
 "T1730" DEC(10,4) NULL,
 "T1745" DEC(10,4) NULL,
 "T1800" DEC(10,4) NULL,
 "T1815" DEC(10,4) NULL,
 "T1830" DEC(10,4) NULL,
 "T1845" DEC(10,4) NULL,
 "T1900" DEC(10,4) NULL,
 "T1915" DEC(10,4) NULL,
 "T1930" DEC(10,4) NULL,
 "T1945" DEC(10,4) NULL,
 "T2000" DEC(10,4) NULL,
 "T2015" DEC(10,4) NULL,
 "T2030" DEC(10,4) NULL,
 "T2045" DEC(10,4) NULL,
 "T2100" DEC(10,4) NULL,
 "T2115" DEC(10,4) NULL,
 "T2130" DEC(10,4) NULL,
 "T2145" DEC(10,4) NULL,
 "T2200" DEC(10,4) NULL,
 "T2215" DEC(10,4) NULL,
 "T2230" DEC(10,4) NULL,
 "T2245" DEC(10,4) NULL,
 "T2300" DEC(10,4) NULL,
 "T2315" DEC(10,4) NULL,
 "T2330" DEC(10,4) NULL,
 "T2345" DEC(10,4) NULL,
 "T2400" DEC(10,4) NULL,
 "REPORT" BIT DEFAULT 0
 NULL,
 "CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NULL,
 "UPDATETIME" TIMESTAMP(6) NULL
);

CREATE TABLE "LOAD_USER_DETAIL"
(
 "TSIE_UID" VARCHAR(32) NOT NULL,
 "CITY_ID" VARCHAR(32) NULL,
 "CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NULL,
 "LAST_UPDATE_PASSWORD_TIME" TIMESTAMP(6) NULL
 NULL
);

CREATE TABLE "LOAD_FEATURE_CITY_YEAR_HIS_SERVICE"
(
 "ID" VARCHAR(32) NOT NULL,
 "YEAR" VARCHAR(4) NOT NULL,
 "CITY_ID" VARCHAR(32) NOT NULL,
 "CALIBER_ID" VARCHAR(32) NOT NULL,
 "MAX_LOAD" DEC(32,4) NULL,
 "MIN_LOAD" DEC(32,4) NULL,
 "MAX_DATE" DATE NULL,
 "AVE_LOAD" DEC(32,4) NULL,
 "MAX_TIME" VARCHAR(5) NULL,
 "MIN_TIME" VARCHAR(5) NULL,
 "PEAK" DEC(32,4) NULL,
 "TROUGH" DEC(32,4) NULL,
 "DIFFERENT" DEC(32,4) NULL,
 "GRADIENT" DEC(32,4) NULL,
 "LOAD_GRADIENT" DEC(32,4) NULL,
 "DAY_UNBALANCE" DEC(32,4) NULL,
 "USE_HOURS" DEC(32,4) NULL,
 "ENERGY" DEC(32,4) NULL,
 "CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NULL,
 "UPDATETIME" TIMESTAMP(6) NULL
);

CREATE TABLE "LOAD_FEATURE_CITY_WEEK_HIS_SERVICE"
(
 "ID" VARCHAR(32) NOT NULL,
 "DATE" DATE NOT NULL,
 "CITY_ID" VARCHAR(32) NOT NULL,
 "CALIBER_ID" VARCHAR(32) NOT NULL,
 "MAX_LOAD" DEC(32,4) NULL,
 "MIN_LOAD" DEC(32,4) NULL,
 "AVE_LOAD" DEC(32,4) NULL,
 "MAX_DATE" DATE NULL,
 "MAX_TIME" VARCHAR(5) NULL,
 "MIN_TIME" VARCHAR(5) NULL,
 "PEAK" DEC(32,4) NULL,
 "TROUGH" DEC(32,4) NULL,
 "DIFFERENT" DEC(32,4) NULL,
 "GRADIENT" DEC(32,4) NULL,
 "LOAD_GRADIENT" DEC(32,4) NULL,
 "ENERGY" DEC(32,4) NULL,
 "CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NULL,
 "UPDATETIME" TIMESTAMP(6) NULL
);

CREATE TABLE "LOAD_FEATURE_CITY_WEEK_FC_SERVICE"
(
 "ID" VARCHAR(32) NOT NULL,
 "DATE" DATE NOT NULL,
 "CITY_ID" VARCHAR(32) NOT NULL,
 "CALIBER_ID" VARCHAR(32) NOT NULL,
 "ALGORITHM_ID" VARCHAR(32) NOT NULL,
 "MAX_LOAD" DECIMAL(32,4) NULL,
 "MIN_LOAD" DECIMAL(32,4) NULL,
 "AVE_LOAD" DECIMAL(32,4) NULL,
 "MAX_DATE" DATE NULL,
 "MAX_TIME" VARCHAR(5) NULL,
 "MIN_TIME" VARCHAR(5) NULL,
 "PEAK" DECIMAL(32,4) NULL,
 "TROUGH" DECIMAL(32,4) NULL,
 "DIFFERENT" DECIMAL(32,4) NULL,
 "GRADIENT" DECIMAL(32,4) NULL,
 "LOAD_GRADIENT" DECIMAL(32,4) NULL,
 "ENERGY" DECIMAL(32,4) NULL,
 "CREATETIME" TIMESTAMP(0) NULL,
 "UPDATETIME" TIMESTAMP(0) NULL
);

CREATE TABLE "LOAD_FEATURE_CITY_QUARTER_HIS_SERVICE"
(
 "ID" VARCHAR(32) NOT NULL,
 "YEAR" VARCHAR(4) NOT NULL,
 "QUARTER" VARCHAR(1) NOT NULL,
 "CITY_ID" VARCHAR(32) NOT NULL,
 "CALIBER_ID" VARCHAR(32) NOT NULL,
 "MAX_LOAD" DEC(32,4) NULL,
 "MIN_LOAD" DEC(32,4) NULL,
 "AVE_LOAD" DEC(32,4) NULL,
 "MAX_DATE" DATE NULL,
 "MAX_TIME" VARCHAR(5) NULL,
 "MIN_TIME" VARCHAR(5) NULL,
 "PEAK" DEC(32,4) NULL,
 "TROUGH" DEC(32,4) NULL,
 "DIFFERENT" DEC(32,4) NULL,
 "GRADIENT" DEC(32,4) NULL,
 "LOAD_GRADIENT" DEC(32,4) NULL,
 "DAY_UNBALANCE" DEC(32,4) NULL,
 "ENERGY" DEC(32,4) NULL,
 "CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NULL,
 "UPDATETIME" TIMESTAMP(6) NULL
);

CREATE TABLE "LOAD_FEATURE_CITY_MONTH_HIS_SERVICE"
(
 "ID" VARCHAR(32) NOT NULL,
 "YEAR" VARCHAR(4) NOT NULL,
 "MONTH" VARCHAR(2) NOT NULL,
 "CITY_ID" VARCHAR(32) NOT NULL,
 "CALIBER_ID" VARCHAR(32) NOT NULL,
 "MAX_LOAD" DEC(32,4) NULL,
 "MIN_LOAD" DEC(32,4) NULL,
 "AVE_LOAD" DEC(32,4) NULL,
 "MAX_DATE" DATE NULL,
 "MAX_TIME" VARCHAR(5) NULL,
 "MIN_TIME" VARCHAR(5) NULL,
 "PEAK" DEC(32,4) NULL,
 "TROUGH" DEC(32,4) NULL,
 "DIFFERENT" DEC(32,4) NULL,
 "GRADIENT" DEC(32,4) NULL,
 "LOAD_GRADIENT" DEC(32,4) NULL,
 "DAY_UNBALANCE" DEC(32,4) NULL,
 "ENERGY" DEC(32,4) NULL,
 "UPPER_STABILITY" DEC(32,4) NULL,
 "LOWER_STABILITY" DEC(32,4) NULL,
 "CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NULL,
 "UPDATETIME" TIMESTAMP(6) NULL
);

CREATE TABLE "LOAD_FEATURE_CITY_MONTH_FC_SERVICE"
(
 "ID" VARCHAR(32) NOT NULL,
 "YEAR" VARCHAR(4) NOT NULL,
 "MONTH" VARCHAR(2) NOT NULL,
 "CITY_ID" VARCHAR(32) NOT NULL,
 "CALIBER_ID" VARCHAR(32) NOT NULL,
 "ALGORITHM_ID" VARCHAR(32) NOT NULL,
 "MAX_LOAD" DECIMAL(32,4) NULL,
 "MIN_LOAD" DECIMAL(32,4) NULL,
 "AVE_LOAD" DECIMAL(32,4) NULL,
 "MAX_DATE" DATE NULL,
 "MAX_TIME" VARCHAR(5) NULL,
 "MIN_TIME" VARCHAR(5) NULL,
 "PEAK" DECIMAL(32,4) NULL,
 "TROUGH" DECIMAL(32,4) NULL,
 "DIFFERENT" DECIMAL(32,4) NULL,
 "GRADIENT" DECIMAL(32,4) NULL,
 "LOAD_GRADIENT" DECIMAL(32,4) NULL,
 "DAY_UNBALANCE" DECIMAL(32,4) NULL,
 "ENERGY" DECIMAL(32,4) NULL,
 "UPPER_STABILITY" DECIMAL(32,4) NULL,
 "LOWER_STABILITY" DECIMAL(32,4) NULL,
 "CREATETIME" TIMESTAMP(0) NULL,
 "UPDATETIME" TIMESTAMP(0) NULL
);

CREATE TABLE "LOAD_FEATURE_CITY_DAY_SHORT_FC_SERVICE"
(
 "ID" VARCHAR(32) NOT NULL,
 "DATE" DATE NOT NULL,
 "CITY_ID" VARCHAR(32) NOT NULL,
 "CALIBER_ID" VARCHAR(32) NOT NULL,
 "ALGORITHM_ID" VARCHAR(32) NOT NULL,
 "TYPE" BIGINT NULL,
 "MAX_LOAD" DEC(32,4) NULL,
 "MIN_LOAD" DEC(32,4) NULL,
 "AVG_LOAD" DEC(32,4) NULL,
 "MAX_TIME" VARCHAR(5) NULL,
 "MIN_TIME" VARCHAR(5) NULL,
 "MAX_LOAD_ACCURACY" DEC(32,4) NULL,
 "MIN_LOAD_ACCURACY" DEC(32,4) NULL,
 "MAX_ACCURACY" DEC(32,4) NULL,
 "MIN_ACCURACY" DEC(32,4) NULL,
 "AVG_ACCURACY" DEC(32,4) NULL,
 "CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NULL,
 "UPDATETIME" TIMESTAMP(6) NULL
);

CREATE TABLE "LOAD_FEATURE_CITY_DAY_HIS_SERVICE"
(
 "ID" VARCHAR(32) NOT NULL,
 "DATE" DATE NOT NULL,
 "CITY_ID" VARCHAR(32) NOT NULL,
 "CALIBER_ID" VARCHAR(32) NOT NULL,
 "MAX_LOAD" DEC(32,4) NULL,
 "MIN_LOAD" DEC(32,4) NULL,
 "AVE_LOAD" DEC(32,4) NULL,
 "MAX_TIME" VARCHAR(5) NULL,
 "MIN_TIME" VARCHAR(5) NULL,
 "PEAK" DEC(32,4) NULL,
 "TROUGH" DEC(32,4) NULL,
 "DIFFERENT" DEC(32,4) NULL,
 "GRADIENT" DEC(32,4) NULL,
 "LOAD_GRADIENT" DEC(32,4) NULL,
 "ENERGY" DEC(32,4) NULL,
 "CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NULL,
 "UPDATETIME" TIMESTAMP(6) NULL
);

CREATE TABLE "LOAD_FEATURE_CITY_DAY_FC_SERVICE"
(
 "ID" VARCHAR(32) NOT NULL,
 "DATE" DATE NOT NULL,
 "CITY_ID" VARCHAR(32) NOT NULL,
 "CALIBER_ID" VARCHAR(32) NOT NULL,
 "ALGORITHM_ID" VARCHAR(32) NOT NULL,
 "MAX_LOAD" DEC(32,4) NULL,
 "MIN_LOAD" DEC(32,4) NULL,
 "AVE_LOAD" DEC(32,4) NULL,
 "MAX_TIME" VARCHAR(5) NULL,
 "MIN_TIME" VARCHAR(5) NULL,
 "PEAK" DEC(32,4) NULL,
 "TROUGH" DEC(32,4) NULL,
 "DIFFERENT" DEC(32,4) NULL,
 "GRADIENT" DEC(32,4) NULL,
 "LOAD_GRADIENT" DEC(32,4) NULL,
 "ENERGY" DEC(32,4) NULL,
 "REPORT" BIT NULL,
 "CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NULL,
 "UPDATETIME" TIMESTAMP(6) NULL
);

CREATE TABLE "LOAD_DECOMPOSE_CITY_WEEK_STABILITY_SERVICE"
(
 "ID" VARCHAR(32) NOT NULL,
 "CITY_ID" VARCHAR(32) NOT NULL,
 "CALIBER_ID" VARCHAR(32) NOT NULL,
 "START_DATE" DATE NOT NULL,
 "END_DATE" DATE NOT NULL,
 "UPPER_STABILITY" DEC(32,4) NOT NULL,
 "LOWER_STABILITY" DEC(32,4) NOT NULL,
 "CREATETIME" TIMESTAMP(6) NULL,
 "UPDATETIME" TIMESTAMP(6) NULL
);

CREATE TABLE "LOAD_DECOMPOSE_CITY_WEEK_SERVICE"
(
 "ID" VARCHAR(32) NOT NULL,
 "DATE" DATE NOT NULL,
 "CITY_ID" VARCHAR(32) NOT NULL,
 "CALIBER_ID" VARCHAR(32) NOT NULL,
 "TYPE" TINYINT NOT NULL,
 "T0000" DEC(32,4) NULL,
 "T0015" DEC(32,4) NULL,
 "T0030" DEC(32,4) NULL,
 "T0045" DEC(32,4) NULL,
 "T0100" DEC(32,4) NULL,
 "T0115" DEC(32,4) NULL,
 "T0130" DEC(32,4) NULL,
 "T0145" DEC(32,4) NULL,
 "T0200" DEC(32,4) NULL,
 "T0215" DEC(32,4) NULL,
 "T0230" DEC(32,4) NULL,
 "T0245" DEC(32,4) NULL,
 "T0300" DEC(32,4) NULL,
 "T0315" DEC(32,4) NULL,
 "T0330" DEC(32,4) NULL,
 "T0345" DEC(32,4) NULL,
 "T0400" DEC(32,4) NULL,
 "T0415" DEC(32,4) NULL,
 "T0430" DEC(32,4) NULL,
 "T0445" DEC(32,4) NULL,
 "T0500" DEC(32,4) NULL,
 "T0515" DEC(32,4) NULL,
 "T0530" DEC(32,4) NULL,
 "T0545" DEC(32,4) NULL,
 "T0600" DEC(32,4) NULL,
 "T0615" DEC(32,4) NULL,
 "T0630" DEC(32,4) NULL,
 "T0645" DEC(32,4) NULL,
 "T0700" DEC(32,4) NULL,
 "T0715" DEC(32,4) NULL,
 "T0730" DEC(32,4) NULL,
 "T0745" DEC(32,4) NULL,
 "T0800" DEC(32,4) NULL,
 "T0815" DEC(32,4) NULL,
 "T0830" DEC(32,4) NULL,
 "T0845" DEC(32,4) NULL,
 "T0900" DEC(32,4) NULL,
 "T0915" DEC(32,4) NULL,
 "T0930" DEC(32,4) NULL,
 "T0945" DEC(32,4) NULL,
 "T1000" DEC(32,4) NULL,
 "T1015" DEC(32,4) NULL,
 "T1030" DEC(32,4) NULL,
 "T1045" DEC(32,4) NULL,
 "T1100" DEC(32,4) NULL,
 "T1115" DEC(32,4) NULL,
 "T1130" DEC(32,4) NULL,
 "T1145" DEC(32,4) NULL,
 "T1200" DEC(32,4) NULL,
 "T1215" DEC(32,4) NULL,
 "T1230" DEC(32,4) NULL,
 "T1245" DEC(32,4) NULL,
 "T1300" DEC(32,4) NULL,
 "T1315" DEC(32,4) NULL,
 "T1330" DEC(32,4) NULL,
 "T1345" DEC(32,4) NULL,
 "T1400" DEC(32,4) NULL,
 "T1415" DEC(32,4) NULL,
 "T1430" DEC(32,4) NULL,
 "T1445" DEC(32,4) NULL,
 "T1500" DEC(32,4) NULL,
 "T1515" DEC(32,4) NULL,
 "T1530" DEC(32,4) NULL,
 "T1545" DEC(32,4) NULL,
 "T1600" DEC(32,4) NULL,
 "T1615" DEC(32,4) NULL,
 "T1630" DEC(32,4) NULL,
 "T1645" DEC(32,4) NULL,
 "T1700" DEC(32,4) NULL,
 "T1715" DEC(32,4) NULL,
 "T1730" DEC(32,4) NULL,
 "T1745" DEC(32,4) NULL,
 "T1800" DEC(32,4) NULL,
 "T1815" DEC(32,4) NULL,
 "T1830" DEC(32,4) NULL,
 "T1845" DEC(32,4) NULL,
 "T1900" DEC(32,4) NULL,
 "T1915" DEC(32,4) NULL,
 "T1930" DEC(32,4) NULL,
 "T1945" DEC(32,4) NULL,
 "T2000" DEC(32,4) NULL,
 "T2015" DEC(32,4) NULL,
 "T2030" DEC(32,4) NULL,
 "T2045" DEC(32,4) NULL,
 "T2100" DEC(32,4) NULL,
 "T2115" DEC(32,4) NULL,
 "T2130" DEC(32,4) NULL,
 "T2145" DEC(32,4) NULL,
 "T2200" DEC(32,4) NULL,
 "T2215" DEC(32,4) NULL,
 "T2230" DEC(32,4) NULL,
 "T2245" DEC(32,4) NULL,
 "T2300" DEC(32,4) NULL,
 "T2315" DEC(32,4) NULL,
 "T2330" DEC(32,4) NULL,
 "T2345" DEC(32,4) NULL,
 "T2400" DEC(32,4) NULL,
 "CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL,
 "UPDATETIME" TIMESTAMP(6) NULL
);

CREATE TABLE "LOAD_CITY_HIS_CLCT_288"
(
 "ID" VARCHAR(32) NOT NULL,
 "DATE" DATE NOT NULL,
 "CITY_ID" VARCHAR(32) NOT NULL,
 "CALIBER_ID" VARCHAR(32) NOT NULL,
 "T0000" DECIMAL(10,4) NULL,
 "T0005" DECIMAL(10,4) NULL,
 "T0010" DECIMAL(10,4) NULL,
 "T0015" DECIMAL(10,4) NULL,
 "T0020" DECIMAL(10,4) NULL,
 "T0025" DECIMAL(10,4) NULL,
 "T0030" DECIMAL(10,4) NULL,
 "T0035" DECIMAL(10,4) NULL,
 "T0040" DECIMAL(10,4) NULL,
 "T0045" DECIMAL(10,4) NULL,
 "T0050" DECIMAL(10,4) NULL,
 "T0055" DECIMAL(10,4) NULL,
 "T0100" DECIMAL(10,4) NULL,
 "T0105" DECIMAL(10,4) NULL,
 "T0110" DECIMAL(10,4) NULL,
 "T0115" DECIMAL(10,4) NULL,
 "T0120" DECIMAL(10,4) NULL,
 "T0125" DECIMAL(10,4) NULL,
 "T0130" DECIMAL(10,4) NULL,
 "T0135" DECIMAL(10,4) NULL,
 "T0140" DECIMAL(10,4) NULL,
 "T0145" DECIMAL(10,4) NULL,
 "T0150" DECIMAL(10,4) NULL,
 "T0155" DECIMAL(10,4) NULL,
 "T0200" DECIMAL(10,4) NULL,
 "T0205" DECIMAL(10,4) NULL,
 "T0210" DECIMAL(10,4) NULL,
 "T0215" DECIMAL(10,4) NULL,
 "T0220" DECIMAL(10,4) NULL,
 "T0225" DECIMAL(10,4) NULL,
 "T0230" DECIMAL(10,4) NULL,
 "T0235" DECIMAL(10,4) NULL,
 "T0240" DECIMAL(10,4) NULL,
 "T0245" DECIMAL(10,4) NULL,
 "T0250" DECIMAL(10,4) NULL,
 "T0255" DECIMAL(10,4) NULL,
 "T0300" DECIMAL(10,4) NULL,
 "T0305" DECIMAL(10,4) NULL,
 "T0310" DECIMAL(10,4) NULL,
 "T0315" DECIMAL(10,4) NULL,
 "T0320" DECIMAL(10,4) NULL,
 "T0325" DECIMAL(10,4) NULL,
 "T0330" DECIMAL(10,4) NULL,
 "T0335" DECIMAL(10,4) NULL,
 "T0340" DECIMAL(10,4) NULL,
 "T0345" DECIMAL(10,4) NULL,
 "T0350" DECIMAL(10,4) NULL,
 "T0355" DECIMAL(10,4) NULL,
 "T0400" DECIMAL(10,4) NULL,
 "T0405" DECIMAL(10,4) NULL,
 "T0410" DECIMAL(10,4) NULL,
 "T0415" DECIMAL(10,4) NULL,
 "T0420" DECIMAL(10,4) NULL,
 "T0425" DECIMAL(10,4) NULL,
 "T0430" DECIMAL(10,4) NULL,
 "T0435" DECIMAL(10,4) NULL,
 "T0440" DECIMAL(10,4) NULL,
 "T0445" DECIMAL(10,4) NULL,
 "T0450" DECIMAL(10,4) NULL,
 "T0455" DECIMAL(10,4) NULL,
 "T0500" DECIMAL(10,4) NULL,
 "T0505" DECIMAL(10,4) NULL,
 "T0510" DECIMAL(10,4) NULL,
 "T0515" DECIMAL(10,4) NULL,
 "T0520" DECIMAL(10,4) NULL,
 "T0525" DECIMAL(10,4) NULL,
 "T0530" DECIMAL(10,4) NULL,
 "T0535" DECIMAL(10,4) NULL,
 "T0540" DECIMAL(10,4) NULL,
 "T0545" DECIMAL(10,4) NULL,
 "T0550" DECIMAL(10,4) NULL,
 "T0555" DECIMAL(10,4) NULL,
 "T0600" DECIMAL(10,4) NULL,
 "T0605" DECIMAL(10,4) NULL,
 "T0610" DECIMAL(10,4) NULL,
 "T0615" DECIMAL(10,4) NULL,
 "T0620" DECIMAL(10,4) NULL,
 "T0625" DECIMAL(10,4) NULL,
 "T0630" DECIMAL(10,4) NULL,
 "T0635" DECIMAL(10,4) NULL,
 "T0640" DECIMAL(10,4) NULL,
 "T0645" DECIMAL(10,4) NULL,
 "T0650" DECIMAL(10,4) NULL,
 "T0655" DECIMAL(10,4) NULL,
 "T0700" DECIMAL(10,4) NULL,
 "T0705" DECIMAL(10,4) NULL,
 "T0710" DECIMAL(10,4) NULL,
 "T0715" DECIMAL(10,4) NULL,
 "T0720" DECIMAL(10,4) NULL,
 "T0725" DECIMAL(10,4) NULL,
 "T0730" DECIMAL(10,4) NULL,
 "T0735" DECIMAL(10,4) NULL,
 "T0740" DECIMAL(10,4) NULL,
 "T0745" DECIMAL(10,4) NULL,
 "T0750" DECIMAL(10,4) NULL,
 "T0755" DECIMAL(10,4) NULL,
 "T0800" DECIMAL(10,4) NULL,
 "T0805" DECIMAL(10,4) NULL,
 "T0810" DECIMAL(10,4) NULL,
 "T0815" DECIMAL(10,4) NULL,
 "T0820" DECIMAL(10,4) NULL,
 "T0825" DECIMAL(10,4) NULL,
 "T0830" DECIMAL(10,4) NULL,
 "T0835" DECIMAL(10,4) NULL,
 "T0840" DECIMAL(10,4) NULL,
 "T0845" DECIMAL(10,4) NULL,
 "T0850" DECIMAL(10,4) NULL,
 "T0855" DECIMAL(10,4) NULL,
 "T0900" DECIMAL(10,4) NULL,
 "T0905" DECIMAL(10,4) NULL,
 "T0910" DECIMAL(10,4) NULL,
 "T0915" DECIMAL(10,4) NULL,
 "T0920" DECIMAL(10,4) NULL,
 "T0925" DECIMAL(10,4) NULL,
 "T0930" DECIMAL(10,4) NULL,
 "T0935" DECIMAL(10,4) NULL,
 "T0940" DECIMAL(10,4) NULL,
 "T0945" DECIMAL(10,4) NULL,
 "T0950" DECIMAL(10,4) NULL,
 "T0955" DECIMAL(10,4) NULL,
 "T1000" DECIMAL(10,4) NULL,
 "T1005" DECIMAL(10,4) NULL,
 "T1010" DECIMAL(10,4) NULL,
 "T1015" DECIMAL(10,4) NULL,
 "T1020" DECIMAL(10,4) NULL,
 "T1025" DECIMAL(10,4) NULL,
 "T1030" DECIMAL(10,4) NULL,
 "T1035" DECIMAL(10,4) NULL,
 "T1040" DECIMAL(10,4) NULL,
 "T1045" DECIMAL(10,4) NULL,
 "T1050" DECIMAL(10,4) NULL,
 "T1055" DECIMAL(10,4) NULL,
 "T1100" DECIMAL(10,4) NULL,
 "T1105" DECIMAL(10,4) NULL,
 "T1110" DECIMAL(10,4) NULL,
 "T1115" DECIMAL(10,4) NULL,
 "T1120" DECIMAL(10,4) NULL,
 "T1125" DECIMAL(10,4) NULL,
 "T1130" DECIMAL(10,4) NULL,
 "T1135" DECIMAL(10,4) NULL,
 "T1140" DECIMAL(10,4) NULL,
 "T1145" DECIMAL(10,4) NULL,
 "T1150" DECIMAL(10,4) NULL,
 "T1155" DECIMAL(10,4) NULL,
 "T1200" DECIMAL(10,4) NULL,
 "T1205" DECIMAL(10,4) NULL,
 "T1210" DECIMAL(10,4) NULL,
 "T1215" DECIMAL(10,4) NULL,
 "T1220" DECIMAL(10,4) NULL,
 "T1225" DECIMAL(10,4) NULL,
 "T1230" DECIMAL(10,4) NULL,
 "T1235" DECIMAL(10,4) NULL,
 "T1240" DECIMAL(10,4) NULL,
 "T1245" DECIMAL(10,4) NULL,
 "T1250" DECIMAL(10,4) NULL,
 "T1255" DECIMAL(10,4) NULL,
 "T1300" DECIMAL(10,4) NULL,
 "T1305" DECIMAL(10,4) NULL,
 "T1310" DECIMAL(10,4) NULL,
 "T1315" DECIMAL(10,4) NULL,
 "T1320" DECIMAL(10,4) NULL,
 "T1325" DECIMAL(10,4) NULL,
 "T1330" DECIMAL(10,4) NULL,
 "T1335" DECIMAL(10,4) NULL,
 "T1340" DECIMAL(10,4) NULL,
 "T1345" DECIMAL(10,4) NULL,
 "T1350" DECIMAL(10,4) NULL,
 "T1355" DECIMAL(10,4) NULL,
 "T1400" DECIMAL(10,4) NULL,
 "T1405" DECIMAL(10,4) NULL,
 "T1410" DECIMAL(10,4) NULL,
 "T1415" DECIMAL(10,4) NULL,
 "T1420" DECIMAL(10,4) NULL,
 "T1425" DECIMAL(10,4) NULL,
 "T1430" DECIMAL(10,4) NULL,
 "T1435" DECIMAL(10,4) NULL,
 "T1440" DECIMAL(10,4) NULL,
 "T1445" DECIMAL(10,4) NULL,
 "T1450" DECIMAL(10,4) NULL,
 "T1455" DECIMAL(10,4) NULL,
 "T1500" DECIMAL(10,4) NULL,
 "T1505" DECIMAL(10,4) NULL,
 "T1510" DECIMAL(10,4) NULL,
 "T1515" DECIMAL(10,4) NULL,
 "T1520" DECIMAL(10,4) NULL,
 "T1525" DECIMAL(10,4) NULL,
 "T1530" DECIMAL(10,4) NULL,
 "T1535" DECIMAL(10,4) NULL,
 "T1540" DECIMAL(10,4) NULL,
 "T1545" DECIMAL(10,4) NULL,
 "T1550" DECIMAL(10,4) NULL,
 "T1555" DECIMAL(10,4) NULL,
 "T1600" DECIMAL(10,4) NULL,
 "T1605" DECIMAL(10,4) NULL,
 "T1610" DECIMAL(10,4) NULL,
 "T1615" DECIMAL(10,4) NULL,
 "T1620" DECIMAL(10,4) NULL,
 "T1625" DECIMAL(10,4) NULL,
 "T1630" DECIMAL(10,4) NULL,
 "T1635" DECIMAL(10,4) NULL,
 "T1640" DECIMAL(10,4) NULL,
 "T1645" DECIMAL(10,4) NULL,
 "T1650" DECIMAL(10,4) NULL,
 "T1655" DECIMAL(10,4) NULL,
 "T1700" DECIMAL(10,4) NULL,
 "T1705" DECIMAL(10,4) NULL,
 "T1710" DECIMAL(10,4) NULL,
 "T1715" DECIMAL(10,4) NULL,
 "T1720" DECIMAL(10,4) NULL,
 "T1725" DECIMAL(10,4) NULL,
 "T1730" DECIMAL(10,4) NULL,
 "T1735" DECIMAL(10,4) NULL,
 "T1740" DECIMAL(10,4) NULL,
 "T1745" DECIMAL(10,4) NULL,
 "T1750" DECIMAL(10,4) NULL,
 "T1755" DECIMAL(10,4) NULL,
 "T1800" DECIMAL(10,4) NULL,
 "T1805" DECIMAL(10,4) NULL,
 "T1810" DECIMAL(10,4) NULL,
 "T1815" DECIMAL(10,4) NULL,
 "T1820" DECIMAL(10,4) NULL,
 "T1825" DECIMAL(10,4) NULL,
 "T1830" DECIMAL(10,4) NULL,
 "T1835" DECIMAL(10,4) NULL,
 "T1840" DECIMAL(10,4) NULL,
 "T1845" DECIMAL(10,4) NULL,
 "T1850" DECIMAL(10,4) NULL,
 "T1855" DECIMAL(10,4) NULL,
 "T1900" DECIMAL(10,4) NULL,
 "T1905" DECIMAL(10,4) NULL,
 "T1910" DECIMAL(10,4) NULL,
 "T1915" DECIMAL(10,4) NULL,
 "T1920" DECIMAL(10,4) NULL,
 "T1925" DECIMAL(10,4) NULL,
 "T1930" DECIMAL(10,4) NULL,
 "T1935" DECIMAL(10,4) NULL,
 "T1940" DECIMAL(10,4) NULL,
 "T1945" DECIMAL(10,4) NULL,
 "T1950" DECIMAL(10,4) NULL,
 "T1955" DECIMAL(10,4) NULL,
 "T2000" DECIMAL(10,4) NULL,
 "T2005" DECIMAL(10,4) NULL,
 "T2010" DECIMAL(10,4) NULL,
 "T2015" DECIMAL(10,4) NULL,
 "T2020" DECIMAL(10,4) NULL,
 "T2025" DECIMAL(10,4) NULL,
 "T2030" DECIMAL(10,4) NULL,
 "T2035" DECIMAL(10,4) NULL,
 "T2040" DECIMAL(10,4) NULL,
 "T2045" DECIMAL(10,4) NULL,
 "T2050" DECIMAL(10,4) NULL,
 "T2055" DECIMAL(10,4) NULL,
 "T2100" DECIMAL(10,4) NULL,
 "T2105" DECIMAL(10,4) NULL,
 "T2110" DECIMAL(10,4) NULL,
 "T2115" DECIMAL(10,4) NULL,
 "T2120" DECIMAL(10,4) NULL,
 "T2125" DECIMAL(10,4) NULL,
 "T2130" DECIMAL(10,4) NULL,
 "T2135" DECIMAL(10,4) NULL,
 "T2140" DECIMAL(10,4) NULL,
 "T2145" DECIMAL(10,4) NULL,
 "T2150" DECIMAL(10,4) NULL,
 "T2155" DECIMAL(10,4) NULL,
 "T2200" DECIMAL(10,4) NULL,
 "T2205" DECIMAL(10,4) NULL,
 "T2210" DECIMAL(10,4) NULL,
 "T2215" DECIMAL(10,4) NULL,
 "T2220" DECIMAL(10,4) NULL,
 "T2225" DECIMAL(10,4) NULL,
 "T2230" DECIMAL(10,4) NULL,
 "T2235" DECIMAL(10,4) NULL,
 "T2240" DECIMAL(10,4) NULL,
 "T2245" DECIMAL(10,4) NULL,
 "T2250" DECIMAL(10,4) NULL,
 "T2255" DECIMAL(10,4) NULL,
 "T2300" DECIMAL(10,4) NULL,
 "T2305" DECIMAL(10,4) NULL,
 "T2310" DECIMAL(10,4) NULL,
 "T2315" DECIMAL(10,4) NULL,
 "T2320" DECIMAL(10,4) NULL,
 "T2325" DECIMAL(10,4) NULL,
 "T2330" DECIMAL(10,4) NULL,
 "T2335" DECIMAL(10,4) NULL,
 "T2340" DECIMAL(10,4) NULL,
 "T2345" DECIMAL(10,4) NULL,
 "T2350" DECIMAL(10,4) NULL,
 "T2355" DECIMAL(10,4) NULL,
 "T2400" DECIMAL(10,4) NULL,
 "CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NULL,
 "UPDATETIME" TIMESTAMP(6) NULL
);

CREATE TABLE "LOAD_CITY_HIS_CLCT"
(
 "ID" VARCHAR(32) NOT NULL,
 "DATE" DATE NOT NULL,
 "CITY_ID" VARCHAR(32) NOT NULL,
 "CALIBER_ID" VARCHAR(32) NOT NULL,
 "T0000" DEC(32,4) NULL,
 "T0015" DEC(32,4) NULL,
 "T0030" DEC(32,4) NULL,
 "T0045" DEC(32,4) NULL,
 "T0100" DEC(32,4) NULL,
 "T0115" DEC(32,4) NULL,
 "T0130" DEC(32,4) NULL,
 "T0145" DEC(32,4) NULL,
 "T0200" DEC(32,4) NULL,
 "T0215" DEC(32,4) NULL,
 "T0230" DEC(32,4) NULL,
 "T0245" DEC(32,4) NULL,
 "T0300" DEC(32,4) NULL,
 "T0315" DEC(32,4) NULL,
 "T0330" DEC(32,4) NULL,
 "T0345" DEC(32,4) NULL,
 "T0400" DEC(32,4) NULL,
 "T0415" DEC(32,4) NULL,
 "T0430" DEC(32,4) NULL,
 "T0445" DEC(32,4) NULL,
 "T0500" DEC(32,4) NULL,
 "T0515" DEC(32,4) NULL,
 "T0530" DEC(32,4) NULL,
 "T0545" DEC(32,4) NULL,
 "T0600" DEC(32,4) NULL,
 "T0615" DEC(32,4) NULL,
 "T0630" DEC(32,4) NULL,
 "T0645" DEC(32,4) NULL,
 "T0700" DEC(32,4) NULL,
 "T0715" DEC(32,4) NULL,
 "T0730" DEC(32,4) NULL,
 "T0745" DEC(32,4) NULL,
 "T0800" DEC(32,4) NULL,
 "T0815" DEC(32,4) NULL,
 "T0830" DEC(32,4) NULL,
 "T0845" DEC(32,4) NULL,
 "T0900" DEC(32,4) NULL,
 "T0915" DEC(32,4) NULL,
 "T0930" DEC(32,4) NULL,
 "T0945" DEC(32,4) NULL,
 "T1000" DEC(32,4) NULL,
 "T1015" DEC(32,4) NULL,
 "T1030" DEC(32,4) NULL,
 "T1045" DEC(32,4) NULL,
 "T1100" DEC(32,4) NULL,
 "T1115" DEC(32,4) NULL,
 "T1130" DEC(32,4) NULL,
 "T1145" DEC(32,4) NULL,
 "T1200" DEC(32,4) NULL,
 "T1215" DEC(32,4) NULL,
 "T1230" DEC(32,4) NULL,
 "T1245" DEC(32,4) NULL,
 "T1300" DEC(32,4) NULL,
 "T1315" DEC(32,4) NULL,
 "T1330" DEC(32,4) NULL,
 "T1345" DEC(32,4) NULL,
 "T1400" DEC(32,4) NULL,
 "T1415" DEC(32,4) NULL,
 "T1430" DEC(32,4) NULL,
 "T1445" DEC(32,4) NULL,
 "T1500" DEC(32,4) NULL,
 "T1515" DEC(32,4) NULL,
 "T1530" DEC(32,4) NULL,
 "T1545" DEC(32,4) NULL,
 "T1600" DEC(32,4) NULL,
 "T1615" DEC(32,4) NULL,
 "T1630" DEC(32,4) NULL,
 "T1645" DEC(32,4) NULL,
 "T1700" DEC(32,4) NULL,
 "T1715" DEC(32,4) NULL,
 "T1730" DEC(32,4) NULL,
 "T1745" DEC(32,4) NULL,
 "T1800" DEC(32,4) NULL,
 "T1815" DEC(32,4) NULL,
 "T1830" DEC(32,4) NULL,
 "T1845" DEC(32,4) NULL,
 "T1900" DEC(32,4) NULL,
 "T1915" DEC(32,4) NULL,
 "T1930" DEC(32,4) NULL,
 "T1945" DEC(32,4) NULL,
 "T2000" DEC(32,4) NULL,
 "T2015" DEC(32,4) NULL,
 "T2030" DEC(32,4) NULL,
 "T2045" DEC(32,4) NULL,
 "T2100" DEC(32,4) NULL,
 "T2115" DEC(32,4) NULL,
 "T2130" DEC(32,4) NULL,
 "T2145" DEC(32,4) NULL,
 "T2200" DEC(32,4) NULL,
 "T2215" DEC(32,4) NULL,
 "T2230" DEC(32,4) NULL,
 "T2245" DEC(32,4) NULL,
 "T2300" DEC(32,4) NULL,
 "T2315" DEC(32,4) NULL,
 "T2330" DEC(32,4) NULL,
 "T2345" DEC(32,4) NULL,
 "T2400" DEC(32,4) NULL,
 "CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NULL,
 "UPDATETIME" TIMESTAMP(6) NULL
);

CREATE TABLE "LOAD_CITY_HIS_BASIC_288"
(
 "ID" VARCHAR(32) NOT NULL,
 "DATE" DATE NOT NULL,
 "CITY_ID" VARCHAR(32) NOT NULL,
 "CALIBER_ID" VARCHAR(32) NOT NULL,
 "T0000" DECIMAL(10,4) NULL,
 "T0005" DECIMAL(10,4) NULL,
 "T0010" DECIMAL(10,4) NULL,
 "T0015" DECIMAL(10,4) NULL,
 "T0020" DECIMAL(10,4) NULL,
 "T0025" DECIMAL(10,4) NULL,
 "T0030" DECIMAL(10,4) NULL,
 "T0035" DECIMAL(10,4) NULL,
 "T0040" DECIMAL(10,4) NULL,
 "T0045" DECIMAL(10,4) NULL,
 "T0050" DECIMAL(10,4) NULL,
 "T0055" DECIMAL(10,4) NULL,
 "T0100" DECIMAL(10,4) NULL,
 "T0105" DECIMAL(10,4) NULL,
 "T0110" DECIMAL(10,4) NULL,
 "T0115" DECIMAL(10,4) NULL,
 "T0120" DECIMAL(10,4) NULL,
 "T0125" DECIMAL(10,4) NULL,
 "T0130" DECIMAL(10,4) NULL,
 "T0135" DECIMAL(10,4) NULL,
 "T0140" DECIMAL(10,4) NULL,
 "T0145" DECIMAL(10,4) NULL,
 "T0150" DECIMAL(10,4) NULL,
 "T0155" DECIMAL(10,4) NULL,
 "T0200" DECIMAL(10,4) NULL,
 "T0205" DECIMAL(10,4) NULL,
 "T0210" DECIMAL(10,4) NULL,
 "T0215" DECIMAL(10,4) NULL,
 "T0220" DECIMAL(10,4) NULL,
 "T0225" DECIMAL(10,4) NULL,
 "T0230" DECIMAL(10,4) NULL,
 "T0235" DECIMAL(10,4) NULL,
 "T0240" DECIMAL(10,4) NULL,
 "T0245" DECIMAL(10,4) NULL,
 "T0250" DECIMAL(10,4) NULL,
 "T0255" DECIMAL(10,4) NULL,
 "T0300" DECIMAL(10,4) NULL,
 "T0305" DECIMAL(10,4) NULL,
 "T0310" DECIMAL(10,4) NULL,
 "T0315" DECIMAL(10,4) NULL,
 "T0320" DECIMAL(10,4) NULL,
 "T0325" DECIMAL(10,4) NULL,
 "T0330" DECIMAL(10,4) NULL,
 "T0335" DECIMAL(10,4) NULL,
 "T0340" DECIMAL(10,4) NULL,
 "T0345" DECIMAL(10,4) NULL,
 "T0350" DECIMAL(10,4) NULL,
 "T0355" DECIMAL(10,4) NULL,
 "T0400" DECIMAL(10,4) NULL,
 "T0405" DECIMAL(10,4) NULL,
 "T0410" DECIMAL(10,4) NULL,
 "T0415" DECIMAL(10,4) NULL,
 "T0420" DECIMAL(10,4) NULL,
 "T0425" DECIMAL(10,4) NULL,
 "T0430" DECIMAL(10,4) NULL,
 "T0435" DECIMAL(10,4) NULL,
 "T0440" DECIMAL(10,4) NULL,
 "T0445" DECIMAL(10,4) NULL,
 "T0450" DECIMAL(10,4) NULL,
 "T0455" DECIMAL(10,4) NULL,
 "T0500" DECIMAL(10,4) NULL,
 "T0505" DECIMAL(10,4) NULL,
 "T0510" DECIMAL(10,4) NULL,
 "T0515" DECIMAL(10,4) NULL,
 "T0520" DECIMAL(10,4) NULL,
 "T0525" DECIMAL(10,4) NULL,
 "T0530" DECIMAL(10,4) NULL,
 "T0535" DECIMAL(10,4) NULL,
 "T0540" DECIMAL(10,4) NULL,
 "T0545" DECIMAL(10,4) NULL,
 "T0550" DECIMAL(10,4) NULL,
 "T0555" DECIMAL(10,4) NULL,
 "T0600" DECIMAL(10,4) NULL,
 "T0605" DECIMAL(10,4) NULL,
 "T0610" DECIMAL(10,4) NULL,
 "T0615" DECIMAL(10,4) NULL,
 "T0620" DECIMAL(10,4) NULL,
 "T0625" DECIMAL(10,4) NULL,
 "T0630" DECIMAL(10,4) NULL,
 "T0635" DECIMAL(10,4) NULL,
 "T0640" DECIMAL(10,4) NULL,
 "T0645" DECIMAL(10,4) NULL,
 "T0650" DECIMAL(10,4) NULL,
 "T0655" DECIMAL(10,4) NULL,
 "T0700" DECIMAL(10,4) NULL,
 "T0705" DECIMAL(10,4) NULL,
 "T0710" DECIMAL(10,4) NULL,
 "T0715" DECIMAL(10,4) NULL,
 "T0720" DECIMAL(10,4) NULL,
 "T0725" DECIMAL(10,4) NULL,
 "T0730" DECIMAL(10,4) NULL,
 "T0735" DECIMAL(10,4) NULL,
 "T0740" DECIMAL(10,4) NULL,
 "T0745" DECIMAL(10,4) NULL,
 "T0750" DECIMAL(10,4) NULL,
 "T0755" DECIMAL(10,4) NULL,
 "T0800" DECIMAL(10,4) NULL,
 "T0805" DECIMAL(10,4) NULL,
 "T0810" DECIMAL(10,4) NULL,
 "T0815" DECIMAL(10,4) NULL,
 "T0820" DECIMAL(10,4) NULL,
 "T0825" DECIMAL(10,4) NULL,
 "T0830" DECIMAL(10,4) NULL,
 "T0835" DECIMAL(10,4) NULL,
 "T0840" DECIMAL(10,4) NULL,
 "T0845" DECIMAL(10,4) NULL,
 "T0850" DECIMAL(10,4) NULL,
 "T0855" DECIMAL(10,4) NULL,
 "T0900" DECIMAL(10,4) NULL,
 "T0905" DECIMAL(10,4) NULL,
 "T0910" DECIMAL(10,4) NULL,
 "T0915" DECIMAL(10,4) NULL,
 "T0920" DECIMAL(10,4) NULL,
 "T0925" DECIMAL(10,4) NULL,
 "T0930" DECIMAL(10,4) NULL,
 "T0935" DECIMAL(10,4) NULL,
 "T0940" DECIMAL(10,4) NULL,
 "T0945" DECIMAL(10,4) NULL,
 "T0950" DECIMAL(10,4) NULL,
 "T0955" DECIMAL(10,4) NULL,
 "T1000" DECIMAL(10,4) NULL,
 "T1005" DECIMAL(10,4) NULL,
 "T1010" DECIMAL(10,4) NULL,
 "T1015" DECIMAL(10,4) NULL,
 "T1020" DECIMAL(10,4) NULL,
 "T1025" DECIMAL(10,4) NULL,
 "T1030" DECIMAL(10,4) NULL,
 "T1035" DECIMAL(10,4) NULL,
 "T1040" DECIMAL(10,4) NULL,
 "T1045" DECIMAL(10,4) NULL,
 "T1050" DECIMAL(10,4) NULL,
 "T1055" DECIMAL(10,4) NULL,
 "T1100" DECIMAL(10,4) NULL,
 "T1105" DECIMAL(10,4) NULL,
 "T1110" DECIMAL(10,4) NULL,
 "T1115" DECIMAL(10,4) NULL,
 "T1120" DECIMAL(10,4) NULL,
 "T1125" DECIMAL(10,4) NULL,
 "T1130" DECIMAL(10,4) NULL,
 "T1135" DECIMAL(10,4) NULL,
 "T1140" DECIMAL(10,4) NULL,
 "T1145" DECIMAL(10,4) NULL,
 "T1150" DECIMAL(10,4) NULL,
 "T1155" DECIMAL(10,4) NULL,
 "T1200" DECIMAL(10,4) NULL,
 "T1205" DECIMAL(10,4) NULL,
 "T1210" DECIMAL(10,4) NULL,
 "T1215" DECIMAL(10,4) NULL,
 "T1220" DECIMAL(10,4) NULL,
 "T1225" DECIMAL(10,4) NULL,
 "T1230" DECIMAL(10,4) NULL,
 "T1235" DECIMAL(10,4) NULL,
 "T1240" DECIMAL(10,4) NULL,
 "T1245" DECIMAL(10,4) NULL,
 "T1250" DECIMAL(10,4) NULL,
 "T1255" DECIMAL(10,4) NULL,
 "T1300" DECIMAL(10,4) NULL,
 "T1305" DECIMAL(10,4) NULL,
 "T1310" DECIMAL(10,4) NULL,
 "T1315" DECIMAL(10,4) NULL,
 "T1320" DECIMAL(10,4) NULL,
 "T1325" DECIMAL(10,4) NULL,
 "T1330" DECIMAL(10,4) NULL,
 "T1335" DECIMAL(10,4) NULL,
 "T1340" DECIMAL(10,4) NULL,
 "T1345" DECIMAL(10,4) NULL,
 "T1350" DECIMAL(10,4) NULL,
 "T1355" DECIMAL(10,4) NULL,
 "T1400" DECIMAL(10,4) NULL,
 "T1405" DECIMAL(10,4) NULL,
 "T1410" DECIMAL(10,4) NULL,
 "T1415" DECIMAL(10,4) NULL,
 "T1420" DECIMAL(10,4) NULL,
 "T1425" DECIMAL(10,4) NULL,
 "T1430" DECIMAL(10,4) NULL,
 "T1435" DECIMAL(10,4) NULL,
 "T1440" DECIMAL(10,4) NULL,
 "T1445" DECIMAL(10,4) NULL,
 "T1450" DECIMAL(10,4) NULL,
 "T1455" DECIMAL(10,4) NULL,
 "T1500" DECIMAL(10,4) NULL,
 "T1505" DECIMAL(10,4) NULL,
 "T1510" DECIMAL(10,4) NULL,
 "T1515" DECIMAL(10,4) NULL,
 "T1520" DECIMAL(10,4) NULL,
 "T1525" DECIMAL(10,4) NULL,
 "T1530" DECIMAL(10,4) NULL,
 "T1535" DECIMAL(10,4) NULL,
 "T1540" DECIMAL(10,4) NULL,
 "T1545" DECIMAL(10,4) NULL,
 "T1550" DECIMAL(10,4) NULL,
 "T1555" DECIMAL(10,4) NULL,
 "T1600" DECIMAL(10,4) NULL,
 "T1605" DECIMAL(10,4) NULL,
 "T1610" DECIMAL(10,4) NULL,
 "T1615" DECIMAL(10,4) NULL,
 "T1620" DECIMAL(10,4) NULL,
 "T1625" DECIMAL(10,4) NULL,
 "T1630" DECIMAL(10,4) NULL,
 "T1635" DECIMAL(10,4) NULL,
 "T1640" DECIMAL(10,4) NULL,
 "T1645" DECIMAL(10,4) NULL,
 "T1650" DECIMAL(10,4) NULL,
 "T1655" DECIMAL(10,4) NULL,
 "T1700" DECIMAL(10,4) NULL,
 "T1705" DECIMAL(10,4) NULL,
 "T1710" DECIMAL(10,4) NULL,
 "T1715" DECIMAL(10,4) NULL,
 "T1720" DECIMAL(10,4) NULL,
 "T1725" DECIMAL(10,4) NULL,
 "T1730" DECIMAL(10,4) NULL,
 "T1735" DECIMAL(10,4) NULL,
 "T1740" DECIMAL(10,4) NULL,
 "T1745" DECIMAL(10,4) NULL,
 "T1750" DECIMAL(10,4) NULL,
 "T1755" DECIMAL(10,4) NULL,
 "T1800" DECIMAL(10,4) NULL,
 "T1805" DECIMAL(10,4) NULL,
 "T1810" DECIMAL(10,4) NULL,
 "T1815" DECIMAL(10,4) NULL,
 "T1820" DECIMAL(10,4) NULL,
 "T1825" DECIMAL(10,4) NULL,
 "T1830" DECIMAL(10,4) NULL,
 "T1835" DECIMAL(10,4) NULL,
 "T1840" DECIMAL(10,4) NULL,
 "T1845" DECIMAL(10,4) NULL,
 "T1850" DECIMAL(10,4) NULL,
 "T1855" DECIMAL(10,4) NULL,
 "T1900" DECIMAL(10,4) NULL,
 "T1905" DECIMAL(10,4) NULL,
 "T1910" DECIMAL(10,4) NULL,
 "T1915" DECIMAL(10,4) NULL,
 "T1920" DECIMAL(10,4) NULL,
 "T1925" DECIMAL(10,4) NULL,
 "T1930" DECIMAL(10,4) NULL,
 "T1935" DECIMAL(10,4) NULL,
 "T1940" DECIMAL(10,4) NULL,
 "T1945" DECIMAL(10,4) NULL,
 "T1950" DECIMAL(10,4) NULL,
 "T1955" DECIMAL(10,4) NULL,
 "T2000" DECIMAL(10,4) NULL,
 "T2005" DECIMAL(10,4) NULL,
 "T2010" DECIMAL(10,4) NULL,
 "T2015" DECIMAL(10,4) NULL,
 "T2020" DECIMAL(10,4) NULL,
 "T2025" DECIMAL(10,4) NULL,
 "T2030" DECIMAL(10,4) NULL,
 "T2035" DECIMAL(10,4) NULL,
 "T2040" DECIMAL(10,4) NULL,
 "T2045" DECIMAL(10,4) NULL,
 "T2050" DECIMAL(10,4) NULL,
 "T2055" DECIMAL(10,4) NULL,
 "T2100" DECIMAL(10,4) NULL,
 "T2105" DECIMAL(10,4) NULL,
 "T2110" DECIMAL(10,4) NULL,
 "T2115" DECIMAL(10,4) NULL,
 "T2120" DECIMAL(10,4) NULL,
 "T2125" DECIMAL(10,4) NULL,
 "T2130" DECIMAL(10,4) NULL,
 "T2135" DECIMAL(10,4) NULL,
 "T2140" DECIMAL(10,4) NULL,
 "T2145" DECIMAL(10,4) NULL,
 "T2150" DECIMAL(10,4) NULL,
 "T2155" DECIMAL(10,4) NULL,
 "T2200" DECIMAL(10,4) NULL,
 "T2205" DECIMAL(10,4) NULL,
 "T2210" DECIMAL(10,4) NULL,
 "T2215" DECIMAL(10,4) NULL,
 "T2220" DECIMAL(10,4) NULL,
 "T2225" DECIMAL(10,4) NULL,
 "T2230" DECIMAL(10,4) NULL,
 "T2235" DECIMAL(10,4) NULL,
 "T2240" DECIMAL(10,4) NULL,
 "T2245" DECIMAL(10,4) NULL,
 "T2250" DECIMAL(10,4) NULL,
 "T2255" DECIMAL(10,4) NULL,
 "T2300" DECIMAL(10,4) NULL,
 "T2305" DECIMAL(10,4) NULL,
 "T2310" DECIMAL(10,4) NULL,
 "T2315" DECIMAL(10,4) NULL,
 "T2320" DECIMAL(10,4) NULL,
 "T2325" DECIMAL(10,4) NULL,
 "T2330" DECIMAL(10,4) NULL,
 "T2335" DECIMAL(10,4) NULL,
 "T2340" DECIMAL(10,4) NULL,
 "T2345" DECIMAL(10,4) NULL,
 "T2350" DECIMAL(10,4) NULL,
 "T2355" DECIMAL(10,4) NULL,
 "T2400" DECIMAL(10,4) NULL,
 "CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NULL,
 "UPDATETIME" TIMESTAMP(6) NULL
);

CREATE TABLE "LOAD_CITY_HIS_BASIC"
(
 "ID" VARCHAR(32) NOT NULL,
 "DATE" DATE NOT NULL,
 "CITY_ID" VARCHAR(32) NOT NULL,
 "CALIBER_ID" VARCHAR(32) NOT NULL,
 "T0000" DEC(32,4) NULL,
 "T0015" DEC(32,4) NULL,
 "T0030" DEC(32,4) NULL,
 "T0045" DEC(32,4) NULL,
 "T0100" DEC(32,4) NULL,
 "T0115" DEC(32,4) NULL,
 "T0130" DEC(32,4) NULL,
 "T0145" DEC(32,4) NULL,
 "T0200" DEC(32,4) NULL,
 "T0215" DEC(32,4) NULL,
 "T0230" DEC(32,4) NULL,
 "T0245" DEC(32,4) NULL,
 "T0300" DEC(32,4) NULL,
 "T0315" DEC(32,4) NULL,
 "T0330" DEC(32,4) NULL,
 "T0345" DEC(32,4) NULL,
 "T0400" DEC(32,4) NULL,
 "T0415" DEC(32,4) NULL,
 "T0430" DEC(32,4) NULL,
 "T0445" DEC(32,4) NULL,
 "T0500" DEC(32,4) NULL,
 "T0515" DEC(32,4) NULL,
 "T0530" DEC(32,4) NULL,
 "T0545" DEC(32,4) NULL,
 "T0600" DEC(32,4) NULL,
 "T0615" DEC(32,4) NULL,
 "T0630" DEC(32,4) NULL,
 "T0645" DEC(32,4) NULL,
 "T0700" DEC(32,4) NULL,
 "T0715" DEC(32,4) NULL,
 "T0730" DEC(32,4) NULL,
 "T0745" DEC(32,4) NULL,
 "T0800" DEC(32,4) NULL,
 "T0815" DEC(32,4) NULL,
 "T0830" DEC(32,4) NULL,
 "T0845" DEC(32,4) NULL,
 "T0900" DEC(32,4) NULL,
 "T0915" DEC(32,4) NULL,
 "T0930" DEC(32,4) NULL,
 "T0945" DEC(32,4) NULL,
 "T1000" DEC(32,4) NULL,
 "T1015" DEC(32,4) NULL,
 "T1030" DEC(32,4) NULL,
 "T1045" DEC(32,4) NULL,
 "T1100" DEC(32,4) NULL,
 "T1115" DEC(32,4) NULL,
 "T1130" DEC(32,4) NULL,
 "T1145" DEC(32,4) NULL,
 "T1200" DEC(32,4) NULL,
 "T1215" DEC(32,4) NULL,
 "T1230" DEC(32,4) NULL,
 "T1245" DEC(32,4) NULL,
 "T1300" DEC(32,4) NULL,
 "T1315" DEC(32,4) NULL,
 "T1330" DEC(32,4) NULL,
 "T1345" DEC(32,4) NULL,
 "T1400" DEC(32,4) NULL,
 "T1415" DEC(32,4) NULL,
 "T1430" DEC(32,4) NULL,
 "T1445" DEC(32,4) NULL,
 "T1500" DEC(32,4) NULL,
 "T1515" DEC(32,4) NULL,
 "T1530" DEC(32,4) NULL,
 "T1545" DEC(32,4) NULL,
 "T1600" DEC(32,4) NULL,
 "T1615" DEC(32,4) NULL,
 "T1630" DEC(32,4) NULL,
 "T1645" DEC(32,4) NULL,
 "T1700" DEC(32,4) NULL,
 "T1715" DEC(32,4) NULL,
 "T1730" DEC(32,4) NULL,
 "T1745" DEC(32,4) NULL,
 "T1800" DEC(32,4) NULL,
 "T1815" DEC(32,4) NULL,
 "T1830" DEC(32,4) NULL,
 "T1845" DEC(32,4) NULL,
 "T1900" DEC(32,4) NULL,
 "T1915" DEC(32,4) NULL,
 "T1930" DEC(32,4) NULL,
 "T1945" DEC(32,4) NULL,
 "T2000" DEC(32,4) NULL,
 "T2015" DEC(32,4) NULL,
 "T2030" DEC(32,4) NULL,
 "T2045" DEC(32,4) NULL,
 "T2100" DEC(32,4) NULL,
 "T2115" DEC(32,4) NULL,
 "T2130" DEC(32,4) NULL,
 "T2145" DEC(32,4) NULL,
 "T2200" DEC(32,4) NULL,
 "T2215" DEC(32,4) NULL,
 "T2230" DEC(32,4) NULL,
 "T2245" DEC(32,4) NULL,
 "T2300" DEC(32,4) NULL,
 "T2315" DEC(32,4) NULL,
 "T2330" DEC(32,4) NULL,
 "T2345" DEC(32,4) NULL,
 "T2400" DEC(32,4) NULL,
 "CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NULL,
 "UPDATETIME" TIMESTAMP(6) NULL
);

CREATE TABLE "LOAD_CITY_FC_SHORT"
(
 "ID" VARCHAR(32) NOT NULL,
 "DATE" DATE NOT NULL,
 "CITY_ID" VARCHAR(32) NOT NULL,
 "CALIBER_ID" VARCHAR(32) NOT NULL,
 "ALGORITHM_ID" VARCHAR(32) NOT NULL,
 "TYPE" TINYINT NOT NULL,
 "START_TIME" VARCHAR(10) NOT NULL,
 "T1" DECIMAL(32,4) NULL,
 "T2" DECIMAL(32,4) NULL,
 "T3" DECIMAL(32,4) NULL,
 "T4" DECIMAL(32,4) NULL,
 "T5" DECIMAL(32,4) NULL,
 "T6" DECIMAL(32,4) NULL,
 "T7" DECIMAL(32,4) NULL,
 "T8" DECIMAL(32,4) NULL,
 "T9" DECIMAL(32,4) NULL,
 "T10" DECIMAL(32,4) NULL,
 "T11" DECIMAL(32,4) NULL,
 "T12" DECIMAL(32,4) NULL,
 "T13" DECIMAL(32,4) NULL,
 "T14" DECIMAL(32,4) NULL,
 "T15" DECIMAL(32,4) NULL,
 "T16" DECIMAL(32,4) NULL,
 "T17" DECIMAL(32,4) NULL,
 "T18" DECIMAL(32,4) NULL,
 "T19" DECIMAL(32,4) NULL,
 "T20" DECIMAL(32,4) NULL,
 "T21" DECIMAL(32,4) NULL,
 "T22" DECIMAL(32,4) NULL,
 "T23" DECIMAL(32,4) NULL,
 "T24" DECIMAL(32,4) NULL,
 "T25" DECIMAL(32,4) NULL,
 "T26" DECIMAL(32,4) NULL,
 "T27" DECIMAL(32,4) NULL,
 "T28" DECIMAL(32,4) NULL,
 "T29" DECIMAL(32,4) NULL,
 "T30" DECIMAL(32,4) NULL,
 "T31" DECIMAL(32,4) NULL,
 "T32" DECIMAL(32,4) NULL,
 "T33" DECIMAL(32,4) NULL,
 "T34" DECIMAL(32,4) NULL,
 "T35" DECIMAL(32,4) NULL,
 "T36" DECIMAL(32,4) NULL,
 "T37" DECIMAL(32,4) NULL,
 "T38" DECIMAL(32,4) NULL,
 "T39" DECIMAL(32,4) NULL,
 "T40" DECIMAL(32,4) NULL,
 "T41" DECIMAL(32,4) NULL,
 "T42" DECIMAL(32,4) NULL,
 "T43" DECIMAL(32,4) NULL,
 "T44" DECIMAL(32,4) NULL,
 "T45" DECIMAL(32,4) NULL,
 "T46" DECIMAL(32,4) NULL,
 "T47" DECIMAL(32,4) NULL,
 "T48" DECIMAL(32,4) NULL,
 "T49" DECIMAL(32,4) NULL,
 "T50" DECIMAL(32,4) NULL,
 "T51" DECIMAL(32,4) NULL,
 "T52" DECIMAL(32,4) NULL,
 "T53" DECIMAL(32,4) NULL,
 "T54" DECIMAL(32,4) NULL,
 "T55" DECIMAL(32,4) NULL,
 "T56" DECIMAL(32,4) NULL,
 "T57" DECIMAL(32,4) NULL,
 "T58" DECIMAL(32,4) NULL,
 "T59" DECIMAL(32,4) NULL,
 "T60" DECIMAL(32,4) NULL,
 "T61" DECIMAL(32,4) NULL,
 "T62" DECIMAL(32,4) NULL,
 "T63" DECIMAL(32,4) NULL,
 "T64" DECIMAL(32,4) NULL,
 "T65" DECIMAL(32,4) NULL,
 "T66" DECIMAL(32,4) NULL,
 "T67" DECIMAL(32,4) NULL,
 "T68" DECIMAL(32,4) NULL,
 "T69" DECIMAL(32,4) NULL,
 "T70" DECIMAL(32,4) NULL,
 "T71" DECIMAL(32,4) NULL,
 "T72" DECIMAL(32,4) NULL,
 "T73" DECIMAL(32,4) NULL,
 "T74" DECIMAL(32,4) NULL,
 "T75" DECIMAL(32,4) NULL,
 "T76" DECIMAL(32,4) NULL,
 "T77" DECIMAL(32,4) NULL,
 "T78" DECIMAL(32,4) NULL,
 "T79" DECIMAL(32,4) NULL,
 "T80" DECIMAL(32,4) NULL,
 "T81" DECIMAL(32,4) NULL,
 "T82" DECIMAL(32,4) NULL,
 "T83" DECIMAL(32,4) NULL,
 "T84" DECIMAL(32,4) NULL,
 "T85" DECIMAL(32,4) NULL,
 "T86" DECIMAL(32,4) NULL,
 "T87" DECIMAL(32,4) NULL,
 "T88" DECIMAL(32,4) NULL,
 "T89" DECIMAL(32,4) NULL,
 "T90" DECIMAL(32,4) NULL,
 "T91" DECIMAL(32,4) NULL,
 "T92" DECIMAL(32,4) NULL,
 "T93" DECIMAL(32,4) NULL,
 "T94" DECIMAL(32,4) NULL,
 "T95" DECIMAL(32,4) NULL,
 "T96" DECIMAL(32,4) NULL,
 "CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL,
 "UPDATETIME" TIMESTAMP(6) NULL
);

CREATE TABLE "LOAD_CITY_FC_DFD"
(
 "ID" VARCHAR(32) NOT NULL,
 "DATE" DATE NOT NULL,
 "CITY_ID" VARCHAR(32) NOT NULL,
 "CALIBER_ID" VARCHAR(32) DEFAULT NULL
 NULL,
 "ALGORITHM_ID" VARCHAR(32) NOT NULL,
 "T0000" DEC(32,4) NULL,
 "T0015" DEC(32,4) NULL,
 "T0030" DEC(32,4) NULL,
 "T0045" DEC(32,4) NULL,
 "T0100" DEC(32,4) NULL,
 "T0115" DEC(32,4) NULL,
 "T0130" DEC(32,4) NULL,
 "T0145" DEC(32,4) NULL,
 "T0200" DEC(32,4) NULL,
 "T0215" DEC(32,4) NULL,
 "T0230" DEC(32,4) NULL,
 "T0245" DEC(32,4) NULL,
 "T0300" DEC(32,4) NULL,
 "T0315" DEC(32,4) NULL,
 "T0330" DEC(32,4) NULL,
 "T0345" DEC(32,4) NULL,
 "T0400" DEC(32,4) NULL,
 "T0415" DEC(32,4) NULL,
 "T0430" DEC(32,4) NULL,
 "T0445" DEC(32,4) NULL,
 "T0500" DEC(32,4) NULL,
 "T0515" DEC(32,4) NULL,
 "T0530" DEC(32,4) NULL,
 "T0545" DEC(32,4) NULL,
 "T0600" DEC(32,4) NULL,
 "T0615" DEC(32,4) NULL,
 "T0630" DEC(32,4) NULL,
 "T0645" DEC(32,4) NULL,
 "T0700" DEC(32,4) NULL,
 "T0715" DEC(32,4) NULL,
 "T0730" DEC(32,4) NULL,
 "T0745" DEC(32,4) NULL,
 "T0800" DEC(32,4) NULL,
 "T0815" DEC(32,4) NULL,
 "T0830" DEC(32,4) NULL,
 "T0845" DEC(32,4) NULL,
 "T0900" DEC(32,4) NULL,
 "T0915" DEC(32,4) NULL,
 "T0930" DEC(32,4) NULL,
 "T0945" DEC(32,4) NULL,
 "T1000" DEC(32,4) NULL,
 "T1015" DEC(32,4) NULL,
 "T1030" DEC(32,4) NULL,
 "T1045" DEC(32,4) NULL,
 "T1100" DEC(32,4) NULL,
 "T1115" DEC(32,4) NULL,
 "T1130" DEC(32,4) NULL,
 "T1145" DEC(32,4) NULL,
 "T1200" DEC(32,4) NULL,
 "T1215" DEC(32,4) NULL,
 "T1230" DEC(32,4) NULL,
 "T1245" DEC(32,4) NULL,
 "T1300" DEC(32,4) NULL,
 "T1315" DEC(32,4) NULL,
 "T1330" DEC(32,4) NULL,
 "T1345" DEC(32,4) NULL,
 "T1400" DEC(32,4) NULL,
 "T1415" DEC(32,4) NULL,
 "T1430" DEC(32,4) NULL,
 "T1445" DEC(32,4) NULL,
 "T1500" DEC(32,4) NULL,
 "T1515" DEC(32,4) NULL,
 "T1530" DEC(32,4) NULL,
 "T1545" DEC(32,4) NULL,
 "T1600" DEC(32,4) NULL,
 "T1615" DEC(32,4) NULL,
 "T1630" DEC(32,4) NULL,
 "T1645" DEC(32,4) NULL,
 "T1700" DEC(32,4) NULL,
 "T1715" DEC(32,4) NULL,
 "T1730" DEC(32,4) NULL,
 "T1745" DEC(32,4) NULL,
 "T1800" DEC(32,4) NULL,
 "T1815" DEC(32,4) NULL,
 "T1830" DEC(32,4) NULL,
 "T1845" DEC(32,4) NULL,
 "T1900" DEC(32,4) NULL,
 "T1915" DEC(32,4) NULL,
 "T1930" DEC(32,4) NULL,
 "T1945" DEC(32,4) NULL,
 "T2000" DEC(32,4) NULL,
 "T2015" DEC(32,4) NULL,
 "T2030" DEC(32,4) NULL,
 "T2045" DEC(32,4) NULL,
 "T2100" DEC(32,4) NULL,
 "T2115" DEC(32,4) NULL,
 "T2130" DEC(32,4) NULL,
 "T2145" DEC(32,4) NULL,
 "T2200" DEC(32,4) NULL,
 "T2215" DEC(32,4) NULL,
 "T2230" DEC(32,4) NULL,
 "T2245" DEC(32,4) NULL,
 "T2300" DEC(32,4) NULL,
 "T2315" DEC(32,4) NULL,
 "T2330" DEC(32,4) NULL,
 "T2345" DEC(32,4) NULL,
 "T2400" DEC(32,4) NULL,
 "CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NULL,
 "UPDATETIME" TIMESTAMP(6) NULL,
 "RECOMMEND" TINYINT DEFAULT NULL
 NULL,
 "REPORT" TINYINT DEFAULT '0'
 NULL,
 "REPORT_TIME" DATETIME(6) DEFAULT NULL
 NULL,
 "USER_ID" VARCHAR(32) DEFAULT NULL
 NULL,
 "SUCCEED" TINYINT DEFAULT NULL
 NULL,
 "TYPE" INTEGER DEFAULT '1'
 NULL
);

CREATE TABLE "LOAD_CITY_FC_BATCH"
(
 "ID" VARCHAR(32) NOT NULL,
 "DATE" DATE NOT NULL,
 "CITY_ID" VARCHAR(32) NOT NULL,
 "CALIBER_ID" VARCHAR(32) NOT NULL,
 "ALGORITHM_ID" VARCHAR(32) NOT NULL,
 "BATCH_ID" VARCHAR(32) NOT NULL,
 "T0000" DEC(32,4) NULL,
 "T0015" DEC(32,4) NULL,
 "T0030" DEC(32,4) NULL,
 "T0045" DEC(32,4) NULL,
 "T0100" DEC(32,4) NULL,
 "T0115" DEC(32,4) NULL,
 "T0130" DEC(32,4) NULL,
 "T0145" DEC(32,4) NULL,
 "T0200" DEC(32,4) NULL,
 "T0215" DEC(32,4) NULL,
 "T0230" DEC(32,4) NULL,
 "T0245" DEC(32,4) NULL,
 "T0300" DEC(32,4) NULL,
 "T0315" DEC(32,4) NULL,
 "T0330" DEC(32,4) NULL,
 "T0345" DEC(32,4) NULL,
 "T0400" DEC(32,4) NULL,
 "T0415" DEC(32,4) NULL,
 "T0430" DEC(32,4) NULL,
 "T0445" DEC(32,4) NULL,
 "T0500" DEC(32,4) NULL,
 "T0515" DEC(32,4) NULL,
 "T0530" DEC(32,4) NULL,
 "T0545" DEC(32,4) NULL,
 "T0600" DEC(32,4) NULL,
 "T0615" DEC(32,4) NULL,
 "T0630" DEC(32,4) NULL,
 "T0645" DEC(32,4) NULL,
 "T0700" DEC(32,4) NULL,
 "T0715" DEC(32,4) NULL,
 "T0730" DEC(32,4) NULL,
 "T0745" DEC(32,4) NULL,
 "T0800" DEC(32,4) NULL,
 "T0815" DEC(32,4) NULL,
 "T0830" DEC(32,4) NULL,
 "T0845" DEC(32,4) NULL,
 "T0900" DEC(32,4) NULL,
 "T0915" DEC(32,4) NULL,
 "T0930" DEC(32,4) NULL,
 "T0945" DEC(32,4) NULL,
 "T1000" DEC(32,4) NULL,
 "T1015" DEC(32,4) NULL,
 "T1030" DEC(32,4) NULL,
 "T1045" DEC(32,4) NULL,
 "T1100" DEC(32,4) NULL,
 "T1115" DEC(32,4) NULL,
 "T1130" DEC(32,4) NULL,
 "T1145" DEC(32,4) NULL,
 "T1200" DEC(32,4) NULL,
 "T1215" DEC(32,4) NULL,
 "T1230" DEC(32,4) NULL,
 "T1245" DEC(32,4) NULL,
 "T1300" DEC(32,4) NULL,
 "T1315" DEC(32,4) NULL,
 "T1330" DEC(32,4) NULL,
 "T1345" DEC(32,4) NULL,
 "T1400" DEC(32,4) NULL,
 "T1415" DEC(32,4) NULL,
 "T1430" DEC(32,4) NULL,
 "T1445" DEC(32,4) NULL,
 "T1500" DEC(32,4) NULL,
 "T1515" DEC(32,4) NULL,
 "T1530" DEC(32,4) NULL,
 "T1545" DEC(32,4) NULL,
 "T1600" DEC(32,4) NULL,
 "T1615" DEC(32,4) NULL,
 "T1630" DEC(32,4) NULL,
 "T1645" DEC(32,4) NULL,
 "T1700" DEC(32,4) NULL,
 "T1715" DEC(32,4) NULL,
 "T1730" DEC(32,4) NULL,
 "T1745" DEC(32,4) NULL,
 "T1800" DEC(32,4) NULL,
 "T1815" DEC(32,4) NULL,
 "T1830" DEC(32,4) NULL,
 "T1845" DEC(32,4) NULL,
 "T1900" DEC(32,4) NULL,
 "T1915" DEC(32,4) NULL,
 "T1930" DEC(32,4) NULL,
 "T1945" DEC(32,4) NULL,
 "T2000" DEC(32,4) NULL,
 "T2015" DEC(32,4) NULL,
 "T2030" DEC(32,4) NULL,
 "T2045" DEC(32,4) NULL,
 "T2100" DEC(32,4) NULL,
 "T2115" DEC(32,4) NULL,
 "T2130" DEC(32,4) NULL,
 "T2145" DEC(32,4) NULL,
 "T2200" DEC(32,4) NULL,
 "T2215" DEC(32,4) NULL,
 "T2230" DEC(32,4) NULL,
 "T2245" DEC(32,4) NULL,
 "T2300" DEC(32,4) NULL,
 "T2315" DEC(32,4) NULL,
 "T2330" DEC(32,4) NULL,
 "T2345" DEC(32,4) NULL,
 "T2400" DEC(32,4) NULL,
 "CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL,
 "UPDATETIME" TIMESTAMP(6) NULL,
 "RECOMMEND" BIT NULL,
 "REPORT" BIT DEFAULT 0
 NULL,
 "USER_ID" VARCHAR(32) NULL,
 "SUCCEED" BIT NULL,
 "REPORT_TIME" TIMESTAMP(6) DEFAULT NULL
 NULL
);

CREATE TABLE "LOAD_CITY_FC_BASIC"
(
 "ID" VARCHAR(32) NOT NULL,
 "DATE" DATE NOT NULL,
 "CITY_ID" VARCHAR(32) NOT NULL,
 "CALIBER_ID" VARCHAR(32) NOT NULL,
 "ALGORITHM_ID" VARCHAR(32) NOT NULL,
 "T0000" DEC(32,4) NULL,
 "T0015" DEC(32,4) NULL,
 "T0030" DEC(32,4) NULL,
 "T0045" DEC(32,4) NULL,
 "T0100" DEC(32,4) NULL,
 "T0115" DEC(32,4) NULL,
 "T0130" DEC(32,4) NULL,
 "T0145" DEC(32,4) NULL,
 "T0200" DEC(32,4) NULL,
 "T0215" DEC(32,4) NULL,
 "T0230" DEC(32,4) NULL,
 "T0245" DEC(32,4) NULL,
 "T0300" DEC(32,4) NULL,
 "T0315" DEC(32,4) NULL,
 "T0330" DEC(32,4) NULL,
 "T0345" DEC(32,4) NULL,
 "T0400" DEC(32,4) NULL,
 "T0415" DEC(32,4) NULL,
 "T0430" DEC(32,4) NULL,
 "T0445" DEC(32,4) NULL,
 "T0500" DEC(32,4) NULL,
 "T0515" DEC(32,4) NULL,
 "T0530" DEC(32,4) NULL,
 "T0545" DEC(32,4) NULL,
 "T0600" DEC(32,4) NULL,
 "T0615" DEC(32,4) NULL,
 "T0630" DEC(32,4) NULL,
 "T0645" DEC(32,4) NULL,
 "T0700" DEC(32,4) NULL,
 "T0715" DEC(32,4) NULL,
 "T0730" DEC(32,4) NULL,
 "T0745" DEC(32,4) NULL,
 "T0800" DEC(32,4) NULL,
 "T0815" DEC(32,4) NULL,
 "T0830" DEC(32,4) NULL,
 "T0845" DEC(32,4) NULL,
 "T0900" DEC(32,4) NULL,
 "T0915" DEC(32,4) NULL,
 "T0930" DEC(32,4) NULL,
 "T0945" DEC(32,4) NULL,
 "T1000" DEC(32,4) NULL,
 "T1015" DEC(32,4) NULL,
 "T1030" DEC(32,4) NULL,
 "T1045" DEC(32,4) NULL,
 "T1100" DEC(32,4) NULL,
 "T1115" DEC(32,4) NULL,
 "T1130" DEC(32,4) NULL,
 "T1145" DEC(32,4) NULL,
 "T1200" DEC(32,4) NULL,
 "T1215" DEC(32,4) NULL,
 "T1230" DEC(32,4) NULL,
 "T1245" DEC(32,4) NULL,
 "T1300" DEC(32,4) NULL,
 "T1315" DEC(32,4) NULL,
 "T1330" DEC(32,4) NULL,
 "T1345" DEC(32,4) NULL,
 "T1400" DEC(32,4) NULL,
 "T1415" DEC(32,4) NULL,
 "T1430" DEC(32,4) NULL,
 "T1445" DEC(32,4) NULL,
 "T1500" DEC(32,4) NULL,
 "T1515" DEC(32,4) NULL,
 "T1530" DEC(32,4) NULL,
 "T1545" DEC(32,4) NULL,
 "T1600" DEC(32,4) NULL,
 "T1615" DEC(32,4) NULL,
 "T1630" DEC(32,4) NULL,
 "T1645" DEC(32,4) NULL,
 "T1700" DEC(32,4) NULL,
 "T1715" DEC(32,4) NULL,
 "T1730" DEC(32,4) NULL,
 "T1745" DEC(32,4) NULL,
 "T1800" DEC(32,4) NULL,
 "T1815" DEC(32,4) NULL,
 "T1830" DEC(32,4) NULL,
 "T1845" DEC(32,4) NULL,
 "T1900" DEC(32,4) NULL,
 "T1915" DEC(32,4) NULL,
 "T1930" DEC(32,4) NULL,
 "T1945" DEC(32,4) NULL,
 "T2000" DEC(32,4) NULL,
 "T2015" DEC(32,4) NULL,
 "T2030" DEC(32,4) NULL,
 "T2045" DEC(32,4) NULL,
 "T2100" DEC(32,4) NULL,
 "T2115" DEC(32,4) NULL,
 "T2130" DEC(32,4) NULL,
 "T2145" DEC(32,4) NULL,
 "T2200" DEC(32,4) NULL,
 "T2215" DEC(32,4) NULL,
 "T2230" DEC(32,4) NULL,
 "T2245" DEC(32,4) NULL,
 "T2300" DEC(32,4) NULL,
 "T2315" DEC(32,4) NULL,
 "T2330" DEC(32,4) NULL,
 "T2345" DEC(32,4) NULL,
 "T2400" DEC(32,4) NULL,
 "CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL,
 "UPDATETIME" TIMESTAMP(6) NULL,
 "RECOMMEND" BIT NULL,
 "REPORT" BIT DEFAULT 0
 NULL,
 "USER_ID" VARCHAR(32) NULL,
 "SUCCEED" BIT NULL,
 "REPORT_TIME" TIMESTAMP(6) DEFAULT NULL
 NULL
);

CREATE TABLE "LOAD_CITY_FC_288"
(
 "ID" VARCHAR(32) NOT NULL,
 "DATE" DATE NOT NULL,
 "CITY_ID" VARCHAR(32) NOT NULL,
 "CALIBER_ID" VARCHAR(32) NOT NULL,
 "ALGORITHM_ID" VARCHAR(32) NOT NULL,
 "T0000" DECIMAL(10,4) NULL,
 "T0005" DECIMAL(10,4) NULL,
 "T0010" DECIMAL(10,4) NULL,
 "T0015" DECIMAL(10,4) NULL,
 "T0020" DECIMAL(10,4) NULL,
 "T0025" DECIMAL(10,4) NULL,
 "T0030" DECIMAL(10,4) NULL,
 "T0035" DECIMAL(10,4) NULL,
 "T0040" DECIMAL(10,4) NULL,
 "T0045" DECIMAL(10,4) NULL,
 "T0050" DECIMAL(10,4) NULL,
 "T0055" DECIMAL(10,4) NULL,
 "T0100" DECIMAL(10,4) NULL,
 "T0105" DECIMAL(10,4) NULL,
 "T0110" DECIMAL(10,4) NULL,
 "T0115" DECIMAL(10,4) NULL,
 "T0120" DECIMAL(10,4) NULL,
 "T0125" DECIMAL(10,4) NULL,
 "T0130" DECIMAL(10,4) NULL,
 "T0135" DECIMAL(10,4) NULL,
 "T0140" DECIMAL(10,4) NULL,
 "T0145" DECIMAL(10,4) NULL,
 "T0150" DECIMAL(10,4) NULL,
 "T0155" DECIMAL(10,4) NULL,
 "T0200" DECIMAL(10,4) NULL,
 "T0205" DECIMAL(10,4) NULL,
 "T0210" DECIMAL(10,4) NULL,
 "T0215" DECIMAL(10,4) NULL,
 "T0220" DECIMAL(10,4) NULL,
 "T0225" DECIMAL(10,4) NULL,
 "T0230" DECIMAL(10,4) NULL,
 "T0235" DECIMAL(10,4) NULL,
 "T0240" DECIMAL(10,4) NULL,
 "T0245" DECIMAL(10,4) NULL,
 "T0250" DECIMAL(10,4) NULL,
 "T0255" DECIMAL(10,4) NULL,
 "T0300" DECIMAL(10,4) NULL,
 "T0305" DECIMAL(10,4) NULL,
 "T0310" DECIMAL(10,4) NULL,
 "T0315" DECIMAL(10,4) NULL,
 "T0320" DECIMAL(10,4) NULL,
 "T0325" DECIMAL(10,4) NULL,
 "T0330" DECIMAL(10,4) NULL,
 "T0335" DECIMAL(10,4) NULL,
 "T0340" DECIMAL(10,4) NULL,
 "T0345" DECIMAL(10,4) NULL,
 "T0350" DECIMAL(10,4) NULL,
 "T0355" DECIMAL(10,4) NULL,
 "T0400" DECIMAL(10,4) NULL,
 "T0405" DECIMAL(10,4) NULL,
 "T0410" DECIMAL(10,4) NULL,
 "T0415" DECIMAL(10,4) NULL,
 "T0420" DECIMAL(10,4) NULL,
 "T0425" DECIMAL(10,4) NULL,
 "T0430" DECIMAL(10,4) NULL,
 "T0435" DECIMAL(10,4) NULL,
 "T0440" DECIMAL(10,4) NULL,
 "T0445" DECIMAL(10,4) NULL,
 "T0450" DECIMAL(10,4) NULL,
 "T0455" DECIMAL(10,4) NULL,
 "T0500" DECIMAL(10,4) NULL,
 "T0505" DECIMAL(10,4) NULL,
 "T0510" DECIMAL(10,4) NULL,
 "T0515" DECIMAL(10,4) NULL,
 "T0520" DECIMAL(10,4) NULL,
 "T0525" DECIMAL(10,4) NULL,
 "T0530" DECIMAL(10,4) NULL,
 "T0535" DECIMAL(10,4) NULL,
 "T0540" DECIMAL(10,4) NULL,
 "T0545" DECIMAL(10,4) NULL,
 "T0550" DECIMAL(10,4) NULL,
 "T0555" DECIMAL(10,4) NULL,
 "T0600" DECIMAL(10,4) NULL,
 "T0605" DECIMAL(10,4) NULL,
 "T0610" DECIMAL(10,4) NULL,
 "T0615" DECIMAL(10,4) NULL,
 "T0620" DECIMAL(10,4) NULL,
 "T0625" DECIMAL(10,4) NULL,
 "T0630" DECIMAL(10,4) NULL,
 "T0635" DECIMAL(10,4) NULL,
 "T0640" DECIMAL(10,4) NULL,
 "T0645" DECIMAL(10,4) NULL,
 "T0650" DECIMAL(10,4) NULL,
 "T0655" DECIMAL(10,4) NULL,
 "T0700" DECIMAL(10,4) NULL,
 "T0705" DECIMAL(10,4) NULL,
 "T0710" DECIMAL(10,4) NULL,
 "T0715" DECIMAL(10,4) NULL,
 "T0720" DECIMAL(10,4) NULL,
 "T0725" DECIMAL(10,4) NULL,
 "T0730" DECIMAL(10,4) NULL,
 "T0735" DECIMAL(10,4) NULL,
 "T0740" DECIMAL(10,4) NULL,
 "T0745" DECIMAL(10,4) NULL,
 "T0750" DECIMAL(10,4) NULL,
 "T0755" DECIMAL(10,4) NULL,
 "T0800" DECIMAL(10,4) NULL,
 "T0805" DECIMAL(10,4) NULL,
 "T0810" DECIMAL(10,4) NULL,
 "T0815" DECIMAL(10,4) NULL,
 "T0820" DECIMAL(10,4) NULL,
 "T0825" DECIMAL(10,4) NULL,
 "T0830" DECIMAL(10,4) NULL,
 "T0835" DECIMAL(10,4) NULL,
 "T0840" DECIMAL(10,4) NULL,
 "T0845" DECIMAL(10,4) NULL,
 "T0850" DECIMAL(10,4) NULL,
 "T0855" DECIMAL(10,4) NULL,
 "T0900" DECIMAL(10,4) NULL,
 "T0905" DECIMAL(10,4) NULL,
 "T0910" DECIMAL(10,4) NULL,
 "T0915" DECIMAL(10,4) NULL,
 "T0920" DECIMAL(10,4) NULL,
 "T0925" DECIMAL(10,4) NULL,
 "T0930" DECIMAL(10,4) NULL,
 "T0935" DECIMAL(10,4) NULL,
 "T0940" DECIMAL(10,4) NULL,
 "T0945" DECIMAL(10,4) NULL,
 "T0950" DECIMAL(10,4) NULL,
 "T0955" DECIMAL(10,4) NULL,
 "T1000" DECIMAL(10,4) NULL,
 "T1005" DECIMAL(10,4) NULL,
 "T1010" DECIMAL(10,4) NULL,
 "T1015" DECIMAL(10,4) NULL,
 "T1020" DECIMAL(10,4) NULL,
 "T1025" DECIMAL(10,4) NULL,
 "T1030" DECIMAL(10,4) NULL,
 "T1035" DECIMAL(10,4) NULL,
 "T1040" DECIMAL(10,4) NULL,
 "T1045" DECIMAL(10,4) NULL,
 "T1050" DECIMAL(10,4) NULL,
 "T1055" DECIMAL(10,4) NULL,
 "T1100" DECIMAL(10,4) NULL,
 "T1105" DECIMAL(10,4) NULL,
 "T1110" DECIMAL(10,4) NULL,
 "T1115" DECIMAL(10,4) NULL,
 "T1120" DECIMAL(10,4) NULL,
 "T1125" DECIMAL(10,4) NULL,
 "T1130" DECIMAL(10,4) NULL,
 "T1135" DECIMAL(10,4) NULL,
 "T1140" DECIMAL(10,4) NULL,
 "T1145" DECIMAL(10,4) NULL,
 "T1150" DECIMAL(10,4) NULL,
 "T1155" DECIMAL(10,4) NULL,
 "T1200" DECIMAL(10,4) NULL,
 "T1205" DECIMAL(10,4) NULL,
 "T1210" DECIMAL(10,4) NULL,
 "T1215" DECIMAL(10,4) NULL,
 "T1220" DECIMAL(10,4) NULL,
 "T1225" DECIMAL(10,4) NULL,
 "T1230" DECIMAL(10,4) NULL,
 "T1235" DECIMAL(10,4) NULL,
 "T1240" DECIMAL(10,4) NULL,
 "T1245" DECIMAL(10,4) NULL,
 "T1250" DECIMAL(10,4) NULL,
 "T1255" DECIMAL(10,4) NULL,
 "T1300" DECIMAL(10,4) NULL,
 "T1305" DECIMAL(10,4) NULL,
 "T1310" DECIMAL(10,4) NULL,
 "T1315" DECIMAL(10,4) NULL,
 "T1320" DECIMAL(10,4) NULL,
 "T1325" DECIMAL(10,4) NULL,
 "T1330" DECIMAL(10,4) NULL,
 "T1335" DECIMAL(10,4) NULL,
 "T1340" DECIMAL(10,4) NULL,
 "T1345" DECIMAL(10,4) NULL,
 "T1350" DECIMAL(10,4) NULL,
 "T1355" DECIMAL(10,4) NULL,
 "T1400" DECIMAL(10,4) NULL,
 "T1405" DECIMAL(10,4) NULL,
 "T1410" DECIMAL(10,4) NULL,
 "T1415" DECIMAL(10,4) NULL,
 "T1420" DECIMAL(10,4) NULL,
 "T1425" DECIMAL(10,4) NULL,
 "T1430" DECIMAL(10,4) NULL,
 "T1435" DECIMAL(10,4) NULL,
 "T1440" DECIMAL(10,4) NULL,
 "T1445" DECIMAL(10,4) NULL,
 "T1450" DECIMAL(10,4) NULL,
 "T1455" DECIMAL(10,4) NULL,
 "T1500" DECIMAL(10,4) NULL,
 "T1505" DECIMAL(10,4) NULL,
 "T1510" DECIMAL(10,4) NULL,
 "T1515" DECIMAL(10,4) NULL,
 "T1520" DECIMAL(10,4) NULL,
 "T1525" DECIMAL(10,4) NULL,
 "T1530" DECIMAL(10,4) NULL,
 "T1535" DECIMAL(10,4) NULL,
 "T1540" DECIMAL(10,4) NULL,
 "T1545" DECIMAL(10,4) NULL,
 "T1550" DECIMAL(10,4) NULL,
 "T1555" DECIMAL(10,4) NULL,
 "T1600" DECIMAL(10,4) NULL,
 "T1605" DECIMAL(10,4) NULL,
 "T1610" DECIMAL(10,4) NULL,
 "T1615" DECIMAL(10,4) NULL,
 "T1620" DECIMAL(10,4) NULL,
 "T1625" DECIMAL(10,4) NULL,
 "T1630" DECIMAL(10,4) NULL,
 "T1635" DECIMAL(10,4) NULL,
 "T1640" DECIMAL(10,4) NULL,
 "T1645" DECIMAL(10,4) NULL,
 "T1650" DECIMAL(10,4) NULL,
 "T1655" DECIMAL(10,4) NULL,
 "T1700" DECIMAL(10,4) NULL,
 "T1705" DECIMAL(10,4) NULL,
 "T1710" DECIMAL(10,4) NULL,
 "T1715" DECIMAL(10,4) NULL,
 "T1720" DECIMAL(10,4) NULL,
 "T1725" DECIMAL(10,4) NULL,
 "T1730" DECIMAL(10,4) NULL,
 "T1735" DECIMAL(10,4) NULL,
 "T1740" DECIMAL(10,4) NULL,
 "T1745" DECIMAL(10,4) NULL,
 "T1750" DECIMAL(10,4) NULL,
 "T1755" DECIMAL(10,4) NULL,
 "T1800" DECIMAL(10,4) NULL,
 "T1805" DECIMAL(10,4) NULL,
 "T1810" DECIMAL(10,4) NULL,
 "T1815" DECIMAL(10,4) NULL,
 "T1820" DECIMAL(10,4) NULL,
 "T1825" DECIMAL(10,4) NULL,
 "T1830" DECIMAL(10,4) NULL,
 "T1835" DECIMAL(10,4) NULL,
 "T1840" DECIMAL(10,4) NULL,
 "T1845" DECIMAL(10,4) NULL,
 "T1850" DECIMAL(10,4) NULL,
 "T1855" DECIMAL(10,4) NULL,
 "T1900" DECIMAL(10,4) NULL,
 "T1905" DECIMAL(10,4) NULL,
 "T1910" DECIMAL(10,4) NULL,
 "T1915" DECIMAL(10,4) NULL,
 "T1920" DECIMAL(10,4) NULL,
 "T1925" DECIMAL(10,4) NULL,
 "T1930" DECIMAL(10,4) NULL,
 "T1935" DECIMAL(10,4) NULL,
 "T1940" DECIMAL(10,4) NULL,
 "T1945" DECIMAL(10,4) NULL,
 "T1950" DECIMAL(10,4) NULL,
 "T1955" DECIMAL(10,4) NULL,
 "T2000" DECIMAL(10,4) NULL,
 "T2005" DECIMAL(10,4) NULL,
 "T2010" DECIMAL(10,4) NULL,
 "T2015" DECIMAL(10,4) NULL,
 "T2020" DECIMAL(10,4) NULL,
 "T2025" DECIMAL(10,4) NULL,
 "T2030" DECIMAL(10,4) NULL,
 "T2035" DECIMAL(10,4) NULL,
 "T2040" DECIMAL(10,4) NULL,
 "T2045" DECIMAL(10,4) NULL,
 "T2050" DECIMAL(10,4) NULL,
 "T2055" DECIMAL(10,4) NULL,
 "T2100" DECIMAL(10,4) NULL,
 "T2105" DECIMAL(10,4) NULL,
 "T2110" DECIMAL(10,4) NULL,
 "T2115" DECIMAL(10,4) NULL,
 "T2120" DECIMAL(10,4) NULL,
 "T2125" DECIMAL(10,4) NULL,
 "T2130" DECIMAL(10,4) NULL,
 "T2135" DECIMAL(10,4) NULL,
 "T2140" DECIMAL(10,4) NULL,
 "T2145" DECIMAL(10,4) NULL,
 "T2150" DECIMAL(10,4) NULL,
 "T2155" DECIMAL(10,4) NULL,
 "T2200" DECIMAL(10,4) NULL,
 "T2205" DECIMAL(10,4) NULL,
 "T2210" DECIMAL(10,4) NULL,
 "T2215" DECIMAL(10,4) NULL,
 "T2220" DECIMAL(10,4) NULL,
 "T2225" DECIMAL(10,4) NULL,
 "T2230" DECIMAL(10,4) NULL,
 "T2235" DECIMAL(10,4) NULL,
 "T2240" DECIMAL(10,4) NULL,
 "T2245" DECIMAL(10,4) NULL,
 "T2250" DECIMAL(10,4) NULL,
 "T2255" DECIMAL(10,4) NULL,
 "T2300" DECIMAL(10,4) NULL,
 "T2305" DECIMAL(10,4) NULL,
 "T2310" DECIMAL(10,4) NULL,
 "T2315" DECIMAL(10,4) NULL,
 "T2320" DECIMAL(10,4) NULL,
 "T2325" DECIMAL(10,4) NULL,
 "T2330" DECIMAL(10,4) NULL,
 "T2335" DECIMAL(10,4) NULL,
 "T2340" DECIMAL(10,4) NULL,
 "T2345" DECIMAL(10,4) NULL,
 "T2350" DECIMAL(10,4) NULL,
 "T2355" DECIMAL(10,4) NULL,
 "T2400" DECIMAL(10,4) NULL,
 "CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NULL,
 "UPDATETIME" TIMESTAMP(6) NULL,
 "REPORT" BIT NULL
);

CREATE TABLE "LOAD_ACCURACY_CITY_MONTH_FC_SERVICE"
(
 "ID" VARCHAR(32) NOT NULL,
 "YEAR" VARCHAR(4) NOT NULL,
 "MONTH" VARCHAR(2) NOT NULL,
 "CITY_ID" VARCHAR(32) NOT NULL,
 "CALIBER_ID" VARCHAR(32) NOT NULL,
 "ALGORITHM_ID" VARCHAR(32) NOT NULL,
 "MAX_ACCURACY" DEC(32,4) NULL,
 "MIN_ACCURACY" DEC(32,4) NULL,
 "AVG_ACCURACY" DEC(32,4) NULL,
 "CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NULL,
 "UPDATETIME" TIMESTAMP(6) NULL
);

CREATE TABLE "HOLIDAY_BASIC"
(
 "ID" VARCHAR(32) NOT NULL,
 "HOLIDAY" VARCHAR(20) NOT NULL,
 "DATE" DATE NOT NULL,
 "TYPE" VARCHAR(255) NULL,
 "START_DATE" DATE NOT NULL,
 "END_DATE" DATE NOT NULL,
 "PRE_EFFECT_DAYS" SMALLINT NOT NULL,
 "AFTER_EFFECT_DAYS" SMALLINT NOT NULL,
 "OFF_DATES" VARCHAR(200) NULL,
 "CODE" SMALLINT NULL,
 "YEAR" VARCHAR(5) NULL,
 "CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NULL,
 "UPDATETIME" TIMESTAMP(6) NULL
);

CREATE TABLE "FORECAST_INFO_BASIC"
(
 "ID" VARCHAR(32) NOT NULL,
 "CITY_ID" VARCHAR(32) NOT NULL,
 "CALIBER_ID" VARCHAR(32) NOT NULL,
 "DATE" DATE NOT NULL,
 "FORECAST_DAY" INTEGER NOT NULL,
 "IS_RECOMMEND" BIT NULL,
 "ALGORITHM_ID" VARCHAR(32) NULL,
 "COLLECT_STATUS_LOAD" BIT NULL,
 "COLLECT_STATUS_WEATHER" BIT NULL,
 "ERROR_DATA" INTEGER NULL,
 "REPAIR_DATA" INTEGER NULL,
 "DATE_TYPE" TINYINT NULL,
 "MAX_TEMPERATURE" DEC(32,4) NULL,
 "TEMPERATURE_STATUS" BIT NULL,
 "MAX_WIND" DEC(32,4) NULL,
 "RAINFALL" DEC(32,4) NULL,
 "IS_MANUAL" BIT NULL,
 "MANUAL_TIME" TIMESTAMP(6) NULL,
 "REPORT_TYPE" TINYINT NULL,
 "STATUS" BIT NULL,
 "REMARK" VARCHAR(255) NULL,
 "CREATETIME" TIMESTAMP(6) NULL,
 "UPDATETIME" TIMESTAMP(6) NULL
);

CREATE TABLE "DISPERSION_LOAD_CITY_FC_SERVICE"
(
 "ID" VARCHAR(32) NOT NULL,
 "DATE" DATE NOT NULL,
 "CITY_ID" VARCHAR(32) NOT NULL,
 "CALIBER_ID" VARCHAR(32) NOT NULL,
 "ALGORITHM_ID" VARCHAR(32) NOT NULL,
 "T0000" DEC(32,4) NULL,
 "T0015" DEC(32,4) NULL,
 "T0030" DEC(32,4) NULL,
 "T0045" DEC(32,4) NULL,
 "T0100" DEC(32,4) NULL,
 "T0115" DEC(32,4) NULL,
 "T0130" DEC(32,4) NULL,
 "T0145" DEC(32,4) NULL,
 "T0200" DEC(32,4) NULL,
 "T0215" DEC(32,4) NULL,
 "T0230" DEC(32,4) NULL,
 "T0245" DEC(32,4) NULL,
 "T0300" DEC(32,4) NULL,
 "T0315" DEC(32,4) NULL,
 "T0330" DEC(32,4) NULL,
 "T0345" DEC(32,4) NULL,
 "T0400" DEC(32,4) NULL,
 "T0415" DEC(32,4) NULL,
 "T0430" DEC(32,4) NULL,
 "T0445" DEC(32,4) NULL,
 "T0500" DEC(32,4) NULL,
 "T0515" DEC(32,4) NULL,
 "T0530" DEC(32,4) NULL,
 "T0545" DEC(32,4) NULL,
 "T0600" DEC(32,4) NULL,
 "T0615" DEC(32,4) NULL,
 "T0630" DEC(32,4) NULL,
 "T0645" DEC(32,4) NULL,
 "T0700" DEC(32,4) NULL,
 "T0715" DEC(32,4) NULL,
 "T0730" DEC(32,4) NULL,
 "T0745" DEC(32,4) NULL,
 "T0800" DEC(32,4) NULL,
 "T0815" DEC(32,4) NULL,
 "T0830" DEC(32,4) NULL,
 "T0845" DEC(32,4) NULL,
 "T0900" DEC(32,4) NULL,
 "T0915" DEC(32,4) NULL,
 "T0930" DEC(32,4) NULL,
 "T0945" DEC(32,4) NULL,
 "T1000" DEC(32,4) NULL,
 "T1015" DEC(32,4) NULL,
 "T1030" DEC(32,4) NULL,
 "T1045" DEC(32,4) NULL,
 "T1100" DEC(32,4) NULL,
 "T1115" DEC(32,4) NULL,
 "T1130" DEC(32,4) NULL,
 "T1145" DEC(32,4) NULL,
 "T1200" DEC(32,4) NULL,
 "T1215" DEC(32,4) NULL,
 "T1230" DEC(32,4) NULL,
 "T1245" DEC(32,4) NULL,
 "T1300" DEC(32,4) NULL,
 "T1315" DEC(32,4) NULL,
 "T1330" DEC(32,4) NULL,
 "T1345" DEC(32,4) NULL,
 "T1400" DEC(32,4) NULL,
 "T1415" DEC(32,4) NULL,
 "T1430" DEC(32,4) NULL,
 "T1445" DEC(32,4) NULL,
 "T1500" DEC(32,4) NULL,
 "T1515" DEC(32,4) NULL,
 "T1530" DEC(32,4) NULL,
 "T1545" DEC(32,4) NULL,
 "T1600" DEC(32,4) NULL,
 "T1615" DEC(32,4) NULL,
 "T1630" DEC(32,4) NULL,
 "T1645" DEC(32,4) NULL,
 "T1700" DEC(32,4) NULL,
 "T1715" DEC(32,4) NULL,
 "T1730" DEC(32,4) NULL,
 "T1745" DEC(32,4) NULL,
 "T1800" DEC(32,4) NULL,
 "T1815" DEC(32,4) NULL,
 "T1830" DEC(32,4) NULL,
 "T1845" DEC(32,4) NULL,
 "T1900" DEC(32,4) NULL,
 "T1915" DEC(32,4) NULL,
 "T1930" DEC(32,4) NULL,
 "T1945" DEC(32,4) NULL,
 "T2000" DEC(32,4) NULL,
 "T2015" DEC(32,4) NULL,
 "T2030" DEC(32,4) NULL,
 "T2045" DEC(32,4) NULL,
 "T2100" DEC(32,4) NULL,
 "T2115" DEC(32,4) NULL,
 "T2130" DEC(32,4) NULL,
 "T2145" DEC(32,4) NULL,
 "T2200" DEC(32,4) NULL,
 "T2215" DEC(32,4) NULL,
 "T2230" DEC(32,4) NULL,
 "T2245" DEC(32,4) NULL,
 "T2300" DEC(32,4) NULL,
 "T2315" DEC(32,4) NULL,
 "T2330" DEC(32,4) NULL,
 "T2345" DEC(32,4) NULL,
 "T2400" DEC(32,4) NULL,
 "REPORT" BIT DEFAULT 0
 NULL,
 "CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NULL,
 "UPDATETIME" TIMESTAMP(6) NULL
);

CREATE TABLE "DEVIATION_LOAD_CITY_FC_SERVICE"
(
 "ID" VARCHAR(32) NOT NULL,
 "DATE" DATE NOT NULL,
 "CITY_ID" VARCHAR(32) NOT NULL,
 "CALIBER_ID" VARCHAR(32) NOT NULL,
 "ALGORITHM_ID" VARCHAR(32) NOT NULL,
 "T0000" DEC(32,4) NULL,
 "T0015" DEC(32,4) NULL,
 "T0030" DEC(32,4) NULL,
 "T0045" DEC(32,4) NULL,
 "T0100" DEC(32,4) NULL,
 "T0115" DEC(32,4) NULL,
 "T0130" DEC(32,4) NULL,
 "T0145" DEC(32,4) NULL,
 "T0200" DEC(32,4) NULL,
 "T0215" DEC(32,4) NULL,
 "T0230" DEC(32,4) NULL,
 "T0245" DEC(32,4) NULL,
 "T0300" DEC(32,4) NULL,
 "T0315" DEC(32,4) NULL,
 "T0330" DEC(32,4) NULL,
 "T0345" DEC(32,4) NULL,
 "T0400" DEC(32,4) NULL,
 "T0415" DEC(32,4) NULL,
 "T0430" DEC(32,4) NULL,
 "T0445" DEC(32,4) NULL,
 "T0500" DEC(32,4) NULL,
 "T0515" DEC(32,4) NULL,
 "T0530" DEC(32,4) NULL,
 "T0545" DEC(32,4) NULL,
 "T0600" DEC(32,4) NULL,
 "T0615" DEC(32,4) NULL,
 "T0630" DEC(32,4) NULL,
 "T0645" DEC(32,4) NULL,
 "T0700" DEC(32,4) NULL,
 "T0715" DEC(32,4) NULL,
 "T0730" DEC(32,4) NULL,
 "T0745" DEC(32,4) NULL,
 "T0800" DEC(32,4) NULL,
 "T0815" DEC(32,4) NULL,
 "T0830" DEC(32,4) NULL,
 "T0845" DEC(32,4) NULL,
 "T0900" DEC(32,4) NULL,
 "T0915" DEC(32,4) NULL,
 "T0930" DEC(32,4) NULL,
 "T0945" DEC(32,4) NULL,
 "T1000" DEC(32,4) NULL,
 "T1015" DEC(32,4) NULL,
 "T1030" DEC(32,4) NULL,
 "T1045" DEC(32,4) NULL,
 "T1100" DEC(32,4) NULL,
 "T1115" DEC(32,4) NULL,
 "T1130" DEC(32,4) NULL,
 "T1145" DEC(32,4) NULL,
 "T1200" DEC(32,4) NULL,
 "T1215" DEC(32,4) NULL,
 "T1230" DEC(32,4) NULL,
 "T1245" DEC(32,4) NULL,
 "T1300" DEC(32,4) NULL,
 "T1315" DEC(32,4) NULL,
 "T1330" DEC(32,4) NULL,
 "T1345" DEC(32,4) NULL,
 "T1400" DEC(32,4) NULL,
 "T1415" DEC(32,4) NULL,
 "T1430" DEC(32,4) NULL,
 "T1445" DEC(32,4) NULL,
 "T1500" DEC(32,4) NULL,
 "T1515" DEC(32,4) NULL,
 "T1530" DEC(32,4) NULL,
 "T1545" DEC(32,4) NULL,
 "T1600" DEC(32,4) NULL,
 "T1615" DEC(32,4) NULL,
 "T1630" DEC(32,4) NULL,
 "T1645" DEC(32,4) NULL,
 "T1700" DEC(32,4) NULL,
 "T1715" DEC(32,4) NULL,
 "T1730" DEC(32,4) NULL,
 "T1745" DEC(32,4) NULL,
 "T1800" DEC(32,4) NULL,
 "T1815" DEC(32,4) NULL,
 "T1830" DEC(32,4) NULL,
 "T1845" DEC(32,4) NULL,
 "T1900" DEC(32,4) NULL,
 "T1915" DEC(32,4) NULL,
 "T1930" DEC(32,4) NULL,
 "T1945" DEC(32,4) NULL,
 "T2000" DEC(32,4) NULL,
 "T2015" DEC(32,4) NULL,
 "T2030" DEC(32,4) NULL,
 "T2045" DEC(32,4) NULL,
 "T2100" DEC(32,4) NULL,
 "T2115" DEC(32,4) NULL,
 "T2130" DEC(32,4) NULL,
 "T2145" DEC(32,4) NULL,
 "T2200" DEC(32,4) NULL,
 "T2215" DEC(32,4) NULL,
 "T2230" DEC(32,4) NULL,
 "T2245" DEC(32,4) NULL,
 "T2300" DEC(32,4) NULL,
 "T2315" DEC(32,4) NULL,
 "T2330" DEC(32,4) NULL,
 "T2345" DEC(32,4) NULL,
 "T2400" DEC(32,4) NULL,
 "REPORT" BIT DEFAULT 0
 NULL,
 "CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NULL,
 "UPDATETIME" TIMESTAMP(6) NULL
);

CREATE TABLE "DEVIATION_ANALYZE_CITY_DAY_FC_SERVICE"
(
 "ID" VARCHAR(32) NOT NULL,
 "CITY_ID" VARCHAR(32) NOT NULL,
 "CALIBER_ID" VARCHAR(10) NULL,
 "DATE" DATE NOT NULL,
 "ACCURACY" DEC(10,4) NULL,
 "PLAN_ID" VARCHAR(32) NULL,
 "ALGORITHM_ID" VARCHAR(32) NULL,
 "CORRECTION" BIT NULL,
 "DEVIATION_HUMAN" DEC(10,4) NULL,
 "DEVIATION_WEATHER" DEC(10,4) NULL,
 "DEVIATION_ALGORITHM" DEC(10,4) NULL,
 "DESCRIPTION" VARCHAR(256) NOT NULL,
 "CREATE_TIME" DATE NULL,
 "UPDATETIME" TIMESTAMP(6) NULL
);

CREATE TABLE "DATA_CHECK_INFO_BASIC"
(
 "ID" VARCHAR(32) NOT NULL,
 "CITY_ID" VARCHAR(32) NULL,
 "CALIBER_ID" VARCHAR(32) NOT NULL,
 "DATE" DATE NULL,
 "TABLE_NAME" VARCHAR(64) NULL,
 "FIELD_NAME" VARCHAR(32) NULL,
 "DATA_ID" VARCHAR(32) NULL,
 "ERROR_TYPE" TINYINT NULL,
 "ORIGINAL_VALUE" DEC(32,4) NULL,
 "REPAIR_VALUE" DEC(32,4) NULL,
 "IS_REPAIR" BIT NULL,
 "REPAIR_TYPE" TINYINT NULL,
 "CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NULL,
 "UPDATETIME" TIMESTAMP(6) NULL
);

CREATE TABLE "CITY_BASE_INIT"
(
 "ID" VARCHAR(32) NULL,
 "CITY" VARCHAR(32) NULL,
 "TYPE" INT NULL,
 "BELONG_ID" VARCHAR(32) NULL,
 "WEATHER_CITY_ID" VARCHAR(32) NULL,
 "ORDER_NO" INT NULL,
 "VALID" INT NULL,
 "CREATE_TIME" TIMESTAMP(6) NULL,
 "UPDATE_TIME" TIMESTAMP(6) NULL,
 "AREA" VARCHAR(32) NULL
);

CREATE TABLE "CALIBER_BASE_INIT"
(
 "ID" VARCHAR(32) NOT NULL,
 "NAME" VARCHAR(64) NOT NULL,
 "VALID" BIT DEFAULT 1
 NOT NULL,
 "DESCRIPTION" VARCHAR(128) NULL,
 "ORDER_NO" BIT NULL
);

CREATE TABLE "ALGORITHM_BASE_INIT"
(
 "ID" VARCHAR(32) NOT NULL,
 "ALGORITHM_EN" VARCHAR(32) NOT NULL,
 "ALGORITHM_CN" VARCHAR(32) NULL,
 "CODE" VARCHAR(255) NULL,
 "ORDER_NO" BIGINT NULL,
 "VALID" BIT NULL,
 "JOIN_MODEL" BIT NULL,
 "PROVINCE_VIEW" TINYINT NULL,
 "CITY_VIEW" TINYINT NULL,
 "TYPE" TINYINT NULL
);

CREATE TABLE "ACCURACY_LOAD_CITY_FC_SERVICE"
(
 "ID" VARCHAR(32) NOT NULL,
 "DATE" DATE NOT NULL,
 "CITY_ID" VARCHAR(32) NOT NULL,
 "CALIBER_ID" VARCHAR(32) NOT NULL,
 "ALGORITHM_ID" VARCHAR(32) NOT NULL,
 "T0000" DEC(10,4) NULL,
 "T0015" DEC(10,4) NULL,
 "T0030" DEC(10,4) NULL,
 "T0045" DEC(10,4) NULL,
 "T0100" DEC(10,4) NULL,
 "T0115" DEC(10,4) NULL,
 "T0130" DEC(10,4) NULL,
 "T0145" DEC(10,4) NULL,
 "T0200" DEC(10,4) NULL,
 "T0215" DEC(10,4) NULL,
 "T0230" DEC(10,4) NULL,
 "T0245" DEC(10,4) NULL,
 "T0300" DEC(10,4) NULL,
 "T0315" DEC(10,4) NULL,
 "T0330" DEC(10,4) NULL,
 "T0345" DEC(10,4) NULL,
 "T0400" DEC(10,4) NULL,
 "T0415" DEC(10,4) NULL,
 "T0430" DEC(10,4) NULL,
 "T0445" DEC(10,4) NULL,
 "T0500" DEC(10,4) NULL,
 "T0515" DEC(10,4) NULL,
 "T0530" DEC(10,4) NULL,
 "T0545" DEC(10,4) NULL,
 "T0600" DEC(10,4) NULL,
 "T0615" DEC(10,4) NULL,
 "T0630" DEC(10,4) NULL,
 "T0645" DEC(10,4) NULL,
 "T0700" DEC(10,4) NULL,
 "T0715" DEC(10,4) NULL,
 "T0730" DEC(10,4) NULL,
 "T0745" DEC(10,4) NULL,
 "T0800" DEC(10,4) NULL,
 "T0815" DEC(10,4) NULL,
 "T0830" DEC(10,4) NULL,
 "T0845" DEC(10,4) NULL,
 "T0900" DEC(10,4) NULL,
 "T0915" DEC(10,4) NULL,
 "T0930" DEC(10,4) NULL,
 "T0945" DEC(10,4) NULL,
 "T1000" DEC(10,4) NULL,
 "T1015" DEC(10,4) NULL,
 "T1030" DEC(10,4) NULL,
 "T1045" DEC(10,4) NULL,
 "T1100" DEC(10,4) NULL,
 "T1115" DEC(10,4) NULL,
 "T1130" DEC(10,4) NULL,
 "T1145" DEC(10,4) NULL,
 "T1200" DEC(10,4) NULL,
 "T1215" DEC(10,4) NULL,
 "T1230" DEC(10,4) NULL,
 "T1245" DEC(10,4) NULL,
 "T1300" DEC(10,4) NULL,
 "T1315" DEC(10,4) NULL,
 "T1330" DEC(10,4) NULL,
 "T1345" DEC(10,4) NULL,
 "T1400" DEC(10,4) NULL,
 "T1415" DEC(10,4) NULL,
 "T1430" DEC(10,4) NULL,
 "T1445" DEC(10,4) NULL,
 "T1500" DEC(10,4) NULL,
 "T1515" DEC(10,4) NULL,
 "T1530" DEC(10,4) NULL,
 "T1545" DEC(10,4) NULL,
 "T1600" DEC(10,4) NULL,
 "T1615" DEC(10,4) NULL,
 "T1630" DEC(10,4) NULL,
 "T1645" DEC(10,4) NULL,
 "T1700" DEC(10,4) NULL,
 "T1715" DEC(10,4) NULL,
 "T1730" DEC(10,4) NULL,
 "T1745" DEC(10,4) NULL,
 "T1800" DEC(10,4) NULL,
 "T1815" DEC(10,4) NULL,
 "T1830" DEC(10,4) NULL,
 "T1845" DEC(10,4) NULL,
 "T1900" DEC(10,4) NULL,
 "T1915" DEC(10,4) NULL,
 "T1930" DEC(10,4) NULL,
 "T1945" DEC(10,4) NULL,
 "T2000" DEC(10,4) NULL,
 "T2015" DEC(10,4) NULL,
 "T2030" DEC(10,4) NULL,
 "T2045" DEC(10,4) NULL,
 "T2100" DEC(10,4) NULL,
 "T2115" DEC(10,4) NULL,
 "T2130" DEC(10,4) NULL,
 "T2145" DEC(10,4) NULL,
 "T2200" DEC(10,4) NULL,
 "T2215" DEC(10,4) NULL,
 "T2230" DEC(10,4) NULL,
 "T2245" DEC(10,4) NULL,
 "T2300" DEC(10,4) NULL,
 "T2315" DEC(10,4) NULL,
 "T2330" DEC(10,4) NULL,
 "T2345" DEC(10,4) NULL,
 "T2400" DEC(10,4) NULL,
 "REPORT" BIT DEFAULT 0
 NULL,
 "CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP()
 NULL,
 "UPDATETIME" TIMESTAMP(6) NULL
);

ALTER TABLE "WEATHER_TYPHOON_RATE" ADD CONSTRAINT "INDEX33555618" PRIMARY KEY("ID") ;

ALTER TABLE "WEATHER_TYPHOON_PROVINCE_HIS_BASIC" ADD CONSTRAINT "INDEX33555616" PRIMARY KEY("ID") ;

ALTER TABLE "WEATHER_TYPHOON_HIS_CLCT" ADD CONSTRAINT "INDEX33555614" PRIMARY KEY("ID") ;

ALTER TABLE "WEATHER_TYPHOON_FC_CLCT" ADD CONSTRAINT "INDEX33555612" PRIMARY KEY("ID") ;

ALTER TABLE "WEATHER_TYPHOON_DEFINITION" ADD CONSTRAINT "INDEX33555610" PRIMARY KEY("ID") ;

ALTER TABLE "WEATHER_TYPHOON_ARCHIVE_CLCT" ADD CONSTRAINT "INDEX33555608" PRIMARY KEY("ID") ;

ALTER TABLE "WEATHER_STATION_HIS_BASIC" ADD CONSTRAINT "INDEX33555654" PRIMARY KEY("ID") ;

ALTER TABLE "WEATHER_STATION_FC_BASIC" ADD CONSTRAINT "INDEX33555656" PRIMARY KEY("ID") ;

ALTER TABLE "WEATHER_FEATURE_STATION_DAY_HIS_SERVICE" ADD CONSTRAINT "INDEX33555658" PRIMARY KEY("ID") ;

ALTER TABLE "WEATHER_FEATURE_STATION_DAY_FC_SERVICE" ADD CONSTRAINT "INDEX33555660" PRIMARY KEY("ID") ;

ALTER TABLE "WEATHER_FEATURE_CITY_QUARTER_HIS_SERVICE" ADD CONSTRAINT "INDEX33555602" PRIMARY KEY("ID") ;

ALTER TABLE "WEATHER_FEATURE_CITY_MONTH_HIS_SERVICE" ADD CONSTRAINT "INDEX33555600" PRIMARY KEY("ID") ;

ALTER TABLE "WEATHER_FEATURE_CITY_DAY_HIS_SERVICE" ADD CONSTRAINT "INDEX33555598" PRIMARY KEY("ID") ;

ALTER TABLE "WEATHER_FEATURE_CITY_DAY_FC_SERVICE" ADD CONSTRAINT "INDEX33555596" PRIMARY KEY("ID") ;

ALTER TABLE "WEATHER_CITY_HIS_BASIC" ADD CONSTRAINT "INDEX33555594" PRIMARY KEY("ID") ;

ALTER TABLE "WEATHER_CITY_FC_BASIC" ADD CONSTRAINT "INDEX33555592" PRIMARY KEY("ID") ;

ALTER TABLE "TSIE_USER_ROLE" ADD CONSTRAINT "INDEX33555586" PRIMARY KEY("ID") ;

ALTER TABLE "TSIE_USER_OAUTHBINDING" ADD CONSTRAINT "INDEX33555584" PRIMARY KEY("ID") ;

ALTER TABLE "TSIE_USER" ADD CONSTRAINT "INDEX33555582" PRIMARY KEY("ID") ;

ALTER TABLE "TSIE_UKEY" ADD CONSTRAINT "INDEX33555580" PRIMARY KEY("ID") ;

ALTER TABLE "TSIE_ROLE" ADD CONSTRAINT "INDEX33555578" PRIMARY KEY("ID") ;

ALTER TABLE "TSIE_RANDOMCODE_HISTORY" ADD CONSTRAINT "INDEX33555576" PRIMARY KEY("ID") ;

ALTER TABLE "TSIE_OPERATE_LOG" ADD CONSTRAINT "INDEX33555574" PRIMARY KEY("ID") ;

ALTER TABLE "TSIE_MESSAGE" ADD CONSTRAINT "INDEX33555571" PRIMARY KEY("ID") ;

ALTER TABLE "TSIE_MENU_ROLE" ADD CONSTRAINT "INDEX33555567" PRIMARY KEY("ID") ;

ALTER TABLE "TSIE_MENU" ADD CONSTRAINT "INDEX33555565" PRIMARY KEY("ID") ;

ALTER TABLE "TSIE_LOGIN_FAIL_COUNT_RECOND" ADD CONSTRAINT "INDEX33555563" PRIMARY KEY("ID") ;

ALTER TABLE "TSIE_GROUP_USER" ADD CONSTRAINT "INDEX33555559" PRIMARY KEY("ID") ;

ALTER TABLE "TSIE_GROUP_ROLE" ADD CONSTRAINT "INDEX33555556" PRIMARY KEY("ID") ;

ALTER TABLE "TSIE_GROUP" ADD CONSTRAINT "INDEX33555554" PRIMARY KEY("ID") ;

ALTER TABLE "TSIE_DB_PARAMETER" ADD CONSTRAINT "INDEX33555552" PRIMARY KEY("PARAM_ID") ;

ALTER TABLE "TSIE_COMMON_RANDOMCODE" ADD CONSTRAINT "INDEX33555550" PRIMARY KEY("ID") ;

ALTER TABLE "STATISTICS_WEATHER_CITY_DAY_FC_STAT" ADD CONSTRAINT "INDEX33555686" PRIMARY KEY("ID") ;

ALTER TABLE "STATISTICS_SYNTHESIZE_WEATHER_CITY_DAY_HIS" ADD CONSTRAINT "INDEX33555545" PRIMARY KEY("ID") ;

ALTER TABLE "STATISTICS_SYNTHESIZE_WEATHER_CITY_DAY_FC" ADD CONSTRAINT "INDEX33555543" PRIMARY KEY("ID") ;

ALTER TABLE "STATISTICS_CITY_DAY_FC_SERVICE" ADD CONSTRAINT "INDEX33555541" PRIMARY KEY("ID") ;

ALTER TABLE "STATISTICS_ACCURACY_WEATHER_CITY_YEAR_HIS" ADD CONSTRAINT "INDEX33555539" PRIMARY KEY("ID") ;

ALTER TABLE "STATISTICS_ACCURACY_WEATHER_CITY_MONTH_HIS" ADD CONSTRAINT "INDEX33555537" PRIMARY KEY("ID") ;

ALTER TABLE "STATISTICS_ACCURACY_WEATHER_CITY_DAY_HIS" ADD CONSTRAINT "INDEX33555535" PRIMARY KEY("ID") ;

ALTER TABLE "SIMILAR_DAY_BASIC" ADD CONSTRAINT "INDEX33555533" PRIMARY KEY("ID") ;

ALTER TABLE "SETTING_REPORT_INIT" ADD CONSTRAINT "INDEX33555531" PRIMARY KEY("ID") ;

ALTER TABLE "SETTING_CHECK_BASIC" ADD CONSTRAINT "INDEX33555525" PRIMARY KEY("ID") ;

ALTER TABLE "REPORT_LOAD_HIS_MONTH_ENERGY" ADD CONSTRAINT  PRIMARY KEY("ID") ;

ALTER TABLE "LOAD_FEATURE_CITY_WEEK_FC_SERVICE" ADD CONSTRAINT "CONS134219370" UNIQUE("DATE","CITY_ID","ALGORITHM_ID","CALIBER_ID") ;

ALTER TABLE "LOAD_FEATURE_CITY_MONTH_FC_SERVICE" ADD CONSTRAINT "CONS134219371" UNIQUE("YEAR","MONTH","CITY_ID","ALGORITHM_ID","CALIBER_ID") ;

ALTER TABLE "LOAD_CITY_HIS_CLCT_288" ADD CONSTRAINT "INDEX33555662" PRIMARY KEY("ID") ;

ALTER TABLE "LOAD_CITY_HIS_BASIC_288" ADD CONSTRAINT "INDEX33555664" PRIMARY KEY("ID") ;

ALTER TABLE "LOAD_CITY_FC_DFD" ADD CONSTRAINT "INDEX33555938" PRIMARY KEY("ID") ;

ALTER TABLE "LOAD_CITY_FC_BASIC" ADD CONSTRAINT "INDEX33555501" PRIMARY KEY("ID") ;

ALTER TABLE "LOAD_ACCURACY_CITY_MONTH_FC_SERVICE" ADD CONSTRAINT "LOAD_ACCURACY_CITY_MONTH_FC_UNIQUE" UNIQUE("YEAR","MONTH","CITY_ID","CALIBER_ID","ALGORITHM_ID") ;

CREATE INDEX "INDEX203705842620537"
ON "TSIE_USER_ROLE"("USERID");

CREATE INDEX "INDEX203705840666094"
ON "TSIE_USER_ROLE"("ROLEID");

CREATE INDEX "MENUID"
ON "TSIE_MENU_ROLE"("MENUID");

CREATE INDEX "INDEX203705837580212"
ON "TSIE_MENU_ROLE"("ROLEID");

CREATE INDEX "USERID"
ON "TSIE_GROUP_USER"("USERID");

CREATE INDEX "GROUPID"
ON "TSIE_GROUP_USER"("GROUPID");

CREATE INDEX "ROLEID"
ON "TSIE_GROUP_ROLE"("ROLEID");

CREATE INDEX "INDEX694117380464200"
ON "REPORT_LOAD_HIS_MONTH_ENERGY"("DATE","CITY_ID");

CREATE UNIQUE INDEX "INDEX694117387360000"
ON "REPORT_LOAD_HIS_MONTH_ENERGY"("ID");

COMMENT ON TABLE "REPORT_LOAD_HIS_MONTH_ENERGY" IS 'Ԥ����Ϣ��';

COMMENT ON COLUMN "REPORT_LOAD_HIS_MONTH_ENERGY"."ID" IS '����';

COMMENT ON COLUMN "REPORT_LOAD_HIS_MONTH_ENERGY"."CITY_ID" IS '����ID';

COMMENT ON COLUMN "REPORT_LOAD_HIS_MONTH_ENERGY"."DATE" IS '����';

COMMENT ON COLUMN "REPORT_LOAD_HIS_MONTH_ENERGY"."HIS_ENERGY" IS 'ʵ�ʵ���';

COMMENT ON COLUMN "REPORT_LOAD_HIS_MONTH_ENERGY"."REPORT_TIME" IS '�ύʱ��';

COMMENT ON COLUMN "REPORT_LOAD_HIS_MONTH_ENERGY"."CREATETIME" IS '����ʱ��';

COMMENT ON COLUMN "REPORT_LOAD_HIS_MONTH_ENERGY"."UPDATETIME" IS '����ʱ��';

CREATE UNIQUE INDEX "PRIMARY"
ON "LOAD_FEATURE_CITY_WEEK_FC_SERVICE"("ID");

COMMENT ON TABLE "LOAD_FEATURE_CITY_WEEK_FC_SERVICE" IS '�ܸ�������Ԥ���';

COMMENT ON COLUMN "LOAD_FEATURE_CITY_WEEK_FC_SERVICE"."ID" IS '����ҵ���id';

COMMENT ON COLUMN "LOAD_FEATURE_CITY_WEEK_FC_SERVICE"."DATE" IS '���ڣ�ÿ����һ������';

COMMENT ON COLUMN "LOAD_FEATURE_CITY_WEEK_FC_SERVICE"."CITY_ID" IS '����id';

COMMENT ON COLUMN "LOAD_FEATURE_CITY_WEEK_FC_SERVICE"."MAX_LOAD" IS '��󸺺�';

COMMENT ON COLUMN "LOAD_FEATURE_CITY_WEEK_FC_SERVICE"."MIN_LOAD" IS '��С����';

COMMENT ON COLUMN "LOAD_FEATURE_CITY_WEEK_FC_SERVICE"."AVE_LOAD" IS 'ƽ������';

COMMENT ON COLUMN "LOAD_FEATURE_CITY_WEEK_FC_SERVICE"."MAX_DATE" IS '��󸺺�����';

COMMENT ON COLUMN "LOAD_FEATURE_CITY_WEEK_FC_SERVICE"."MAX_TIME" IS '��󸺺ɷ���ʱ��';

COMMENT ON COLUMN "LOAD_FEATURE_CITY_WEEK_FC_SERVICE"."MIN_TIME" IS '��С���ɷ���ʱ��';

COMMENT ON COLUMN "LOAD_FEATURE_CITY_WEEK_FC_SERVICE"."PEAK" IS '���ƽ������';

COMMENT ON COLUMN "LOAD_FEATURE_CITY_WEEK_FC_SERVICE"."TROUGH" IS '�͹�ƽ������';

COMMENT ON COLUMN "LOAD_FEATURE_CITY_WEEK_FC_SERVICE"."DIFFERENT" IS '��Ȳ�';

COMMENT ON COLUMN "LOAD_FEATURE_CITY_WEEK_FC_SERVICE"."GRADIENT" IS '��Ȳ���';

COMMENT ON COLUMN "LOAD_FEATURE_CITY_WEEK_FC_SERVICE"."LOAD_GRADIENT" IS '������';

COMMENT ON COLUMN "LOAD_FEATURE_CITY_WEEK_FC_SERVICE"."ENERGY" IS '����';

CREATE UNIQUE INDEX "INDEX693757197161300"
ON "LOAD_FEATURE_CITY_MONTH_FC_SERVICE"("ID");

COMMENT ON TABLE "LOAD_FEATURE_CITY_MONTH_FC_SERVICE" IS '�¸�������Ԥ���';

COMMENT ON COLUMN "LOAD_FEATURE_CITY_MONTH_FC_SERVICE"."ID" IS '����ҵ���id';

COMMENT ON COLUMN "LOAD_FEATURE_CITY_MONTH_FC_SERVICE"."YEAR" IS '���';

COMMENT ON COLUMN "LOAD_FEATURE_CITY_MONTH_FC_SERVICE"."MONTH" IS '�·�';

COMMENT ON COLUMN "LOAD_FEATURE_CITY_MONTH_FC_SERVICE"."CITY_ID" IS '����id';

COMMENT ON COLUMN "LOAD_FEATURE_CITY_MONTH_FC_SERVICE"."MAX_LOAD" IS '��󸺺�';

COMMENT ON COLUMN "LOAD_FEATURE_CITY_MONTH_FC_SERVICE"."MIN_LOAD" IS '��С����';

COMMENT ON COLUMN "LOAD_FEATURE_CITY_MONTH_FC_SERVICE"."AVE_LOAD" IS 'ƽ������';

COMMENT ON COLUMN "LOAD_FEATURE_CITY_MONTH_FC_SERVICE"."MAX_DATE" IS '��󸺺���';

COMMENT ON COLUMN "LOAD_FEATURE_CITY_MONTH_FC_SERVICE"."MAX_TIME" IS '��󸺺ɷ���ʱ��';

COMMENT ON COLUMN "LOAD_FEATURE_CITY_MONTH_FC_SERVICE"."MIN_TIME" IS '��С���ɷ���ʱ��';

COMMENT ON COLUMN "LOAD_FEATURE_CITY_MONTH_FC_SERVICE"."PEAK" IS '���ƽ������';

COMMENT ON COLUMN "LOAD_FEATURE_CITY_MONTH_FC_SERVICE"."TROUGH" IS '�͹�ƽ������';

COMMENT ON COLUMN "LOAD_FEATURE_CITY_MONTH_FC_SERVICE"."DIFFERENT" IS '��Ȳ�';

COMMENT ON COLUMN "LOAD_FEATURE_CITY_MONTH_FC_SERVICE"."GRADIENT" IS '��Ȳ���';

COMMENT ON COLUMN "LOAD_FEATURE_CITY_MONTH_FC_SERVICE"."LOAD_GRADIENT" IS '������';

COMMENT ON COLUMN "LOAD_FEATURE_CITY_MONTH_FC_SERVICE"."DAY_UNBALANCE" IS '�ղ�����ϵ��';

COMMENT ON COLUMN "LOAD_FEATURE_CITY_MONTH_FC_SERVICE"."ENERGY" IS '����';

COMMENT ON COLUMN "LOAD_FEATURE_CITY_MONTH_FC_SERVICE"."UPPER_STABILITY" IS '�ȶ�������';

COMMENT ON COLUMN "LOAD_FEATURE_CITY_MONTH_FC_SERVICE"."LOWER_STABILITY" IS '�ȶ�������';

COMMENT ON COLUMN "LOAD_FEATURE_CITY_MONTH_FC_SERVICE"."CREATETIME" IS '����ʱ��';

CREATE INDEX "FORECAST_INFO_INDEX"
ON "FORECAST_INFO_BASIC"("DATE","CITY_ID","CALIBER_ID");

INSERT INTO "ALGORITHM_BASE_INIT"("ID","ALGORITHM_EN","ALGORITHM_CN","CODE","ORDER_NO","VALID","JOIN_MODEL","PROVINCE_VIEW","CITY_VIEW","TYPE") VALUES('10','NEW','������','109',11,1,1,1,0,1);
INSERT INTO "ALGORITHM_BASE_INIT"("ID","ALGORITHM_EN","ALGORITHM_CN","CODE","ORDER_NO","VALID","JOIN_MODEL","PROVINCE_VIEW","CITY_VIEW","TYPE") VALUES('11','TYPHOON','̨��Ԥ���㷨','110',12,1,0,0,0,3);
INSERT INTO "ALGORITHM_BASE_INIT"("ID","ALGORITHM_EN","ALGORITHM_CN","CODE","ORDER_NO","VALID","JOIN_MODEL","PROVINCE_VIEW","CITY_VIEW","TYPE") VALUES('3','MSSD','������','101',4,1,0,0,0,1);
INSERT INTO "ALGORITHM_BASE_INIT"("ID","ALGORITHM_EN","ALGORITHM_CN","CODE","ORDER_NO","VALID","JOIN_MODEL","PROVINCE_VIEW","CITY_VIEW","TYPE") VALUES('4','MSSD-DF','ƫ���','102',5,1,0,0,0,1);
INSERT INTO "ALGORITHM_BASE_INIT"("ID","ALGORITHM_EN","ALGORITHM_CN","CODE","ORDER_NO","VALID","JOIN_MODEL","PROVINCE_VIEW","CITY_VIEW","TYPE") VALUES('5','SVM','֧��������','103',6,1,0,1,1,1);
INSERT INTO "ALGORITHM_BASE_INIT"("ID","ALGORITHM_EN","ALGORITHM_CN","CODE","ORDER_NO","VALID","JOIN_MODEL","PROVINCE_VIEW","CITY_VIEW","TYPE") VALUES('6','XGBOOST','�ݶ�����','106',7,1,1,1,1,1);
INSERT INTO "ALGORITHM_BASE_INIT"("ID","ALGORITHM_EN","ALGORITHM_CN","CODE","ORDER_NO","VALID","JOIN_MODEL","PROVINCE_VIEW","CITY_VIEW","TYPE") VALUES('7','PREDICT','���','104',8,1,0,1,1,1);
INSERT INTO "ALGORITHM_BASE_INIT"("ID","ALGORITHM_EN","ALGORITHM_CN","CODE","ORDER_NO","VALID","JOIN_MODEL","PROVINCE_VIEW","CITY_VIEW","TYPE") VALUES('9','HOLIDAY','�ڼ����㷨','107',10,1,0,1,1,2);
INSERT INTO "ALGORITHM_BASE_INIT"("ID","ALGORITHM_EN","ALGORITHM_CN","CODE","ORDER_NO","VALID","JOIN_MODEL","PROVINCE_VIEW","CITY_VIEW","TYPE") VALUES('8','REPLENISH_LGB','������','105',13,1,0,1,0,1);
INSERT INTO "ALGORITHM_BASE_INIT"("ID","ALGORITHM_EN","ALGORITHM_CN","CODE","ORDER_NO","VALID","JOIN_MODEL","PROVINCE_VIEW","CITY_VIEW","TYPE") VALUES('16','COMPREHENSIVE','�ۺ�ģ��','800',14,1,0,1,0,1);
INSERT INTO "ALGORITHM_BASE_INIT"("ID","ALGORITHM_EN","ALGORITHM_CN","CODE","ORDER_NO","VALID","JOIN_MODEL","PROVINCE_VIEW","CITY_VIEW","TYPE") VALUES('12','SHORT','������','112',15,1,0,1,0,3);
INSERT INTO "ALGORITHM_BASE_INIT"("ID","ALGORITHM_EN","ALGORITHM_CN","CODE","ORDER_NO","VALID","JOIN_MODEL","PROVINCE_VIEW","CITY_VIEW","TYPE") VALUES('0','MD','�˹������ϱ����','100',1,1,0,1,1,0);
INSERT INTO "ALGORITHM_BASE_INIT"("ID","ALGORITHM_EN","ALGORITHM_CN","CODE","ORDER_NO","VALID","JOIN_MODEL","PROVINCE_VIEW","CITY_VIEW","TYPE") VALUES('99','OPS','OPS����','900',2,1,0,0,0,1);

INSERT INTO "CALIBER_BASE_INIT"("ID","NAME","VALID","DESCRIPTION","ORDER_NO") VALUES('51032001','����',1,'�����ھ�',1);

INSERT INTO "CITY_BASE_INIT"("ID","CITY","TYPE","BELONG_ID","WEATHER_CITY_ID","ORDER_NO","VALID","CREATE_TIME","UPDATE_TIME","AREA") VALUES('510100','�ɶ�',2,'1','510100',2,1,null,null,null);
INSERT INTO "CITY_BASE_INIT"("ID","CITY","TYPE","BELONG_ID","WEATHER_CITY_ID","ORDER_NO","VALID","CREATE_TIME","UPDATE_TIME","AREA") VALUES('510600','����',2,'1','510600',3,1,null,null,null);
INSERT INTO "CITY_BASE_INIT"("ID","CITY","TYPE","BELONG_ID","WEATHER_CITY_ID","ORDER_NO","VALID","CREATE_TIME","UPDATE_TIME","AREA") VALUES('510700','����',2,'1','510700',4,1,null,null,null);
INSERT INTO "CITY_BASE_INIT"("ID","CITY","TYPE","BELONG_ID","WEATHER_CITY_ID","ORDER_NO","VALID","CREATE_TIME","UPDATE_TIME","AREA") VALUES('510800','��Ԫ',2,'1','510800',5,1,null,null,null);
INSERT INTO "CITY_BASE_INIT"("ID","CITY","TYPE","BELONG_ID","WEATHER_CITY_ID","ORDER_NO","VALID","CREATE_TIME","UPDATE_TIME","AREA") VALUES('510400','��֦��',2,'1','510400',6,1,null,null,null);
INSERT INTO "CITY_BASE_INIT"("ID","CITY","TYPE","BELONG_ID","WEATHER_CITY_ID","ORDER_NO","VALID","CREATE_TIME","UPDATE_TIME","AREA") VALUES('511100','��ɽ',2,'1','511100',8,1,null,null,null);
INSERT INTO "CITY_BASE_INIT"("ID","CITY","TYPE","BELONG_ID","WEATHER_CITY_ID","ORDER_NO","VALID","CREATE_TIME","UPDATE_TIME","AREA") VALUES('511000','�ڽ�',2,'1','511000',9,1,null,null,null);
INSERT INTO "CITY_BASE_INIT"("ID","CITY","TYPE","BELONG_ID","WEATHER_CITY_ID","ORDER_NO","VALID","CREATE_TIME","UPDATE_TIME","AREA") VALUES('510300','�Թ�',2,'1','510300',10,1,null,null,null);
INSERT INTO "CITY_BASE_INIT"("ID","CITY","TYPE","BELONG_ID","WEATHER_CITY_ID","ORDER_NO","VALID","CREATE_TIME","UPDATE_TIME","AREA") VALUES('511500','�˱�',2,'1','511500',11,1,null,null,null);
INSERT INTO "CITY_BASE_INIT"("ID","CITY","TYPE","BELONG_ID","WEATHER_CITY_ID","ORDER_NO","VALID","CREATE_TIME","UPDATE_TIME","AREA") VALUES('510500','����',2,'1','510500',12,1,null,null,null);
INSERT INTO "CITY_BASE_INIT"("ID","CITY","TYPE","BELONG_ID","WEATHER_CITY_ID","ORDER_NO","VALID","CREATE_TIME","UPDATE_TIME","AREA") VALUES('511300','�ϳ�',2,'1','511300',13,1,null,null,null);
INSERT INTO "CITY_BASE_INIT"("ID","CITY","TYPE","BELONG_ID","WEATHER_CITY_ID","ORDER_NO","VALID","CREATE_TIME","UPDATE_TIME","AREA") VALUES('511700','����',2,'1','511700',14,1,null,null,null);
INSERT INTO "CITY_BASE_INIT"("ID","CITY","TYPE","BELONG_ID","WEATHER_CITY_ID","ORDER_NO","VALID","CREATE_TIME","UPDATE_TIME","AREA") VALUES('511900','����',2,'1','511900',15,1,null,null,null);
INSERT INTO "CITY_BASE_INIT"("ID","CITY","TYPE","BELONG_ID","WEATHER_CITY_ID","ORDER_NO","VALID","CREATE_TIME","UPDATE_TIME","AREA") VALUES('511600','�㰲',2,'1','511600',16,1,null,null,null);
INSERT INTO "CITY_BASE_INIT"("ID","CITY","TYPE","BELONG_ID","WEATHER_CITY_ID","ORDER_NO","VALID","CREATE_TIME","UPDATE_TIME","AREA") VALUES('511400','üɽ',2,'1','511400',17,1,null,null,null);
INSERT INTO "CITY_BASE_INIT"("ID","CITY","TYPE","BELONG_ID","WEATHER_CITY_ID","ORDER_NO","VALID","CREATE_TIME","UPDATE_TIME","AREA") VALUES('512000','����',2,'1','512000',18,1,null,null,null);
INSERT INTO "CITY_BASE_INIT"("ID","CITY","TYPE","BELONG_ID","WEATHER_CITY_ID","ORDER_NO","VALID","CREATE_TIME","UPDATE_TIME","AREA") VALUES('511800','�Ű�',2,'1','511800',21,1,null,null,null);
INSERT INTO "CITY_BASE_INIT"("ID","CITY","TYPE","BELONG_ID","WEATHER_CITY_ID","ORDER_NO","VALID","CREATE_TIME","UPDATE_TIME","AREA") VALUES('510900','����',2,'1','510900',22,1,null,null,null);
INSERT INTO "CITY_BASE_INIT"("ID","CITY","TYPE","BELONG_ID","WEATHER_CITY_ID","ORDER_NO","VALID","CREATE_TIME","UPDATE_TIME","AREA") VALUES('513229','�����',3,'1','513229',24,1,null,null,null);
INSERT INTO "CITY_BASE_INIT"("ID","CITY","TYPE","BELONG_ID","WEATHER_CITY_ID","ORDER_NO","VALID","CREATE_TIME","UPDATE_TIME","AREA") VALUES('513321','����',2,'1','513321',25,1,null,null,null);
INSERT INTO "CITY_BASE_INIT"("ID","CITY","TYPE","BELONG_ID","WEATHER_CITY_ID","ORDER_NO","VALID","CREATE_TIME","UPDATE_TIME","AREA") VALUES('513401','��ɽ',2,'1','513401',26,1,null,null,null);
INSERT INTO "CITY_BASE_INIT"("ID","CITY","TYPE","BELONG_ID","WEATHER_CITY_ID","ORDER_NO","VALID","CREATE_TIME","UPDATE_TIME","AREA") VALUES('1','�Ĵ�',1,null,'510100',1,1,null,null,null);

INSERT INTO "HOLIDAY_BASIC"("ID","HOLIDAY","DATE","TYPE","START_DATE","END_DATE","PRE_EFFECT_DAYS","AFTER_EFFECT_DAYS","OFF_DATES","CODE","YEAR","CREATETIME","UPDATETIME") VALUES('1','Ԫ��','2018-01-01',null,'2017-12-30','2018-01-01',0,0,null,101,'2018','2020-10-29 18:34:09.000000',null);
INSERT INTO "HOLIDAY_BASIC"("ID","HOLIDAY","DATE","TYPE","START_DATE","END_DATE","PRE_EFFECT_DAYS","AFTER_EFFECT_DAYS","OFF_DATES","CODE","YEAR","CREATETIME","UPDATETIME") VALUES('10','������','2017-04-04',null,'2017-04-02','2017-04-04',0,0,'2017-04-01',103,'2017','2020-10-29 18:34:09.000000',null);
INSERT INTO "HOLIDAY_BASIC"("ID","HOLIDAY","DATE","TYPE","START_DATE","END_DATE","PRE_EFFECT_DAYS","AFTER_EFFECT_DAYS","OFF_DATES","CODE","YEAR","CREATETIME","UPDATETIME") VALUES('11','�Ͷ���','2017-05-01',null,'2017-04-29','2017-05-01',0,0,null,104,'2017','2020-10-29 18:34:09.000000',null);
INSERT INTO "HOLIDAY_BASIC"("ID","HOLIDAY","DATE","TYPE","START_DATE","END_DATE","PRE_EFFECT_DAYS","AFTER_EFFECT_DAYS","OFF_DATES","CODE","YEAR","CREATETIME","UPDATETIME") VALUES('12','�����','2017-05-30',null,'2017-05-28','2017-05-30',0,0,'2017-05-27',105,'2017','2020-10-29 18:34:09.000000',null);
INSERT INTO "HOLIDAY_BASIC"("ID","HOLIDAY","DATE","TYPE","START_DATE","END_DATE","PRE_EFFECT_DAYS","AFTER_EFFECT_DAYS","OFF_DATES","CODE","YEAR","CREATETIME","UPDATETIME") VALUES('13','�����','2017-10-04',null,'2017-10-01','2017-10-08',0,0,'2017-09-30',106,'2017','2020-10-29 18:34:09.000000',null);
INSERT INTO "HOLIDAY_BASIC"("ID","HOLIDAY","DATE","TYPE","START_DATE","END_DATE","PRE_EFFECT_DAYS","AFTER_EFFECT_DAYS","OFF_DATES","CODE","YEAR","CREATETIME","UPDATETIME") VALUES('14','�����','2017-10-01',null,'2017-10-01','2017-10-08',0,0,'2017-09-30',107,'2017','2020-10-29 18:34:09.000000',null);
INSERT INTO "HOLIDAY_BASIC"("ID","HOLIDAY","DATE","TYPE","START_DATE","END_DATE","PRE_EFFECT_DAYS","AFTER_EFFECT_DAYS","OFF_DATES","CODE","YEAR","CREATETIME","UPDATETIME") VALUES('15','Ԫ��','2016-01-01',null,'2016-01-01','2016-01-03',0,0,null,101,'2016','2020-10-29 18:34:09.000000',null);
INSERT INTO "HOLIDAY_BASIC"("ID","HOLIDAY","DATE","TYPE","START_DATE","END_DATE","PRE_EFFECT_DAYS","AFTER_EFFECT_DAYS","OFF_DATES","CODE","YEAR","CREATETIME","UPDATETIME") VALUES('16','����','2016-02-08',null,'2016-02-07','2016-02-13',0,0,'2016-02-06,2016-02-14',102,'2016','2020-10-29 18:34:09.000000',null);
INSERT INTO "HOLIDAY_BASIC"("ID","HOLIDAY","DATE","TYPE","START_DATE","END_DATE","PRE_EFFECT_DAYS","AFTER_EFFECT_DAYS","OFF_DATES","CODE","YEAR","CREATETIME","UPDATETIME") VALUES('17','������','2016-04-04',null,'2016-04-02','2016-04-04',0,0,null,103,'2016','2020-10-29 18:34:09.000000',null);
INSERT INTO "HOLIDAY_BASIC"("ID","HOLIDAY","DATE","TYPE","START_DATE","END_DATE","PRE_EFFECT_DAYS","AFTER_EFFECT_DAYS","OFF_DATES","CODE","YEAR","CREATETIME","UPDATETIME") VALUES('18','�Ͷ���','2016-05-01',null,'2016-04-30','2016-05-02',0,0,null,104,'2016','2020-10-29 18:34:09.000000',null);
INSERT INTO "HOLIDAY_BASIC"("ID","HOLIDAY","DATE","TYPE","START_DATE","END_DATE","PRE_EFFECT_DAYS","AFTER_EFFECT_DAYS","OFF_DATES","CODE","YEAR","CREATETIME","UPDATETIME") VALUES('19','�����','2016-06-09',null,'2016-06-09','2016-06-11',0,0,'2016-06-12',105,'2016','2020-10-29 18:34:09.000000',null);
INSERT INTO "HOLIDAY_BASIC"("ID","HOLIDAY","DATE","TYPE","START_DATE","END_DATE","PRE_EFFECT_DAYS","AFTER_EFFECT_DAYS","OFF_DATES","CODE","YEAR","CREATETIME","UPDATETIME") VALUES('1a','Ԫ��','2019-01-01',null,'2018-12-30','2019-01-01',0,0,null,101,'2019','2020-10-29 18:34:09.000000',null);
INSERT INTO "HOLIDAY_BASIC"("ID","HOLIDAY","DATE","TYPE","START_DATE","END_DATE","PRE_EFFECT_DAYS","AFTER_EFFECT_DAYS","OFF_DATES","CODE","YEAR","CREATETIME","UPDATETIME") VALUES('1b','Ԫ��','2020-01-01',null,'2020-01-01','2020-01-01',0,0,null,101,'2020','2020-10-29 18:34:09.000000',null);
INSERT INTO "HOLIDAY_BASIC"("ID","HOLIDAY","DATE","TYPE","START_DATE","END_DATE","PRE_EFFECT_DAYS","AFTER_EFFECT_DAYS","OFF_DATES","CODE","YEAR","CREATETIME","UPDATETIME") VALUES('2','����','2018-02-16',null,'2018-02-15','2018-02-21',0,0,'2018-02-24',102,'2018','2020-10-29 18:34:09.000000',null);
INSERT INTO "HOLIDAY_BASIC"("ID","HOLIDAY","DATE","TYPE","START_DATE","END_DATE","PRE_EFFECT_DAYS","AFTER_EFFECT_DAYS","OFF_DATES","CODE","YEAR","CREATETIME","UPDATETIME") VALUES('20','�����','2016-09-15',null,'2016-09-15','2016-09-17',0,0,'2016-09-18',106,'2016','2020-10-29 18:34:09.000000',null);
INSERT INTO "HOLIDAY_BASIC"("ID","HOLIDAY","DATE","TYPE","START_DATE","END_DATE","PRE_EFFECT_DAYS","AFTER_EFFECT_DAYS","OFF_DATES","CODE","YEAR","CREATETIME","UPDATETIME") VALUES('21','�����','2016-10-01',null,'2016-10-01','2016-10-07',0,0,'2016-10-08,2018-10-09',107,'2016','2020-10-29 18:34:09.000000',null);
INSERT INTO "HOLIDAY_BASIC"("ID","HOLIDAY","DATE","TYPE","START_DATE","END_DATE","PRE_EFFECT_DAYS","AFTER_EFFECT_DAYS","OFF_DATES","CODE","YEAR","CREATETIME","UPDATETIME") VALUES('23','����','2015-02-19',null,'2015-02-18','2015-02-24',0,0,'2015-02-28',102,'2015','2020-10-29 18:34:09.000000',null);
INSERT INTO "HOLIDAY_BASIC"("ID","HOLIDAY","DATE","TYPE","START_DATE","END_DATE","PRE_EFFECT_DAYS","AFTER_EFFECT_DAYS","OFF_DATES","CODE","YEAR","CREATETIME","UPDATETIME") VALUES('24','������','2015-04-05',null,'2015-04-04','2015-04-06',0,0,null,103,'2015','2020-10-29 18:34:09.000000',null);
INSERT INTO "HOLIDAY_BASIC"("ID","HOLIDAY","DATE","TYPE","START_DATE","END_DATE","PRE_EFFECT_DAYS","AFTER_EFFECT_DAYS","OFF_DATES","CODE","YEAR","CREATETIME","UPDATETIME") VALUES('25','�Ͷ���','2015-05-01',null,'2015-05-01','2015-05-03',0,0,null,104,'2015','2020-10-29 18:34:09.000000',null);
INSERT INTO "HOLIDAY_BASIC"("ID","HOLIDAY","DATE","TYPE","START_DATE","END_DATE","PRE_EFFECT_DAYS","AFTER_EFFECT_DAYS","OFF_DATES","CODE","YEAR","CREATETIME","UPDATETIME") VALUES('26','�����','2015-06-20',null,'2015-06-20','2015-06-22',0,0,null,105,'2015','2020-10-29 18:34:09.000000',null);
INSERT INTO "HOLIDAY_BASIC"("ID","HOLIDAY","DATE","TYPE","START_DATE","END_DATE","PRE_EFFECT_DAYS","AFTER_EFFECT_DAYS","OFF_DATES","CODE","YEAR","CREATETIME","UPDATETIME") VALUES('27','�����','2015-09-26',null,'2015-09-26','2015-09-27',0,0,null,106,'2015','2020-10-29 18:34:09.000000',null);
INSERT INTO "HOLIDAY_BASIC"("ID","HOLIDAY","DATE","TYPE","START_DATE","END_DATE","PRE_EFFECT_DAYS","AFTER_EFFECT_DAYS","OFF_DATES","CODE","YEAR","CREATETIME","UPDATETIME") VALUES('28','�����','2015-10-01',null,'2015-10-01','2015-10-07',0,0,'2015-10-10',107,'2015','2020-10-29 18:34:09.000000',null);
INSERT INTO "HOLIDAY_BASIC"("ID","HOLIDAY","DATE","TYPE","START_DATE","END_DATE","PRE_EFFECT_DAYS","AFTER_EFFECT_DAYS","OFF_DATES","CODE","YEAR","CREATETIME","UPDATETIME") VALUES('29','�����','2014-10-01',null,'2014-10-01','2014-10-07',0,0,'2014-10-10',107,'2014','2020-10-29 18:34:09.000000',null);
INSERT INTO "HOLIDAY_BASIC"("ID","HOLIDAY","DATE","TYPE","START_DATE","END_DATE","PRE_EFFECT_DAYS","AFTER_EFFECT_DAYS","OFF_DATES","CODE","YEAR","CREATETIME","UPDATETIME") VALUES('2a','����','2019-02-05',null,'2019-02-04','2019-02-10',0,0,'2019-02-24',102,'2019','2020-10-29 18:34:09.000000',null);
INSERT INTO "HOLIDAY_BASIC"("ID","HOLIDAY","DATE","TYPE","START_DATE","END_DATE","PRE_EFFECT_DAYS","AFTER_EFFECT_DAYS","OFF_DATES","CODE","YEAR","CREATETIME","UPDATETIME") VALUES('2b','����','2020-01-25',null,'2020-01-24','2020-01-30',0,0,'2020-01-19,2020-02-01',102,'2020','2020-10-29 18:34:09.000000',null);
INSERT INTO "HOLIDAY_BASIC"("ID","HOLIDAY","DATE","TYPE","START_DATE","END_DATE","PRE_EFFECT_DAYS","AFTER_EFFECT_DAYS","OFF_DATES","CODE","YEAR","CREATETIME","UPDATETIME") VALUES('3','������','2018-04-05',null,'2018-04-05','2018-04-07',0,0,'2018-04-08',103,'2018','2020-10-29 18:34:09.000000',null);
INSERT INTO "HOLIDAY_BASIC"("ID","HOLIDAY","DATE","TYPE","START_DATE","END_DATE","PRE_EFFECT_DAYS","AFTER_EFFECT_DAYS","OFF_DATES","CODE","YEAR","CREATETIME","UPDATETIME") VALUES('3a','������','2019-04-05',null,'2019-04-05','2019-04-07',0,0,'2019-04-08',103,'2019','2020-10-29 18:34:09.000000',null);
INSERT INTO "HOLIDAY_BASIC"("ID","HOLIDAY","DATE","TYPE","START_DATE","END_DATE","PRE_EFFECT_DAYS","AFTER_EFFECT_DAYS","OFF_DATES","CODE","YEAR","CREATETIME","UPDATETIME") VALUES('3b','������','2020-04-04',null,'2020-04-04','2020-04-06',0,0,'2020-04-08',103,'2020','2020-10-29 18:34:09.000000',null);
INSERT INTO "HOLIDAY_BASIC"("ID","HOLIDAY","DATE","TYPE","START_DATE","END_DATE","PRE_EFFECT_DAYS","AFTER_EFFECT_DAYS","OFF_DATES","CODE","YEAR","CREATETIME","UPDATETIME") VALUES('4','�Ͷ���','2018-05-01',null,'2018-04-29','2018-05-01',0,0,'2018-04-28',104,'2018','2020-10-29 18:34:09.000000',null);
INSERT INTO "HOLIDAY_BASIC"("ID","HOLIDAY","DATE","TYPE","START_DATE","END_DATE","PRE_EFFECT_DAYS","AFTER_EFFECT_DAYS","OFF_DATES","CODE","YEAR","CREATETIME","UPDATETIME") VALUES('4a','�Ͷ���','2019-05-01',null,'2019-05-01','2019-05-04',0,0,'2019-04-28',104,'2019','2020-10-29 18:34:09.000000',null);
INSERT INTO "HOLIDAY_BASIC"("ID","HOLIDAY","DATE","TYPE","START_DATE","END_DATE","PRE_EFFECT_DAYS","AFTER_EFFECT_DAYS","OFF_DATES","CODE","YEAR","CREATETIME","UPDATETIME") VALUES('4b','�Ͷ���','2020-05-01',null,'2020-05-01','2020-05-05',0,0,'2020-04-26,2020-05-09',104,'2020','2020-10-29 18:34:09.000000',null);
INSERT INTO "HOLIDAY_BASIC"("ID","HOLIDAY","DATE","TYPE","START_DATE","END_DATE","PRE_EFFECT_DAYS","AFTER_EFFECT_DAYS","OFF_DATES","CODE","YEAR","CREATETIME","UPDATETIME") VALUES('5','�����','2018-06-18',null,'2018-06-16','2018-06-18',0,0,null,105,'2018','2020-10-29 18:34:09.000000',null);
INSERT INTO "HOLIDAY_BASIC"("ID","HOLIDAY","DATE","TYPE","START_DATE","END_DATE","PRE_EFFECT_DAYS","AFTER_EFFECT_DAYS","OFF_DATES","CODE","YEAR","CREATETIME","UPDATETIME") VALUES('5a','�����','2019-06-07',null,'2019-06-07','2019-06-09',0,0,null,105,'2019','2020-10-29 18:34:09.000000',null);
INSERT INTO "HOLIDAY_BASIC"("ID","HOLIDAY","DATE","TYPE","START_DATE","END_DATE","PRE_EFFECT_DAYS","AFTER_EFFECT_DAYS","OFF_DATES","CODE","YEAR","CREATETIME","UPDATETIME") VALUES('5b','�����','2020-06-25',null,'2020-06-25','2020-06-27',0,0,'2020-06-28',105,'2020','2020-10-29 18:34:09.000000',null);
INSERT INTO "HOLIDAY_BASIC"("ID","HOLIDAY","DATE","TYPE","START_DATE","END_DATE","PRE_EFFECT_DAYS","AFTER_EFFECT_DAYS","OFF_DATES","CODE","YEAR","CREATETIME","UPDATETIME") VALUES('6','�����','2018-09-24',null,'2018-09-22','2018-09-24',0,0,null,106,'2018','2020-10-29 18:34:09.000000',null);
INSERT INTO "HOLIDAY_BASIC"("ID","HOLIDAY","DATE","TYPE","START_DATE","END_DATE","PRE_EFFECT_DAYS","AFTER_EFFECT_DAYS","OFF_DATES","CODE","YEAR","CREATETIME","UPDATETIME") VALUES('6a','�����','2019-09-13',null,'2019-09-13','2019-09-15',0,0,null,106,'2019','2020-10-29 18:34:09.000000',null);
INSERT INTO "HOLIDAY_BASIC"("ID","HOLIDAY","DATE","TYPE","START_DATE","END_DATE","PRE_EFFECT_DAYS","AFTER_EFFECT_DAYS","OFF_DATES","CODE","YEAR","CREATETIME","UPDATETIME") VALUES('7','�����','2018-10-01',null,'2018-10-01','2018-10-07',0,0,'2018-09-29,2018-09-30',107,'2018','2020-10-29 18:34:09.000000',null);
INSERT INTO "HOLIDAY_BASIC"("ID","HOLIDAY","DATE","TYPE","START_DATE","END_DATE","PRE_EFFECT_DAYS","AFTER_EFFECT_DAYS","OFF_DATES","CODE","YEAR","CREATETIME","UPDATETIME") VALUES('7a','�����','2019-10-01',null,'2019-10-01','2019-10-07',0,0,'2019-09-29,2019-09-30',107,'2019','2020-10-29 18:34:09.000000',null);
INSERT INTO "HOLIDAY_BASIC"("ID","HOLIDAY","DATE","TYPE","START_DATE","END_DATE","PRE_EFFECT_DAYS","AFTER_EFFECT_DAYS","OFF_DATES","CODE","YEAR","CREATETIME","UPDATETIME") VALUES('8','Ԫ��','2017-01-01',null,'2016-12-31','2017-01-02',0,0,null,101,'2017','2020-10-29 18:34:09.000000',null);
INSERT INTO "HOLIDAY_BASIC"("ID","HOLIDAY","DATE","TYPE","START_DATE","END_DATE","PRE_EFFECT_DAYS","AFTER_EFFECT_DAYS","OFF_DATES","CODE","YEAR","CREATETIME","UPDATETIME") VALUES('9','����','2017-01-28',null,'2017-01-27','2017-02-02',0,0,'2017-01-22,2017-02-04',102,'2017','2020-10-29 18:34:09.000000',null);
INSERT INTO "HOLIDAY_BASIC"("ID","HOLIDAY","DATE","TYPE","START_DATE","END_DATE","PRE_EFFECT_DAYS","AFTER_EFFECT_DAYS","OFF_DATES","CODE","YEAR","CREATETIME","UPDATETIME") VALUES('6b','�����','2020-10-01',null,'2020-10-01','2020-10-08',0,0,'2020-09-27,2020-10-10',106,'2020','2020-10-29 18:34:09.000000',null);
INSERT INTO "HOLIDAY_BASIC"("ID","HOLIDAY","DATE","TYPE","START_DATE","END_DATE","PRE_EFFECT_DAYS","AFTER_EFFECT_DAYS","OFF_DATES","CODE","YEAR","CREATETIME","UPDATETIME") VALUES('7b','�����','2020-10-01',null,'2020-10-01','2020-10-08',0,0,'2020-09-27,2020-10-10',107,'2020','2020-10-29 18:34:09.000000',null);
INSERT INTO "HOLIDAY_BASIC"("ID","HOLIDAY","DATE","TYPE","START_DATE","END_DATE","PRE_EFFECT_DAYS","AFTER_EFFECT_DAYS","OFF_DATES","CODE","YEAR","CREATETIME","UPDATETIME") VALUES('1c','Ԫ��','2021-01-01','1','2021-01-01','2021-01-03',0,0,null,101,'2021',null,'2021-09-14 19:21:02.321000');
INSERT INTO "HOLIDAY_BASIC"("ID","HOLIDAY","DATE","TYPE","START_DATE","END_DATE","PRE_EFFECT_DAYS","AFTER_EFFECT_DAYS","OFF_DATES","CODE","YEAR","CREATETIME","UPDATETIME") VALUES('2c','����','2021-02-12',null,'2021-02-11','2021-02-17',0,0,'2021-02-07,2021-02-20',102,'2021','2020-12-07 00:00:00.000000',null);
INSERT INTO "HOLIDAY_BASIC"("ID","HOLIDAY","DATE","TYPE","START_DATE","END_DATE","PRE_EFFECT_DAYS","AFTER_EFFECT_DAYS","OFF_DATES","CODE","YEAR","CREATETIME","UPDATETIME") VALUES('3c','������','2021-04-04',null,'2021-04-03','2021-04-05',0,0,null,103,'2021','2020-12-07 00:00:00.000000',null);
INSERT INTO "HOLIDAY_BASIC"("ID","HOLIDAY","DATE","TYPE","START_DATE","END_DATE","PRE_EFFECT_DAYS","AFTER_EFFECT_DAYS","OFF_DATES","CODE","YEAR","CREATETIME","UPDATETIME") VALUES('4c','�Ͷ���','2021-05-01',null,'2021-05-01','2021-05-05',0,0,'2021-04-25,2021-05-08',104,'2021','2020-12-07 00:00:00.000000',null);
INSERT INTO "HOLIDAY_BASIC"("ID","HOLIDAY","DATE","TYPE","START_DATE","END_DATE","PRE_EFFECT_DAYS","AFTER_EFFECT_DAYS","OFF_DATES","CODE","YEAR","CREATETIME","UPDATETIME") VALUES('5c','�����','2021-06-14',null,'2021-06-12','2021-06-14',0,0,null,105,'2021','2020-12-07 00:00:00.000000',null);
INSERT INTO "HOLIDAY_BASIC"("ID","HOLIDAY","DATE","TYPE","START_DATE","END_DATE","PRE_EFFECT_DAYS","AFTER_EFFECT_DAYS","OFF_DATES","CODE","YEAR","CREATETIME","UPDATETIME") VALUES('6c','�����','2021-09-21',null,'2021-09-19','2021-09-21',0,0,'2021-09-18',106,'2021','2020-12-07 00:00:00.000000',null);
INSERT INTO "HOLIDAY_BASIC"("ID","HOLIDAY","DATE","TYPE","START_DATE","END_DATE","PRE_EFFECT_DAYS","AFTER_EFFECT_DAYS","OFF_DATES","CODE","YEAR","CREATETIME","UPDATETIME") VALUES('7c','�����','2021-10-01',null,'2021-10-01','2021-10-07',0,0,'2021-09-26,2021-10-09',107,'2021','2020-12-07 00:00:00.000000',null);
INSERT INTO "HOLIDAY_BASIC"("ID","HOLIDAY","DATE","TYPE","START_DATE","END_DATE","PRE_EFFECT_DAYS","AFTER_EFFECT_DAYS","OFF_DATES","CODE","YEAR","CREATETIME","UPDATETIME") VALUES('ff8080817be35f24017be39f22ec0027','�����','2020-10-01','1','2020-10-01','2020-10-08',0,0,'2020-09-27,2020-10-10',107,'2022','2021-09-14 17:24:22.381000','2021-09-14 17:24:22.381000');



INSERT INTO "LOAD_USER_DETAIL"("TSIE_UID","CITY_ID","CREATETIME","LAST_UPDATE_PASSWORD_TIME") VALUES('402879816e83525a016e8357d49f0000','512000','2019-11-19 19:07:40.000000','2021-11-01 09:30:18.976000');
INSERT INTO "LOAD_USER_DETAIL"("TSIE_UID","CITY_ID","CREATETIME","LAST_UPDATE_PASSWORD_TIME") VALUES('ff8080817bdc2c88017bde35f2eb0023','1','2021-09-13 16:11:22.818000','2021-09-13 16:11:22.818000');

INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1101','1','2019',98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1102','2','2019',98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1103','3','2019',97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1104','4','2019',97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1105','5','2019',97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1106','6','2019',97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1107','7','2019',97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1108','8','2019',97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1109','9','2019',97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1110','10','2019',97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1111','11','2019',98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1112','12','2019',97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1113','13','2019',97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1114','14','2019',97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1115','15','2019',97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1116','16','2019',97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1117','17','2019',97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1118','18','2019',97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1119','19','2019',97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1120','20','2019',97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1121','21','2019',97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1122','1','2020',97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,'2020-01-10 14:34:32.000000','2020-04-24 09:56:51.000000');
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1123','2','2020',98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1124','3','2020',97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1125','4','2020',97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1126','5','2020',97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1127','6','2020',97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1128','7','2020',97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1129','8','2020',97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1130','9','2020',97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1131','10','2020',97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1132','11','2020',97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1133','12','2020',97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1134','13','2020',97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1135','14','2020',97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1136','15','2020',97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1137','16','2020',97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1138','17','2020',97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1139','18','2020',97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1140','19','2020',97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1141','20','2020',97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1142','21','2020',97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1143','1','2021',98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,'2020-01-10 14:34:32.000000','2021-09-06 17:38:19.000000');
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1144','2','2021',98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,'2020-01-10 14:34:32.000000','2021-09-06 17:38:19.000000');
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1145','3','2021',97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,'2020-01-10 14:34:32.000000','2021-09-06 17:38:19.000000');
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1146','4','2021',97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,'2020-01-10 14:34:32.000000','2021-09-06 17:38:19.000000');
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1147','5','2021',97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,'2020-01-10 14:34:32.000000','2021-09-06 17:38:19.000000');
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1148','6','2021',97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,'2020-01-10 14:34:32.000000','2021-09-06 17:38:19.000000');
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1149','7','2021',97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,'2020-01-10 14:34:32.000000','2021-09-06 17:38:19.000000');
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1150','8','2021',97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,'2020-01-10 14:34:32.000000','2021-09-06 17:38:19.000000');
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1151','9','2021',97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,'2020-01-10 14:34:32.000000','2021-09-06 17:38:19.000000');
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1152','10','2021',97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,'2020-01-10 14:34:32.000000','2021-09-06 17:38:19.000000');
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1153','11','2021',98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,'2020-01-10 14:34:32.000000','2021-09-06 17:38:19.000000');
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1154','12','2021',97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,'2020-01-10 14:34:32.000000','2021-09-06 17:38:19.000000');
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1155','13','2021',97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,'2020-01-10 14:34:32.000000','2021-09-06 17:38:19.000000');
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1156','14','2021',97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,'2020-01-10 14:34:32.000000','2021-09-06 17:38:19.000000');
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1157','15','2021',97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,'2020-01-10 14:34:32.000000','2021-09-06 17:38:19.000000');
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1158','16','2021',97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,'2020-01-10 14:34:32.000000','2021-09-06 17:38:19.000000');
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1159','17','2021',97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,'2020-01-10 14:34:32.000000','2021-09-06 17:38:19.000000');
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1160','18','2021',97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,'2020-01-10 14:34:32.000000','2021-09-06 17:38:19.000000');
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1161','19','2021',97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,'2020-01-10 14:34:32.000000','2021-09-06 17:38:19.000000');
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1162','20','2021',97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,'2020-01-10 14:34:32.000000','2021-09-06 17:38:19.000000');
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1163','21','2021',97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,'2020-01-10 14:34:32.000000','2021-09-06 17:38:19.000000');
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1164','1','2022',98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1165','2','2022',98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1166','3','2022',97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1167','4','2022',97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1168','5','2022',97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1169','6','2022',97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1170','7','2022',97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1171','8','2022',97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1172','9','2022',97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1173','10','2022',97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1174','11','2022',98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,98.0000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1175','12','2022',97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1176','13','2022',97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1177','14','2022',97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1178','15','2022',97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1179','16','2022',97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1180','17','2022',97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1181','18','2022',97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1182','19','2022',97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,97.3000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1183','20','2022',97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,'2020-01-10 14:34:32.000000',null);
INSERT INTO "SETTING_CHECK_PRECISION"("ID","CITY_ID","YEAR","JANUARY","FEBRUARY","MARCH","APRIL","MAY","JUNE","JULY","AUGUST","SEPTEMBER","OCTOBER","NOVEMBER","DECEMBER","CREATETIME","UPDATETIME") VALUES('1184','21','2022',97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,97.5000,'2020-01-10 14:34:32.000000',null);


INSERT INTO "SETTING_REPORT_INIT"("ID","CITY_ID","STANDARD_ACCURACY","REPORT_TIME") VALUES('1','1',0.9700,'10:00');
INSERT INTO "SETTING_REPORT_INIT"("ID","CITY_ID","STANDARD_ACCURACY","REPORT_TIME") VALUES('2','2',0.9700,'9:00');
INSERT INTO "SETTING_REPORT_INIT"("ID","CITY_ID","STANDARD_ACCURACY","REPORT_TIME") VALUES('3','3',0.9700,'9:00');
INSERT INTO "SETTING_REPORT_INIT"("ID","CITY_ID","STANDARD_ACCURACY","REPORT_TIME") VALUES('4','4',0.9700,'9:00');
INSERT INTO "SETTING_REPORT_INIT"("ID","CITY_ID","STANDARD_ACCURACY","REPORT_TIME") VALUES('5','5',0.9700,'9:00');
INSERT INTO "SETTING_REPORT_INIT"("ID","CITY_ID","STANDARD_ACCURACY","REPORT_TIME") VALUES('6','6',0.9700,'9:00');
INSERT INTO "SETTING_REPORT_INIT"("ID","CITY_ID","STANDARD_ACCURACY","REPORT_TIME") VALUES('7','7',0.9700,'9:00');
INSERT INTO "SETTING_REPORT_INIT"("ID","CITY_ID","STANDARD_ACCURACY","REPORT_TIME") VALUES('8','8',0.9700,'9:00');
INSERT INTO "SETTING_REPORT_INIT"("ID","CITY_ID","STANDARD_ACCURACY","REPORT_TIME") VALUES('9','9',0.9700,'9:00');
INSERT INTO "SETTING_REPORT_INIT"("ID","CITY_ID","STANDARD_ACCURACY","REPORT_TIME") VALUES('10','10',0.9700,'9:00');
INSERT INTO "SETTING_REPORT_INIT"("ID","CITY_ID","STANDARD_ACCURACY","REPORT_TIME") VALUES('11','11',0.9700,'9:00');
INSERT INTO "SETTING_REPORT_INIT"("ID","CITY_ID","STANDARD_ACCURACY","REPORT_TIME") VALUES('12','12',0.9700,'9:00');
INSERT INTO "SETTING_REPORT_INIT"("ID","CITY_ID","STANDARD_ACCURACY","REPORT_TIME") VALUES('13','13',0.9700,'9:00');
INSERT INTO "SETTING_REPORT_INIT"("ID","CITY_ID","STANDARD_ACCURACY","REPORT_TIME") VALUES('14','14',0.9700,'9:00');
INSERT INTO "SETTING_REPORT_INIT"("ID","CITY_ID","STANDARD_ACCURACY","REPORT_TIME") VALUES('15','15',0.9700,'9:00');
INSERT INTO "SETTING_REPORT_INIT"("ID","CITY_ID","STANDARD_ACCURACY","REPORT_TIME") VALUES('16','16',0.9700,'9:00');
INSERT INTO "SETTING_REPORT_INIT"("ID","CITY_ID","STANDARD_ACCURACY","REPORT_TIME") VALUES('17','17',0.9700,'9:00');
INSERT INTO "SETTING_REPORT_INIT"("ID","CITY_ID","STANDARD_ACCURACY","REPORT_TIME") VALUES('18','18',0.9700,'9:00');
INSERT INTO "SETTING_REPORT_INIT"("ID","CITY_ID","STANDARD_ACCURACY","REPORT_TIME") VALUES('19','19',0.9700,'9:00');
INSERT INTO "SETTING_REPORT_INIT"("ID","CITY_ID","STANDARD_ACCURACY","REPORT_TIME") VALUES('20','20',0.9700,'9:00');
INSERT INTO "SETTING_REPORT_INIT"("ID","CITY_ID","STANDARD_ACCURACY","REPORT_TIME") VALUES('21','21',0.9700,'9:00');
INSERT INTO "SETTING_REPORT_INIT"("ID","CITY_ID","STANDARD_ACCURACY","REPORT_TIME") VALUES('22','22',0.9700,'9:00');

INSERT INTO "SETTING_SYSTEM_INIT"("ID","FIELD","VALUE","NAME","DESCRIPTION") VALUES('1','test','test','test',null);
INSERT INTO "SETTING_SYSTEM_INIT"("ID","FIELD","VALUE","NAME","DESCRIPTION") VALUES('10','step_max','0.3','��Ծֵ��ֵ',null);
INSERT INTO "SETTING_SYSTEM_INIT"("ID","FIELD","VALUE","NAME","DESCRIPTION") VALUES('2','standard_accuracy','0.97','���˱�׼Ĭ��׼ȷ��',null);
INSERT INTO "SETTING_SYSTEM_INIT"("ID","FIELD","VALUE","NAME","DESCRIPTION") VALUES('3','report_time','10:00','Ĭ���ϱ�ʱ��',null);
INSERT INTO "SETTING_SYSTEM_INIT"("ID","FIELD","VALUE","NAME","DESCRIPTION") VALUES('4','peak_time','05:30~07:00,10:00~12:00,18:00~20:00','尖峰时段',null);
INSERT INTO "SETTING_SYSTEM_INIT"("ID","FIELD","VALUE","NAME","DESCRIPTION") VALUES('5','trough_time','02:00~05:00,12:00~14:00,22:00~23:45','ƽ��ʱ��',null);
INSERT INTO "SETTING_SYSTEM_INIT"("ID","FIELD","VALUE","NAME","DESCRIPTION") VALUES('6','accuracy_max','0.97','׼ȷ�ʸ���ֵ',null);
INSERT INTO "SETTING_SYSTEM_INIT"("ID","FIELD","VALUE","NAME","DESCRIPTION") VALUES('7','accuracy_min','0.95','׼ȷ�ʵ���ֵ',null);
INSERT INTO "SETTING_SYSTEM_INIT"("ID","FIELD","VALUE","NAME","DESCRIPTION") VALUES('8','similarity_max','0.95','���ƶȸ���ֵ',null);
INSERT INTO "SETTING_SYSTEM_INIT"("ID","FIELD","VALUE","NAME","DESCRIPTION") VALUES('9','similarity_min','0.9','���ƶȵ���ֵ',null);
INSERT INTO "SETTING_SYSTEM_INIT"("ID","FIELD","VALUE","NAME","DESCRIPTION") VALUES('14','humidity','1,0.4000','ʪ��ƫ��ֵ',null);
INSERT INTO "SETTING_SYSTEM_INIT"("ID","FIELD","VALUE","NAME","DESCRIPTION") VALUES('15','temperature','1,0.4000','�¶�ƫ��ֵ',null);
INSERT INTO "SETTING_SYSTEM_INIT"("ID","FIELD","VALUE","NAME","DESCRIPTION") VALUES('16','rainfall','1,0.4000','������ƫ��ֵ',null);
INSERT INTO "SETTING_SYSTEM_INIT"("ID","FIELD","VALUE","NAME","DESCRIPTION") VALUES('17','windspeed','1,0.4000','����ƫ��ֵ',null);
INSERT INTO "SETTING_SYSTEM_INIT"("ID","FIELD","VALUE","NAME","DESCRIPTION") VALUES('18','scale_comprehensive_model','{"10":"0.4","6":"0.3","5":"0.2","7":"0.1"}','�ۺ�ģ���㷨����',null);
INSERT INTO "SETTING_SYSTEM_INIT"("ID","FIELD","VALUE","NAME","DESCRIPTION") VALUES('19','forecast_day','7','Ԥ������',null);
INSERT INTO "SETTING_SYSTEM_INIT"("ID","FIELD","VALUE","NAME","DESCRIPTION") VALUES('20','normal_algorithm','16,6','�������Ƽ��㷨',null);
INSERT INTO "SETTING_SYSTEM_INIT"("ID","FIELD","VALUE","NAME","DESCRIPTION") VALUES('21','holiday_algorithm','9,9','�ڼ����Ƽ��㷨',null);
INSERT INTO "SETTING_SYSTEM_INIT"("ID","FIELD","VALUE","NAME","DESCRIPTION") VALUES('22','auto_report','1,1','�Զ��ϱ�',null);
INSERT INTO "SETTING_SYSTEM_INIT"("ID","FIELD","VALUE","NAME","DESCRIPTION") VALUES('23','end_report_time','0,13:00','�����ϱ���ֹʱ��',null);
INSERT INTO "SETTING_SYSTEM_INIT"("ID","FIELD","VALUE","NAME","DESCRIPTION") VALUES('24','province_short_5','1,4','ȫ��5���ӳ�����Ԥ��',null);
INSERT INTO "SETTING_SYSTEM_INIT"("ID","FIELD","VALUE","NAME","DESCRIPTION") VALUES('25','province_short_15','1,4','ȫ��15���ӳ�����Ԥ��',null);
INSERT INTO "SETTING_SYSTEM_INIT"("ID","FIELD","VALUE","NAME","DESCRIPTION") VALUES('26','city_short_5','0,4','����15���ӳ�����Ԥ��',null);
INSERT INTO "SETTING_SYSTEM_INIT"("ID","FIELD","VALUE","NAME","DESCRIPTION") VALUES('27','city_short_15','0,4','����15���ӳ�����Ԥ��',null);
INSERT INTO "SETTING_SYSTEM_INIT"("ID","FIELD","VALUE","NAME","DESCRIPTION") VALUES('30','early_start_holiday_days','7','�ڼ�����ǰ��������','�ڼ�����ǰ��������');
INSERT INTO "SETTING_SYSTEM_INIT"("ID","FIELD","VALUE","NAME","DESCRIPTION") VALUES('31','target_accuracy','0.97','Ŀ��׼ȷ��',null);
INSERT INTO "SETTING_SYSTEM_INIT"("ID","FIELD","VALUE","NAME","DESCRIPTION") VALUES('32', 'humidity', '1,0.4000', '湿度偏差值', '第一个参数为开关，第二个参数为偏差值');
INSERT INTO "SETTING_SYSTEM_INIT"("ID","FIELD","VALUE","NAME","DESCRIPTION") VALUES('33', 'temperature', '1,0.4000', '温度偏差值', '第一个参数为开关，第二个参数为偏差值');
INSERT INTO "SETTING_SYSTEM_INIT"("ID","FIELD","VALUE","NAME","DESCRIPTION") VALUES('34', 'rainfall', '1,0.4000', '降雨量偏差值', '第一个参数为开关，第二个参数为偏差值');
INSERT INTO "SETTING_SYSTEM_INIT"("ID","FIELD","VALUE","NAME","DESCRIPTION") VALUES('35','windspeed', '1,0.4000', '风速偏差值', '第一个参数为开关，第二个参数为偏差值');
INSERT INTO "SETTING_SYSTEM_INIT"("ID","FIELD","VALUE","NAME","DESCRIPTION") VALUES('36','peak_section_time','08:00~22:00','峰段电量时段',null);
INSERT INTO "SETTING_SYSTEM_INIT"("ID","FIELD","VALUE","NAME","DESCRIPTION") VALUES('37','province_end_report_time','0,11:00','全网上报截止时间和开关','全网上报截止时间和开关');
INSERT INTO "SETTING_SYSTEM_INIT"("ID","FIELD","VALUE","NAME","DESCRIPTION") VALUES('38','login_info','清能互联电网,智能短期,广东','登录页名称','登录页名称');





INSERT INTO "TSIE_GROUP"("ID","GROUPNAME","DESCRIPTION") VALUES('402879816e83525a016e8357d5000005','��������Ա','��������Ա');
INSERT INTO "TSIE_GROUP"("ID","GROUPNAME","DESCRIPTION") VALUES('402879816e83525a016e8357d51f0008','��ͨ����Ա','��ͨ����Ա');

INSERT INTO "TSIE_GROUP_ROLE"("ID","GROUPID","ROLEID") VALUES('402879816e83525a016e8357d5100007','402879816e83525a016e8357d5000005','402879816e83525a016e8357d4bc0001');

INSERT INTO "TSIE_GROUP_USER"("ID","USERID","GROUPID") VALUES('402879816e83525a016e8357d5080006','402879816e83525a016e8357d49f0000','402879816e83525a016e8357d5000005');


INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d5270009','��ҳ',1,'GuangdongIndex','root','��ҳ','navTab','��ҳ',2);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d53c000b','���ɷ���',1,'LoadAnalysis','root','���ɷ���','navTab','���ɷ���',9999);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d55f000d','���Է���',1,'CharacteristicAnalysis','402879816e83525a016e8357d53c000b','���Է���','navTab','���Է���',9999);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d569000f','����Է���',1,'CorrelateAnalysis','402879816e83525a016e8357d53c000b','����Է���','navTab','����Է���',9999);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d5760011','�ȶ��ȷ���',1,'StabilityAnalysis','402879816e83525a016e8357d53c000b','�ȶ��ȷ���','navTab','�ȶ��ȷ���',9999);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d5820013','�����ȷ���',1,'SensitivityAnalysis','402879816e83525a016e8357d53c000b','�����ȷ���','navTab','�����ȷ���',9999);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d58d0015','����Ԥ��',1,'LoadForecast','root','����Ԥ��','navTab','����Ԥ��',1);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d5980017','�����ϱ�',1,'RevisedReport','402879816e83525a016e8357d58d0015','�����ϱ�','navTab','�����ϱ�',2);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d59e0019','�ֶ�Ԥ��',1,'AutoForecast','402879816e83525a016e8357d58d0015','�ֶ�Ԥ��','navTab','�ֶ�Ԥ��',4);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d5a9001b','�ڼ���Ԥ��',1,'HolidayForecast','root','�ڼ���Ԥ��','navTab','�ڼ���Ԥ��',9999);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d5b2001d','�ڼ������Է���',1,'HolidayAnalysis','402879816e83525a016e8357d5a9001b','�ڼ������Է���','navTab','�ڼ������Է���',9999);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d5bc001f','�ڼ��������ϱ�',1,'HolidayCorrect','402879816e83525a016e8357d5a9001b','�ڼ��������ϱ�','navTab','�ڼ��������ϱ�',9999);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d5c30021','��������Ԥ��',1,'ExtremeWeatherForecast','root','��������Ԥ��','navTab','��������Ԥ��',9999);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d5cc0023','̨����Ϣ����',1,'ExtremeWeatherTyphoonInfo','402879816e83525a016e8357d5c30021','̨����Ϣ����','navTab','̨����Ϣ����',9999);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d5d60025','̨��Ӱ�����',1,'ExtremeWeatherTyphoon','402879816e83525a016e8357d5c30021','̨��Ӱ�����','navTab','̨��Ӱ�����',9999);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d5e30027','̨����Ԥ��',1,'ExtremeWeatherTyphoonManualPrediction','402879816e83525a016e8357d5c30021','̨����Ԥ��','navTab','̨����Ԥ��',9999);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d5ed0029','Ԥ�������',1,'PredictionEvaluation','root','Ԥ�������','navTab','Ԥ�������',9999);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d5f8002b','Ԥ���ѯ',1,'PredictionQuery','402879816e83525a016e8357d5ed0029','Ԥ���ѯ','navTab','Ԥ���ѯ',9999);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d602002d','����Ԥ�������',1,'LoadForecastEvaluation','402879816e83525a016e8357d5ed0029','����Ԥ�������','navTab','����Ԥ�������',9999);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d60a002f','����Ԥ�������',1,'WeatherPredictionEvaluation','402879816e83525a016e8357d5ed0029','����Ԥ�������','navTab','����Ԥ�������',9999);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d6140031','����Ԥ��׼ȷ��',1,'WeatherPredictionAccuracy','402879816e83525a016e8357d5ed0029','����Ԥ��׼ȷ��','navTab','����Ԥ��׼ȷ��',9999);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d61d0033','������',1,'Analysis','402879816e83525a016e8357d5ed0029','������','navTab','������',9999);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d6280035','���˹���',1,'CheckManagement','root','���˹���','navTab','���˹���',9999);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d6320037','��������',0,'CheckSetting','402879816e83525a016e8357d6280035','��������','navTab','��������',9999);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d63d0039','����ͳ��',1,'CheckStatistics','402879816e83525a016e8357d6280035','����ͳ��','navTab','����ͳ��',9999);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d644003b','�⿼����',1,'CheckApply','402879816e83525a016e8357d6280035','�⿼����','navTab','�⿼����',9999);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d64d003d','�⿼����',1,'CheckApproval','402879816e83525a016e8357d6280035','�⿼����','navTab','�⿼����',9999);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d657003f','���ݹ���',1,'DataManagement','root','���ݹ���','navTab','���ݹ���',9999);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d6610041','ϵͳ����',1,'SystemSetting','root','ϵͳ����','navTab','ϵͳ����',9999);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d6680043','Ȩ�޹���',1,'AuthManage','402879816e83525a016e8357d6610041','Ȩ�޹���','navTab','Ȩ�޹���',9999);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d6720045','ϵͳ����',1,'SystemSettingPage','402879816e83525a016e8357d6610041','ϵͳ����','navTab','ϵͳ����',9999);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d67b0047','ʵʩҳ��',1,'Implementation','root','ʵʩҳ��','navTab','ʵʩҳ��',9999);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d6870049','����Ԥ��Ա�',2,'/web/analyze/loadForecastCompare','402879816e83525a016e8357d657003f','����Ԥ��Ա�','navTab','����Ԥ��Ա�',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d692004b','��ȡ�������ʷ��Ԥ������',2,'/web/analyze/weather-his-fc','402879816e83525a016e8357d5ed0029','��ȡ�������ʷ��Ԥ������','navTab','��ȡ�������ʷ��Ԥ������',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d699004d','ͳ���������',2,'/web/analyze/human-weather-algorithm','402879816e83525a016e8357d657003f','ͳ���������','navTab','ͳ���������',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d6a4004f','��������¼',2,'/web/analyze/deviationAnalyze','402879816e83525a016e8357d657003f','��������¼','navTab','��������¼',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d6ae0051','׼ȷ���ٲ�',2,'/web/analyze/accuracy','402879816e83525a016e8357d657003f','׼ȷ���ٲ�','navTab','׼ȷ���ٲ�',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d6b80053','���ͳ��',2,'/web/analyze/accuracy-pass','402879816e83525a016e8357d657003f','���ͳ��','navTab','���ͳ��',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d6c40055','���˹���',2,'/web/check/*','402879816e83525a016e8357d6280035','���˹���','navTab','���˹���',10000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d6cc0057','ƫ���',2,'/web/check/deviation','402879816e83525a016e8357d5270009','ƫ���','navTab','ƫ���',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d6d50059','����׼ȷ����',2,'/web/check/accuracy/city','402879816e83525a016e8357d5270009','����׼ȷ����','navTab','����׼ȷ����',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d6de005b','��������',2,'/web/check/todo','402879816e83525a016e8357d5270009','��������','navTab','��������',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d6e9005d','�����⿼',2,'/web/settingCheck/add','402879816e83525a016e8357d644003b','�����⿼','navTab','�����⿼',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d6f4005f','�ҵ���ʷ����',2,'/web/settingCheck/list/history','402879816e83525a016e8357d64d003d','�ҵ���ʷ����','navTab','�ҵ���ʷ����',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d6ff0061','������������',2,'/web/settingCheck/detail','402879816e83525a016e8357d64d003d','������������','navTab','������������',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d7060063','������������',2,'/web/settingCheck/detail','402879816e83525a016e8357d644003b','������������','navTab','������������',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d7100065','�ҵ���ʷ����',2,'/web/settingCheck/list/history/mine','402879816e83525a016e8357d644003b','�ҵ���ʷ����','navTab','�ҵ���ʷ����',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d71a0067','�ҵ�����',2,'/web/settingCheck/list/todo/mine','402879816e83525a016e8357d644003b','�ҵ�����','navTab','�ҵ�����',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d7260069','��������',2,'/web/settingCheck/list/todo','402879816e83525a016e8357d64d003d','��������','navTab','��������',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d730006b','������������',2,'/web/settingCheck/doCheck','402879816e83525a016e8357d64d003d','������������','navTab','������������',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d737006d','Ԥ�������',2,'/web/evalucation/*','402879816e83525a016e8357d5ed0029','Ԥ�������','navTab','Ԥ�������',10000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d742006f','ƽ��׼ȷ�ʺͺϸ��ʡ���ɢ��',2,'/web/evalucation/accuracy-pass-discrete','402879816e83525a016e8357d5270009','ƽ��׼ȷ�ʺͺϸ��ʡ���ɢ��','navTab','ƽ��׼ȷ�ʺͺϸ��ʡ���ɢ��',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d74c0071','ģ�͹���',2,'/web/algorithm/*','402879816e83525a016e8357d6720045','ģ�͹���','navTab','ģ�͹���',10000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d7570073','�ֶ�Ԥ��',2,'/web/forecast/start','402879816e83525a016e8357d5270009','�ֶ�Ԥ��','navTab','�ֶ�Ԥ��',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d7640075','��������',2,'/web/forecast/auto/recorrect','402879816e83525a016e8357d5980017','��������','navTab','��������',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d76f0077','����ƽ��',2,'/web/forecast/auto/*','402879816e83525a016e8357d5980017','����ƽ��','navTab','����ƽ��',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d7770079','Ԥ��ſ�',2,'/web/forecast/overview','402879816e83525a016e8357d5270009','Ԥ��ſ�','navTab','Ԥ��ſ�',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d77e007b','��ȡԤ����������',2,'/web/forecast/auto/detail','402879816e83525a016e8357d5270009','��ȡԤ����������','navTab','��ȡԤ����������',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d788007d','����Ԥ������',2,'/web/forecast/setting','402879816e83525a016e8357d5270009','����Ԥ������','navTab','����Ԥ������',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d792007f','Ԥ�⸺�ɵ���',2,'/web/forecast/auto/import','402879816e83525a016e8357d5980017','Ԥ�⸺�ɵ���','navTab','Ԥ�⸺�ɵ���',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d7a90081','��ȡ����Ԥ����������ۺ�ָ��',2,'/web/forecast/weather/overview','402879816e83525a016e8357d5980017','��ȡ����Ԥ����������ۺ�ָ��','navTab','��ȡ����Ԥ����������ۺ�ָ��',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d7af0083','��������ϱ������ݴ�',2,'/web/forecast/auto','402879816e83525a016e8357d5980017','��������ϱ������ݴ�','navTab','��������ϱ������ݴ�',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d7ba0085','��ȡԤ�⸺�ɡ���׼�ա������պ͸߷�ʱ��',2,'/web/forecast/auto','402879816e83525a016e8357d5270009','��ȡԤ�⸺�ɡ���׼�ա������պ͸߷�ʱ��','navTab','��ȡԤ�⸺�ɡ���׼�ա������պ͸߷�ʱ��',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d7c30087','��ȡ������',2,'/web/forecast/similarDay','402879816e83525a016e8357d5980017','��ȡ������','navTab','��ȡ������',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d7cd0089','��ȡʵʱ��������',2,'/web/forecast/weather','402879816e83525a016e8357d5980017','��ȡʵʱ��������','navTab','��ȡʵʱ��������',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d7d2008b','��ȡ������',2,'/web/forecast/get/similarDays','402879816e83525a016e8357d5980017','��ȡ������','navTab','��ȡ������',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d7db008d','��ȡ�ݴ���',2,'/web/forecast/auto/cache','402879816e83525a016e8357d5980017','��ȡ�ݴ���','navTab','��ȡ�ݴ���',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d7e0008f','�߼�Ԥ��������',2,'/web/forecast/normal','402879816e83525a016e8357d59e0019','�߼�Ԥ��������','navTab','�߼�Ԥ��������',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d7e80091','��Ӳ�������',2,'/web/forecast/addReference','402879816e83525a016e8357d5980017','��Ӳ�������','navTab','��Ӳ�������',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d7f60093','�߼�Ԥ��ڼ���',2,'/web/forecast/holiday','402879816e83525a016e8357d59e0019','�߼�Ԥ��ڼ���','navTab','�߼�Ԥ��ڼ���',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d7fe0095','�ڼ�������',2,'/web/holiday/*','402879816e83525a016e8357d5a9001b','�ڼ�������','navTab','�ڼ�������',10000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d8080097','��ȡ������Ϣ��',2,'/web/holiday/off-day','402879816e83525a016e8357d59e0019','��ȡ������Ϣ��','navTab','��ȡ������Ϣ��',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d80e0099','��ȡ���нڼ���',2,'/web/holiday/all','402879816e83525a016e8357d5a9001b','��ȡ���нڼ���','navTab','��ȡ���нڼ���',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d815009b','ͳ��Ԥ�������',2,'/web/stats/forecastResult','402879816e83525a016e8357d67b0047','ͳ��Ԥ�������','navTab','ͳ��Ԥ�������',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d820009d','�鿴���б�',2,'/web/stats/allCity','402879816e83525a016e8357d67b0047','�鿴���б�','navTab','�鿴���б�',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d829009f','�鿴���������',2,'/web/stats/weather/date','402879816e83525a016e8357d67b0047','�鿴���������','navTab','�鿴���������',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d82f00a1','ͳ�Ƶ����������Բ����',2,'/web/stats/load','402879816e83525a016e8357d67b0047','ͳ�Ƶ����������Բ����','navTab','ͳ�Ƶ����������Բ����',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d83800a3','�鿴���ɵ�����',2,'/web/stats/load/date','402879816e83525a016e8357d67b0047','�鿴���ɵ�����','navTab','�鿴���ɵ�����',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d83d00a5','�������������㷨',2,'/web/stats/do/dataRepair','402879816e83525a016e8357d67b0047','�������������㷨','navTab','�������������㷨',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d84700a7','ͳ����ʷ�������Բ����',2,'/web/stats/weather','402879816e83525a016e8357d67b0047','ͳ����ʷ�������Բ����','navTab','ͳ����ʷ�������Բ����',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d85000a9','�鿴Ԥ�����ݵ�����',2,'/web/stats/fcLoad/date','402879816e83525a016e8357d67b0047','�鿴Ԥ�����ݵ�����','navTab','�鿴Ԥ�����ݵ�����',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d85a00ab','ȫʡ24Сʱʵʱ���',2,'/web/load/monitor','402879816e83525a016e8357d5270009','ȫʡ24Сʱʵʱ���','navTab','ȫʡ24Сʱʵʱ���',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d85f00ad','24�������������',2,'/web/load/curve','402879816e83525a016e8357d53c000b','24�������������','navTab','24�������������',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d86800af','������ʷ����',2,'/web/load/history/import','402879816e83525a016e8357d657003f','������ʷ����','navTab','������ʷ����',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d86e00b1','�ȶ��ȷ���',2,'/web/load/stability','402879816e83525a016e8357d5760011','�ȶ��ȷ���','navTab','�ȶ��ȷ���',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d87600b3','��ѯ�ֽܷ�����',2,'/web/load/resolution/week','402879816e83525a016e8357d53c000b','��ѯ�ֽܷ�����','navTab','��ѯ�ֽܷ�����',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d88000b5','��ѯ��������',2,'/web/load/feature/week','402879816e83525a016e8357d53c000b','��ѯ��������','navTab','��ѯ��������',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d88b00b7','������ʷ����',2,'/web/load/history/export','402879816e83525a016e8357d657003f','������ʷ����','navTab','������ʷ����',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d89000b9','��ѯ��������',2,'/web/load/feature/curve','402879816e83525a016e8357d53c000b','��ѯ��������','navTab','��ѯ��������',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d89800bb','��ѯ��󸺺�������',2,'/web/load/max','402879816e83525a016e8357d53c000b','��ѯ��󸺺�������','navTab','��ѯ��󸺺�������',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d8a100bd','��ȡʵ�ʸ��ɺ�Ԥ�⸺��',2,'/web/load/monitor/monitorCity','402879816e83525a016e8357d5270009','��ȡʵ�ʸ��ɺ�Ԥ�⸺��','navTab','��ȡʵ�ʸ��ɺ�Ԥ�⸺��',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d8a600bf','��ȡʵ�ʸ��ɺ�Ԥ�⸺��',2,'/web/load/monitor/monitorCity','402879816e83525a016e8357d5980017','��ȡʵ�ʸ��ɺ�Ԥ�⸺��','navTab','��ȡʵ�ʸ��ɺ�Ԥ�⸺��',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d8af00c1','��ȡʵ�ʸ��ɺ�Ԥ�⸺��',2,'/web/load/monitor/monitorCity','402879816e83525a016e8357d5ed0029','��ȡʵ�ʸ��ɺ�Ԥ�⸺��','navTab','��ȡʵ�ʸ��ɺ�Ԥ�⸺��',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d8b800c3','��ȡ��������',2,'/web/load/feature','402879816e83525a016e8357d53c000b','��ȡ��������','navTab','��ȡ��������',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d8bd00c5','������ʷ����',2,'/web/load/history','402879816e83525a016e8357d657003f','������ʷ����','navTab','������ʷ����',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d8c600c7','��ȡ�������и���',2,'/web/load/weather','402879816e83525a016e8357d53c000b','��ȡ�������и���','navTab','��ȡ�������и���',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d8ca00c9','��ȡ������ʷ����',2,'/web/load/history','402879816e83525a016e8357d53c000b','��ȡ������ʷ����','navTab','��ȡ������ʷ����',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d8d300cb','��ȡ��ҵ����',2,'/web/load/industry','402879816e83525a016e8357d53c000b','��ȡ��ҵ����','navTab','��ȡ��ҵ����',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d8dc00cd','ȫʡ24Сʱʵʱ���',2,'/web/load/monitorV2','402879816e83525a016e8357d5270009','ȫʡ24Сʱʵʱ���','navTab','ȫʡ24Сʱʵʱ���',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d8e200cf','���¶�̬',2,'/web/news/*','402879816e83525a016e8357d5270009','���¶�̬','navTab','���¶�̬',10000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d8ec00d1','�û�����Ȩ��',2,'/web/sysUserManage/*','402879816e83525a016e8357d6610041','�û�����Ȩ��','navTab','�û�����Ȩ��',10000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d8f800d3','�޸��û�����',2,'/web/sysUserManage/user/changeMyPassword','402879816e83525a016e8357d6610041','�޸��û�����','navTab','�޸��û�����',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d8fe00d5','���ϵ������',2,'/web/typhoonAnalysis/correlation','402879816e83525a016e8357d6720045','���ϵ������','navTab','���ϵ������',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d90600d7','����ɢ�����',2,'/web/typhoonAnalysis/weatherScatter','402879816e83525a016e8357d6720045','����ɢ�����','navTab','����ɢ�����',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d90e00d9','��¼�ջָ��շ���',2,'/web/typhoonAnalysis/anlysis','402879816e83525a016e8357d5d60025','��¼�ջָ��շ���','navTab','��¼�ջָ��շ���',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d91500db','���Բ���',2,'/web/typhoonAnalysis/wave','402879816e83525a016e8357d5d60025','���Բ���','navTab','���Բ���',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d91e00dd','������������',2,'/web/typhoonAnalysis/curve','402879816e83525a016e8357d5d60025','������������','navTab','������������',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d92900df','��������ͳ��',2,'/web/typhoonCollect/extremeWeather/stat','402879816e83525a016e8357d6720045','��������ͳ��','navTab','��������ͳ��',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d93000e1','���������������ֲ�',2,'/web/typhoonCollect/extreme/distribution','402879816e83525a016e8357d6720045','���������������ֲ�','navTab','���������������ֲ�',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d93900e3','��ֵͳ��',2,'/web/typhoonCollect/extremum/stats','402879816e83525a016e8357d6720045','��ֵͳ��','navTab','��ֵͳ��',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d94200e5','����k��ͼ',2,'/web/typhoonCollect/load/feature','402879816e83525a016e8357d6720045','����k��ͼ','navTab','����k��ͼ',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d94700e7','����������ʱ��ֲ�',2,'/web/typhoonCollect/extreme/count','402879816e83525a016e8357d6720045','����������ʱ��ֲ�','navTab','����������ʱ��ֲ�',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d94f00e9','��������ͼ',2,'/web/typhoonCollect/load/weather','402879816e83525a016e8357d6720045','��������ͼ','navTab','��������ͼ',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d95800eb','���¼����������ݸ���',2,'/web/weather/updateWeatherTyphoon','402879816e83525a016e8357d657003f','���¼����������ݸ���','navTab','���¼����������ݸ���',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d95d00ed','����������ʷ����',2,'/web/weather/history/export','402879816e83525a016e8357d657003f','����������ʷ����','navTab','����������ʷ����',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d96600ef','��ȡ�������ж�',2,'/web/weather/sensitivity','402879816e83525a016e8357d5820013','��ȡ�������ж�','navTab','��ȡ�������ж�',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d96b00f1','��ȡ������ʷ����',2,'/web/weather/history','402879816e83525a016e8357d657003f','��ȡ������ʷ����','navTab','��ȡ������ʷ����',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d97400f3','��ȡ������������',2,'/web/weather/getWeatherTyphoon','402879816e83525a016e8357d657003f','��ȡ������������','navTab','��ȡ������������',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d97c00f5','��ȡ�����������ر�ʶ',2,'/web/weather/feature','402879816e83525a016e8357d5820013','��ȡ�����������ر�ʶ','navTab','��ȡ�����������ر�ʶ',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d98300f7','���ؼ���ȫʡ��������(����ռȫʡ�ı���)',2,'/web/weather/getWeatherTyphoonRate','402879816e83525a016e8357d657003f','���ؼ���ȫʡ��������(����ռȫʡ�ı���)','navTab','���ؼ���ȫʡ��������(����ռȫʡ�ı���)',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d98b00f9','��ѯȫʡ��������(����ռȫʡ�ı���)',2,'/web/weather/fingWeatherTyphoonRate','402879816e83525a016e8357d657003f','��ѯȫʡ��������(����ռȫʡ�ı���)','navTab','��ѯȫʡ��������(����ռȫʡ�ı���)',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d99500fb','��ȡ����Ԥ������',2,'/web/weather/fc','402879816e83525a016e8357d657003f','��ȡ����Ԥ������','navTab','��ȡ����Ԥ������',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d99b00fd','��ȡ�������ʷ��Ԥ������',2,'/web/weather/his-fc','402879816e83525a016e8357d5ed0029','��ȡ�������ʷ��Ԥ������','navTab','��ȡ�������ʷ��Ԥ������',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d9a500ff','������ʵ������',2,'/web/weather/similar/weather','402879816e83525a016e8357d5980017','������ʵ������','navTab','������ʵ������',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d9af0101','����ȫʡ��������(����ռȫʡ�ı���)',2,'/web/weather/updateTyphoonRate','402879816e83525a016e8357d657003f','����ȫʡ��������(����ռȫʡ�ı���)','navTab','����ȫʡ��������(����ռȫʡ�ı���)',20000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('3213213214131','������',1,'UltraShortTerm','root','������','navTab','������',10000);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d9assdes','Ԥ���ѯ',1,'Accuracy','3213213214131','Ԥ���ѯ','navTab','Ԥ���ѯ',1);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d9ass12p','��׼ȷ������',1,'DayAccuracy','3213213214131','��׼ȷ������','navTab','��׼ȷ������',2);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402879816e83525a016e8357d38769d','����׼ȷ������',1,'BatchAccuracy','3213213214131','����׼ȷ������','navTab','����׼ȷ������',3);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('4028d0816cb8644d016cb86f057d0111','���Է���',1,'OutputCharacteristicAnalysis','root','���Է���','navTab','���Է���',9999);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('4028d0816cb8644d016cb86f057d0122','�ճ�������',1,'DayOutputCharacteristic','4028d0816cb8644d016cb86f057d0111','�ճ�������','navTab','�ճ�������',9999);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('4028d0816cb8644d016cb86f057d0133','�³�������',1,'MonthOutputCharacteristic','4028d0816cb8644d016cb86f057d0111','�³�������','navTab','�³�������',9999);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('4028d0816cb8644d016cb86f057d0144','���������',1,'YearOutputCharacteristic','4028d0816cb8644d016cb86f057d0111','���������','navTab','���������',9999);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402863816cf9ee38016cf9ef66c92220','�ۺ������ղ���',1,'MultipleSimilarDays','402879816e83525a016e8357d53c000b','�ۺ������ղ���','navTab','�ۺ������ղ���',9999);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('3','�ϱ���¼',1,'LoadForecastSubmissionManagement','402879816e83525a016e8357d58d0015','�ϱ���¼','navTab','�ϱ���¼',5);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('14','�ڼ�����Ϣ����',1,'HolidaySetting','402879816e83525a016e8357d5a9001b','�ڼ�����Ϣ����','navTab','�ڼ�����Ϣ����',9999);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('4028fa816c8b208b12343da97f90254','�¶�׼ȷ�ʷ���',1,'MonthAccuracyAnalysis','402879816e83525a016e8357d5ed0029','�¶�׼ȷ�ʷ���',null,null,null);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('20','����Դ����',1,'WeathersComparativeEvaluation','402879816e83525a016e8357d5ed0029','����Դ����','navTab','����Դ����',9999);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('402863816cf123123','����Ӱ�����',1,'MeteorologicalImpactAnalysis','402879816e83525a016e8357d53c000b','����Ӱ�����','navTab','����Ӱ�����',9999);
INSERT INTO "TSIE_MENU"("ID","NAME","TYPE","MENUPATH","PARENTID","DESCRIPTION","TARGETTYPE","REL","SORT") VALUES('748567291738cg5o3','�޵��������ϱ�',1,'OtherRevisedReport','402879816e83525a016e8357d58d0015','OtherRevisedReport','navTab','�޵��������ϱ�',3);

INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37b80091','3','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37b90092','14','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37b90093','402879816e83525a016e8357d59e0019','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37b90094','402879816e83525a016e8357d5270009','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37b90095','402879816e83525a016e8357d657003f','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37b90096','402863816cf9ee38016cf9ef66c92220','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37b90097','402879816e83525a016e8357d5820013','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37b90098','402879816e83525a016e8357d55f000d','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37b90099','402879816e83525a016e8357d5760011','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37b9009a','402879816e83525a016e8357d569000f','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37b9009b','4028fa816c8b208b12343da97f90254','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37b9009c','402879816e83525a016e8357d60a002f','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37b9009d','402879816e83525a016e8357d602002d','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37b9009e','402879816e83525a016e8357d5f8002b','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37b9009f','402879816e83525a016e8357d5b2001d','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37b900a0','4028d0816cb8644d016cb86f057d0144','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37b900a1','4028d0816cb8644d016cb86f057d0111','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37b900a2','4028d0816cb8644d016cb86f057d0122','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37b900a3','4028d0816cb8644d016cb86f057d0133','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37b900a4','402879816e83525a016e8357d9assdes','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37b900a5','3213213214131','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37b900a6','402879816e83525a016e8357d9ass12p','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37b900a7','402879816e83525a016e8357d38769d','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37b900a8','402879816e83525a016e8357d53c000b','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37b900a9','402863816cf123123','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37b900aa','402879816e83525a016e8357d58d0015','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37b900ab','402879816e83525a016e8357d5ed0029','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37b900ac','402879816e83525a016e8357d5a9001b','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37c500ad','402879816e83525a016e8357d6870049','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37c500ae','402879816e83525a016e8357d692004b','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37c500af','402879816e83525a016e8357d699004d','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37c500b0','402879816e83525a016e8357d6a4004f','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37c500b1','402879816e83525a016e8357d6ae0051','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37c500b2','402879816e83525a016e8357d6b80053','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37c500b3','402879816e83525a016e8357d6cc0057','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37c500b4','402879816e83525a016e8357d6d50059','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37c500b5','402879816e83525a016e8357d6de005b','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37c500b6','402879816e83525a016e8357d737006d','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37c500b7','402879816e83525a016e8357d742006f','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37c500b8','402879816e83525a016e8357d7570073','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37c600b9','402879816e83525a016e8357d7770079','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37c600ba','402879816e83525a016e8357d77e007b','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37c600bb','402879816e83525a016e8357d788007d','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37c600bc','402879816e83525a016e8357d7ba0085','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37c600bd','402879816e83525a016e8357d7e0008f','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37c600be','402879816e83525a016e8357d7f60093','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37c600bf','402879816e83525a016e8357d7fe0095','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37c600c0','402879816e83525a016e8357d8080097','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37c600c1','402879816e83525a016e8357d80e0099','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37c600c2','402879816e83525a016e8357d85a00ab','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37c600c3','402879816e83525a016e8357d85f00ad','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37c600c4','402879816e83525a016e8357d86800af','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37c600c5','402879816e83525a016e8357d86e00b1','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37c600c6','402879816e83525a016e8357d87600b3','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37c600c7','402879816e83525a016e8357d88000b5','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37c600c8','402879816e83525a016e8357d88b00b7','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37c600c9','402879816e83525a016e8357d89000b9','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37c600ca','402879816e83525a016e8357d89800bb','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37c600cb','402879816e83525a016e8357d8a100bd','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37c600cc','402879816e83525a016e8357d8af00c1','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37c600cd','402879816e83525a016e8357d8b800c3','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37c600ce','402879816e83525a016e8357d8bd00c5','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37c600cf','402879816e83525a016e8357d8c600c7','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37c600d0','402879816e83525a016e8357d8ca00c9','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37c600d1','402879816e83525a016e8357d8d300cb','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37c600d2','402879816e83525a016e8357d8dc00cd','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37c700d3','402879816e83525a016e8357d8e200cf','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37c700d4','402879816e83525a016e8357d95800eb','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37c700d5','402879816e83525a016e8357d95d00ed','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37c700d6','402879816e83525a016e8357d96600ef','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37c700d7','402879816e83525a016e8357d96b00f1','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37c700d8','402879816e83525a016e8357d97400f3','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37c700d9','402879816e83525a016e8357d97c00f5','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37c700da','402879816e83525a016e8357d98300f7','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37c700db','402879816e83525a016e8357d98b00f9','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37c700dc','402879816e83525a016e8357d99500fb','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37c700dd','402879816e83525a016e8357d99b00fd','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e37c700de','402879816e83525a016e8357d9af0101','402879816e83525a016e8357d4f30003');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e8295014c','402879816e83525a016e8357d5980017','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e8295014d','402879816e83525a016e8357d59e0019','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e8295014e','402879816e83525a016e8357d5270009','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e8295014f','402879816e83525a016e8357d657003f','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82950150','402879816e83525a016e8357d6320037','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82950151','402879816e83525a016e8357d6280035','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82950152','402879816e83525a016e8357d63d0039','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82950153','402879816e83525a016e8357d64d003d','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82950154','402879816e83525a016e8357d644003b','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82950155','402879816e83525a016e8357d6720045','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82950156','402879816e83525a016e8357d5820013','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82950157','402879816e83525a016e8357d55f000d','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82950158','402879816e83525a016e8357d5760011','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82950159','402879816e83525a016e8357d569000f','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e8295015a','402879816e83525a016e8357d61d0033','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e8295015b','402879816e83525a016e8357d6140031','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e8295015c','402879816e83525a016e8357d60a002f','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e8295015d','402879816e83525a016e8357d602002d','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e8295015e','402879816e83525a016e8357d5f8002b','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e8295015f','402879816e83525a016e8357d5bc001f','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82950160','402879816e83525a016e8357d5b2001d','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82950161','402879816e83525a016e8357d58d0015','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82950162','402879816e83525a016e8357d6610041','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82960163','402879816e83525a016e8357d53c000b','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82960164','402879816e83525a016e8357d5ed0029','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82960165','402879816e83525a016e8357d5a9001b','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c00166','402879816e83525a016e8357d6870049','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c00167','402879816e83525a016e8357d692004b','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c00168','402879816e83525a016e8357d699004d','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c00169','402879816e83525a016e8357d6a4004f','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c0016a','402879816e83525a016e8357d6ae0051','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c0016b','402879816e83525a016e8357d6b80053','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c0016c','402879816e83525a016e8357d6c40055','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c0016d','402879816e83525a016e8357d6cc0057','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c0016e','402879816e83525a016e8357d6d50059','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c0016f','402879816e83525a016e8357d6de005b','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c00170','402879816e83525a016e8357d6e9005d','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c00171','402879816e83525a016e8357d6f4005f','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c00172','402879816e83525a016e8357d6ff0061','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c00173','402879816e83525a016e8357d7060063','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c00174','402879816e83525a016e8357d7100065','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c00175','402879816e83525a016e8357d71a0067','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c00176','402879816e83525a016e8357d7260069','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c00177','402879816e83525a016e8357d730006b','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c00178','402879816e83525a016e8357d737006d','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c00179','402879816e83525a016e8357d742006f','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c0017a','402879816e83525a016e8357d74c0071','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c0017b','402879816e83525a016e8357d7570073','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c0017c','402879816e83525a016e8357d7640075','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c0017d','402879816e83525a016e8357d76f0077','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c0017e','402879816e83525a016e8357d7770079','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c1017f','402879816e83525a016e8357d77e007b','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c10180','402879816e83525a016e8357d788007d','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c10181','402879816e83525a016e8357d792007f','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c10182','402879816e83525a016e8357d7a90081','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c10183','402879816e83525a016e8357d7af0083','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c10184','402879816e83525a016e8357d7ba0085','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c10185','402879816e83525a016e8357d7c30087','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c10186','402879816e83525a016e8357d7cd0089','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c10187','402879816e83525a016e8357d7d2008b','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c10188','402879816e83525a016e8357d7db008d','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c10189','402879816e83525a016e8357d7e0008f','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c1018a','402879816e83525a016e8357d7e80091','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c1018b','402879816e83525a016e8357d7f60093','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c1018c','402879816e83525a016e8357d7fe0095','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c1018d','402879816e83525a016e8357d8080097','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c1018e','402879816e83525a016e8357d80e0099','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c1018f','402879816e83525a016e8357d85a00ab','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c10190','402879816e83525a016e8357d85f00ad','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c10191','402879816e83525a016e8357d86800af','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c10192','402879816e83525a016e8357d86e00b1','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c10193','402879816e83525a016e8357d87600b3','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c20194','402879816e83525a016e8357d88000b5','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c20195','402879816e83525a016e8357d88b00b7','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c20196','402879816e83525a016e8357d89000b9','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c20197','402879816e83525a016e8357d89800bb','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c20198','402879816e83525a016e8357d8a100bd','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c20199','402879816e83525a016e8357d8a600bf','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c2019a','402879816e83525a016e8357d8af00c1','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c2019b','402879816e83525a016e8357d8b800c3','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c2019c','402879816e83525a016e8357d8bd00c5','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c2019d','402879816e83525a016e8357d8c600c7','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c2019e','402879816e83525a016e8357d8ca00c9','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c2019f','402879816e83525a016e8357d8d300cb','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c201a0','402879816e83525a016e8357d8dc00cd','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c201a1','402879816e83525a016e8357d8e200cf','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c201a2','402879816e83525a016e8357d8ec00d1','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c201a3','402879816e83525a016e8357d8f800d3','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c201a4','402879816e83525a016e8357d8fe00d5','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c201a5','402879816e83525a016e8357d90600d7','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c201a6','402879816e83525a016e8357d92900df','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c301a7','402879816e83525a016e8357d93000e1','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c301a8','402879816e83525a016e8357d93900e3','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c301a9','402879816e83525a016e8357d94200e5','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c301aa','402879816e83525a016e8357d94700e7','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c301ab','402879816e83525a016e8357d94f00e9','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c301ac','402879816e83525a016e8357d95800eb','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c301ad','402879816e83525a016e8357d95d00ed','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c301ae','402879816e83525a016e8357d96600ef','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c301af','402879816e83525a016e8357d96b00f1','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c301b0','402879816e83525a016e8357d97400f3','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c301b1','402879816e83525a016e8357d97c00f5','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c301b2','402879816e83525a016e8357d98300f7','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c301b3','402879816e83525a016e8357d98b00f9','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c301b4','402879816e83525a016e8357d99500fb','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c301b5','402879816e83525a016e8357d99b00fd','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c301b6','402879816e83525a016e8357d9a500ff','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('4028835a7bdcf44e017bde6e82c301b7','402879816e83525a016e8357d9af0101','402882816e8e1834016ecf8984670005');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884514b0002','3','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884514b0003','14','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884514b0004','20','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884514b0005','402879816e83525a016e8357d58d0015','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884514b0006','402879816e83525a016e8357d5a9001b','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884514b0007','402879816e83525a016e8357d5980017','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884514b0008','748567291738cg5o3','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884514b0009','402879816e83525a016e8357d59e0019','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884514b000a','402879816e83525a016e8357d5bc001f','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884514b000b','402879816e83525a016e8357d5b2001d','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884514b000c','402879816e83525a016e8357d5270009','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884514b000d','4028d0816cb8644d016cb86f057d0122','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884514b000e','4028d0816cb8644d016cb86f057d0111','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884514b000f','4028d0816cb8644d016cb86f057d0133','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884514b0010','4028d0816cb8644d016cb86f057d0144','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884514c0011','402863816cf123123','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884514c0012','402879816e83525a016e8357d53c000b','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884514c0013','402879816e83525a016e8357d55f000d','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884514c0014','402879816e83525a016e8357d5760011','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884514c0015','402879816e83525a016e8357d5820013','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884514c0016','402863816cf9ee38016cf9ef66c92220','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884514c0017','402879816e83525a016e8357d569000f','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884514c0018','402879816e83525a016e8357d657003f','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884514c0019','402879816e83525a016e8357d60a002f','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884514c001a','402879816e83525a016e8357d6140031','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884514c001b','402879816e83525a016e8357d5f8002b','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884514c001c','402879816e83525a016e8357d602002d','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884514c001d','402879816e83525a016e8357d67b0047','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884514c001e','402879816e83525a016e8357d6720045','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884514c001f','402879816e83525a016e8357d6610041','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884514d0020','402879816e83525a016e8357d6680043','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884514d0021','402879816e83525a016e8357d6320037','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884514d0022','402879816e83525a016e8357d644003b','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884514d0023','402879816e83525a016e8357d64d003d','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884514d0024','402879816e83525a016e8357d5ed0029','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884514d0025','402879816e83525a016e8357d6280035','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884515d0026','402879816e83525a016e8357d7fe0095','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884515d0027','402879816e83525a016e8357d80e0099','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884515d0028','402879816e83525a016e8357d7640075','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884515d0029','402879816e83525a016e8357d9a500ff','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884515d002a','402879816e83525a016e8357d8a600bf','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884515d002b','402879816e83525a016e8357d7e80091','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884515d002c','402879816e83525a016e8357d7db008d','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884515d002d','402879816e83525a016e8357d7d2008b','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884515d002e','402879816e83525a016e8357d7cd0089','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884515d002f','402879816e83525a016e8357d7c30087','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884515d0030','402879816e83525a016e8357d7af0083','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884515d0031','402879816e83525a016e8357d7a90081','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884515d0032','402879816e83525a016e8357d792007f','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884515e0033','402879816e83525a016e8357d76f0077','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884515e0034','402879816e83525a016e8357d7e0008f','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884515e0035','402879816e83525a016e8357d8080097','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884515e0036','402879816e83525a016e8357d7f60093','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884515e0037','402879816e83525a016e8357d6cc0057','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884515e0038','402879816e83525a016e8357d8e200cf','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884515e0039','402879816e83525a016e8357d8dc00cd','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884515e003a','402879816e83525a016e8357d8a100bd','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884515e003b','402879816e83525a016e8357d85a00ab','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884515e003c','402879816e83525a016e8357d7ba0085','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884515e003d','402879816e83525a016e8357d788007d','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884515e003e','402879816e83525a016e8357d77e007b','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884515e003f','402879816e83525a016e8357d7770079','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884515e0040','402879816e83525a016e8357d7570073','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884515e0041','402879816e83525a016e8357d742006f','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884515e0042','402879816e83525a016e8357d6de005b','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884515f0043','402879816e83525a016e8357d6d50059','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884515f0044','402879816e83525a016e8357d85f00ad','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884515f0045','402879816e83525a016e8357d8d300cb','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884515f0046','402879816e83525a016e8357d8ca00c9','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884515f0047','402879816e83525a016e8357d8c600c7','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884515f0048','402879816e83525a016e8357d8b800c3','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884515f0049','402879816e83525a016e8357d89800bb','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884515f004a','402879816e83525a016e8357d89000b9','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884515f004b','402879816e83525a016e8357d88000b5','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884515f004c','402879816e83525a016e8357d87600b3','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884515f004d','402879816e83525a016e8357d86e00b1','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884515f004e','402879816e83525a016e8357d96600ef','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884515f004f','402879816e83525a016e8357d97c00f5','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884515f0050','402879816e83525a016e8357d6870049','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884515f0051','402879816e83525a016e8357d9af0101','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884515f0052','402879816e83525a016e8357d99500fb','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884515f0053','402879816e83525a016e8357d98b00f9','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884515f0054','402879816e83525a016e8357d98300f7','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d0884515f0055','402879816e83525a016e8357d97400f3','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d088451600056','402879816e83525a016e8357d96b00f1','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d088451600057','402879816e83525a016e8357d95d00ed','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d088451600058','402879816e83525a016e8357d95800eb','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d088451600059','402879816e83525a016e8357d8bd00c5','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d08845160005a','402879816e83525a016e8357d88b00b7','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d08845160005b','402879816e83525a016e8357d86800af','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d08845160005c','402879816e83525a016e8357d6b80053','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d08845160005d','402879816e83525a016e8357d6ae0051','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d08845160005e','402879816e83525a016e8357d6a4004f','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d08845160005f','402879816e83525a016e8357d699004d','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d088451600060','402879816e83525a016e8357d815009b','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d088451600061','402879816e83525a016e8357d85000a9','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d088451600062','402879816e83525a016e8357d84700a7','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d088451600063','402879816e83525a016e8357d83d00a5','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d088451600064','402879816e83525a016e8357d83800a3','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d088451600065','402879816e83525a016e8357d82f00a1','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d088451600066','402879816e83525a016e8357d829009f','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d088451600067','402879816e83525a016e8357d820009d','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d088451600068','402879816e83525a016e8357d74c0071','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d088451600069','402879816e83525a016e8357d94f00e9','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d08845160006a','402879816e83525a016e8357d94700e7','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d08845160006b','402879816e83525a016e8357d94200e5','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d08845160006c','402879816e83525a016e8357d93900e3','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d08845160006d','402879816e83525a016e8357d93000e1','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d08845161006e','402879816e83525a016e8357d92900df','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d08845161006f','402879816e83525a016e8357d90600d7','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d088451610070','402879816e83525a016e8357d8fe00d5','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d088451610071','402879816e83525a016e8357d8ec00d1','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d088451610072','402879816e83525a016e8357d8f800d3','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d088451610073','402879816e83525a016e8357d6e9005d','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d088451610074','402879816e83525a016e8357d71a0067','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d088451610075','402879816e83525a016e8357d7100065','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d088451610076','402879816e83525a016e8357d7060063','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d088451610077','402879816e83525a016e8357d6f4005f','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d088451610078','402879816e83525a016e8357d730006b','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d088451610079','402879816e83525a016e8357d7260069','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d08845161007a','402879816e83525a016e8357d6ff0061','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d08845161007b','402879816e83525a016e8357d692004b','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d08845161007c','402879816e83525a016e8357d99b00fd','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d08845161007d','402879816e83525a016e8357d8af00c1','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d08845161007e','402879816e83525a016e8357d737006d','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_MENU_ROLE"("ID","MENUID","ROLEID") VALUES('402883657d0882c4017d08845161007f','402879816e83525a016e8357d6c40055','402879816e83525a016e8357d4bc0001');


INSERT INTO "TSIE_ROLE"("ID","ROLE","DESCPT","CATEGORY") VALUES('402879816e83525a016e8357d4bc0001','ROLE_ADMIN','��������Ա','����Ȩ��');
INSERT INTO "TSIE_ROLE"("ID","ROLE","DESCPT","CATEGORY") VALUES('402879816e83525a016e8357d4f30003','ROLE_USER','��ͨ����Ա','��ͨ����');
INSERT INTO "TSIE_ROLE"("ID","ROLE","DESCPT","CATEGORY") VALUES('402879816e83525a016e8357d4f80004','ROLE_COMMON_ACTION','���з���Ȩ�޻������ƣ�������ɾ����','ͨ������');
INSERT INTO "TSIE_ROLE"("ID","ROLE","DESCPT","CATEGORY") VALUES('402882816e8e1834016ecf8984670005','ROLE_CITY','�е�','�е�');

INSERT INTO "TSIE_USER"("ID","USERNAME","ENABLE","PASSWD","NICKNAME","EMAIL","PHONENO","PASSWD_CHANGE_PROMPT","EXPIRED_DATE","LOGIN_MODE") VALUES('402879816e83525a016e8357d49f0000','admin',1,'513d5404723b2cc5b33a574d0016c875','ϵͳ����Ա','<EMAIL>','18600928842',0,null,null);
INSERT INTO "TSIE_USER"("ID","USERNAME","ENABLE","PASSWD","NICKNAME","EMAIL","PHONENO","PASSWD_CHANGE_PROMPT","EXPIRED_DATE","LOGIN_MODE") VALUES('ff8080817bdc2c88017bde35f2eb0023','yangzm',1,'513d5404723b2cc5b33a574d0016c875','��־��',null,null,1,null,2);

INSERT INTO "TSIE_USER_ROLE"("ID","USERID","ROLEID") VALUES('402879816e83525a016e8357d4da0002','402879816e83525a016e8357d49f0000','402879816e83525a016e8357d4bc0001');
INSERT INTO "TSIE_USER_ROLE"("ID","USERID","ROLEID") VALUES('ff8080817bdc2c88017bde35f2f90024','ff8080817bdc2c88017bde35f2eb0023','402879816e83525a016e8357d4f80004');
INSERT INTO "TSIE_USER_ROLE"("ID","USERID","ROLEID") VALUES('ff8080817bdc2c88017bde3614220026','ff8080817bdc2c88017bde35f2eb0023','402879816e83525a016e8357d4bc0001');

INSERT INTO "HOLIDAY_BASIC"("ID", "HOLIDAY", "DATE", "TYPE", "START_DATE", "END_DATE", "PRE_EFFECT_DAYS", "AFTER_EFFECT_DAYS", "OFF_DATES", "CODE", "YEAR", "CREATETIME", "UPDATETIME") VALUES ('4028fa817c9c6910017c9c6b09b00001', 'Ԫ��', '2022-01-01', '1', '2022-01-01', '2022-01-03', '0', '0', NULL, '101', '2022', '2021-11-02 00:00:00', '2021-11-02 00:00:00');
INSERT INTO "HOLIDAY_BASIC"("ID", "HOLIDAY", "DATE", "TYPE", "START_DATE", "END_DATE", "PRE_EFFECT_DAYS", "AFTER_EFFECT_DAYS", "OFF_DATES", "CODE", "YEAR", "CREATETIME", "UPDATETIME") VALUES ('2c9868ea7ccbd0bc017cdfbd62a60590', '����', '2022-02-01', '1', '2022-01-31', '2022-02-06', '0', '0', '2022-01-29,2022-01-30', '102', '2022', '2021-11-02 00:00:00', '2021-11-02 00:00:00');
INSERT INTO "HOLIDAY_BASIC"("ID", "HOLIDAY", "DATE", "TYPE", "START_DATE", "END_DATE", "PRE_EFFECT_DAYS", "AFTER_EFFECT_DAYS", "OFF_DATES", "CODE", "YEAR", "CREATETIME", "UPDATETIME") VALUES ('2c9868ea7ccbd0bc017cdfbe330a0592', '������', '2022-04-05', '1', '2022-04-03', '2022-04-05', '0', '0', '2022-04-02', '103', '2022', '2021-11-02 00:00:00', '2021-11-02 00:00:00');
INSERT INTO "HOLIDAY_BASIC"("ID", "HOLIDAY", "DATE", "TYPE", "START_DATE", "END_DATE", "PRE_EFFECT_DAYS", "AFTER_EFFECT_DAYS", "OFF_DATES", "CODE", "YEAR", "CREATETIME", "UPDATETIME") VALUES ('2c9868ea7ccbd0bc017cdfbf01ea0593', '�Ͷ���', '2022-05-01', '1', '2022-04-30', '2022-05-04', '0', '0', '2022-04-24,2022-05-07', '104', '2022', '2021-11-02 00:00:00', '2021-11-02 00:00:00');
INSERT INTO "HOLIDAY_BASIC"("ID", "HOLIDAY", "DATE", "TYPE", "START_DATE", "END_DATE", "PRE_EFFECT_DAYS", "AFTER_EFFECT_DAYS", "OFF_DATES", "CODE", "YEAR", "CREATETIME", "UPDATETIME") VALUES ('2c9868ea7ccbd0bc017cdfbfc36a0594', '�����', '2022-06-03', '1', '2022-06-03', '2022-06-05', '0', '0', NULL, '105', '2022', '2021-11-02 00:00:00', '2021-11-02 00:00:00');
INSERT INTO "HOLIDAY_BASIC"("ID", "HOLIDAY", "DATE", "TYPE", "START_DATE", "END_DATE", "PRE_EFFECT_DAYS", "AFTER_EFFECT_DAYS", "OFF_DATES", "CODE", "YEAR", "CREATETIME", "UPDATETIME") VALUES ('2c9868ea7ccbd0bc017cdfc0ff480596', '�����', '2022-09-10', '1', '2022-09-10', '2022-09-12', '0', '0', NULL, '106', '2022', '2021-11-02 00:00:00', '2021-11-02 00:00:00');
INSERT INTO "HOLIDAY_BASIC"("ID", "HOLIDAY", "DATE", "TYPE", "START_DATE", "END_DATE", "PRE_EFFECT_DAYS", "AFTER_EFFECT_DAYS", "OFF_DATES", "CODE", "YEAR", "CREATETIME", "UPDATETIME") VALUES ('2c9868ea7ccbd0bc017cdfcc9fce059b', '�����', '2022-10-01', '1', '2022-10-01', '2022-10-07', '0', '0', '2022-10-08,2022-10-09', '107', '2022', '2021-11-02 00:00:00', '2021-11-02 00:00:00');


 CREATE TABLE "WEATHER_CITY_FC_LOAD_FORECAST"(
"ID" VARCHAR(32),
"DATE" DATE NOT NULL,
"CITY_ID" VARCHAR(32) NOT NULL,
"ALGORITHM_ID" VARCHAR(32) NOT NULL,
"TYPE" TINYINT NOT NULL,
"T0000" DEC(32,2),
"T0015" DEC(32,2),
"T0030" DEC(32,2),
"T0045" DEC(32,2),
"T0100" DEC(32,2),
"T0115" DEC(32,2),
"T0130" DEC(32,2),
"T0145" DEC(32,2),
"T0200" DEC(32,2),
"T0215" DEC(32,2),
"T0230" DEC(32,2),
"T0245" DEC(32,2),
"T0300" DEC(32,2),
"T0315" DEC(32,2),
"T0330" DEC(32,2),
"T0345" DEC(32,2),
"T0400" DEC(32,2),
"T0415" DEC(32,2),
"T0430" DEC(32,2),
"T0445" DEC(32,2),
"T0500" DEC(32,2),
"T0515" DEC(32,2),
"T0530" DEC(32,2),
"T0545" DEC(32,2),
"T0600" DEC(32,2),
"T0615" DEC(32,2),
"T0630" DEC(32,2),
"T0645" DEC(32,2),
"T0700" DEC(32,2),
"T0715" DEC(32,2),
"T0730" DEC(32,2),
"T0745" DEC(32,2),
"T0800" DEC(32,2),
"T0815" DEC(32,2),
"T0830" DEC(32,2),
"T0845" DEC(32,2),
"T0900" DEC(32,2),
"T0915" DEC(32,2),
"T0930" DEC(32,2),
"T0945" DEC(32,2),
"T1000" DEC(32,2),
"T1015" DEC(32,2),
"T1030" DEC(32,2),
"T1045" DEC(32,2),
"T1100" DEC(32,2),
"T1115" DEC(32,2),
"T1130" DEC(32,2),
"T1145" DEC(32,2),
"T1200" DEC(32,2),
"T1215" DEC(32,2),
"T1230" DEC(32,2),
"T1245" DEC(32,2),
"T1300" DEC(32,2),
"T1315" DEC(32,2),
"T1330" DEC(32,2),
"T1345" DEC(32,2),
"T1400" DEC(32,2),
"T1415" DEC(32,2),
"T1430" DEC(32,2),
"T1445" DEC(32,2),
"T1500" DEC(32,2),
"T1515" DEC(32,2),
"T1530" DEC(32,2),
"T1545" DEC(32,2),
"T1600" DEC(32,2),
"T1615" DEC(32,2),
"T1630" DEC(32,2),
"T1645" DEC(32,2),
"T1700" DEC(32,2),
"T1715" DEC(32,2),
"T1730" DEC(32,2),
"T1745" DEC(32,2),
"T1800" DEC(32,2),
"T1815" DEC(32,2),
"T1830" DEC(32,2),
"T1845" DEC(32,2),
"T1900" DEC(32,2),
"T1915" DEC(32,2),
"T1930" DEC(32,2),
"T1945" DEC(32,2),
"T2000" DEC(32,2),
"T2015" DEC(32,2),
"T2030" DEC(32,2),
"T2045" DEC(32,2),
"T2100" DEC(32,2),
"T2115" DEC(32,2),
"T2130" DEC(32,2),
"T2145" DEC(32,2),
"T2200" DEC(32,2),
"T2215" DEC(32,2),
"T2230" DEC(32,2),
"T2245" DEC(32,2),
"T2300" DEC(32,2),
"T2315" DEC(32,2),
"T2330" DEC(32,2),
"T2345" DEC(32,2),
"T2400" DEC(32,2),
"CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(),
"UPDATETIME" TIMESTAMP(6),
"CALIBER_ID" VARCHAR(255),
PRIMARY KEY("ID"),
CONSTRAINT "WEATHER_INFO_FC_LOAD_FORECAST_UNIQUE" UNIQUE("DATE", "CITY_ID", "TYPE", "ALGORITHM_ID", "CALIBER_ID"))
 STORAGE( INITIAL 1 , NEXT 1 , MINEXTENTS 1 , on "PRIMARY", FILLFACTOR 0 ) ;


CREATE TABLE "LF_SHOW"."LF_SHOW"."REPORT_ACCURACY_SYNTHESIZE_CUMULATIVE"(
"id" VARCHAR(32),
"city_id" VARCHAR(32) NOT NULL,
"date" DATE NOT NULL,
"accuracy" DEC(10,4),
"accuracy_correct" DEC(10,4),
"report_time" TIMESTAMP(6) NOT NULL,
"create_time" TIMESTAMP(6) NOT NULL,
"update_time" TIMESTAMP(6) NOT NULL,
"type" INTEGER,
"point_accuracy" DEC(10,4),
"min_synthesize_accuracy" DEC(10,4),
"max_synthesize_accuracy" DEC(10,4),
"energy_synthesize_accuracy" DEC(10,4),
PRIMARY KEY("id"),
CONSTRAINT "uniqueindex" UNIQUE("city_id", "date"))
 STORAGE( INITIAL 1 , NEXT 1 , MINEXTENTS 1 , on "PRIMARY", FILLFACTOR 0 ) ;

 CREATE TABLE "LF_SHOW"."LF_SHOW"."STATISTICS_ACCURACY_LOAD_CITY_MONTH_HIS"(
"ID" VARCHAR(32),
"CITY_ID" VARCHAR(32) NOT NULL,
"CALIBER_ID" VARCHAR(32) NOT NULL,
"YEAR" VARCHAR(4) NOT NULL,
"MONTH" VARCHAR(2) NOT NULL,
"ACCURACY" DEC(10,4),
"CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(),
"UPDATETIME" TIMESTAMP(6),
PRIMARY KEY("ID"))
 STORAGE( INITIAL 1 , NEXT 1 , MINEXTENTS 1 , on "PRIMARY", FILLFACTOR 0 ) ;

 CREATE TABLE "LF_SHOW"."LF_SHOW"."STATISTICS_ACCURACY_LOAD_CITY_YEAR_HIS"(
"ID" VARCHAR(32),
"CITY_ID" VARCHAR(32) NOT NULL,
"CALIBER_ID" VARCHAR(32) NOT NULL,
"YEAR" VARCHAR(4) NOT NULL,
"ACCURACY" DEC(10,4),
"CREATETIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP(),
"UPDATETIME" TIMESTAMP(6),
PRIMARY KEY("ID"))
 STORAGE( INITIAL 1 , NEXT 1 , MINEXTENTS 1 , on "PRIMARY", FILLFACTOR 0 ) ;
 -- 项目初始化 DM
