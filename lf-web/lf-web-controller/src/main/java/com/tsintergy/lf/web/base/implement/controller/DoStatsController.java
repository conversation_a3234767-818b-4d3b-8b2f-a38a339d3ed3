package com.tsintergy.lf.web.base.implement.controller;


import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.sax.handler.<PERSON>H<PERSON>ler;
import com.alibaba.excel.EasyExcel;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.tsieframework.cloud.security.web.businesslog.annotation.OperateLog;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.aif.algorithm.serviceapi.base.constants.AlgorithmConstants;
import com.tsintergy.lf.core.constants.CityConstants;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.constants.ShortConstants;
import com.tsintergy.lf.core.constants.SystemConstant;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.core.util.MultipleThreadInvoker;
import com.tsintergy.lf.core.util.PeriodDataUtil;
import com.tsintergy.lf.serviceapi.algorithm.api.AreaForecastAlgorithmService;
import com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum;
import com.tsintergy.lf.serviceapi.base.analyze.api.DeviationAnalyzeCityDayFcService;
import com.tsintergy.lf.serviceapi.base.area.api.*;
import com.tsintergy.lf.serviceapi.base.area.dto.NetLossCoefficientDTO;
import com.tsintergy.lf.serviceapi.base.area.pojo.BasePlanDO;
import com.tsintergy.lf.serviceapi.base.base.api.CaliberService;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.api.HolidayService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CaliberDO;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.evalucation.api.StatisticsAccuracyLoadCityMonthHisService;
import com.tsintergy.lf.serviceapi.base.evalucation.api.StatisticsAccuracyLoadCityYearHisService;
import com.tsintergy.lf.serviceapi.base.evalucation.api.StatisticsWeatherCityDayFcStatService;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.StatisticsAccuracyLoadCityMonthHisDO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.StatisticsAccuracyLoadCityYearHisDO;
import com.tsintergy.lf.serviceapi.base.forecast.api.*;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.AlgorithmDO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityFcClctService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisClctService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureStatService;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadStatDTO;
import com.tsintergy.lf.serviceapi.base.load.pojo.*;
import com.tsintergy.lf.serviceapi.base.report.api.ReportAccuracyDayService;
import com.tsintergy.lf.serviceapi.base.report.api.ReportAccuracyMonthService;
import com.tsintergy.lf.serviceapi.base.report.api.ReportAccuracySynthesizeMonthService;
import com.tsintergy.lf.serviceapi.base.report.api.ReportAccuracyWeekService;
import com.tsintergy.lf.serviceapi.base.report.pojo.ReportAccuracyDayDO;
import com.tsintergy.lf.serviceapi.base.report.pojo.ReportAccuracyMonthDO;
import com.tsintergy.lf.serviceapi.base.report.pojo.ReportAccuracySynthesizeMonthDO;
import com.tsintergy.lf.serviceapi.base.report.pojo.ReportAccuracyWeekDO;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceapi.base.system.dto.SystemAvgTemperatureDTO;
import com.tsintergy.lf.serviceapi.base.system.dto.SystemTotalAvgTemperatureDTO;
import com.tsintergy.lf.serviceapi.base.system.pojo.SettingSystemDO;
import com.tsintergy.lf.serviceapi.base.weather.api.*;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherStationHisBasicDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherStationInfoDO;
import com.tsintergy.lf.web.base.controller.CommonBaseController;
import com.tsintergy.lf.web.base.forecast.thread.AutoForecastTask;
import com.tsintergy.lf.web.base.implement.WeatherStationImportDO;
import com.tsintergy.lf.web.base.implement.WeatherStationListener;
import com.tsintergy.lf.web.base.implement.response.LoadAndWeatherResponse;
import com.tsintergy.lf.web.base.load.request.ForecsatRequest;
import com.tsintergy.lf.web.base.load.response.NewForecsatRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.SneakyThrows;
import net.sf.cglib.beans.BeanMap;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import static com.tsieframework.core.base.exception.TsieExceptionUtils.newBusinessException;

/**
 * 统计负荷特性并入库
 */
@Api(tags = "统计负荷特性并入库")
@RestController
@RequestMapping("/stats")
public class DoStatsController extends CommonBaseController {

    private final Logger logger = LogManager.getLogger(DoStatsController.class);
    @Autowired
    SettingSystemService settingSystemService;
    @Autowired
    StatisticsAccuracyLoadCityMonthHisService statisticsAccuracyLoadCityMonthHisService;
    @Autowired
    StatisticsAccuracyLoadCityYearHisService statisticsAccuracyLoadCityYearHisService;
    @Autowired
    Environment environment;
    @Autowired
    DeviationAnalyzeCityDayFcService deviationAnalyzeCityDayFcService;
    @Autowired
    WeatherStatService weatherStatService;
    @Autowired
    LoadAreaHisService loadAreaHisService;
    @Autowired
    LoadAreaFcService loadAreaFcService;
    @Autowired
    WeatherAreaFcService weatherAreaFcService;
    @Autowired
    WeatherAreaHisService weatherAreaHisService;
    @Autowired
    BasePlanService basePlanService;
    // 分旬
    //String url = "http://192.168.3.99:81/lf_bgd/web/fcModel/month/forecastAgain?cityId={cityId}&algorithmId={algorithmId}&forecastDay={forecastDay}&status={status}";
    String url = "http://10.42.6.100:8001/lf_hb/web/fcModel/month/forecastAgain?cityId={cityId}&algorithmId={algorithmId}&forecastDay={forecastDay}&status={status}";
    // 月度
    //String url1 = "http://192.168.3.99:81/lf_bgd/web/fcModel/quarter/forecastAgain?cityId={cityId}&algorithmId={algorithmId}&forecastDay={forecastDay}&endDay={endDay}&season={season}&status={status}";
    String url1 = "http://10.42.6.100:8001/lf_hb/web/fcModel/quarter/forecastAgain?cityId={cityId}&algorithmId={algorithmId}&forecastDay={forecastDay}&endDay={endDay}&season={season}&status={status}";
    // 年度
    //String url2 = "http://192.168.3.99:81/lf_bgd/web/fcModel/year/forecastAgain?cityId={cityId}&algorithmId={algorithmId}&forecastDay={forecastDay}&endDay={endDay}&status={status}";
    String url2 = "http://10.42.6.100:8001/lf_hb/web/fcModel/year/forecastAgain?cityId={cityId}&algorithmId={algorithmId}&forecastDay={forecastDay}&endDay={endDay}&status={status}";
    // 分旬准确率
    //String url3 = "http://192.168.3.99:81/lf_bgd/web/fcModel/month/doMonthAccuracyFc?cityId={cityId}&caliberId={caliberId}&algorithmId={algorithmId}&startDate={forecastDay}&endDate={forecastDay1}";
    String url3 = "http://10.42.6.100:8001/lf_hb/web/fcModel/month/doMonthAccuracyFc?cityId={cityId}&caliberId={caliberId}&algorithmId={algorithmId}&startDate={forecastDay}&endDate={forecastDay1}";
    @Autowired
    LoadCityHisService loadCityHisService;
    @Autowired
    WeatherStationHisBasicService weatherStationHisBasicService;
    @Autowired
    WeatherStationService weatherStationService;
    @Autowired
    LoadCityFcClctService loadCityFcClctService;
    @Autowired
    private LoadFeatureStatService loadFeatureStatService;
    @Autowired
    private WeatherFeatureStatService weatherFeatureStatService;
    @Autowired
    private CityService cityService;
    @Autowired
    private LoadCityHisClctService loadCityHisClctService;
    @Autowired
    private LoadCityFcService loadCityFcService;
    @Autowired
    private WeatherCityHisService weatherCityHisService;
    @Autowired
    private CaliberService caliberService;
    @Autowired
    private ForecastResultStatService forecastResultStatService;
    @Autowired
    private ForecastService forecastService;
    @Autowired
    private AutoForecastService autoForecastService;
    @Autowired
    private AlgorithmService algorithmService;
    @Autowired
    private HolidayService holidayService;
    @Autowired
    private StatisticsWeatherCityDayFcStatService statisticsWeatherCityDayFcStatService;
    @Autowired
    private StatisticsSynthesizeWeatherCityDayHisService statisticsSynthesizeWeatherCityDayHisService;
    @Autowired
    private StatisticsSynthesizeWeatherCityDayFcService statisticsSynthesizeWeatherCityDayFcService;
    @Autowired
    private StatisticsAccuracyWeatherCityDayHisService statisticsAccuracyWeatherCityDayHisService;
    @Autowired
    private StatisticsAccuracyWeatherCityMonthHisService statisticsAccuracyWeatherCityMonthHisService;
    //String url = "http://*************:8307/lf/web/fcModel/month/forecastAgain?cityId={cityId}&algorithmId={algorithmId}&forecastDay={forecastDay}&status={status}";
    @Autowired
    private StatisticsAccuracyWeatherCityYearHisService statisticsAccuracyWeatherCityYearHisService;
    //String url1 = "http://*************:8307/lf/web/fcModel/quarter/forecastAgain?cityId={cityId}&algorithmId={algorithmId}&forecastDay={forecastDay}&endDay={endDay}&season={season}&status={status}";
    @Autowired
    private ScheduledThreadPoolExecutor scheduledThreadPoolExecutor;
    //String url2 = "http://*************:8307/lf/web/fcModel/year/forecastAgain?cityId={cityId}&algorithmId={algorithmId}&forecastDay={forecastDay}&endDay={endDay}&status={status}";
    @Autowired
    private ReportAccuracyDayService reportAccuracyDayService;
    //String url3 = "http://localhost:8305/lf/web/fcModel/month/doMonthAccuracyFc?cityId={cityId}&caliberId={caliberId}&algorithmId={algorithmId}&startDate={forecastDay}&endDate={forecastDay1}";
    @Autowired
    private ReportAccuracyWeekService reportAccuracyWeekService;
    @Autowired
    private ReportAccuracyMonthService reportAccuracyMonthService;
    @Autowired
    private ReportAccuracySynthesizeMonthService reportAccuracySynthesizeMonthService;
    @Autowired
    private AreaForecastAlgorithmService areaForecastAlgorithmService;

    /**
     * 功能描述: <br> 查看城市表
     *
     * <AUTHOR>
     * @since 1.0.0
     */
    @ApiOperation("查看城市表")
    @OperateLog(operate = "查看城市表")
    @RequestMapping(value = "/allCity", method = RequestMethod.GET)
    public BaseResp<List<CityDO>> initCity() throws Exception {
        BaseResp resp = BaseResp.succResp();
        List<CityDO> cityList = cityService.findAllCitys();
        resp.setData(cityList);
        return resp;
    }

    /**
     * 功能描述: <br> 查看负荷的日期
     *
     * <AUTHOR>
     * @since 1.0.0
     */
    @ApiOperation("查看负荷的日期")
    @OperateLog(operate = "查看负荷的日期")
    @RequestMapping(value = "/load/date", method = RequestMethod.GET)
    public BaseResp<List<LoadStatDTO>> initLoadDate() throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<CityDO> cityList = cityService.findAllCitys();
        List<LoadStatDTO> list = new ArrayList<>();
        for (CityDO cityVO : cityList) {
            List<LoadCityHisClctDO> loadCityHisClctVOS = loadCityHisClctService.findLoadCityHisVOS(cityVO.getId(),
                    null, null, null);
            if (CollectionUtils.isEmpty(loadCityHisClctVOS)) {
                LoadStatDTO load = new LoadStatDTO();
                load.setCityId(cityVO.getId());
                load.setCityName(cityVO.getCity());
                load.setCaliberId("ALL");
                load.setDate("空");
                list.add(load);
            } else {
                Map<String, List<LoadCityHisClctDO>> mapData =
                        loadCityHisClctVOS.stream().collect(Collectors.groupingBy(LoadCityHisClctDO::getCaliberId));
                mapData.forEach((k, v) -> {
                    LoadStatDTO load = new LoadStatDTO();
                    load.setCityId(cityVO.getId());
                    load.setCityName(cityVO.getCity());
                    String caliber = caliberService.findCaliberDOByPk(k).getName();
                    load.setCaliberId(caliber);
                    Date startDate = v.get(0).getDate();
                    Date endDate = v.get(v.size() - 1).getDate();
                    String dateStr =
                            DateUtils.date2String(startDate, DateFormatType.SIMPLE_DATE_FORMAT_STR) + "----" + DateUtils
                                    .date2String(endDate, DateFormatType.SIMPLE_DATE_FORMAT_STR);
                    load.setDate(dateStr);
                    list.add(load);
                });
            }
        }
        baseResp.setData(list);
        return baseResp;
    }

    /**
     * 功能描述: <br> 查看预测数据的日期
     *
     * <AUTHOR>
     * @since 1.0.0
     */
    @ApiOperation("查看预测数据的日期")
    @OperateLog(operate = "查看预测数据的日期")
    @RequestMapping(value = "/fcLoad/date", method = RequestMethod.GET)
    public BaseResp<List<LoadStatDTO>> initFcDate() throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<CityDO> cityList = cityService.findAllCitys();
        List<LoadStatDTO> list = new ArrayList<>();
        for (CityDO cityVO : cityList) {
            List<LoadCityFcDO> loadCityFcVOS = loadCityFcService.findLoadCityFcDO(cityVO.getId(), null, null);
            if (CollectionUtils.isEmpty(loadCityFcVOS)) {
                LoadStatDTO load = new LoadStatDTO();
                load.setCityId(cityVO.getId());
                load.setCityName(cityVO.getCity());
                load.setDate("空");
                list.add(load);
            } else {
                Map<String, Map<String, List<LoadCityFcDO>>> mapData =
                        loadCityFcVOS.stream().collect(Collectors.groupingBy(LoadCityFcDO::getCaliberId,
                                Collectors.groupingBy(LoadCityFcDO::getAlgorithmId)));
                mapData.forEach((k, v) -> {
                    for (Map.Entry<String, List<LoadCityFcDO>> entry : v.entrySet()) {
                        try {
                            LoadStatDTO load = new LoadStatDTO();
                            load.setCityId(cityVO.getId());
                            load.setCityName(cityVO.getCity());
                            String caliber = caliberService.findCaliberDOByPk(k).getName();
                            load.setCaliberId(caliber);
                            AlgorithmDO algorithmVO = null;
                            algorithmVO = algorithmService.findAlgorithmVOByPk(entry.getKey());
                            load.setAlgorithmName(algorithmVO.getAlgorithmCn());
                            if (entry.getValue() == null) {
                                load.setDate("空");
                            } else {
                                List<LoadCityFcDO> loadCityFcVOS1 = entry.getValue();
//                                loadCityFcVOS1.sort(Comparator.comparing(LoadCityFcDO::getDate));
                                Date startDate = loadCityFcVOS1.get(0).getDate();
                                Date endDate = loadCityFcVOS1.get(loadCityFcVOS1.size() - 1).getDate();
                                String dateStr = DateUtils.date2String(startDate,
                                        DateFormatType.SIMPLE_DATE_FORMAT_STR) + "----" + DateUtils
                                        .date2String(endDate, DateFormatType.SIMPLE_DATE_FORMAT_STR);
                                load.setDate(dateStr);
                            }
                            list.add(load);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                });
            }
        }
        baseResp.setData(list);
        return baseResp;
    }

    /**
     * 功能描述: <br> 查看气象的日期
     *
     * <AUTHOR>
     * @since 1.0.0
     */
    @ApiOperation("查看气象的日期")
    @OperateLog(operate = "查看气象的日期")
    @RequestMapping(value = "/weather/date", method = RequestMethod.GET)
    public BaseResp<List<LoadAndWeatherResponse>> initWeatherDate() throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<CityDO> cityVOList = cityService.findAllCitys();
        List<LoadAndWeatherResponse> loadAndWeatherResponseList = new ArrayList<>();
        for (CityDO cityVO : cityVOList) {
            List<WeatherCityHisDO> weatherCityHisVOS = weatherCityHisService.findWeatherCityHisDOs(cityVO.getId(),
                    null, null, null);
            LoadAndWeatherResponse loadAndWeatherResponse = new LoadAndWeatherResponse();
            loadAndWeatherResponse.setCityId(cityVO.getId());
            loadAndWeatherResponse.setCityName(cityVO.getCity());
            loadAndWeatherResponse.setCaliberId(cityVO.getCity());
            if (CollectionUtils.isEmpty(weatherCityHisVOS)) {
                loadAndWeatherResponse.setDate("空");
            } else {
                Date startDate = weatherCityHisVOS.get(0).getDate();
                Date endDate = weatherCityHisVOS.get(weatherCityHisVOS.size() - 1).getDate();
                String dateStr =
                        DateUtils.date2String(startDate, DateFormatType.SIMPLE_DATE_FORMAT_STR) + "----" + DateUtils
                                .date2String(endDate, DateFormatType.SIMPLE_DATE_FORMAT_STR);
                loadAndWeatherResponse.setDate(dateStr);
            }
            loadAndWeatherResponseList.add(loadAndWeatherResponse);
        }
        baseResp.setData(loadAndWeatherResponseList);
        return baseResp;
    }

    /**
     * 统计地区历史负荷特性并入库 需要参数：startDate 、endDate
     */
    @ApiOperation("统计地区负荷特性并入库")
    @OperateLog(operate = "统计地区负荷特性并入库")
    @RequestMapping(value = "/load", method = RequestMethod.POST)
    public BaseResp saveLoadDay(@RequestBody ForecsatRequest forecsatRequest) throws Exception {
        BaseResp resp = BaseResp.succResp();
        List<String> cityIds = forecsatRequest.getCityIds();
        Date startDate = forecsatRequest.getStartDate();
        Date endDate = forecsatRequest.getEndDate();
        if (cityIds.size() == 1 && cityIds.get(0).equals("all")) {
            //全部城市 不用城市筛选条件
            cityIds = null;
        }

        //统计日负荷特性
        List<LoadFeatureCityDayHisDO> loadDay = loadFeatureStatService.doStatLoadFeatureCityDay(cityIds, startDate,
                endDate, null);
        //统计周负荷特性
        List<LoadFeatureCityWeekHisDO> loadWeek = loadFeatureStatService.doStatLoadFeatureCityWeek(cityIds, startDate,
                endDate, null);
        //统计月负荷特性
        List<LoadFeatureCityMonthHisDO> loadMouth = loadFeatureStatService.doStatLoadFeatureCityMonth(cityIds, startDate
                , endDate, null);
        //统计季负荷特性
        List<LoadFeatureCityQuarterHisDO> loadQuarter = loadFeatureStatService.doStatLoadFeatureCityQuarter(cityIds,
                startDate, endDate, null);
        // 统计年负荷特性
        List<LoadFeatureCityYearHisDO> loadYear = loadFeatureStatService.doStatLoadFeatureCityYear(cityIds, startDate,
                endDate, null);
        return resp;
    }

    /**
     * 统计累计平均温度并入库 需要参数：startDate 、endDate
     */
    @ApiOperation("统计累计平均温度并入库")
    @OperateLog(operate = "统计累计平均温度并入库")
    @RequestMapping(value = "/weather/avgTemperature", method = RequestMethod.POST)
    public BaseResp saveAveTemperature(@RequestBody ForecsatRequest forecsatRequest) throws Exception {
        BaseResp resp = BaseResp.succResp();
        List<String> cityIds = forecsatRequest.getCityIds();
        Date startDate = forecsatRequest.getStartDate();
        Date endDate = forecsatRequest.getEndDate();
        if (cityIds.size() == 1 && cityIds.get(0).equals("all")) {
            //全部城市 不用城市筛选条件
            List<CityDO> cityVOS = cityService.findAllCitys();
            cityIds = cityVOS.stream().map(CityDO::getId).collect(Collectors.toList());
        }

        for (String cityId : cityIds) {
            weatherFeatureStatService.doStatWeatherFeatureCityDayAvgTemperature(cityId, startDate, endDate);
        }
        return resp;

    }

    /**
     * 功能描述: 统计地区预测负荷特性并入库<br>
     *
     * @Return: {@link BaseResp}
     * @Version: 1.0.0
     * @Author:<EMAIL>
     * @Date: 2021/11/8 16:36
     */

    @ApiOperation("负荷预测")
    @RequestMapping(value = "/loadFcFeature", method = RequestMethod.POST)
    public BaseResp statsLoadFcFeature(@RequestBody ForecsatRequest forecsatRequest) throws Exception {
        List<String> cityIds = forecsatRequest.getCityIds();
        Date startDate = forecsatRequest.getStartDate();
        Date endDate = forecsatRequest.getEndDate();
        if (cityIds.size() == 1 && cityIds.get(0).equals("all")) {
            //全部城市 不用城市筛选条件
            List<CityDO> cityVOS = cityService.findAllCitys();
            cityIds = cityVOS.stream().map(CityDO::getId).collect(Collectors.toList());
        }

        //统计日预测负荷特性
        loadFeatureStatService.doStatLoadFeatureCityDayFc(cityIds, startDate, endDate, null);
        //统计周预测负荷特性
        loadFeatureStatService.doStatLoadFeatureCityWeekFc(cityIds, startDate,
                endDate, "1");
        //统计月预测负荷特性
        loadFeatureStatService.doStatLoadFeatureCityMonthFc(cityIds, startDate
                , endDate, "1");
        BaseResp baseResp = BaseResp.succResp();
        return baseResp;

    }

    /**
     * 统计历史气象特性并入库
     */
    @ApiOperation("统计历史气象特性并入库")
    @OperateLog(operate = "统计历史气象特性并入库")
    @RequestMapping(value = "/weather", method = RequestMethod.POST)
    public BaseResp saveWeatherDay(@RequestBody ForecsatRequest forecsatRequest) throws Exception {
        BaseResp resp = BaseResp.succResp();
        List<String> cityIds = forecsatRequest.getCityIds();
        Date startDate = forecsatRequest.getStartDate();
        Date endDate = forecsatRequest.getEndDate();
        if (cityIds.size() == 1 && cityIds.get(0).equals("all")) {
            //全部城市 不用城市筛选条件
            List<CityDO> cityVOS = cityService.findAllCitys();
            cityIds = cityVOS.stream().map(CityDO::getId).collect(Collectors.toList());
        }

        try {
            //日气象
            Date start = startDate;
            while (!start.after(endDate)) {
                Date end = DateUtils.addMonths(start, 1);
                MultipleThreadInvoker.getInstance().invoke(new Worker(endDate, cityIds, start, end));
                start = DateUtils.addDays(end, 1);
            }


//            //月气象  todo wangchen 使用多线程后，需要等多线程执行完成后才能统计
//            List<WeatherFeatureCityMonthHisDO> weatherMonth =
//                weatherFeatureStatService.doStatWeatherFeatureCityMonth(cityIds, startDate, endDate);
//            //季气象
//            List<WeatherFeatureCityQuarterHisDO> weatherQuarter =
//                weatherFeatureStatService.doStatWeatherFeatureCityQuarter(cityIds, startDate, endDate);
        } catch (Exception e) {
            throw newBusinessException(e.getMessage(), e);
        }
        return resp;
    }

    /**
     * 统计预测气象特性并入库
     */
    @ApiOperation("统计预测气象特性并入库")
    @OperateLog(operate = "统计预测气象特性并入库")
    @RequestMapping(value = "/weatherFcFeature", method = RequestMethod.POST)
    public BaseResp saveWeatherFcFeature(@RequestBody ForecsatRequest forecsatRequest) {
        BaseResp resp = BaseResp.succResp();
        List<String> cityIds = forecsatRequest.getCityIds();
        Date startDate = forecsatRequest.getStartDate();
        Date endDate = forecsatRequest.getEndDate();
        try {
            if (cityIds.size() == 1 && cityIds.get(0).equals("all")) {
                List<CityDO> cityVOS = cityService.findAllCitys();
                cityIds = cityVOS.stream().map(CityDO::getId).collect(Collectors.toList());
            }
            //预测日气象
            for (String cityId : cityIds) {
                weatherFeatureStatService.doStatWeatherFeatureCityDayFc(cityId, startDate, endDate);
            }
        } catch (Exception e) {
            throw newBusinessException(e.getMessage(), e);
        }
        return resp;
    }

    /**
     * 功能描述: <br>
     *
     * <AUTHOR>
     * @since 1.0.0
     */
    @ApiOperation("统计预测后评估")
    @OperateLog(operate = "统计预测后评估")
    @RequestMapping(value = "/forecastResult", method = RequestMethod.POST)
    public BaseResp testStatForecastResult(@RequestBody ForecsatRequest forecsatRequest) throws Exception {
        List<String> cityIds = forecsatRequest.getCityIds();
        Date startDate = forecsatRequest.getStartDate();
        Date endDate = forecsatRequest.getEndDate();
        if (CollectionUtils.isEmpty(forecsatRequest.getCaliberIds())) {
            forecsatRequest.setCaliberIds(
                    caliberService.findAllCalibers().stream().map(CaliberDO::getId).collect(Collectors.toList()));
        }
        List<ReportAccuracyDayDO> reportAccuracyDayVOS = new ArrayList<>();
        System.out.println(cityIds);
        if (cityIds.size() == 1 && cityIds.get(0).equals("all")) {
            forecastResultStatService.statForecastResult(null, null, null, startDate, endDate);
            //deviationAnalyzeCityDayFcService.doInitDeviationAnalyze(startDate, endDate, null);

            List<CityDO> cityVOS = cityService.findAllCitys();
            for (String caliberId : forecsatRequest.getCaliberIds()) {
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
                String date1 = formatter.format(startDate);
                String date2 = formatter.format(endDate);
                try {
                    reportAccuracyDayVOS = reportAccuracyDayService
                            .doStatSaveReportAccuracy(null, startDate, endDate, caliberId);
                    List<StatisticsAccuracyLoadCityMonthHisDO> monthVOList = statisticsAccuracyLoadCityMonthHisService
                            .getReportMonthAccuracy(cityVOS, caliberId, date1.substring(0, 7), date2.substring(0, 7));
                    statisticsAccuracyLoadCityMonthHisService.doSaveOrUpdate(monthVOList);
                } catch (Exception e) {
                    logger.error("统计月负荷准确率异常...", e);
                    e.printStackTrace();
                }

                try {
                    List<StatisticsAccuracyLoadCityYearHisDO> yearVOList = statisticsAccuracyLoadCityYearHisService
                            .getReportYearAccuracy(cityVOS, caliberId, date1.substring(0, 4), date2.substring(0, 4));
                    statisticsAccuracyLoadCityYearHisService.doSaveOrUpdate(yearVOList);
                } catch (Exception e) {
                    logger.error("统计年负荷准确率异常...", e);
                    e.printStackTrace();
                }
            }
        } else {
            for (String cityId : cityIds) {
                forecastResultStatService.statForecastResult(cityId, null, null, startDate, endDate);
                //deviationAnalyzeCityDayFcService.doInitDeviationAnalyze(startDate, endDate, cityId);
            }
            List<CityDO> dos = new ArrayList<>();
            for (String cityId : cityIds) {
                CityDO cityById = cityService.findCityById(cityId);
                dos.add(cityById);

            }
            for (String caliberId : forecsatRequest.getCaliberIds()) {
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
                String date1 = formatter.format(startDate);
                String date2 = formatter.format(endDate);
                for (String cityId : cityIds) {
                    reportAccuracyDayVOS
                            .addAll(
                                    reportAccuracyDayService.doStatSaveReportAccuracy(cityId, startDate, endDate, caliberId));
                }
                try {
                    List<StatisticsAccuracyLoadCityMonthHisDO> monthVOList = statisticsAccuracyLoadCityMonthHisService
                            .getReportMonthAccuracy(dos, caliberId, date1.substring(0, 7), date2.substring(0, 7));
                    statisticsAccuracyLoadCityMonthHisService.doSaveOrUpdate(monthVOList);
                } catch (Exception e) {
                    logger.error("统计月负荷准确率异常...", e);
                    e.printStackTrace();
                }

                try {
                    List<StatisticsAccuracyLoadCityYearHisDO> yearVOList = statisticsAccuracyLoadCityYearHisService
                            .getReportYearAccuracy(dos, caliberId, date1.substring(0, 4), date2.substring(0, 4));
                    statisticsAccuracyLoadCityYearHisService.doSaveOrUpdate(yearVOList);
                } catch (Exception e) {
                    logger.error("统计年负荷准确率异常...", e);
                    e.printStackTrace();
                }

            }
        }
        //统计日负荷填报准确率
        reportAccuracyDayService.doSaveOrUpdate(reportAccuracyDayVOS);

        BaseResp baseResp = BaseResp.succResp();
        return baseResp;
    }

    /**
     * 实施页面接口
     */
    @ApiOperation("调用修正数据算法")
    @OperateLog(operate = "调用修正数据算法")
    @RequestMapping(value = "/do/dataRepair", method = RequestMethod.POST)
    public BaseResp doAllDataRepair(@RequestBody ForecsatRequest forecsatRequest) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<String> cityIds = forecsatRequest.getCityIds();
        if (cityIds.size() == 1 && cityIds.get(0).equals("all")) {
            List<CityDO> cityVOS = cityService.findAllCitys();
            cityIds = cityVOS.stream().map(CityDO::getId).collect(Collectors.toList());
        }
        List<String> caliberIds = forecsatRequest.getCaliberIds();
        if (CollectionUtils.isEmpty(cityIds) || CollectionUtils.isEmpty(caliberIds)) {
            baseResp.setRetCode("T100");
            baseResp.setRetMsg("修正算法失败！ 请选择要预测的城市或口径");
        }
        Date startDate = forecsatRequest.getStartDate();
        Date endDate = forecsatRequest.getEndDate();
        for (String cityId : cityIds) {
            for (String caliberId : caliberIds) {
                try {
                    forecastService.doDataRepairAlgorithm(cityId, caliberId, startDate, endDate);
                } catch (Exception e) {
                    continue;
                }
            }
        }
        return baseResp;
    }

    /**
     * 统计综合气象指标
     */
    @ApiOperation("统计综合气象指标")
    @RequestMapping(value = "/statisticsSynthesizeWeather", method = RequestMethod.POST)
    public BaseResp statisticsSynthesizeWeather(@RequestBody ForecsatRequest forecsatRequest) throws Exception {
        List<String> cityIds = forecsatRequest.getCityIds();
        if (cityIds.size() == 1 && cityIds.get(0).equals("all")) {
            List<CityDO> cityVOS = cityService.findAllCitys();
            cityIds = cityVOS.stream().map(CityDO::getId).collect(Collectors.toList());
        }
        Date startDate = forecsatRequest.getStartDate();
        Date endDate = forecsatRequest.getEndDate();
        for (String cityId : cityIds) {
            statisticsSynthesizeWeatherCityDayHisService.doStatisticsSynthesizeWeatherCityDayHis(cityId, startDate,
                    endDate, null);
            statisticsSynthesizeWeatherCityDayFcService
                    .doStatisticsSynthesizeWeatherCityDaFc(cityId, startDate, endDate,
                            null);
        }

        BaseResp baseResp = BaseResp.succResp();
        baseResp.setRetMsg("统计综合气象指标完成....");
        return baseResp;
    }

    /**
     * 统计日气象预测准确率
     */
    @ApiOperation("统计日气象预测准确率")
    @RequestMapping(value = "/statisticsAccuracyWeatherCityDay", method = RequestMethod.POST)
    public BaseResp statisticsAccuracyWeatherCityDayHis(@RequestBody ForecsatRequest forecsatRequest) throws Exception {
        List<String> cityIds = forecsatRequest.getCityIds();
        if (cityIds.size() == 1 && cityIds.get(0).equals("all")) {
            List<CityDO> cityVOS = cityService.findAllCitys();
            cityIds = cityVOS.stream().map(CityDO::getId).collect(Collectors.toList());
        }
        Date startDate = forecsatRequest.getStartDate();
        Date endDate = forecsatRequest.getEndDate();
        for (String cityId : cityIds) {
            try {
                statisticsAccuracyWeatherCityDayHisService
                        .doStatAccuracyWeatherCityDay(cityId, startDate, endDate, null);
            } catch (Exception e) {
                logger.error("统计日气象准确率失败", e);
            }
            try {
                statisticsAccuracyWeatherCityMonthHisService.doStatAccuracyWeatherCityMonth(cityId, startDate, endDate,
                        null);
            } catch (Exception e) {
                logger.error("统计月气象准确率异常...", e);
            }
            try {
                statisticsAccuracyWeatherCityYearHisService
                        .doStatAccuracyWeatherCityYear(cityId, startDate, endDate, null);
            } catch (Exception e) {
                logger.error("统计年气象准确率异常...", e);
            }
        }

        BaseResp baseResp = BaseResp.succResp();
        baseResp.setRetMsg("统计日气象准确率完成");
        return baseResp;
    }

    /**
     * 统计日气象预测准确率- 表statistics_weather_city_day_fc_stat
     */
    @ApiOperation("统计日气象预测准确率")
    @RequestMapping(value = "/doStatisticsWeatherCityDayFcStat", method = RequestMethod.GET)
    public BaseResp doStatisticsWeatherCityDayFcStat(String cityId, Date startDate, Date endDate) {
        try {
            statisticsWeatherCityDayFcStatService.doStatWeatherAccuracy(cityId, startDate, endDate);
        } catch (Exception e) {
            logger.error("统计日气象准确率(doStatisticsWeatherCityDayFcStat)失败", e);
        }
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setRetMsg("统计日气象准确率(doStatisticsWeatherCityDayFcStat)完成");
        return baseResp;
    }

    /**
     * 新息算法调用
     */
    @ApiOperation("新息算法调用")
    @RequestMapping(value = "/doNewAlgorithm", method = RequestMethod.GET)
    public BaseResp doNewAlgorithm(Date startDate, Date endDate) throws Exception {
        List<CityDO> cityVOS = cityService.findAllCitys();
        List<CaliberDO> caliberVOS = caliberService.findAllCalibers();
        logger.info("城市数量为===========" + caliberVOS.size());
        Integer forecastType = Integer.parseInt(environment.getProperty("implement.forecast.type"));
        List<AlgorithmEnum> enums = new ArrayList<>();
        enums.add(AlgorithmEnum.FORECAST_INNOVATION);
        //1 所有城市
        for (CityDO cityVO : cityVOS) {
            if (!cityVO.getId().equals(CityConstants.PROVINCE_ID)) {
                continue;
            }
            //2 所有口径
            for (CaliberDO caliberVO : caliberVOS) {
                logger.info("开始预测：   城市id为=====" + cityVO.getId() + ", 口径id为===========" + caliberVO.getId());
                scheduledThreadPoolExecutor.schedule(new AutoForecastTask(forecastType, getUid(), cityVO.getId(),
                                caliberVO.getId(), autoForecastService, startDate, endDate, enums, null, null, null), 0,
                        TimeUnit.MILLISECONDS);
            }
        }
        logger.info("新息预测算法预测结束");
        BaseResp baseResp = BaseResp.succResp("新息预测算法调用成功，正在后台执行");
        return baseResp;
    }

    /**
     * 节假日算法调用
     */
    @ApiOperation("节假日算法调用")
    @RequestMapping(value = "/doHolidayAlgorithm", method = RequestMethod.POST)
    public BaseResp doHolidayAlgorithm(@RequestBody ForecsatRequest forecsatRequest) throws Exception {
        BaseResp baseResp = new BaseResp();
        List<String> cityIds = forecsatRequest.getCityIds();
        List<String> caliberIds = forecsatRequest.getCaliberIds();
        Date startDate = forecsatRequest.getStartDate();
        Date endDate = forecsatRequest.getEndDate();
        if (CollectionUtils.isEmpty(cityIds) || CollectionUtils.isEmpty(caliberIds)
                || forecsatRequest.getWeatherType() == null || forecsatRequest.getType() == null) {
            baseResp.setRetCode("T100");
            baseResp.setRetMsg("预测算法失败！ 请选择要预测的城市或口径");
        }
        if (cityIds.size() == 1 && cityIds.get(0).equals("all")) {
            List<CityDO> cityVOS = cityService.findAllCitys();
            cityIds = cityVOS.stream().map(CityDO::getId).collect(Collectors.toList());
        }
        if (caliberIds.size() == 1 && caliberIds.get(0).equals("all")) {
            List<CaliberDO> caliberVOS = caliberService.findAllCalibers();
            caliberIds = caliberVOS.stream().map(CaliberDO::getId).collect(Collectors.toList());
        }
        logger.info("节假日算法开始预测");
        String uid = getUid();
        //1 所有城市
        for (String cityId : cityIds) {
            //2 所有口径
            for (String caliberId : caliberIds) {
                //新版节假日禁止使用多线程调用
                autoForecastService.doForecastHoliday(uid, cityId, caliberId, startDate, endDate);
            }
        }
        baseResp = BaseResp.succResp("节假日算法调用成功，正在后台执行");
        return baseResp;
    }

    /**
     * 统计日，周负荷填报准确率
     */
    @ApiOperation("周负荷填报准确率")
    @RequestMapping(value = "/report/accuracy", method = RequestMethod.POST)
    public BaseResp statReportAccuracy(@RequestBody ForecsatRequest forecsatRequest) throws Exception {
        BaseResp baseResp = new BaseResp();
        List<String> cityIds = forecsatRequest.getCityIds();
        Date startDate = forecsatRequest.getStartDate();
        Date endDate = forecsatRequest.getEndDate();
        List<ReportAccuracyDayDO> reportAccuracyDayVOS = new ArrayList<>();
        List<ReportAccuracyWeekDO> reportAccuracyWeekVOList = new ArrayList<>();
        List<ReportAccuracyMonthDO> monthVOList = new ArrayList<>();
        List<CaliberDO> allCalibers = caliberService.findAllCalibers();
        for (CaliberDO allCaliber : allCalibers) {
            if (cityIds.size() == 1 && cityIds.get(0).equals("all")) {
                reportAccuracyDayVOS = reportAccuracyDayService
                        .doStatSaveReportAccuracy(null, startDate, endDate, allCaliber.getId());
//            reportAccuracyWeekVOList = reportAccuracyWeekService.doStatWeekAccuracy(null, startDate, endDate);
//            monthVOList = reportAccuracyMonthService.statReportMonthAccuracy(null, startDate, endDate);
            } else {
                for (String cityId : cityIds) {
                    reportAccuracyDayVOS
                            .addAll(reportAccuracyDayService
                                    .doStatSaveReportAccuracy(cityId, startDate, endDate, allCaliber.getId()));
//                reportAccuracyWeekVOList
//                    .addAll(reportAccuracyWeekService.doStatWeekAccuracy(cityId, startDate, endDate));
//                monthVOList.addAll(reportAccuracyMonthService.statReportMonthAccuracy(cityId, startDate, endDate));
                }
            }
        }
        //统计日负荷填报准确率
        reportAccuracyDayService.doSaveOrUpdate(reportAccuracyDayVOS);
        //统计周负荷填报准确率
//        reportAccuracyWeekService.doSaveOrUpdate(reportAccuracyWeekVOList);
        //统计月准确率
//        reportAccuracyMonthService.doSaveOrUpdate(monthVOList);
        baseResp = BaseResp.succResp("预测准确率统计成功");
        return baseResp;
    }

    /**
     *       统计月准确率  其中包括 月度最大最小，周度月平均最大最小，日度月平均最大最小准确率
     */
    @ApiOperation("统计月准确率")
    @RequestMapping(value = "/month/accuracy", method = RequestMethod.GET)
    public BaseResp statMonthReportAccuracy(Date startDate, Date endDate) throws Exception {
        List<ReportAccuracyMonthDO> monthVOList = reportAccuracyMonthService
                .statReportMonthAccuracy(null, startDate, endDate);
        reportAccuracyMonthService.doSaveOrUpdate(monthVOList);
        return BaseResp.succResp();
    }

    /**
     * 模型融合算法调用 2020/6/12改动 新息算法调用移到实施的页面调用所有算法接口中 这块暂时修改成 调用模型融合算法
     */
    @ApiOperation("模型融合算法调用")
    @RequestMapping(value = "/doModelFusionAlgorithm", method = RequestMethod.POST)
    public BaseResp doModelFusionAlgorithm(@RequestBody ForecsatRequest forecsatRequest) throws Exception {
        BaseResp baseResp = new BaseResp();
        List<String> cityIds = forecsatRequest.getCityIds();
        List<String> caliberIds = forecsatRequest.getCaliberIds();
        Date startDate = forecsatRequest.getStartDate();
        Date endDate = forecsatRequest.getEndDate();
        if (CollectionUtils.isEmpty(cityIds) || CollectionUtils.isEmpty(caliberIds)) {
            baseResp.setRetCode("T100");
            baseResp.setRetMsg("预测算法失败！ 请选择要预测的城市或口径");
        }
        if (cityIds.size() == 1 && cityIds.get(0).equals("all")) {
            List<CityDO> cityVOS = cityService.findAllCitys();
            cityIds = cityVOS.stream().map(CityDO::getId).collect(Collectors.toList());
        }
        if (caliberIds.size() == 1 && caliberIds.get(0).equals("all")) {
            List<CaliberDO> caliberVOS = caliberService.findAllCalibers();
            caliberIds = caliberVOS.stream().map(CaliberDO::getId).collect(Collectors.toList());
        }
        Integer forecastType = Integer.parseInt(environment.getProperty("implement.forecast.type"));
        List<AlgorithmEnum> enums = new ArrayList<>();
        enums.add(AlgorithmEnum.COMPREHENSIVE_MODEL);
        List<Date> listBetweenDay = DateUtil.getListBetweenDay(startDate, endDate);
        //1 所有城市
        for (String cityId : cityIds) {
            if (!cityId.equals(CityConstants.PROVINCE_ID)) {
                continue;
            }
            //2 所有口径
            for (String caliberId : caliberIds) {
                logger.info("开始预测：   城市id为=====" + cityId + ", 口径id为===========" + caliberId);
//                scheduledThreadPoolExecutor.schedule(new AutoForecastTask(forecastType, getUid(), cityId,
//                        caliberId, autoForecastService, startDate, endDate, enums, null, null, null), 0,
//                    TimeUnit.MILLISECONDS);
                for (Date date : listBetweenDay) {
                    autoForecastService.completionModelFusionforecast(cityId, caliberId, date, date,
                            AlgorithmConstants.FC_WAY_T2);
                }
            }
        }
        logger.info(" 模型融合算法预测结束");
        baseResp = BaseResp.succResp(" 模型融合算法调用调用成功，正在后台执行");
        return baseResp;
    }

    /**
     *    功能描述: <br>  计算统计综合准确率   
     *
     * @return  
     * <AUTHOR> 
     * @since 1.0.0     
     */
    @ApiOperation("计算统计综合准确率")
    @RequestMapping(value = "/synthesize/accuracy", method = RequestMethod.POST)
    public BaseResp statSynthesizeReportAccuracy(@RequestBody ForecsatRequest request) throws Exception {
        this.statReportAccuracy(request);
        List<String> cityIds = request.getCityIds();
        if (cityIds != null && cityIds.size() > 0) {
            String city = cityIds.get(0);
            if (city.equals("all")) {
                List<CityDO> cityVOList = cityService.findAllCitys();
                List<String> cityId = cityVOList.stream().map(CityDO::getId).collect(Collectors.toList());
                cityIds.addAll(cityId);
            }
        }
        Date startDate = request.getStartDate();
        Date endDate = request.getEndDate();
        List<Date> dateList = DateUtil.getListBetweenDay(startDate, endDate);
        List<String> stringList = new ArrayList<>();
        for (Date date : dateList) {
            String ym = DateUtil.getMonthByDate(date);
            if (!stringList.contains(ym)) {
                stringList.add(ym);
            }
        }
        for (String ym : stringList) {
            List<ReportAccuracySynthesizeMonthDO> synthesizeMonthVO = reportAccuracySynthesizeMonthService
                    .statSynthesizeMonthAccuracy(cityIds, ym.substring(0, 4), ym.substring(5, 7));
            reportAccuracySynthesizeMonthService.doSaveOrUpdate(synthesizeMonthVO);
        }
        return BaseResp.succResp();
    }

    // 手动计算法  已废除
    @Deprecated
    @ApiOperation("集成模型算法")
    @PostMapping("/doIntegrateModelAlgorithm")
    public void integratedModelAlgorithm(String startDateStr, String endDateStr) throws Exception {
        Date startDate = DateUtils.string2Date(startDateStr, DateFormatType.SIMPLE_DATE_FORMAT_STR);
        Date endDate = DateUtils.string2Date(endDateStr, DateFormatType.SIMPLE_DATE_FORMAT_STR);
        List<CityDO> cityVOS = cityService.findAllCitys();
        List<CityDO> collect = cityVOS.stream().filter(t -> t.getType() == 1).collect(Collectors.toList());
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(collect)) {
            CityDO cityVO = collect.get(0);
            while (startDate.before(DateUtils.addDays(endDate, 1))) {
                loadCityFcService.doIntegratedModelAlgorithmData(cityVO, startDate);
                startDate = DateUtils.addDays(startDate, 1);
            }
        }
    }

    /**
     * 实施的页面调用  补充96&288的超短期预测数据  type  5 分钟间隔 or 15分钟间隔
     */
    @ApiOperation("实施的页面调用超短期预测数据")
    @RequestMapping(value = "/short/algorithm", method = RequestMethod.POST)
    public BaseResp doShortForecast(@RequestBody ForecsatRequest forecsatRequest) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        int timeSpan;
        int startTimePointFlag = 0;
        if (ShortConstants.MINUTE.equals(forecsatRequest.getType())) {
            timeSpan = 5;
            startTimePointFlag = 288;
        } else {
            timeSpan = 15;
            startTimePointFlag = 96;
        }
        List<String> cityIds = forecsatRequest.getCityIds();
        List<String> caliberIds = Arrays.asList("1");
        Date startDate = forecsatRequest.getStartDate();
        Date endDate = forecsatRequest.getEndDate();
        List<Date> dateList = DateUtil.getListBetweenDay(startDate, endDate);
        for (String cityId : cityIds) {
            for (String caliberId : caliberIds) {
                for (Date date : dateList) {
                    for (int startTimePoint = 0; startTimePoint < startTimePointFlag; startTimePoint++) {
                        //超短期预测
                        forecastService
                                .doShortForecast(cityId, caliberId, date, timeSpan, startTimePoint);
                    }
                }
            }
        }
        return baseResp;
    }

    /**
     * 实施的页面调用  统计超短期预测日准确率
     */
    @ApiOperation("实施的页面统计超短期预测日准确率")
    @RequestMapping(value = "/short/fcAccuracy", method = RequestMethod.POST)
    public BaseResp doStatShortFcAccuracy(@RequestBody ForecsatRequest request) throws Exception {
        List<String> cityIds = request.getCityIds();
        Date startDate = request.getStartDate();
        Date endDate = request.getEndDate();
        List<AlgorithmDO> allAlgorithmsNotCache = algorithmService.getAllAlgorithmsNotCache();
        List<AlgorithmDO> pageAlgorithms = allAlgorithmsNotCache.stream().filter(
                        t -> com.tsintergy.lf.core.constants.AlgorithmConstants.SHORT_ALGORITHM_TYPE.equals(t.getType()))
                .collect(Collectors.toList());


        if (cityIds != null && cityIds.size() > 0) {
            String city = cityIds.get(0);
            if (city.equals("all")) {
                for (AlgorithmDO algorithmDO : pageAlgorithms) {
                    loadFeatureStatService.doShortFcLoadFeatureCityDay(null, startDate, endDate, null, algorithmDO.getId());
                }
            } else {
                for (AlgorithmDO algorithmDO : pageAlgorithms) {
                    for (String cityId : cityIds) {
                        loadFeatureStatService.doShortFcLoadFeatureCityDay(cityId, startDate, endDate, null, algorithmDO.getId());
                    }
                }
            }
        }
        return BaseResp.succResp();

    }

    @ApiOperation("统计区域实际负荷")
    @RequestMapping(value = "/statAreaHisLoad", method = RequestMethod.POST)
    public BaseResp statAreaHisLoad(@RequestBody ForecsatRequest request) throws Exception {
        BasePlanDO defaultPlan = basePlanService.getDefaultPlan();
        for (String caliberId : request.getCaliberIds()) {
            String[] split = defaultPlan.getAreaIds().split(Constants.SEPARATOR_PUNCTUATION);
            List<String> areaIds = Arrays.asList(split);
            for (String areaId : areaIds) {
                loadAreaHisService.statAreaHisLoad(caliberId, areaId, request.getStartDate(), request.getEndDate());
            }

        }
        return BaseResp.succResp();
    }

    @ApiOperation("统计区域实际气象")
    @RequestMapping(value = "/statAreaHisWeather", method = RequestMethod.POST)
    public BaseResp statAreaHisWeather(@RequestBody ForecsatRequest request) throws Exception {
        BasePlanDO defaultPlan = basePlanService.getDefaultPlan();
        String[] split = defaultPlan.getAreaIds().split(Constants.SEPARATOR_PUNCTUATION);
        List<String> areaIds = Arrays.asList(split);
        for (String areaId : areaIds) {
            weatherAreaHisService.statAreaHisWeather(areaId, request.getStartDate(), request.getEndDate());
        }
        return BaseResp.succResp();
    }

    @ApiOperation("统计区域预测气象")
    @RequestMapping(value = "/statAreaFcWeather", method = RequestMethod.POST)
    public BaseResp statAreaFcWeather(@RequestBody ForecsatRequest request) throws Exception {
        BasePlanDO defaultPlan = basePlanService.getDefaultPlan();
        String[] split = defaultPlan.getAreaIds().split(Constants.SEPARATOR_PUNCTUATION);
        List<String> areaIds = Arrays.asList(split);
        for (String areaId : areaIds) {
            weatherAreaFcService.statAreaFcWeather(areaId, request.getStartDate(), request.getEndDate());
        }
        return BaseResp.succResp();
    }

    @ApiOperation("实施的页面分区预测")
    @RequestMapping(value = "/doAreaForecast", method = RequestMethod.POST)
    public BaseResp doAreaForecast(@RequestBody ForecsatRequest request) throws Exception {
        List<String> defaultAreaIds = basePlanService.getDefaultAreaIds();
        BasePlanDO defaultPlan = basePlanService.getDefaultPlan();

        //预测方式 1系统预测 2 地市上报
        Integer fcMode = defaultPlan.getFcMode();
        if (fcMode.equals(Constants.FORECAST_MODEL)) {
            for (String caliberId : request.getCaliberIds()) {
                loadAreaFcService.doAreaForecast(request.getStartDate(), request.getEndDate(), caliberId);
            }
        }
        //系统预测
        else {
            for (String caliberId : request.getCaliberIds()) {
                for (String areaId : defaultAreaIds) {
                    areaForecastAlgorithmService.doStatAreaForecast(areaId, caliberId
                            , request.getPointNum(), request.getAlgorithmIds()
                            , request.getType(), request.getWeatherType() + 1
                            , request.getStartDate(), request.getEndDate()
                            , AlgorithmConstants.FC_TYPE_COMPLEMENT
                    );
                }
            }
        }
        return BaseResp.succResp();
    }


//    @GetMapping("/importCityHisLoad")
//    public BaseResp importCityHisLoad() throws Exception {
//
//        String path = "D:\\hubei\\地区实时值导出\\宜昌地区2019年-2022年8月.xlsx";
//
//        Map<String,Map<String,BigDecimal>> dateValueMap1 = new HashMap<>();
//        Map<String,Map<String,BigDecimal>> dateValueMap2 = new HashMap<>();
//        Map<String,Map<String,BigDecimal>> dateValueMap3 = new HashMap<>();
//        Map<String,Map<String,BigDecimal>> dateValueMap4 = new HashMap<>();
//        Map<String,Map<String,BigDecimal>> dateValueMap5 = new HashMap<>();
//        Map<String,Map<String,BigDecimal>> dateValueMap6 = new HashMap<>();
//        Map<String,Map<String,BigDecimal>> dateValueMap7 = new HashMap<>();
//
//
//
//        ExcelUtil.readBySax(new File(path), 0, new RowHandler() {
//
//            @SneakyThrows
//            @Override
//            public void handle(int sheetIndex, int rowIndex, List<Object> rowList) {
//                if (rowIndex==0){
//                    return;
//                }
//                //2019-01-03 00:00:00
//                Date dateTime = (Date) rowList.get(0);
//                String dateTimeStr = DateUtil.getDateToStrFORMAT(dateTime, "yyyy-MM-dd HH:mm:dd");
//                String[] split = dateTimeStr.split(" ");
//                String dateStr = split[0];
//                String timeStr = split[1];
//                //00:00:00
//                String[] split1 = timeStr.split(":");
//                String filedName = "t"+split1[0]+split1[1];
//
//                Map<String, BigDecimal> valueMap1 = dateValueMap1.get(dateStr);
//                if(valueMap1 ==null){
//                    valueMap1 = new HashMap<>();
//                    dateValueMap1.put(dateStr,valueMap1);
//                }
//
//                Map<String, BigDecimal> valueMap2 = dateValueMap2.get(dateStr);
//                if(valueMap2 ==null){
//                    valueMap2 = new HashMap<>();
//                    dateValueMap2.put(dateStr,valueMap2);
//                }
//                Map<String, BigDecimal> valueMap3 = dateValueMap3.get(dateStr);
//                if(valueMap3 ==null){
//                    valueMap3 = new HashMap<>();
//                    dateValueMap3.put(dateStr,valueMap3);
//                }
//                Map<String, BigDecimal> valueMap4 = dateValueMap4.get(dateStr);
//                if(valueMap4 ==null){
//                    valueMap4 = new HashMap<>();
//                    dateValueMap4.put(dateStr,valueMap4);
//                }
//                Map<String, BigDecimal> valueMap5 = dateValueMap5.get(dateStr);
//                if(valueMap5 ==null){
//                    valueMap5 = new HashMap<>();
//                    dateValueMap5.put(dateStr,valueMap5);
//                }
//                Map<String, BigDecimal> valueMap6 = dateValueMap6.get(dateStr);
//                if(valueMap6 ==null){
//                    valueMap6 = new HashMap<>();
//                    dateValueMap6.put(dateStr,valueMap6);
//                }
//                Map<String, BigDecimal> valueMap7 = dateValueMap7.get(dateStr);
//                if(valueMap7 ==null){
//                    valueMap7 = new HashMap<>();
//                    dateValueMap7.put(dateStr,valueMap7);
//                }
//                try {
//                    Object value1 = rowList.get(1);
//                    valueMap1.put(filedName,value1!=null?new BigDecimal(value1.toString()):null);
//                    dateValueMap1.put(dateStr,valueMap1);
//                }catch (Exception e){
//                    valueMap1.put(filedName,null);
//                    dateValueMap1.put(dateStr,valueMap1);
//                }
//
//                try {
//                    Object value2 = rowList.get(2);
//                    valueMap2.put(filedName,value2!=null?new BigDecimal(value2.toString()):null);
//                    dateValueMap2.put(dateStr,valueMap2);
//                }catch (Exception e){
//                    valueMap2.put(filedName,null);
//                    dateValueMap2.put(dateStr,valueMap2);
//                }
//
//                try {
//                    Object value3 = rowList.get(3);
//                    valueMap3.put(filedName,value3!=null?new BigDecimal(value3.toString()):null);
//                    dateValueMap3.put(dateStr,valueMap3);
//                }catch (Exception e){
//                    valueMap3.put(filedName,null);
//                    dateValueMap3.put(dateStr,valueMap3);
//                }
//
//                try {
//                    Object value4 = rowList.get(4);
//                    valueMap4.put(filedName,value4!=null?new BigDecimal(value4.toString()):null);
//                    dateValueMap4.put(dateStr,valueMap4);
//                }catch (Exception e){
//                    valueMap4.put(filedName,null);
//                    dateValueMap4.put(dateStr,valueMap4);
//                }
//
//                try {
//                    Object value5 = rowList.get(5);
//                    valueMap5.put(filedName,value5!=null?new BigDecimal(value5.toString()):null);
//                    dateValueMap5.put(dateStr,valueMap5);
//                }catch (Exception e){
//                    valueMap5.put(filedName,null);
//                    dateValueMap5.put(dateStr,valueMap5);
//                }
//
//                try {
//                    Object value6 = rowList.get(6);
//                    valueMap6.put(filedName,value6!=null?new BigDecimal(value6.toString()):null);
//                    dateValueMap6.put(dateStr,valueMap6);
//                }catch (Exception e){
//                    valueMap6.put(filedName,null);
//                    dateValueMap6.put(dateStr,valueMap6);
//                }
//
//                try {
//                    Object value7 = rowList.get(7);
//                    valueMap7.put(filedName,value7!=null?new BigDecimal(value7.toString()):null);
//                    dateValueMap7.put(dateStr,valueMap7);
//                }catch (Exception e){
//                    valueMap7.put(filedName,null);
//                    dateValueMap7.put(dateStr,valueMap7);
//                }
//            }
//        });
//
//        //6	小水
//        //7	小火
//        //8	光伏
//        //9	风电
//
//        loadCityHisClctService.doInsertOrUpdates(getValueList("2", dateValueMap1));
//        loadCityHisClctService.doInsertOrUpdates(getValueList("1", dateValueMap2));
//        loadCityHisClctService.doInsertOrUpdates(getValueList("6", dateValueMap3));
//        loadCityHisClctService.doInsertOrUpdates(getValueList("7", dateValueMap4));
//        loadCityHisClctService.doInsertOrUpdates(getValueList("9", dateValueMap5));
//        loadCityHisClctService.doInsertOrUpdates(getValueList("8", dateValueMap6));
//        loadCityHisClctService.doInsertOrUpdates(getValueList("3", dateValueMap7));
//
//
//
//        return BaseResp.succResp();
//    }
//
//    private List<LoadCityHisClctDO> getValueList(String caliberId,Map<String,Map<String,BigDecimal>> dateValueMap)
//        throws Exception {
//        List<LoadCityHisClctDO> result = new ArrayList<>();
//        for (Entry<String, Map<String, BigDecimal>> stringMapEntry : dateValueMap.entrySet()) {
//            String dateStr = stringMapEntry.getKey();
//            Map<String, BigDecimal> valueMap = stringMapEntry.getValue();
//            LoadCityHisClctDO loadCityHisClctDO = new LoadCityHisClctDO();
//            Date today = DateUtil.getDate(dateStr, "yyyy-MM-dd");
//            Date tomorrow = DateUtil.getMoveDay(today, 1);
//            loadCityHisClctDO.setDate(new java.sql.Date(today.getTime()));
//            loadCityHisClctDO.setCityId("7");
//            loadCityHisClctDO.setCaliberId(caliberId);
//            BasePeriodUtils.setAllFiled(loadCityHisClctDO,valueMap);
//            Map<String, BigDecimal> tomorrowMap = dateValueMap.get(DateUtil.formateDate(tomorrow));
//            if (tomorrowMap!=null){
//                BigDecimal t0000 = tomorrowMap.get("t0000");
//                if (t0000!=null){
//                    loadCityHisClctDO.setT2400(t0000);
//                }
//            }
//            result.add(loadCityHisClctDO);
//        }
//        return result;
//    }


//    @GetMapping("/importCityHisLoad")
//    public BaseResp importCityHisLoad() throws Exception {
//
//        String path = "D:\\hubei\\地区实时值导出\\宜昌地区2019年-2022年8月.xlsx";
//
//        Map<String,Map<String,BigDecimal>> dateValueMap1 = new HashMap<>();
//        Map<String,Map<String,BigDecimal>> dateValueMap2 = new HashMap<>();
//        Map<String,Map<String,BigDecimal>> dateValueMap3 = new HashMap<>();
//        Map<String,Map<String,BigDecimal>> dateValueMap4 = new HashMap<>();
//        Map<String,Map<String,BigDecimal>> dateValueMap5 = new HashMap<>();
//        Map<String,Map<String,BigDecimal>> dateValueMap6 = new HashMap<>();
//        Map<String,Map<String,BigDecimal>> dateValueMap7 = new HashMap<>();
//
//
//
//        ExcelUtil.readBySax(new File(path), 0, new RowHandler() {
//
//            @SneakyThrows
//            @Override
//            public void handle(int sheetIndex, int rowIndex, List<Object> rowList) {
//                if (rowIndex==0){
//                    return;
//                }
//                //2019-01-03 00:00:00
//                Date dateTime = (Date) rowList.get(0);
//                String dateTimeStr = DateUtil.getDateToStrFORMAT(dateTime, "yyyy-MM-dd HH:mm:dd");
//                String[] split = dateTimeStr.split(" ");
//                String dateStr = split[0];
//                String timeStr = split[1];
//                //00:00:00
//                String[] split1 = timeStr.split(":");
//                String filedName = "t"+split1[0]+split1[1];
//
//                Map<String, BigDecimal> valueMap1 = dateValueMap1.get(dateStr);
//                if(valueMap1 ==null){
//                    valueMap1 = new HashMap<>();
//                    dateValueMap1.put(dateStr,valueMap1);
//                }
//
//                Map<String, BigDecimal> valueMap2 = dateValueMap2.get(dateStr);
//                if(valueMap2 ==null){
//                    valueMap2 = new HashMap<>();
//                    dateValueMap2.put(dateStr,valueMap2);
//                }
//                Map<String, BigDecimal> valueMap3 = dateValueMap3.get(dateStr);
//                if(valueMap3 ==null){
//                    valueMap3 = new HashMap<>();
//                    dateValueMap3.put(dateStr,valueMap3);
//                }
//                Map<String, BigDecimal> valueMap4 = dateValueMap4.get(dateStr);
//                if(valueMap4 ==null){
//                    valueMap4 = new HashMap<>();
//                    dateValueMap4.put(dateStr,valueMap4);
//                }
//                Map<String, BigDecimal> valueMap5 = dateValueMap5.get(dateStr);
//                if(valueMap5 ==null){
//                    valueMap5 = new HashMap<>();
//                    dateValueMap5.put(dateStr,valueMap5);
//                }
//                Map<String, BigDecimal> valueMap6 = dateValueMap6.get(dateStr);
//                if(valueMap6 ==null){
//                    valueMap6 = new HashMap<>();
//                    dateValueMap6.put(dateStr,valueMap6);
//                }
//                Map<String, BigDecimal> valueMap7 = dateValueMap7.get(dateStr);
//                if(valueMap7 ==null){
//                    valueMap7 = new HashMap<>();
//                    dateValueMap7.put(dateStr,valueMap7);
//                }
//                try {
//                    Object value1 = rowList.get(1);
//                    valueMap1.put(filedName,value1!=null?new BigDecimal(value1.toString()):null);
//                    dateValueMap1.put(dateStr,valueMap1);
//                }catch (Exception e){
//                    valueMap1.put(filedName,null);
//                    dateValueMap1.put(dateStr,valueMap1);
//                }
//
//                try {
//                    Object value2 = rowList.get(2);
//                    valueMap2.put(filedName,value2!=null?new BigDecimal(value2.toString()):null);
//                    dateValueMap2.put(dateStr,valueMap2);
//                }catch (Exception e){
//                    valueMap2.put(filedName,null);
//                    dateValueMap2.put(dateStr,valueMap2);
//                }
//
//                try {
//                    Object value3 = rowList.get(3);
//                    valueMap3.put(filedName,value3!=null?new BigDecimal(value3.toString()):null);
//                    dateValueMap3.put(dateStr,valueMap3);
//                }catch (Exception e){
//                    valueMap3.put(filedName,null);
//                    dateValueMap3.put(dateStr,valueMap3);
//                }
//
//                try {
//                    Object value4 = rowList.get(4);
//                    valueMap4.put(filedName,value4!=null?new BigDecimal(value4.toString()):null);
//                    dateValueMap4.put(dateStr,valueMap4);
//                }catch (Exception e){
//                    valueMap4.put(filedName,null);
//                    dateValueMap4.put(dateStr,valueMap4);
//                }
//
//                try {
//                    Object value5 = rowList.get(5);
//                    valueMap5.put(filedName,value5!=null?new BigDecimal(value5.toString()):null);
//                    dateValueMap5.put(dateStr,valueMap5);
//                }catch (Exception e){
//                    valueMap5.put(filedName,null);
//                    dateValueMap5.put(dateStr,valueMap5);
//                }
//
//                try {
//                    Object value6 = rowList.get(6);
//                    valueMap6.put(filedName,value6!=null?new BigDecimal(value6.toString()):null);
//                    dateValueMap6.put(dateStr,valueMap6);
//                }catch (Exception e){
//                    valueMap6.put(filedName,null);
//                    dateValueMap6.put(dateStr,valueMap6);
//                }
//
//                try {
//                    Object value7 = rowList.get(7);
//                    valueMap7.put(filedName,value7!=null?new BigDecimal(value7.toString()):null);
//                    dateValueMap7.put(dateStr,valueMap7);
//                }catch (Exception e){
//                    valueMap7.put(filedName,null);
//                    dateValueMap7.put(dateStr,valueMap7);
//                }
//            }
//        });
//
//        //6	小水
//        //7	小火
//        //8	光伏
//        //9	风电
//
//        loadCityHisClctService.doInsertOrUpdates(getValueList("2", dateValueMap1));
//        loadCityHisClctService.doInsertOrUpdates(getValueList("1", dateValueMap2));
//        loadCityHisClctService.doInsertOrUpdates(getValueList("6", dateValueMap3));
//        loadCityHisClctService.doInsertOrUpdates(getValueList("7", dateValueMap4));
//        loadCityHisClctService.doInsertOrUpdates(getValueList("9", dateValueMap5));
//        loadCityHisClctService.doInsertOrUpdates(getValueList("8", dateValueMap6));
//        loadCityHisClctService.doInsertOrUpdates(getValueList("3", dateValueMap7));
//
//
//
//        return BaseResp.succResp();
//    }
//
//    private List<LoadCityHisClctDO> getValueList(String caliberId,Map<String,Map<String,BigDecimal>> dateValueMap)
//        throws Exception {
//        List<LoadCityHisClctDO> result = new ArrayList<>();
//        for (Entry<String, Map<String, BigDecimal>> stringMapEntry : dateValueMap.entrySet()) {
//            String dateStr = stringMapEntry.getKey();
//            Map<String, BigDecimal> valueMap = stringMapEntry.getValue();
//            LoadCityHisClctDO loadCityHisClctDO = new LoadCityHisClctDO();
//            Date today = DateUtil.getDate(dateStr, "yyyy-MM-dd");
//            Date tomorrow = DateUtil.getMoveDay(today, 1);
//            loadCityHisClctDO.setDate(new java.sql.Date(today.getTime()));
//            loadCityHisClctDO.setCityId("7");
//            loadCityHisClctDO.setCaliberId(caliberId);
//            BasePeriodUtils.setAllFiled(loadCityHisClctDO,valueMap);
//            Map<String, BigDecimal> tomorrowMap = dateValueMap.get(DateUtil.formateDate(tomorrow));
//            if (tomorrowMap!=null){
//                BigDecimal t0000 = tomorrowMap.get("t0000");
//                if (t0000!=null){
//                    loadCityHisClctDO.setT2400(t0000);
//                }
//            }
//            result.add(loadCityHisClctDO);
//        }
//        return result;
//    }

    @ApiOperation("实施的页面统计分区预测总负荷")
    @RequestMapping(value = "/statAllAreaFcLoad", method = RequestMethod.POST)
    public BaseResp statAllAreaFcLoad(@RequestBody ForecsatRequest request) throws Exception {
        List<String> defaultAreaIds = basePlanService.getDefaultAreaIds();
        List<NetLossCoefficientDTO> netLossCoefficientDTO = settingSystemService.getNetLossCoefficientDTO();
        List<String> caliberIds = request.getCaliberIds();
        for (int i = 0; i < netLossCoefficientDTO.size(); i++) {
            if (caliberIds.contains(netLossCoefficientDTO.get(i).getCaliberId())) {
                loadAreaFcService
                        .doStatAllAreaFcLoad(request.getStartDate(), request.getEndDate(), defaultAreaIds,
                                netLossCoefficientDTO.get(i).getCaliberId(),
                                netLossCoefficientDTO.get(i).getNetLossCoefficient());
            }

        }

        return BaseResp.succResp();
    }

    @ApiOperation("实施页面统计预测回溯准确率")
    @RequestMapping(value = "/statRecallAccuracy", method = RequestMethod.POST)
    public BaseResp statRecallAccuracy(@RequestBody ForecsatRequest request) throws Exception {
        List<String> cityIds = request.getCityIds();
        List<String> caliberIds = request.getCaliberIds();
        Date startDate = request.getStartDate();
        Date endDate = request.getEndDate();
        for (String cityId : cityIds) {
            for (String caliberId : caliberIds) {
                forecastResultStatService.statForecastRecallResult(cityId, null, caliberId, startDate, endDate);
            }
        }

        return BaseResp.succResp();
    }

    @ApiOperation("将地市调度负荷按净负荷保存一份")
    @RequestMapping(value = "/saveCityLoadByDiaodu", method = RequestMethod.POST)
    public BaseResp saveCityLoadByDiaodu() throws Exception {
        Date startDate = DateUtil.getDate("2022-01-01", "yyyy-MM-dd");
        Date endDate = DateUtil.getDate("2022-09-30", "yyyy-MM-dd");

        List<LoadCityFcDO> loadCityHisDOS = loadCityFcService.findFcByAlgorithmId(null, "2", "0", startDate, endDate);
        List<LoadCityFcDO> result = new ArrayList<>();
        for (LoadCityFcDO loadCityFcDO : loadCityHisDOS) {
            if (loadCityFcDO.getCityId().equals("1")) {
                continue;
            }
            LoadCityFcDO newDo = new LoadCityFcDO();
            BeanUtils.copyProperties(loadCityFcDO, newDo);
            newDo.setCaliberId("4");
            newDo.setAlgorithmId("0");
            result.add(newDo);
        }
        loadCityFcService.doSaveBatch(result);
        return BaseResp.succResp();
    }

    @GetMapping("/importHisLoad")
    public BaseResp importHisLoad() throws Exception {
        String path = "C:\\Users\\<USER>\\Desktop\\湖北各地市负荷20-21.xlsx";

        Map<String, List<CityDO>> collect = cityService.findAllCitys().stream()
                .collect(Collectors.groupingBy(t -> t.getCity()));
        Map<String, List<CaliberDO>> caliberNameCollect = caliberService.findAllCalibers().stream()
                .collect(Collectors.groupingBy(CaliberDO::getDescription));
        List<LoadCityHisDO> loadCityHisDOS = new ArrayList<>();
        ExcelUtil.readBySax(new File(path), 0, new RowHandler() {
            @SneakyThrows
            @Override
            public void handle(int sheetIndex, int rowIndex, List<Object> rowList) {
                String cityName = (String) rowList.get(0);
                List<CityDO> cityDOS = collect.get(cityName);
                String cityId = "1";
                if (!CollectionUtils.isEmpty(cityDOS)) {
                    cityId = cityDOS.get(0).getId();
                    String caliberName = (String) rowList.get(2);
                    String caliberId = "2";
                    List<CaliberDO> caliberDOS = caliberNameCollect.get(caliberName);
                    if (!CollectionUtils.isEmpty(caliberDOS)) {
                        caliberId = caliberDOS.get(0).getId();
                    }
                    Date date = DateUtils
                            .string2Date(rowList.get(1).toString(), DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);

                    rowList.remove(0);
                    rowList.remove(0);
                    rowList.remove(0);
                    List<BigDecimal> collect = rowList.stream()
                            .map(t -> t.equals("NULL") ? null : new BigDecimal(t.toString()))
                            .collect(Collectors.toList());
                    Map<String, BigDecimal> map = ColumnUtil
                            .listToMap(collect, Constants.WEATHER_CURVE_START_WITH_ZERO);
                    JSONObject jsonObject = JSONUtil.parseFromMap(map);
                    LoadCityHisDO loadCityHisDO = JSONUtil.toBean(jsonObject, LoadCityHisDO.class);
                    loadCityHisDO.setDate(new java.sql.Date(date.getTime()));
                    loadCityHisDO.setCityId(cityId);
                    loadCityHisDO.setCaliberId(caliberId);
                    loadCityHisDOS.add(loadCityHisDO);
                }
            }
        });

        ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat("thread-txtWeatherResolver").build();
        ThreadPoolExecutor pool = new ThreadPoolExecutor(5, 512, 0L,
                TimeUnit.MILLISECONDS,
                new LinkedBlockingDeque<Runnable>(1024),
                threadFactory, new ThreadPoolExecutor.CallerRunsPolicy());
        List<LoadCityHisDO> values = loadCityHisDOS.stream().collect(Collectors.toList());
        int i = values.size() / 512;
        i = ++i;
        List<List<LoadCityHisDO>> lists = new ArrayList<>();
        for (int j = 0; j < values.size(); j = j + i) {
            int z = j + i > values.size() ? values.size() : j + i;
            List<LoadCityHisDO> weatherStationHisBasicDOS = values.subList(j, z);
            lists.add(weatherStationHisBasicDOS);
        }

        for (List<LoadCityHisDO> value : lists) {
            //System.out.println("***************************************" + value.getDate());
            pool.execute(new Runnable() {
                @SneakyThrows
                @Override
                public void run() {
                    for (LoadCityHisDO loadCityHisDO : value) {
                        loadCityHisService.doInsertOrUpdate(loadCityHisDO);
                    }
                }
            });
        }
        return BaseResp.succResp();
    }

    @GetMapping("/importHisWeather")
    public BaseResp importHisWeather() throws Exception {
        logger.info("开始解析excel....,时间：{}", DateUtils.date2String(new Date(), DateFormatType.DATE_FORMAT_STR));
        List<String> pathList = new ArrayList<String>() {
            {
//                add("D:\\hubei\\2020.xlsx");
                add("/home/<USER>/lf/sys/dev/hubei/hisWeather/2021.xlsx");
                add("/home/<USER>/lf/sys/dev/hubei/hisWeather/2022.xlsx");
                add("/home/<USER>/lf/sys/dev/hubei/hisWeather/2019.xlsx");
                add("/home/<USER>/lf/sys/dev/hubei/hisWeather/2018.xlsx");
            }
        };
        Map<String, String> cityMap = weatherStationService.getWeatherStationInfo(null, null)
                .stream().collect(Collectors.toMap(
                        WeatherStationInfoDO::getStationId, WeatherStationInfoDO::getCityId));
        for (String path : pathList) {
            EasyExcel.read(path, WeatherStationImportDO.class,
                    new WeatherStationListener(weatherStationHisBasicService, cityMap)).sheet(0).doRead();
        }
        return BaseResp.succResp();
    }


    @GetMapping("/mergeCityWeatherHis")
    public BaseResp mergeCityWeatherHis(String start, String end) throws Exception {
        Set<String> dateSet = new HashSet<>();

        if (org.apache.commons.lang3.StringUtils.isBlank(start) && org.apache.commons.lang3.StringUtils.isBlank(end)) {
            start = "2018-01-01";
            end = "2018-12-31";
            logger.info("开始统计城市气象，开始时间：" + start + ",结束时间：" + end);
            List<String> listBetweenDay = DateUtil.getListBetweenDay(start, end);
            dateSet.addAll(listBetweenDay);
            wrapperCityWeather(dateSet);

            start = "2019-01-01";
            end = "2019-12-31";
            logger.info("开始统计城市气象，开始时间：" + start + ",结束时间：" + end);
            listBetweenDay = DateUtil.getListBetweenDay(start, end);
            dateSet.clear();
            dateSet.addAll(listBetweenDay);
            wrapperCityWeather(dateSet);

            start = "2020-01-01";
            end = "2020-12-31";
            logger.info("开始统计城市气象，开始时间：" + start + ",结束时间：" + end);
            listBetweenDay = DateUtil.getListBetweenDay(start, end);
            dateSet.clear();
            dateSet.addAll(listBetweenDay);
            wrapperCityWeather(dateSet);
        } else {
            List<String> listBetweenDay = DateUtil.getListBetweenDay(start, end);
            dateSet.addAll(listBetweenDay);
            wrapperCityWeather(dateSet);
        }
        return BaseResp.succResp();
    }

    @GetMapping("/test")
    public BaseResp test() throws Exception {
        BufferedReader br;
        String line = "";
        Map<String, List<CityDO>> collect = cityService.findAllCitys().stream()
                .collect(Collectors.groupingBy(t -> t.getCity()));
        Map<String, List<CaliberDO>> caliberMap = caliberService.findAllCalibers().stream()
                .collect(Collectors.groupingBy(t -> t.getDescription()));

        try {
            br = new BufferedReader(
                    new InputStreamReader(new FileInputStream("C:\\Users\\<USER>\\Desktop\\地市预测值.txt"), "GBK"));
            int count = 0;
            Map<String, LoadCityFcDO> todayMap = new HashMap<>();
            while ((line = br.readLine()) != null) {
                count++;
                if (count == 1) {
                    continue;
                }
                //安徽系统负荷调度口径,26/10/2021,26370.52148,26157.26563,26102.75391
                String[] split = line.split(",");
//                20180101
                String dateKey = split[1].replaceAll("\"", "");
                String cityName = split[0].replaceAll("\"", "");
                String caliberName = split[2].replaceAll("\"", "");
                List<CityDO> cityDOS = collect.get(cityName);
                if (CollectionUtils.isEmpty(cityDOS)) {
                    continue;
                }

                List<CaliberDO> caliberDOS = caliberMap.get(caliberName);
                if (CollectionUtils.isEmpty(caliberDOS)) {
                    continue;
                }
                Date date = DateUtil.getDate(dateKey, "yyyyMMdd");
                String cityId = cityDOS.get(0).getId();
                String caliberId = caliberDOS.get(0).getId();
                LoadCityFcDO todayLoadCityFcDO = todayMap.get(dateKey + "-" + cityId + "-" + caliberId);
                if (todayLoadCityFcDO == null) {
                    todayLoadCityFcDO = new LoadCityFcDO();
                    todayLoadCityFcDO.setDate(new java.sql.Date(date.getTime()));
                    todayLoadCityFcDO.setCaliberId(caliberId);
                    todayLoadCityFcDO.setCityId(cityId);
                    todayLoadCityFcDO.setAlgorithmId(
                            com.tsintergy.lf.core.constants.AlgorithmConstants.MD_ALGORITHM_ID);
                    todayMap.put(dateKey + "-" + cityId + "-" + caliberId, todayLoadCityFcDO);
                }
                String[] valuesArr = ArrayUtils.subarray(split, 3, split.length);
                List<BigDecimal> valueList = new ArrayList<>();

                for (String s : valuesArr) {
                    valueList.add(s.replaceAll("\"", "").equals("NULL") || StringUtils.isEmpty(s) ? null
                            : new BigDecimal(s.replaceAll("\"", "")));
                }
                Map<String, BigDecimal> stringBigDecimalMap = ColumnUtil.listToMap(valueList, false);
                BasePeriodUtils.setAllFiled(todayLoadCityFcDO, stringBigDecimalMap);
            }


            List<LoadCityFcDO> values = new ArrayList<>();
            for (Entry<String, LoadCityFcDO> entry : todayMap.entrySet()) {
                String key = entry.getKey();
                LoadCityFcDO todayValue = entry.getValue();
                String[] split = key.split("-");
                String dateStr = split[0];
                String cityId = split[1];
                String caliberId = split[2];
                Date today = DateUtil.getDate(dateStr, "yyyyMMdd");
                Date yesterday = DateUtils.addDays(today, -1);
                String yesterdayToStrFORMAT = DateUtil.getDateToStrFORMAT(yesterday, "yyyyMMdd");
                LoadCityFcDO yesterdayLoadCityHisDO = todayMap.get(
                        yesterdayToStrFORMAT + "-" + cityId + "-" + caliberId);
                if (yesterdayLoadCityHisDO == null) {
                    LoadCityFcDO LoadCityFcDO = loadCityFcService
                            .getLoadCityFcDO(yesterday, cityId, caliberId,
                                    com.tsintergy.lf.core.constants.AlgorithmConstants.MD_ALGORITHM_ID);
                    if (LoadCityFcDO != null) {
                        todayValue.setT0000(LoadCityFcDO.getT2400());
                    }
                } else {
                    if (yesterdayLoadCityHisDO.getT2400() != null) {
                        todayValue.setT0000(yesterdayLoadCityHisDO.getT2400());
                    }
                }
                values.add(todayValue);
            }
            for (LoadCityFcDO value : values) {
                scheduledThreadPoolExecutor.execute(new Runnable() {
                    @SneakyThrows
                    @Override
                    public void run() {
                        loadCityFcService.doSaveOrUpdateLoadCityFcDO96(value);
                    }
                });
            }


        } catch (Exception e) {
            e.printStackTrace();
        }
        return BaseResp.succResp();
    }

    /**
     * 功能描述: 处理2300-2400补点错误，执行前需要将2315、2330、2345、2400置空<br>
     *
     * @Return: {@link BaseResp}
     * @Version: 1.0.0
     * @Author:<EMAIL>
     * @Date: 2022/6/27 16:06
     */

    @GetMapping("/mergeStationWeatherHis")
    public BaseResp mergeStationWeatherHis(Date start, Date end) throws Exception {

        List<WeatherStationHisBasicDO> weatherStationHisBasicDOList = weatherStationHisBasicService
                .getWeatherStationHisBasicDO(null, null, start, end, null);
        if (!CollectionUtils.isEmpty(weatherStationHisBasicDOList)) {
            //将数据放入map
            Map<String, WeatherStationHisBasicDO> weatherStationHisBasicDOMap = new HashMap<>();
            for (WeatherStationHisBasicDO weatherStationHisBasicDO : weatherStationHisBasicDOList) {
                String key = getStringMapKey(weatherStationHisBasicDO);
                if (weatherStationHisBasicDOMap.get(key) == null) {
                    weatherStationHisBasicDOMap.put(key, weatherStationHisBasicDO);
                }
            }

            //补全2400点
            for (WeatherStationHisBasicDO weatherStationHisBasicDO : weatherStationHisBasicDOList) {
                String stationId = weatherStationHisBasicDO.getStationId();
                Integer type = weatherStationHisBasicDO.getType();
                Date tomorrow = DateUtils.addDays(weatherStationHisBasicDO.getDate(), 1);
                String dateFormate = DateUtils.date2String(tomorrow, DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);
                String key = stationId + "-" + dateFormate + "-" + type;

                //如果第二天的负荷在map中找不到，则将查询第二天的负荷并放入到map中
                WeatherStationHisBasicDO tomorrowWeatherStationHisBasicDO = weatherStationHisBasicDOMap.get(key);


                if (tomorrowWeatherStationHisBasicDO == null) {
                    boolean contain = false;
                    for (String keyString : weatherStationHisBasicDOMap.keySet()) {
                        if (keyString.contains(dateFormate)) {
                            contain = true;
                            break;
                        }
                    }
                    if (!contain) {
                        List<WeatherStationHisBasicDO> tomorrowDOS = weatherStationHisBasicService
                                .getWeatherStationHisBasicDO(null, null, tomorrow, tomorrow, null);
                        if (!CollectionUtils.isEmpty(tomorrowDOS)) {
                            for (WeatherStationHisBasicDO tomorrowDO : tomorrowDOS) {
                                String mapKey = getStringMapKey(tomorrowDO);
                                weatherStationHisBasicDOMap.put(mapKey, tomorrowDO);
                            }
                        }
                    }
                }

                tomorrowWeatherStationHisBasicDO = weatherStationHisBasicDOMap.get(key);
                if (tomorrowWeatherStationHisBasicDO != null) {
                    weatherStationHisBasicDO.setT2400(tomorrowWeatherStationHisBasicDO.getT0000());
                }
            }


            //批量插入
            List<WeatherStationHisBasicDO> values = weatherStationHisBasicDOList;
            List<WeatherStationHisBasicDO> updateD0List = new ArrayList<>();
            int size = 100;
            int loop = 1;
            for (int i = 0; i < values.size(); i++) {
                try {
                    PeriodDataUtil.do24To96VO(values.get(i));
                    updateD0List.add(values.get(i));
                    if (i >= loop * size - 1) {
                        logger.info("第{}次批量入库开始....,时间：{}", (i + 1) / 100,
                                DateUtils.date2String(new java.util.Date(), DateFormatType.DATE_FORMAT_STR));
                        weatherStationHisBasicService.doSaveOrUpdateBatch(updateD0List);
                        logger.info("第{}次批量入库完成....,时间：{}", (i + 1) / 100,
                                DateUtils.date2String(new java.util.Date(), DateFormatType.DATE_FORMAT_STR));
                        updateD0List.clear();
                        loop = loop + 1;
                    }
                } catch (Exception e) {
                    logger.error("批量保存历史气象异常", e);
                }
            }
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(updateD0List)) {
                weatherStationHisBasicService.doSaveOrUpdateBatch(updateD0List);
            }
            logger.info("批量入库完成....,时间：{}",
                    DateUtils.date2String(new java.util.Date(), DateFormatType.DATE_FORMAT_STR));
        }

        return BaseResp.succResp();
    }

    private String getStringMapKey(WeatherStationHisBasicDO weatherStationHisBasicDO) {
        String stationId = weatherStationHisBasicDO.getStationId();
        Integer type = weatherStationHisBasicDO.getType();
        String dateFormate = DateUtils.date2String(weatherStationHisBasicDO.getDate(),
                DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);
        return stationId + "-" + dateFormate + "-" + type;
    }

    public void wrapperCityWeather(Set<String> dates) throws Exception {
        logger.info("开始计算平均值。。。");
        //计算平均值
        Map<String, WeatherCityHisDO> weatherMap = calculateAvg(dates);

        logger.info("开始补全2400点。。。");
        //补全2400点
        supplement2400(weatherMap);

        logger.info("开始批量存储。。。");
        // 批量存储
        saveBatch(weatherMap.values().stream().collect(Collectors.toList()));

    }

    private void saveBatch(List<WeatherCityHisDO> values) {
        List<WeatherCityHisDO> updateD0List = new ArrayList<>();
        int size = 100;
        int loop = 1;
        for (int i = 0; i < values.size(); i++) {
            try {
                PeriodDataUtil.do24To96VO(values.get(i));
                updateD0List.add(values.get(i));
                if (i >= loop * size - 1) {
                    logger.info("第{}次批量入库开始....,时间：{}", (i + 1) / 100,
                            DateUtils.date2String(new Date(), DateFormatType.DATE_FORMAT_STR));
                    weatherCityHisService.doSaveOrUpdateBatch(updateD0List);
                    logger.info("第{}次批量入库完成....,时间：{}", (i + 1) / 100,
                            DateUtils.date2String(new Date(), DateFormatType.DATE_FORMAT_STR));
                    updateD0List.clear();
                    loop = loop + 1;
                }
            } catch (Exception e) {
                logger.error("批量保存历史气象异常", e);
            }
        }
        if (!CollectionUtils.isEmpty(updateD0List)) {
            weatherCityHisService.doSaveOrUpdateBatch(updateD0List);
        }
        logger.info("批量入库完成....,时间：{}", DateUtils.date2String(new Date(), DateFormatType.DATE_FORMAT_STR));
    }

    private void supplement2400(Map<String, WeatherCityHisDO> weatherMap) {
        weatherMap.forEach((k, v) -> {
            String[] splits = k.split("-");
            String cityId = splits[0];
            String dateFormate = splits[1];
            String type = splits[2];
            Date date = DateUtils.string2Date(dateFormate, DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);
            Date tomorrow = DateUtils.addDays(date, 1);
            String tomorrowFormate = DateUtils.date2String(tomorrow, DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);
            String tomorrowKey = cityId + "-" + tomorrowFormate + "-" + type;
            if (v.getT2400() == null) {
                WeatherCityHisDO tomorrowDO = weatherMap.get(tomorrowKey);
                if (tomorrowDO == null) {
                    try {
                        tomorrowDO = weatherCityHisService
                                .findWeatherCityHisDO(cityId, Integer.valueOf(type), tomorrow);
                    } catch (Exception e) {
                        logger.error("查询城市历史气象异常...");
                    }
                }
                if (tomorrowDO != null) {
                    v.setT2400(tomorrowDO.getT0000());
                }
            }
        });
    }

    private Map<String, WeatherCityHisDO> calculateAvg(Set<String> dates) throws Exception {
        Map<String, WeatherCityHisDO> weatherMap = new HashMap<>();
        for (String dateStr : dates) {
            Date date = DateUtils
                    .string2Date(dateStr, DateFormatType.SIMPLE_DATE_FORMAT_STR);

            List<WeatherStationHisBasicDO> list = weatherStationHisBasicService
                    .getWeatherStationHisBasicDO(null, null, date, date, null);

            Map<String, List<WeatherStationHisBasicDO>> listMap = list.stream()
                    .filter(weatherStationHisBasicDO -> weatherStationHisBasicDO.getCityId() != null)
                    .collect(Collectors.groupingBy(WeatherStationHisBasicDO::getCityId));


            for (Entry<String, List<WeatherStationHisBasicDO>> entry : listMap.entrySet()) {

                String cityId = entry.getKey();
                List<WeatherStationHisBasicDO> value = entry.getValue();

                Map<Integer, List<WeatherStationHisBasicDO>> collect = value.stream()
                        .collect(Collectors.groupingBy(WeatherStationHisBasicDO::getType));

                //计算平均值，并将结果放到map中
                for (Entry<Integer, List<WeatherStationHisBasicDO>> typeEntry : collect.entrySet()) {
                    List<WeatherStationHisBasicDO> entryValue = typeEntry.getValue();
                    Map<String, BigDecimal> avg = getAvgByWeatherStationDatas(entryValue, typeEntry.getKey());
                    WeatherCityHisDO weatherCityHisDO = new WeatherCityHisDO();
                    BasePeriodUtils.setAllFiled(weatherCityHisDO, avg);
                    weatherCityHisDO.setCityId(cityId);
                    weatherCityHisDO.setDate(new java.sql.Date(date.getTime()));
                    weatherCityHisDO.setType(typeEntry.getKey());

                    String dateFormate = DateUtils.date2String(date, DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);
                    String key = cityId + "-" + dateFormate + "-" + typeEntry.getKey();
                    weatherMap.put(key, weatherCityHisDO);
                }
            }
        }
        return weatherMap;
    }

    public Map<String, BigDecimal> getAvgByWeatherStationDatas(List<WeatherStationHisBasicDO> value, Integer type) {
        List<Map<String, BigDecimal>> list = new ArrayList<>();
        for (WeatherStationHisBasicDO weatherStationHisBasicDO : value) {
            Map<String, BigDecimal> decimalMap = BasePeriodUtils
                    .toMap(weatherStationHisBasicDO, 96, true);
            list.add(decimalMap);
        }
        //这里得到的集合是0000-2345
        List<String> columns = ColumnUtil.getColumns(96, true, true);

        Map<String, BigDecimal> result = new HashMap<>();
        for (String column : columns) {
            int countSize = 0;
            BigDecimal count = null;
            for (Map<String, BigDecimal> map : list) {
                if (map.get(column) != null) {
                    if (count == null) {
                        count = BigDecimal.ZERO;
                    }

                    //判断数据是否超过范围
                    if (type == 1) {
                        if (map.get(column).compareTo(new BigDecimal(100)) > 0) {
                            continue;
                        }
                    } else if (type == 2) {
                        if (map.get(column).compareTo(new BigDecimal(50)) > 0) {
                            continue;
                        }
                    } else if (type == 3) {
                        if (map.get(column).compareTo(new BigDecimal(50)) > 0) {
                            continue;
                        }
                    } else if (type == 4) {
                        if (map.get(column).compareTo(new BigDecimal(50)) > 0) {
                            continue;
                        }
                    }
                    count = count.add(map.get(column));
                    countSize++;
                }
            }
            if (countSize == 0) {
                result.put(column, null);
            } else {
                BigDecimal avg = count.divide(new BigDecimal(countSize), 2, BigDecimal.ROUND_HALF_UP);
                result.put(column, avg);
            }
        }
        return result;
    }

    public void saveOrUpdateWeatherCityHisBasic(String cityId, Date date, int type, Map<String, BigDecimal> map)
            throws Exception {

        if (map.get("t0000") != null) {
            Date yesterday = com.tsieframework.core.base.format.datetime.DateUtils.addDays(date, -1);
            Map<String, BigDecimal> yesterdayHashMap = new HashMap<>();
            yesterdayHashMap.put("t2400", map.get("t0000"));
            saveOrUpdateWeatherCityHisBasic(cityId, yesterday, type, yesterdayHashMap);
        }

        List<WeatherCityHisDO> weatherCityHisDOs = weatherCityHisService
                .findWeatherCityHisDOs(cityId, type, date, date);
        if (CollectionUtils.isEmpty(weatherCityHisDOs)) {
            WeatherCityHisDO weatherCityHisDO = new WeatherCityHisDO();
            weatherCityHisDO.setCreatetime(new Timestamp(System.currentTimeMillis()));
            weatherCityHisDO.setDate(new java.sql.Date(date.getTime()));
            weatherCityHisDO.setCityId(cityId);
            weatherCityHisDO.setType(type);
            weatherCityHisDO.setId(UUID.randomUUID().toString().replace("-", "").toLowerCase());
            BeanMap beanMap = BeanMap.create(weatherCityHisDO);
            beanMap.putAll(map);
            weatherCityHisDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            //PeriodDataUtil.do24To96VO(weatherCityHisDO);
            weatherCityHisService.doCreate(weatherCityHisDO);
        } else {
            WeatherCityHisDO weatherCityHisDO = weatherCityHisDOs.get(0);
            BeanMap beanMap = BeanMap.create(weatherCityHisDO);
            beanMap.putAll(map);
            weatherCityHisDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
            //PeriodDataUtil.do24To96VO(weatherCityHisDO);
            weatherCityHisService.doUpdateWeatherCityHisDO(weatherCityHisDO);
        }

    }

    /**
     * 统计湖北历史气象
     */
    @GetMapping("/provinceWeather")
    public BaseResp provinceWeather(Date start, Date end) throws Exception {
        weatherStatService.statPorviceWeather(start, end);
        return BaseResp.succResp();
    }

    /**
     * 查询湖北-累计平均温度
     */
    @GetMapping("/avgTemperature")
    public BaseResp getAvgTemperature() throws Exception {
        BaseResp baseResp = null;
        try {
            SettingSystemDO byFieldId = settingSystemService.findByFieldId(SystemConstant.AVG_TEMPERATURE_VALUE);
            String avgTemperatureStr = byFieldId.getValue();
            com.alibaba.fastjson.JSONObject map = com.alibaba.fastjson.JSONObject.parseObject(avgTemperatureStr);
            List<SystemTotalAvgTemperatureDTO> list = new ArrayList<>();
            Set<Entry<String, Object>> entries = map.entrySet();
            for (Entry<String, Object> entry : entries) {
                SystemTotalAvgTemperatureDTO dto = new SystemTotalAvgTemperatureDTO();
                dto.setKey(entry.getKey());
                dto.setDate(entry.getKey());
                dto.setValue(entry.getValue() + "");
                list.add(dto);
            }
            baseResp = BaseResp.succResp();
            baseResp.setData(list);
        } catch (Exception e) {
            e.printStackTrace();
            baseResp = BaseResp.failResp("获取对应的累计平均温度设置权重值失败");
        }
        return baseResp;
    }

    /**
     * 保存累计平均温度权重值
     */
    @RequestMapping(value = "/avgTemperature", method = RequestMethod.POST)
    public BaseResp updateCoefficient(@RequestBody SystemAvgTemperatureDTO systemAvgTemperatureDTO) throws Exception {
        String[] date = systemAvgTemperatureDTO.getDate();
        String[] value = systemAvgTemperatureDTO.getValue();
        Map<String, String> map = new HashMap<>();
        for (int i = 0; i < date.length; i++) {
            map.put(date[i], value[i]);
        }
        String avgTemperatureValueStr = com.alibaba.fastjson.JSONObject.toJSONString(map);
        settingSystemService.doUpdateOrSaveValueByKey(SystemConstant.AVG_TEMPERATURE_VALUE, avgTemperatureValueStr);
        BaseResp baseResp = BaseResp.succResp();
        return baseResp;
    }

    @ApiOperation("分旬算法调用")
    @RequestMapping(value = "/doMonthAlgorithmFc", method = RequestMethod.POST)
    public BaseResp doMonthAlgorithmFc(@RequestBody ForecsatRequest forecsatRequest) throws Exception {
        BaseResp baseResp = new BaseResp();
        RestTemplate restTemplate = new RestTemplate();
        List<String> cityIds = forecsatRequest.getCityIds();
        List<String> caliberIds = forecsatRequest.getCaliberIds();
        List<String> algorithmIds = forecsatRequest.getAlgorithmIds();
        Date startDate = forecsatRequest.getStartDate();
        Date endDate = forecsatRequest.getEndDate();
        if (CollectionUtils.isEmpty(cityIds) || CollectionUtils.isEmpty(caliberIds)) {
            baseResp.setRetCode("T100");
            baseResp.setRetMsg("预测算法失败！ 请选择要预测的城市或口径");
        }
        if (cityIds.size() == 1 && cityIds.get(0).equals("all")) {
            List<CityDO> cityVOS = cityService.findAllCitys();
            cityIds = cityVOS.stream().map(CityDO::getId).collect(Collectors.toList());
        }
        if (caliberIds.size() == 1 && caliberIds.get(0).equals("all")) {
            List<CaliberDO> caliberVOS = caliberService.findAllCalibers();
            caliberIds = caliberVOS.stream().map(CaliberDO::getId).collect(Collectors.toList());
        }
        String dateToStr = DateUtil.getDateToStr(startDate);
        String result = null;
        String status = "1";
        //1 所有城市
        for (String cityId : cityIds) {
            //2 所有口径
            for (String caliberId : caliberIds) {
                for (String algorithmId : algorithmIds) {
                    result = restTemplate.getForObject(url, String.class, cityId, caliberId, algorithmId, dateToStr,
                            status);
                }
            }
        }
        if (result != null) {
            BaseResp baseResp1 = JSONUtil.toBean(JSONUtil.toJsonStr(result), BaseResp.class);
            if ("T200".equals(baseResp1.getRetCode())) {
                return baseResp;
            } else {
                return new BaseResp("T701");
            }
        } else {
            return new BaseResp("T706");
        }
    }

    @ApiOperation("冬夏算法调用")
    @RequestMapping(value = "/doSeasonAlgorithmFc", method = RequestMethod.POST)
    public BaseResp doSeasonAlgorithmFc(@RequestBody NewForecsatRequest forecsatRequest) throws Exception {
        BaseResp baseResp = new BaseResp();
        RestTemplate restTemplate = new RestTemplate();
        List<String> cityIds = forecsatRequest.getCityIds();
        List<String> caliberIds = forecsatRequest.getCaliberIds();
        List<String> algorithmIds = forecsatRequest.getAlgorithmIds();
        List<String> seasonIds = forecsatRequest.getSeasonIds();
        Date startDate = forecsatRequest.getStartDate();
        Date endDate = forecsatRequest.getEndDate();
        if (CollectionUtils.isEmpty(cityIds) || CollectionUtils.isEmpty(caliberIds)) {
            baseResp.setRetCode("T100");
            baseResp.setRetMsg("预测算法失败！ 请选择要预测的城市或口径");
        }
        if (cityIds.size() == 1 && cityIds.get(0).equals("all")) {
            List<CityDO> cityVOS = cityService.findAllCitys();
            cityIds = cityVOS.stream().map(CityDO::getId).collect(Collectors.toList());
        }
        if (caliberIds.size() == 1 && caliberIds.get(0).equals("all")) {
            List<CaliberDO> caliberVOS = caliberService.findAllCalibers();
            caliberIds = caliberVOS.stream().map(CaliberDO::getId).collect(Collectors.toList());
        }
        String dateToStr = DateUtil.getDateToStr(startDate);
        String dateToStr1 = DateUtil.getDateToStr(endDate);
        String result = null;
        String status = "1";
        //1 所有城市
        for (String cityId : cityIds) {
            //2 所有口径
            for (String caliberId : caliberIds) {
                for (String algorithmId : algorithmIds) {
                    for (String seasonId : seasonIds) {
                        result = restTemplate.getForObject(url1, String.class, cityId, caliberId, algorithmId,
                                dateToStr, dateToStr1, seasonId, status);
                    }
                }
            }
        }
        if (result != null) {
            BaseResp baseResp1 = JSONUtil.toBean(JSONUtil.toJsonStr(result), BaseResp.class);
            if ("T200".equals(baseResp1.getRetCode())) {
                return baseResp;
            } else {
                return new BaseResp("T701");
            }
        } else {
            return new BaseResp("T706");
        }
    }

    @ApiOperation("年度算法调用")
    @RequestMapping(value = "/doYearAlgorithmFc", method = RequestMethod.POST)
    public BaseResp doYearAlgorithmFc(@RequestBody ForecsatRequest forecsatRequest) throws Exception {
        BaseResp baseResp = new BaseResp();
        RestTemplate restTemplate = new RestTemplate();
        List<String> cityIds = forecsatRequest.getCityIds();
        List<String> caliberIds = forecsatRequest.getCaliberIds();
        List<String> algorithmIds = forecsatRequest.getAlgorithmIds();
        Date startDate = forecsatRequest.getStartDate();
        Date endDate = forecsatRequest.getEndDate();
        if (CollectionUtils.isEmpty(cityIds) || CollectionUtils.isEmpty(caliberIds)) {
            baseResp.setRetCode("T100");
            baseResp.setRetMsg("预测算法失败！ 请选择要预测的城市或口径");
        }
        if (cityIds.size() == 1 && cityIds.get(0).equals("all")) {
            List<CityDO> cityVOS = cityService.findAllCitys();
            cityIds = cityVOS.stream().map(CityDO::getId).collect(Collectors.toList());
        }
        if (caliberIds.size() == 1 && caliberIds.get(0).equals("all")) {
            List<CaliberDO> caliberVOS = caliberService.findAllCalibers();
            caliberIds = caliberVOS.stream().map(CaliberDO::getId).collect(Collectors.toList());
        }
        String dateToStr = DateUtil.getDateToStr(startDate);
        String dateToStr1 = DateUtil.getDateToStr(endDate);
        String result = null;
        String status = "1";
        //1 所有城市
        for (String cityId : cityIds) {
            //2 所有口径
            for (String caliberId : caliberIds) {
                for (String algorithmId : algorithmIds) {
                    result = restTemplate.getForObject(url2, String.class, cityId, caliberId, algorithmId, dateToStr,
                            dateToStr1, status);
                }
            }
        }
        if (result != null) {
            BaseResp baseResp1 = JSONUtil.toBean(JSONUtil.toJsonStr(result), BaseResp.class);
            if ("T200".equals(baseResp1.getRetCode())) {
                return baseResp;
            } else {
                return new BaseResp("T701");
            }
        } else {
            return new BaseResp("T706");
        }
    }

    @ApiOperation("分旬准确率统计")
    @RequestMapping(value = "/doMonthAccuracyFc", method = RequestMethod.POST)
    public BaseResp doMonthAccuracyFc(@RequestBody ForecsatRequest forecsatRequest) throws Exception {
        BaseResp baseResp = new BaseResp();
        RestTemplate restTemplate = new RestTemplate();
        List<String> cityIds = forecsatRequest.getCityIds();
        List<String> caliberIds = forecsatRequest.getCaliberIds();
        List<String> algorithmIds = forecsatRequest.getAlgorithmIds();
        Date startDate = forecsatRequest.getStartDate();
        Date endDate = forecsatRequest.getEndDate();
        if (CollectionUtils.isEmpty(cityIds) || CollectionUtils.isEmpty(caliberIds)) {
            baseResp.setRetCode("T100");
            baseResp.setRetMsg("预测算法失败！ 请选择要预测的城市或口径");
        }
        if (cityIds.size() == 1 && cityIds.get(0).equals("all")) {
            List<CityDO> cityVOS = cityService.findAllCitys();
            cityIds = cityVOS.stream().map(CityDO::getId).collect(Collectors.toList());
        }
        if (caliberIds.size() == 1 && caliberIds.get(0).equals("all")) {
            List<CaliberDO> caliberVOS = caliberService.findAllCalibers();
            caliberIds = caliberVOS.stream().map(CaliberDO::getId).collect(Collectors.toList());
        }
        String dateToStr = DateUtil.getDateToStr(startDate);
        String dateToStr1 = DateUtil.getDateToStr(endDate);
        String result = null;
        //1 所有城市
        for (String cityId : cityIds) {
            //2 所有口径
            for (String caliberId : caliberIds) {
                for (String algorithmId : algorithmIds) {
                    result = restTemplate.getForObject(url3, String.class, cityId, caliberId, algorithmId, dateToStr,
                            dateToStr1);
                }
            }
        }
        if (result != null) {
            BaseResp baseResp1 = JSONUtil.toBean(JSONUtil.toJsonStr(result), BaseResp.class);
            if ("T200".equals(baseResp1.getRetCode())) {
                return baseResp;
            } else {
                return new BaseResp("T701");
            }
        } else {
            return new BaseResp("T706");
        }
    }

    class Worker implements Runnable {

        Date endDate;

        List<String> cityIds;

        Date start;

        Date end;

        public Worker(Date endDate, List<String> cityIds, Date start, Date end) {
            this.endDate = endDate;
            this.cityIds = cityIds;
            this.start = start;
            this.end = end;
        }

        @Override
        public void run() {
            try {
                if (end.before(endDate)) {
                    logger.info(
                            "线程" + Thread.currentThread().getName() + "统计日气象特性开始，start=" + start + ",end="
                                    + end);
                    weatherFeatureStatService.doStatWeatherFeatureCityDay(cityIds,
                            start, end);
                    logger.info(
                            "线程" + Thread.currentThread().getName() + "统计日气象特性完成...，start=" + start + ",end="
                                    + end);
                } else {
                    logger.info(
                            "线程" + Thread.currentThread().getName() + "统计日气象特性开始，start=" + start + ",end="
                                    + endDate);
                    weatherFeatureStatService.doStatWeatherFeatureCityDay(cityIds,
                            start, endDate);
                    logger.info(
                            "线程" + Thread.currentThread().getName() + "统计日气象特性完成...，start=" + start + ",end="
                                    + endDate);
                }
            } catch (Exception e) {
                logger.error("统计气象特性异常...", e);
            }
        }
    }
}
