/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司 Author:  wangfeng Date:  2019/12/6 2:02 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.ultra.controller;

import com.tsieframework.core.base.dao.type.DataPointListMeta;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.math.BigDecimalFunctions;
import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.aif.algorithm.serviceapi.base.constants.ShortConstants;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.core.util.UltraSystemUtils;
import com.tsintergy.lf.core.util.VslfDateUtil;
import com.tsintergy.lf.serviceapi.base.ultra.load.pojo.LoadCityFcUltraDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadBatchAccuracyService;
import com.tsintergy.lf.serviceapi.base.ultra.load.api.LoadCityFcUltraService;
import com.tsintergy.lf.serviceapi.base.ultra.api.LoadCityHisUltraBasicService;
import com.tsintergy.lf.serviceapi.base.ultra.pojo.AccuracyLoadCityFcUltraDO;
import com.tsintergy.lf.web.base.ultra.response.MultipointShortAccuracyResponse;
import com.tsintergy.lf.web.base.ultra.response.MultipointShortData;
import com.tsintergy.lf.web.base.ultra.response.MultipointShortResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description:  <br> 超短期多点多批次准预测 页面相关接口
 *
 * <AUTHOR>
 * @create 2019/12/6
 * @since 1.0.0
 */
@Api(value = "超短期预测-多时段查询与准确率", tags = "[超短期模块][超短期预测][多时段查询与准确率]")
@RequestMapping("/ultra/multipoint")
@RestController
public class MultipointShortController extends UltraBaseController {

    @Autowired
    private LoadCityHisUltraBasicService loadCityHisUltraBasicService;

    @Autowired
    private LoadCityFcUltraService loadCityFcUltraService;

    @Autowired
    private LoadBatchAccuracyService loadBatchAccuracyService;

    private List<String> fixTimeList(List<Date> dates, Integer totalColums) {
        List<String> columns = ColumnUtil.getColumns(totalColums, UltraSystemUtils.startWithZero(), false);
        List<String> result = new ArrayList<>();
        for (Date date : dates) {
            String dateString = DateUtils.date2String(date, DateFormatType.SIMPLE_DATE_FORMAT_STR);
            for (String column : columns) {
                String finalTime = dateString + " " + column.substring(0, 2) + ":" + column.substring(2);
                result.add(finalTime);
            }
        }
        return result;
    }

    /**
     * 超短期 多点多批次预测曲线查询 查询预测数据
     *
     * @param timeSpan 时间间隔 5 or 15
     * @return 数据集
     * <AUTHOR>
     */
    @ApiOperation(value = "多时段预测查询")
    @RequestMapping(value = "/short/forecast", method = RequestMethod.GET)
    public BaseResp getShortCurveData(Date startDate, Date endDate, String cityId, Integer timeSpan, String caliberId, String algorithmId) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<Date> dayList = VslfDateUtil.getListBetweenDay(startDate, endDate);
        MultipointShortResponse response = new MultipointShortResponse();
        int totalColums = timeSpan == DataPointListMeta.MINUTE_5 ? DataPointListMeta.POINTS_288 : DataPointListMeta.POINTS_96;
        if (StringUtils.isEmpty(caliberId)){
            caliberId = this.getCaliberId();
        }
        List<BigDecimal> real = new ArrayList<>();
        List<MultipointShortData> resultList = new ArrayList<>();
        for (Date day : dayList) {



            List<BigDecimal> realMid = loadCityHisUltraBasicService.findLoadCityHisDO(cityId, day, day, caliberId, timeSpan);
            if (CollectionUtils.isEmpty(realMid)) {
                real.addAll(ColumnUtil.getZeroOrNullList(totalColums, null));
            } else {
                real.addAll(realMid);
            }
            List<LoadCityFcUltraDO> loadCityFcD0List = loadCityFcUltraService
                .findLoadCityFcD0(cityId, caliberId, algorithmId, day, day, null, timeSpan);
            for (LoadCityFcUltraDO srcDO : loadCityFcD0List) {
                MultipointShortData result = new MultipointShortData();
                result.setMultipointType(srcDO.getMultipointType());
                result.setShortForecast(setShortData(srcDO));
                resultList.add(result);
            }
        }
        if (!CollectionUtils.isEmpty(real) && real.size() > 0) {
            response.setReal(real);
        }
        List<String> timeStrings = fixTimeList(dayList, totalColums);
        response.setTimeList(timeStrings);
        response.setUltraDataList(resultList.stream().sorted(Comparator.comparing(MultipointShortData::getMultipointType)).collect(Collectors.toList()));
        baseResp.setData(response);
        return baseResp;
    }


    /**
     * 超短期 多点多批次预测准确率查询  5 or 15分钟 都是查询的288点的准确率；15分钟展示时时刻点以及对应数据调整为15分钟间隔即可。
     *
     * @param timeSpan 时间间隔 5 or 15;
     * @return 数据集
     * <AUTHOR>
     */
    @ApiOperation(value = "多时段预测准确率")
    @RequestMapping(value = "/short/accuracy", method = RequestMethod.GET)
    public BaseResp getShortCurveAccuracyData(Date date, String cityId, Integer timeSpan, String caliberId, String algorithmId) throws Exception {
        MultipointShortAccuracyResponse resp = new MultipointShortAccuracyResponse();
        BaseResp baseResp = BaseResp.succResp();
        if (StringUtils.isEmpty(caliberId)){
            caliberId = getCaliberId();
        }
        List<AccuracyLoadCityFcUltraDO> accuracyLoadCityFcDO = this.loadBatchAccuracyService
            .findAccuracyDO(cityId, date, caliberId, timeSpan, algorithmId, null);
        List<MultipointShortData> resultList = new ArrayList<>();
        for (AccuracyLoadCityFcUltraDO accuracyDO : accuracyLoadCityFcDO) {
            MultipointShortData result = new MultipointShortData();
            List<BigDecimal> tvData = accuracyDO.getTvData();
            tvData = BigDecimalFunctions.listMultiplyValue(tvData, new BigDecimal(100));
            result.setShortForecast(tvData);
            result.setMultipointType(accuracyDO.getMultipointType());
            resultList.add(result);
        }
        List<Date> dayList = new ArrayList<>();
        dayList.add(date);
        List<String> timeStrings = fixTimeList(dayList,
            ShortConstants.MINUTE.equals(timeSpan) ? 288 : 96);
        resp.setTimeList(timeStrings);
        resp.setShortForecastAccuracy(resultList);
        baseResp.setData(resp);
        return baseResp;

    }

    private List<BigDecimal> setShortData(LoadCityFcUltraDO data) {
        List<BigDecimal> shortForecast = new ArrayList<>();
        if (data != null) {
            shortForecast.addAll(data.getTvData());
        } else {
            shortForecast.addAll(ColumnUtil.getZeroOrNullList(288, null));
        }
        return shortForecast;
    }


}