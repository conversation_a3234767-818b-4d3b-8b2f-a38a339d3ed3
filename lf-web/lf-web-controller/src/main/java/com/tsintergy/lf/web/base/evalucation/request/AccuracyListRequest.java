package com.tsintergy.lf.web.base.evalucation.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
@ApiModel("准确率列表")
public class AccuracyListRequest implements Serializable {
    @ApiModelProperty(value = "城市列表")
    private List<String> cityIdList;
    @ApiModelProperty(value = "口径")
    private String caliberId;
    @ApiModelProperty(value = "开始日期")
    private String startDate;
    @ApiModelProperty(value = "结束日期")
    private String endDate;
    @ApiModelProperty(value = "类型")
    private String type;

    public List<String> getCityIdList() {
        return cityIdList;
    }

    public void setCityIdList(List<String> cityIdList) {
        this.cityIdList = cityIdList;
    }

    public String getCaliberId() {
        return caliberId;
    }

    public void setCaliberId(String caliberId) {
        this.caliberId = caliberId;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public void setCityIdList() {
    }
}