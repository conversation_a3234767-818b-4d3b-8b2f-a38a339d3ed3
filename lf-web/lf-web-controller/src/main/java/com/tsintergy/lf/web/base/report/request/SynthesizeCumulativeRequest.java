package com.tsintergy.lf.web.base.report.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import javax.validation.constraints.NotNull;

@ApiModel("累计准确率修正请求")
public class SynthesizeCumulativeRequest implements Serializable {

    @ApiModelProperty("城市id")
    @NotNull
    private String cityId;

    @ApiModelProperty("修正累计准确率")
    private BigDecimal comprehensiveAccuracyRight;

    @ApiModelProperty("日期")
    @NotNull
    private String date;

    @ApiModelProperty("类型")
    private Integer type;

    public String getCityId() {
        return cityId;
    }

    public void setCityId(String cityId) {
        this.cityId = cityId;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public BigDecimal getComprehensiveAccuracyRight() {
        return comprehensiveAccuracyRight;
    }

    public void setComprehensiveAccuracyRight(BigDecimal comprehensiveAccuracyRight) {
        this.comprehensiveAccuracyRight = comprehensiveAccuracyRight;
    }
}