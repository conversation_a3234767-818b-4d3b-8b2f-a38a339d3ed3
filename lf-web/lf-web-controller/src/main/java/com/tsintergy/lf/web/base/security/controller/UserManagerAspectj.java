//package com.tsintergy.lf.web.base.security.controller;
//
//import com.tsieframework.cloud.security.serviceapi.system.pojo.TsieUserVO;
//import com.tsieframework.core.base.dao.DataPackage;
//import com.tsieframework.core.base.web.BaseResp;
//import com.tsieframework.core.base.web.BaseRespBuilder;
//import com.tsintergy.lf.serviceapi.base.base.api.CityService;
//import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
//import com.tsintergy.lf.serviceapi.base.base.pojo.LoadUserDO;
//import com.tsintergy.lf.serviceapi.base.security.api.UserService;
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Map;
//import java.util.stream.Collectors;
//import org.aspectj.lang.JoinPoint;
//import org.aspectj.lang.ProceedingJoinPoint;
//import org.aspectj.lang.annotation.Around;
//import org.aspectj.lang.annotation.Aspect;
//import org.aspectj.lang.annotation.Before;
//import org.aspectj.lang.annotation.Pointcut;
//import org.springframework.beans.BeanUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
///**
// * 强化平台组的用户管理SysUserController的查询用户列表方法
// */
//@Component
//@Aspect
//public class UserManagerAspectj {
//
//    @Autowired
//    protected UserService userService;
//
//    @Autowired
//    private CityService cityService;
//
//    @Pointcut("execution(* com.tsieframework.cloud.security.web.system.controller.SysUserController.list(..))")
//    public void userManagerListPoint() {
//    }
//
//    @Pointcut("execution(* com.tsieframework.cloud.security.web.system.controller.SysUserController.get(..))")
//    public void userManagerGetPoint() {
//    }
//
//    @Pointcut("execution(* com.tsieframework.cloud.security.web.system.controller.SysUserController.saveOrUpdate(..))")
//    public void userManagerSaveOrUpdatePoint() {
//    }
//
//    @Around("userManagerListPoint()")
//    @SuppressWarnings("all")
//    public Object doInfluenceUserList(ProceedingJoinPoint pjp) throws Throwable {
//        Object object = pjp.proceed();
//        BaseResp<DataPackage<LoadUserDO>> bb = (BaseResp) object;
//        DataPackage<LoadUserDO> data = bb.getData();
//        List<LoadUserDO> loadUserList = userService.findLoadUserList(null);
//        Map<String, String> collect = loadUserList.stream().collect(
//            Collectors.toMap(LoadUserDO::getTsieUId, LoadUserDO::getCity));
//        List<LoadUserDO> resultList = new ArrayList<>();
//        for (Object objectData : data.getDatas()) {
//            TsieUserVO tsieUserVO  = (TsieUserVO)objectData;
//            LoadUserDO result = new LoadUserDO();
//            BeanUtils.copyProperties(tsieUserVO, result);
//            String city = collect.get(tsieUserVO.getId());
//            result.setCity(city);
//            resultList.add(result);
//        }
//        data.setDatas(resultList);
//        return BaseRespBuilder.success().setData(data).build();
//    }
//
//    @Around("userManagerGetPoint()")
//    public Object doInfluenceUserGet(ProceedingJoinPoint pjp) throws Throwable {
//        Object object = pjp.proceed();
//        BaseResp<Object> srcResult = (BaseResp) object;
//        TsieUserVO data = (TsieUserVO)srcResult.getData();
//        LoadUserDO loadUserList = userService.findUserById(data.getId());
//        CityDO cityById = cityService.findCityById(loadUserList.getCityId());
//        LoadUserDO result = new LoadUserDO();
//        BeanUtils.copyProperties(data, result);
//        if (cityById != null) {
//            result.setCity(cityById.getCity());
//        }
//        return BaseRespBuilder.success().setData(result).build();
//    }
//
//    @Before("userManagerSaveOrUpdatePoint()")
//    public Object doInfluenceUserSaveOrUpdate(JoinPoint joinPoint) throws Throwable {
//        Object[] args = joinPoint.getArgs();
//
//        System.out.println("111");
////        Object object = pjp.proceed();
////        BaseResp<Object> srcResult = (BaseResp) object;
////        TsieUserVO data = (TsieUserVO)srcResult.getData();
////        LoadUserDO loadUserList = userService.findUserById(data.getId());
//
////        LoadUserDO loadUserVO = new LoadUserDO();
////        CityDO cityById = cityService.findCityById(req.getCityId());
////        LoadUserDO user = userService.findUserById(tsieResp.getData().getId());
////        if(cityById != null){
////            loadUserVO.setCity(cityById.getCity());
////        }
////        loadUserVO.setTsieUId(tsieResp.getData().getId());
////        loadUserVO.setCityId(req.getCityId());
////        if (user != null){
////            loadUserVO.setCreateTime(user.getCreateTime());
////        }
//        //loadUserVO.setCreateTime(new Timestamp(System.currentTimeMillis()));
////        userService.doSaveOrUpdateUser(loadUserVO);
//        return null;
//
//
//    }
//
//
//}
