/**
 * Copyright(C),2015‐2021,北京清能互联科技有限公司
 */
package com.tsintergy.lf.web.base.index.response;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2021/11/16 16:45
 * @Version: 1.0.0
 */
@Data
public class IndexLfReponse {
    @ApiModelProperty(value = "昨天最大功率",example = "213215")
    private BigDecimal yesterdayMaxPower;
    @ApiModelProperty(value = "今天预测最大功率",example = "213215")
    private BigDecimal todayFcMaxPower;
    @ApiModelProperty(value = "时刻点")
    private List<BigDecimal> realTime;
}