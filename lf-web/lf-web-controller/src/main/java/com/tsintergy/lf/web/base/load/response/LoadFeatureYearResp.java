package com.tsintergy.lf.web.base.load.response;

import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.sql.Date;

/**
 * <AUTHOR>
 * @date : 2024/8/28
 */
@ApiModel
@Data
public class LoadFeatureYearResp {
    /**
     * 年
     */
    @ApiModelProperty(value = "年")
    private String year;

    /**
     * 最大负荷
     */
    @ApiModelProperty(value = "最大负荷")
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal maxLoad;

    /**
     * 最小负荷
     */
    @ApiModelProperty(value = "最小负荷")
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal minLoad;

    /**
     * 平均负荷
     */
    @ApiModelProperty(name = "平均负荷")
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal aveLoad;

    /**
     * 最大负荷日
     */
    @ApiModelProperty(name = "最大负荷出现时间")
    private String maxDate;

    @ApiModelProperty(name = "平均最大负荷")
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal avgMaxLoad;

    @ApiModelProperty(name = "最小负荷发生时刻")
    private String minDate;

    @ApiModelProperty(name = "平均最小负荷")
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal avgMinLoad;

    /**
     * 尖峰平均负荷
     */
    @ApiModelProperty(name = "尖峰平均负荷")
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal peak;

    /**
     * 低谷平均负荷
     */
    @ApiModelProperty(name = "低谷平均负荷")
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal trough;

    /**
     * 峰谷差
     */
    @ApiModelProperty(name = "峰谷差")
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal different;

    /**
     * 峰谷差率
     */
    @ApiModelProperty(name = "峰谷差率")
    @BigdecimalJsonFormat(scale = 4)
    private BigDecimal gradient;

    /**
     * 负荷率
     */
    @ApiModelProperty(name = "负荷率")
    @BigdecimalJsonFormat(scale = 4)
    private BigDecimal loadGradient;

    /**
     * 日不均衡系数
     */
    @ApiModelProperty(name = "日不均衡系数")
    private BigDecimal dayUnbalance;

    /**
     * 年电量
     */
    @ApiModelProperty(name = "年电量")
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal energy;

    /**
     * 最大负荷利用小时数
     */
    @ApiModelProperty(name = "最大负荷利用小时数")
    private BigDecimal useHours;

    /**
     * 最大早峰负荷
     */
    @ApiModelProperty(name = "最大早峰负荷")
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal maxMorningPeak;

    /**
     * 最大早峰负荷发生日期
     */
    @ApiModelProperty(name = "最大早峰负荷发生日期")
    private String maxMorningPeakDate;

    @ApiModelProperty(name = "平均最大早峰负荷")
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal avgMaxMorningPeak;

    /**
     * 最小腰荷（午间最小负荷）
     */
    @ApiModelProperty(name = "最小腰荷（午间最小负荷）")
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal minNoontimePeak;

    @ApiModelProperty(name = "最小腰荷（午间最小负荷）发生时间")
    private String minNoontimePeakDate;

    @ApiModelProperty(name = "平均最小腰荷（午间最小负荷）")
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal avgMinNoontimePeak;
    // 最大腰荷（午间负荷）
    @ApiModelProperty(name = "最大腰荷（午间负荷）")
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal maxNoontimePeak;

    @ApiModelProperty(name = "最大腰荷（午间负荷）发生时间")
    private String maxNoontimePeakDate;

    @ApiModelProperty(name = "平均最大腰荷（午间负荷）")
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal avgMaxNoontimePeak;

    // 晚峰负荷
    @ApiModelProperty(name = "晚峰负荷")
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal maxEveningPeak;

    @ApiModelProperty(name = "晚峰负荷发生时间")
    private String maxEveningPeakDate;

    @ApiModelProperty(name = "平均晚峰负荷")
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal avgMaxEveningPeak;

    // 最大负荷率
    @ApiModelProperty(name = "最大负荷率")
    @BigdecimalJsonFormat(scale = 4)
    private BigDecimal maxLoadGradient;

    @ApiModelProperty(name = "最大负荷率发生日期")
    private Date maxLoadGradientDate;

    // 最小负荷率
    @ApiModelProperty(name = "最小负荷率")
    @BigdecimalJsonFormat(scale = 4)
    private BigDecimal minLoadGradient;

    @ApiModelProperty(name = "最小负荷率发生日期")
    private Date minLoadGradientDate;

    @ApiModelProperty(name = "平均负荷率")
    @BigdecimalJsonFormat(scale = 4)
    private BigDecimal avgLoadGradient;

    // 最大峰谷差
    @ApiModelProperty(name = "最大峰谷差")
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal maxDifferent;

    @ApiModelProperty(name = "最大峰谷差发生日期")
    private Date maxDifferentDate;

    // 最小峰谷差
    @ApiModelProperty(name = "最小峰谷差")
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal minDifferent;

    @ApiModelProperty(name = "最小峰谷差发生日期")
    private Date minDifferentDate;

    @ApiModelProperty(name = "平均峰谷差")
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal avgDifferent;

    @ApiModelProperty(name = "平均负荷率")
    private BigDecimal avgGradient;
}
