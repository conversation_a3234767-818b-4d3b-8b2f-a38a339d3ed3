package com.tsintergy.lf.web.base.forecast.request;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * Description: 中长期预测
 *
 * <AUTHOR>
 * @create 2023-03-15
 * @since 1.0.0
 */
@Data
public class ManualRequest4 {

    /**
     * 101人工调整  102气象源
     */
    @ApiModelProperty(value = "天气code")
    private String weatherCode;

    /**
     * 城市id
     */
    @ApiModelProperty(value = "城市id")
    private String cityId;

    /**
     * 预测算法id
     */
    @ApiModelProperty(value = "预测算法id")
    private List<String> algorithmIds;

    /**
     * 预测开始时间
     */
    @ApiModelProperty(value = "预测开始日")
    private String startDate;

}
