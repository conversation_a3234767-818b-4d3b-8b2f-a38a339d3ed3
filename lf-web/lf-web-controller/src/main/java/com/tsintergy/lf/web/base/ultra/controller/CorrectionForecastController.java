package com.tsintergy.lf.web.base.ultra.controller;

import com.alibaba.excel.EasyExcel;
import com.tsieframework.cloud.security.web.businesslog.annotation.OperateLog;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.enums.IfEnum;
import com.tsintergy.lf.core.util.VslfDateUtil;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.forecast.api.ForecastService;
import com.tsintergy.lf.serviceapi.base.ultra.load.pojo.LoadCityFcUltraDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadBatchAccuracyService;
import com.tsintergy.lf.serviceapi.base.ultra.load.api.LoadCityFcUltraService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureStatService;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceapi.base.system.dto.SystemData;
import com.tsintergy.lf.serviceapi.base.ultra.api.LoadCityHisUltraBasicService;
import com.tsintergy.lf.serviceapi.base.ultra.api.WeatherDataManagerService;
import com.tsintergy.lf.serviceapi.base.ultra.dto.UltraForecastDTO;
import com.tsintergy.lf.serviceapi.base.ultra.dto.UltraLoadFeatureFcDTO;
import com.tsintergy.lf.serviceapi.base.ultra.dto.WeatherNameDTO;
import com.tsintergy.lf.serviceapi.base.ultra.pojo.LoadCityHisUltraBasicDO;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityHisService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureStatService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayFcDO;
import com.tsintergy.lf.web.base.forecast.request.RecorrectRequest;
import com.tsintergy.lf.web.base.forecast.request.SmoothLineRequest;
import com.tsintergy.lf.web.base.forecast.response.SmoothLineResp;
import com.tsintergy.lf.web.base.ultra.request.UltraReferenceRequest;
import com.tsintergy.lf.web.base.ultra.response.UltraReferenceResponse;
import com.tsintergy.lf.web.base.util.ultra.ExcelMoreDayForecast288FcListener;
import com.tsintergy.lf.web.base.util.ultra.ExcelMoreDayForecast288HisListener;
import com.tsintergy.lf.web.base.util.ultra.ExcelMoreDayForecast96FcListener;
import com.tsintergy.lf.web.base.util.ultra.ExcelMoreDayForecast96HisListener;
import com.tsintergy.lf.web.base.util.ultra.ExcelMoreDayForecast96Listener;
import com.tsintergy.lf.web.base.util.ultra.ExcelMoreDayForecastData96;
import com.tsintergy.lf.web.base.ultra.request.UltraForecastRequest;
import com.tsintergy.lf.web.base.ultra.request.UltraResultPushOrSaveRequest;
import com.tsintergy.lf.web.base.ultra.response.CorrectionStatusResp;
import com.tsintergy.lf.web.base.ultra.response.UltraForecastResp;
import com.tsintergy.lf.web.base.ultra.response.UltraLoadAndWeatherFeatureFcResp;
import com.tsintergy.lf.web.base.util.CheckoutFileTypeSecurity;
import com.tsintergy.lf.web.base.util.ultra.ExcelMoreDayForecastDataHis96;
import com.tsintergy.lf.web.base.util.ultra.ExcelMoreDayWeather96Listener;
import com.tsintergy.lf.web.base.util.ultra.ExcelMoreDayWeatherDataHis96;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * @Description 修正上报相关页
 * @Date 2023/5/23 8:53
 * <AUTHOR>
 **/
@Slf4j
@Api(value = "超短期预测-人工修正", tags = "[超短期模块][超短期预测][人工修正]")
@RestController
@RequestMapping("/ultra/forecast")
public class CorrectionForecastController extends UltraBaseLoadFeatureController {

    @Autowired
    LoadBatchAccuracyService loadBatchAccuracyService;

    @Autowired
    LoadCityFcUltraService loadCityFcUltraService;

    @Autowired
    WeatherCityHisService weatherCityHisService;

    @Autowired
    SettingSystemService settingSystemService;

    @Autowired
    CityService cityService;

    @Autowired
    LoadCityHisUltraBasicService loadCityHisUltraBasicService;

    @Autowired
    ForecastService forecastService;

    @Autowired
    WeatherDataManagerService weatherDataManagerService;

    @Autowired
    LoadFeatureStatService loadFeatureStatService;

    @Autowired
    WeatherCityFcService weatherCityFcService;

    @Autowired
    WeatherFeatureStatService weatherFeatureStatService;

    @ApiOperation("获取上报状态")
    @RequestMapping(value = "/get/status", method = RequestMethod.GET)
    public BaseResp<CorrectionStatusResp> getReportStatu(String cityId, String caliberId, Date date, Integer timeSpan)
        throws Exception {
        CorrectionStatusResp correctionStatusResp = new CorrectionStatusResp();
        LoadCityFcUltraDO loadCityFcUltraDO = loadCityFcUltraService.getReportLoadCityFcDO(date, cityId, caliberId, IfEnum.YES,
            timeSpan, Constants.ULTRA_FORECAST_1);
        if (loadCityFcUltraDO != null) {
            Date reportTime = loadCityFcUltraDO.getReportTime();
            if (reportTime == null) {
                if (loadCityFcUltraDO.getUpdateTime() != null) {
                    reportTime = loadCityFcUltraDO.getUpdateTime();
                } else {
                    reportTime = loadCityFcUltraDO.getCreateTime();
                }
            }
            if (reportTime != null) {
                boolean report = settingSystemService.findShortIsLater(reportTime, loadCityFcUltraDO);
                if (report) {
                    correctionStatusResp.setReportStatus(3);
                } else {
                    correctionStatusResp.setReportStatus(1);
                }
                correctionStatusResp
                    .setTodayReportTime(DateUtils.date2String(reportTime, DateFormatType.DATE_FORMAT_STR));
            }
        } else {
            correctionStatusResp.setReportStatus(2);
        }

        //上报时间要查询系统设置
        SystemData systemSetting = settingSystemService.getSystemSetting();
        //上报截止时间
        correctionStatusResp.setReportTime(systemSetting.getEndReportTime());

        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(correctionStatusResp);
        return baseResp;
    }


    /**
     * get autoForecast data and its ref data
     */
    @ApiOperation("获取预测负荷、基准日、相似日和高峰时间")
    @RequestMapping(value = "/auto", method = RequestMethod.GET)
    public BaseResp<UltraForecastResp> getAutoForecast(UltraForecastRequest forecastRequest) throws Exception {
        Date today = this.getSystemDate();
        BaseResp<UltraForecastResp> resp = BaseResp.succResp("查询成功");
        UltraForecastResp forecastResp = new UltraForecastResp();
        //目标日---》系统日期后一天
        //基准日---》目标日期前两天
        //预测曲线-》 预测负荷
        //相似日曲线》预测气象数据当作目标气象，查询相似日
        if (forecastRequest.getTargetDay() == null) {
            forecastRequest.setTargetDay(VslfDateUtil.getMoveDay(today, 1));
        }
        if (forecastRequest.getCityId() == null) {
            forecastRequest.setCityId(this.getLoginCityId());
        }
        if (forecastRequest.getCaliberId() == null) {
            forecastRequest.setCaliberId(this.getCaliberId());
        }
        String cityId = forecastRequest.getCityId();
        //预测曲线,算法id为空时,默认查询上报数据 否则查询对应算法
        if (StringUtils.isBlank(forecastRequest.getAlgorithmId())) {
            LoadCityFcUltraDO reportLoadCityFcDO = loadCityFcUltraService
                .getReportLoadCityFcDO(forecastRequest.getTargetDay(), cityId, forecastRequest.getCaliberId(),
                    IfEnum.YES,
                    forecastRequest.getTimeSpan(), Constants.ULTRA_FORECAST_1);
            UltraForecastDTO forecastDTO = new UltraForecastDTO();
            forecastDTO.setAlgorithmId(reportLoadCityFcDO.getAlgorithmId());
            forecastDTO.setCaliberId(reportLoadCityFcDO.getCaliberId());
            forecastDTO.setList(reportLoadCityFcDO.getTvData());
            forecastResp.setForecast(forecastDTO);
        } else {
            //查询预测曲线
            LoadCityFcUltraDO loadCityFcDO = loadCityFcUltraService
                .getLoadCityFcDO(forecastRequest.getTargetDay(), cityId, forecastRequest.getCaliberId(),
                    forecastRequest.getAlgorithmId(), forecastRequest.getTimeSpan(), Constants.ULTRA_FORECAST_1
                );
            if (loadCityFcDO == null) {
                BaseResp baseResp = BaseResp.failResp();
                baseResp.setRetCode("T706");
                baseResp.setRetMsg("数据为空");
                return baseResp;
            }
            UltraForecastDTO forecastDTO = new UltraForecastDTO();
            forecastDTO.setAlgorithmId(loadCityFcDO.getAlgorithmId());
            forecastDTO.setCaliberId(loadCityFcDO.getCaliberId());
            forecastDTO.setList(loadCityFcDO.getTvData());
            forecastResp.setForecast(forecastDTO);
            //查询最终上报的曲线
            LoadCityFcUltraDO report = loadCityFcUltraService
                .getReportLoadCityFcDO(forecastRequest.getTargetDay(), cityId, forecastRequest.getCaliberId(),
                    IfEnum.YES,
                    forecastRequest.getTimeSpan(), Constants.ULTRA_FORECAST_1);

            //如果是人工决策的id  则是查询最终上报的曲线
            if (forecastRequest.getAlgorithmId().equals(Constants.MD_ALGORITHM_ID)) {
                //如果算法id是人工决策的算法id 则是查询最终上报的曲线
                UltraForecastDTO forecastDTO1 = new UltraForecastDTO();
                if (report != null) {
                    forecastDTO1.setList(report.getTvData());
                    forecastResp.setForecast(forecastDTO1);
                }
            }
            if (forecastResp.getForecast() != null && forecastResp.getForecast().getList() != null) {
                forecastResp.setModifyLoad(forecastResp.getForecast().getList());
            } else {
                //如果查询的数据为空，则返回一条修正曲线为0的曲线。

                List<BigDecimal> reportLoad = new ArrayList<>();
                for (int i = 0; i < 96; i++) {
                    reportLoad.add(new BigDecimal(0));
                }
                forecastResp.setModifyLoad(reportLoad);
            }
            //有最终上报的曲线 展示最终上报的曲线
            if (report != null) {
                forecastResp.setReportLoad(report.getTvData());
                forecastResp.setModifyLoad(forecastResp.getReportLoad());
            }
        }
        List<LoadCityHisUltraBasicDO> loadCityHisVOS = loadCityHisUltraBasicService
            .getLoadCityHisDOS(cityId, forecastRequest.getCaliberId(),
                forecastRequest.getStartDate(),
                forecastRequest.getStartDate(), forecastRequest.getTimeSpan());
        if (!CollectionUtils.isEmpty(loadCityHisVOS)) {
            forecastResp.setBase(loadCityHisVOS.get(0).getTvData());
        }

        //气象取值----如果是省 则用省会城市的气象
        cityId = cityService.findWeatherCityId(cityId);
        forecastResp
            .setWeatherNameDTOS(weatherDataManagerService.findHisAllByDateAndCityId(cityId,
                forecastRequest.getTargetDay()));
        resp.setData(forecastResp);
        return resp;
    }


    /**
     * 结果上报
     */
    @ApiOperation("结果上报")
    @RequestMapping(value = "/auto", method = RequestMethod.POST)
    @OperateLog(operate = "结果上报")
    public BaseResp saveResult(@RequestBody @Validated UltraResultPushOrSaveRequest resultRequest, BindingResult result)
        throws Exception {
        validateFormValue(result);
        BaseResp resp = BaseResp.succResp("保存成功");
        if (StringUtils.isBlank(resultRequest.getCaliberId())) {
            resultRequest.setCaliberId(this.getCaliberId());
        }
        if (StringUtils.isBlank(resultRequest.getCityId())) {
            resultRequest.setCityId(this.getLoginCityId());
        }
//        BaseResp checkResp = checkReportDeadlineTime(resultRequest.getCityId(), AppSetting.ORDINARY_DAY,
//            resultRequest.getForecastList().getTargetDay(), resultRequest.getForecastList().getTargetDay(), null);
//        if (checkResp.getRetCode().equals("T0")) {
//            return checkResp;
//        }
        LoadCityFcUltraDO loadCityFcDO = forecastService.creatFcDO(resultRequest.getCityId(), resultRequest.getCaliberId(),
            new java.sql.Date(resultRequest.getForecastList().getTargetDay().getTime()), resultRequest.getTimeSpan(),
            Constants.MD_ALGORITHM_ID, resultRequest.getForecastList().getValue(), Constants.ULTRA_FORECAST_1,
            IfEnum.YES, false);
        try {
            loadCityFcUltraService.report(loadCityFcDO);
        } catch (BusinessException e) {
            if ("03D20180601".equals(e.getErrorCode())) {
                resp.setRetCode("T0");
                resp.setRetMsg("操作失败 超过上报时间，不允许上报！");
                return resp;
            }
        }

        //统计预测负荷特性
        loadFeatureStatService
            .doStatLoadFeatureCityDayFc(Arrays.asList(resultRequest.getCityId()), loadCityFcDO.getDateTime(),
                loadCityFcDO.getDateTime(),
                loadCityFcDO.getCaliberId());

        return resp;
    }


    /**
     * 曲线修正
     */
    @ApiOperation("曲线修正")
    @RequestMapping(value = "/auto/recorrect", method = RequestMethod.POST)
    @OperateLog(operate = "曲线修正")

    public BaseResp<SmoothLineResp> recorrectLoad(@Validated @RequestBody RecorrectRequest recorrectRequest,
        BindingResult result)
        throws Exception {
        validateFormValue(result);
        BaseResp<SmoothLineResp> resp = BaseResp.succResp("修正完成");
        SmoothLineResp smoothLineResp = new SmoothLineResp();
        smoothLineResp.setForecast(forecastService
            .recorrectLoad(recorrectRequest.getForecast(), recorrectRequest.getMaxLoad(),
                recorrectRequest.getMinLoad()));
        resp.setData(smoothLineResp);
        return resp;
    }

    /**
     * 获取预测特性
     */
    @ApiOperation("获取预测特性")
    @RequestMapping(value = "/get/character", method = RequestMethod.GET)
    public BaseResp<UltraLoadAndWeatherFeatureFcResp> getForecastCharacter(@ApiParam(value = "城市id") String cityId,
        @ApiParam(value = "算法id") String algorithmId, @ApiParam(value = "日期") Date date,
        @ApiParam(value = "时间类型") Integer timeSpan) throws Exception {
        String weatherCityId = cityService.findWeatherCityId(cityId);
        List<WeatherCityFcDO> weatherFeature = this.weatherCityFcService
            .findWeatherCityFcDOs(weatherCityId, null, date, date);
        WeatherFeatureCityDayFcDO weatherFcVo = new WeatherFeatureCityDayFcDO();
        if (weatherFeature.size() != 0) {
            weatherFcVo = weatherFeatureStatService
                .getStatWeatherByCityAndType(weatherFeature);
        }
        String caliberId = super.getCaliberId();
        LoadCityFcUltraDO loadCityFc = this.loadCityFcUltraService
            .getLoadCityFcDO(date, cityId, caliberId, algorithmId, timeSpan, Constants.ULTRA_FORECAST_1);
        if (weatherFeature.size() == 0 && loadCityFc == null) {
            BaseResp baseResp = BaseResp.failResp();
            baseResp.setRetCode("T706");
            baseResp.setRetMsg("数据为空");
            return baseResp;
        }
        UltraLoadFeatureFcDTO loadFeatureFcDTO = this.loadFeatureStatService.findStatisticsDayFeature(loadCityFc);
        UltraLoadAndWeatherFeatureFcResp resp = new UltraLoadAndWeatherFeatureFcResp(weatherFcVo,
            loadFeatureFcDTO);
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(resp);
        return baseResp;
    }

    /**
     * 曲线平滑
     */
    @ApiOperation("曲线平滑")
    @RequestMapping(value = "/auto/data", method = RequestMethod.POST)
    public BaseResp<SmoothLineResp> smoothLine(
        @RequestBody SmoothLineRequest smoothLineRequest) throws Exception {
        BaseResp<SmoothLineResp> resp = BaseResp.succResp("平滑成功");
        SmoothLineResp smoothLineResp = new SmoothLineResp();
        smoothLineResp.setForecast(forecastService
            .smoothLineVslf(smoothLineRequest.getForecast(), smoothLineRequest.getStartTime().replace(":", ""),
                smoothLineRequest.getEndTime().replace(":", "")));
        resp.setData(smoothLineResp);
        return resp;
    }


    /**
     * 导入预测负荷
     */
    @ApiOperation("预测负荷导入")
    @RequestMapping(value = "/auto/import", method = RequestMethod.POST)
    @OperateLog(operate = "预测负荷导入")
    public BaseResp importForecastData(
        @RequestParam(value = "forecastFile", required = false) MultipartFile forecastFile,
        @ApiParam(value = "城市id") String cityId, Integer timeSpan)
        throws Exception {
        boolean check = CheckoutFileTypeSecurity.checkoutFileTypeSecurity(forecastFile);
        if (!check) {
            return BaseResp.failResp("文件安全性存在问题");
        }
//        if (DataPointListMeta.MINUTE_15 == timeSpan) {
//            EasyExcel.read(forecastFile.getInputStream(),
//                ExcelMoreDayForecastData96.class,
//                new ExcelMoreDayForecast96Listener(loadCityFcService, cityId, "1", "1"))
//                .sheet()
//                .headRowNumber(1).doRead();
//        } else {
//            EasyExcel.read(forecastFile.getInputStream(),
//                ExcelMoreDayForecastData288.class,
//                new ExcelMoreDayForecast288Listener(loadCityFcService, cityId, "1", "1"))
//                .sheet()
//                .headRowNumber(1).doRead();
//        }
        EasyExcel.read(forecastFile.getInputStream(),
            ExcelMoreDayForecastData96.class,
            new ExcelMoreDayForecast96Listener(loadCityFcUltraService, "1", "1", "1"))
            .sheet()
            .headRowNumber(1).doRead();

        return BaseResp.succResp("导入成功");
    }

    /**
     * 导入预测负荷
     */
    @ApiOperation("导入气象数据")
    @RequestMapping(value = "/auto/import/weather", method = RequestMethod.POST)
    @OperateLog(operate = "导入气象数据")
    public BaseResp importWeatherData(
        @RequestParam(value = "forecastFile", required = false) MultipartFile forecastFile,
        @ApiParam(value = "城市id") String cityId, Integer timeSpan)
        throws Exception {
        boolean check = CheckoutFileTypeSecurity.checkoutFileTypeSecurity(forecastFile);
        if (!check) {
            return BaseResp.failResp("文件安全性存在问题");
        }
        EasyExcel.read(forecastFile.getInputStream(),
            ExcelMoreDayWeatherDataHis96.class,
            new ExcelMoreDayWeather96Listener(weatherCityHisService, weatherCityFcService, "1", "1", "1"))
            .sheet()
            .headRowNumber(1).doRead();

        return BaseResp.succResp("导入成功");
    }


    /**
     * 导入测试负荷
     */
    @ApiOperation("测试负荷导入")
    @RequestMapping(value = "/auto/import/testHis", method = RequestMethod.POST)
    @OperateLog(operate = "测试负荷导入")
    public BaseResp importForecastDataTest(
        @RequestParam(value = "forecastFile", required = false) MultipartFile forecastFile,
        @ApiParam(value = "城市id") String cityId)
        throws Exception {
        EasyExcel.read(forecastFile.getInputStream(),
            ExcelMoreDayForecastDataHis96.class,
            new ExcelMoreDayForecast96HisListener(loadCityHisUltraBasicService, cityId, "1", "1"))
            .sheet()
            .headRowNumber(1).doRead();
        EasyExcel.read(forecastFile.getInputStream(),
            ExcelMoreDayForecastDataHis96.class,
            new ExcelMoreDayForecast288HisListener(loadCityHisUltraBasicService, cityId, "1", "1"))
            .sheet()
            .headRowNumber(1).doRead();

        return BaseResp.succResp("导入成功");
    }


    /**
     * 导入测试测试负荷
     */
    @ApiOperation("预测测试负荷导入")
    @RequestMapping(value = "/auto/import/testFc", method = RequestMethod.POST)
    @OperateLog(operate = "测试负荷导入")
    public BaseResp importForecastDataTestFc(
        @RequestParam(value = "forecastFile", required = false) MultipartFile forecastFile,
        @ApiParam(value = "城市id") String cityId)
        throws Exception {
        EasyExcel.read(forecastFile.getInputStream(),
            ExcelMoreDayForecastDataHis96.class,
            new ExcelMoreDayForecast96FcListener(loadCityFcUltraService, loadCityHisUltraBasicService, cityId, "1", "1"))
            .sheet()
            .headRowNumber(1).doRead();
        EasyExcel.read(forecastFile.getInputStream(),
            ExcelMoreDayForecastDataHis96.class,
            new ExcelMoreDayForecast288FcListener(loadCityFcUltraService, loadCityHisUltraBasicService, cityId, "1", "1"))
            .sheet()
            .headRowNumber(1).doRead();

        return BaseResp.succResp("导入成功");
    }



    @ApiOperation("添加参照曲线")
    @RequestMapping(value = "/addReference", method = RequestMethod.GET)
    @OperateLog(operate = "添加参照曲线")
    public BaseResp<UltraReferenceResponse> getReferenceLoad(UltraReferenceRequest referenceRequest, String algorithmId,
        String type) throws Exception {
        if (StringUtils.isBlank(referenceRequest.getCaliberId())) {
            referenceRequest.setCaliberId(this.getCaliberId());
        }
        if (StringUtils.isBlank(referenceRequest.getCityId())) {
            referenceRequest.setCityId(this.getLoginCityId());
        }

        String caliberId = referenceRequest.getCaliberId();
        String cityId = referenceRequest.getCityId();
        Date date = referenceRequest.getReferenceDate();
        List<BigDecimal> load = new ArrayList<>();
        if (Constants.HIS_TYPE.equals(type)) {
            LoadCityHisUltraBasicDO loadCityHisDO = loadCityHisUltraBasicService
                .getLoadCityHisDO(cityId, caliberId, date, referenceRequest.getTimeSpan());
            if (loadCityHisDO == null) {
                return new BaseResp("T706");
            }
            load = loadCityHisDO.getTvData();
        } else if (Constants.REPORT_TYPE.equals(type)) {
            LoadCityFcUltraDO reportLoadCityFcDO = loadCityFcUltraService
                .getReportLoadCityFcDO(date, cityId, caliberId, IfEnum.YES, referenceRequest.getTimeSpan(),
                    Constants.ULTRA_FORECAST_1);
            if (reportLoadCityFcDO == null) {
                return new BaseResp("T706");
            }
            load = reportLoadCityFcDO.getTvData();
        }
        if (algorithmId != null) {
            LoadCityFcUltraDO reportLoadCityFcDO = loadCityFcUltraService
                .getLoadCityFcDO(date, cityId, caliberId, algorithmId, referenceRequest.getTimeSpan(),
                    Constants.ULTRA_FORECAST_1);
            if (reportLoadCityFcDO == null) {
                return new BaseResp("T706");
            }
            load = reportLoadCityFcDO.getTvData();
        }
        cityId = cityService.findWeatherCityId(cityId);
        List<WeatherNameDTO> weatherCityHisVOS;
        if (Constants.HIS_TYPE.equals(type)) {
            weatherCityHisVOS = weatherDataManagerService
                .findHisAllByDateAndCityId(cityId, referenceRequest.getReferenceDate());
        } else {
            weatherCityHisVOS = weatherDataManagerService
                .findFcAllByDateAndCityId(cityId, referenceRequest.getReferenceDate());
        }
        BaseResp baseResp = BaseResp.succResp();
        UltraReferenceResponse response = new UltraReferenceResponse();
        response.setDate(referenceRequest.getReferenceDate());
        CityDO city = cityService.findCityById(referenceRequest.getCityId());
        String dateStr = DateUtils.date2String(date, DateFormatType.SIMPLE_DATE_FORMAT_STR);
        response.setReferenceName(city.getCity() + "-" + dateStr);
        response.setLoad(load);
        response.setWeatherLoad(weatherCityHisVOS);
        baseResp.setData(response);
        return baseResp;
    }

}
