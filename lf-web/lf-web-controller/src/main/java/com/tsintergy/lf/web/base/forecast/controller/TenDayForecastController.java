package com.tsintergy.lf.web.base.forecast.controller;

import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcService;
import com.tsintergy.lf.serviceapi.base.forecast.api.TenDaysLoadForecastService;
import com.tsintergy.lf.serviceapi.base.forecast.bean.DuplicateForecastException;
import com.tsintergy.lf.serviceapi.base.forecast.dto.TenDayForecastCurveDTO;
import com.tsintergy.lf.serviceapi.base.forecast.dto.TenDayForecastDTO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.web.base.controller.CommonBaseController;
import com.tsintergy.lf.web.base.forecast.request.ManualRequest3;
import com.tsintergy.lf.web.base.forecast.request.ManualRequest4;
import com.tsintergy.lf.web.base.forecast.response.ReportResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description: 十日预测
 *
 * <AUTHOR>
 * @create 2023-03-06
 * @since 1.0.0
 */
@Api(tags = "十日预测")
@RequestMapping("/forecast")
@RestController
public class TenDayForecastController extends CommonBaseController {

    @Autowired
    private TenDaysLoadForecastService tenDaysLoadForecastService;

    @Autowired
    private LoadCityFcService loadCityFcService;

    @Autowired
    private ForecastManager forecastManager;

    @ApiOperation("十日负荷预测")
    @GetMapping("/tenDayForecast")
    public BaseResp<List<TenDayForecastDTO>> queryTenDayForecast(String cityId, Date startDate, Date endDate,
        String algorithmId) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<TenDayForecastDTO> result = tenDaysLoadForecastService.getListLoadCityFc(cityId, getCaliberId(cityId),
            startDate, endDate, algorithmId);
        if (!Objects.isNull(result)) {
            baseResp.setData(result);
            return baseResp;
        }
        return new BaseResp("T706");
    }

    @ApiOperation("十日负荷预测曲线")
    @GetMapping("/tenDayForecastCurve")
    public BaseResp<TenDayForecastCurveDTO> queryTenDayForecastCurve(String cityId, Date startDate, Date endDate,
        String algorithmId) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        TenDayForecastCurveDTO result = tenDaysLoadForecastService.getListLoadCityFcCurve(cityId, getCaliberId(cityId),
            startDate, endDate, algorithmId);
        if (!Objects.isNull(result)) {
            baseResp.setData(result);
            return baseResp;
        }
        return new BaseResp("T706");
    }

    /**
     * 调用预测算法
     */
    @ApiOperation("手动预测")
    @RequestMapping(value = "/algorithm/tenDayForecast", method = RequestMethod.POST)
    public BaseResp<Boolean> forecast(@RequestBody ManualRequest3 manualRequest3) throws Exception {
        List<String> algorithmIds = manualRequest3.getAlgorithmIds();
        List<AlgorithmEnum> algorithmEnums = new ArrayList<>();
        for (AlgorithmEnum algorithmEnum : AlgorithmEnum.values()) {
            if (algorithmIds.contains(algorithmEnum.getId())) {
                algorithmEnums.add(algorithmEnum);
            }
        }
        BaseResp baseResp = BaseResp.succResp();
        List<LoadCityFcDO> forecastResult = loadCityFcService.getLoadFcByAlgorithmId(
            manualRequest3.getStartDate(), super.getLoginCityId(), super.getCaliberId(),
            manualRequest3.getAlgorithmIds());
        if (!CollectionUtils.isEmpty(forecastResult)) {
            for (LoadCityFcDO loadCityFcDO : forecastResult) {
                loadCityFcDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
                loadCityFcService.doSaveOrUpdateLoadCityFcDO96(loadCityFcDO);
            }
        }
        try {
            forecastManager.forecast(super.getLoginUserId(), super.getLoginCityId(), super.getCaliberId(),
                manualRequest3.getStartDate(), manualRequest3.getEndDate(), algorithmEnums,
                manualRequest3.getWeatherCode());
            baseResp.setData(true);
        } catch (DuplicateForecastException e) {
            baseResp.setData(false);
        } catch (Exception e) {
            baseResp = BaseResp.failResp("调用预测算法失败...");
        }
        return baseResp;
    }

    /**
     * 调用预测算法（中长期）
     */
    @ApiOperation("手动预测")
    @RequestMapping(value = "/algorithm/LongTermForecast", method = RequestMethod.POST)
    public BaseResp<Boolean> longTermForecast(@RequestBody ManualRequest4 manualRequest3) throws Exception {
        String startDate = manualRequest3.getStartDate();
        Date firstDay = null;
        Date lastDay = null;
        if ("06".equals(startDate.split("-")[1])) {
            firstDay = DateUtil.getFirstDay(Integer.valueOf(startDate.split("-")[0]), Integer.valueOf(startDate.split("-")[1]));
            startDate = startDate.split("-")[0] + "-09";
            lastDay = DateUtil.getLastDay(Integer.valueOf(startDate.split("-")[0]), Integer.valueOf(startDate.split("-")[1]));
        } else if ("01".equals(startDate.split("-")[1])) {
            firstDay = DateUtil.getFirstDay(Integer.valueOf(startDate.split("-")[0]), Integer.valueOf(startDate.split("-")[1]));
            startDate = startDate.split("-")[0] + "-12";
            lastDay = DateUtil.getLastDay(Integer.valueOf(startDate.split("-")[0]), Integer.valueOf(startDate.split("-")[1]));
        } else if ("11".equals(startDate.split("-")[1])) {
            firstDay = DateUtil.getFirstDay(Integer.valueOf(startDate.split("-")[0]), Integer.valueOf(startDate.split("-")[1]));
            startDate = startDate.split("-")[0] + "-02";
            lastDay = DateUtil.getLastDay(Integer.valueOf(startDate.split("-")[0])+1, Integer.valueOf(startDate.split("-")[1]));
        } else {
            // 月度
            firstDay = DateUtil.getFirstDay(Integer.valueOf(startDate.split("-")[0]), Integer.valueOf(startDate.split("-")[1]));
            lastDay = DateUtil.getLastDay(Integer.valueOf(startDate.split("-")[0]), Integer.valueOf(startDate.split("-")[1]));
        }
        List<String> algorithmIds = manualRequest3.getAlgorithmIds();
        List<AlgorithmEnum> algorithmEnums = new ArrayList<>();
        for (AlgorithmEnum algorithmEnum : AlgorithmEnum.values()) {
            if (algorithmIds.contains(algorithmEnum.getId())) {
                algorithmEnums.add(algorithmEnum);
            }
        }
        BaseResp baseResp = BaseResp.succResp();
        List<LoadCityFcDO> forecastResult = loadCityFcService.getLoadFcByAlgorithmId(
            firstDay, super.getLoginCityId(), super.getCaliberId(),
            manualRequest3.getAlgorithmIds());
        if (!CollectionUtils.isEmpty(forecastResult)) {
            for (LoadCityFcDO loadCityFcDO : forecastResult) {
                loadCityFcDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
                loadCityFcService.doSaveOrUpdateLoadCityFcDO96(loadCityFcDO);
            }
        }
        try {
            forecastManager.forecast(super.getLoginUserId(), super.getLoginCityId(), super.getCaliberId(),
                firstDay, lastDay, algorithmEnums,
                manualRequest3.getWeatherCode());
            baseResp.setData(true);
        } catch (DuplicateForecastException e) {
            baseResp.setData(false);
        } catch (Exception e) {
            baseResp = BaseResp.failResp("调用预测算法失败...");
        }
        return baseResp;
    }

    /**
     * 查看上报状态
     */
    @ApiOperation("查看上报状态")
    @RequestMapping(value = "/report/status", method = RequestMethod.GET)
    public BaseResp<ReportResp> manualReport(Date date, String algorithmId) throws Exception {
        LoadCityFcDO report = loadCityFcService.getReport(super.getLoginCityId(), super.getCaliberId(), date);
        BaseResp baseResp = BaseResp.succResp();
        ReportResp reportResp = new ReportResp(false, null);
        if (report != null) {
            reportResp = new ReportResp(true, report.getReportTime());
        }
        LoadCityFcDO loadCityFc = loadCityFcService.getLoadCityFc(super.getLoginCityId(), super.getCaliberId(), date, date, algorithmId);
        if (loadCityFc != null) {
            reportResp.setForecastTime(DateUtils.date2String((Date)loadCityFc.getUpdatetime(), DateFormatType.DATE_FORMAT_STR));
        }
        baseResp.setData(reportResp);
        return baseResp;
    }

    /*@ApiOperation("批量上报接口")
    @RequestMapping(value = "/tenDayReport", method = RequestMethod.POST)
    public BaseResp report(@RequestBody TenDayExistRequest request) throws Exception {
        // 算法列表
        List<String> ids = request.getIds();
        for (LoadCityFcDTO DTO : request.getLoadCityFcDTOS()) {
            LoadCityFcDO fcVO = new LoadCityFcDO();
            BeanUtils.copyProperties(DTO, fcVO);
            fcVO.setReport(true);
            fcVO.setReportTime(new Timestamp(System.currentTimeMillis()));
            fcVO.setCityId(DTO.getCityId());
            fcVO.setRecommend(false);
            fcVO.setCaliberId(getCaliberId(DTO.getCityId()));
            BasePeriodUtils.setAllFiled(fcVO,
                ColumnUtil.listToMap(DTO.getData(), Constants.LOAD_CURVE_START_WITH_ZERO));
            for (String id : ids) {
                LoadCityFcDO exist = loadCityFcService
                    .getLoadCityFc(DTO.getCityId(), getCaliberId(DTO.getCityId()), DTO.getDate(), DTO.getDate(), id);
                if (exist != null) {
                    fcVO.setId(exist.getId());
                    fcVO.setAlgorithmId(id);
                    fcVO.setReport(exist.getReport());
                    this.loadCityFcService.doRemoveLoadCityFcDO(exist);
                    this.loadCityFcService
                        .doSaveOrUpdateLoadCityFcDO96(fcVO);
                }
            }
        }
        return BaseResp.succResp();
    }*/

    private String getCaliberId(String cityId){
        if (Constants.PROVINCE_TYPE == Integer.parseInt(cityId)){
            return Constants.CALIBER_ID_BG_QW;
        }else{
            return Constants.CALIBER_ID_BG_DS;
        }
    }

}
