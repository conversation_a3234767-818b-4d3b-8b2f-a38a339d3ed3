package com.tsintergy.lf.web.base.bgd.controller;

import cn.hutool.json.JSONUtil;
import com.alibaba.excel.util.CollectionUtils;
import com.github.liaochong.myexcel.utils.StringUtil;
import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.bgd.api.MonthForecastModelService;
import com.tsintergy.lf.serviceapi.base.bgd.dto.FeatureDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.MonthManualForecastDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.MonthReportDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.MonthTypicalCurveDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.MonthWeatherDefaultDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.MonthWeatherResultDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.MonthWeatherSettingDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.TenDaysOfMonthAccuracyDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.TenDaysOfMonthFeatureDTO;
import com.tsintergy.lf.serviceapi.base.system.api.ReportSystemService;
import com.tsintergy.lf.serviceapi.base.system.pojo.ReportSystemInitDO;
import com.tsintergy.lf.web.base.common.util.ExcelUtil;
import io.swagger.annotations.ApiOperation;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

/**
 * Description:  <br>
 * 预测模型月维度预测
 * @Author: <EMAIL>
 * @Date: 2022/11/17 15:37
 * @Version: 1.0.0
 */
@RestController
@RequestMapping("/fcModel")
public class MonthForecastController {

    @Autowired
    private MonthForecastModelService monthForecastModelService;

    @Autowired
    private ReportSystemService reportSystemService;

    /**
     * 是否可以预测(true:可以预测；false:不能预测）
     */
    private static final Map<String, Boolean> forecastStatus = new ConcurrentHashMap<>();

    private static final Map<String, Boolean> forecastStatus1 = new ConcurrentHashMap<>();

    private static final Map<String, Boolean> forecastStatus2 = new ConcurrentHashMap<>();

    // 分旬
    //String url = "http://************:81/lf_bgd/web/fcModel/month/forecastAgain?cityId={cityId}&algorithmId={algorithmId}&forecastDay={forecastDay}&status={status}";
    String url = "http://***********:8001/lf_hb/web/fcModel/month/forecastAgain?cityId={cityId}&algorithmId={algorithmId}&forecastDay={forecastDay}&status={status}";
     //String url = "http://localhost:8307/lf/web/fcModel/month/forecastAgain?cityId={cityId}&algorithmId={algorithmId}&forecastDay={forecastDay}&status={status}";

    // 月度
    //String url1 = "http://************:81/lf_bgd/web/fcModel/quarter/forecastAgain?cityId={cityId}&algorithmId={algorithmId}&forecastDay={forecastDay}&endDay={endDay}&season={season}&status={status}";
    String url1 = "http://***********:8001/lf_hb/web/fcModel/quarter/forecastAgain?cityId={cityId}&algorithmId={algorithmId}&forecastDay={forecastDay}&endDay={endDay}&season={season}&status={status}";
     //String url1 = "http://localhost:8307/lf/web/fcModel/quarter/forecastAgain?cityId={cityId}&algorithmId={algorithmId}&forecastDay={forecastDay}&endDay={endDay}&season={season}&status={status}";

    // 年度
    //String url2 = "http://************:81/lf_bgd/web/fcModel/year/forecastAgain?cityId={cityId}&algorithmId={algorithmId}&forecastDay={forecastDay}&endDay={endDay}&status={status}";
    String url2 = "http://***********:8001/lf_hb/web/fcModel/year/forecastAgain?cityId={cityId}&algorithmId={algorithmId}&forecastDay={forecastDay}&endDay={endDay}&status={status}";
    // String url2 = "http://localhost:8307/lf/web/fcModel/year/forecastAgain?cityId={cityId}&algorithmId={algorithmId}&forecastDay={forecastDay}&endDay={endDay}&status={status}";

    @ApiOperation("获取月度负荷预测")
    @GetMapping(value = "/month/getMonthLoadFc")
    public BaseResp<List<TenDaysOfMonthFeatureDTO>> getMonthLoadFc(String cityId, String caliberId, Date date, String algorithmId){
        BaseResp baseResp = BaseResp.succResp();
        List<TenDaysOfMonthFeatureDTO> tenDaysOfMonthFeatureDTOList = monthForecastModelService.getMonthLoadFc(cityId,getCaliberId(cityId),date,algorithmId);
        baseResp.setData(tenDaysOfMonthFeatureDTOList);
        return baseResp;
    }

    @ApiOperation("获取月度典型曲线")
    @GetMapping(value = "/month/getMonthTypicalCurve")
    public BaseResp<MonthTypicalCurveDTO> getMonthTypicalCurve(String cityId, Date date){
        BaseResp baseResp = BaseResp.succResp();
        MonthTypicalCurveDTO monthTypicalCurve = monthForecastModelService.getMonthTypicalCurve(cityId,
            getCaliberId(cityId), date);
        baseResp.setData(monthTypicalCurve);
        return baseResp;
    }

    @ApiOperation("获取历史准确率")
    @GetMapping(value = "/month/getHistoryAccuracy")
    public BaseResp<List<TenDaysOfMonthAccuracyDTO>> getHistoryAccuracy(String cityId, String caliberId, Date date, Integer type)
        throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<TenDaysOfMonthAccuracyDTO> historyAccuracy = monthForecastModelService.getHistoryAccuracy(cityId,
            getCaliberId(cityId), date, type);
        baseResp.setData(historyAccuracy);
        return baseResp;
    }

    @ApiOperation("获取校核上报")
    @GetMapping(value = "/month/getReportList")
    public BaseResp<List<FeatureDTO>> getReportList(String cityId,String caliberId,String algorithmName,Date date){
        BaseResp baseResp = BaseResp.succResp();
        List<FeatureDTO> tenDaysOfMonthAccuracyDTOList = monthForecastModelService.getReportList(cityId,getCaliberId(cityId),algorithmName,date);
        baseResp.setData(tenDaysOfMonthAccuracyDTOList);
        return baseResp;
    }

    @ApiOperation("上报")
    @PostMapping(value = "/month/report")
    public BaseResp<String> report(@RequestBody List<MonthReportDTO> monthReportDTOS){
        BaseResp baseResp = BaseResp.succResp();
        monthForecastModelService.report(monthReportDTOS);
        baseResp.setRetMsg("上报成功");
        return baseResp;
    }

    @ApiOperation("获取最新上报时间")
    @GetMapping(value = "/month/getReportTime")
    public BaseResp<String> getReportTime(String cityId,String caliberId,Date date){
        BaseResp baseResp = BaseResp.succResp();
        String result =  monthForecastModelService.getReportTime(cityId,getCaliberId(cityId),date);
        baseResp.setData(result);
        return baseResp;
    }

    @ApiOperation("获取最新预测时间")
    @GetMapping(value = "/month/getForecastTime")
    public BaseResp<String> getForecastTime(String cityId,Date date,String algorithmId){
        BaseResp baseResp = BaseResp.succResp();
        String result =  monthForecastModelService.getForecastTime(cityId,getCaliberId(cityId),date, algorithmId);
        baseResp.setData(result);
        return baseResp;
    }

    @ApiOperation("获取冬夏年预测时间")
    @GetMapping(value = "/season/getForecastTime")
    public BaseResp<String> getSeasonForecastTime(String cityId,Date date,String algorithmId,String season){
        BaseResp baseResp = BaseResp.succResp();
        String result =  monthForecastModelService.getSeasonForecastTime(cityId,getCaliberId(cityId),date, algorithmId, season);
        baseResp.setData(result);
        return baseResp;
    }

    @ApiOperation("获取月度负荷预测对比")
    @GetMapping(value = "/month/getMonthLoadFcCompare")
    public BaseResp<List<TenDaysOfMonthFeatureDTO>> getMonthLoadFcCompare(String cityId, String caliberId, Date date, String algorithmId)
        throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<TenDaysOfMonthFeatureDTO> tenDaysOfMonthFeatureDTOList = monthForecastModelService.getMonthLoadFcCompare(cityId,getCaliberId(cityId),date,algorithmId);
        baseResp.setData(tenDaysOfMonthFeatureDTOList);
        return baseResp;
    }

    private String getCaliberId(String cityId){
        if (Constants.PROVINCE_TYPE == Integer.parseInt(cityId)){
            return Constants.CALIBER_ID_BG_QW;
        }else{
            return Constants.CALIBER_ID_BG_DS;
        }
    }

    @ApiOperation("获取气象概况")
    @GetMapping(value = "/getTenDayAccuracy")
    public BaseResp<List<FeatureDTO>> getWeatherInfo(String cityId, Date date,Integer type,String algorithmId) {
        BaseResp baseResp = BaseResp.succResp();
        if (StringUtil.isBlank(cityId)){
            cityId = String.valueOf(Constants.PROVINCE_TYPE);
        }
        List<FeatureDTO> tenDayAccuracy = new ArrayList<>(16);
        try {
            tenDayAccuracy = monthForecastModelService.getTenDayAccuracy(cityId, date, type, algorithmId, getCaliberId(cityId));
            baseResp.setData(tenDayAccuracy);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (CollectionUtils.isEmpty(tenDayAccuracy)) {
            return new BaseResp("T200");
        }
        return baseResp;
    }

    /**
     * 判断手动预测算法是否可以预测
     */
    @ApiOperation("判断手动预测算法是否可以预测")
    @RequestMapping(value = "/month/fcStatus", method = RequestMethod.GET)
    public BaseResp<Integer> isForecast() {
        BaseResp baseResp = BaseResp.succResp();
        if (forecastStatus.size() == 0) {
            baseResp.setData(1);
        } else {
            boolean isForecast = forecastStatus.get("1");
            if (isForecast) {
                baseResp.setData(1);
            } else {
                baseResp.setData(0);
            }
        }
        return baseResp;
    }

    @ApiOperation("判断手动预测算法是否可以预测")
    @RequestMapping(value = "/season/fcStatus", method = RequestMethod.GET)
    public BaseResp<Integer> isSeasonForecast() {
        BaseResp baseResp = BaseResp.succResp();
        if (forecastStatus1.size() == 0) {
            baseResp.setData(1);
        } else {
            boolean isForecast = forecastStatus1.get("1");
            if (isForecast) {
                baseResp.setData(1);
            } else {
                baseResp.setData(0);
            }
        }
        return baseResp;
    }

    @ApiOperation("判断手动预测算法是否可以预测")
    @RequestMapping(value = "/year/fcStatus", method = RequestMethod.GET)
    public BaseResp<Integer> isYearForecast() {
        BaseResp baseResp = BaseResp.succResp();
        if (forecastStatus2.size() == 0) {
            baseResp.setData(1);
        } else {
            boolean isForecast = forecastStatus2.get("1");
            if (isForecast) {
                baseResp.setData(1);
            } else {
                baseResp.setData(0);
            }
        }
        return baseResp;
    }

    @ApiOperation("分旬重新预测")
    @PostMapping(value = "/month/forecastAgain")
    public BaseResp<String> forecastAgain(@RequestBody MonthManualForecastDTO monthForecastDTO){
        BaseResp baseResp = BaseResp.succResp();
        // Http请求参数
        String cityId = monthForecastDTO.getCityId();
        RestTemplate restTemplate = new RestTemplate();
        String dateToStr = DateUtil.getDateToStr(monthForecastDTO.getForecastDay());
        String result = null;
        String status = monthForecastDTO.getStatus();
        for (String algorithmId : monthForecastDTO.getAlgorithmIds()) {
            forecastStatus.put("1", false);
            result = restTemplate.getForObject(url, String.class, cityId, algorithmId, dateToStr, status);
        }
        forecastStatus.put("1", true);
        if (result != null) {
            BaseResp baseResp1 = JSONUtil.toBean(JSONUtil.toJsonStr(result), BaseResp.class);
            if ("T200".equals(baseResp1.getRetCode())) {
                return baseResp;
            } else {
                return new BaseResp("T701");
            }
        } else {
            return new BaseResp("T706");
        }
    }

    @ApiOperation("月度重新预测")
    @PostMapping(value = "/season/forecastAgain")
    public BaseResp<String> seasonForecastAgain(@RequestBody MonthManualForecastDTO monthForecastDTO){
        BaseResp baseResp = BaseResp.succResp();
        // Http请求参数
        String cityId = monthForecastDTO.getCityId();
        RestTemplate restTemplate = new RestTemplate();
        String dateToStr = DateUtil.getDateToStr(monthForecastDTO.getForecastDay());
        String result = null;
        String status = monthForecastDTO.getStatus();
        Date endDay = monthForecastDTO.getEndDay();
        for (String algorithmId : monthForecastDTO.getAlgorithmIds()) {
            forecastStatus1.put("1", false);
            result = restTemplate.getForObject(url1, String.class, cityId, algorithmId, dateToStr, DateUtil.getDateToStr(endDay), monthForecastDTO.getSeason(), status);
        }
        forecastStatus1.put("1", true);
        if (result != null) {
            BaseResp baseResp1 = JSONUtil.toBean(JSONUtil.toJsonStr(result), BaseResp.class);
            if ("T200".equals(baseResp1.getRetCode())) {
                return baseResp;
            } else {
                return new BaseResp("T701");
            }
        } else {
            return new BaseResp("T706");
        }
    }

    @ApiOperation("年度重新预测")
    @PostMapping(value = "/year/forecastAgain")
    public BaseResp<String> yearForecastAgain(@RequestBody MonthManualForecastDTO monthForecastDTO) {
        BaseResp baseResp = BaseResp.succResp();
        // Http请求参数
        String cityId = monthForecastDTO.getCityId();
        RestTemplate restTemplate = new RestTemplate();
        String dateToStr = DateUtil.getDateToStr(monthForecastDTO.getForecastDay());
        String result = null;
        String status = monthForecastDTO.getStatus();
        Date endDay = monthForecastDTO.getEndDay();
        for (String algorithmId : monthForecastDTO.getAlgorithmIds()) {
            forecastStatus2.put("1", false);
            result = restTemplate.getForObject(url2, String.class, cityId, algorithmId, dateToStr,
                DateUtil.getDateToStr(endDay), status);
        }
        forecastStatus2.put("1", true);
        if (result != null) {
            BaseResp baseResp1 = JSONUtil.toBean(JSONUtil.toJsonStr(result), BaseResp.class);
            if ("T200".equals(baseResp1.getRetCode())) {
                return baseResp;
            } else {
                return new BaseResp("T701");
            }
        } else {
            return new BaseResp("T706");
        }
    }

    @ApiOperation("获取气象设置")
    @PostMapping(value = "/month/getWeatherSetting")
    public BaseResp<List<MonthWeatherResultDTO>> getWeatherSetting(
        @RequestBody MonthWeatherSettingDTO monthWeatherSettingDTO) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<MonthWeatherResultDTO> weatherSetting = monthForecastModelService.getWeatherSetting(
            monthWeatherSettingDTO);
        if (CollectionUtils.isEmpty(weatherSetting)) {
            return new BaseResp("T200");
        }
        baseResp.setData(weatherSetting);
        return baseResp;
    }

    @ApiOperation("保存气象设置")
    @PostMapping(value = "/month/saveWeatherSetting")
    public BaseResp<Void> saveWeatherSetting(@RequestBody List<MonthWeatherResultDTO> monthWeatherResultDTOS)
        throws Exception {
        monthForecastModelService.saveWeatherSetting(monthWeatherResultDTOS);
        return BaseResp.succResp();
    }

    @ApiOperation("获取气象配置信息")
    @GetMapping(value = "/month/getMonthWeather")
    public BaseResp<MonthWeatherDefaultDTO> getMonthWeather(String cityId, String dateStr, String season) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        MonthWeatherDefaultDTO monthWeather = monthForecastModelService.getMonthWeather(cityId, dateStr, season);
        if (monthWeather == null) {
            return new BaseResp("T200");
        }
        baseResp.setData(monthWeather);
        return baseResp;
    }

    @ApiOperation("获取气象信息")
    @GetMapping(value = "/month/getWeatherInfo")
    public BaseResp<List<MonthWeatherResultDTO>> getWeatherInfo(String dateStr, String cityId) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<MonthWeatherResultDTO> weatherSetting = monthForecastModelService.getWeatherInfo(dateStr, cityId);
        if (CollectionUtils.isEmpty(weatherSetting)) {
            return new BaseResp("T200");
        }
        baseResp.setData(weatherSetting);
        return baseResp;
    }

    @ApiOperation("月度导入上报结果")
    @RequestMapping(value = "/month/import", method = RequestMethod.POST)
    public BaseResp importMonthReport(
        @RequestParam(value = "reportFile", required = false) MultipartFile forecastFile, String cityId)
        throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<MonthReportDTO> monthReportDTOS = new ArrayList<>();
        List<MonthReportDTO> result = new ArrayList<>();
        String reportStatus = null;
        List<ReportSystemInitDO> month_report_time = reportSystemService.findReportSystemConfig(cityId,
            "month_report_time", null);
        if (Boolean.TRUE.equals(month_report_time.get(0).getStatus())) {
            String value = month_report_time.get(0).getValue();
            reportStatus = value;
        }
        try {
            InputStream io = forecastFile.getInputStream();
            List<Map<Integer, Object>> list = ExcelUtil.resolve(io, 1, false);
            if (list != null) {
                list.subList(0, 3).clear();
                for (Map<Integer, Object> integerObjectMap : list) {
                    MonthReportDTO monthReportDTO = new MonthReportDTO();
                    if (integerObjectMap.get(0) == null) {
                        continue;
                    }
                    monthReportDTO.setDate(integerObjectMap.get(0).toString().substring(0, 7) + "-01");
                    monthReportDTO.setCityId(cityId);
                    monthReportDTO.setType(integerObjectMap.get(0).toString().substring(7, 9));
                    monthReportDTO.setMaxLoad(new BigDecimal(integerObjectMap.get(1).toString()));
                    monthReportDTO.setMinLoad(new BigDecimal(integerObjectMap.get(2).toString()));
                    monthReportDTO.setNoonTimeLoad(new BigDecimal(integerObjectMap.get(3).toString()));
                    monthReportDTO.setEnergy(new BigDecimal(integerObjectMap.get(4).toString()));
                    monthReportDTO.setExtremeMaxLoad(new BigDecimal(integerObjectMap.get(5).toString()));
                    monthReportDTO.setExtremeMinLoad(new BigDecimal(integerObjectMap.get(6).toString()));
                    monthReportDTO.setExtremeNoonTimeLoad(new BigDecimal(integerObjectMap.get(7).toString()));
                    monthReportDTO.setExtremeEnergy(new BigDecimal(integerObjectMap.get(8).toString()));
                    monthReportDTOS.add(monthReportDTO);
                }
            }
        } catch (Exception e) {
            baseResp.setRetCode("T706");
            baseResp.setRetMsg("导入失败！");
            throw TsieExceptionUtils.newBusinessException("需要导入的数据有空值");
        }
        if (reportStatus != null) {
            Date date = DateUtil.getDate(reportStatus, "yyyy-MM-dd");
            String monthByDate = DateUtil.getMonthByDate(new Date()).split("-")[1];
            if (date.compareTo(new Date()) >= 0) {
                // 可以上报
                for (MonthReportDTO monthReportDTO : monthReportDTOS) {
                    Integer integer = Integer.valueOf(monthByDate);
                    Integer integer1 = Integer.valueOf(monthReportDTO.getDate().split("-")[1]);
                    if (integer1 >= integer) {
                        result.add(monthReportDTO);
                    }
                }
            }
            monthForecastModelService.report(result);
            if (result.size() == monthReportDTOS.size()) {
                baseResp.setRetCode("T200");
                baseResp.setRetMsg("全部导入成功");
            } else {
                baseResp.setRetCode("T200");
                baseResp.setRetMsg("部分导入失败，存在超过上报时间记录。");
            }
        } else {
            monthForecastModelService.report(monthReportDTOS);
            baseResp.setRetCode("T200");
            baseResp.setRetMsg("全部导入成功");
        }
        return baseResp;
    }
}
