package com.tsintergy.lf.web.base.datamanage.controller;


import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.serviceapi.base.base.api.CaliberService;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.datamanage.dto.LoadBatchQueryDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.api.StatisticsCityDayFcBatchService;
import com.tsintergy.lf.serviceapi.base.evalucation.api.StatisticsCityDayFcService;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.StatisticsDayBatchDTO;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcBatchService;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.system.api.SettingBatchInitService;
import com.tsintergy.lf.serviceapi.base.system.dto.SettingBatchInitDTO;
import com.tsintergy.lf.serviceapi.base.system.pojo.SettingBatchInitDO;
import com.tsintergy.lf.web.base.controller.BaseController;
import com.tsintergy.lf.web.base.datamanage.request.BatchRequest;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @date: 4/11/18 12:02 PM
 * @author: angel
 **/
@RestController
@RequestMapping("/data")
public class DataManageBatchController extends BaseController {

    @Autowired
    LoadCityFcService loadCityFcService;

    @Autowired
    CityService cityService;

    @Autowired
    CaliberService caliberService;

    @Autowired
    LoadCityHisService loadCityHisService;

    @Autowired
    StatisticsCityDayFcService statisticsCityDayFcService;

    @Autowired
    LoadCityFcBatchService loadCityFcBatchService;

    @Autowired
    private StatisticsCityDayFcBatchService statisticsCityDayFcBatchService;

    @Autowired
    private SettingBatchInitService settingBatchInitService;

    @PostMapping("/batchFcWeather")
    public BaseResp batchFcWeatherData(@RequestBody BatchRequest batchRequest) throws Exception {
        String cityId = cityService.findWeatherCityId(batchRequest.getCityId());
        LoadBatchQueryDTO weatherInfoFcLoadForecast = loadCityFcBatchService
                .findWeather(batchRequest.getBatchIds(), batchRequest.getAlgorithmIds(), cityId,
                        batchRequest.getCaliberId()
                        , batchRequest.getStartDate(), batchRequest.getEndDate(), batchRequest.getType());
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(weatherInfoFcLoadForecast);
        return baseResp;
    }


    @PostMapping("/fcLoadDataList")
    public BaseResp batchFcLoadData(@RequestBody BatchRequest batchRequest) throws Exception {
        LoadBatchQueryDTO loadCityFcBatchDOS = loadCityFcBatchService
                .findLoad(batchRequest.getBatchIds(), batchRequest.getAlgorithmIds(), batchRequest.getCityId(),
                        batchRequest.getCaliberId()
                        , batchRequest.getStartDate(), batchRequest.getEndDate());
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(loadCityFcBatchDOS);
        return baseResp;
    }

    @PostMapping("/fcAccuracyBatch")
    public BaseResp<List<StatisticsDayBatchDTO>> fcAccuracyBatch(@RequestBody BatchRequest batchRequest)
            throws Exception {
        List<StatisticsDayBatchDTO> loadCityFcBatchDOS = statisticsCityDayFcBatchService
                .getDayAccuracy(batchRequest.getCityId(), batchRequest.getCaliberId(), batchRequest.getAlgorithmIds(),
                        batchRequest.getBatchIds(), batchRequest.getStartDate(), batchRequest.getEndDate());
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(loadCityFcBatchDOS);
        return baseResp;
    }

    @GetMapping("/getBatch")
    public BaseResp<List<SettingBatchInitDO>> getBatch() throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<SettingBatchInitDO> settingBatchInitDOS = settingBatchInitService.getBatchList();

        Map<String, CityDO> cityDOMap = cityService.findAllCitys().stream().collect(Collectors.toMap(CityDO::getId, Function.identity()));
        List<SettingBatchInitDTO> settingBatchInitDTOList = settingBatchInitDOS.stream().map(batchInitDO -> {
            SettingBatchInitDTO settingBatchInitDTO = new SettingBatchInitDTO();
            CityDO cityDO = cityDOMap.get(batchInitDO.getCityId());
            BeanUtils.copyProperties(batchInitDO, settingBatchInitDTO);
            if (cityDO != null) {
                settingBatchInitDTO.setCityName(cityDO.getCity());
            }
            return settingBatchInitDTO;
        }).collect(Collectors.toList());

        baseResp.setData(settingBatchInitDTOList);
        return baseResp;
    }

    @RequestMapping(value = "/changeSavedTime", method = RequestMethod.PUT)
    public BaseResp changeSavedTimeByCityID(@RequestBody BatchRequest request) throws Exception {
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm");
        try {
            LocalTime.parse(request.getTimeStr(), timeFormatter);
        } catch (DateTimeParseException e) {
            return BaseResp.failResp("时间格式错误");
        }
        settingBatchInitService.doChangeSavedTimeByCityID(request.getCityId(), request.getTimeStr());
        return BaseResp.succResp();
    }
}
