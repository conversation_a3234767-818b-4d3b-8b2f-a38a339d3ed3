/**
 * Copyright(C),2015‐2021,北京清能互联科技有限公司
 */
package com.tsintergy.lf.web.base.common.handler;

import com.tsieframework.cloud.security.serviceapi.businesslog.pojo.TsieOperateLogVO;
import java.util.ArrayList;
import java.util.List;
import lombok.Builder;
import org.aspectj.lang.ProceedingJoinPoint;
import org.springframework.util.CollectionUtils;

/**
 *Description:  <br>
 *
 *@Author: <EMAIL>
 *@Date: 2021/12/29 8:19
 *@Version: 1.0.0
 */
public class ProceedingJoinPointLogHandlerChain {

    private List<ProceedingJoinPointLogHandler> joinPointLogHandlers = new ArrayList<>();

    public void joinPointLog(ProceedingJoinPoint proceedingJoinPoint, TsieOperateLogVO operateLog){
        if (!CollectionUtils.isEmpty(joinPointLogHandlers)) {
            for (ProceedingJoinPointLogHandler handler : joinPointLogHandlers) {
                handler.proceedingJoinPointLog(proceedingJoinPoint,operateLog);
                if (handler.isMaching(proceedingJoinPoint,operateLog)) {
                    break;
                }
            }
        }
    }

    public void addJoinPointLogHandler(ProceedingJoinPointLogHandler joinPointLogHandler) {
        joinPointLogHandlers.add(joinPointLogHandler);
    }


}