package com.tsintergy.lf.web.base.ultra.controller;

import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.serviceapi.base.load.api.LoadBatchAccuracyService;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadBatchAccuracyDTO;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadBatchAccuracyRequest;
import com.tsintergy.lf.serviceapi.base.ultra.dto.LoadUltraFcAccuracyDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description
 * @Date 2023/1/10 8:53
 * <AUTHOR>
 **/
@Slf4j
@Api(value = "超短期预测-批次准确率评估", tags = "[超短期模块][超短期预测][批次准确率评估]")
@RestController
@RequestMapping("/ultra/batchAccuracy")
public class BatchTimeAccuracyEvaluateController extends UltraBaseLoadFeatureController {

    @Autowired
    LoadBatchAccuracyService loadBatchAccuracyService;

    /**
     * 批次准确率
     */
    @ApiOperation(value = "日批次准确率")
    @RequestMapping(value = "/batchFcAccuracy", method = RequestMethod.POST)
    public BaseResp<List<LoadBatchAccuracyDTO>> batchFcFeatureCity(@RequestBody LoadBatchAccuracyRequest request) throws Exception {
        BaseResp baseResp = null;
        try {
            String caliberId = request.getCaliberId();
            if (StringUtils.isEmpty(caliberId)){
                caliberId = getCaliberId();
            }
            request.setCaliberId(caliberId);
            List<LoadBatchAccuracyDTO> loadBatchAccuracyDTOS = loadBatchAccuracyService.findBatchFcAccuracy(request);
            baseResp = BaseResp.succResp();
            baseResp.setData(loadBatchAccuracyDTOS);
        } catch (Exception e) {
            e.printStackTrace();
            baseResp = BaseResp.failResp("查询批次准确率失败");
        }
        return baseResp;
    }


    /**
     * 批次时刻准确率
     */
    @ApiOperation(value = "时刻批次准确率")
    @RequestMapping(value = "/batchFcTimeAccuracy", method = RequestMethod.GET)

    public BaseResp<List<LoadUltraFcAccuracyDTO>> batchFcTimeAccuracy(String batchId) throws Exception {
        BaseResp baseResp = null;
        try {
            List<LoadUltraFcAccuracyDTO> loadUltraFcAccuracyDTOS = loadBatchAccuracyService.findBatchFcTimeAccuracy(batchId);
            baseResp = BaseResp.succResp();
            baseResp.setData(loadUltraFcAccuracyDTOS);
        } catch (Exception e) {
            e.printStackTrace();
            baseResp = BaseResp.failResp("查询超短期批次时刻准确率失败");
        }
        return baseResp;
    }

}
