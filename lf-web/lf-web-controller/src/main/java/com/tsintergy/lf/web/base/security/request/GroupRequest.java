/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司 Author:  wangchen Date:  2019/4/24 17:16 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.security.request;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * Description: 权限参数 <br>
 *
 * <AUTHOR>
 * @create 2020/12/11
 * @since 1.0.0
 */
@Data
public class GroupRequest implements Serializable {

    @ApiModelProperty(value = "组ID")
    private String groupid;

    @ApiModelProperty(value = "用户ID")
    private String userid;


}
