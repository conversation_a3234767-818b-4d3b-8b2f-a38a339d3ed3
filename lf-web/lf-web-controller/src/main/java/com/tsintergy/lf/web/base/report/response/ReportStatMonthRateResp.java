/**
 * Copyright (c), 2015-2021, 北京清能互联科技有限公司
 * Author: zhangjy
 * Date: 2021/1/18 15:22
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.report.response;

import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**  
 * Description: 单地市月上报率信息 <br> 
 * 
 * <AUTHOR>  
 * @create 2020/11/10  
 * @since 1.0.0  
 */
public class ReportStatMonthRateResp implements Serializable {

    @ApiModelProperty(value = "城市名称",example = "广东")
    private String city;

    @ApiModelProperty(value = "正常上报率",example = "90")
    @BigdecimalJsonFormat(scale = 2)
    private BigDecimal normal;

    @ApiModelProperty(value = "延迟上报率",example = "5")
    @BigdecimalJsonFormat(scale = 2)
    private BigDecimal delayed;

    @ApiModelProperty(value = "总上报率",example = "10")
    @BigdecimalJsonFormat(scale = 2)
    private BigDecimal total;


    @ApiModelProperty(value = "延迟上报详情")
    private List<DelayReportResp> delayReports;


    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public BigDecimal getNormal() {
        return normal;
    }

    public void setNormal(BigDecimal normal) {
        this.normal = normal;
    }

    public BigDecimal getDelayed() {
        return delayed;
    }

    public void setDelayed(BigDecimal delayed) {
        this.delayed = delayed;
    }

    public BigDecimal getTotal() {
        return total;
    }

    public void setTotal(BigDecimal total) {
        this.total = total;
    }

    public List<DelayReportResp> getDelayReports() {
        return delayReports;
    }

    public void setDelayReports(List<DelayReportResp> delayReports) {
        this.delayReports = delayReports;
    }
}