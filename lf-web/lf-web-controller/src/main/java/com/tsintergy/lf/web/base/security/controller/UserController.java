package com.tsintergy.lf.web.base.security.controller;

import static com.tsieframework.cloud.security.core.base.SecurityConstants.GROUP_USER_OPT_DELETE;
import static com.tsieframework.core.base.TsieConstants.TSIE_TYPE_DELETE;

import com.alibaba.fastjson.JSON;
import com.tsieframework.cloud.security.serviceapi.system.api.RedisService;
import com.tsieframework.cloud.security.serviceapi.system.api.SecurityService;
import com.tsieframework.cloud.security.serviceapi.system.bean.TsieUserRequest;
import com.tsieframework.cloud.security.serviceapi.system.bean.TsieUserRoleRequest;
import com.tsieframework.cloud.security.serviceapi.system.pojo.TsieUserRoleVO;
import com.tsieframework.cloud.security.serviceapi.system.pojo.TsieUserVO;
import com.tsieframework.cloud.security.web.businesslog.annotation.OperateLog;
import com.tsieframework.cloud.security.web.common.util.Md5Util;
import com.tsieframework.cloud.security.web.system.request.sysusercontroller.ChangeMyPasswordRequest;
import com.tsieframework.cloud.security.web.system.request.sysusercontroller.ChangeUserPasswordRequest;
import com.tsieframework.cloud.security.web.system.request.sysusercontroller.ListRequest;
import com.tsieframework.cloud.security.web.system.request.sysusercontroller.UserAuthorizeGroupAddRequest;
import com.tsieframework.cloud.security.web.system.request.sysusercontroller.UserAuthorizeGroupDeleteRequest;
import com.tsieframework.cloud.security.web.system.request.sysusercontroller.UserAuthorizeGroupListRequest;
import com.tsieframework.cloud.security.web.system.request.sysusercontroller.UserRoleAddRequest;
import com.tsieframework.cloud.security.web.system.request.sysusercontroller.UserRoleDeleteRequest;
import com.tsieframework.cloud.security.web.system.request.sysusercontroller.UserRoleListRequest;
import com.tsieframework.cloud.security.web.system.request.sysusercontroller.WebTsieUserRequest;
import com.tsieframework.core.base.dao.DataPackage;
import com.tsieframework.core.base.dao.hibernate.BaseDAO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.dao.hibernate.QueryOp;
import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.util.TsieConditions;
import com.tsieframework.core.base.web.BaseResp;
import com.tsieframework.core.base.web.BaseRespBuilder;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.base.pojo.LoadUserDO;
import com.tsintergy.lf.serviceapi.base.security.api.UserService;
import com.tsintergy.lf.web.base.controller.BaseController;
import com.tsintergy.lf.web.base.security.properties.SecurityProperties;
import com.tsintergy.lf.web.base.security.request.GroupRequest;
import com.tsintergy.lf.web.base.security.request.PermissionRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * @date: 6/14/18 11:39 AM
 * @author: angel
 **/
@RequestMapping({"/sysUserManage"})
@RestController
@Slf4j
@Api(tags = "用户控制器")
public class UserController extends BaseController {


    private static final String DEFAULT_PASSWORD = "123456";

    @Autowired
    protected SecurityService securityService;

    @Autowired
    private SsoLoadForecastAccountManager accountManager;

    @Autowired
    protected UserService userService;

    @Autowired
    private CityService cityService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private SecurityProperties securityProperties;

    /**
     * 1. 系统管理-用户管理/1.用户管理/1. 用户管理列表
     */
    @ApiOperation("用户管理列表")
    @RequestMapping(value = {"/user/list"}, method = {RequestMethod.GET})
    public BaseResp<DataPackage> list(@Valid ListRequest req, @ApiParam(value = "城市ID") String cityId) throws Exception {
        DBQueryParam param = DBQueryParam.builder().build();
        if (null != req && StringUtils.isNotBlank(req.getNickname())) {
            param.getQueryConditions().put("_sk_nickname", req.getNickname());
        }
        if (null != req && StringUtils.isNotBlank(req.getEnable())) {
            param.getQueryConditions().put("_ne_enable", Integer.valueOf(req.getEnable()));
        }
        DataPackage totalDp = this.securityService.queryTsieUserVO(param);
        BaseResp baseResp = this.list(req);
        DataPackage tiseDp = (DataPackage) baseResp.getData();
        if (tiseDp.getRowCount() == 0) {
            return baseResp;
        }
        List<String> ids = new ArrayList<String>();
        for (Object tsie : totalDp.getDatas()) {
            ids.add(((TsieUserVO) tsie).getId());
        }
        DataPackage loadDp = findUserDetail(req, cityId, ids);
        for (Object obj : loadDp.getDatas()) {
            LoadUserDO loadUserVO = (LoadUserDO) obj;
            CityDO cityVO = this.cityService.findCityById(loadUserVO.getCityId());
            if (null != cityVO) {
                loadUserVO.setCity(cityVO.getCity());
            }
            for (Object object : totalDp.getDatas()) {
                if (loadUserVO.getTsieUId().equals(((TsieUserVO) object).getId())) {
                    BeanUtils.copyProperties(object, loadUserVO);
                }
            }
        }
        tiseDp.setDatas(loadDp.getDatas());
        if (StringUtils.isNotEmpty(cityId)) {
            tiseDp.setRowCount(loadDp.getRowCount());
        }
        baseResp.setData(tiseDp);
        return baseResp;
    }


    public BaseResp<DataPackage<TsieUserVO>> list(@Valid ListRequest req)  throws Exception{
        DBQueryParamBuilder builder = req.resolveParamBuilder();

        //添加用户昵称(nickname)查询参数
        if (StringUtils.isNotEmpty(req.getNickname())) {
            builder.where(QueryOp.StringLike, "nickname",  req.getNickname());
        }
        if (StringUtils.isNotEmpty(req.getEnable())) {
            builder.where(QueryOp.NumberEqualTo, "enable",  Integer.valueOf(req.getEnable()));
        }

        DataPackage dp = securityService.queryTsieUserVO(builder.build());

        for(Object obj : dp.getDatas()){
            TsieUserVO user = (TsieUserVO)obj;
            user.setPasswd(null);//不返回密码到前端
        }

        return BaseRespBuilder
                .success()
                .setData(dp)
                .build();
    }

    private DataPackage findUserDetail(@Valid ListRequest req, String cityId,
                                       List<String> ids) {
        DBQueryParam param = getDbQueryParam(req);
        if (StringUtils.isNotBlank(cityId)) {
            param.getQueryConditions().put("_se_cityId", cityId);
        }
        if (!CollectionUtils.isEmpty(ids)) {
            param.getQueryConditions().put("_sin_tsieUId", ids);
        }
        return this.userService.queryLoaderVO(param);
    }

    /**
     * 1. 系统管理-用户管理/1.用户管理/2. 获取用户信息
     */
    @ApiOperation("获取用户信息")
    @RequestMapping(value = {"/user/get/{id}"}, method = {RequestMethod.GET})
    public BaseResp<LoadUserDO> get(@PathVariable("id")@ApiParam(value = "id") String id) throws Exception {
        DBQueryParam param = DBQueryParamBuilder.create().where(QueryOp.StringEqualTo, "id", id).queryDataOnly().build();
        TsieUserVO userVO = securityService.queryTsieUserVo(param);
        if(userVO == null){
            return BaseRespBuilder.fail("用户不存在").build();
        }
        //把密码赋值为空，避免前端获取到密码信息
        userVO.setPasswd(null);
        LoadUserDO loadUserVO = this.userService.findUserById(id);
        CityDO cityVO = null;
        try {
            cityVO = this.cityService.findCityById(loadUserVO.getCityId());
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (null != cityVO) {
            loadUserVO.setCity(cityVO.getCity());
        }
        BeanUtils.copyProperties(userVO, loadUserVO);
        BaseResp resp = BaseResp.succResp();
        resp.setData(loadUserVO);
        return resp;
    }

    /**
     * 1. 系统管理-用户管理/1.用户管理/4. 用户删除
     */
    @ApiOperation("用户删除")
    @RequestMapping(value = {"/user/delete/{id}"}, method = {RequestMethod.DELETE})
    @OperateLog(operate = "删除用户")
    public BaseResp delete(@PathVariable("id")@ApiParam(value = "id") String id) throws Exception {
        TsieConditions.isNull(id, "id 为空");
        TsieUserRequest request = new TsieUserRequest();
        TsieUserVO vo = new TsieUserVO();
        vo.setId(id);
        request.setOpType(TSIE_TYPE_DELETE);
        request.setVo(vo);
        securityService.doTsieUserVo(request);
        this.userService.doDeleteUserById(id);
        return BaseResp.succResp();
    }


    @OperateLog(operate = "新增用户")
    @RequestMapping(value = {"/user/saveOrUpdate/I"}, method = {RequestMethod.POST, RequestMethod.PUT})
    public BaseResp save(@RequestBody UserRequest req) throws Exception {
        return saveOrUpdate("I",req);
    }

    @OperateLog(operate = "修改用户")
    @RequestMapping(value = {"/user/saveOrUpdate/U"}, method = {RequestMethod.POST, RequestMethod.PUT})
    public BaseResp update(@RequestBody UserRequest req) throws Exception {
        return saveOrUpdate("U",req);
    }

    /**
     * 1. 系统管理-用户管理/1.用户管理/3. 保存或更新用户信息
     */
    @ApiOperation("更新用户")
    @OperateLog(operate = "更新用户信息")
    //@RequestMapping(value = {"/user/saveOrUpdate/{opType}"}, method = {RequestMethod.POST, RequestMethod.PUT})
    public BaseResp saveOrUpdate(@PathVariable("opType") @ApiParam(value = "opType")String opType, @RequestBody UserRequest req) throws Exception {
        if (null == req.getCityId()) {
            return BaseResp.failResp();
        }
        BaseResp<TsieUserVO> tsieResp = this.check(opType, req);
        log.debug("----tsie返回-----：" + JSON.toJSONString(tsieResp));
        if (!"T200".equals(tsieResp.getRetCode())) {
            return tsieResp;
        }
        LoadUserDO loadUserVO = new LoadUserDO();
        CityDO cityById = cityService.findCityById(req.getCityId());
        if(cityById != null){
            loadUserVO.setCity(cityById.getCity());
        }
        loadUserVO.setTsieUId(tsieResp.getData().getId());
        loadUserVO.setCityId(req.getCityId());
        loadUserVO.setCreateTime(new Timestamp(System.currentTimeMillis()));
        userService.doSaveOrUpdateUser(loadUserVO);
        BeanUtils.copyProperties(tsieResp.getData(), loadUserVO);
        BaseResp<LoadUserDO> resp = BaseResp.succResp();
        resp.setData(loadUserVO);
        return resp;
    }

    /**
     * 1. 系统管理-用户管理/1.用户管理/5. 修改用户密码
     */
    @ApiOperation("修改用户密码")
    @RequestMapping(value = {"/user/changeMyPassword"}, method = {RequestMethod.PUT})
    @OperateLog(operate = "修改用户密码")
    public BaseResp<Void> changePasswordSummit(@RequestBody ChangeMyPasswordRequest request) throws Exception {
        BaseResp baseResp = this.changeMyPassword(request);
        if (BaseResp.SUCC.equals(baseResp.getRetCode())) {
            TsieUserVO tsieUserVO = super.getCurrnTsieUserVO();
            LoadUserDO loadUserDetailDO = this.userService.findUserById(tsieUserVO.getId());
            loadUserDetailDO.setLastUpdatePasswordTime(new Timestamp(System.currentTimeMillis()));
            userService.doSaveOrUpdateUser(loadUserDetailDO);
            redisService.redisAdd(securityProperties.getTokenCacheKeyPrefix()+"_dict.lastUpdatePasswordTime." + tsieUserVO.getUsername(),loadUserDetailDO.getLastUpdatePasswordTime());
            redisService.redisAdd(securityProperties.getTokenCacheKeyPrefix()+"_dict.createTime." + tsieUserVO.getUsername(),loadUserDetailDO.getCreateTime());

        }
        return baseResp;
    }

    /**
     * 管理员重置用户的密码
     * @return
     */
    @ApiOperation("管理员重置用户的密码")
    @RequestMapping(value = "/user/resetUserPassword", method = RequestMethod.PUT)
    @ResponseBody
    public BaseResp<Map<String, String>> resetUserPassword(@RequestBody ChangeUserPasswordRequest request) throws Exception {
        String password = Md5Util.md5(DEFAULT_PASSWORD);
        TsieConditions.notEmpty(password, "T714");
        securityService.doResetUserPassword(request.getUserId(), password);
        //返回重置后的密码
        return BaseRespBuilder.success().putDataValue("resetPassword",DEFAULT_PASSWORD).build();
    }

    public BaseResp<Void> changeMyPassword(@RequestBody ChangeMyPasswordRequest request) throws Exception {

        TsieConditions.notEmpty(request.getCurPass(), "T711");
        TsieConditions.notEmpty(request.getNewPass(), "T712");
        TsieConditions.notEmpty(request.getNewPassConfirm(), "T713");

        String curPass = accountManager.decodePassword(request.getCurPass());
        String newPass = accountManager.decodePassword(request.getNewPass());
        String newPassConfirm = accountManager.decodePassword(request.getNewPassConfirm());

        TsieUserVO userVO = getLoginUser();

        String userId = userVO.getId();
        securityService.doChangeMyPassword(userId, curPass, newPass, newPassConfirm);

        return BaseRespBuilder.successResp();
    }

    /**
     * 1. 系统管理-用户管理/2.用户授权-权限管理/1. 权限管理列表
     */
    @ApiOperation("权限管理列表")
    @RequestMapping(value = {"/userAuthorize/role/list"}, method = {RequestMethod.GET})
    @ResponseBody
    public BaseResp<DataPackage> userRoleList(UserRoleListRequest request) throws Exception {
        DBQueryParamBuilder builder = request.resolveParamBuilder();

        if (StringUtils.isNotEmpty(request.getId())) {
            builder.whereForNameQuery("userid", request.getId());
        }else{
            throw TsieExceptionUtils.newBusinessException("用户编码异常");
        }
        if (null != request && StringUtils.isNotBlank(request.getId())) {
            builder.where(QueryOp.StringLike, "category", request.getRoleName());
        }

        return BaseRespBuilder
                .success()
                .setData(securityService.queryUserRoleByUserid(builder.build()))
                .build();
    }

    /**
     * 1. 系统管理-用户管理/2.用户授权-权限管理/3. 添加权限
     */
    @ApiOperation("添加权限")
    @RequestMapping(value = {"/userAuthorize/role/add"}, method = {RequestMethod.POST})
    @OperateLog(operate = "新增权限")
    public BaseResp userRoleAdd(@RequestBody PermissionRequest permissionRequest) throws Exception {
        UserRoleAddRequest request = new UserRoleAddRequest();
        request.setRoleid(permissionRequest.getRoleid());
        request.setUserid(permissionRequest.getUserid());
        TsieConditions.isNull(request, "参数不能为空");
        TsieConditions.isNull(request.getRoleid(), "权限不能为空");
        TsieConditions.isNull(request.getUserid(), "用户不能为空");

        //注意roleid以逗号隔开时则为批量添加角色(权限)到用户
        securityService.doDealTsieUserRoleVOBatch(request.getUserid(), request.getRoleid());
        return BaseRespBuilder.successResp();
    }

    /**
     * 1. 系统管理-用户管理/2.用户授权-权限管理/4. 删除权限
     */
    @ApiOperation("删除权限")
    @RequestMapping(value = {"/userAuthorize/role/delete"}, method = {RequestMethod.DELETE})
    @OperateLog(operate = "删除权限")
    public BaseResp userRoleDelete(@RequestBody PermissionRequest permissionRequest) throws Exception {
        UserRoleDeleteRequest request = new UserRoleDeleteRequest();
        request.setUserid(permissionRequest.getUserid());
        request.setRoleid(permissionRequest.getRoleid());
        TsieConditions.isNull(request, "参数不能为空");
        TsieConditions.isNull(request.getRoleid(), "权限不能为空");
        TsieConditions.isNull(request.getUserid(), "用户不能为空");

        TsieUserRoleRequest req = new TsieUserRoleRequest();
        req.setOpType(TSIE_TYPE_DELETE);
        TsieUserRoleVO vo = new TsieUserRoleVO();
        vo.setUserid(request.getUserid());
        vo.setRoleid(request.getRoleid());
        req.setVo(vo);

        securityService.doDealTsieUserRoleVO(req);

        return BaseRespBuilder.successResp();
    }

    /**
     * 1. 系统管理-用户管理/4. 用户授权-用户组管理/1. 用户组管理列表
     */
    @ApiOperation("用户组管理列表")
    @RequestMapping(value = "/userAuthorize/group/list", method = RequestMethod.GET)
    @ResponseBody
    public BaseResp<DataPackage> userAuthorizeGroupList(UserAuthorizeGroupListRequest request) throws Exception {
        DBQueryParam param = getDbQueryParam(request);

        if (null != request && StringUtils.isNotBlank(request.getId())) {
            param.getQueryConditions().put("userid", request.getId());
        } else {
            TsieExceptionUtils.newBusinessException("用户编码异常");
        }
        if (null != request && StringUtils.isNotBlank(request.getId())) {
            param.getQueryConditions().put("_sk_groupname", request.getGroupname());
        }

        BaseResp resp = BaseResp.succResp();
        resp.setData(securityService.queryTsieUserGroup(param));
        return resp;
    }



    /**
     * 1. 系统管理-用户管理/4. 用户授权-用户组管理/3. 添加用户组
     */
    @ApiOperation("添加用户组")
    @RequestMapping(value = "/userAuthorize/group/add", method = RequestMethod.POST)
    @OperateLog(operate = "新增用户组")
    @ResponseBody
    public BaseResp userAuthorizeGroupAdd(@RequestBody  GroupRequest groupRequest ) throws Exception {
        UserAuthorizeGroupAddRequest request = new UserAuthorizeGroupAddRequest();
        request.setGroupid(groupRequest.getGroupid());
        request.setUserId(groupRequest.getUserid());
        TsieConditions.isNull(request, "参数不能为空");
        TsieConditions.isNull(request.getGroupid(), "组不能为空");
        TsieConditions.isNull(request.getUserId(), "用户不能为空");

        //注意groupid以逗号隔开时则为批量添加多个权限组给用户
        securityService.doDealTsieGroupUserBatch2(request.getGroupid(), request.getUserId());
        return BaseRespBuilder.successResp();
    }

    /**
     * 1. 系统管理-用户管理/4. 用户授权-用户组管理/4. 删除用户组
     * 参考：4. 系统管理-权限组管理/3. 权限组管理-组成员管理/4. 删除组用户
     */

    /**
     * 1. 系统管理-用户管理/4. 用户授权-用户组管理/4. 删除用户组
     */
    @ApiOperation("删除用户组")
    @RequestMapping(value = "/userAuthorize/group/delete", method = RequestMethod.DELETE)
    @OperateLog(operate = "删除用户组")
    @ResponseBody
    public BaseResp userAuthorizeGroupDelete(@RequestBody  GroupRequest groupRequest) throws Exception {
        UserAuthorizeGroupDeleteRequest request = new UserAuthorizeGroupDeleteRequest();
        String userId = groupRequest.getUserid();
        String groupid = groupRequest.getGroupid();
        request.setUserId(userId);
        request.setGroupid(groupid);
        TsieConditions.isNull(request, "参数不能为空");
        TsieConditions.isNull(request.getGroupid(), "组不能为空");
        TsieConditions.isNull(request.getUserId(), "用户不能为空");
        TsieConditions.isNull(userId, "用户不能为空");
        TsieConditions.isNull(groupid, "组不能为空");
        securityService.doDealTsieGroupUser(GROUP_USER_OPT_DELETE, groupid, userId);

        return BaseRespBuilder.successResp();
    }

    private DBQueryParam getDbQueryParam(UserAuthorizeGroupListRequest request) {
        DBQueryParam param = DBQueryParam.builder().build();
        if (request != null) {
            param.setPageNo(request.getPageNum());
            param.setPageSize(request.getNumPerPage());
            param.setQueryType(BaseDAO.QUERY_TYPE_COUNT_AND_DATA);
        } else {
            param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        }

        if (StringUtils.isNotBlank(request.getOrderField())
                && StringUtils.isNotBlank(request.getOrderDirection())) {
            param.setOrderby(request.getOrderField());
            param.setDesc(request.getOrderDirection().equals("asc") ? "0" : "1");
        }
        return param;
    }


    protected DBQueryParam getDbQueryParam(ListRequest request) {
        DBQueryParam param = DBQueryParam.builder().build();
        if (request != null) {
            param.setPageNo(request.getPageNum());
            param.setPageSize(request.getNumPerPage());
            param.setQueryType(BaseDAO.QUERY_TYPE_COUNT_AND_DATA);
        } else {
            param.setQueryType(BaseDAO.QUERY_TYPE_DATA_ONLY);
        }

        if (StringUtils.isNotBlank(request.getOrderField())
            && StringUtils.isNotBlank(request.getOrderDirection())) {
            param.setOrderby(request.getOrderField());
            param.setDesc(request.getOrderDirection().equals("asc") ? "0" : "1");
        }
        return param;
    }

    private BaseResp<TsieUserVO> check(@RequestBody @PathVariable("opType") String opType,
                                       @RequestBody WebTsieUserRequest req) throws Exception {
        TsieConditions.isNull(opType, "操作类型不能为空");
        TsieUserRequest request = new TsieUserRequest();
        DBQueryParam param;
        TsieUserVO vo;
        if ("I".equals(opType)) {
            param = DBQueryParamBuilder.create().queryDataOnly()
                    .where(QueryOp.StringEqualTo, "username", req.getUsername()).build();
            vo = this.securityService.queryTsieUserVo(param);
            if (null != vo) {
                return BaseRespBuilder.fail("帐号已被注册").build();
            }
            vo = new TsieUserVO();
            BeanUtils.copyProperties(req, vo);
            vo.setEnable(TsieUserVO.ENABLE_YES);
            request.setOpType(opType);
            String pass = this.accountManager.decodePassword(vo.getPasswd());
            vo.setPasswd(pass);
            vo.setPasswdChangePrompt(TsieUserVO.PASSWD_CHANGE_PROMPT_YES);
            request.setVo(vo);
        } else if ("U".equals(opType)) {
            param = DBQueryParamBuilder.create().queryDataOnly().where(QueryOp.StringEqualTo, "id", req.getId())
                    .build();
            vo = this.securityService.queryTsieUserVo(param);
            vo.setNickname(req.getNickname());
            if (!vo.getUsername().equals(req.getUsername())) {
                return BaseRespBuilder.fail("账号不允许更改").build();
            }

            String dbPassword = vo.getPasswd();
            BeanUtils.copyProperties(req, vo);
            vo.setPasswd(dbPassword);
            request.setOpType(opType);
            request.setVo(vo);
        }

        TsieUserVO retVo = this.securityService.doTsieUserVo(request);
        retVo.setPasswd((String) null);
        return BaseRespBuilder.success("账号不允许更改").setData(retVo).build();
    }



}
