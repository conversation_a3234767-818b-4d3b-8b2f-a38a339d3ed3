/**
 * Copyright (c), 2015-2021, 北京清能互联科技有限公司
 * Author: zhangjy
 * Date: 2021/1/18 15:23
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.report.response;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**  
 * Description: 延迟上报详细信息 <br> 
 * 
 * <AUTHOR>  
 * @create 2020/11/10  
 * @since 1.0.0  
 */
public class DelayReportResp implements Serializable {


    @ApiModelProperty(value = "日期",example = "2022-02-01")
    private Date date;

    @ApiModelProperty(value = "上报日期",example = "2022-02-01")
    private String reoprtDate;

    @ApiModelProperty(value = "昵称",example = "测试")
    private String nickName;


    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public String getReoprtDate() {
        return reoprtDate;
    }

    public void setReoprtDate(String reoprtDate) {
        this.reoprtDate = reoprtDate;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }
}