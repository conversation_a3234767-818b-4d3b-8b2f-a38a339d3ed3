package com.tsintergy.lf.web.base.ultra.controller;


import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.serviceapi.base.ultra.api.AccuracyEvaluateService;
import com.tsintergy.lf.serviceapi.base.ultra.dto.LoadFeatureUltraFcDayDTO;
import com.tsintergy.lf.serviceapi.base.ultra.dto.LoadUltraFcAccuracyDTO;
import com.tsintergy.lf.web.base.ultra.request.LoadUltraFcFeatureRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description
 * @Date 2023/1/9 15:02
 * <AUTHOR>
 **/
@Slf4j
@RestController
@Api(value = "超短期预测-日准确率评估", tags = "[超短期模块][超短期预测][日准确率评估]")
@RequestMapping("/ultra/dayAccuracy")
public class DayAccuracyEvaluateController extends UltraBaseLoadFeatureController {

    @Autowired
    AccuracyEvaluateService accuracyEvaluateService;

    /**
     * 日准确率
     */
    @ApiOperation(value = "日准确率")
    @RequestMapping(value = "/day", method = RequestMethod.POST)
    public BaseResp<List<LoadFeatureUltraFcDayDTO>> statisticsFeatureCity(@RequestBody LoadUltraFcFeatureRequest request) throws Exception {
        BaseResp baseResp = null;
        try {
            String caliberId = request.getCaliberId();
            if (StringUtils.isEmpty(caliberId)){
                caliberId = getCaliberId();
            }
            List<LoadFeatureUltraFcDayDTO> loadFeatureUltraFcDayDTOS =
                accuracyEvaluateService.findFeatureStatisDTOS(request.getStartDate(), request.getEndDate(), caliberId, request.getCityId(), request.getType(), request.getSort(), request.getAlgorithmId());
            baseResp = BaseResp.succResp();
            baseResp.setData(loadFeatureUltraFcDayDTOS);
        } catch (Exception e) {
            e.printStackTrace();
            baseResp = BaseResp.failResp("查询超短期预测日准确率失败");
        }
        return baseResp;
    }


    /**
     * 时刻准确率
     */
    @ApiOperation(value = "时刻准确率")
    @RequestMapping(value = "/dayTime", method = RequestMethod.POST)
    public BaseResp<List<LoadUltraFcAccuracyDTO>> dayTimeAccuracy(@RequestBody LoadUltraFcFeatureRequest request) throws Exception {
        BaseResp baseResp = null;
        try {
            String caliberId = request.getCaliberId();
            if (StringUtils.isEmpty(caliberId)){
                caliberId = getCaliberId();
            }
            Date date = request.getDate();
            if (date == null) {
                date = this.getSystemDate();
            }
            List<LoadUltraFcAccuracyDTO> loadUltraFcAccuracyDTOS =
                accuracyEvaluateService.findDayTimeAccuracy(date, request.getCityId(), caliberId, request.getType(), request.getSort(), request.getAlgorithmId());
            baseResp = BaseResp.succResp();
            baseResp.setData(loadUltraFcAccuracyDTOS);
        } catch (Exception e) {
            e.printStackTrace();
            baseResp = BaseResp.failResp("查询超短期预测时刻准确率失败");
        }
        return baseResp;
    }
}
