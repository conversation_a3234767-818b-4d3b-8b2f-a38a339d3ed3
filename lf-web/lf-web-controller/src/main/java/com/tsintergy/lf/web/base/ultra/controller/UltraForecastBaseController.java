package com.tsintergy.lf.web.base.ultra.controller;

import com.tsieframework.core.base.dao.type.DataPointListMeta;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.aif.algorithm.serviceapi.base.constants.ShortConstants;
import com.tsintergy.aif.tool.core.utils.data.ColumnUtil;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.core.util.LoadCalUtil;
import com.tsintergy.lf.serviceapi.algorithm.dto.AlgorithmEnum;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.forecast.api.ForecastService;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcService;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.ultra.api.LoadCityHisUltraBasicService;
import com.tsintergy.lf.serviceapi.base.ultra.forecast.api.ForecastAlarmService;
import com.tsintergy.lf.serviceapi.base.ultra.forecast.dto.ForecastAlarmAllDTO;
import com.tsintergy.lf.serviceapi.base.ultra.forecast.dto.ForecastAlarmConfigDTO;
import com.tsintergy.lf.serviceapi.base.ultra.forecast.dto.ForecastAlarmCurveDTO;
import com.tsintergy.lf.serviceapi.base.ultra.load.api.LoadCityFcUltraService;
import com.tsintergy.lf.serviceapi.base.ultra.load.pojo.LoadCityFcUltraDO;
import com.tsintergy.lf.serviceapi.base.ultra.pojo.LoadCityHisUltraBasicDO;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityHisService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisDO;
import com.tsintergy.lf.web.base.ultra.request.UltraForecastParamRequest;
import com.tsintergy.lf.web.base.ultra.response.UltraForecastResponse;
import com.tsintergy.lf.web.base.weather.response.WeatherDataResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2022/12/30
 * @since 1.0.0
 */
@Slf4j
@Api(value = "超短期预测", tags = "[超短期模块][超短期预测][查询与触发]")
@RestController
 @RequestMapping("/ultra")
public class UltraForecastBaseController extends UltraBaseController {

    @Resource
    LoadCityHisUltraBasicService loadCityHisUltraBasicService;

    @Resource
    LoadCityFcUltraService loadCityFcUltraService;

    @Autowired
    CityService cityService;

    @Autowired
    WeatherCityHisService weatherCityHisService;

    @Autowired
    WeatherCityFcService weatherCityFcService;

    @Autowired
    ForecastAlarmService forecastAlarmService;

    @Autowired
    LoadCityFcService loadCityFcService;


    @Autowired
    ForecastService forecastService;



    /**
     * 超短期 手动预测接口
     *
     * @param ultraForecastParamRequest 时间间隔 5 or 15
     */
    @ApiOperation("超短期手动预测接口")
    @RequestMapping(value = "/forecast/algorithm", method = RequestMethod.POST)
    public BaseResp getShortCurve(@RequestBody  UltraForecastParamRequest ultraForecastParamRequest) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        String caliberId = this.getCaliberId();
        Integer timeSpan = ultraForecastParamRequest.getTimeSpan();
        String algorithmId = ultraForecastParamRequest.getAlgorithmId();
        String cityId = ultraForecastParamRequest.getCityId();
        Date date = getSystemDate();
        int totalColums = 0;
        if (com.tsintergy.lf.core.constants.ShortConstants.MINUTE.equals(timeSpan)) {
            totalColums = 288;
        } else {
            totalColums = 96;
        }
        String hour = DateUtil.getStrHourFromDate(new Date());
        Integer startTimePoint = Integer
            .valueOf(String.valueOf(DateUtil
                .getTimePoint(hour, totalColums)
            ));
        //超短期预测
        if(algorithmId.equals(com.tsintergy.aif.algorithm.serviceapi.base.dto.AlgorithmEnum.ULTRA_CATBOOST_FORECAST.getId())) {
            forecastService
                .doUltraForecast(cityId, caliberId, timeSpan, date, startTimePoint,
                    com.tsintergy.aif.algorithm.serviceapi.base.dto.AlgorithmEnum.ULTRA_CATBOOST_FORECAST, Constants.FORECAST);
        }else if(algorithmId.equals(com.tsintergy.aif.algorithm.serviceapi.base.dto.AlgorithmEnum.ULTRA_LGB_FORECAST.getId())) {
            forecastService
                .doUltraForecast(cityId, caliberId, timeSpan, date, startTimePoint,
                    com.tsintergy.aif.algorithm.serviceapi.base.dto.AlgorithmEnum.ULTRA_LGB_FORECAST, Constants.FORECAST);
        }
        return baseResp;
    }




    /**
     * 超短期预测页面查询数据；
     */
    @GetMapping("/forecast/data")
    @ApiOperation("预测查询")
    BaseResp<UltraForecastResponse> queryData(Date startDate, Date endDate, String cityId, Integer timeSpan, String caliberId, String algorithmId) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<Date> dayList = com.tsintergy.aif.tool.core.utils.date.DateUtil.getListBetweenDay(startDate, endDate);
        UltraForecastResponse response = new UltraForecastResponse();
        if (StringUtils.isEmpty(caliberId)) {
            caliberId = this.getCaliberId();
        }
        List<BigDecimal> real = new ArrayList<>();
        //超短期预测数据
        List<BigDecimal> ultraLine = new ArrayList<>();
        List<BigDecimal> fc = new ArrayList<>();
        Integer totalColumn = DataPointListMeta.MINUTE_5 == timeSpan ? DataPointListMeta.POINTS_288 : DataPointListMeta.POINTS_96;
        for (Date day : dayList) {

            LoadCityHisUltraBasicDO loadCityHisDO = loadCityHisUltraBasicService.getLoadCityHisDO(cityId, caliberId, day, timeSpan);
            if (ObjectUtils.isEmpty(loadCityHisDO)) {
                //获取空值集合
                real.addAll(ColumnUtil.getZeroOrNullList(totalColumn, null));
            } else {
                real.addAll(loadCityHisDO.getTvData());
            }

            LoadCityFcUltraDO fcDO = loadCityFcUltraService
                    .getLoadCityFcDO(day, cityId, caliberId, algorithmId, timeSpan, Constants.ULTRA_FORECAST_1);
            if (fcDO != null) {
                ultraLine.addAll(fcDO.getTvData());
            } else {
                ultraLine.addAll(ColumnUtil.getZeroOrNullList(totalColumn, null));
            }

            LoadCityFcDO fcByAlgorithmId = loadCityFcService.getLoadFc(day, cityId, caliberId, AlgorithmEnum.QR_FORECAST.getId());
            if (fcDO != null) {
                fc.addAll(fcByAlgorithmId.getloadList());
            } else {
                fc.addAll(ColumnUtil.getZeroOrNullList(totalColumn, null));
            }
        }
        if (!CollectionUtils.isEmpty(real) && real.size() > 0) {
            response.setReal(real);
        }
        response.setUltraLine(ultraLine);
        if (response.getFc() == null && response.getReal() == null && response.getUltraLine() == null) {
            return new BaseResp("T706");
        }
        response.setFc(fc);

        List<String> timeStrings = fixTimeList(dayList, totalColumn);
        response.setTimeList(timeStrings);
        baseResp.setData(response);
        return baseResp;
    }

    @ApiOperation("获取天气")
    @GetMapping(value = "/weather/days")
    public BaseResp<WeatherDataResp> findWeather(@ApiParam("城市ID") String cityId, @ApiParam("开始日期") Date startDate,
        @ApiParam("结束日期") Date endDate,
        @ApiParam("类型") Integer type) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        cityId = cityService.findWeatherCityId(cityId);
        List<WeatherCityHisDO> weatherCityHisVOS = weatherCityHisService
            .findWeatherCityHisDOs(cityId, type, startDate, endDate);
        List<WeatherCityFcDO> fcVOS = weatherCityFcService.findWeatherCityFcDOs(cityId, type, startDate, endDate);
        if (weatherCityHisVOS.size() < 1 && fcVOS.size() < 1) {
            return new BaseResp("T706");
        }
        //转map
        Map<Date, WeatherCityHisDO> realMap = weatherCityHisVOS.stream()
            .collect(Collectors.toMap(WeatherCityHisDO::getDate, Function.identity(), (key1, key2) -> key2));
        Map<Date, WeatherCityFcDO> fcMap = fcVOS.stream()
            .collect(Collectors.toMap(WeatherCityFcDO::getDate, Function.identity(), (key1, key2) -> key2));
        List<Date> dateList = DateUtil.getListBetweenDay(startDate, endDate);
        List<BigDecimal> his = new ArrayList<>();
        List<BigDecimal> fc = new ArrayList<>();
        WeatherDataResp dataResp = new WeatherDataResp();
        for (Date date : dateList) {
            WeatherCityHisDO hisVO = realMap.get(date);
            if (hisVO != null) {
                his.addAll(BasePeriodUtils
                    .toList(hisVO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO));
            } else {
                his.addAll(LoadCalUtil.getNullList(Constants.LOAD_CURVE_POINT_NUM));
            }
            WeatherCityFcDO fcVO = fcMap.get(date);
            if (fcVO != null) {
                fc.addAll(
                    BasePeriodUtils.toList(fcVO, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO));
            } else {
                fc.addAll(LoadCalUtil.getNullList(Constants.LOAD_CURVE_POINT_NUM));
            }
        }
        dataResp.setForecast(fc);
        dataResp.setReal(his);
        baseResp.setData(dataResp);
        return baseResp;
    }




    @GetMapping("/forecast/alarm")
    public BaseResp<ForecastAlarmAllDTO> findForecastAlarm(Date date, String cityId, Integer timeSpan,
        String caliberId, String algorithmId) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        ForecastAlarmAllDTO forecastAlarm = forecastAlarmService.findForecastAlarm(date, cityId, timeSpan, caliberId,
            algorithmId);
        if (Objects.isNull(forecastAlarm)) {
            return new BaseResp("T706");
        } else {
            baseResp.setData(forecastAlarm);
        }
        return baseResp;
    }

    @GetMapping("/forecast/alarm/config")
    public BaseResp<ForecastAlarmConfigDTO> findForecastAlarmConfig(String cityId) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        ForecastAlarmConfigDTO forecastAlarmConfig = forecastAlarmService.findForecastAlarmConfig(cityId);
        if (Objects.isNull(forecastAlarmConfig)) {
            return new BaseResp("T706");
        } else {
            baseResp.setData(forecastAlarmConfig);
        }
        return baseResp;
    }

    @PostMapping("/forecast/alarm/config")
    public void findForecastAlarmConfig(@RequestBody ForecastAlarmConfigDTO forecastAlarmConfigDTO) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        forecastAlarmService.saveForecastAlarmConfig(forecastAlarmConfigDTO);
    }

    @GetMapping("/forecast/alarm/curve")
    public BaseResp<ForecastAlarmCurveDTO> findForecastAlarmCurve(Date date, String cityId, Integer timeSpan,
        String caliberId, String algorithmId) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        ForecastAlarmCurveDTO forecastAlarmCurve = forecastAlarmService.findForecastAlarmCurve(date, cityId, timeSpan,
            caliberId, algorithmId);
        if (Objects.isNull(forecastAlarmCurve)) {
            return new BaseResp("T706");
        } else {
            baseResp.setData(forecastAlarmCurve);
        }
        return baseResp;
    }

    private List<String> fixTimeList(List<Date> dates, Integer totalColums) {
        List<String> columns = ColumnUtil.getColumns(totalColums, Constants.LOAD_CURVE_START_WITH_ZERO, false);
        List<String> result = new ArrayList<>();
        for (Date date : dates) {
            String dateString = DateUtils.date2String(date, DateFormatType.SIMPLE_DATE_FORMAT_STR);
            for (String column : columns) {
                String finalTime = dateString + " " + column.substring(0, 2) + ":" + column.substring(2);
                result.add(finalTime);
            }
        }
        return result;
    }


    /**
     * 判断数据null，如果是 则填充null
     *
     * @param timeSpan 5or15
     */
    private List<BigDecimal> checkNull(List<BigDecimal> dataList, Integer timeSpan) {
        if (!CollectionUtils.isEmpty(dataList)) {
            return dataList;
        }
        if (ShortConstants.MINUTE.equals(timeSpan)) {
            dataList.addAll(ColumnUtil.getZeroOrNullList(DataPointListMeta.POINTS_288, null));
        } else {
            dataList.addAll(ColumnUtil.getZeroOrNullList(DataPointListMeta.POINTS_96, null));
        }
        return dataList;
    }


}
