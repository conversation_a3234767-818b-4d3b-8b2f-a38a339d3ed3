/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司
 * Author:  wangchen
 * Date:  2019/4/24 17:16
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.security.request;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;

/**
 * Description: 权限参数 <br>
 *
 * <AUTHOR>
 * @create 2019/4/24 
 * @since 1.0.0
 */
public class PermissionRequest implements Serializable {
    @ApiModelProperty(value = "权限ID")
    private String roleid;
    @ApiModelProperty(value = "用户ID")
    private String userid;

    public String getRoleid() {
        return roleid;
    }

    public void setRoleid(String roleid) {
        this.roleid = roleid;
    }

    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }
}
