package com.tsintergy.lf.web.base.evalucation.controller;

import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.serviceapi.base.load.api.LoadAccuracyCityMonthFcService;
import com.tsintergy.lf.serviceapi.base.load.dto.ComparisonDTO;
import com.tsintergy.lf.serviceapi.base.load.dto.DayPrecisionDTO;
import com.tsintergy.lf.serviceapi.base.load.dto.OutrightPrecisionDTO;
import com.tsintergy.lf.web.base.controller.CommonBaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2021/4/14
 * @since 1.0.0
 */
@Api(tags = "月精确度")
@RequestMapping("/month-precision")
@RestController
public class MonthAccuracyController extends CommonBaseController {

    @Autowired
    private LoadAccuracyCityMonthFcService loadAccuracyCityMonthFcService;

    private static final Logger logger = LoggerFactory.getLogger(MonthAccuracyController.class);

    @ApiOperation("准确率")
    @RequestMapping(value = "/precision", method = RequestMethod.GET)
    public BaseResp<OutrightPrecisionDTO>  getListByCityIdAndDate(@RequestParam @ApiParam(value = "城市id") String cityId,
        @RequestParam @ApiParam(value = "开始时间")String startDate,
        @RequestParam @ApiParam(value = "结束时间")String endDate) {
        BaseResp baseResp = BaseResp.succResp();
        try {
            OutrightPrecisionDTO listByCityIdAndDate = loadAccuracyCityMonthFcService
                .findListByCityIdAndDate(cityId, startDate, endDate);
            if (listByCityIdAndDate == null) {
                return new BaseResp("T706");
            }
            baseResp.setData(listByCityIdAndDate);
        } catch (Exception e) {
            logger.error("获取预测准确率下限数据失败！", e);
        }
        return baseResp;
    }

    @ApiOperation("日准确率")
    @RequestMapping(value = "/precision-day", method = RequestMethod.GET)
    public BaseResp<List<DayPrecisionDTO>> getDayPrecisionByDate(@RequestParam @ApiParam(value = "城市id")String cityId, @RequestParam@ApiParam(value = "月日期") String monthDate) {
        BaseResp baseResp = BaseResp.succResp();
        try {
            List<DayPrecisionDTO> dayPrecisionByDate = loadAccuracyCityMonthFcService
                .findDayPrecisionByDate(cityId, monthDate);
            baseResp.setData(dayPrecisionByDate);
        } catch (Exception e) {
            logger.error("获取日预测准确率数据失败！", e);
        }
        return baseResp;
    }

    @ApiOperation("历史比较")
    @RequestMapping(value = "/his-comparison", method = RequestMethod.GET)
    public BaseResp<List<ComparisonDTO>> getComparisonByDate(@RequestParam@ApiParam(value = "城市id") String cityId, @RequestParam @ApiParam(value = "开始时间")String startDate,
        @RequestParam @ApiParam(value = "结束时间")String endDate,
        @ApiParam(value = "比较年") String comparisonYears, @ApiParam(value = "精度标准")String accuracyFlag) {
        BaseResp baseResp = BaseResp.succResp();
        try {
            List<ComparisonDTO> comparisonByDate = loadAccuracyCityMonthFcService
                .findComparisonByDate(cityId, startDate, endDate, comparisonYears, accuracyFlag);
            baseResp.setData(comparisonByDate);
        } catch (Exception e) {
            logger.error("获取月份集合数据失败！", e);
        }
        return baseResp;
    }

}
