/**
 * Copyright(C),2015‐2021,北京清能互联科技有限公司
 */
package com.tsintergy.lf.web.base.evalucation.response;

import com.tsintergy.lf.serviceapi.base.evalucation.dto.WeatherDayDeviationDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2021/11/16 16:59
 * @Version: 1.0.0
 */
@Data
@ApiModel
public class DayDeciationResponse {
    @ApiModelProperty(value = "数据条数",example = "5")
    private Integer totalCount;
    @ApiModelProperty(value = "数据")
    private List<WeatherDayDeviationDTO> datas;

    /**
     * 高亮设置
     */
    private Integer highLightSetting;
}