/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.web.base.area.controller;

import com.tsieframework.cloud.security.serviceapi.system.api.RedisService;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.core.constants.CacheConstants;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.constants.SystemConstant;
import com.tsintergy.lf.serviceapi.base.area.api.BaseAreaService;
import com.tsintergy.lf.serviceapi.base.area.api.BasePlanService;
import com.tsintergy.lf.serviceapi.base.area.api.LoadAreaHisService;
import com.tsintergy.lf.serviceapi.base.area.api.WeatherAreaFcService;
import com.tsintergy.lf.serviceapi.base.area.api.WeatherAreaHisService;
import com.tsintergy.lf.serviceapi.base.area.api.WeatherFeatureAreaFcService;
import com.tsintergy.lf.serviceapi.base.area.api.WeatherFeatureAreaHisService;
import com.tsintergy.lf.serviceapi.base.area.dto.AreaFCModelDTO;
import com.tsintergy.lf.serviceapi.base.area.dto.BasePlanDTO;
import com.tsintergy.lf.serviceapi.base.area.dto.CityAndAreaDTO;
import com.tsintergy.lf.serviceapi.base.area.dto.CoefficientDTO;
import com.tsintergy.lf.serviceapi.base.area.dto.PlanDTO;
import com.tsintergy.lf.serviceapi.base.area.dto.PlanModelDTO;
import com.tsintergy.lf.serviceapi.base.area.dto.*;
import com.tsintergy.lf.serviceapi.base.area.enums.AreaModelEnum;
import com.tsintergy.lf.serviceapi.base.base.api.CaliberService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CaliberDO;

import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceapi.base.system.pojo.SettingSystemDO;
import com.tsintergy.lf.web.base.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/8/4 14:10
 * @Version: 1.0.0
 */
@Api(tags = "分区预测")
@RequestMapping("/areaFc")
@RestController
public class AreaController extends BaseController {

    @Autowired
    private BaseAreaService baseAreaService;

    @Autowired
    private BasePlanService basePlanService;

    @Autowired
    SettingSystemService settingSystemService;

    @Autowired
    LoadAreaHisService loadAreaHisService;

    @Autowired
    WeatherAreaFcService weatherAreaFcService;

    @Autowired
    WeatherAreaHisService weatherAreaHisService;

    @Autowired
    CaliberService caliberService;

    @Autowired
    RedisService redisService;

    @Autowired
    private WeatherFeatureAreaHisService weatherFeatureAreaHisService;

    @Autowired
    private WeatherFeatureAreaFcService weatherFeatureAreaFcService;

    @Autowired
    private ScheduledThreadPoolExecutor scheduledThreadPoolExecutor;

    @ApiOperation(value = "方案轮询")
    @RequestMapping(value = "/polling", method = RequestMethod.GET)
    public BaseResp polling() throws Exception {
        Object area_flag = redisService.redisGet(CacheConstants.AREA_FLAG, Integer.class);
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(area_flag);
        return baseResp;
    }

    @ApiOperation(value = "获取对应口径的网损系数以及状态")
    @RequestMapping(value = "/getNetLossCoefficient", method = RequestMethod.GET)
    public BaseResp<NetLossCoefficientDTO> getNetLossCoefficient() throws Exception {
        BaseResp baseResp = null;
        try {
            String caliberId = getCaliberId();
            SettingSystemDO settingSystemDO = settingSystemService.findByFieldId(SystemConstant.COEFFICIENT);
            String coefficientJsonFormat = settingSystemDO.getValue();
            NetLossCoefficientDTO netLossCoefficientDTO = settingSystemService.getNetLossCoefficientDTO(caliberId, coefficientJsonFormat);
            baseResp = BaseResp.succResp();
            baseResp.setData(netLossCoefficientDTO);
        } catch (Exception e) {
            e.printStackTrace();
            baseResp = BaseResp.failResp("获取对应口径的网损系数以及状态失败");
        }
        return baseResp;

    }


    @ApiOperation(value = "根据实施页面用户输入的网损系数更新对应口径下的值")
    @RequestMapping(value = "/updateNetLossCoefficient", method = RequestMethod.POST)
    public BaseResp updateCoefficient(@RequestBody NetLossCoefficientDTO netLossCoefficientDTO) throws Exception {
        String caliberId = getCaliberId();
        String netLossCoefficient = netLossCoefficientDTO.getNetLossCoefficient();
        String switchStatus = netLossCoefficientDTO.getSwitchStatus();
        settingSystemService
                .doUpdateOrSaveNetLossCoefficient(caliberId, netLossCoefficient,switchStatus);
        BaseResp baseResp = BaseResp.succResp();
        return baseResp;
    }



    @ApiOperation(value = "预测方案数据")
    @RequestMapping(value = "/coefficient", method = RequestMethod.GET)
    public BaseResp<CoefficientDTO> coefficient() throws Exception {


        SettingSystemDO byFieldId = settingSystemService.findByFieldId(SystemConstant.COEFFICIENT);
        String value = byFieldId.getValue();
        CoefficientDTO coefficientDTO = new CoefficientDTO();
        coefficientDTO.setKey(value);
        if (value.equals("2")) {
            SettingSystemDO fieldId = settingSystemService.findByFieldId(SystemConstant.COEFFICIENT_NUM);
            coefficientDTO.setValue(fieldId.getValue());
        }
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(coefficientDTO);
        return baseResp;
    }

    @ApiOperation(value = "预测方案数据")
    @RequestMapping(value = "/coefficient", method = RequestMethod.POST)
    public BaseResp coefficientNum(@RequestBody CoefficientDTO coefficientDTO) throws Exception {
        settingSystemService
            .doUpdateOrSaveValueByKey(SystemConstant.COEFFICIENT, coefficientDTO.getKey());
        settingSystemService
            .doUpdateOrSaveValueByKey(SystemConstant.COEFFICIENT_NUM, coefficientDTO.getValue());
        BaseResp baseResp = BaseResp.succResp();
        return baseResp;
    }

    @ApiOperation(value = "预测方案数据")
    @RequestMapping(value = "/planList", method = RequestMethod.GET)
    public BaseResp<List<BasePlanDTO>> planList() {
        List<BasePlanDTO> allPlans = basePlanService.findAllPlans();
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(allPlans);
        return baseResp;
    }

    @ApiOperation(value = "分区页面数据")
    @RequestMapping(value = "/cityList", method = RequestMethod.GET)
    public BaseResp<CityAndAreaDTO> cityAndArea(String planId) throws Exception {
        CityAndAreaDTO cityAndAreaDTO = baseAreaService.findCityAndAreaDTO(planId);
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(cityAndAreaDTO);
        return baseResp;
    }

    @ApiOperation(value = "预测方案数据")
    @RequestMapping(value = "/type", method = RequestMethod.GET)
    public BaseResp<List<AreaFCModelDTO>> fcType() {
        AreaModelEnum[] values = AreaModelEnum.values();
        List<AreaFCModelDTO> areaFCModelDTOS = new ArrayList<>();
        for (AreaModelEnum value : values) {
            AreaFCModelDTO areaFCModelDTO = new AreaFCModelDTO();
            areaFCModelDTO.setId(value.getKey());
            areaFCModelDTO.setName(value.getValue());
            areaFCModelDTOS.add(areaFCModelDTO);
        }
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(areaFCModelDTOS);
        return baseResp;
    }


    @ApiOperation(value = "修改方案")
    @RequestMapping(value = "/area", method = RequestMethod.POST)
    public BaseResp planAndArea(@RequestBody PlanDTO plan) throws Exception {
        redisService.redisAdd(CacheConstants.AREA_FLAG, 1);
        //修改处理方案
        if (plan.getPlanName() != null) {
            try {
                basePlanService.findPlanByName(plan.getPlanName(), plan.getPlanId());
            } catch (Exception e) {
                BaseResp baseResp = BaseResp.failResp();
                baseResp.setRetCode("T666");
                baseResp.setRetMsg(e.getCause().getCause().getMessage());
                return baseResp;
            }
        }
        List<String> list = baseAreaService.doSaveOrUpdate(plan.getAreas());
        basePlanService.doUpdate(plan.getPlanId(), plan.getPlanName(), null, list, plan.getFcMode());

        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
//                    deleteData(list);
                    statData(list);
                } catch (Exception e) {
                    e.printStackTrace();
                    redisService.redisAdd(CacheConstants.AREA_FLAG, 2);
                }
            }
        }).start();


        BaseResp baseResp = BaseResp.succResp();
        return baseResp;
    }

    private void deleteData(List<String> list) throws Exception {
        loadAreaHisService.deleteByAreasIds(list);
        weatherAreaHisService.deleteByAreasIds(list);
        weatherAreaFcService.deleteByAreasIds(list);
    }

    private void statData(List<String> list) throws Exception {
        StringBuffer stringBuffer = new StringBuffer();
        for (int i = 0; i < list.size(); i++) {
            if (i == 0) {
                stringBuffer.append(list.get(i));
            } else {
                stringBuffer.append("," + list.get(i));
            }
        }
        List<CaliberDO> allCalibers = caliberService.findAllCalibers();
        Date start = DateUtils.addDays(new Date(), -1200);
        Date end = new Date();
        List<String> areaIds = Arrays.asList(stringBuffer.toString().split(Constants.SEPARATOR_PUNCTUATION));
        final int[] count = {0};
        try {
            for (String areaId:areaIds) {
                scheduledThreadPoolExecutor.schedule(new Runnable() {
                    @SneakyThrows
                    @Override
                    public void run() {
                        long s = System.currentTimeMillis();
                        for (CaliberDO allCaliber : allCalibers) {
                            loadAreaHisService
                                .statAreaHisLoad(allCaliber.getId(),areaId, start, end);
                        }
                        weatherAreaFcService.statAreaFcWeather(areaId, end, DateUtils.addDays(end, 3));
                        weatherAreaHisService.statAreaHisWeather(areaId, start, end);
                        weatherFeatureAreaFcService.statAreaWeatherFcFeature(end,DateUtils.addDays(end, 3),areaId);
                        weatherFeatureAreaHisService.statAreaWeatherHisFeature(start,end,areaId);
                        synchronized (this.getClass()){
                            count[0]++;
                            if (count[0] == areaIds.size()){
                                redisService.redisAdd(CacheConstants.AREA_FLAG, 2);
                            }
                        }
                        System.out.println(System.currentTimeMillis() - s);
                        Thread.sleep(1000);
                    }
                }, 0, TimeUnit.MILLISECONDS);
            }
        }catch (Exception e){
            redisService.redisAdd(CacheConstants.AREA_FLAG, 2);
        }
    }

    @ApiOperation(value = "分区删除")
    @RequestMapping(value = "/area", method = RequestMethod.DELETE)
    public BaseResp area(@RequestBody List<String> areaList) throws Exception {
        baseAreaService.doDelete(areaList);
        deleteData(areaList);
        BaseResp baseResp = BaseResp.succResp();
        return baseResp;
    }

    @ApiOperation(value = "方案修改或者删除")
    @RequestMapping(value = "/plan", method = RequestMethod.POST)
    public BaseResp planList(@RequestBody PlanModelDTO planModelDTO) throws Exception {
        boolean delete = planModelDTO.getDelete();
        if (delete) {
            basePlanService.doDelete(planModelDTO.getPlanId());
            deleteData(planModelDTO.getAreaIds());
        } else {
            basePlanService
                .doUpdate(planModelDTO.getPlanId(), planModelDTO.getPlanName(), planModelDTO.getValid(), null,
                    planModelDTO.getFcMode());
        }
        BaseResp baseResp = BaseResp.succResp();
        return baseResp;
    }

    @ApiOperation(value = "预测方案数据新增")
    @RequestMapping(value = "/newPlan", method = RequestMethod.POST)
    public BaseResp newPlan(@RequestBody PlanModelDTO planModelDTO) throws Exception {
        redisService.redisAdd(CacheConstants.AREA_FLAG, 1);
        List<String> list = baseAreaService.doSaveOrUpdate(planModelDTO.getAreas());

        basePlanService.doInsert(planModelDTO.getPlanName(), list,planModelDTO.getFcMode());
            new Thread(new Runnable() {
                @Override
                public void run() {
                    try {
                        statData(list);
                    } catch (Exception e) {
                        e.printStackTrace();
                        redisService.redisAdd(CacheConstants.AREA_FLAG, 2);
                    }
                }
            }).start();
        BaseResp baseResp = BaseResp.succResp();
        return baseResp;
    }

}