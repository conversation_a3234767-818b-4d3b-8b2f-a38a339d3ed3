package com.tsintergy.lf.web.base.load.controller;

import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.web.BaseResp;
import com.tsieframework.util.compatible.BigDecimalUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.api.LoadMonthYearFeatureService;
import com.tsintergy.lf.serviceapi.base.base.dto.LoadMonthFeatureDTO;
import com.tsintergy.lf.serviceapi.base.bgd.api.LoadFeatureCityTenDaysHisService;
import com.tsintergy.lf.serviceapi.base.bgd.pojo.LoadFeatureCityTenDaysHisServiceDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityDayHisService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityDayHisDO;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityHisService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityDayHisService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityTenDaysHisService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayHisDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityTenDaysHisDO;
import com.tsintergy.lf.web.base.load.request.LoadAnalyseRequest;
import com.tsintergy.lf.web.base.load.response.FeatureAnalyseResp;
import com.tsintergy.lf.web.base.load.response.LoadWeatherHisYearMonthResp;
import com.tsintergy.lf.web.base.load.response.YearMonthFeatureAnalyseResp;
import com.tsintergy.lf.web.base.load.response.YearMonthFeatureStatisticsResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 旬负荷特性
 *
 * @author: wangh
 **/
@RequestMapping("/character/tenDay")
@RestController
@Api(tags = "城市特征旬历史控制器")
public class LoadFeatureCityTenDayHisController extends BaseLoadFeatureController {

    @Autowired
    private LoadCityHisService loadCityHisService;

    @Autowired
    private LoadFeatureCityTenDaysHisService loadFeatureCityTenDaysHisService;

    @Autowired
    private WeatherCityHisService weatherCityHisService;

    @Autowired
    private WeatherFeatureCityTenDaysHisService weatherFeatureCityTenDaysHisService;

    @Autowired
    private CityService cityService;

    @Autowired
    private LoadFeatureCityDayHisService loadFeatureCityDayHisService;

    @Autowired
    private WeatherFeatureCityDayHisService weatherFeatureCityDayHisService;

    @Autowired
    private LoadMonthYearFeatureService loadMonthYearFeatureService;

    @RequestMapping(value = "/power_K", method = RequestMethod.POST)
    @ApiOperation("获取旬负荷图表")
    public BaseResp<List<FeatureAnalyseResp>> getTenDayLoadKChart(@RequestBody LoadAnalyseRequest monthRequest) throws Exception {
        //参数检查 如果时间为空，结束时间在系统时间当前时间，开始时间往前推2个月
        this.checkParam(monthRequest);
        String endYear = DateUtil.getYearByDate(DateUtils.addMonths(this.getSystemDate(), -1));
        String endMonth = DateUtil.getMonthByDate(DateUtils.addMonths(this.getSystemDate(), -1)).substring(5);
        String startYear = DateUtil.getYearByDate(DateUtils.addMonths(this.getSystemDate(), -2));
        String startMonth = DateUtil.getMonthByDate(DateUtils.addMonths(this.getSystemDate(), -2)).substring(5);
        String startYM = startYear + "-" + startMonth;
        String endYM = endYear + "-" + endMonth;
        if (monthRequest.getStartDate() != null) {
            startYM = monthRequest.getStartDate();
        }
        if (monthRequest.getEndDate() != null) {
            endYM = monthRequest.getEndDate();
        }

        String weatherCityId = cityService.findWeatherCityId(monthRequest.getCityId());
        String caliberId = getCaliberId();
        //旬特性
        List<LoadFeatureCityTenDaysHisServiceDO> powerFeatureCityDayHisStatDOS =
                loadFeatureCityTenDaysHisService.findByRangeCondition(monthRequest.getCityId(), caliberId, startYM, endYM);
        List<WeatherFeatureCityTenDaysHisDO> weatherFeatureCityTenDaysHisDOS = weatherFeatureCityTenDaysHisService.findByRangeCondition(weatherCityId, startYM, endYM);
        if (CollectionUtils.isEmpty(powerFeatureCityDayHisStatDOS)) {
            throw TsieExceptionUtils.newBusinessException("T706");
        }
        //将气象的日期和降雨量放到map中
        Map<String, BigDecimal> rainfallMap = new HashMap<>();
        //将气象的日期和最高温放到map中
        Map<String, BigDecimal> highestTemperatureMap = new HashMap<>();
        if (weatherFeatureCityTenDaysHisDOS != null && weatherFeatureCityTenDaysHisDOS.size() > 0) {
            Map<String, List<WeatherFeatureCityTenDaysHisDO>> weatherFeatureYearMap = weatherFeatureCityTenDaysHisDOS
                    .stream().collect(Collectors.groupingBy(WeatherFeatureCityTenDaysHisDO::getYear));

            weatherFeatureYearMap.forEach((year, weatherFeatureCityTenDayHisStatDOList) -> {
                Map<String, BigDecimal> tempRainfallMap = weatherFeatureCityTenDayHisStatDOList.stream().collect(
                        Collectors.toMap(x -> x.getYear() + "-" + x.getMonth() + "-" + x.getType(),
                                weather -> weather.getRainfall() == null ? BigDecimal.ZERO : weather.getRainfall()));
                rainfallMap.putAll(tempRainfallMap);

                Map<String, BigDecimal> highestTemperatureDateMap = weatherFeatureCityTenDayHisStatDOList.stream().collect(
                        Collectors.toMap(x -> x.getYear() + "-" + x.getMonth() + "-" + x.getType(),
                                WeatherFeatureCityTenDaysHisDO::getHighestTemperature));
                highestTemperatureMap.putAll(highestTemperatureDateMap);
            });
        }
        Map<String, List<LoadFeatureCityTenDaysHisServiceDO>> mapByDate = powerFeatureCityDayHisStatDOS.stream()
                .collect(Collectors.groupingBy(dp -> dp.getYear() + "-" + dp.getMonth() + "-" + dp.getType()));

        //构建vo
        List<YearMonthFeatureAnalyseResp> resultList = new ArrayList<>();
        powerFeatureCityDayHisStatDOS.forEach(powerFeatureCityDayHisStatDO -> {
            YearMonthFeatureAnalyseResp resp = new YearMonthFeatureAnalyseResp();
            BeanUtils.copyProperties(powerFeatureCityDayHisStatDO, resp);
            resp.setDate(powerFeatureCityDayHisStatDO.getYear() + "-" + powerFeatureCityDayHisStatDO.getMonth() + "-" + powerFeatureCityDayHisStatDO.getType());
            //获取前三旬的出力数据
            List<LoadFeatureCityTenDaysHisServiceDO> firstData = mapByDate.get(powerFeatureCityDayHisStatDO.getYear() + "-" + powerFeatureCityDayHisStatDO.getMonth() + "-上旬");
            List<LoadFeatureCityTenDaysHisServiceDO> secondData = mapByDate.get(powerFeatureCityDayHisStatDO.getYear() + "-" + powerFeatureCityDayHisStatDO.getMonth() + "-中旬");
            List<LoadFeatureCityTenDaysHisServiceDO> thirdData = mapByDate.get(powerFeatureCityDayHisStatDO.getYear() + "-" + powerFeatureCityDayHisStatDO.getMonth() + "-下旬");
            if (firstData != null && secondData != null && thirdData != null) {
                BigDecimal add = BigDecimalUtils.add(firstData.get(0).getAveLoad(), secondData.get(0).getAveLoad());
                BigDecimal add1 = BigDecimalUtils.add(add, thirdData.get(0).getAveLoad());
                BigDecimal divide = BigDecimalUtils.divide(add1, new BigDecimal(3), 4);
                resp.setAvgPeriod(divide);
            }
            resp.setAvgLoad(powerFeatureCityDayHisStatDO.getAveLoad());
            resp.setRain(rainfallMap.get(
                    powerFeatureCityDayHisStatDO.getYear() + "-" + powerFeatureCityDayHisStatDO.getMonth() + "-" + powerFeatureCityDayHisStatDO.getType()));
            resp.setHighestTemperature(highestTemperatureMap.get(
                    powerFeatureCityDayHisStatDO.getYear() + "-" + powerFeatureCityDayHisStatDO.getMonth() + "-" + powerFeatureCityDayHisStatDO.getType()));

            resultList.add(resp);
        });

        if (resultList.size() < 1) {
            throw TsieExceptionUtils.newBusinessException("T706");
        }
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(resultList);
        return baseResp;
    }

    /**
     * 特性分析-旬特性统计查询
     */
    @ApiOperation("旬特性统计查询")
    @RequestMapping(value = "/statisticsFeatureCity", method = RequestMethod.POST)
    public BaseResp<YearMonthFeatureStatisticsResp> statisticsFeatureCity(@RequestBody LoadAnalyseRequest loadAnalyseRequest) throws Exception {
        checkParam(loadAnalyseRequest);
        Date date = this.getSystemDate();
        if (loadAnalyseRequest.getDate() != null) {
            String dateStr = loadAnalyseRequest.getDate();
            date = DateUtils.string2Date(dateStr, DateFormatType.YEAR_MONTH_STR);
        }

        String caliberId = getCaliberId();
        String year = DateUtil.getYearByDate(date);
        String month = DateUtil.getMonthByDate(date).substring(5);
        Date last = DateUtils.addYears(date, -1);
        String lastYear = DateUtil.getYearByDate(last);
        String lastMonth = DateUtil.getMonthByDate(last).substring(5);
        String type = loadAnalyseRequest.getType();
        List<LoadFeatureCityTenDaysHisServiceDO> loadFeatureCityTenDaysDOS = loadFeatureCityTenDaysHisService
                .findLoadFeatureCityTenDaysHisServiceDOs(loadAnalyseRequest.getCityId(), caliberId, year, month, type);
        YearMonthFeatureStatisticsResp yearMonthFeatureStatisticsResp = new YearMonthFeatureStatisticsResp();
        if (CollectionUtils.isNotEmpty(loadFeatureCityTenDaysDOS)) {
            LoadFeatureCityTenDaysHisServiceDO loadTenDay = loadFeatureCityTenDaysDOS.get(0);
            yearMonthFeatureStatisticsResp.setAvgloadGradient(loadTenDay.getLoadGradient());
            yearMonthFeatureStatisticsResp.setAvgLoad(loadTenDay.getAveLoad());
            yearMonthFeatureStatisticsResp.setMaxLoad(loadTenDay.getMaxLoad());
            yearMonthFeatureStatisticsResp.setMinLoad(loadTenDay.getMinLoad());
            yearMonthFeatureStatisticsResp.setTotalEnergy(loadTenDay.getEnergy());
            yearMonthFeatureStatisticsResp.setDifferent(loadTenDay.getDifferent());
            yearMonthFeatureStatisticsResp.setGradient(loadTenDay.getGradient());
            yearMonthFeatureStatisticsResp.setDate(year + "-" + month + "-" + type);
        }
        List<LoadFeatureCityTenDaysHisServiceDO> lastLoadFeatureCityTenDaysDOS = loadFeatureCityTenDaysHisService
                .findLoadFeatureCityTenDaysHisServiceDOs(loadAnalyseRequest.getCityId(), caliberId, lastYear, lastMonth, type);
        if (CollectionUtils.isNotEmpty(lastLoadFeatureCityTenDaysDOS)) {
            LoadFeatureCityTenDaysHisServiceDO lastLoadTenDay = lastLoadFeatureCityTenDaysDOS.get(0);
            yearMonthFeatureStatisticsResp.setLastLoadGradient(lastLoadTenDay.getLoadGradient());
            yearMonthFeatureStatisticsResp.setLastAvgLoad(lastLoadTenDay.getAveLoad());
            yearMonthFeatureStatisticsResp.setLastMaxLoad(lastLoadTenDay.getMaxLoad());
            yearMonthFeatureStatisticsResp.setLastMinLoad(lastLoadTenDay.getMinLoad());
            yearMonthFeatureStatisticsResp.setLastDifferent(lastLoadTenDay.getDifferent());
            yearMonthFeatureStatisticsResp.setLastGradient(lastLoadTenDay.getGradient());
            yearMonthFeatureStatisticsResp.setLastTotalEnergy(lastLoadTenDay.getEnergy());
        }
        Date date2 = DateUtil.getDate(loadAnalyseRequest.getDate(), "yyyy-MM");
        Date first = DateUtil.getFirstDayOfMonth(date2);
        Date last1 = DateUtil.getLastDayOfMonth(date2);
        Date date1 = DateUtils.addYears(first, -1);
        Date date3 = DateUtils.addYears(last1, -1);
        List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisVOS = loadFeatureCityDayHisService.findLoadFeatureCityDayHisVOS(
                loadAnalyseRequest.getCityId(), first, last1, getCaliberId(loadAnalyseRequest.getCityId()));
        List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisVOS1 = loadFeatureCityDayHisService.findLoadFeatureCityDayHisVOS(
                loadAnalyseRequest.getCityId(), date1, date3, getCaliberId(loadAnalyseRequest.getCityId()));
        List<BigDecimal> bigDecimals = new ArrayList<>();
        List<BigDecimal> bigDecimals1 = new ArrayList<>();
        List<BigDecimal> bigDecimals2 = new ArrayList<>();
        List<BigDecimal> bigDecimals3 = new ArrayList<>();
        List<BigDecimal> bigDecimals4 = new ArrayList<>();
        List<BigDecimal> bigDecimals5 = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(loadFeatureCityDayHisVOS)) {
            for (LoadFeatureCityDayHisDO loadFeatureCityDayHisVO : loadFeatureCityDayHisVOS) {
                BigDecimal loadGradient = loadFeatureCityDayHisVO.getLoadGradient();
                bigDecimals.add(loadGradient);
                BigDecimal different = loadFeatureCityDayHisVO.getDifferent();
                bigDecimals2.add(different);
                BigDecimal gradient = loadFeatureCityDayHisVO.getGradient();
                bigDecimals4.add(gradient);
            }
        }
        if (CollectionUtils.isNotEmpty(bigDecimals)) {
            BigDecimal bigDecimal = com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(
                    bigDecimals);
            yearMonthFeatureStatisticsResp.setAvgloadGradient(bigDecimal);
        }
        if (CollectionUtils.isNotEmpty(loadFeatureCityDayHisVOS1)) {
            for (LoadFeatureCityDayHisDO loadFeatureCityDayHisVO : loadFeatureCityDayHisVOS1) {
                BigDecimal loadGradient = loadFeatureCityDayHisVO.getLoadGradient();
                bigDecimals1.add(loadGradient);
                BigDecimal different = loadFeatureCityDayHisVO.getDifferent();
                bigDecimals3.add(different);
                BigDecimal gradient = loadFeatureCityDayHisVO.getGradient();
                bigDecimals5.add(gradient);
            }
        }
        if (CollectionUtils.isNotEmpty(bigDecimals1)) {
            BigDecimal bigDecimal = com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(
                    bigDecimals1);
            yearMonthFeatureStatisticsResp.setLastLoadGradient(bigDecimal);
        }
        if (CollectionUtils.isNotEmpty(bigDecimals2)) {
            BigDecimal bigDecimal = com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(
                    bigDecimals2);
            yearMonthFeatureStatisticsResp.setDifferent(bigDecimal);
        }
        if (CollectionUtils.isNotEmpty(bigDecimals3)) {
            BigDecimal bigDecimal = com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(
                    bigDecimals3);
            yearMonthFeatureStatisticsResp.setLastDifferent(bigDecimal);
        }
        if (CollectionUtils.isNotEmpty(bigDecimals4)) {
            BigDecimal bigDecimal = com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(
                    bigDecimals4);
            yearMonthFeatureStatisticsResp.setGradient(bigDecimal);
        }
        if (CollectionUtils.isNotEmpty(bigDecimals5)) {
            BigDecimal bigDecimal = com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(
                    bigDecimals5);
            yearMonthFeatureStatisticsResp.setLastGradient(bigDecimal);
        }
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(yearMonthFeatureStatisticsResp);
        return baseResp;
    }

    /**
     * 特性分析-月历史曲线
     */
    @ApiOperation("旬历史曲线")
        @RequestMapping(value = "/hisPowerAndRain", method = RequestMethod.POST)
    public BaseResp<LoadWeatherHisYearMonthResp> getTenDayHisPowerAndRain(@RequestBody LoadAnalyseRequest loadAnalyseRequest) throws Exception {
        checkParam(loadAnalyseRequest);
        Date date = this.getSystemDate();
        String period = loadAnalyseRequest.getType();
        if (loadAnalyseRequest.getDate() != null) {
            String dateStr = loadAnalyseRequest.getDate();
            date = DateUtils.string2Date(dateStr, DateFormatType.YEAR_MONTH_STR);
        }
        Date startDate = DateUtil.getFirstDayOfTenDays(date, period);
        Date endDate = DateUtil.getLastDayOfTenDays(date, period);

        //获取月负荷特性
        List<LoadFeatureCityDayHisDO> loadList = loadFeatureCityDayHisService
                .findLoadFeatureCityDayHisDOS(loadAnalyseRequest.getCityId(), startDate, endDate,
                        getCaliberId(loadAnalyseRequest.getCityId()));
        if (CollectionUtils.isEmpty(loadList)) {
            throw TsieExceptionUtils.newBusinessException("T706");
        }
        List<Date> dateList = DateUtil.getListBetweenDay(startDate, endDate);
        Map<java.sql.Date, LoadFeatureCityDayHisDO> collect = loadList.stream()
                .collect(Collectors.toMap(LoadFeatureCityDayHisDO::getDate, Function.identity(), (key1, key2) -> key2));
        List<LoadFeatureCityDayHisDO> resultList = new ArrayList<>();
        for (Date oneDay : dateList) {
            LoadFeatureCityDayHisDO hisDO = collect.get(new java.sql.Date(oneDay.getTime()));
            resultList.add(hisDO);
        }
        List<BigDecimal> minPower = resultList.stream()
                .map(load -> load == null ? null : load.getMinLoad()).collect(Collectors.toList());
        List<BigDecimal> avePower = resultList.stream()
                .map(load -> load == null ? null : load.getAveLoad()).collect(Collectors.toList());
        List<BigDecimal> loadGradient = resultList.stream()
                .map(load -> load == null ? null : load.getLoadGradient()).collect(Collectors.toList());
        List<BigDecimal> maxPower = resultList.stream()
                .map(load -> load == null ? null : load.getMaxLoad()).collect(Collectors.toList());
        LoadWeatherHisYearMonthResp powerWeatherHisYearMonthVO = new LoadWeatherHisYearMonthResp();
        powerWeatherHisYearMonthVO.setAveLoad(avePower);
        powerWeatherHisYearMonthVO.setLoadGradient(loadGradient);
        powerWeatherHisYearMonthVO.setMaxLoad(maxPower);
        powerWeatherHisYearMonthVO.setMinLoad(minPower);

        //查询气象特性数据
        List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisStatDOS = weatherFeatureCityDayHisService
                .listWeatherFeatureCityDayHisDO(loadAnalyseRequest.getCityId(), startDate, endDate);
        if (!CollectionUtils.isEmpty(weatherFeatureCityDayHisStatDOS)) {
            Map<java.sql.Date, WeatherFeatureCityDayHisDO> weatherCollect = weatherFeatureCityDayHisStatDOS.stream()
                    .collect(Collectors.toMap(WeatherFeatureCityDayHisDO::getDate, Function.identity(), (key1, key2) -> key2));
            List<WeatherFeatureCityDayHisDO> weatherResultList = new ArrayList<>();
            for (Date oneDay : dateList) {
                WeatherFeatureCityDayHisDO weatherDO = weatherCollect.get(new java.sql.Date(oneDay.getTime()));
                weatherResultList.add(weatherDO);
            }
            //获取旬降雨量
            List<BigDecimal> rain = weatherResultList.stream()
                    .map(weather -> weather == null ? null : weather.getRainfall()).collect(Collectors.toList());

            //获取旬温度
            List<BigDecimal> highestTemperature = weatherResultList.stream()
                    .map(weather -> weather == null ? null : weather.getHighestTemperature()).collect(Collectors.toList());
            powerWeatherHisYearMonthVO.setRain(rain);
            powerWeatherHisYearMonthVO.setHighestTemperature(highestTemperature);
        }
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(powerWeatherHisYearMonthVO);
        return baseResp;
    }

    /**
     * 特性分析-旬历史曲线
     */
    @ApiOperation("旬历史负荷表格")
    @RequestMapping(value = "/hisTenDaysFeature", method = RequestMethod.GET)
    public BaseResp<List<LoadMonthFeatureDTO>> getHisTenDaysFeature(String startDate, String endDate,
                                                                    String cityId, String caliberId) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        if (caliberId == null) {
            caliberId = getCaliberId(cityId);
        }
        List<LoadMonthFeatureDTO> hisMonthFeature = loadMonthYearFeatureService.getHisTenDaysFeature(startDate, endDate,
                cityId, caliberId);
        baseResp.setData(hisMonthFeature);
        return baseResp;
    }

    private String getCaliberId(String cityId) {
        if (Constants.PROVINCE_TYPE == Integer.parseInt(cityId)) {
            return Constants.CALIBER_ID_BG_QW;
        } else {
            return Constants.CALIBER_ID_BG_DS;
        }
    }
}
