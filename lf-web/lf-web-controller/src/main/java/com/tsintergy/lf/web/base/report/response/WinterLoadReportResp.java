package com.tsintergy.lf.web.base.report.response;

import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class WinterLoadReportResp {

    /**
     * 日期串
     */
    private String dateStr;

    /**
     * 最大负荷出现时期
     */
    private String maxLoadPeriod;

    /**
     * 最大负荷偏差
     */
    private BigDecimal maxLoadDiff;

    /**
     * 最大负荷偏差率
     */
    @BigdecimalJsonFormat(percentConvert =100)
    private BigDecimal maxLoadDiffRate;

    /**
     * 早高峰最大负荷
     */
    private BigDecimal maxMorningPeak;

    /**
     * 晚高峰最大负荷
     */
    private BigDecimal maxEveningPeak;

    /**
     * 最大负荷时刻空调负荷
     */
    private BigDecimal acLoad;

    /**
     * 最大负荷时刻空调负荷偏差
     */
    private BigDecimal acLoadDiff;

    /**
     * 空调负荷曲线
     */
    private List<BigDecimal> acLoads;

    /**
     * 历史负荷曲线
     */
    private List<BigDecimal> hisLoads;

    /**
     * 温度曲线
     */
    private List<BigDecimal> temperatures;

    /**
     * 地市负荷特性
     */
    private List<CityLoadReportResp> cityLoadReports;
}
