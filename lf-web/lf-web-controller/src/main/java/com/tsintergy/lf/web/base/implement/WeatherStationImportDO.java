/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.web.base.implement;

import com.alibaba.excel.annotation.ExcelProperty;
import java.math.BigDecimal;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 *Description:  <br>
 *
 *@Author: <EMAIL>
 *@Date: 2022/6/21 16:37
 *@Version: 1.0.0
 */
@Getter
@Setter
@EqualsAndHashCode
public class WeatherStationImportDO {

    @ExcelProperty(index = 0)
    private String stationId;

    @ExcelProperty(index = 1)
    private String dateTime;

    @ExcelProperty(index = 3)
    private BigDecimal humidity;

    @ExcelProperty(index = 2)
    private BigDecimal temperature;

    @ExcelProperty(index = 4)
    private BigDecimal rainfall;

    @ExcelProperty(index = 5)
    private BigDecimal windspeed;
}