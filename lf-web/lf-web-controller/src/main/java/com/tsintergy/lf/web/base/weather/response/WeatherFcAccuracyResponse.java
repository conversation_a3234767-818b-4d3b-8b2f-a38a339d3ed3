/**
 * Copyright(C),2015‐2021,北京清能互联科技有限公司
 */
package com.tsintergy.lf.web.base.weather.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2021/11/16 17:04
 * @Version: 1.0.0
 */
@Data
@ApiModel
public class WeatherFcAccuracyResponse {

    @ApiModelProperty(value = "真实温度数据")
    private List<BigDecimal> hisTemBigDecimals;

    @ApiModelProperty(value = "预测温度数据")
    private List<BigDecimal> fcTemBigDecimals;

    @ApiModelProperty(value = "温度偏差")
    private List<BigDecimal> temDeviation;

    @ApiModelProperty(value = "真实雨量数据")
    private List<BigDecimal> hisRainBigDecimals;

    @ApiModelProperty(value = "预测雨量数据")
    private List<BigDecimal> fcRainBigDecimals;

    @ApiModelProperty(value = "雨量偏差")
    private List<BigDecimal> rainDeviation;
}