/**
 * Copyright(C),2015‐2021,北京清能互联科技有限公司
 */
package com.tsintergy.lf.web.base.datamanage.request;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * Description:  <br>
 *
 * @Author: liu<PERSON><EMAIL>
 * @Date: 2021/12/23 14:43
 * @Version: 1.0.0
 */
@Data
public class ForecastWeatherCitiesExcelRequest extends Base97ForecastWeatherExcelRequest {
    @ExcelProperty(value = "单位",index = 0)
    private String cityName;
    @ExcelProperty(value = "日期",index = 1)
    private String date;
    @ExcelProperty(value = "类型",index = 2)
    private String type;
}