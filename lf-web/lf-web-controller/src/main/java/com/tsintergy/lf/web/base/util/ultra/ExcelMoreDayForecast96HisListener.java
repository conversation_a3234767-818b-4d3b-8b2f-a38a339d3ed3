package com.tsintergy.lf.web.base.util.ultra;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.core.util.VslfDateUtil;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.ultra.api.LoadCityHisUltraBasicService;
import com.tsintergy.lf.serviceapi.base.ultra.pojo.LoadCityHisUltraBasicDO;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

/**
 * 数据导入监听类
 *
 * <AUTHOR>
 */
public class ExcelMoreDayForecast96HisListener extends AnalysisEventListener<ExcelMoreDayForecastDataHis96> {

    private List<ExcelMoreDayForecastDataHis96> list = new ArrayList();

    private LoadCityHisUltraBasicService loadCityHisUltraBasicService ;

    private String cityId;

    private String userId;

    private String caliberId;


    public ExcelMoreDayForecast96HisListener(LoadCityHisUltraBasicService loadCityHisUltraBasicService, String cityId, String userId,
        String caliberId) {
        this.loadCityHisUltraBasicService = loadCityHisUltraBasicService;
        this.cityId = cityId;
        this.userId = userId;
        this.caliberId = caliberId;
    }

    /**
     * 逐行解析数据
     */
    @Override
    public void invoke(ExcelMoreDayForecastDataHis96 data, AnalysisContext context) {
        list.add(data);
    }

    /**
     * 逐行的返回头信息
     */
    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {

    }


    /**
     * 所有数据解析完成后的回调函数
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<LoadCityHisUltraBasicDO> resultList = new ArrayList<>();
        list.forEach(e -> {
            LoadCityHisUltraBasicDO loadCityHisDO = new LoadCityHisUltraBasicDO();
            BeanUtils.copyProperties(e, loadCityHisDO);
            loadCityHisDO.setCityId(cityId);
            loadCityHisDO.setCaliberId(caliberId);
            String date1 = e.getDate();
            Date date = VslfDateUtil.getDate(date1, "yyyy-MM-dd");
            loadCityHisDO.setDateTime(new java.sql.Date(date.getTime()));
            loadCityHisDO.setId(e.getId());
            loadCityHisDO.setCreateTime(new Timestamp(System.currentTimeMillis()));
            loadCityHisDO.setUpdateTime(new Timestamp(System.currentTimeMillis()));
            List<BigDecimal> bigDecimals = BasePeriodUtils.toList(e, Constants.LOAD_CURVE_POINT_NUM,
                true);
            loadCityHisDO.setTvMeta(DataPointMetaBuilder.createPointMeta(date, 15));
            loadCityHisDO.setTvData(bigDecimals);
            resultList.add(loadCityHisDO);

        });
        try {
            loadCityHisUltraBasicService.doSaveOrUpdateList(resultList);
        } catch (Exception exception) {
            exception.printStackTrace();
        }
    }

}
