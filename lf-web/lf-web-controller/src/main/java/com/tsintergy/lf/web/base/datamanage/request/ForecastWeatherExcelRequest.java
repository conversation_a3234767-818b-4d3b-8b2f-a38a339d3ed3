/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.web.base.datamanage.request;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/1/14 10:49
 * @Version: 1.0.0
 */

@Data
public class ForecastWeatherExcelRequest {

    /**
     * 观测时间
     */
    @ExcelProperty(index = 0)
    private String dateTime;

    /**
     * 气温
     */
    @ExcelProperty(index = 1)
    private String temperature;

    /**
     * 相对湿度
     */
    @ExcelProperty(index = 2)
    private String humidity;
    /**
     * 降雨
     */
    @ExcelProperty(index = 3)
    private String rain;
    /**
     * 风速
     */
    @ExcelProperty(index = 4)
    private String wind;
    /**
     * 实感温度
     */
    @ExcelProperty(index = 5)
    private String realTemperature;
    /**
     * 湿寒
     */
    @ExcelProperty(index = 6)
    private String dampCold;


}