/**
 * Copyright(C);2015‐2022;北京清能互联科技有限公司
 */
package com.tsintergy.lf.web.base.trade.request;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/9/3 16:57
 * @Version: 1.0.0
 */
@Data
public class TradeRequest implements Serializable {

    String cityId;

    Date startDate;

    Date endDate;

    List<String> codeIds;
}