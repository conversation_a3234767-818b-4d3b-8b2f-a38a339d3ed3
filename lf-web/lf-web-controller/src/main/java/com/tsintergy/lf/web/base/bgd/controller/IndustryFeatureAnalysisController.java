package com.tsintergy.lf.web.base.bgd.controller;

import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.serviceapi.base.bgd.api.IndustryFeatureService;
import com.tsintergy.lf.serviceapi.base.bgd.dto.IndustrialImportTypeDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.IndustryAllTypeDateLoadDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.IndustryAllTypeLoadDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.IndustryEnergyHisDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.IndustryEnergyValueDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.IndustryLoadHisDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description: 产业负荷特性分析 <br>
 *
 * <AUTHOR>
 * @create 2023-02-16
 * @since 1.0.0
 */
@Api("产业负荷特性分析")
@RestController
@RequestMapping("/industry")
public class IndustryFeatureAnalysisController {

    @Autowired
    IndustryFeatureService industryFeatureService;

    @ApiOperation("产业累计电量占比")
    @GetMapping("/totalEnergy")
    public BaseResp<List<IndustryEnergyValueDTO>> queryTotalEnergy(String cityId, Date startDate, Date endDate){
        BaseResp baseResp = BaseResp.succResp();
        List<IndustryEnergyValueDTO> result = industryFeatureService.queryTotalEnergy(cityId, startDate,
            endDate);
        if (!Objects.isNull(result)){
            baseResp.setData(result);
            return baseResp;
        }
        return new BaseResp("T706");
    }

    @ApiOperation("负荷曲线")
    @GetMapping("/loadCurve")
    public BaseResp<List<IndustryLoadHisDTO>> queryIndustryLoad(String cityId, Date startDate, Date endDate){
        BaseResp baseResp = BaseResp.succResp();
        List<IndustryLoadHisDTO> result = industryFeatureService.queryLoadIndustry(cityId, startDate,
            endDate);
        if (!Objects.isNull(result)){
            baseResp.setData(result);
            return baseResp;
        }
        return new BaseResp("T706");
    }

    @ApiOperation("电量曲线")
    @GetMapping("/energyCurve")
    public BaseResp<List<IndustryLoadHisDTO>> queryIndustryEnergy(String cityId, Date startDate, Date endDate){
        BaseResp baseResp = BaseResp.succResp();
        List<IndustryLoadHisDTO> result = industryFeatureService.queryEnergyIndustry(cityId, startDate,
            endDate);
        if (!Objects.isNull(result)){
            baseResp.setData(result);
            return baseResp;
        }
        return new BaseResp("T706");
    }

    @ApiOperation("行业负荷分析")
    @GetMapping("/loadIndustry")
    public BaseResp<List<IndustryAllTypeLoadDTO>> queryAllLoadIndustry(String cityId, Date startDate, Date endDate){
        BaseResp baseResp = BaseResp.succResp();
        List<IndustryAllTypeLoadDTO> result = industryFeatureService.queryAllLoadIndustry(cityId,
            startDate, endDate);
        if (!Objects.isNull(result)){
            baseResp.setData(result);
            return baseResp;
        }
        return new BaseResp("T706");
    }

    @ApiOperation("行业累计电量占比")
    @GetMapping("/energyIndustry")
    public BaseResp<List<IndustryAllTypeLoadDTO>> queryAllEnergyIndustry(String cityId, Date startDate, Date endDate){
        BaseResp baseResp = BaseResp.succResp();
        List<IndustryAllTypeLoadDTO> result = industryFeatureService.queryAllEnergyIndustry(cityId,
            startDate, endDate);
        if (!Objects.isNull(result)){
            baseResp.setData(result);
            return baseResp;
        }
        return new BaseResp("T706");
    }

    @ApiOperation("行业负荷详情分析")
    @GetMapping("/loadIndustryDetails")
    public BaseResp<List<IndustryAllTypeLoadDTO>> queryAllLoadIndustryDetails(String cityId, Date startDate, Date endDate, String industryType){
        BaseResp baseResp = BaseResp.succResp();
        List<IndustryAllTypeLoadDTO> result = industryFeatureService.queryAllLoadIndustryDetails(cityId,
            startDate, endDate, industryType);
        if (!Objects.isNull(result)){
            baseResp.setData(result);
            return baseResp;
        }
        return new BaseResp("T706");
    }

    @ApiOperation("行业负荷详情-重点行业")
    @GetMapping("/loadCurveDetails")
    public BaseResp<List<IndustrialImportTypeDTO>> queryLoadCurveDetails(String cityId, Date startDate, Date endDate, String industryType){
        BaseResp baseResp = BaseResp.succResp();
        List<IndustrialImportTypeDTO> result = industryFeatureService.queryAllLoadDateIndustryDetails(
            cityId, startDate, endDate, industryType);
        if (!Objects.isNull(result)){
            baseResp.setData(result);
            return baseResp;
        }
        return new BaseResp("T706");
    }

}
