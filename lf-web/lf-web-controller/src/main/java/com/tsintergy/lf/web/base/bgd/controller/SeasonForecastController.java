/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.web.base.bgd.controller;

import com.alibaba.excel.util.CollectionUtils;
import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.bgd.api.SeasonForecastModelService;
import com.tsintergy.lf.serviceapi.base.bgd.dto.AlgorithmDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.FeaturesDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.MonthReportDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.MonthWeatherDefaultDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.MonthWeatherResultDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.MonthWeatherSettingDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.SeasonReportDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.TempCompareDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.TenDaysOfMonthAccuracyDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.TenDaysOfMonthFeatureDTO;
import com.tsintergy.lf.serviceapi.base.system.api.ReportSystemService;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceapi.base.system.pojo.ReportSystemInitDO;
import com.tsintergy.lf.web.base.common.util.ExcelUtil;
import io.swagger.annotations.ApiOperation;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * Description:  <br>
 * 度冬
 *
 * <AUTHOR>
 * @Author: <EMAIL>
 * @Date: 2022/11/19 13:43
 * @Version: 1.0.0
 * package com.tsintergy.lf.web.base.bgd.controller;
 * <p>
 * import com.tsieframework.core.base.web.BaseResp;
 * import com.tsintergy.lf.core.constants.Constants;
 * import com.tsintergy.lf.serviceapi.base.bgd.api.SeasonForecastModelService;
 * import com.tsintergy.lf.serviceapi.base.bgd.dto.SeasonReportDTO;
 * import com.tsintergy.lf.serviceapi.base.bgd.dto.TenDaysOfMonthFeatureDTO;
 * import io.swagger.annotations.ApiOperation;
 * import java.util.Date;
 * import java.util.List;
 * import org.springframework.beans.factory.annotation.Autowired;
 * import org.springframework.web.bind.annotation.GetMapping;
 * import org.springframework.web.bind.annotation.PostMapping;
 * import org.springframework.web.bind.annotation.RequestBody;
 * import org.springframework.web.bind.annotation.RequestMapping;
 * import org.springframework.web.bind.annotation.RestController;
 * <p>
 * /**
 * Description: TODO <br>
 * @create 2023-02-17
 * @since 1.0.0
 */
@RestController
@RequestMapping("/fcModel")
public class SeasonForecastController {

    @Autowired
    SeasonForecastModelService seasonForecastModelService;

    @Autowired
    SettingSystemService settingSystemService;

    @Autowired
    ReportSystemService reportSystemService;

    private String getCaliberId(String cityId){
        if (Constants.PROVINCE_TYPE == Integer.parseInt(cityId)){
            return Constants.CALIBER_ID_BG_QW;
        }else{
            return Constants.CALIBER_ID_BG_DS;
        }
    }

    @ApiOperation("获取季负荷预测")
    @GetMapping(value = "/season/getMonthLoadFc")
    public BaseResp<List<TenDaysOfMonthFeatureDTO>> getMonthLoadFc(String cityId, String caliberId, Date date, String season, String algorithmId) {
        BaseResp baseResp = BaseResp.succResp();
        List<TenDaysOfMonthFeatureDTO> monthFeatureDTOS = seasonForecastModelService.getMonthLoadFc(cityId, getCaliberId(cityId), date, season, algorithmId);
        baseResp.setData(monthFeatureDTOS);
        return baseResp;
    }

    @ApiOperation("获取季校核上报")
    @GetMapping(value = "/season/getMonthReportList")
    public BaseResp<List<TenDaysOfMonthFeatureDTO>> getMonthReportList(String cityId, String caliberId, Date date,
        String algorithmName, String season, String startStr, String endStr) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<TenDaysOfMonthFeatureDTO> monthReportDTOS = seasonForecastModelService.getMonthReportList(cityId,
            getCaliberId(cityId), date, algorithmName, season, startStr, endStr);
        baseResp.setData(monthReportDTOS);
        return baseResp;
    }

    @ApiOperation("上报")
    @PostMapping(value = "/season/report")
    public BaseResp<String> report(@RequestBody List<SeasonReportDTO> monthReportDTOS) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        seasonForecastModelService.report(monthReportDTOS);
        baseResp.setRetMsg("上报成功");
        return baseResp;
    }

    @ApiOperation("获取最新上报时间")
    @GetMapping(value = "/season/getReportTime")
    public BaseResp<String> getReportTime(String cityId,String caliberId,Date date,String season) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        String result =  seasonForecastModelService.getReportTime(cityId,getCaliberId(cityId),date,season);
        baseResp.setData(result);
        return baseResp;
    }


    @ApiOperation("获取历史准确率")
    @GetMapping(value = "/season/getHistoryAccuracy")
    public BaseResp<List<TenDaysOfMonthAccuracyDTO>> getHistoryAccuracy(String cityId, String caliberId, Date date,
        String
            year, String season) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<TenDaysOfMonthAccuracyDTO> historyAccuracy = seasonForecastModelService.getHistoryAccuracy(cityId,
            getCaliberId(cityId), date, year, season);
        baseResp.setData(historyAccuracy);
        return baseResp;
    }

    @ApiOperation("获取气象预测对比")
    @GetMapping(value = "getQuarterTemp")
    public BaseResp<List<TempCompareDTO>> getQuarterTemp(String cityId, String year, String season, Integer type,
        String startStr, String endStr) {
        BaseResp baseResp = BaseResp.succResp();
        List<TempCompareDTO> tempCompareDTOList = new ArrayList<>(6);
        try {
            tempCompareDTOList = seasonForecastModelService.getTempCompareDTOList(cityId, year, season, type, startStr, endStr);
            if (!CollectionUtils.isEmpty(tempCompareDTOList)) {
                List<TempCompareDTO> collect = tempCompareDTOList
                    .stream()
                    .sorted(Comparator.comparing(TempCompareDTO::getType))
                    .collect(Collectors.toList());
                baseResp.setData(collect);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (CollectionUtils.isEmpty(tempCompareDTOList)) {
            return new BaseResp("T706");
        }
        return baseResp;
    }

    @ApiOperation("获取历史准确率-季度页面")
    @GetMapping(value = "/quarter/getHisAccuracy")
    public BaseResp<List<FeaturesDTO>> getHisAccuracy(String cityId, String year, String season, String algorithmId) {
        BaseResp baseResp = BaseResp.succResp();
        List<FeaturesDTO> featureList = new ArrayList<>(6);
        try {
            featureList = seasonForecastModelService.getFeatureList(cityId, year, season, algorithmId);
            baseResp.setData(featureList);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (CollectionUtils.isEmpty(featureList)) {
            baseResp.setRetMsg("操作成功");
            return baseResp;
        }
        return baseResp;
    }

    @ApiOperation("获取算法")
    @GetMapping(value = "getAlgorithm")
    public BaseResp<List<AlgorithmDTO>> getAlgorithm() {
        BaseResp baseResp = BaseResp.succResp();
        List<AlgorithmDTO> algorithmDTOList = new ArrayList<>(6);
        try {
            algorithmDTOList = settingSystemService.getQuarterAlgorithmDTOList();
            baseResp.setData(algorithmDTOList);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (CollectionUtils.isEmpty(algorithmDTOList)) {
            return new BaseResp("T706");
        }
        return baseResp;
    }

    @ApiOperation("获取气象设置")
    @PostMapping(value = "/season/getWeatherSetting")
    public BaseResp<List<MonthWeatherResultDTO>> getWeatherSetting(
        @RequestBody MonthWeatherSettingDTO monthWeatherSettingDTO) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<MonthWeatherResultDTO> weatherSetting = seasonForecastModelService.getWeatherSetting(
            monthWeatherSettingDTO);
        if (CollectionUtils.isEmpty(weatherSetting)) {
            return new BaseResp("T200");
        }
        baseResp.setData(weatherSetting);
        return baseResp;
    }

    @ApiOperation("保存气象设置")
    @PostMapping(value = "/season/saveWeatherSetting")
    public BaseResp<Void> saveWeatherSetting(@RequestBody List<MonthWeatherResultDTO> monthWeatherResultDTOS)
        throws Exception {
        seasonForecastModelService.saveWeatherSetting(monthWeatherResultDTOS);
        return BaseResp.succResp();
    }

    @ApiOperation("获取气象信息")
    @GetMapping(value = "/season/getWeatherInfo")
    public BaseResp<List<MonthWeatherResultDTO>> getWeatherInfo(String startStr, String endStr, String cityId)
        throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<MonthWeatherResultDTO> weatherSetting = seasonForecastModelService.getWeatherInfo(startStr, endStr, cityId);
        if (CollectionUtils.isEmpty(weatherSetting)) {
            return new BaseResp("T200");
        }
        baseResp.setData(weatherSetting);
        return baseResp;
    }

    @ApiOperation("冬夏导入上报结果")
    @RequestMapping(value = "/season/import", method = RequestMethod.POST)
    public BaseResp importMonthReport(
        @RequestParam(value = "reportFile", required = false) MultipartFile forecastFile, String cityId)
        throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        Date nowDate = new Date();
        List<SeasonReportDTO> monthReportDTOS = new ArrayList<>();
        List<SeasonReportDTO> result = new ArrayList<>();
        String reportStatus = null;
        String filed = "summer_report_time";
        try {
            InputStream io = forecastFile.getInputStream();
            List<Map<Integer, Object>> list = ExcelUtil.resolve(io, 1, true);
            String substring = list.get(3).get(0).toString().substring(7, 9);
            if ("度冬".equals(substring)) {
                filed = "winter_report_time";
            }
            List<ReportSystemInitDO> month_report_time = reportSystemService.findReportSystemConfig(cityId,
                filed, null);
            if (Boolean.TRUE.equals(month_report_time.get(0).getStatus())) {
                String value = month_report_time.get(0).getValue();
                reportStatus = value;
            }
            if (list != null) {
                list.subList(0, 3).clear();
                for (Map<Integer, Object> integerObjectMap : list) {
                    SeasonReportDTO monthReportDTO = new SeasonReportDTO();
                    monthReportDTO.setDate(integerObjectMap.get(0).toString().substring(0, 7) + "-01");
                    monthReportDTO.setCityId(cityId);
                    monthReportDTO.setSeason("1");
                    if ("度冬".equals(integerObjectMap.get(0).toString().substring(7, 9))) {
                        monthReportDTO.setSeason("2");
                    }
                    monthReportDTO.setMaxLoad(new BigDecimal(integerObjectMap.get(1).toString()));
                    monthReportDTO.setMinLoad(new BigDecimal(integerObjectMap.get(2).toString()));
                    monthReportDTO.setNoonTimeLoad(new BigDecimal(integerObjectMap.get(3).toString()));
                    monthReportDTO.setEnergy(new BigDecimal(integerObjectMap.get(4).toString()));
                    monthReportDTO.setExtremeMaxLoad(new BigDecimal(integerObjectMap.get(5).toString()));
                    monthReportDTO.setExtremeMinLoad(new BigDecimal(integerObjectMap.get(6).toString()));
                    monthReportDTO.setExtremeNoonTimeLoad(new BigDecimal(integerObjectMap.get(7).toString()));
                    monthReportDTO.setExtremeEnergy(new BigDecimal(integerObjectMap.get(8).toString()));
                    monthReportDTOS.add(monthReportDTO);
                }
            }
        } catch (Exception e) {
            baseResp.setRetCode("T706");
            baseResp.setRetMsg("导入失败！");
            throw TsieExceptionUtils.newBusinessException("需要导入的数据有空值");
        }
        if (reportStatus != null) {
            Date date = DateUtil.getDate(reportStatus, "yyyy-MM-dd");
            //String monthByDate = DateUtil.getMonthByDate(new Date()).split("-")[1];
            // 可以上报
            for (SeasonReportDTO monthReportDTO : monthReportDTOS) {
                if (date.compareTo(nowDate) >= 0) {
                    // 可以导入
                    result.add(monthReportDTO);
                }
            }
            seasonForecastModelService.report(result);
            if (result.size() == monthReportDTOS.size()) {
                baseResp.setRetCode("T200");
                baseResp.setRetMsg("全部导入成功");
            } else if (result.size() == 0) {
                baseResp.setRetCode("T200");
                baseResp.setRetMsg("全部导入失败。");
            } else {
                baseResp.setRetCode("T200");
                baseResp.setRetMsg("部分导入失败，存在超过上报时间记录。");
            }
        } else {
            seasonForecastModelService.report(monthReportDTOS);
            baseResp.setRetCode("T200");
            baseResp.setRetMsg("全部导入成功");
        }
        return baseResp;
    }
}

