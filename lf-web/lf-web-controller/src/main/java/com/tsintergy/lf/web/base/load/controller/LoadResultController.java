/**
 * Copyright (C), 2015‐2020, 北京清能互联科技有限公司 Author:  wangfeng Date:  2020/11/27 4:56 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.load.controller;

import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.DayAssessDTO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadResultService;
import com.tsintergy.lf.serviceapi.base.load.dto.*;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadAssessDTO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadAssessRankDTO;
import com.tsintergy.lf.web.base.controller.CommonBaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Description:  <br>
 * 预测结果对比分析页面
 *
 * <AUTHOR>
 * @create 2020/11/27
 * @since 2.0.0
 */
@RestController
@RequestMapping(value = "/load/result")
@Api(tags = "负荷结果")
public class LoadResultController extends CommonBaseController {

    @Autowired
    private LoadResultService loadResultService;


    /**
     * 时刻准确率
     *
     * @param cityId
     * @param caliberId
     * @param date
     * @return
     * @throws Exception
     */
    @ApiOperation("时刻准确率")
    @GetMapping(value = "/point")
    public BaseResp<LoadAccuracyDTO> getPointAccuracy(String cityId, String caliberId, Date date) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        /*if (StringUtils.isEmpty(caliberId)) {
            caliberId = this.getCaliberId();
        }*/
        if (StringUtils.isEmpty(cityId)) {
            cityId = this.getLoginCityId();
        }
        LoadAccuracyDTO accuracyDTO = loadResultService.findAccuracy(cityId, getCaliberId(cityId), date);
        baseResp.setData(accuracyDTO);
        return baseResp;
    }

    @ApiOperation("多日准确率")
    @GetMapping(value = "/daysPoint")
    public BaseResp<LoadDaysAccuracyDTO> getDaysPointAccuracy(String cityId, Date startDate, Date endDate) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<LoadDaysAccuracyDTO> daysAccuracy = loadResultService.findDaysAccuracy(cityId, getCaliberId(cityId),
            startDate, endDate);
        baseResp.setData(daysAccuracy);
        return baseResp;
    }

    @ApiOperation("多月准确率")
    @GetMapping(value = "/monthPoint")
    public BaseResp<LoadDaysAccuracyDTO> getMonthPointAccuracy(String cityId, Date startDate, Date endDate) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<LoadDaysAccuracyDTO> daysAccuracy = loadResultService.findMonthAccuracy(cityId, getCaliberId(cityId),
            startDate, endDate);
        baseResp.setData(daysAccuracy);
        return baseResp;
    }

    @ApiOperation("多年准确率")
    @GetMapping(value = "/yearPoint")
    public BaseResp<LoadDaysAccuracyDTO> getYearPointAccuracy(String cityId, String startYear, String endYear) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<LoadDaysAccuracyDTO> daysAccuracy = loadResultService.findYearAccuracy(cityId, getCaliberId(cityId),
            startYear, endYear);
        baseResp.setData(daysAccuracy);
        return baseResp;
    }

    @ApiOperation("中长期多月准确率")
    @GetMapping(value = "/longMonthPoint")
    public BaseResp<LoadLongTermAccuracyDTO> getLongMonthPointAccuracy(String cityId, Date startDate, Date endDate) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<LoadLongTermAccuracyDTO> daysAccuracy = loadResultService.findLongMonthAccuracy(cityId, getCaliberId(cityId),
            startDate, endDate);
        baseResp.setData(daysAccuracy);
        return baseResp;
    }

    @ApiOperation("中长期冬夏准确率")
    @GetMapping(value = "/longSeasonPoint")
    public BaseResp<SeasonDTO> getSeasonPointAccuracy(String cityId, String year, String season) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        SeasonDTO daysAccuracy = loadResultService.findSeasonAccuracy(cityId, getCaliberId(cityId),
            year, season);
        baseResp.setData(daysAccuracy);
        return baseResp;
    }

    @ApiOperation("中长期年度准确率")
    @GetMapping(value = "/longYearPoint")
    public BaseResp<SeasonDTO> getLongYearPointAccuracy(String cityId, String year, String season) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        SeasonDTO daysAccuracy = loadResultService.findLongYearAccuracy(cityId, getCaliberId(cityId),
                year, season);
        baseResp.setData(daysAccuracy);
        return baseResp;
    }

    @ApiOperation("日地市考核评价")
    @GetMapping(value = "/dayAssess")
    public BaseResp<DayAssessDTO> dayAssess(Date startDate, Date endDate, String batchIds) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<DayAssessDTO> result = loadResultService.findDayAssess(startDate, endDate, Constants.CALIBER_ID_BG_DS, batchIds);
        baseResp.setData(result);
        return baseResp;
    }

    @ApiOperation("月度地市考核评价")
    @GetMapping(value = "/monthAssess")
    public BaseResp<LoadAssessDTO> getMonthAssess(Date startDate) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<LoadAssessDTO> result = loadResultService.findMonthAssess(startDate, Constants.CALIBER_ID_BG_DS);
        baseResp.setData(result);
        return baseResp;
    }

    @ApiOperation("月度排名环比")
    @GetMapping(value = "/monthAssessRank")
    public BaseResp<LoadAssessRankDTO> getMonthAssessRank(Date startDate, Date endDate) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        Date date = new Date();
        List<LoadAssessRankDTO> loadAssessRankDTOS = new ArrayList<>();
        if (Integer.parseInt(DateUtil.getDateToStr(endDate).split("-")[1]) > Integer.parseInt(
            DateUtil.getDateToStr(date).split("-")[1])) {
            baseResp.setData(loadAssessRankDTOS);
            return baseResp;
        }
        // 计算本月的排名
        List<LoadAssessDTO> resultMonth = loadResultService.findMonthAssess(endDate, Constants.CALIBER_ID_BG_DS);
        // 计算上一个月的排名
        List<LoadAssessDTO> resultLastMonth = loadResultService.findMonthAssess(startDate, Constants.CALIBER_ID_BG_DS);
        if (!CollectionUtils.isEmpty(resultMonth) && !CollectionUtils.isEmpty(resultLastMonth)) {
            for (int i = 0; i < resultMonth.size(); i++) {
                for (int i1 = 0; i1 < resultLastMonth.size(); i1++) {
                    if (resultMonth.get(i).getCityName().equals(resultLastMonth.get(i1).getCityName())) {
                        LoadAssessRankDTO loadAssessRankDTO = new LoadAssessRankDTO();
                        int differ = i1 - i;
                        loadAssessRankDTO.setScore(differ);
                        loadAssessRankDTO.setCityName(resultMonth.get(i).getCityName());
                        loadAssessRankDTOS.add(loadAssessRankDTO);
                    }
                }
            }
        }
        baseResp.setData(loadAssessRankDTOS);
        return baseResp;
    }

    @ApiOperation("年度地市考核评价")
    @GetMapping(value = "/yearAssess")
    public BaseResp<LoadAssessDTO> getYearAssess(Date startDate, Date endDate) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        Date date = new Date();
        if (Integer.parseInt(DateUtil.getDateToStr(startDate).split("-")[0]) < Integer.parseInt(
            DateUtil.getDateToStr(date).split("-")[0])) {
            endDate = DateUtil.getDate(Integer.valueOf(DateUtil.getDateToStr(startDate).split("-")[0]) + "-12",
                "yyyy-MM");
        } else if (Integer.parseInt(DateUtil.getDateToStr(startDate).split("-")[0]) > Integer.parseInt(
            DateUtil.getDateToStr(date).split("-")[0])) {
            endDate = DateUtil.getDate(Integer.valueOf(DateUtil.getDateToStr(startDate).split("-")[0]) + "-12",
                "yyyy-MM");
        }
        List<LoadAssessDTO> daysAccuracy = loadResultService.findYearAssess(startDate, endDate, Constants.CALIBER_ID_BG_DS);
        baseResp.setData(daysAccuracy);
        return baseResp;
    }

    @ApiOperation("年度排名环比")
    @GetMapping(value = "/yearAssessRank")
    public BaseResp<LoadAssessRankDTO> getYearAssessRank(Date startDate, Date endDate) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<LoadAssessRankDTO> loadAssessRankDTOS = new ArrayList<>();
        Date date = new Date();
        if (Integer.parseInt(DateUtil.getDateToStr(startDate).split("-")[0]) < Integer.parseInt(
            DateUtil.getDateToStr(date).split("-")[0])) {
            endDate = DateUtil.getDate(Integer.valueOf(DateUtil.getDateToStr(startDate).split("-")[0]) + "-12",
                "yyyy-MM");
        } else if (Integer.parseInt(DateUtil.getDateToStr(startDate).split("-")[0]) > Integer.parseInt(
            DateUtil.getDateToStr(date).split("-")[0])) {
            baseResp.setData(loadAssessRankDTOS);
            return baseResp;
        }
        int lastYear = Integer.parseInt(DateUtil.getDateToStr(startDate).split("-")[0]) - 1;
        String lastMonth = DateUtil.getDateToStr(endDate).split("-")[1];
        String lastStart = lastYear + "-01";
        String lastEnd = lastYear + "-" + lastMonth;
        Date lastStartDate = DateUtil.getDate(lastStart, "yyyy-MM");
        Date lastEndDate = DateUtil.getDate(lastEnd, "yyyy-MM");
        // 计算本月的排名
        List<LoadAssessDTO> resultMonth = loadResultService.findYearAssess(startDate, endDate, Constants.CALIBER_ID_BG_DS);
        // 计算上一年的排名
        List<LoadAssessDTO> resultLastMonth = loadResultService.findYearAssess(lastStartDate, lastEndDate, Constants.CALIBER_ID_BG_DS);
        if (!CollectionUtils.isEmpty(resultMonth) && !CollectionUtils.isEmpty(resultLastMonth)) {
            for (int i = 0; i < resultMonth.size(); i++) {
                for (int i1 = 0; i1 < resultLastMonth.size(); i1++) {
                    if (resultMonth.get(i).getCityName().equals(resultLastMonth.get(i1).getCityName())) {
                        LoadAssessRankDTO loadAssessRankDTO = new LoadAssessRankDTO();
                        int differ = i1 - i;
                        loadAssessRankDTO.setScore(differ);
                        loadAssessRankDTO.setCityName(resultMonth.get(i).getCityName());
                        loadAssessRankDTOS.add(loadAssessRankDTO);
                    }
                }
            }
        }
        baseResp.setData(loadAssessRankDTOS);
        return baseResp;
    }

    @ApiOperation("地市上报状态")
    @GetMapping(value = "/cityReportState")
    public BaseResp<CityReportStateDTO> getCityReportState(Date startDate) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<CityReportStateDTO> result = loadResultService.findCityReportState(startDate, Constants.CALIBER_ID_BG_DS);
        baseResp.setData(result);
        return baseResp;
    }

    @ApiOperation("短期上报时间设置")
    @RequestMapping(value = "/doShortTimeConfig", method = RequestMethod.POST)
    public BaseResp doShortTermConfig(@RequestBody ReportConfigRequest request) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        loadResultService.doCityReportTimeConfig(request.getStartDate(), request.getEndDate(),
            request.getFirstCityIds(), request.getLastCityIds(), request.getType1(), request.getType2());
        return baseResp;
    }

    @ApiOperation("短期上报时间获取")
    @RequestMapping(value = "/getShortTimeConfig", method = RequestMethod.POST)
    public BaseResp<CityReportResultDTO> getShortTermConfig(@RequestBody ReportConfigRequest request) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        CityReportResultDTO cityReportTimeConfig = loadResultService.getCityReportTimeConfig(request.getFirstCityIds(),
            request.getLastCityIds());
        baseResp.setData(cityReportTimeConfig);
        return baseResp;
    }

    @ApiOperation("中长期上报时间设置")
    @RequestMapping(value = "/doLongTimeConfig", method = RequestMethod.POST)
    public BaseResp doLongTimeConfig(@RequestBody LongReportConfigRequest request) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        loadResultService.doCityLongReportTimeConfig(request.getMonthDate(), request.getSummerDate(),
            request.getWinterDate(), request.getYearDate(), request.getMonthCityIds(), request.getSummerCityIds(),
            request.getWinterCityIds(), request.getYearCityIds(), request.getMonthType(), request.getSummerType(),
            request.getWinterType(), request.getYearType());
        return baseResp;
    }

    @ApiOperation("中长期上报时间获取")
    @RequestMapping(value = "/getLongTimeConfig", method = RequestMethod.POST)
    public BaseResp<LongCityReportResultDTO> getLongTermConfig(@RequestBody LongReportConfigRequest request) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        LongCityReportResultDTO longCityReportTimeConfig = loadResultService.getLongCityReportTimeConfig(
            request.getMonthCityIds(), request.getSummerCityIds(), request.getWinterCityIds(),
            request.getYearCityIds());
        baseResp.setData(longCityReportTimeConfig);
        return baseResp;
    }

    /*@ApiOperation("短期上报状态获取")
    @GetMapping(value = "/shortReportState")
    public BaseResp<CityReportStateDTO> getShortReportState(Date startDate) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<CityReportStateDTO> result = loadResultService.findCityReportState(startDate, Constants.CALIBER_ID_BG_DS);
        baseResp.setData(result);
        return baseResp;
    }

    @ApiOperation("中长期上报状态获取")
    @GetMapping(value = "/longReportState")
    public BaseResp<CityReportStateDTO> getLongReportState(Date startDate) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<CityReportStateDTO> result = loadResultService.findCityReportState(startDate, Constants.CALIBER_ID_BG_DS);
        baseResp.setData(result);
        return baseResp;
    }*/

        private String getCaliberId(String cityId){
        if (Constants.PROVINCE_TYPE == Integer.parseInt(cityId)){
            return Constants.CALIBER_ID_BG_QW;
        }else{
            return Constants.CALIBER_ID_BG_DS;
        }
    }

}
