/**
 *   
 *  Copyright (C), 2015‐2020, 北京清能互联科技有限公司  
 *  Author:  EDZ  
 *  Date:  2020/10/28 14:46  
 *  History:  
 *  <author> <time> <version> <desc> 
 */
package com.tsintergy.lf.web.base.report.request;

import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;

/**  
 * Description: 上报准确率 <br> 
 * 
 * <AUTHOR>  
 * @create 2020/10/28  
 * @since 1.0.0  
 */
public class ReportAccuracyRequest {

    @ApiModelProperty(value = "城市ID")
    private String cityId;

    @ApiModelProperty(value = "口径ID")
    private String caliberId;

    @ApiModelProperty(value = "开始日期")
    private Date startDate;

    @ApiModelProperty(value = "结束日期")
    private Date endDate;

    @ApiModelProperty(value = "用户列表")
    private List<String> userIds;

    public String getCityId() {
        return cityId;
    }

    public void setCityId(String cityId) {
        this.cityId = cityId;
    }

    public String getCaliberId() {
        return caliberId;
    }

    public void setCaliberId(String caliberId) {
        this.caliberId = caliberId;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public List<String> getUserIds() {
        return userIds;
    }

    public void setUserIds(List<String> userIds) {
        this.userIds = userIds;
    }
}