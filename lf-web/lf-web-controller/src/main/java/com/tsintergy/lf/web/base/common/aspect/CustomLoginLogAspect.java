/**
 * Copyright(C),2015‐2021,北京清能互联科技有限公司
 */
package com.tsintergy.lf.web.base.common.aspect;

import com.tsieframework.cloud.security.serviceapi.businesslog.pojo.TsieLoginFailCountRecordVO;
import com.tsieframework.cloud.security.serviceapi.businesslog.pojo.TsieOperateLogVO;
import com.tsieframework.cloud.security.web.businesslog.cuts.LoginLogAspect;
import com.tsieframework.cloud.security.web.common.security.UserTokenHelper;
import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.web.BaseResp;
import com.tsieframework.core.base.web.BaseRespBuilder;
import java.text.SimpleDateFormat;
import java.util.Date;
import javax.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 *Description:  重写日志记录，满足安全测评要求<br>
 *
 *@Author: <EMAIL>
 *@Date: 2021/12/28 18:24
 *@Version: 1.0.0
 */
@Slf4j
@Aspect
public class CustomLoginLogAspect extends LoginLogAspect {

    @Override
    @Around("controllerAspect()")
    public Object doAround(ProceedingJoinPoint pjp) throws Throwable {
        TsieOperateLogVO operateLog = new TsieOperateLogVO();

        BaseResp var5;
        try {
            HttpServletRequest request = ((ServletRequestAttributes)RequestContextHolder.getRequestAttributes()).getRequest();
            String userName = this.getUserName(pjp);
            if (!StringUtils.isEmpty(userName)) {
                Date beginTime = new Date();
                log.debug("用户名: {} 登录时间: {}  URI: {}", new Object[]{userName, (new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS")).format(beginTime), request.getRequestURI()});

                operateLog.setUserName(userName);
                String title = "";

                try {
                    title = this.getOperate(pjp);
                } catch (Exception var13) {
                    log.debug("无法获取@LoginLog注解配置", var13);
                }

                operateLog.setId(this.getUuid());
                operateLog.setTitle(title);
                operateLog.setType("info");
                operateLog.setRemoteAddr(UserTokenHelper.getIPAddress());
                operateLog.setRequestUri(request.getRequestURI());
                operateLog.setException("无异常");
                operateLog.setCreateDate(beginTime);

                boolean accountLocked = this.businessLogService.queryUserIfCanLogin(userName);
                if (accountLocked) {
                    throw TsieExceptionUtils.newBusinessException("账号已锁定", new String[0]);
                }

                Object var8 = pjp.proceed();
                return var8;
            }

            var5 = BaseRespBuilder.fail("用户名为空").build();
        } catch (Throwable var14) {
            operateLog.setType("error");
            operateLog.setException(this.getExecptionContent(var14));
            throw var14;
        } finally {
            this.saveOperate(operateLog);
            if ("error".equals(operateLog.getType())) {
                this.markLoginFailMessage(operateLog);
            }

        }

        return var5;
    }

    private void markLoginFailMessage(TsieOperateLogVO logVO) {
        try {
            TsieLoginFailCountRecordVO vo = new TsieLoginFailCountRecordVO();
            vo.setUserName(logVO.getUserName());
            vo.setIp(logVO.getRemoteAddr());
            this.businessLogService.doCreateLoginFailRecord(vo);
        } catch (Exception var3) {
            log.error("记录用户登录错误次数日志异常，对应用户为：" + logVO.getUserName(), var3);
        }
    }



}
