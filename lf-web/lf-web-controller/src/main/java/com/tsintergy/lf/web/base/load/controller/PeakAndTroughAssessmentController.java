package com.tsintergy.lf.web.base.load.controller;

import com.tsieframework.core.base.web.BaseResp;
import com.tsieframework.util.compatible.StringUtils;
import com.tsintergy.lf.serviceapi.base.load.api.PeakAndTroughAssessmentService;
import com.tsintergy.lf.serviceapi.base.load.dto.AccuracyDistributionDTO;
import com.tsintergy.lf.serviceapi.base.load.dto.AccuracyEstimationsDTO;
import com.tsintergy.lf.web.base.load.request.AccuracyDistributionRequest;
import com.tsintergy.lf.web.base.load.request.AccuracyEstimationRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/load/assessment")
@Api("峰谷评估控制器")
public class PeakAndTroughAssessmentController {


    @Autowired
    PeakAndTroughAssessmentService peakAndTroughAssessmentService;


    /**
     * 准确率统计
     * @param accuracyEstimationRequest
     * @return
     */
    @ApiOperation("准确率统计")
    @RequestMapping(value = "/accuracyEstimation" ,method = RequestMethod.POST)
    public BaseResp<AccuracyEstimationsDTO> accuracyEstimation(@RequestBody AccuracyEstimationRequest accuracyEstimationRequest) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        if (StringUtils.isAnyBlank(accuracyEstimationRequest.getCaliberId(),accuracyEstimationRequest.getCityId(),accuracyEstimationRequest.getDateType(),accuracyEstimationRequest.getStartDate())){
            return BaseResp.failResp();
        }
        baseResp.setData(peakAndTroughAssessmentService.getAccuracyStatistics(accuracyEstimationRequest.getCityId(),accuracyEstimationRequest.getCaliberId(),accuracyEstimationRequest.getStartDate(),accuracyEstimationRequest.getEndDate(),accuracyEstimationRequest.getDateType()));
        if (baseResp.getData()==null){
            baseResp.setRetCode("T706");
        }
        return baseResp;
    }


    /**
     * 准确率分布
     * @return
     */
    @ApiOperation("准确率分布")
    @RequestMapping(value = "/accuracyDistribution",method = RequestMethod.POST)
    public BaseResp<List<AccuracyDistributionDTO>> accuracyDistribution(@RequestBody AccuracyDistributionRequest accuracyDistributionRequest) throws Exception {
        if (StringUtils.isAnyBlank(accuracyDistributionRequest.getAccuracyType(),accuracyDistributionRequest.getCityId(),accuracyDistributionRequest.getDateType(),accuracyDistributionRequest.getStartDate())){
            return BaseResp.failResp();
        }
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(peakAndTroughAssessmentService.getAccuracyDistribution(accuracyDistributionRequest.getAccuracyType(),accuracyDistributionRequest.getCaliberId(),accuracyDistributionRequest.getCityId(),accuracyDistributionRequest.getDateType(),accuracyDistributionRequest.getStartDate(),accuracyDistributionRequest.getEndDate()));
        if (baseResp.getData()==null){
            baseResp.setRetCode("T706");
        }
        return baseResp;
    }
}