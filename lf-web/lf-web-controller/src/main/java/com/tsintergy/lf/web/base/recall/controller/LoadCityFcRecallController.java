package com.tsintergy.lf.web.base.recall.controller;

import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.serviceapi.base.recall.api.AccuracyLoadCityFcRecallService;
import com.tsintergy.lf.serviceapi.base.recall.api.LoadCityFcRecallService;
import com.tsintergy.lf.serviceapi.base.recall.api.StatisticsCityDayFcRecallService;
import com.tsintergy.lf.serviceapi.base.recall.dto.AccuracyLoadFcRecallDTO;
import com.tsintergy.lf.serviceapi.base.recall.dto.LoadFcQueryDTO;
import com.tsintergy.lf.serviceapi.base.recall.dto.StatisticsAccuracyDTO;
import com.tsintergy.lf.web.base.controller.CommonBaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Api(tags = "预测回溯")
@RequestMapping("/recall")
@RestController
public class LoadCityFcRecallController extends CommonBaseController {

    @Autowired
    LoadCityFcRecallService loadCityFcRecallService;

    @Autowired
    AccuracyLoadCityFcRecallService accuracyLoadCityFcRecallService;

    @Autowired
    StatisticsCityDayFcRecallService statisticsCityDayFcRecallService;


    /**
     * 负荷预测对比
     *
     * @param cityId
     * @param algorithmId
     * @return
     * @throws Exception
     */
    @ApiOperation("负荷预测对比")
    @GetMapping(value = "/loadFcCompare")
    public BaseResp<LoadFcQueryDTO> findLoad(String cityId, Date date, String algorithmId) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        String caliberId = this.getCaliberId();
        LoadFcQueryDTO fcQueryDTO = loadCityFcRecallService.getLoadFcQueryDTO(cityId, caliberId, date, algorithmId);
        if (fcQueryDTO == null) {
            return new BaseResp("T706");
        }
        baseResp.setData(fcQueryDTO);
        return baseResp;
    }


    @ApiOperation("日准确率")
    @RequestMapping(value = "/dayAccuracy", method = RequestMethod.GET)
    public BaseResp<List<AccuracyLoadFcRecallDTO>> statisticsFeatureCity(String cityId, Date startDate, Date endDate, String algorithmId) throws Exception {
        BaseResp baseResp = null;
        try {
            String caliberId = getCaliberId();
            List<AccuracyLoadFcRecallDTO> AccuracyLoadFcRecallDTOS = accuracyLoadCityFcRecallService
                    .getDTOS(cityId, caliberId, startDate, endDate, algorithmId);
            baseResp = BaseResp.succResp();
            baseResp.setData(AccuracyLoadFcRecallDTOS);
        } catch (Exception e) {
            e.printStackTrace();
            baseResp = BaseResp.failResp("查询日准确率失败");
        }
        return baseResp;
    }


    @ApiOperation("准确率统计")
    @RequestMapping(value = "/statistics/accuracy", method = RequestMethod.GET)
    public BaseResp<StatisticsAccuracyDTO> statisticsAccuracy(String cityId, Date startDate, Date endDate, String algorithmId) throws Exception {
        BaseResp baseResp = null;
        try {
            String caliberId = getCaliberId();
            StatisticsAccuracyDTO AccuracyLoadFcRecallDTOS = statisticsCityDayFcRecallService
                    .getStatisticsAccuracyDTO(cityId, caliberId, startDate, endDate, algorithmId);
            baseResp = BaseResp.succResp();
            baseResp.setData(AccuracyLoadFcRecallDTOS);
        } catch (Exception e) {
            e.printStackTrace();
            baseResp = BaseResp.failResp("准确率统计失败");
        }
        return baseResp;
    }
}
