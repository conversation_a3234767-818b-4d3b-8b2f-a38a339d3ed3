package com.tsintergy.lf.web.base.util.ultra;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.enums.IfEnum;
import com.tsintergy.lf.core.util.PeriodDataUtil;
import com.tsintergy.lf.core.util.VslfDateUtil;
import com.tsintergy.lf.serviceapi.base.ultra.load.pojo.LoadCityFcUltraDO;
import com.tsintergy.lf.serviceapi.base.ultra.load.api.LoadCityFcUltraService;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

/**
 * 数据导入监听类
 *
 * <AUTHOR>
 */
public class ExcelMoreDayForecast96Listener extends AnalysisEventListener<ExcelMoreDayForecastData96> {

    private List<ExcelMoreDayForecastData96> list = new ArrayList();

    private LoadCityFcUltraService loadCityFcUltraService;

    private String cityId;

    private String userId;

    private String caliberId;



    public ExcelMoreDayForecast96Listener(LoadCityFcUltraService loadCityFcService, String cityId, String userId, String caliberId) {
        this.loadCityFcUltraService = loadCityFcService;
        this.cityId = cityId;
        this.userId = userId;
        this.caliberId = caliberId;
    }

    /**
     * 逐行解析数据
     */
    @Override
    public void invoke(ExcelMoreDayForecastData96 data, AnalysisContext context) {
        list.add(data);
    }

    /**
     * 逐行的返回头信息
     */
    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {

    }


    /**
     * 所有数据解析完成后的回调函数
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        list.forEach(e->{
            LoadCityFcUltraDO fcVO = new LoadCityFcUltraDO();
            BeanUtils.copyProperties(e, fcVO);
            fcVO.setCityId(cityId);
            fcVO.setCaliberId(caliberId);
            Date date = VslfDateUtil.getDate(e.getDate(), "yyyy-MM-dd");
            fcVO.setDateTime(new java.sql.Date(date.getTime()));
            fcVO.setAlgorithmId(Constants.MD_ALGORITHM_ID);
            fcVO.setReport(IfEnum.YES);
            fcVO.setSucceed(true);
            fcVO.setRecommend(false);
            fcVO.setMultipointType(1);
            List<BigDecimal> bigDecimals = BasePeriodUtils.toList(e, Constants.LOAD_CURVE_POINT_NUM,
                true);
            fcVO.setTvMeta(DataPointMetaBuilder.createPointMeta(date, 15));
            fcVO.setTvData(bigDecimals);
            fcVO.setCreateTime(new Timestamp(System.currentTimeMillis()));
            fcVO.setReportTime(new Timestamp(System.currentTimeMillis()));
            try {
                loadCityFcUltraService.report(fcVO);
            } catch (Exception exception) {
                exception.printStackTrace();
            }


            LoadCityFcUltraDO fcVO288 = new LoadCityFcUltraDO();
            BeanUtils.copyProperties(e, fcVO288);
            fcVO288.setCityId(cityId);
            fcVO288.setCaliberId(caliberId);
            Date date1 = VslfDateUtil.getDate(e.getDate(), "yyyy-MM-dd");
            fcVO288.setDateTime(new java.sql.Date(date1.getTime()));
            fcVO288.setAlgorithmId(Constants.MD_ALGORITHM_ID);
            fcVO288.setReport(IfEnum.YES);
            fcVO288.setSucceed(true);
            fcVO288.setMultipointType(1);
            fcVO288.setRecommend(false);
            List<BigDecimal> bigDecimals288 = BasePeriodUtils.toList(e, Constants.NUM_288,
                true);
            List<BigDecimal> bigDecimals1 = PeriodDataUtil.data96to288(bigDecimals288, true);
            fcVO288.setTvMeta(DataPointMetaBuilder.createPointMeta(date, 5));
            fcVO288.setTvData(bigDecimals1);
            fcVO288.setCreateTime(new Timestamp(System.currentTimeMillis()));
            fcVO288.setReportTime(new Timestamp(System.currentTimeMillis()));
            try {
                loadCityFcUltraService.report(fcVO288);
            } catch (Exception exception) {
                exception.printStackTrace();
            }

        });

    }

}
