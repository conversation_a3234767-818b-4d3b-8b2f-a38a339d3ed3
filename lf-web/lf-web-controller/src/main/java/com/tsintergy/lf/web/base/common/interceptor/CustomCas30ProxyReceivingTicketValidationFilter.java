/**
 * Copyright (C), 2015‐2020, 北京清能互联科技有限公司
 * Author:  wangchen
 * Date:  2020/7/16 16:09
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.common.interceptor;

import com.tsintergy.lf.web.base.security.controller.SsoLoadForecastAccountManager;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jasig.cas.client.validation.Assertion;
import org.jasig.cas.client.validation.Cas30ProxyReceivingTicketValidationFilter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2020/7/16 
 * @since 1.0.0
 */
@Component
public class CustomCas30ProxyReceivingTicketValidationFilter extends Cas30ProxyReceivingTicketValidationFilter {

    private static final Logger logger = LoggerFactory.getLogger(CustomCas30ProxyReceivingTicketValidationFilter.class);

    @Autowired
    SsoLoadForecastAccountManager accountManager;

    @Override
    protected void onSuccessfulValidation(HttpServletRequest request, HttpServletResponse response, Assertion assertion) {
        try {
            accountManager.login(request,response,assertion.getPrincipal().getName());
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
        }
        super.onSuccessfulValidation(request, response, assertion);
    }

    public SsoLoadForecastAccountManager getAccountManager() {
        return accountManager;
    }

    public void setAccountManager(SsoLoadForecastAccountManager accountManager) {
        this.accountManager = accountManager;
    }
}
