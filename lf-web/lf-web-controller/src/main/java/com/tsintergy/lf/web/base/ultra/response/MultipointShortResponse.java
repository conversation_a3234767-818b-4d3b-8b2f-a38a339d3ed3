/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司 Author:  wangfeng Date:  2019/12/6 2:05 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.ultra.response;

import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * Description:  <br> 超短期多点多批次预测曲线对象
 *
 * <AUTHOR>
 * @create 2019/12/6
 * @since 1.0.0
 */
@Data
public class MultipointShortResponse implements Serializable {

    @ApiModelProperty("超短期数据")
    @BigdecimalJsonFormat(scale = 0)
    private List<BigDecimal> real;

    @ApiModelProperty("超短期数据")
    @BigdecimalJsonFormat(scale = 0)
    private List<MultipointShortData> ultraDataList;

    @ApiModelProperty("时间轴")
    private List<String> timeList;


}