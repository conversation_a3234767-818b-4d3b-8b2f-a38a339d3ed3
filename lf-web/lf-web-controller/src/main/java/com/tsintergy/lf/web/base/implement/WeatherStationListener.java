/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.web.base.implement;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.read.listener.ReadListener;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.core.util.MultipleThreadInvoker;
import com.tsintergy.lf.core.util.PeriodDataUtil;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherEnum;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherStationHisBasicService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherStationHisBasicDO;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;


/**
 * Description: 站点气象导入监听 <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/6/21 16:35
 * @Version: 1.0.0
 */
@Slf4j
public class WeatherStationListener implements ReadListener<WeatherStationImportDO> {

    private Map<String, WeatherStationHisBasicDO> weatherStationHisBasicDOMap = new HashMap<>();

    private final List<WeatherEnum> weatherEnums = new ArrayList<WeatherEnum>() {
        {
            add(WeatherEnum.HUMIDITY);
            add(WeatherEnum.TEMPERATURE);
            add(WeatherEnum.RAINFALL);
            add(WeatherEnum.WINDSPEED);
        }
    };

    private WeatherStationHisBasicService weatherStationHisBasicService;

    private  Map<String, String> cityMap;

    public WeatherStationListener(
        WeatherStationHisBasicService weatherStationHisBasicService,
        Map<String, String> cityMap) {
        this.weatherStationHisBasicService = weatherStationHisBasicService;
        this.cityMap = cityMap;
    }

    @Override
    public void onException(Exception e, AnalysisContext analysisContext) throws Exception {

    }

    @Override
    public void invokeHead(Map<Integer, CellData> map, AnalysisContext analysisContext) {

    }


    @Override
    public void invoke(WeatherStationImportDO weatherStationImportDO, AnalysisContext analysisContext) {
        String stationId = weatherStationImportDO.getStationId();
        String date = DateUtils
            .date2String(DateUtils.string2Date(weatherStationImportDO.getDateTime(), DateFormatType.DATE_FORMAT_STR),
                DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);

        weatherEnums.forEach(weatherEnum -> {
            try {
                String key = stationId + "-" + date + "-" + weatherEnum.value();
                WeatherStationHisBasicDO weatherStationHisBasicDO = weatherStationHisBasicDOMap.get(key);
                if (weatherStationHisBasicDO == null) {
                    weatherStationHisBasicDO = new WeatherStationHisBasicDO();
                    weatherStationHisBasicDO.setDate(
                        new java.sql.Date(DateUtils.string2Date(date, DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR).getTime()));
                    weatherStationHisBasicDO.setStationId(weatherStationImportDO.getStationId());
                    weatherStationHisBasicDO.setType(weatherEnum.value());
                    weatherStationHisBasicDO.setCityId(cityMap.get(weatherStationImportDO.getStationId()));
                    weatherStationHisBasicDOMap.put(key, weatherStationHisBasicDO);
                }

                String point = weatherStationImportDO.getDateTime().split(" ")[1];
                String fieldName = "t" + point.split(":")[0] + point.split(":")[1];
                Field declaredField = WeatherStationHisBasicDO.class.getSuperclass().getSuperclass()
                    .getDeclaredField(fieldName);
                declaredField.setAccessible(true);
                switch (weatherEnum) {
                    case RAINFALL:
                        declaredField.set(weatherStationHisBasicDO, weatherStationImportDO.getRainfall());
                        break;
                    case HUMIDITY:
                        declaredField.set(weatherStationHisBasicDO, weatherStationImportDO.getHumidity());
                        break;
                    case TEMPERATURE:
                        declaredField.set(weatherStationHisBasicDO, weatherStationImportDO.getTemperature());
                        break;
                    case WINDSPEED:
                        declaredField.set(weatherStationHisBasicDO, weatherStationImportDO.getWindspeed());
                        break;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }

        });
    }


    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        log.info("完成excel解析....,时间：{}", DateUtils.date2String(new java.util.Date(), DateFormatType.DATE_FORMAT_STR));


        log.info("开始补全2400点....,时间：{}", DateUtils.date2String(new Date(), DateFormatType.DATE_FORMAT_STR));
        for (Entry<String, WeatherStationHisBasicDO> entry : weatherStationHisBasicDOMap.entrySet()) {
            String key = entry.getKey();
            WeatherStationHisBasicDO value = entry.getValue();
            String[] split = key.split("-");
            String stationId = split[0];
            String dateStr =  split[1];
            String type = split[2];
            Date date = DateUtils.string2Date(dateStr,DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);
            Date tomorrow = DateUtils.addDays(date, 1);
            String dateToStr = DateUtils.date2String(tomorrow,DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR);

            WeatherStationHisBasicDO weatherStationHisBasicDO = weatherStationHisBasicDOMap
                .get(stationId + "-" + dateToStr + "-" + type);
            if (weatherStationHisBasicDO != null) {
                value.setT2400(weatherStationHisBasicDO.getT0000());
            } else {
                List<WeatherStationHisBasicDO> weatherStationHisBasicDO1 = weatherStationHisBasicService
                    .getWeatherStationHisBasicDO(value.getCityId(), stationId, tomorrow, tomorrow,
                        Integer.valueOf(type));
                if (!CollectionUtils.isEmpty(weatherStationHisBasicDO1)) {
                    value.setT2400(weatherStationHisBasicDO1.get(0).getT0000());
                }
            }
        }
        log.info("完成2400点补全....,时间：{}", DateUtils.date2String(new Date(), DateFormatType.DATE_FORMAT_STR));


        List<WeatherStationHisBasicDO> values = weatherStationHisBasicDOMap.values().stream()
            .collect(Collectors.toList());
        List<WeatherStationHisBasicDO> updateD0List = new ArrayList<>();
        int size = 100;
        int loop = 1;
        for (int i = 0; i < values.size(); i++) {
            try {
                PeriodDataUtil.do24To96VO(values.get(i));
                updateD0List.add(values.get(i));
                if (i >= loop * size - 1) {
                    log.info("第{}次批量入库开始....,时间：{}",(i+1)/100, DateUtils.date2String(new java.util.Date(), DateFormatType.DATE_FORMAT_STR));
                    weatherStationHisBasicService.doSaveOrUpdateBatch(updateD0List);
                    log.info("第{}次批量入库完成....,时间：{}",(i+1)/100, DateUtils.date2String(new java.util.Date(), DateFormatType.DATE_FORMAT_STR));
                    updateD0List.clear();
                    loop = loop + 1;
                }
            } catch (Exception e) {
                log.error("批量保存历史气象异常",e);
            }
        }
        if (CollectionUtils.isNotEmpty(updateD0List)) {
            weatherStationHisBasicService.doSaveOrUpdateBatch(updateD0List);
        }
        log.info("批量入库完成....,时间：{}", DateUtils.date2String(new java.util.Date(), DateFormatType.DATE_FORMAT_STR));
    }

    @Override
    public boolean hasNext(AnalysisContext analysisContext) {
        return true;
    }
}