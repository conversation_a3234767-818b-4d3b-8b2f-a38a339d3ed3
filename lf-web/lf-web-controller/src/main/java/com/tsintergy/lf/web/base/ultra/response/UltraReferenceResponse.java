/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司 Author:  ThinkPad Date:  2019/4/15 11:33 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.ultra.response;


import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import com.tsintergy.lf.serviceapi.base.ultra.dto.WeatherNameDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2019/4/15
 * @since 1.0.0
 */
@ApiModel
public class UltraReferenceResponse implements Serializable {

    @ApiModelProperty(value = "参照名称",example = "参照1")
    private String referenceName;

    @ApiModelProperty(value = "日期",example = "2021-03-12")
    private Date date;


    /**
     * 参照曲线负荷
     */
    @BigdecimalJsonFormat(scale = 0)
    @ApiModelProperty(value = "参照曲线负荷")
    private List<BigDecimal> load;

    /**
     * 气象曲线
     */
    @ApiModelProperty(value = "气象曲线")
    private List<WeatherNameDTO> weatherLoad;

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public String getReferenceName() {
        return referenceName;
    }

    public void setReferenceName(String referenceName) {
        this.referenceName = referenceName;
    }

    public List<BigDecimal> getLoad() {
        return load;
    }

    public void setLoad(List<BigDecimal> load) {
        this.load = load;
    }

    public List<WeatherNameDTO> getWeatherLoad() {
        return weatherLoad;
    }

    public void setWeatherLoad(List<WeatherNameDTO> weatherLoad) {
        this.weatherLoad = weatherLoad;
    }
}