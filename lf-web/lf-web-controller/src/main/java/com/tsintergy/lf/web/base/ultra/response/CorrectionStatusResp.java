/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司
 * Author:  ThinkPad
 * Date:  2019/4/19 1:18
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.ultra.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;

/**
 * Description: 上报状态结果对象 <br>
 *
 * <AUTHOR>
 * @create 2023/5/23
 * @since 1.0.0
 */
@ApiModel
public class CorrectionStatusResp implements Serializable {


    /**
     * 上报截止时间
     */
    @ApiModelProperty(value = "上报截止时间",example = "2021-03-17")
    private String reportTime;

    /**
     * 上报状态(1 上报   2 未上报   3 延时上报)
     */
    @ApiModelProperty(value = "上报状态",example = "2")
    private Integer reportStatus;


    /**
     * 上报时间
     */
    @ApiModelProperty(value = "上报时间",example = "2021-03-17")
    private String todayReportTime;

    public String getReportTime() {
        return reportTime;
    }

    public void setReportTime(String reportTime) {
        this.reportTime = reportTime;
    }

    public Integer getReportStatus() {
        return reportStatus;
    }

    public void setReportStatus(Integer reportStatus) {
        this.reportStatus = reportStatus;
    }

    public String getTodayReportTime() {
        return todayReportTime;
    }

    public void setTodayReportTime(String todayReportTime) {
        this.todayReportTime = todayReportTime;
    }
}