/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.web.base.system.response;

import com.tsintergy.lf.core.constants.SystemConstant;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/4/1 10:47
 * @Version: 1.0.0
 */

@Data
public class IndexInfoResp implements Serializable{

    @ApiModelProperty(value = "登录页名称",example = "清能互联电网")
    private String loginIndexName;


    @ApiModelProperty(value = "首页tittle信息",example = "清能互联电网")
    private String indexTitlel;


    @ApiModelProperty(value = "地图省份名称",example = "广东")
    private String MapName;



    public static IndexInfoResp builderBySetting(String setting){
        String[] values = setting.split(SystemConstant.SEPARATOR_PUNCTUATION);
        IndexInfoResp indexInfoResp = new IndexInfoResp();
        indexInfoResp.setLoginIndexName(values[0]);
        indexInfoResp.setIndexTitlel(values[1]);
        indexInfoResp.setMapName(values[2]);
        return indexInfoResp;
    }
}