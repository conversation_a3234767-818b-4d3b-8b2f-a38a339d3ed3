package com.tsintergy.lf.web.base.ultra.request;

import java.util.Date;
import lombok.Data;

@Data
public class LoadUltraFcFeatureRequest {
    private Date startDate;

    private Date endDate;

    private Date date;

    /**
     * 时间间隔 5 五分钟 5 十五分钟
     */
    private Integer type;

    private String cityId;

    private String caliberId;

    private String algorithmId;

    /**
     * 时间间隔 1 降序 2升序
     */
    private Integer sort;
}
