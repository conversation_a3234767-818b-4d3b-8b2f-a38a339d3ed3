package com.tsintergy.lf.web.base.datamanage.controller;


import com.alibaba.excel.EasyExcel;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.base.api.CaliberService;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CaliberDO;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.datamanage.api.DataCheckInfoService;
import com.tsintergy.lf.serviceapi.base.datamanage.dto.ErrorDataDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.api.StatisticsCityDayFcService;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.StatisticsCityDayFcDO;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcBatchService;
import com.tsintergy.lf.serviceapi.base.forecast.api.LoadCityFcService;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcBatchDO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcLoadForecastService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityHisService;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherDTO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcLoadForecastDO;
import com.tsintergy.lf.web.base.check.response.CommonResp;
import com.tsintergy.lf.web.base.common.request.CommonRequest;
import com.tsintergy.lf.web.base.controller.CommonBaseController;
import com.tsintergy.lf.web.base.datamanage.listener.ForecastWeatherCitiesExcelListener;
import com.tsintergy.lf.web.base.datamanage.listener.ForecastWeatherExcelListener;
import com.tsintergy.lf.web.base.datamanage.request.ForecastWeatherCitiesExcelRequest;
import com.tsintergy.lf.web.base.datamanage.request.ForecastWeatherExcelRequest;
import com.tsintergy.lf.web.base.datamanage.response.BatchFcWeatherVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @date: 4/11/18 12:02 PM
 * @author: angel
 **/
@Api(value = "数据清洗", tags = "数据清洗")
@RestController
@RequestMapping("/data")
public class DataManageController extends CommonBaseController {

    @Autowired
    LoadCityFcService loadCityFcService;
    @Autowired
    CityService cityService;
    @Autowired
    CaliberService caliberService;
    @Autowired
    LoadCityHisService loadCityHisService;
    @Autowired
    StatisticsCityDayFcService statisticsCityDayFcService;
    @Autowired
    WeatherCityFcLoadForecastService weatherCityFcLoadForecastService;
    @Autowired
    LoadCityFcBatchService loadCityFcBatchService;
    @Autowired
    private DataCheckInfoService dataCheckInfoService;
    @Autowired
    private WeatherCityFcService weatherCityFcService;
    @Autowired
    private WeatherCityHisService weatherCityHisService;

    /**
     * 预测气象数据导入 type为1
     */
    @RequestMapping(value = "/fcWeather/excel", method = RequestMethod.POST)
    public Object getFcExcel(@RequestParam(value = "file", required = false) MultipartFile file, String cityId)
            throws Exception {
        EasyExcel.read(file.getInputStream(), ForecastWeatherExcelRequest.class,
                        new ForecastWeatherExcelListener(weatherCityHisService, weatherCityFcService, cityService, cityId, "1"))
                .sheet(0)
                .headRowNumber(1).doRead();
        return BaseResp.succResp();
    }

    /**
     * 实际气象数据导入type为2
     */
    @RequestMapping(value = "/hisWeather/excel", method = RequestMethod.POST)
    public Object getHisExcel(@RequestParam(value = "file", required = false) MultipartFile file, String cityId)
            throws Exception {
        EasyExcel.read(file.getInputStream(), ForecastWeatherExcelRequest.class,
                        new ForecastWeatherExcelListener(weatherCityHisService, weatherCityFcService, cityService, cityId, "2"))
                .sheet(0)
                .headRowNumber(1).doRead();
        return BaseResp.succResp();
    }

    /**
     * 可选择多日多地市的气象导入 type 1 是预测数据导入  type2是实际数据导入
     */
    @RequestMapping(value = "/weather/excel", method = RequestMethod.POST)
    public Object getExcel(@RequestParam(value = "file", required = false) MultipartFile file, String type)
            throws Exception {
        EasyExcel.read(file.getInputStream(), ForecastWeatherCitiesExcelRequest.class,
                        new ForecastWeatherCitiesExcelListener(weatherCityHisService, weatherCityFcService, cityService, type))
                .sheet(0)
                .headRowNumber(1).doRead();
        return BaseResp.succResp();
    }

    /**
     * 获取数据清洗信息
     *
     * @param commonRequest
     * @param result
     * @return
     * @throws Exception
     */
    @ApiOperation("获取数据清洗信息")
    @RequestMapping(value = "/error", method = RequestMethod.GET)
    public BaseResp<CommonResp<ErrorDataDTO>> getDateError(@Validated CommonRequest commonRequest, BindingResult result) throws Exception {
      /*todo wangchen  暂时屏蔽该功能
        validateFormValue(result);
        BaseResp<CommonResp<ErrorDataDTO>> baseResp = BaseResp.succResp();
        CommonResp<ErrorDataDTO> errorDetailDTOCommonResp = new CommonResp<ErrorDataDTO>();
        errorDetailDTOCommonResp.setDataList(dataCheckInfoService.findLoadDataCheckInfoVO(commonRequest.getCityId(), commonRequest.getStartDate(), commonRequest.getEndDate()));
        baseResp.setData(errorDetailDTOCommonResp);*/
        BaseResp<CommonResp<ErrorDataDTO>> baseResp = BaseResp.succResp();
        baseResp.setRetCode("T708");
        baseResp.setRetMsg("该功能暂未开放");
        return baseResp;
    }

    @ApiOperation("获取数据清洗信息")
    @RequestMapping(value = "/error", method = RequestMethod.PUT)
    public BaseResp doDataClean(@Validated CommonRequest commonRequest, BindingResult result) throws Exception {
        validateFormValue(result);
        BaseResp baseResp = BaseResp.succResp("清洗成功");
        dataCheckInfoService.doCheckAndFixData(commonRequest.getStartDate(), commonRequest.getEndDate(), commonRequest.getCityId());
        return baseResp;
    }


    /**
     * 功能描述: <br>
     * 〈〉
     *
     * @param startDate
     * @param endDate
     * @param cityId
     * @param caliberId
     * @return:com.tsie.core.common.base.BaseResp
     * @since: 1.0.0
     * @Author:yangjin
     * @Date: 2019/8/12 15:46
     */
    @ApiOperation("获取负荷预测")
    @RequestMapping(value = "/powerLoadFc", method = RequestMethod.GET)
    public BaseResp<List<WeatherDTO>> getPowerLoadFc(@ApiParam(value = "开始时间") String startDate, @ApiParam(value = "结束时间") String endDate, @ApiParam(value = "城市id") String cityId, @ApiParam(value = "口径id") String caliberId) throws Exception {

        if (StringUtils.isEmpty(caliberId)) {
            caliberId = this.getCaliberId();
        }

        if (StringUtils.isEmpty(cityId)) {
            cityId = this.getLoginCityId();
        }
        SimpleDateFormat sdf = new SimpleDateFormat(DateUtil.DATE_FORMAT2);

        Date startTime = sdf.parse(startDate);
        Date endTime = sdf.parse(endDate);
        List<WeatherDTO> fc = loadCityFcService.getLoadCityFcByDateVO(startTime, endTime, cityId, caliberId, null);

        if (fc == null || fc.size() == 0) {
            TsieExceptionUtils.newBusinessException("T706");
        }
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(fc);
        return baseResp;
    }

    @ApiOperation("导出")
    @PostMapping("/export")
    public BaseResp export(@RequestParam("cityId") @ApiParam(value = "城市集合") List<String> cityIds, @ApiParam(value = "开始时间") Date startDate, @ApiParam(value = "结束时间") Date endDate,
                           HttpServletRequest request) throws Exception {
        String caliberId = null;
        if (caliberId == null) {
            caliberId = this.getCaliberId();
        }

        if (cityIds != null && cityIds.size() == 1) {
            if ("all".equals(cityIds.get(0))) {
                List<CityDO> allCitys = cityService.findAllCitys();
                List<String> collect = allCitys.stream().map(CityDO::getId).collect(Collectors.toList());
                cityIds = collect;
            }
        }

        // 创建工作簿对象
        XSSFWorkbook xssfWorkbook = new XSSFWorkbook();
        XSSFSheet sheet = xssfWorkbook.createSheet("负荷统计");
        XSSFSheet sheet1 = xssfWorkbook.createSheet("准确率统计");
        XSSFRow firstRow = sheet.createRow(0);
        XSSFCell firstRowFirstCell = firstRow.createCell(0);
        XSSFCell firstRowSecondtCell = firstRow.createCell(1);
        XSSFCell firstRowThirdCell = firstRow.createCell(2);
        XSSFCell firstRowFourthCell = firstRow.createCell(3);
        XSSFCell firstRowFiveCell = firstRow.createCell(4);
        firstRowFirstCell.setCellValue("时间");
        firstRowSecondtCell.setCellValue("城市");
        firstRowThirdCell.setCellValue("口径");
        firstRowFourthCell.setCellValue("预测负荷");
        firstRowFiveCell.setCellValue("实际负荷");

        XSSFRow secondSheetSixRow = sheet1.createRow(6);
        XSSFCell secondSheetSixRowFirstCell = secondSheetSixRow.createCell(0);
        XSSFCell secondSheetSixRowSecondtCell = secondSheetSixRow.createCell(1);
        XSSFCell secondSheetSixRowThirdCell = secondSheetSixRow.createCell(2);
        XSSFCell secondSheetSixRowFourthCell = secondSheetSixRow.createCell(3);
        secondSheetSixRowFirstCell.setCellValue("日期");
        secondSheetSixRowSecondtCell.setCellValue("城市");
        secondSheetSixRowThirdCell.setCellValue("口径");
        secondSheetSixRowFourthCell.setCellValue("准确率");

        //rowNum 记录行号
        int sheet1rowNum = 1;
        int sheet2RowNum = 7;


        CaliberDO caliberVO = caliberService.findCaliberDOByPk(caliberId);
        while (startDate.before(com.tsieframework.core.base.format.datetime.DateUtils.addDays(endDate, 1))) {
            for (String cityId : cityIds) {
                CityDO city = cityService.findCityById(cityId);
                List<LoadCityHisDO> loadCityHisVOS = loadCityHisService.getLoadCityHisDOS(cityId, caliberId, startDate, startDate);
                LoadCityFcDO reportLoadCityFcVO = null;
                try {
                    reportLoadCityFcVO = loadCityFcService.getReportLoadCityFcDO(startDate, cityId, caliberId);
                } catch (BusinessException e) {

                }
                List<BigDecimal> fcBigdecimals = null;
                if (reportLoadCityFcVO != null) {
                    fcBigdecimals = BasePeriodUtils.toList(reportLoadCityFcVO, 96, Constants.LOAD_CURVE_START_WITH_ZERO);
                }

                List<BigDecimal> hisBigDecimals = null;

                if (loadCityHisVOS != null && loadCityHisVOS.size() == 1) {
                    hisBigDecimals = BasePeriodUtils.toList(loadCityHisVOS.get(0), 96, Constants.LOAD_CURVE_START_WITH_ZERO);
                }
                String startStr = com.tsieframework.core.base.format.datetime.DateUtils
                        .date2String(startDate, DateFormatType.SIMPLE_DATE_FORMAT_STR);
                List<StatisticsCityDayFcDO> reportAccuracy = statisticsCityDayFcService
                        .getReportAccuracy(cityId, caliberId, startDate, startDate);
                StatisticsCityDayFcDO statisticsCityDayFcVO = null;
                if (reportAccuracy != null && reportAccuracy.size() == 1) {
                    statisticsCityDayFcVO = reportAccuracy.get(0);
                }

                XSSFRow sheet2row = sheet1.createRow(sheet2RowNum);

                sheet2row.createCell(0).setCellValue(startStr);
                sheet2row.createCell(1).setCellValue(city.getCity());
                sheet2row.createCell(2).setCellValue(caliberVO.getName());
                if (statisticsCityDayFcVO == null) {
                    sheet2row.createCell(3).setCellValue("");
                } else {
                    sheet2row.createCell(3).setCellValue(statisticsCityDayFcVO.getAccuracy() == null ? null
                            : statisticsCityDayFcVO.getAccuracy().doubleValue());
                }
                sheet2RowNum++;

                //sheet1循环次数
                int count = sheet1rowNum + 96;

                List<String> columns = ColumnUtil.getColumns(96, Constants.LOAD_CURVE_START_WITH_ZERO, false);
                for (int i = sheet1rowNum; i < count; i++) {
                    XSSFRow row = sheet.createRow(i);
                    XSSFCell cell0 = row.createCell(0);
                    XSSFCell cell1 = row.createCell(1);
                    XSSFCell cell2 = row.createCell(2);
                    XSSFCell cell3 = row.createCell(3);
                    XSSFCell cell4 = row.createCell(4);
                    String s1 = columns.get((i - 1) % 96);
                    String s = com.tsieframework.core.base.format.datetime.DateUtils
                            .date2String(startDate, DateFormatType.SIMPLE_DATE_FORMAT_VIRGULE_STR) + " " + s1
                            .substring(0, 2) + ":" + s1.substring(2) + ":00";
                    cell0.setCellValue(s);
                    cell1.setCellValue(city.getCity());
                    cell2.setCellValue(caliberVO.getName());
                    if (fcBigdecimals != null) {
                        cell3.setCellValue(fcBigdecimals.get((i - 1) % 96) == null ? null
                                : fcBigdecimals.get((i - 1) % 96).doubleValue());
                    }
                    if (hisBigDecimals != null) {
                        cell4.setCellValue(hisBigDecimals.get((i - 1) % 96) == null ? null
                                : hisBigDecimals.get((i - 1) % 96).doubleValue());
                    }
                    sheet1rowNum++;
                }
            }
            startDate = com.tsieframework.core.base.format.datetime.DateUtils.addDays(startDate, 1);
        }

        if (xssfWorkbook != null) {
            try {
                String fileName = "数据导出_" + com.tsieframework.core.base.format.datetime.DateUtils
                        .date2String(new Date(), DateFormatType.SIMPLE_DATE_FORMAT_COMMON_STR) + ".xlsx";
                String header = request.getHeader("User-Agent").toUpperCase();
                if (request.getHeader("User-Agent").toLowerCase().indexOf("firefox") > 0) {
                    fileName = new String(fileName.getBytes("UTF-8"), "ISO8859-1"); // firefox浏览器
                } else if (header.contains("MSIE") || header.contains("TRIDENT") || header.contains("EDGE")) {
                    fileName = URLEncoder.encode(fileName, "UTF-8");// IE浏览器
                } else if (request.getHeader("User-Agent").toUpperCase().indexOf("CHROME") > 0) {
                    fileName = new String(fileName.getBytes("UTF-8"), "ISO8859-1");// 谷歌
                }
                HttpServletResponse response = this.getResponse();
                OutputStream out = response.getOutputStream();
                response.reset();
                response.setContentType("application/x-xls");
                response.setCharacterEncoding("UTF-8");
                response.setHeader("Content-disposition",
                        "attachment; filename=" + fileName);
                xssfWorkbook.write(out);
                out.flush();
                out.close();
            } catch (IOException e) {
                e.printStackTrace();
            }

        }
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setRetMsg("导出成功");
        return baseResp;
    }

    @GetMapping("/batchFcWeatherData")
    public BaseResp batchFcWeatherData(String cityId, String caliberId, Date startDate, Date endDate, Integer type, String algorithmId) throws Exception {
        cityId = cityService.findWeatherCityId(cityId);
        List<WeatherCityFcLoadForecastDO> weatherInfoFcLoadForecast = weatherCityFcLoadForecastService.findWeatherInfoFcLoadForecast(
                cityId, type, algorithmId, startDate, endDate, caliberId);
        List<BatchFcWeatherVO> batchFcWeatherVOS = new ArrayList<>();
        if (!CollectionUtils.isEmpty(weatherInfoFcLoadForecast)) {
            CityDO cityById = cityService.findCityById(cityId);

            weatherInfoFcLoadForecast.forEach(t -> {
                BatchFcWeatherVO batchFcWeatherVO = new BatchFcWeatherVO();

                batchFcWeatherVO.setCityName(cityById.getCity());
                batchFcWeatherVO.setDate(t.getDate());
                Timestamp time = t.getCreatetime() != null ? t.getCreatetime() : t.getUpdatetime();
                String timeStr = DateUtils.date2String(time, DateFormatType.DATE_FORMAT_STR);
                timeStr = timeStr.substring(0, timeStr.length() - 3);
                batchFcWeatherVO.setFcDateStr(timeStr);
                List<BigDecimal> bigDecimals = BasePeriodUtils.toList(t, Constants.LOAD_CURVE_POINT_NUM,
                        Constants.LOAD_CURVE_START_WITH_ZERO);
                batchFcWeatherVO.setData(bigDecimals);
                batchFcWeatherVOS.add(batchFcWeatherVO);
            });
        }
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(batchFcWeatherVOS);
        return baseResp;
    }


    @GetMapping("/batchFcLoadData")
    public BaseResp batchFcLoadData(String cityId, String caliberId, Date startDate, Date endDate, String algorithmId) throws Exception {
        List<LoadCityFcBatchDO> loadCityFcBatchDOS = loadCityFcBatchService.findByCondition(
                cityId, startDate, endDate, caliberId, algorithmId);
        List<BatchFcWeatherVO> result = null;
        if (!CollectionUtils.isEmpty(loadCityFcBatchDOS)) {
            List<BatchFcWeatherVO> batchFcWeatherVOS = new ArrayList<>();
            CityDO cityById = cityService.findCityById(cityId);
            loadCityFcBatchDOS.forEach(t -> {
                BatchFcWeatherVO batchFcWeatherVO = new BatchFcWeatherVO();
                List<BigDecimal> bigDecimals = BasePeriodUtils.toList(t, Constants.LOAD_CURVE_POINT_NUM,
                        Constants.LOAD_CURVE_START_WITH_ZERO);
                batchFcWeatherVO.setData(bigDecimals);
                batchFcWeatherVO.setCityName(cityById.getCity());
                batchFcWeatherVO.setDate(t.getDate());
                String timeStr = DateUtils.date2String(t.getCreatetime(), DateFormatType.DATE_FORMAT_STR);
                timeStr = timeStr.substring(0, timeStr.length() - 3);
                batchFcWeatherVO.setFcDateStr(timeStr);
                batchFcWeatherVO.setBatchId(t.getBatchId());
                batchFcWeatherVOS.add(batchFcWeatherVO);
            });

            //排序
            result = batchFcWeatherVOS.stream()
                    .sorted(Comparator.comparing(BatchFcWeatherVO::getDate)
                            .thenComparing(BatchFcWeatherVO::getBatchId).reversed()).collect(Collectors.toList());
        }
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(result);
        return baseResp;
    }
}
