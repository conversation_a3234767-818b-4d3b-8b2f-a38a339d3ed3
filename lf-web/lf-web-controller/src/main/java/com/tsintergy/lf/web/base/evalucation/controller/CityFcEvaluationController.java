package com.tsintergy.lf.web.base.evalucation.controller;

import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.evalucation.api.EvalucationService;
import com.tsintergy.lf.serviceapi.base.evalucation.api.StatisticsCityDayFcService;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.CityDayAccuracyDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.DayFcEvaluateDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.PeakLoadDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.PeakLoadDeviationMonitorDTO;
import com.tsintergy.lf.web.base.controller.CommonBaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Api(tags = "地势预测评估")
@RequestMapping("/history")
@RestController
public class CityFcEvaluationController extends CommonBaseController {

    @Autowired
    StatisticsCityDayFcService statisticsCityDayFcService;

    @Autowired
    CityService cityService;

    @Autowired
    EvalucationService evalucationService;

    /**
     * 获取地势准确率排名
     */
    @ApiOperation("获取地势准确率排名")
    @GetMapping(value = "/terrainAccuracy")
    public BaseResp<CityDayAccuracyDTO> findTerrainAccuracy(Date yesterdayDate) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        String caliberId = this.getCaliberId();
        List<String> cityIDs = new ArrayList<>();
        List<CityDO> citys = cityService.findAllCitys();
        for (CityDO cityDO : citys){
            if(cityDO.getId() != null && !"1".equals(cityDO.getId())){
                cityIDs.add(cityDO.getId());
            }
        }
        List<CityDayAccuracyDTO> cityDayAccuracyDTOList = statisticsCityDayFcService.getCityDayAccuracyDTOList(cityIDs, caliberId, "0", yesterdayDate);
        if (cityDayAccuracyDTOList == null) {
            return new BaseResp("T706");
        }
        baseResp.setData(cityDayAccuracyDTOList);
        return baseResp;
    }

    /**
     * 高峰负荷偏差监控
     */
    @ApiOperation("高峰负荷偏差监控")
    @GetMapping(value = "/peak/monitor")
    public BaseResp<PeakLoadDeviationMonitorDTO> findPeakMonitor(Date yesterdayDate) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        String caliberId = this.getCaliberId();
        List<String> cityIDs = new ArrayList<>();
        List<CityDO> citys = cityService.findAllCitys();
        for (CityDO cityDO : citys){
            if(cityDO.getId() != null && !"1".equals(cityDO.getId())){
                cityIDs.add(cityDO.getId());
            }
        }
        List<PeakLoadDeviationMonitorDTO> PeakLoadDeviationMonitorDTOList = evalucationService.getPeakLoadDeviationMonitorDTOList(cityIDs, caliberId,yesterdayDate);
        if (PeakLoadDeviationMonitorDTOList == null) {
            return new BaseResp("T706");
        }
        baseResp.setData(PeakLoadDeviationMonitorDTOList);
        return baseResp;
    }

    /**
     * 获取近7日高峰负荷
     */
    @ApiOperation("获取近7日高峰负荷")
    @GetMapping(value = "/peak/load")
    public BaseResp<PeakLoadDTO> findPeakLoad(String cityId,Date startDate,Date endDate) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        String caliberId = this.getCaliberId();
        PeakLoadDTO peakLoadDTO = evalucationService.getPeakLoadDTO(cityId, caliberId, startDate, endDate);
        if (peakLoadDTO == null) {
            return new BaseResp("T706");
        }
        baseResp.setData(peakLoadDTO);
        return baseResp;
    }


    /**
     * 获取当日预测评估
     */
    @ApiOperation("获取当日预测评估")
    @GetMapping(value = "/day/evaluate")
    public BaseResp<DayFcEvaluateDTO> findDayEvaluate(Date currentDate) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        String caliberId = this.getCaliberId();
        List<String> cityIDs = new ArrayList<>();
        List<CityDO> citys = cityService.findAllCitys();
        for (CityDO cityDO : citys){
            if(cityDO.getId() != null && !"1".equals(cityDO.getId())){
                cityIDs.add(cityDO.getId());
            }
        }
        List<DayFcEvaluateDTO> DayFcEvaluateDTOList = evalucationService.getDayFcEvaluateDTOList(cityIDs,currentDate,caliberId);
        if (DayFcEvaluateDTOList == null) {
            return new BaseResp("T706");
        }
        baseResp.setData(DayFcEvaluateDTOList);
        return baseResp;
    }



}
