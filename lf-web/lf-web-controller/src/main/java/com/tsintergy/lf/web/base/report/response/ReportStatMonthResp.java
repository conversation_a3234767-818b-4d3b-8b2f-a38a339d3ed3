/**
 * Copyright (c), 2015-2021, 北京清能互联科技有限公司
 * Author: zhangjy
 * Date: 2021/1/18 15:15
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.report.response;

/**  
 * Description: 月上报准确率信息 <br> 
 * 
 * <AUTHOR>  
 * @create 2020/11/10  
 * @since 1.0.0  
 */
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;


public class ReportStatMonthResp implements Serializable {


    @ApiModelProperty(value = "城市id",example = "1")
    private String cityId;

    @ApiModelProperty(value = "城市名称",example = "广东")
    private String city;

    @ApiModelProperty(value = "正常上报天数",example = "5")
    private int reportNormal = 0;

    @ApiModelProperty(value = "延迟上报天数",example = "5")
    private int reprotDelayed = 0;

    @ApiModelProperty(value = "总计上报天数",example = "5")
    private int reportTotal = 0;

    public String getCityId() {
        return cityId;
    }

    public void setCityId(String cityId) {
        this.cityId = cityId;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public int getReportNormal() {
        return reportNormal;
    }

    public void setReportNormal(int reportNormal) {
        this.reportNormal = reportNormal;
    }

    public int getReprotDelayed() {
        return reprotDelayed;
    }

    public void setReprotDelayed(int reprotDelayed) {
        this.reprotDelayed = reprotDelayed;
    }

    public int getReportTotal() {
        return reportTotal;
    }

    public void setReportTotal(int reportTotal) {
        this.reportTotal = reportTotal;
    }
}