package com.tsintergy.lf.web.base.util.ultra;

import com.tsieframework.core.base.vo.BasePeriod96VO;
import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ExcelMoreDayWeatherDataHis96 extends BasePeriod96VO {

    private String id;

    private String date;

    private String cityId;

    private String type;

    private BigDecimal t0000;

    private BigDecimal t0015;

    private BigDecimal t0030;

    private BigDecimal t0045;

    private BigDecimal t0100;

    private BigDecimal t0115;

    private BigDecimal t0130;

    private BigDecimal t0145;

    private BigDecimal t0200;

    private BigDecimal t0215;

    private BigDecimal t0230;

    private BigDecimal t0245;

    private BigDecimal t0300;

    private BigDecimal t0315;

    private BigDecimal t0330;

    private BigDecimal t0345;

    private BigDecimal t0400;

    private BigDecimal t0415;

    private BigDecimal t0430;

    private BigDecimal t0445;

    private BigDecimal t0500;

    private BigDecimal t0515;

    private BigDecimal t0530;

    private BigDecimal t0545;

    private BigDecimal t0600;

    private BigDecimal t0615;

    private BigDecimal t0630;

    private BigDecimal t0645;

    private BigDecimal t0700;

    private BigDecimal t0715;

    private BigDecimal t0730;

    private BigDecimal t0745;

    private BigDecimal t0800;

    private BigDecimal t0815;

    private BigDecimal t0830;

    private BigDecimal t0845;

    private BigDecimal t0900;

    private BigDecimal t0915;

    private BigDecimal t0930;

    private BigDecimal t0945;

    private BigDecimal t1000;

    private BigDecimal t1015;

    private BigDecimal t1030;

    private BigDecimal t1045;

    private BigDecimal t1100;

    private BigDecimal t1115;

    private BigDecimal t1130;

    private BigDecimal t1145;

    private BigDecimal t1200;

    private BigDecimal t1215;

    private BigDecimal t1230;

    private BigDecimal t1245;

    private BigDecimal t1300;

    private BigDecimal t1315;

    private BigDecimal t1330;

    private BigDecimal t1345;

    private BigDecimal t1400;

    private BigDecimal t1415;

    private BigDecimal t1430;

    private BigDecimal t1445;

    private BigDecimal t1500;

    private BigDecimal t1515;

    private BigDecimal t1530;

    private BigDecimal t1545;

    private BigDecimal t1600;

    private BigDecimal t1615;

    private BigDecimal t1630;

    private BigDecimal t1645;

    private BigDecimal t1700;

    private BigDecimal t1715;

    private BigDecimal t1730;

    private BigDecimal t1745;

    private BigDecimal t1800;

    private BigDecimal t1815;

    private BigDecimal t1830;

    private BigDecimal t1845;

    private BigDecimal t1900;

    private BigDecimal t1915;

    private BigDecimal t1930;

    private BigDecimal t1945;

    private BigDecimal t2000;

    private BigDecimal t2015;

    private BigDecimal t2030;

    private BigDecimal t2045;

    private BigDecimal t2100;

    private BigDecimal t2115;

    private BigDecimal t2130;

    private BigDecimal t2145;

    private BigDecimal t2200;

    private BigDecimal t2215;

    private BigDecimal t2230;

    private BigDecimal t2245;

    private BigDecimal t2300;

    private BigDecimal t2315;

    private BigDecimal t2330;

    private BigDecimal t2345;

    private String createTime;

    private String updateTime;


}
