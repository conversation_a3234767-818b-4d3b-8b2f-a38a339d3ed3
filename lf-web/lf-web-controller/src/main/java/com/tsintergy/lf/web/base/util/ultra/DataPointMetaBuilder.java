/**
 * Copyright (C), 2015‐2021, 北京清能互联科技有限公司 Author:  wa<PERSON><PERSON>@tsintergy.com Date:  2021/12/6 2:32 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.util.ultra;

import com.tsieframework.core.base.dao.type.DataPointListMeta;
import com.tsieframework.core.base.dao.type.hibernate.DeltaUnit;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsintergy.aif.tool.core.utils.date.DateUtil;
import com.tsintergy.lf.core.util.UltraSystemUtils;
import java.util.Calendar;
import java.util.Date;

/**
 * Description:  <br> 数据项设置默认构造
 *
 * <AUTHOR>
 * @create 2023/01/09
 * @since 1.0.0
 */
public class DataPointMetaBuilder {

    private DataPointListMeta dataPointListMeta;

    private static String TIME_CONFINE = "2400";


    public static DataPointMetaBuilder create() {
        return new DataPointMetaBuilder();
    }


    /**
     * 构建默认的DataPointListMeta 开始点读取startWithZero；true；00:00:00 false 00:15:00;默认点数：96点 默认间隔：15分钟 默认间隔单位：分钟
     */
    public static DataPointMetaBuilder default96StartWithZero(Date date) {
        DataPointMetaBuilder builder = create();
        Date newDate = clearHourMinute(date);
        if (!UltraSystemUtils.startWithZero()) {
            newDate = DateUtils.addMinutes(newDate, 15);
        }
        DataPointListMeta defaultDataPoint = DataPointListMeta
                .create(newDate, DataPointListMeta.POINTS_96, DataPointListMeta.MINUTE_15, DeltaUnit.MINUTES);
        builder.dataPointListMeta = defaultDataPoint;
        return builder;
    }


    /**
     * 构建默认的DataPointListMeta 开始点读取startWithZero；true；00:00:00 false 00:15:00;默认点数：96点 默认间隔：15分钟 默认间隔单位：分钟
     */
    public static DataPointListMeta createPointMeta(Date date, Integer delta) {
        Date newDate = clearHourMinute(date);
        if (!UltraSystemUtils.startWithZero()) {
            newDate = DateUtils.addMinutes(newDate, 15);
        }
        Integer pointSize =
                DataPointListMeta.MINUTE_5 == delta ? DataPointListMeta.POINTS_288 : DataPointListMeta.POINTS_96;
        return DataPointListMeta
                .create(newDate, pointSize, delta, DeltaUnit.MINUTES);
    }

    /**
     * 构建默认的DataPointListMeta 开始点读取startWithZero；true；00:00:00 false 00:15:00;默认点数：288点 默认间隔：5分钟 默认间隔单位：分钟
     */
    public static DataPointMetaBuilder default288StartWithZero(Date date) {
        DataPointMetaBuilder builder = create();
        Date newDate = clearHourMinute(date);
        if (!UltraSystemUtils.startWithZero()) {
            newDate = DateUtils.addMinutes(newDate, 5);
        }
        DataPointListMeta defaultDataPoint = DataPointListMeta
                .create(newDate, DataPointListMeta.POINTS_288, DataPointListMeta.MINUTE_5, DeltaUnit.MINUTES);
        builder.dataPointListMeta = defaultDataPoint;
        return builder;
    }


    /**
     * 根据传入的时间构建meta对象
     *
     * @param delta 5 or 15
     */
    public static DataPointListMeta dataPointMetaBuilderOnTime(Date date, String startTime, Integer delta) {

        String dateTimeStr =
                DateUtil.date2String(date, DateFormatType.SIMPLE_DATE_FORMAT_STR) + " " + startTime.substring(0, 2) + ":"
                        + startTime.substring(2) + ":" + "00";
        Date startDateTime = DateUtil.string2Date(dateTimeStr, DateFormatType.DATE_FORMAT_STR);
        if (DataPointMetaBuilder.TIME_CONFINE.equals(startTime)) {
            //START_WITH_ZERO false时会出现时刻点 2400；Data存储2400会自动跨天为0000，当前存储为23:59:59避免跨天；
            startDateTime = DateUtils.addSeconds(startDateTime, -1);
        }
        Integer pointSize =
                DataPointListMeta.MINUTE_5 == delta ? DataPointListMeta.POINTS_288 : DataPointListMeta.POINTS_96;
        return DataPointListMeta
                .create(startDateTime, pointSize, delta, DeltaUnit.MINUTES);
    }

    public DataPointListMeta build() {
        return this.dataPointListMeta;
    }

    private static Date clearHourMinute(Date date) {
        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(date);
        // 将时分秒,毫秒域清零
        cal1.set(Calendar.HOUR_OF_DAY, 0);
        cal1.set(Calendar.MINUTE, 0);
        cal1.set(Calendar.SECOND, 0);
        cal1.set(Calendar.MILLISECOND, 0);
        return cal1.getTime();
    }
}