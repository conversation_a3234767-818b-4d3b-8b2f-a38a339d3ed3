package com.tsintergy.lf.web.base.typicalload.controller;

import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.serviceapi.base.bgd.dto.LongInfoDTO;
import com.tsintergy.lf.serviceapi.base.typical.api.TypicalLoadCurveService;
import com.tsintergy.lf.serviceapi.base.typical.dto.Curve96Load;
import com.tsintergy.lf.web.base.controller.CommonBaseController;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description: 典型负荷曲线
 *
 * <AUTHOR>
 * @create 2023-09-18
 * @since 1.0.0
 */
@RequestMapping("/typical")
@RestController
public class TypicalLoadCurveController extends CommonBaseController {

    @Autowired
    private TypicalLoadCurveService typicalLoadCurveService;

    @ApiOperation("周曲线对比")
    @GetMapping(value = "/week/curveCompare")
    public BaseResp<LongInfoDTO> findWeekCurveCompare(String cityId, String dateStr)
        throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<Curve96Load> weekCurveCompare = typicalLoadCurveService.findWeekCurveCompare(cityId, dateStr);
        if (CollectionUtils.isEmpty(weekCurveCompare)) {
            return new BaseResp("T706");
        }
        baseResp.setData(weekCurveCompare);
        return baseResp;
    }

    @ApiOperation("周多年对比")
    @GetMapping(value = "/week/yearCurveCompare")
    public BaseResp<LongInfoDTO> findWeekYearCurveCompare(String cityId, String dateStr, Integer type)
        throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<Curve96Load> weekCurveCompare = typicalLoadCurveService.findYearCurveCompare(cityId, dateStr, type);
        if (CollectionUtils.isEmpty(weekCurveCompare)) {
            return new BaseResp("T706");
        }
        baseResp.setData(weekCurveCompare);
        return baseResp;
    }

    @ApiOperation("旬上中下旬对比")
    @GetMapping(value = "/tenDays/curveCompare")
    public BaseResp<LongInfoDTO> findTenDaysCurveCompare(String cityId, String dateStr, Integer type)
        throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<Curve96Load> weekCurveCompare = typicalLoadCurveService.findTenDaysCurveCompare(cityId, dateStr, type);
        if (CollectionUtils.isEmpty(weekCurveCompare)) {
            return new BaseResp("T706");
        }
        baseResp.setData(weekCurveCompare);
        return baseResp;
    }

    @ApiOperation("旬多年对比")
    @GetMapping(value = "/tenDays/yearCurveCompare")
    public BaseResp<LongInfoDTO> findTenDaysYearCurveCompare(String cityId, String dateStr, Integer type,
        Integer tenDaysType) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<Curve96Load> weekCurveCompare = typicalLoadCurveService.findTenDaysYearCurveCompare(cityId, dateStr, type,
            tenDaysType);
        if (CollectionUtils.isEmpty(weekCurveCompare)) {
            return new BaseResp("T706");
        }
        baseResp.setData(weekCurveCompare);
        return baseResp;
    }

    @ApiOperation("月连续多月")
    @GetMapping(value = "/month/curveCompare")
    public BaseResp<LongInfoDTO> findMonthCurveCompare(String cityId, String dateStart, String dateEnd, Integer type)
        throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<Curve96Load> weekCurveCompare = typicalLoadCurveService.findMonthCurveCompare(cityId, dateStart, dateEnd, type);
        if (CollectionUtils.isEmpty(weekCurveCompare)) {
            return new BaseResp("T706");
        }
        baseResp.setData(weekCurveCompare);
        return baseResp;
    }

    @ApiOperation("月多年对比")
    @GetMapping(value = "/month/yearCurveCompare")
    public BaseResp<LongInfoDTO> findMonthYearCurveCompare(String cityId, String dateStr, Integer type)
        throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<Curve96Load> weekCurveCompare = typicalLoadCurveService.findMonthYearCurveCompare(cityId, dateStr, type);
        if (CollectionUtils.isEmpty(weekCurveCompare)) {
            return new BaseResp("T706");
        }
        baseResp.setData(weekCurveCompare);
        return baseResp;
    }

    @ApiOperation("季四季对比")
    @GetMapping(value = "/season/curveCompare")
    public BaseResp<LongInfoDTO> findSeasonCurveCompare(String cityId, String dateStr, Integer type)
        throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<Curve96Load> weekCurveCompare = typicalLoadCurveService.findSeasoCurveCompare(cityId, dateStr, type);
        if (CollectionUtils.isEmpty(weekCurveCompare)) {
            return new BaseResp("T706");
        }
        baseResp.setData(weekCurveCompare);
        return baseResp;
    }

    @ApiOperation("季多年对比")
    @GetMapping(value = "/season/yearCurveCompare")
    public BaseResp<LongInfoDTO> findSeasonYearCurveCompare(String cityId, String dateStr, Integer type,
        Integer seasonType) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<Curve96Load> weekCurveCompare = typicalLoadCurveService.findSeasonYearCurveCompare(cityId, dateStr, type,
            seasonType);
        if (CollectionUtils.isEmpty(weekCurveCompare)) {
            return new BaseResp("T706");
        }
        baseResp.setData(weekCurveCompare);
        return baseResp;
    }

    @ApiOperation("年典型曲线")
    @GetMapping(value = "/year/yearCurveCompare")
    public BaseResp<LongInfoDTO> findYearCurveCompare(String cityId, String dateStr, Integer type)
        throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<Curve96Load> weekCurveCompare = typicalLoadCurveService.findAllYearCurveCompare(cityId, dateStr, type);
        if (CollectionUtils.isEmpty(weekCurveCompare)) {
            return new BaseResp("T706");
        }
        baseResp.setData(weekCurveCompare);
        return baseResp;
    }
}
