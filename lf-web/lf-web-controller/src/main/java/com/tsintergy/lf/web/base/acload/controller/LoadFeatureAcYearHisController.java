/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.web.base.acload.controller;

import com.tsieframework.core.base.web.BaseResp;
import com.tsieframework.core.base.web.BaseRespBuilder;
import com.tsintergy.lf.serviceapi.base.airconditioner.api.LoadFeatureAcHisService;
import com.tsintergy.lf.serviceapi.base.airconditioner.dto.HighestTemperatureDTO;
import com.tsintergy.lf.serviceapi.base.airconditioner.dto.MaxAcLoadDTO;
import com.tsintergy.lf.serviceapi.base.airconditioner.dto.StatisticsAcDTO;
import com.tsintergy.lf.web.base.acload.request.AcLoadYearAnalyseRequest;
import com.tsintergy.lf.web.base.controller.CommonBaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Description:<br>
 *
 * @Author:liujp
 */
@RequestMapping("/ac/feature")
@RestController
@Api(tags = "空调负荷年特性分析")
public class LoadFeatureAcYearHisController extends CommonBaseController {

    private Logger logger = LogManager.getLogger(LoadFeatureAcYearHisController.class);

    @Autowired
    LoadFeatureAcHisService loadFeatureAcHisService;

    /**
     * 获取最大空调负荷曲线
     */
    @RequestMapping(value = "/maxAirLoad", method = RequestMethod.POST)
    @ApiOperation("获取最大空调负荷曲线")
    public BaseResp<List<MaxAcLoadDTO>> getMaxAirLoad(@RequestBody AcLoadYearAnalyseRequest request)
            throws Exception {
        List<MaxAcLoadDTO> maxAcLoadDTOList = loadFeatureAcHisService
                .getMaxAcLoadDTOList(request.getCityId(), getCaliberId(), request.getYear(), request.getType());
        return BaseRespBuilder.success().setData(maxAcLoadDTOList).build();
    }


    /**
     * 最高温度曲线
     */
    @RequestMapping(value = "/highestTemperature", method = RequestMethod.POST)
    @ApiOperation("获取最高温度曲线")
    public BaseResp<List<HighestTemperatureDTO>> getHighestTemperature(@RequestBody AcLoadYearAnalyseRequest request)
            throws Exception {
        List<HighestTemperatureDTO> highestTemperatureDTOList = loadFeatureAcHisService
                .getHighestTemperatureDTOList(request.getCityId(), getCaliberId(), request.getYear(), request.getType());
        return BaseRespBuilder.success().setData(highestTemperatureDTOList).build();
    }


    @ApiOperation("特性统计")
    @RequestMapping(value = "/statisticsAcYearFeature", method = RequestMethod.POST)
    public BaseResp<List<StatisticsAcDTO>> getLoadCurve(@RequestBody AcLoadYearAnalyseRequest request) throws Exception {
        List<StatisticsAcDTO> statisticsAcDTOList = loadFeatureAcHisService
                .getStatisticsAcDTOList(request.getCityId(), getCaliberId(), request.getYear(), request.getType());
        return BaseRespBuilder.success().setData(statisticsAcDTOList).build();
    }

}