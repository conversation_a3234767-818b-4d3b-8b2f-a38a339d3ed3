package com.tsintergy.lf.web.base.ultra.request;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotNull;
import org.hibernate.validator.constraints.NotEmpty;

/**
 * @date: 3/5/18 4:22 PM
 * @author: wangh
 **/
public class UltraResultPushOrSaveRequest {

    @ApiModelProperty(value = "城市id")
    private String cityId;
    /**
     * 预测结果列表
     */
    @ApiModelProperty(value = "预测结果列表")
    private Result forecastList;

    @NotNull(message = "类型不能为空")
    @ApiModelProperty(value = "类型")
    private Integer type;

    @ApiModelProperty(value = "口径id")
    private String caliberId;

    @ApiModelProperty(value = "时间戳 5 or 15")
    private Integer timeSpan;


    public Integer getTimeSpan() {
        return timeSpan;
    }

    public void setTimeSpan(Integer timeSpan) {
        this.timeSpan = timeSpan;
    }

    public Result getForecastList() {
        return forecastList;
    }

    public void setForecastList(Result forecastList) {
        this.forecastList = forecastList;
    }

    public String getCaliberId() {
        return caliberId;
    }

    public void setCaliberId(String caliberId) {
        this.caliberId = caliberId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public static class Result {

        @NotEmpty(message = "算法id不能为空")
        private String algorithmId;

        @NotNull(message = "日期不能为空")
        private Date targetDay;

        @NotEmpty(message = "预测数据不能为空")
        private List<BigDecimal> value;

        public String getAlgorithmId() {
            return algorithmId;
        }

        public void setAlgorithmId(String algorithmId) {
            this.algorithmId = algorithmId;
        }

        public Date getTargetDay() {
            return targetDay;
        }

        public void setTargetDay(Date targetDay) {
            this.targetDay = targetDay;
        }

        public List<BigDecimal> getValue() {
            return value;
        }

        public void setValue(List<BigDecimal> value) {
            this.value = value;
        }
    }

    public String getCityId() {
        return cityId;
    }

    public void setCityId(String cityId) {
        this.cityId = cityId;
    }
}
