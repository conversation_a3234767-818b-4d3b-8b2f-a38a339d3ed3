/**
 * Copyright(C),2015‐2021,北京清能互联科技有限公司
 */
package com.tsintergy.lf.web.base.datamanage.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.forecast.enums.WeatherEnum;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityHisService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisDO;
import com.tsintergy.lf.web.base.datamanage.request.ForecastWeatherCitiesExcelRequest;
import java.sql.Date;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.beans.BeanUtils;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2021/12/23 14:40
 * @Version: 1.0.0
 */
public class ForecastWeatherCitiesExcelListener extends AnalysisEventListener<ForecastWeatherCitiesExcelRequest> {

    private WeatherCityHisService weatherCityHisService;

    private WeatherCityFcService weatherCityFcService;

    private CityService cityService;

    private String type;

    private static final String FC = "1";

    private static final String HIS = "2";

    private List<ForecastWeatherCitiesExcelRequest> list;

    public ForecastWeatherCitiesExcelListener(
        WeatherCityHisService weatherCityHisService,
        WeatherCityFcService weatherCityFcService, CityService cityService,
        String type) {
        this.weatherCityHisService = weatherCityHisService;
        this.weatherCityFcService = weatherCityFcService;
        this.type = type;
        this.cityService = cityService;
        list = new ArrayList<>();
    }

    @Override
    public void invoke(ForecastWeatherCitiesExcelRequest data, AnalysisContext context) {
        list.add(data);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        for (ForecastWeatherCitiesExcelRequest forecastWeatherCitiesExcelRequest : list) {
            try {
                processExcelData(forecastWeatherCitiesExcelRequest);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private void processExcelData(ForecastWeatherCitiesExcelRequest forecastWeatherCitiesExcelRequest)
        throws Exception {
        List<CityDO> allCitys = cityService.findAllCitys();
        Map<String, CityDO> cityDOMap = allCitys.stream()
            .collect(Collectors.toMap(CityDO::getCity, e -> e, (o, v) -> v));
        Date date = new Date(DateUtils.string2Date(forecastWeatherCitiesExcelRequest.getDate(),
            DateFormatType.DATE_FORMAT_STR).getTime());
        String cityId = cityDOMap.get(forecastWeatherCitiesExcelRequest.getCityName()).getId();
        Integer weatherType = WeatherEnum.getTypeByName(forecastWeatherCitiesExcelRequest.getType());
        if (type.equals(FC)) {
            WeatherCityFcDO weatherCityFcDO = weatherCityFcService
                .findWeatherCityFcDO(cityId, weatherType,
                    date);
            if (weatherCityFcDO == null) {
                weatherCityFcDO = new WeatherCityFcDO();
                weatherCityFcDO.setCityId(cityId);
                weatherCityFcDO.setDate(date);

                weatherCityFcDO.setCreatetime(new Timestamp(System.currentTimeMillis()));
            }
            BeanUtils.copyProperties(forecastWeatherCitiesExcelRequest, weatherCityFcDO);
            weatherCityFcDO.setType(weatherType);
            weatherCityFcService.doInsertOrUpdate(weatherCityFcDO);
        } else if (type.equals(HIS)) {
            WeatherCityHisDO weatherCityHisDO = weatherCityHisService
                .findWeatherCityHisDO(cityId, weatherType,
                    date);
            if (weatherCityHisDO == null) {
                weatherCityHisDO = new WeatherCityHisDO();
                weatherCityHisDO.setCityId(cityId);
                weatherCityHisDO.setDate(date);
                weatherCityHisDO.setCreatetime(new Timestamp(System.currentTimeMillis()));
            }
            BeanUtils.copyProperties(forecastWeatherCitiesExcelRequest, weatherCityHisDO);
            weatherCityHisDO.setType(weatherType);
            weatherCityHisService.doInsertOrUpdate(weatherCityHisDO);
        } else {
            throw new Exception("类型入参错误");
        }
    }

}