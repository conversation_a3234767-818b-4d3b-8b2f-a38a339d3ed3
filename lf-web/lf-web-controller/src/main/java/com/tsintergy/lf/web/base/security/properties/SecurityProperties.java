/**
 * Copyright (C), 2015‐2020, 北京清能互联科技有限公司
 * Author:  wangchen
 * Date:  2020/2/5 17:21
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.security.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2020/2/5 
 * @since 1.0.0
 */
@Data
@ConfigurationProperties(prefix = "security")
public class SecurityProperties {

    private boolean tokenInterceptorOpen = false;

    private Password password;

    private String tokenCacheKeyPrefix;

}
