package com.tsintergy.lf.web.base.ultra.response;

import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import com.tsintergy.lf.serviceapi.base.ultra.dto.UltraLoadFeatureFcDTO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayFcDO;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @date: 3/21/18 4:41 PM
 * @author: angel
 **/
public class UltraLoadAndWeatherFeatureFcResp implements Serializable {

    /**
     * 最大负荷
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal maxLoad;

    /**
     * 最小负荷
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal minLoad;

    /**
     * 平均负荷
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal aveLoad;

    /**
     * 最大负荷发生时刻
     */
    private String maxTime;

    /**
     * 最小负荷发生时刻
     */
    private String minTime;

    /**
     * 峰谷差
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal different;

    /**
     * 峰谷差率
     */
    @BigdecimalJsonFormat(scale = 4,percentConvert = 100)
    private BigDecimal gradient;


    /**
     * 积分电量
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal integralLoad;

    /**
     * 峰段电量
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal peakSectionLoad;


    /**
     * 最高温度
     */
    private BigDecimal highestTemperature;

    /**
     * 最低温度
     */
    private BigDecimal lowestTemperature;

    /**
     * 平均温度
     */
    private BigDecimal aveTemperature;

    /**
     * 最大风速
     */
    private BigDecimal maxWinds;


    /**
     * 降雨量
     */
    private BigDecimal rainfall;

    /**
     * 平均相对湿度
     */
    private BigDecimal aveHumidity;

    public UltraLoadAndWeatherFeatureFcResp(WeatherFeatureCityDayFcDO weatherFeature, UltraLoadFeatureFcDTO loadFeatureFcDTO) {
        if (loadFeatureFcDTO != null) {
            this.maxLoad = loadFeatureFcDTO.getMaxLoad();
            this.minLoad = loadFeatureFcDTO.getMinLoad();
            this.aveLoad = loadFeatureFcDTO.getAveLoad();
            this.maxTime = loadFeatureFcDTO.getMaxTime();
            this.minTime = loadFeatureFcDTO.getMinTime();
            this.different = loadFeatureFcDTO.getDifferent();
            this.gradient = loadFeatureFcDTO.getGradient();
            this.integralLoad = loadFeatureFcDTO.getIntegralLoad();
        }
        if (weatherFeature != null) {
            this.highestTemperature = weatherFeature.getHighestTemperature();
            this.lowestTemperature = weatherFeature.getLowestTemperature();
            this.aveTemperature = weatherFeature.getAveTemperature();
            this.maxWinds = weatherFeature.getMaxWinds();
            this.rainfall = weatherFeature.getRainfall();
            this.aveHumidity = weatherFeature.getAveHumidity();
        }
    }

    public BigDecimal getAveHumidity() {
        return aveHumidity;
    }

    public void setAveHumidity(BigDecimal aveHumidity) {
        this.aveHumidity = aveHumidity;
    }

    public BigDecimal getHighestTemperature() {
        return highestTemperature;
    }

    public void setHighestTemperature(BigDecimal highestTemperature) {
        this.highestTemperature = highestTemperature;
    }

    public BigDecimal getLowestTemperature() {
        return lowestTemperature;
    }

    public void setLowestTemperature(BigDecimal lowestTemperature) {
        this.lowestTemperature = lowestTemperature;
    }

    public BigDecimal getAveTemperature() {
        return aveTemperature;
    }

    public void setAveTemperature(BigDecimal aveTemperature) {
        this.aveTemperature = aveTemperature;
    }

    public BigDecimal getMaxWinds() {
        return maxWinds;
    }

    public void setMaxWinds(BigDecimal maxWinds) {
        this.maxWinds = maxWinds;
    }

    public BigDecimal getRainfall() {
        return rainfall;
    }

    public void setRainfall(BigDecimal rainfall) {
        this.rainfall = rainfall;
    }

    public BigDecimal getMaxLoad() {
        return maxLoad;
    }

    public void setMaxLoad(BigDecimal maxLoad) {
        this.maxLoad = maxLoad;
    }

    public BigDecimal getMinLoad() {
        return minLoad;
    }

    public void setMinLoad(BigDecimal minLoad) {
        this.minLoad = minLoad;
    }

    public BigDecimal getAveLoad() {
        return aveLoad;
    }

    public void setAveLoad(BigDecimal aveLoad) {
        this.aveLoad = aveLoad;
    }

    public String getMaxTime() {
        return maxTime;
    }

    public void setMaxTime(String maxTime) {
        this.maxTime = maxTime;
    }

    public String getMinTime() {
        return minTime;
    }

    public void setMinTime(String minTime) {
        this.minTime = minTime;
    }

    public BigDecimal getDifferent() {
        return different;
    }

    public void setDifferent(BigDecimal different) {
        this.different = different;
    }

    public BigDecimal getGradient() {
        return gradient;
    }

    public void setGradient(BigDecimal gradient) {
        this.gradient = gradient;
    }

    public BigDecimal getIntegralLoad() {
        return integralLoad;
    }

    public void setIntegralLoad(BigDecimal integralLoad) {
        this.integralLoad = integralLoad;
    }

    public BigDecimal getPeakSectionLoad() {
        return peakSectionLoad;
    }

    public void setPeakSectionLoad(BigDecimal peakSectionLoad) {
        this.peakSectionLoad = peakSectionLoad;
    }
}
