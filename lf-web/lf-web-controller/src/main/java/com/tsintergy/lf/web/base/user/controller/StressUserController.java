package com.tsintergy.lf.web.base.user.controller;

import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityUserAddHisService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureUserAddDayHisService;
import com.tsintergy.lf.serviceapi.base.load.api.SettingWarnInitService;
import com.tsintergy.lf.serviceapi.base.load.dto.*;
import com.tsintergy.lf.serviceapi.base.load.pojo.SettingWarnInitDO;
import com.tsintergy.lf.web.base.controller.BaseController;
import com.tsintergy.lf.web.base.user.request.SettingWarnInitRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@RequestMapping("/stressUser")
@RestController
@Api(tags = "重点行业运行监测")
public class StressUserController extends BaseController {

    @Autowired
    private LoadCityUserAddHisService loadCityUserAddHisService;

    @Autowired
    private LoadFeatureUserAddDayHisService loadFeatureUserAddDayHisService;

    @Autowired
    private SettingWarnInitService settingWarnInitService;

    @ApiOperation("重点用户激变预警-获取同类型日曲线对比")
    @RequestMapping(value = "/sameDayLoad", method = RequestMethod.GET)
    public BaseResp<LoadUserAddDTO> getSameDayLoad(String cityId, String caliberId, Date date) {
        LoadUserAddDTO userLoadContrast = loadCityUserAddHisService.getUserLoadContrast(cityId, caliberId, date);
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(userLoadContrast);
        return baseResp;
    }

    @ApiOperation("重点用户激变预警-重点用户激变预警")
    @RequestMapping(value = "/userWarn", method = RequestMethod.GET)
    public BaseResp<List<LoadUserFeatureDTO>> getUserWarn(String cityId, String caliberId, Date startDate,Date endDate) {
        List<LoadUserFeatureDTO> loadUserFeatureDTOS = loadFeatureUserAddDayHisService.getUserWarn(cityId, caliberId, startDate, endDate);
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(loadUserFeatureDTOS);
        return baseResp;
    }

    @ApiOperation("重点用户激变预警-获取预警条件")
    @RequestMapping(value = "/warnCondition", method = RequestMethod.GET)
    public BaseResp<List<SettingWarnInitDO>> getWarnCondition(String cityId) {
        List<SettingWarnInitDO> settingWarnInitDOS = settingWarnInitService.findByCityId(cityId);
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(settingWarnInitDOS);
        return baseResp;
    }

    @ApiOperation("重点用户激变预警-保存预警条件")
    @RequestMapping(value = "/saveWarnCondition", method = RequestMethod.POST)
    public BaseResp saveWarnCondition(@RequestBody SettingWarnInitRequest settingWarnInitRequest) {
        settingWarnInitService.saveOrUpdate(settingWarnInitRequest.getSettingWarnInitDOS());
        return BaseResp.succResp();
    }

    @ApiOperation("重点用户特性分析-重点用户总加负荷")
    @RequestMapping(value = "/userAddLoad", method = RequestMethod.GET)
    public BaseResp<List<BigDecimal>> getUserAddLoad(String cityId, String caliberId, Date startDate,Date endDate) {
        List<BigDecimal> load = loadCityUserAddHisService.getLoad(cityId, caliberId, startDate, endDate);
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(load);
        return baseResp;
    }

    @ApiOperation("重点用户特性分析-典型负荷")
    @RequestMapping(value = "/typicalUserAddLoad", method = RequestMethod.GET)
    public BaseResp<LoadUserAddTypicalDTO> getTypicalUserAddLoad(String cityId, String caliberId, Date startDate,Date endDate) {
        LoadUserAddTypicalDTO userTypicalLoad = loadCityUserAddHisService.getUserTypicalLoad(cityId, caliberId, startDate, endDate);
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(userTypicalLoad);
        return baseResp;
    }

    @ApiOperation("重点用户特性分析-数据统计-负荷曲线")
    @RequestMapping(value = "/stat/userAddLoad", method = RequestMethod.GET)
    public BaseResp<List<LoadUserAddAndFeatureDTO>> getStatUserAddLoad(String cityId, String caliberId, Date startDate,Date endDate) {
        List<LoadUserAddAndFeatureDTO> userLoadAndFeature = loadCityUserAddHisService.getUserLoadAndFeature(cityId, caliberId, startDate, endDate);
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(userLoadAndFeature);
        return baseResp;
    }

    @ApiOperation("重点用户特性分析-数据统计-典型负荷曲线")
    @RequestMapping(value = "/stat/userTypicalAddLoad", method = RequestMethod.GET)
    public BaseResp<List<LoadUserAddAndFeatureDTO>> getStatTypicalUserAddLoad(String cityId, String caliberId, Date startDate,Date endDate) {
        List<LoadUserAddAndFeatureDTO> userLoadAndFeature = loadCityUserAddHisService.getTypicalLoadAndFeature(cityId, caliberId, startDate, endDate);
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(userLoadAndFeature);
        return baseResp;
    }
}
