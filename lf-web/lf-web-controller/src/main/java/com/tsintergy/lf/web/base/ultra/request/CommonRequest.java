package com.tsintergy.lf.web.base.ultra.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import java.util.Date;

/**
 * @date: 18-1-19 上午10:22
 * @author: angel
 **/
public class CommonRequest {

    private String cityId;

    private String caliberId;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
//    @NotNull(message = "开始时间不能为空")
    private Date startDate;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
//    @NotNull(message = "结束时间不能为空")
    private Date endDate;

    private Integer timeSpan;

    public Integer getTimeSpan() {
        return timeSpan;
    }

    public void setTimeSpan(Integer timeSpan) {
        this.timeSpan = timeSpan;
    }

    public String getCityId() {
        return cityId;
    }

    public void setCityId(String cityId) {
        this.cityId = cityId;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = DateUtils.string2Date(startDate, DateFormatType.SIMPLE_DATE_FORMAT_STR);
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = DateUtils.string2Date(endDate, DateFormatType.SIMPLE_DATE_FORMAT_STR);
    }

    public String getCaliberId() {
        return caliberId;
    }

    public void setCaliberId(String caliberId) {
        this.caliberId = caliberId;
    }
}
