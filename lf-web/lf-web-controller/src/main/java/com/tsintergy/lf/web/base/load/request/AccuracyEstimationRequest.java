package com.tsintergy.lf.web.base.load.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("精度估计")
public class AccuracyEstimationRequest {

    //单位
    @ApiModelProperty("单位")
    private String cityId;

    //口径
    @ApiModelProperty("口径")
    private String caliberId;
    @ApiModelProperty("开始日期")
    private String startDate;
    @ApiModelProperty("结束日期")
    private String endDate;

    //0日 1月
    @ApiModelProperty("日期类型")
    private String dateType;


}
