package com.tsintergy.lf.web.base.load.response;

import com.tsintergy.lf.serviceapi.base.evalucation.dto.MultipleAccuracyDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

@Data
@ApiModel("日准确率")
public class MultipleAccuracyResp {

    /**
     * 多日准确率信息
     */
    @ApiModelProperty("准确率列表")
    private List<MultipleAccuracyDTO> accuracyList;

    /**
     * 开始日期
     */
    @ApiModelProperty("开始日期")
    private String startDate;

    /**
     * 结束日期
     */
    @ApiModelProperty("结束日期")
    private String endDate;

    /**
     * 类型（1/2/3）
     */
    @ApiModelProperty("类型")
    private String type;
}