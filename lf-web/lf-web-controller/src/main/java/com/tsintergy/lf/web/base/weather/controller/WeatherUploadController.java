package com.tsintergy.lf.web.base.weather.controller;

import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityRpaService;
import com.tsintergy.lf.web.base.controller.BaseController;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2024-12-04
 * @since 1.0.0
 */
@RequestMapping("/rpa")
@RestController
@Api(value = "rpa气象上传" ,tags = "rpa气象上传")
public class WeatherUploadController extends BaseController {

    @Autowired
    WeatherCityRpaService weatherCityRpaService;

    @RequestMapping(value = "/weather/import", method = RequestMethod.POST)
    public BaseResp weatherImport(@RequestParam(value = "file", required = false) MultipartFile file, Integer type)
        throws Exception {
        if (file == null || file.isEmpty()) {
            return BaseResp.failResp("上传文件为空，导入失败");
        }
        if (!file.getOriginalFilename().endsWith(".xlsx")) {
            return BaseResp.failResp("导入文件格式不匹配");
        }
        weatherCityRpaService.weatherUpload(file.getInputStream(), type);
        BaseResp baseResp = BaseResp.succResp();
        return baseResp;
    }
}
