package com.tsintergy.lf.web.base.ultra.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import java.util.Date;

/**
 * @date: 18-1-19 上午10:22
 * @author: angel
 **/
public class CommonDayRequest {

    private String cityId;

    private String caliberId;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
//    @NotNull(message = "结束时间不能为空")
    private Date date;

    private Integer timeSpan;

    public Integer getTimeSpan() {
        return timeSpan;
    }

    public void setTimeSpan(Integer timeSpan) {
        this.timeSpan = timeSpan;
    }

    public String getCityId() {
        return cityId;
    }

    public void setCityId(String cityId) {
        this.cityId = cityId;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = DateUtils.string2Date(date, DateFormatType.SIMPLE_DATE_FORMAT_STR);
    }

    public String getCaliberId() {
        return caliberId;
    }

    public void setCaliberId(String caliberId) {
        this.caliberId = caliberId;
    }
}
