/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司 Author:  wangfeng Date:  2019/12/6 2:05 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.ultra.response;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * 超短期多点多批次预测准确率对象
 *
 * <AUTHOR>
 * @create 2022/10/21
 * @since 1.0.0
 */
@Data
public class MultipointShortAccuracyResponse implements Serializable {

    @ApiModelProperty("准确率list")
    private List<MultipointShortData> shortForecastAccuracy;

    @ApiModelProperty("时间列表")
    private List<String> timeList;


}