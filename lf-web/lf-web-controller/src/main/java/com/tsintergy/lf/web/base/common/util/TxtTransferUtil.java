package com.tsintergy.lf.web.base.common.util;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import org.springframework.web.multipart.MultipartFile;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2023-07-06
 * @since 1.0.0
 */
public class TxtTransferUtil {

    public static List<String> convertToBigDecimal(MultipartFile forecastFile) throws IOException {
        String originalFilename = forecastFile.getOriginalFilename();
        String prefix = originalFilename.substring(originalFilename.lastIndexOf("."));
        File file = File.createTempFile(originalFilename, prefix);
        forecastFile.transferTo(file);
        List<String> collect = new ArrayList<>(16);
        try (BufferedReader reader = new BufferedReader(new FileReader(file))) {
            String line;
            while ((line = reader.readLine()) != null) {
                String substring = line.substring(0,1);
                if ("2".equals(substring)) {
                    collect.add(line);
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return collect;
    }

    public static String convertTo96Load(MultipartFile forecastFile) throws IOException {
        String originalFilename = forecastFile.getOriginalFilename();
        String prefix = originalFilename.substring(originalFilename.lastIndexOf("."));
        File file = File.createTempFile(originalFilename, prefix);
        forecastFile.transferTo(file);
        List<String> collect = new ArrayList<>(16);
        StringBuilder htmlBuilder = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new FileReader(file))) {
            String line;
            while ((line = reader.readLine()) != null) {
                htmlBuilder.append(line);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        String s = htmlBuilder.toString();
        return htmlBuilder.toString();
    }
}
