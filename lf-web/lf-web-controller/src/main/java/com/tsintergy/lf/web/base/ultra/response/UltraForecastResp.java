package com.tsintergy.lf.web.base.ultra.response;

import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import com.tsintergy.lf.serviceapi.base.ultra.dto.UltraForecastDTO;
import com.tsintergy.lf.serviceapi.base.ultra.dto.WeatherNameDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;

/**
 * @date: 2/27/18 5:23 PM
 * @author: angel
 **/
@ApiModel
public class UltraForecastResp {

    @ApiModelProperty(value = "基准日")
    private List<BigDecimal> base;

    /**
     * 最终上报的曲线
     */
    @BigdecimalJsonFormat(scale = 0)
    @ApiModelProperty(value = "最终上报的曲线")
    private List<BigDecimal> reportLoad;

    /**
     * 修正的曲线
     */
    @BigdecimalJsonFormat(scale = 0)
    @ApiModelProperty(value = "修正的曲线")
    private List<BigDecimal> modifyLoad;

    /**
     * 预测数据
     */
    @ApiModelProperty(value = "预测数据")
    UltraForecastDTO forecast;

    /**
     * 置信上限
     */
    @ApiModelProperty(value = "置信上限")
    List<BigDecimal> max;

    /**
     * 置信下限
     */
    @ApiModelProperty(value = "置信下限")
    List<BigDecimal> min;

    /**
     * 相似日
     */
    @ApiModelProperty(value = "相似日")
    List<BigDecimal> reference;


    /**
     * 气象数据
     */
    @ApiModelProperty(value = "气象数据")
    private List<WeatherNameDTO> weatherNameDTOS;


    public List<BigDecimal> getModifyLoad() {
        return modifyLoad;
    }

    public void setModifyLoad(List<BigDecimal> modifyLoad) {
        this.modifyLoad = modifyLoad;
    }

    public UltraForecastDTO getForecast() {
        return forecast;
    }

    public void setForecast(UltraForecastDTO forecast) {
        this.forecast = forecast;
    }

    public List<BigDecimal> getMax() {
        return max;
    }

    public void setMax(List<BigDecimal> max) {
        this.max = max;
    }

    public List<BigDecimal> getMin() {
        return min;
    }

    public void setMin(List<BigDecimal> min) {
        this.min = min;
    }

    public List<BigDecimal> getReference() {
        return reference;
    }

    public void setReference(List<BigDecimal> reference) {
        this.reference = reference;
    }


    public List<BigDecimal> getBase() {
        return base;
    }

    public void setBase(List<BigDecimal> base) {
        this.base = base;
    }

    public List<BigDecimal> getReportLoad() {
        return reportLoad;
    }

    public void setReportLoad(List<BigDecimal> reportLoad) {
        this.reportLoad = reportLoad;
    }

    public List<WeatherNameDTO> getWeatherNameDTOS() {
        return weatherNameDTOS;
    }

    public void setWeatherNameDTOS(List<WeatherNameDTO> weatherNameDTOS) {
        this.weatherNameDTOS = weatherNameDTOS;
    }
}
