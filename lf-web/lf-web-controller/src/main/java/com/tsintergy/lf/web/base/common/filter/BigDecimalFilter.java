/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司 Author:  wangfeng Date:  2019/9/25 4:38 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.common.filter;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ValueFilter;
import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Description:  <br>
 * 对BigDecimal类型数据做拦截
 * 统一处理
 *
 * <AUTHOR>
 * @create 2019/9/25
 * @since 1.0.0
 */
public class BigDecimalFilter implements ValueFilter {

    private static final Logger logger = LoggerFactory.getLogger(BigDecimalFilter.class);

    /**
     * @param object 对象
     * @param name   对象的字段的名称
     * @param value  对象的字段的值
     */
    @Override
    public Object process(Object object, String name, Object value) {
        if (null != value && value instanceof Collection) {
            List<Object> list = new ArrayList<>();
            ((Collection) value).forEach(o -> {
                if (o instanceof BigDecimal) {
                    o = getCaleValue(object, name, o);
                }
                list.add(o);
            });
            return list;
        }
        if (null != value && value instanceof BigDecimal) {
            return getCaleValue(object, name, value);
        }
        return value;
    }

    private BigDecimal getCaleValue(Object object, String name, Object value) {
        BigDecimal number = (BigDecimal) value;
        String fieldName = name;
        name = name.toLowerCase();

        try {
            Field field = getDeclaredField(object.getClass(), fieldName);
            JSONField jsonField = field.getAnnotation(JSONField.class);
            if (jsonField != null) {
                String formate = jsonField.format();
                return number.setScale(Integer.valueOf(formate.split("\\.")[1]), BigDecimal.ROUND_HALF_DOWN);
            }
            BigdecimalJsonFormat bigdecimalJsonFormat = field.getAnnotation(BigdecimalJsonFormat.class);
            if (bigdecimalJsonFormat != null) {
                int scale = bigdecimalJsonFormat.scale();
                int roundingMode = bigdecimalJsonFormat.roundingMode();
                int percent = bigdecimalJsonFormat.percentConvert();
                int divideConvert = bigdecimalJsonFormat.divideConvert();
                return number.multiply(new BigDecimal(percent)).divide(new BigDecimal(divideConvert)).setScale(scale, roundingMode);
            }
            if (name.contains("percent") || name.equals("energyavg") || name.contains("accuracy") || name
                .contains("pass") || name.contains("discrete")
                || name.contains("dispersion") || name.equals("algorithm") || name.contains("link") || name
                .contains("basics") || name.contains("recovery")) {
                number = number.multiply(new BigDecimal(100)).setScale(8, BigDecimal.ROUND_HALF_DOWN);
            }
        } catch (NoSuchFieldException e) {
            logger.warn("{}在对象中未声明", fieldName);
        }
        return number.setScale(2, BigDecimal.ROUND_HALF_DOWN);
    }


    private Field getDeclaredField(Class clazz, String name) throws NoSuchFieldException {
        Field field;
        Class temp = clazz;
        while (temp != null) {
            try {
                field = temp.getDeclaredField(name);
                return field;
            } catch (NoSuchFieldException e) {
            }
            temp = temp.getSuperclass();
        }
        throw new NoSuchFieldException(name);
    }

}
