/**
 * Copyright(C),2015‐2021,北京清能互联科技有限公司
 */
package com.tsintergy.lf.web.base.common.handler;

import com.tsieframework.cloud.security.serviceapi.businesslog.pojo.TsieOperateLogVO;
import com.tsieframework.cloud.security.serviceapi.system.api.SecurityService;
import com.tsieframework.cloud.security.serviceapi.system.pojo.TsieUserVO;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.dao.hibernate.QueryOp;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2021/12/28 21:20
 * @Version: 1.0.0
 */
@Slf4j
public class DeleteUserProceedingJoinPointLogHandler extends BaseProceedingJoinPointLogHandler {

    private SecurityService securityService;

    public DeleteUserProceedingJoinPointLogHandler(
        SecurityService securityService) {
        this.securityService = securityService;
    }

    @Override
    List<String> initOperateTitle() {
        return new ArrayList<String>() {
            {
                add("删除用户");
            }
        };
    }


    @Override
    public void proceedingJoinPointLog(ProceedingJoinPoint proceedingJoinPoint, TsieOperateLogVO operateLog) {
        if (isMaching(proceedingJoinPoint, operateLog)) {
            String userId = (String) proceedingJoinPoint.getArgs()[0];
            DBQueryParam param = DBQueryParamBuilder.create().where(QueryOp.StringEqualTo, "id", userId).queryDataOnly()
                .build();
            try {
                TsieUserVO userVO = securityService.queryTsieUserVo(param);
                StringBuilder description = new StringBuilder(operateLog.getTitle())
                    .append(",")
                    .append("用户名为")
                    .append(userVO.getUsername());
                operateLog.setDescription(description.toString());
            } catch (Exception e) {
                log.error("记录删除用户信息失败", e);
            }
        }
    }


}