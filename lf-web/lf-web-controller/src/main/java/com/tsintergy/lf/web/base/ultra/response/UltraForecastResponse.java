/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司 Author:  wangfeng Date:  2019/12/6 2:05 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.ultra.response;

import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * Description:  <br> 超短期数据查询页面对象；
 *
 * <AUTHOR>
 * @create 2022/1/6
 * @since 1.0.0
 */
@Data
@ApiModel(description = "超短期数据查询页面对象")
public class UltraForecastResponse implements Serializable {

    @ApiModelProperty("实际负荷")
    @BigdecimalJsonFormat(scale = 0)
    private List<BigDecimal> real;

    @ApiModelProperty("超短期数据")
    @BigdecimalJsonFormat(scale = 0)
    private List<BigDecimal> ultraLine;

    @ApiModelProperty("时间列表")
    private List<String> timeList;

    @ApiModelProperty("短期预测")
    @BigdecimalJsonFormat(scale = 0)
    private List<BigDecimal> fc;

    @ApiModelProperty("最新数据生成时间")
    private String time;

}