package com.tsintergy.lf.web.base.evalucation.request;


import com.tsintergy.lf.web.base.common.request.CommonRequest;
import io.swagger.annotations.ApiModelProperty;

/**
 * 准确率查询类
 * @date: 18-2-6 上午10:22
 * @author: taojingui
 **/
public class AccuracyRequest extends CommonRequest {

    /**
     * 开始时段
     */
    @ApiModelProperty(value = "开始时段")
    public String startPeriod;

    /**
     * 是否包含节假日
     */
    @ApiModelProperty(value = "是否包含节假日")
    private String isHoliday;

    /**
     * 	结束时段
     */
    @ApiModelProperty(value = "结束时段")
    public String endPeriod;

    /**
     * 准确率类型
     */
    @ApiModelProperty(value = "准确率类型")
    public String accuracyType;


    @ApiModelProperty(value = "批次id")
    public String batchIds;


    @ApiModelProperty(value = "日前天数")
    public Integer days;

    public String getBatchIds() {
        return batchIds;
    }

    public void setBatchIds(String batchIds) {
        this.batchIds = batchIds;
    }

    public Integer getDays() {
        return days;
    }

    public void setDays(Integer days) {
        this.days = days;
    }

    public String getStartPeriod() {
        return startPeriod;
    }

    public void setStartPeriod(String startPeriod) {
        this.startPeriod = startPeriod;
    }

    public String getEndPeriod() {
        return endPeriod;
    }

    public void setEndPeriod(String endPeriod) {
        this.endPeriod = endPeriod;
    }

    public String getIsHoliday() {
        return isHoliday;
    }

    public void setIsHoliday(String isHoliday) {
        this.isHoliday = isHoliday;
    }

    public String getAccuracyType() {
        return accuracyType;
    }

    public void setAccuracyType(String accuracyType) {
        this.accuracyType = accuracyType;
    }
}
