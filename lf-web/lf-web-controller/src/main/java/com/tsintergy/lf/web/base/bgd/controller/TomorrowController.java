package com.tsintergy.lf.web.base.bgd.controller;

import com.github.liaochong.myexcel.utils.StringUtil;
import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.core.constants.Constants;

import com.tsintergy.lf.serviceapi.base.bgd.api.TomorrowPageService;
import com.tsintergy.lf.serviceapi.base.bgd.dto.LongInfoDTO;
import com.tsintergy.lf.web.base.controller.CommonBaseController;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * <AUTHOR>
 * 预测总览-次日
 */
@RequestMapping("/tomorrow")
@RestController
public class TomorrowController extends CommonBaseController {

    @Autowired
    private TomorrowPageService tomorrowPageService;

    @ApiOperation("中长期概要")
    @GetMapping(value = "/longInfo")
    public BaseResp<LongInfoDTO> findAreaFc(String cityId, Date date, Integer dateType, String season) {
        BaseResp baseResp = BaseResp.succResp();
        if (StringUtil.isBlank(cityId)){
            cityId = String.valueOf(Constants.PROVINCE_TYPE);
        }
        LongInfoDTO longInfoDTO = null;
        try {
            longInfoDTO = tomorrowPageService.getLongInfoDTO(cityId,date,dateType,season);
            baseResp.setData(longInfoDTO);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (longInfoDTO == null) {
            return new BaseResp("T706");
        }
        return baseResp;
    }
}
