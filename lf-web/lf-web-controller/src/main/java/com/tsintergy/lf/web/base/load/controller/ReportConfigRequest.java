package com.tsintergy.lf.web.base.load.controller;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2023-05-07
 * @since 1.0.0
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class ReportConfigRequest implements Serializable {
    @ApiModelProperty(value = "首次上报设置时间")
    private String startDate;
    @ApiModelProperty(value = "最终上报截止时间")
    private String endDate;
    @ApiModelProperty(value = "首次上报设置城市ID")
    private List<String> firstCityIds;
    @ApiModelProperty(value = "最终上报设置城市ID")
    private List<String> lastCityIds;
    @ApiModelProperty(value = "首次上报地市或者全网")
    private String type1;
    @ApiModelProperty(value = "最终上报地市或者全网")
    private String type2;

    public String getType1() {
        return type1;
    }

    public void setType1(String type1) {
        this.type1 = type1;
    }

    public String getType2() {
        return type2;
    }

    public void setType2(String type2) {
        this.type2 = type2;
    }

    public List<String> getFirstCityIds() {
        return firstCityIds;
    }

    public void setFirstCityIds(List<String> firstCityIds) {
        this.firstCityIds = firstCityIds;
    }

    public List<String> getLastCityIds() {
        return lastCityIds;
    }

    public void setLastCityIds(List<String> lastCityIds) {
        this.lastCityIds = lastCityIds;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }
}
