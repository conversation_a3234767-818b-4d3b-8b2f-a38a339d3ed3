package com.tsintergy.lf.web.base.report.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;

@ApiModel("累计准确率更新")
public class AccuracyLoadRequest implements Serializable {

    @ApiModelProperty("开始时间")
    String start;

    @ApiModelProperty("结束时间")
    String end;

    @ApiModelProperty("城市列表")
    List<String> cityIds;

    public List<String> getCityIds() {
        return cityIds;
    }

    public void setCityIds(List<String> cityIds) {
        this.cityIds = cityIds;
    }

    public String getStart() {
        return start;
    }

    public void setStart(String start) {
        this.start = start;
    }

    public String getEnd() {
        return end;
    }

    public void setEnd(String end) {
        this.end = end;
    }
}
