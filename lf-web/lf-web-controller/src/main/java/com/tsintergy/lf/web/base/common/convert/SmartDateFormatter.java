package com.tsintergy.lf.web.base.common.convert;

import java.util.Date;
import java.util.Locale;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.format.Formatter;

/**
 * <p>
 *     智能日期格式化处理，默认支持格式为："yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd",  "yyyy-MM", 配置方式：
 *     <pre>{@code
 *          @Configuration
 *          public class WebMvcConfig implements WebMvcConfigurer {
 *              @Override
 *              public void addFormatters(FormatterRegistry registry) {
 *                  registry.addFormatter(new SmartDateFormatter());
 *              }
 *          }
 *     }
 *     </pre>
 * </p>
 *
 * <AUTHOR>
 * @date 2019/2/15 15:27
 */
public class SmartDateFormatter implements Formatter<Date> {

    private static final Logger logger = LoggerFactory.getLogger(SmartDateFormatter.class);

    private static final String[] DEFAULT_FORMATTERS = new String[]{
            "yyyy-MM-dd HH:mm:ss",
            "yyyy-MM-dd",
            "yyyy-MM",
    };

    String[] formatters;

    public SmartDateFormatter(){
        this(null);
    }

    public SmartDateFormatter(String[] formatters){

        if(formatters == null || formatters.length == 0){
            logger.info("使用默认日期格式");
            this.formatters = DEFAULT_FORMATTERS;
        }else{
            this.formatters = formatters;
        }
    }

    @Override
    public Date parse(String text, Locale locale) throws IllegalArgumentException {
        if (StringUtils.isEmpty(text)) {
            return null;
        }

        Exception ex = null;
        for (String formatter : formatters) {
            try {
                DateTime dateTime = DateTime.parse(text, DateTimeFormat.forPattern(formatter));
                return dateTime.toDate();
            } catch (Exception e) {
                ex = e;
            }
        }

        throw new IllegalArgumentException(text, ex);
    }

    @Override
    public String print(Date date, Locale locale) {
        return new DateTime(date).toString(DEFAULT_FORMATTERS[0]);
    }
}