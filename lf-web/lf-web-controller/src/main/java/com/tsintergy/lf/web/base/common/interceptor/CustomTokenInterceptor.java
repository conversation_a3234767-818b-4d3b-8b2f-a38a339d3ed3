/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司
 * Author:  wangchen
 * Date:  2019/8/29 13:10
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.common.interceptor;

import com.tsieframework.cloud.security.serviceapi.system.bean.TokenBean;
import com.tsieframework.cloud.security.web.common.security.UserTokenHelper;
import com.tsieframework.cloud.security.web.common.security.authz.Authorizer;
import com.tsieframework.cloud.security.web.common.security.authz.AuthorizingRealm;
import com.tsieframework.cloud.security.web.servlet.TokenInterceptor;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.exception.TsieExceptionUtils;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2019/8/29 
 * @since 1.0.0
 */
public class CustomTokenInterceptor extends TokenInterceptor{

    private static final Logger logger = LogManager.getLogger(CustomTokenInterceptor.class);

    private Authorizer authorizer = new AuthorizingRealm();

    @Override
    public boolean preHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o) {
        String token;
        if (logger.isDebugEnabled()) {
            token = UserTokenHelper.getIPAddress();
            logger.debug("客户ip：" + token);
        }

        try {
            token = this.tokenManager.getToken();
            String username = this.tokenManager.getLoginUser().getUsername();
            String tokenBeanCacheKey = this.tokenManager.getTokenBeanCacheKey(username);
            TokenBean tokenBean = this.tokenManager.getTokenBeanFromCache(username);
            if (null == tokenBean) {
                if (logger.isDebugEnabled()) {
                    logger.debug("2. 缓存找不到 redisKey = " + tokenBeanCacheKey);
                }

                throw TsieExceptionUtils.newBusinessException("T000", new String[0]);
            } else if (!token.equals(tokenBean.getToken())) {
                if (logger.isDebugEnabled()) {
                    logger.debug("3. cookie的token与redis的token不一致, cookie token = " + token + ", redis token = " + tokenBean.getToken());
                }

                throw TsieExceptionUtils.newBusinessException("T000", new String[0]);
            } else {
                 this.tokenManager.refreshTokenBeanInCache(tokenBeanCacheKey);
               /* String reqPath = this.getReqPath(httpServletRequest);
                if (!this.authorizer.isPermitted(tokenBean, reqPath, httpServletRequest.getMethod())) {
                    if (logger.isDebugEnabled()) {
                        logger.debug("4. 用户 username=" + username + ", 没有权限访问 httpServletRequest.getRequestURI() = " + httpServletRequest.getRequestURI() + ", reqPath = " + reqPath);
                        logger.debug("5. 用户的权限为: " + JSONObject.toJSONString(tokenBean, new SerializerFeature[]{SerializerFeature.PrettyFormat}));
                    }

                    throw TsieExceptionUtils.newBusinessException("T813", new String[0]);
                } else {
                    return true;
                }*/
                return true;
            }
        } catch (BusinessException var9) {
            throw var9;
        } catch (Exception var10) {
            throw TsieExceptionUtils.newBusinessException("T100", var10, new String[0]);
        }
    }

}
