package com.tsintergy.lf.web.base.forecast.request;

import com.tsintergy.lf.serviceapi.base.forecast.dto.LoadCityFcDTO;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;

/**
 * Description: 十日预测
 *
 * <AUTHOR>
 * @create 2023-03-07
 * @since 1.0.0
 */
public class TenDayExistRequest {

    @ApiModelProperty(value = "预测数据dto对象")
    private List<LoadCityFcDTO> loadCityFcDTOS;

    @ApiModelProperty(value = "算法id")
    private List<String> ids;

    public List<LoadCityFcDTO> getLoadCityFcDTOS() {
        return loadCityFcDTOS;
    }

    public void setLoadCityFcDTOS(List<LoadCityFcDTO> loadCityFcDTOS) {
        this.loadCityFcDTOS = loadCityFcDTOS;
    }

    public List<String> getIds() {
        return ids;
    }

    public void setIds(List<String> ids) {
        this.ids = ids;
    }
}
