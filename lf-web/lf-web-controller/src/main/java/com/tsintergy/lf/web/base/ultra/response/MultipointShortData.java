/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司 Author:  wangfeng Date:  2019/12/6 2:05 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.ultra.response;

import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * Description:  <br> 超短期多点多批次预测曲线对象
 *
 * <AUTHOR>
 * @create 2019/12/6
 * @since 1.0.0
 */
@Data
public class MultipointShortData implements Serializable {

    @ApiModelProperty("分钟预测；以5分钟间隔示例：1,3,6...对应5分，15分，30分")
    private Integer multipointType;

    @ApiModelProperty("对应分钟的超短期数据")
    @BigdecimalJsonFormat(scale = 0)
    private List<BigDecimal> shortForecast;

}