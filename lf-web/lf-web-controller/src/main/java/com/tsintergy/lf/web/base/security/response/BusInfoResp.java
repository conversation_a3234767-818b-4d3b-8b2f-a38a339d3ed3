/**
 * Copyright (C), 2015‐2020, 北京清能互联科技有限公司
 * Author:  wang<PERSON>
 * Date:  2020/11/12 10:38
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.security.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2020/11/12 
 * @since 2.0.0
 */
@Data
@ApiModel
public class BusInfoResp  implements Serializable {


    @ApiModelProperty(value = "名称",example = "母线")
    private  String name;

    @ApiModelProperty(value = "路径",example = "http://127.0.0.1:8080/cas/login?service=http://127.0.0.1:8082/buslf/web/logincontroller/choose")
    private  String path;

}