/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司
 * Author:  ThinkPad
 * Date:  2019/4/15 11:20
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.ultra.request;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;

/**
 * Description: 添加参照曲线 <br>
 *
 * <AUTHOR>
 * @create 2019/4/15
 * @since 1.0.0
 */
public class UltraReferenceRequest implements Serializable {

    /**
     * 城市id
     */
    @ApiModelProperty(value = "城市id")
    private String cityId;


    /**
     * 口径id
     */
    @ApiModelProperty(value = "口径id")
    private String caliberId;

    @ApiModelProperty(value = "算法id")
    private String algorithmId;

    /**
     * 气象类型
     */
    @ApiModelProperty(value = "气象类型",example = "4")
    private Integer weatherType;

    /**
     * 参照日期
     */
    @ApiModelProperty(value = "参照日期")
    private Date referenceDate;

    /**
     * 时刻点；5 or 15
     */
    @ApiModelProperty(value = "时刻点；5 or 15")
    private Integer timeSpan;


    public String getCityId() {
        return cityId;
    }

    public void setCityId(String cityId) {
        this.cityId = cityId;
    }

    public String getCaliberId() {
        return caliberId;
    }

    public void setCaliberId(String caliberId) {
        this.caliberId = caliberId;
    }

    public Integer getWeatherType() {
        return weatherType;
    }

    public void setWeatherType(Integer weatherType) {
        this.weatherType = weatherType;
    }

    public Date getReferenceDate() {
        return referenceDate;
    }

    public void setReferenceDate(Date referenceDate) {
        this.referenceDate = referenceDate;
    }


    public Integer getTimeSpan() {
        return timeSpan;
    }

    public void setTimeSpan(Integer timeSpan) {
        this.timeSpan = timeSpan;
    }
}