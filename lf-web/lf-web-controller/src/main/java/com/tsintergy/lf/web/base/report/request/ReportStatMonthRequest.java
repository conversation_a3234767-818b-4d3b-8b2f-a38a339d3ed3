/**
 * Copyright (c), 2015-2021, 北京清能互联科技有限公司
 * Author: zhangjy
 * Date: 2021/1/18 15:08
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.report.request;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**  
 * Description: 上报率统计 <br> 
 * 
 * <AUTHOR>  
 * @create 2020/11/10  
 * @since 1.0.0  
 */
public class ReportStatMonthRequest implements Serializable {

    @ApiModelProperty(value = "城市集合")
    private List<String> cityIds;

    @ApiModelProperty(value = "日期")
    private String date;

    @ApiModelProperty(value = "口径id")
    private String caliberId;


    public List<String> getCityIds() {
        return cityIds;
    }

    public void setCityIds(List<String> cityIds) {
        this.cityIds = cityIds;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getCaliberId() {
        return caliberId;
    }

    public void setCaliberId(String caliberId) {
        this.caliberId = caliberId;
    }
}