package com.tsintergy.lf.web.base.bgd.controller;

import com.alibaba.excel.util.CollectionUtils;
import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.bgd.api.SeasonForecastModelService;
import com.tsintergy.lf.serviceapi.base.bgd.api.YearForecastService;
import com.tsintergy.lf.serviceapi.base.bgd.dto.MonthWeatherResultDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.MonthWeatherSettingDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.SeasonReportDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.YearLoadFeatureDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.YearWeatherFeatureDTO;
import com.tsintergy.lf.serviceapi.base.system.api.ReportSystemService;
import com.tsintergy.lf.serviceapi.base.system.pojo.ReportSystemInitDO;
import com.tsintergy.lf.web.base.common.util.ExcelUtil;
import io.swagger.annotations.ApiOperation;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/year")
public class YearForecastController {


    @Autowired
    private YearForecastService yearForecastService;

    @Autowired
    ReportSystemService reportSystemService;

    @Autowired
    SeasonForecastModelService seasonForecastModelService;

    @ApiOperation("获取年度预测")
    @GetMapping(value = "/getYearLoadFc")
    public BaseResp<List<YearLoadFeatureDTO>> getYearLoadFc(String cityId, String caliberId, String year,
        String algorithmId, String startStr, String endStr) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<YearLoadFeatureDTO> yearLoadFeatureDTOS = yearForecastService.getYearLoadFc(cityId, getCaliberId(cityId),
            year, algorithmId, startStr, endStr);
        baseResp.setData(yearLoadFeatureDTOS);
        return baseResp;
    }

    @ApiOperation("年度预测保存")
    @PostMapping(value = "/save")
    public BaseResp save(@RequestBody List<SeasonReportDTO> monthReportDTOS) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        yearForecastService.save(monthReportDTOS);
        return baseResp;
    }

    @ApiOperation("获取年度负荷对比")
    @GetMapping(value = "/getYearLoadComparison")
    public BaseResp<List<YearLoadFeatureDTO>> getYearLoadComparison(String cityId, String caliberId, String year,
        String algorithmId, String startStr, String endStr) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<YearLoadFeatureDTO> yearLoadFeatureDTOS = yearForecastService.getYearLoadComparison(cityId,
            getCaliberId(cityId), year, algorithmId, startStr, endStr);
        baseResp.setData(yearLoadFeatureDTOS);
        return baseResp;
    }

    @ApiOperation("获取年度气象对比")
    @GetMapping(value = "/getYearWeatherComparison")
    public BaseResp<List<YearWeatherFeatureDTO>> getYearWeatherComparison(String cityId, String year, String startStr,
        String endStr) {
        BaseResp baseResp = BaseResp.succResp();
        List<YearWeatherFeatureDTO> yearLoadFeatureDTOS = yearForecastService.getYearWeatherComparison(cityId, year,
            startStr, endStr);
        baseResp.setData(yearLoadFeatureDTOS);
        return baseResp;
    }

    @ApiOperation("获取气象设置")
    @PostMapping(value = "/getWeatherSetting")
    public BaseResp<List<MonthWeatherResultDTO>> getWeatherSetting(
        @RequestBody MonthWeatherSettingDTO monthWeatherSettingDTO) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<MonthWeatherResultDTO> weatherSetting = yearForecastService.getWeatherSetting(
            monthWeatherSettingDTO);
        if (CollectionUtils.isEmpty(weatherSetting)) {
            return new BaseResp("T200");
        }
        baseResp.setData(weatherSetting);
        return baseResp;
    }

    @ApiOperation("保存气象设置")
    @PostMapping(value = "/saveWeatherSetting")
    public BaseResp<Void> saveWeatherSetting(@RequestBody List<MonthWeatherResultDTO> monthWeatherResultDTOS)
        throws Exception {
        yearForecastService.saveWeatherSetting(monthWeatherResultDTOS);
        return BaseResp.succResp();
    }

    @ApiOperation("年度导入上报结果")
    @RequestMapping(value = "/import", method = RequestMethod.POST)
    public BaseResp importMonthReport(
        @RequestParam(value = "reportFile", required = false) MultipartFile forecastFile, String cityId)
        throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        Date nowDate = new Date();
        List<SeasonReportDTO> monthReportDTOS = new ArrayList<>();
        List<SeasonReportDTO> result = new ArrayList<>();
        String reportStatus = null;
        try {
            InputStream io = forecastFile.getInputStream();
            List<Map<Integer, Object>> list = ExcelUtil.resolve(io, 1, true);
            List<ReportSystemInitDO> month_report_time = reportSystemService.findReportSystemConfig(cityId,
                "year_report_time", null);
            if (Boolean.TRUE.equals(month_report_time.get(0).getStatus())) {
                String value = month_report_time.get(0).getValue();
                reportStatus = value;
            }
            if (list != null) {
                list.subList(0, 3).clear();
                for (Map<Integer, Object> integerObjectMap : list) {
                    SeasonReportDTO monthReportDTO = new SeasonReportDTO();
                    monthReportDTO.setDate(integerObjectMap.get(0).toString().substring(0, 7) + "-01");
                    monthReportDTO.setCityId(cityId);
                    monthReportDTO.setSeason("3");
                    monthReportDTO.setMaxLoad(new BigDecimal(integerObjectMap.get(1).toString()));
                    monthReportDTO.setMinLoad(new BigDecimal(integerObjectMap.get(2).toString()));
                    monthReportDTO.setNoonTimeLoad(new BigDecimal(integerObjectMap.get(3).toString()));
                    monthReportDTO.setEnergy(new BigDecimal(integerObjectMap.get(4).toString()));
                    monthReportDTO.setExtremeMaxLoad(new BigDecimal(integerObjectMap.get(5).toString()));
                    monthReportDTO.setExtremeMinLoad(new BigDecimal(integerObjectMap.get(6).toString()));
                    monthReportDTO.setExtremeNoonTimeLoad(new BigDecimal(integerObjectMap.get(7).toString()));
                    monthReportDTO.setExtremeEnergy(new BigDecimal(integerObjectMap.get(8).toString()));
                    monthReportDTOS.add(monthReportDTO);
                }
            }
        } catch (Exception e) {
            baseResp.setRetCode("T706");
            baseResp.setRetMsg("导入失败！");
            throw TsieExceptionUtils.newBusinessException("需要导入的数据有空值");
        }
        if (reportStatus != null) {
            Date date = DateUtil.getDate(reportStatus, "yyyy-MM-dd");
            // 可以上报
            for (SeasonReportDTO monthReportDTO : monthReportDTOS) {
                if (date.compareTo(nowDate) > 0) {
                    // 可以导入
                    result.add(monthReportDTO);
                }
            }
            seasonForecastModelService.report(result);
            if (result.size() == monthReportDTOS.size()) {
                baseResp.setRetCode("T200");
                baseResp.setRetMsg("全部导入成功");
            } else if (result.size() == 0) {
                baseResp.setRetCode("T200");
                baseResp.setRetMsg("全部导入失败。");
            } else {
                baseResp.setRetCode("T200");
                baseResp.setRetMsg("部分导入失败，存在超过上报时间记录。");
            }
        } else {
            seasonForecastModelService.report(monthReportDTOS);
            baseResp.setRetCode("T200");
            baseResp.setRetMsg("全部导入成功");
        }
        return baseResp;
    }

    private String getCaliberId(String cityId){
        if (Constants.PROVINCE_TYPE == Integer.parseInt(cityId)){
            return Constants.CALIBER_ID_BG_QW;
        }else{
            return Constants.CALIBER_ID_BG_DS;
        }
    }
}
