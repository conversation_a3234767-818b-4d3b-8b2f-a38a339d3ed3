package com.tsintergy.lf.web.base.forecast.request;

import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2023-05-05
 * @since 1.0.0
 */
@Data
public class SimilarFeatureRequest {

    /**
     * 城市id
     */
    private String cityId;

    /**
     * 目标日期
     */
    private Date targetDate;

    /**
     * 开始时段
     */
    private String startTime;

    /**
     * 结束时段
     */
    private String endTime;

    /**
     * 搜索范围 开始日期
     */
    private Date startDate;

    /**
     * 搜索范围 结束日期
     */
    private Date endDate;

    /**
     * 日期类型 工作日 0 否 1 是
     */
    private List<String> dateType;

    /**
     * 查找条件设置 1 气象变化  2 负荷变化  3 自定义
     */
    private String condition;

    /**
     *  1 气象指标  2 气象曲线
     */
    private String method;

    /**
     * 气象指标
     */
    private List<String> weatherFeatureList;

    /**
     * 气象范围
     */
    private String weatherRange;

    /**
     * 气象范围开始
     */
    private String rangeStart;

    /**
     * 气象范围结束
     */
    private String rangeEnd;

    /**
     * 连续变化天数
     */
    private String changeDays;

    /**
     * 气象曲线
     */
    private List<String> weatherCurveList;

    /**
     * 最大结果数量
     */
    private String maxResultCount;

    /**
     * 排除相似度小于
     */
    private String exclude;

    /**
     * 负荷特性指标
     */
    private List<String> loadList;

    /**
     * 已选日期
     */
    private List<Date> chooseDateList;

    /**
     * 右侧复选框选择相似日日期
     */
    private List<Date> similarDays;

}
