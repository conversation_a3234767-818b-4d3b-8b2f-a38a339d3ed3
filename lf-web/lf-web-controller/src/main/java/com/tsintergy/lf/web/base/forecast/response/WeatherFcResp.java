/**
 *   
 *  Copyright (C), 2015‐2020, 北京清能互联科技有限公司  
 *  Author:  EDZ  
 *  Date:  2020/10/13 10:07  
 *  History:  
 *  <author> <time> <version> <desc> 
 */
package com.tsintergy.lf.web.base.forecast.response;

import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherDTO;
import com.tsintergy.lf.serviceapi.base.weather.dto.WeatherFeatureDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**  
 * Description:  <br> 
 * 
 * <AUTHOR>  
 * @create 2020/10/13  
 * @since 1.0.0  
 */
@Data
public class WeatherFcResp implements Serializable {

    @ApiModelProperty(value = "最新预测气象数据")
    private List<WeatherDTO> newWeatherFcList;

    @ApiModelProperty(value = "算法使用预测气象数据")
    private List<WeatherDTO> algorithmWeatherFcList;

    @ApiModelProperty(value = "气象特性")
    private List<WeatherFeatureDTO> weatherFeature;

}