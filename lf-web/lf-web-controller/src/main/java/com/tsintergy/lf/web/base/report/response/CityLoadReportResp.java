package com.tsintergy.lf.web.base.report.response;

import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class CityLoadReportResp {

    /**
     * 城市id
     */
    private String cityId;

    /**
     * 城市名
     */
    private String cityName;

    /**
     * 昨日最大负荷
     */
    private BigDecimal huanbiMaxLoad;

    /**
     * 最大负荷
     */
    private BigDecimal maxLoad;

    /**
     * 偏差量
     */
    private BigDecimal maxLoadDiff;

    /**
     * 偏差占比
     */
    @BigdecimalJsonFormat(percentConvert =100)
    private BigDecimal maxLoadDiffRate;
}
