package com.tsintergy.lf.web.base.ultra.controller;

import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.serviceapi.base.ultra.api.UltraLoadAccuracyDayService;
import com.tsintergy.lf.serviceapi.base.ultra.dto.AccuracyLowDetailsDayDTO;
import com.tsintergy.lf.serviceapi.base.ultra.dto.AccuracyUltraData;
import com.tsintergy.lf.serviceapi.base.ultra.dto.AccuracyUltraDayDTO;
import com.tsintergy.lf.serviceapi.base.ultra.dto.MultiPeriodAccuracyDTO;
import com.tsintergy.lf.serviceapi.base.ultra.dto.TimeAccuracyDTO;
import com.tsintergy.lf.web.base.ultra.request.CommonDayRequest;
import com.tsintergy.lf.web.base.ultra.request.CommonRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description
 * @Date 2023/8/415 18:40
 * <AUTHOR>
 **/
@Api(value = "数据管理-负荷数据", tags = "[超短期模块][日准确率评估]")
@RestController
@Slf4j
@RequestMapping("/ultra/accuracy/day")
public class LoadAccuracyDayController extends UltraBaseLoadFeatureController {


    @Resource
    private UltraLoadAccuracyDayService loadAccuracyDayService;


    /**
     * 查询日准确率列表
     */
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    @ApiOperation(value = "获取负荷历史数据")
    public BaseResp<List<AccuracyUltraDayDTO>> getLoadHistory(@Validated CommonRequest commonRequest, String algorithmId,
        String startTime, String endTime) throws Exception {
        AccuracyUltraData data = new AccuracyUltraData();
        BeanUtils.copyProperties(commonRequest, data);
        data.setAlgorithmId(algorithmId);
        data.setStartTime(checkTime(startTime));
        data.setEndTime(checkTime(endTime));
        List<AccuracyUltraDayDTO> loadHistory = loadAccuracyDayService.getLoadHistory(data);
        if (CollectionUtils.isEmpty(loadHistory)) {
            return new BaseResp("T706");
        }
        BaseResp resp = BaseResp.succResp("查询成功");
        resp.setData(loadHistory);
        return resp;
    }

    /**
     * 查询目标日期的96点数据
     */
    @RequestMapping(value = "/data", method = RequestMethod.GET)
    @ApiOperation(value = "查询目标日期的96点数据")
    public BaseResp<TimeAccuracyDTO> getTimeAccuracy(@Validated CommonDayRequest commonRequest, String algorithmId,
        String startTime, String endTime) throws Exception {
        AccuracyUltraData data = new AccuracyUltraData();
        BeanUtils.copyProperties(commonRequest, data);
        data.setAlgorithmId(algorithmId);
        data.setStartTime(checkTime(startTime));
        data.setEndTime(checkTime(endTime));
        TimeAccuracyDTO loadHistory = loadAccuracyDayService.getTimeAccuracy(data);
        if (loadHistory == null) {
            return new BaseResp("T706");
        }
        BaseResp resp = BaseResp.succResp("查询成功");
        resp.setData(loadHistory);
        return resp;

    }

    /**
     * 获取最低准确率预测情况
     */
    @RequestMapping(value = "/details", method = RequestMethod.GET)
    @ApiOperation(value = "获取最低准确率预测情况")
    public BaseResp<AccuracyLowDetailsDayDTO> getTimeAccuracyDetails(@Validated CommonDayRequest commonRequest,
        String algorithmId, String time) throws Exception {
        AccuracyUltraData data = new AccuracyUltraData();
        BeanUtils.copyProperties(commonRequest, data);
        data.setAlgorithmId(algorithmId);
        data.setTime(checkTime(time));
        AccuracyLowDetailsDayDTO accuracyLowDetails = loadAccuracyDayService.getAccuracyLowDetails(data);
        if (accuracyLowDetails == null) {
            return new BaseResp("T706");
        }
        BaseResp resp = BaseResp.succResp("查询成功");
        resp.setData(accuracyLowDetails);
        return resp;
    }

    /**
     * 多时段预测对比页面获取数据
     */
    @RequestMapping(value = "/multiperiod/accuracy", method = RequestMethod.GET)
    @ApiOperation(value = "多时段预测对比页面获取数据")
    public BaseResp<MultiPeriodAccuracyDTO> getmultiperiodAccuracy(@Validated CommonDayRequest commonRequest,
        String algorithmId, String startTime, String endTime) throws Exception {
        AccuracyUltraData data = new AccuracyUltraData();
        BeanUtils.copyProperties(commonRequest, data);
        data.setAlgorithmId(algorithmId);
        data.setStartTime(checkTime(startTime));
        data.setEndTime(checkTime(endTime));
        MultiPeriodAccuracyDTO multiPeriodAccuracy = loadAccuracyDayService.getMultiPeriodAccuracy(data);
        if (multiPeriodAccuracy == null) {
            return new BaseResp("T706");
        }
        BaseResp resp = BaseResp.succResp("查询成功");
        resp.setData(multiPeriodAccuracy);
        return resp;
    }

}
