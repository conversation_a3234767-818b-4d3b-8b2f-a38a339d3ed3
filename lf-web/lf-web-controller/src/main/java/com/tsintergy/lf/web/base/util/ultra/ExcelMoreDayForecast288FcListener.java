package com.tsintergy.lf.web.base.util.ultra;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsintergy.aif.tool.core.utils.date.DateUtil;
import com.tsintergy.lf.core.enums.IfEnum;
import com.tsintergy.lf.serviceapi.base.ultra.load.pojo.LoadCityFcUltraDO;
import com.tsintergy.lf.serviceapi.base.ultra.load.api.LoadCityFcUltraService;
import com.tsintergy.lf.serviceapi.base.ultra.api.LoadCityHisUltraBasicService;
import com.tsintergy.lf.serviceapi.base.ultra.enums.UltraMultipointFifteenEnum;
import com.tsintergy.lf.serviceapi.base.ultra.pojo.LoadCityHisUltraBasicDO;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.springframework.beans.BeanUtils;

/**
 * 数据导入监听类
 *
 * <AUTHOR>
 */
public class ExcelMoreDayForecast288FcListener extends AnalysisEventListener<ExcelMoreDayForecastDataHis96> {

    private List<ExcelMoreDayForecastDataHis96> list = new ArrayList();

    private LoadCityFcUltraService loadCityFcUltraService;

    private LoadCityHisUltraBasicService loadCityHisUltraBasicService;

    private String cityId;

    private String userId;

    private String caliberId;


    public ExcelMoreDayForecast288FcListener(LoadCityFcUltraService loadCityFcUltraService, LoadCityHisUltraBasicService loadCityHisUltraBasicService,String cityId, String userId,
        String caliberId) {
        this.loadCityFcUltraService = loadCityFcUltraService;
        this.loadCityHisUltraBasicService = loadCityHisUltraBasicService;
        this.cityId = cityId;
        this.userId = userId;
        this.caliberId = caliberId;
    }

    /**
     * 逐行解析数据
     */
    @Override
    public void invoke(ExcelMoreDayForecastDataHis96 data, AnalysisContext context) {
        list.add(data);
    }

    /**
     * 逐行的返回头信息
     */
    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {

    }


    /**
     * 所有数据解析完成后的回调函数
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
//        if (CollectionUtils.isEmpty(list)) {
//            return;
//        }
        Date start = DateUtil.string2Date("2022-01-01", DateFormatType.SIMPLE_DATE_FORMAT_STR);
        Date end = DateUtil.string2Date("2022-12-31", DateFormatType.SIMPLE_DATE_FORMAT_STR);

        try {
            List<LoadCityHisUltraBasicDO> loadCityHisDOS = loadCityHisUltraBasicService.getLoadCityHisDOS("1", "1", start, end, 5);
            List<LoadCityFcUltraDO> resultList = new ArrayList<>();
        loadCityHisDOS.forEach(e -> {
            resultList.add(getLoadCityFcDO(e, "202", 1, true));
            resultList.add(getLoadCityFcDO(e, "202", UltraMultipointFifteenEnum.ULTRA_FORECAST_2.getType(), false));
            resultList.add(getLoadCityFcDO(e, "202", UltraMultipointFifteenEnum.ULTRA_FORECAST_3.getType(), false));
            resultList.add(getLoadCityFcDO(e, "202", UltraMultipointFifteenEnum.ULTRA_FORECAST_4.getType(), false));
            resultList.add(getLoadCityFcDO(e, "202", UltraMultipointFifteenEnum.ULTRA_FORECAST_8.getType(), false));
            resultList.add(getLoadCityFcDO(e, "202", UltraMultipointFifteenEnum.ULTRA_FORECAST_16.getType(), false));
            resultList.add(getLoadCityFcDO(e, "203", 1, false));
            resultList.add(getLoadCityFcDO(e, "203", UltraMultipointFifteenEnum.ULTRA_FORECAST_2.getType(), false));
            resultList.add(getLoadCityFcDO(e, "203", UltraMultipointFifteenEnum.ULTRA_FORECAST_3.getType(), false));
            resultList.add(getLoadCityFcDO(e, "203", UltraMultipointFifteenEnum.ULTRA_FORECAST_4.getType(), false));
            resultList.add(getLoadCityFcDO(e, "203", UltraMultipointFifteenEnum.ULTRA_FORECAST_8.getType(), false));
            resultList.add(getLoadCityFcDO(e, "203", UltraMultipointFifteenEnum.ULTRA_FORECAST_16.getType(), false));

        });

            loadCityFcUltraService.doSaveOrUpdateList(resultList);
        } catch (Exception exception) {
            exception.printStackTrace();
        }
    }



    private LoadCityFcUltraDO getLoadCityFcDO(LoadCityHisUltraBasicDO e, String algorithmId, Integer type,
        Boolean report) {
        LoadCityFcUltraDO loadCityFcDO = new LoadCityFcUltraDO();
        BeanUtils.copyProperties(e, loadCityFcDO);
        loadCityFcDO.setCityId("1");
        loadCityFcDO.setCaliberId(caliberId);
        Date date = e.getDateTime();
        loadCityFcDO.setDateTime(new java.sql.Date(date.getTime()));
        loadCityFcDO.setId(e.getId());
        loadCityFcDO.setMultipointType(type);
        loadCityFcDO.setAlgorithmId(algorithmId);
        loadCityFcDO.setRecommend(report);
        loadCityFcDO.setReport(report ? IfEnum.YES : IfEnum.NO);
        loadCityFcDO.setReportTime(report ? new Timestamp(System.currentTimeMillis()) : null);
        loadCityFcDO.setCreateTime(new Date());
        loadCityFcDO.setUpdateTime(new Date());
        float minF = new BigDecimal(0.5).floatValue();
        float maxF = new BigDecimal(5).floatValue();
        List<BigDecimal> bigDecimals = e.getTvData();
        loadCityFcDO.setTvMeta(DataPointMetaBuilder.createPointMeta(date, 5));
        List<BigDecimal> finalData = new ArrayList<>();
        for (BigDecimal one : bigDecimals) {
            BigDecimal db = new BigDecimal(Math.random() * (maxF - minF) + minF).setScale(2, BigDecimal.ROUND_HALF_UP);
            finalData.add(one.add(db));
        }

        loadCityFcDO.setTvData(finalData);
        return loadCityFcDO;
    }


}
