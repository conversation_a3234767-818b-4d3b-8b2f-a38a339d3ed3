/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司
 * Author:  wang<PERSON>
 * Date:  2019/9/25 3:12
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.security.controller;

import com.alibaba.fastjson.JSON;
import com.tsieframework.cloud.security.serviceapi.system.api.RedisService;
import com.tsieframework.cloud.security.serviceapi.system.api.SecurityService;
import com.tsieframework.cloud.security.serviceapi.system.bean.TokenBean;
import com.tsieframework.cloud.security.serviceapi.system.bean.TsieMenuTreeBean;
import com.tsieframework.cloud.security.serviceapi.system.pojo.TsieMenuVO;
import com.tsieframework.cloud.security.serviceapi.system.pojo.TsieRoleVO;
import com.tsieframework.cloud.security.serviceapi.system.pojo.TsieUserVO;
import com.tsieframework.cloud.security.web.common.security.TokenManager;
import com.tsieframework.cloud.security.web.common.security.UserTokenHelper;
import com.tsieframework.cloud.security.web.common.security.account.DefaultAccountManager;
import com.tsieframework.cloud.security.web.common.security.account.LoginRequest;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.dao.hibernate.QueryOp;
import com.tsieframework.core.base.exception.BusinessException;
import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.format.datetime.DateFormatType;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.core.constants.CacheConstants;
import com.tsintergy.lf.core.constants.CityConstants;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.constants.SystemConstant;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.base.pojo.LoadUserDO;
import com.tsintergy.lf.serviceapi.base.security.api.UserService;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.serviceapi.base.system.pojo.SettingSystemDO;
import com.tsintergy.lf.web.base.common.util.IpUtil;
import com.tsintergy.lf.web.base.security.properties.SecurityProperties;
import com.tsintergy.lf.web.base.security.response.LoginResp;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * Description:  <br>
 * 登录扩展类
 *
 * <AUTHOR>
 * @create 2019/9/25
 * @since 1.0.0
 */
@Component
public class SsoLoadForecastAccountManager extends DefaultAccountManager {

    public static final long DAY_MS = 24 * 60 * 60 * 1000;

    @Autowired
    private SecurityProperties securityProperties;

    @Autowired
    private UserService userService;

    @Autowired
    private CityService cityService;

    @Autowired
    private RedisService redisService;

    @Autowired
    SettingSystemService settingSystemService;


    protected SecurityService securityService;
    protected TokenManager tokenManager;



    public SsoLoadForecastAccountManager(SecurityService securityService, TokenManager tokenManager) {
        this.securityService = securityService;
        this.tokenManager = tokenManager;
    }


    public BaseResp login(HttpServletRequest request, HttpServletResponse response, String userName) throws Exception {
        BaseResp resp = BaseResp.succResp();
        DBQueryParam param = DBQueryParamBuilder.create().where(QueryOp.StringEqualTo, "username", userName).queryDataOnly().build();
        TsieUserVO user =  securityService.queryTsieUserVo(param);
        this.validEnable(null, user);
        this.validExpired(null, user);
        String token = tokenManager.setCookieToken(user);
        List<TsieMenuVO> menuList = securityService.queryTsieMenuAllByUserId(user.getId(),null);
        TokenBean tokenBean = tokenManager.createTokenBean(token, user, menuList);
        tokenManager.addTokenBeanToCache(user.getUsername(), tokenBean);
        return resp;
    }




    /**
     * 项目自定义操作，扩展TSIE组件内容
     *
     * @param resp
     * @param request
     * @param user
     * @throws Exception
     */
    @Override
    protected void afterLoginSuccess(BaseResp resp, LoginRequest request, TsieUserVO user) throws Exception {
        HttpServletRequest httpServletRequest =
                ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        HttpSession session = httpServletRequest.getSession();
        LoginResp loginResp = new LoginResp();
        loginResp.setPasswdChangePrompt(user.getPasswdChangePrompt());
        LoadUserDO LoadUserDetailDO = this.userService.findUserById(user.getId());
        logger.debug("userService返回的 = " + JSON.toJSON(user));
        if (null != LoadUserDetailDO) {
            loginResp.setCityId(LoadUserDetailDO.getCityId());
            session.setAttribute(CacheConstants.USER_ID, user.getId());
            session.setAttribute(CacheConstants.CITY_ID, LoadUserDetailDO.getCityId());
            if (StringUtils.isNotBlank(LoadUserDetailDO.getCityId())) {
                CityDO cityVO = cityService.findCityById(LoadUserDetailDO.getCityId());
                session.setAttribute(CacheConstants.CALIBER_ID, CityConstants.PROVINCE_ID.equals(cityVO.getId())?Constants.CALIBER_ID_BG_QW:Constants.CALIBER_ID_BG_DS);

                if (cityVO != null) {
                    loginResp.setCity(cityVO.getCity());
                    loginResp.setCityType(cityVO.getType());
                }
            }
            redisService.redisAdd(securityProperties.getTokenCacheKeyPrefix()+"_dict.lastUpdatePasswordTime." + user.getUsername(),LoadUserDetailDO.getLastUpdatePasswordTime());
            redisService.redisAdd(securityProperties.getTokenCacheKeyPrefix()+"_dict.createTime." + user.getUsername(),LoadUserDetailDO.getCreateTime());

            //判断是否需要重置密码
            reseatPassword(LoadUserDetailDO,loginResp);
        } else {
            throw TsieExceptionUtils.newBusinessException("T814");
        }
        loginResp.setRoleSystemTime(false);
        //查询用户的权限
        List<TsieRoleVO> userRoles = securityService.queryUserRoleAll(user.getId());
        if (CollectionUtils.isNotEmpty(userRoles) && userRoles.size() > 0) {
            for (TsieRoleVO tsieRoleVO : userRoles) {
                if ("ROLE_ADMIN".equals(tsieRoleVO.getRole())) {
                    loginResp.setRoleSystemTime(true);
                }
            }
        }
        loginResp.setNickname(user.getNickname());
        loginResp.setSystemTime(DateUtils.date2String(new Date(), DateFormatType.DATE_FORMAT_STR));
        this.getServletRequest().getSession().setAttribute(CacheConstants.SYSTEM_DATE, loginResp.getSystemTime());
        TsieMenuTreeBean menuTreeBean = securityService.queryTsieUserMenuTree(user.getId(),null);
        String defalutMenuPath=null;
        defalutMenuPath=getDefalutMenuPathList(menuTreeBean,getDefalutMenuPath(menuTreeBean));
        loginResp.setDefaultMenuPath(getDefalutMenuPath(menuTreeBean));
        List<TsieMenuVO> datas = securityService.queryAllMenu(DBQueryParamBuilder.create().build()).getDatas();
        List<TsieMenuVO> collect = datas.stream().filter(t -> {
            if (loginResp.getDefaultMenuPath().equals(t.getMenupath())) {
                return true;
            }
            return false;
        }).collect(Collectors.toList());
        loginResp.setDefaultMenuPathName(collect.get(0).getName());
        List<SettingSystemDO> settingSystemDOS=  settingSystemService.findByKey(SystemConstant.colorStr);
        List<SettingSystemDO> settingSystemDO= settingSystemService.findByKey(SystemConstant.isOpenReseau);
        if (CollectionUtils.isNotEmpty(settingSystemDOS)){
            loginResp.setColorStr(settingSystemDOS.get(0).getValue());
        }else {
            loginResp.setColorStr("#5B5B5B");
        }
        if (CollectionUtils.isNotEmpty(settingSystemDO)){
            loginResp.setOpenReseau(settingSystemDO.get(0).getValue());
        }else {
            loginResp.setOpenReseau(SystemConstant.OpenReseau);
        }

        loginResp.setLoginMessage(StringUtils.isNotBlank(loginResp.getLoginMessage()) ? loginResp.getLoginMessage() : "登陆成功");
        loginResp.setStartWithZero(Constants.LOAD_CURVE_START_WITH_ZERO);
        loginResp.setDefaultMenuPath(defalutMenuPath);
        resp.setData(loginResp);
        logger.debug("时间 = " + new Date() + "用户 username = " + user.getUsername() + " ip = " + IpUtil.getRealIP(this.getServletRequest()) + "登陆成功");
    }
    private String getDefalutMenuPathList(TsieMenuTreeBean tsieMenuTreeBean, String key) {
        if (tsieMenuTreeBean.getChildren() == null || tsieMenuTreeBean.getChildren().size() == 0) {
            return null;
        }
        if (tsieMenuTreeBean != null) {
            for (TsieMenuTreeBean menuTreeBean : tsieMenuTreeBean.getChildren()) {

                if (menuTreeBean.getMenu().getMenupath().equals(key)) {
                    return menuTreeBean.getMenu().getMenupath();
                }
                String defalutMenuPathList = getDefalutMenuPathList(menuTreeBean, key);
                if (defalutMenuPathList != null) {
                    return menuTreeBean.getMenu().getMenupath() + defalutMenuPathList;
                }

            }
        }
        return null;
    }
    private void reseatPassword(LoadUserDO loadUserVO,LoginResp loginResp) {
        Date lastUpdatePassword = loadUserVO.getLastUpdatePasswordTime();
        if (lastUpdatePassword == null) {
            lastUpdatePassword = loadUserVO.getCreateTime();
        }
        Integer resetInterval = securityProperties.getPassword().getResetInterval();
        Integer warningInterval = securityProperties.getPassword().getWarningInterval();

        //如果使用同一个密码超过resetInterval-warningInterval，提示重置密码
        long useTime = System.currentTimeMillis() - lastUpdatePassword.getTime();
        long remainingTime = DAY_MS * resetInterval - useTime;
        if (remainingTime < DAY_MS * warningInterval) {
            long remainingDay = (remainingTime % DAY_MS == 0 ? remainingTime / DAY_MS : remainingTime / DAY_MS + 1);
            loginResp.setLoginMessage("距密码过期还剩" + (remainingDay < 0 ? 0 : remainingDay) + "天，请重置密码");

            //如果使用同一个密码超过resetInterval，重置密码
            if (remainingDay <= 0) {
                loginResp.setPasswdChangePrompt(TsieUserVO.PASSWD_CHANGE_PROMPT_YES);
            }
        }
    }
    private String getDefalutMenuPath(TsieMenuTreeBean tsieMenuTreeBean) {
        String defaultMenuPath = tsieMenuTreeBean.getMenu().getMenupath();
        if (tsieMenuTreeBean.getChildren() != null && tsieMenuTreeBean.getChildren().size() > 0) {
            tsieMenuTreeBean = tsieMenuTreeBean.getChildren().get(0);
            if (tsieMenuTreeBean.getMenu().getType() == 1) {
                defaultMenuPath = getDefalutMenuPath(tsieMenuTreeBean);
                if (defaultMenuPath != null) {
                    return defaultMenuPath;
                }
            }
            return defaultMenuPath;
        }
        return defaultMenuPath;
    }


    public BaseResp getSystemInfo() throws Exception {
        BaseResp resp = BaseResp.succResp();
        TokenBean bean = getTokenBean();
        TsieUserVO user = bean.getTsieUserVO();
        user.setPasswd((String) null);
        user.setLoginMode(this.getUserLoginMode(user));
        this.setUserPasswdChangePrompt(user, true);
        Map<String, Object> data = new HashMap();
        data.put("user", user);
        resp.setData(data);
        this.afterLoginSuccess(resp, null, user);
        return resp;
    }

    private TokenBean getTokenBean() {
        TokenBean bean;
        String token = tokenManager.getCookieToken();
        if (null == token) {
            if (logger.isDebugEnabled()) {
                logger.debug("1. cookie 未找到 loginTokenKey = " + UserTokenHelper.getIPAddress());
            }

            throw new BusinessException("T000", "cookie 未找到 loginTokenKey = " + UserTokenHelper.getIPAddress());
        } else {
            Claims claims = Jwts.parser().setSigningKey("base64EncodedSecretKey").parseClaimsJws(token).getBody();
            String username = claims.get("username", String.class);
            String tokenBeanCacheKey = this.tokenManager.getTokenBeanCacheKey(username);
            bean = (TokenBean) redisService.redisGet(tokenBeanCacheKey, TokenBean.class);
            if (null == bean) {
                if (logger.isDebugEnabled()) {
                    logger.debug("2. 缓存找不到 redisKey = dict.token." + username);
                }

                throw new BusinessException("T000", "2. 缓存找不到 redisKey = dict.token." + username);
            }
        }
        return bean;
    }


    @Override
    protected void validUserPassword(LoginRequest request, TsieUserVO user) {
        if (!this.passwordValidator.valid(request, user, this)) {
            this.throwBusinessException("T8810");
        }
    }

    @Override
    protected void validEnable(LoginRequest request, TsieUserVO user) {
        if (!TsieUserVO.isEnable(user)) {
            this.throwBusinessException("T8810");
        }
    }

    @Override
    protected TsieUserVO getUserAndValidRequest(LoginRequest request) throws Exception {
        if (StringUtils.isEmpty(request.getUsername())) {
            this.throwBusinessException("T809");
        }

        if (StringUtils.isEmpty(request.getPassword())) {
            this.throwBusinessException("T810");
        }

        TsieUserVO user = this.getUserByUsername(request.getUsername());
        if (user == null) {
            this.throwBusinessException("T8810");
        }

        return user;
    }
}