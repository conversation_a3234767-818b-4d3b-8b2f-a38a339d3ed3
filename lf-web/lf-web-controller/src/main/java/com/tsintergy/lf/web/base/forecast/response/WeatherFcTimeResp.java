/**
 *   
 *  Copyright (C), 2015‐2020, 北京清能互联科技有限公司  
 *  Author:  EDZ  
 *  Date:  2020/10/12 15:50  
 *  History:  
 *  <author> <time> <version> <desc> 
 */
package com.tsintergy.lf.web.base.forecast.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**  
 * Description:  <br> 
 * 
 * <AUTHOR>  
 * @create 2020/10/12  
 * @since 1.0.0  
 */
@Data
public class WeatherFcTimeResp implements Serializable {

    @ApiModelProperty(value = "最新采集时间",example = "2022-02-01 01:00:00")
    private String newWeatherFcTime;

    @ApiModelProperty(value = "算法预测使用气象时间",example = "2022-02-01 01:00:00")
    private String algorithmWeatherFcTime;
}