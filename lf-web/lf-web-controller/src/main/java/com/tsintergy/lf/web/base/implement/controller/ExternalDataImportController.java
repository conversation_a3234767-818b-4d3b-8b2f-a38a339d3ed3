package com.tsintergy.lf.web.base.implement.controller;

import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.aif.tool.core.enums.WeatherEnum;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.core.util.PeriodDataUtil;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityHisService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisDO;
import com.tsintergy.lf.web.base.common.util.ExcelUtil;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/** @Description
 * <AUTHOR>
 * 外部数据导入
 * @Date 2025/7/3 09:55
 **/
@Api(tags = "外部数据导入")
@RestController
@RequestMapping("/externalData")
public class ExternalDataImportController {

    @Autowired
    private LoadCityHisService loadCityHisService;

    @Autowired
    WeatherCityHisService weatherCityHisService;

    @Autowired
    private WeatherCityFcService weatherCityFcService;


    @RequestMapping(value = "/importLoad", method = RequestMethod.GET)
    public BaseResp importLoad(@RequestParam(value = "file") MultipartFile file) throws Exception {
        BaseResp resp = BaseResp.succResp();
        List<List<List<String>>> read = ExcelUtil.read(file.getInputStream(), false);

        Map<Date, Map<String, BigDecimal>> resultMap = new HashMap<>();

        read.stream()
                .flatMap(List::stream)
                .skip(1)
                .forEach(list -> {
                    String load = list.get(2);
                    if (StringUtils.isEmpty(load)) return;
                    BigDecimal loadValue = new BigDecimal(load);
                    String timeStr = list.get(1);
                    String[] strings = timeStr.split(" ");
                    String date = strings[0];
                    Date newDate = DateUtil.getDate(date, "yyyy-MM-dd");
                    String time = strings[1];
                    String field = "t" + time.split(":")[0] + time.split(":")[1];
                    resultMap.computeIfAbsent(newDate, k -> new HashMap<>())
                            .put(field, loadValue);
                });

        for (Map.Entry<Date, Map<String, BigDecimal>> entry : resultMap.entrySet()) {
            Date date = entry.getKey();
            Map<String, BigDecimal> valueMap = entry.getValue();
            completion2400(date, resultMap, valueMap);
            LoadCityHisDO loadCityHisDO = new LoadCityHisDO();
            loadCityHisDO.setCityId("410000");
            loadCityHisDO.setDate(new java.sql.Date(date.getTime()));
            loadCityHisDO.setCaliberId("1");
            BasePeriodUtils.setAllFiled(loadCityHisDO, valueMap);
            loadCityHisService.doInsertOrUpdate(loadCityHisDO);
        }
        return resp;
    }

    @Autowired
    private CityService cityService;

    @RequestMapping(value = "/importHisWeather", method = RequestMethod.GET)
    public BaseResp importHisWeather(@RequestParam(value = "file") MultipartFile file) throws Exception {
        BaseResp resp = BaseResp.succResp();
        List<List<List<String>>> read = ExcelUtil.read(file.getInputStream(), false);

        Map<String, String> cityMap = cityService.findAllCitys().stream().collect(Collectors.toMap(CityDO::getCity, CityDO::getId));
        Map<String, Map<Date, Map<String, BigDecimal>>> tmpMap = new HashMap<>();
        Map<String, Map<Date, Map<String, BigDecimal>>> humMap = new HashMap<>();
        Map<String, Map<Date, Map<String, BigDecimal>>> windMap = new HashMap<>();
        Map<String, Map<Date, Map<String, BigDecimal>>> rainMap = new HashMap<>();

        read.stream()
                .flatMap(List::stream)
                .skip(1) // 跳过标题行
                .forEach(list -> {
                    String cityName = list.get(0);
                    String cityId = cityMap.get(cityName);
                    if (cityId == null) return;

                    // 解析时间
                    String[] timeParts = list.get(6).split(" ");
                    Date date = DateUtil.getDate(timeParts[0], "yyyy-MM-dd");
                    String[] hourMinute = timeParts[1].split(":");
                    String field = "t" + hourMinute[0] + hourMinute[1];

                    // 处理温度数据
                    makeMapValue(list, tmpMap, cityId, date, field, 1);

                    // 处理湿度数据
                    makeMapValue(list, humMap, cityId, date, field, 2);

                    // 处理风速数据
                    makeMapValue(list, windMap, cityId, date, field, 3);

                    // 处理雨量数据
                    makeMapValue(list, rainMap, cityId, date, field, 4);

                });


        saveHisWeather(tmpMap, WeatherEnum.TEMPERATURE.getType());
        saveHisWeather(humMap,WeatherEnum.HUMIDITY.getType());
        saveHisWeather(windMap,WeatherEnum.WINDSPEED.getType());
        saveHisWeather(rainMap,WeatherEnum.RAINFALL.getType());

        return resp;
    }


    @RequestMapping(value = "/importFcWeather", method = RequestMethod.GET)
    public BaseResp importFcWeather(@RequestParam(value = "file") MultipartFile file) throws Exception {
        BaseResp resp = BaseResp.succResp();
        List<List<List<String>>> read = ExcelUtil.read(file.getInputStream(), false);

        Map<String, String> cityMap = cityService.findAllCitys().stream().collect(Collectors.toMap(CityDO::getCity, CityDO::getId));
        Map<String, Map<Date, Map<String, BigDecimal>>> tmpMap = new HashMap<>();
        Map<String, Map<Date, Map<String, BigDecimal>>> humMap = new HashMap<>();
        Map<String, Map<Date, Map<String, BigDecimal>>> tgwdMap = new HashMap<>();
        Map<String, Map<Date, Map<String, BigDecimal>>> rainMap = new HashMap<>();

        read.stream()
                .flatMap(List::stream)
                .skip(1) // 跳过标题行
                .forEach(list -> {
                    String cityName = list.get(0);
                    String cityId = cityMap.get(cityName);
                    if (cityId == null) return;

                    // 解析时间
                    String[] timeParts = list.get(2).split(" ");
                    Date date = DateUtil.getDate(timeParts[0], "yyyy-MM-dd");
                    String[] hourMinute = timeParts[1].split(":");
                    String field = "t" + hourMinute[0] + hourMinute[1];

                    // 处理温度数据
                    makeMapValue(list, tmpMap, cityId, date, field, 3);

                    // 处理湿度数据
                    makeMapValue(list, humMap, cityId, date, field, 4);

                    // 处理雨量数据
                    makeMapValue(list, rainMap, cityId, date, field, 5);


                    // 处理体感温度数据
                    makeMapValue(list, tgwdMap, cityId, date, field, 6);
                });


        saveFcWeather(tmpMap, WeatherEnum.TEMPERATURE.getType());
        saveFcWeather(humMap,WeatherEnum.HUMIDITY.getType());
        saveFcWeather(tgwdMap,WeatherEnum.EFFECTIVE_TEMPERATURE.getType());
        saveFcWeather(rainMap,WeatherEnum.RAINFALL.getType());

        return resp;
    }

    private void saveHisWeather(Map<String, Map<Date, Map<String, BigDecimal>>> tmpMap,int type) throws Exception {
        for (Map.Entry<String, Map<Date, Map<String, BigDecimal>>> cityEntry : tmpMap.entrySet()) {
            String cityId = cityEntry.getKey();
            Map<Date, Map<String, BigDecimal>> dateMap = cityEntry.getValue();
            for (Map.Entry<Date, Map<String, BigDecimal>> dateEntry : cityEntry.getValue().entrySet()) {
                Date date = dateEntry.getKey();
                Map<String, BigDecimal> valueMap = dateEntry.getValue();
                //补全2400值
                completion2400(date, dateMap, valueMap);
                WeatherCityHisDO weatherCityHisDO = new WeatherCityHisDO();
                weatherCityHisDO.setCityId(cityId);
                weatherCityHisDO.setDate(new java.sql.Date(date.getTime()));
                weatherCityHisDO.setType(type);
                BasePeriodUtils.setAllFiled(weatherCityHisDO, valueMap);
                PeriodDataUtil.do24To96VO(weatherCityHisDO);
                weatherCityHisService.doInsertOrUpdate(weatherCityHisDO);
            }
        }
    }

    private void saveFcWeather(Map<String, Map<Date, Map<String, BigDecimal>>> tmpMap,int type) throws Exception {
        for (Map.Entry<String, Map<Date, Map<String, BigDecimal>>> cityEntry : tmpMap.entrySet()) {
            String cityId = cityEntry.getKey();
            Map<Date, Map<String, BigDecimal>> dateMap = cityEntry.getValue();
            for (Map.Entry<Date, Map<String, BigDecimal>> dateEntry : cityEntry.getValue().entrySet()) {
                Date date = dateEntry.getKey();
                Map<String, BigDecimal> valueMap = dateEntry.getValue();
                //补全2400值
                completion2400(date, dateMap, valueMap);
                WeatherCityFcDO weatherCityHisDO = new WeatherCityFcDO();
                weatherCityHisDO.setCityId(cityId);
                weatherCityHisDO.setDate(new java.sql.Date(date.getTime()));
                weatherCityHisDO.setType(type);
                BasePeriodUtils.setAllFiled(weatherCityHisDO, valueMap);
                PeriodDataUtil.do24To96VO(weatherCityHisDO);
                weatherCityFcService.doInsertOrUpdate(weatherCityHisDO);
            }
        }
    }

    private static void makeMapValue(List<String> list, Map<String, Map<Date, Map<String, BigDecimal>>> weatherMap,
                                     String cityId, Date date, String field, int type) {
        BigDecimal weatherValue = new BigDecimal(list.get(type));
        weatherMap.computeIfAbsent(cityId, k -> new HashMap<>())
                .computeIfAbsent(date, k -> new HashMap<>())
                .put(field, weatherValue);
    }

    private static void completion2400(Date date, Map<Date, Map<String, BigDecimal>> dateMap, Map<String, BigDecimal> valueMap) {
        Date moveDay = DateUtil.getMoveDay(date, 1);
        Map<String, BigDecimal> nextMap = dateMap.get(moveDay);
        if (Objects.nonNull(nextMap)) {
            valueMap.put("t2400", nextMap.get("t0000"));
        }
    }
}
