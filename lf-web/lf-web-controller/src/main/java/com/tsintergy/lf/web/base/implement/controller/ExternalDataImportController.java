package com.tsintergy.lf.web.base.implement.controller;

import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.aif.tool.core.enums.WeatherEnum;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.core.util.PeriodDataUtil;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityHisDO;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityHisService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisDO;
import com.tsintergy.lf.web.base.common.util.ExcelUtil;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/** @Description
 * <AUTHOR>
 * 外部数据导入
 * @Date 2025/7/3 09:55
 **/
@Api(tags = "外部数据导入")
@RestController
@RequestMapping("/externalData")
public class ExternalDataImportController {

    @Autowired
    private LoadCityHisService loadCityHisService;

    @Autowired
    WeatherCityHisService weatherCityHisService;

    @Autowired
    private WeatherCityFcService weatherCityFcService;


    @RequestMapping(value = "/importLoad", method = RequestMethod.GET)
    public BaseResp importLoad(@RequestParam(value = "file") MultipartFile file) throws Exception {
        BaseResp resp = BaseResp.succResp();
        List<List<List<String>>> read = ExcelUtil.read(file.getInputStream(), false);

        Map<Date, Map<String, BigDecimal>> resultMap = new HashMap<>();

        read.stream()
                .flatMap(List::stream)
                .skip(1)
                .forEach(list -> {
                    String load = list.get(2);
                    if (StringUtils.isEmpty(load)) return;
                    BigDecimal loadValue = new BigDecimal(load);
                    String timeStr = list.get(1);
                    String[] strings = timeStr.split(" ");
                    String date = strings[0];
                    Date newDate = DateUtil.getDate(date, "yyyy-MM-dd");
                    String time = strings[1];
                    String field = "t" + time.split(":")[0] + time.split(":")[1];
                    resultMap.computeIfAbsent(newDate, k -> new HashMap<>())
                            .put(field, loadValue);
                });

        for (Map.Entry<Date, Map<String, BigDecimal>> entry : resultMap.entrySet()) {
            Date date = entry.getKey();
            Map<String, BigDecimal> valueMap = entry.getValue();
            completion2400(date, resultMap, valueMap);
            LoadCityHisDO loadCityHisDO = new LoadCityHisDO();
            loadCityHisDO.setCityId("410000");
            loadCityHisDO.setDate(new java.sql.Date(date.getTime()));
            loadCityHisDO.setCaliberId("1");
            BasePeriodUtils.setAllFiled(loadCityHisDO, valueMap);
            loadCityHisService.doInsertOrUpdate(loadCityHisDO);
        }
        return resp;
    }

    @Autowired
    private CityService cityService;

    @RequestMapping(value = "/importHisWeather", method = RequestMethod.GET)
    public BaseResp importHisWeather(@RequestParam(value = "file") MultipartFile file) throws Exception {
        BaseResp resp = BaseResp.succResp();
        List<List<List<String>>> read = ExcelUtil.read(file.getInputStream(), false);

        Map<String, String> cityMap = cityService.findAllCitys().stream().collect(Collectors.toMap(CityDO::getCity, CityDO::getId));
        Map<String, Map<Date, Map<String, BigDecimal>>> tmpMap = new HashMap<>();
        Map<String, Map<Date, Map<String, BigDecimal>>> humMap = new HashMap<>();
        Map<String, Map<Date, Map<String, BigDecimal>>> windMap = new HashMap<>();
        Map<String, Map<Date, Map<String, BigDecimal>>> rainMap = new HashMap<>();

        read.stream()
                .flatMap(List::stream)
                .skip(1) // 跳过标题行
                .forEach(list -> {
                    String cityName = list.get(0);
                    String cityId = cityMap.get(cityName);
                    if (cityId == null) return;

                    // 解析时间
                    String[] timeParts = list.get(6).split(" ");
                    Date date = DateUtil.getDate(timeParts[0], "yyyy-MM-dd");
                    String[] hourMinute = timeParts[1].split(":");
                    String field = "t" + hourMinute[0] + hourMinute[1];

                    // 处理温度数据
                    makeMapValue(list, tmpMap, cityId, date, field, 1);

                    // 处理湿度数据
                    makeMapValue(list, humMap, cityId, date, field, 2);

                    // 处理风速数据
                    makeMapValue(list, windMap, cityId, date, field, 3);

                    // 处理雨量数据
                    makeMapValue(list, rainMap, cityId, date, field, 4);

                });


        saveHisWeather(tmpMap, WeatherEnum.TEMPERATURE.getType());
        saveHisWeather(humMap,WeatherEnum.HUMIDITY.getType());
        saveHisWeather(windMap,WeatherEnum.WINDSPEED.getType());
        saveHisWeather(rainMap,WeatherEnum.RAINFALL.getType());

        return resp;
    }


    @RequestMapping(value = "/importFcWeather", method = RequestMethod.GET)
    public BaseResp importFcWeather(@RequestParam(value = "file") MultipartFile file) throws Exception {
        BaseResp resp = BaseResp.succResp();
        List<List<List<String>>> read = ExcelUtil.read(file.getInputStream(), false);

        Map<String, String> cityMap = cityService.findAllCitys().stream().collect(Collectors.toMap(CityDO::getCity, CityDO::getId));
        Map<String, Map<Date, Map<String, BigDecimal>>> tmpMap = new HashMap<>();
        Map<String, Map<Date, Map<String, BigDecimal>>> humMap = new HashMap<>();
        Map<String, Map<Date, Map<String, BigDecimal>>> tgwdMap = new HashMap<>();
        Map<String, Map<Date, Map<String, BigDecimal>>> rainMap = new HashMap<>();

        read.stream()
                .flatMap(List::stream)
                .skip(1) // 跳过标题行
                .forEach(list -> {
                    String cityName = list.get(0);
                    String cityId = cityMap.get(cityName);
                    if (cityId == null) return;

                    // 解析时间
                    String[] timeParts = list.get(2).split(" ");
                    Date date = DateUtil.getDate(timeParts[0], "yyyy-MM-dd");
                    String[] hourMinute = timeParts[1].split(":");
                    String field = "t" + hourMinute[0] + hourMinute[1];

                    // 处理温度数据
                    makeMapValue(list, tmpMap, cityId, date, field, 3);

                    // 处理湿度数据
                    makeMapValue(list, humMap, cityId, date, field, 4);

                    // 处理雨量数据
                    makeMapValue(list, rainMap, cityId, date, field, 5);


                    // 处理体感温度数据
                    makeMapValue(list, tgwdMap, cityId, date, field, 6);
                });


        saveFcWeather(tmpMap, WeatherEnum.TEMPERATURE.getType());
        saveFcWeather(humMap,WeatherEnum.HUMIDITY.getType());
        saveFcWeather(tgwdMap,WeatherEnum.EFFECTIVE_TEMPERATURE.getType());
        saveFcWeather(rainMap,WeatherEnum.RAINFALL.getType());

        return resp;
    }

    private void saveHisWeather(Map<String, Map<Date, Map<String, BigDecimal>>> tmpMap,int type) throws Exception {
        for (Map.Entry<String, Map<Date, Map<String, BigDecimal>>> cityEntry : tmpMap.entrySet()) {
            String cityId = cityEntry.getKey();
            Map<Date, Map<String, BigDecimal>> dateMap = cityEntry.getValue();
            for (Map.Entry<Date, Map<String, BigDecimal>> dateEntry : cityEntry.getValue().entrySet()) {
                Date date = dateEntry.getKey();
                Map<String, BigDecimal> valueMap = dateEntry.getValue();
                //补全2400值
                completion2400(date, dateMap, valueMap);
                WeatherCityHisDO weatherCityHisDO = new WeatherCityHisDO();
                weatherCityHisDO.setCityId(cityId);
                weatherCityHisDO.setDate(new java.sql.Date(date.getTime()));
                weatherCityHisDO.setType(type);
                BasePeriodUtils.setAllFiled(weatherCityHisDO, valueMap);
                PeriodDataUtil.do24To96VO(weatherCityHisDO);
                weatherCityHisService.doInsertOrUpdate(weatherCityHisDO);
            }
        }
    }

    private void saveFcWeather(Map<String, Map<Date, Map<String, BigDecimal>>> tmpMap,int type) throws Exception {
        for (Map.Entry<String, Map<Date, Map<String, BigDecimal>>> cityEntry : tmpMap.entrySet()) {
            String cityId = cityEntry.getKey();
            Map<Date, Map<String, BigDecimal>> dateMap = cityEntry.getValue();
            for (Map.Entry<Date, Map<String, BigDecimal>> dateEntry : cityEntry.getValue().entrySet()) {
                Date date = dateEntry.getKey();
                Map<String, BigDecimal> valueMap = dateEntry.getValue();
                //补全缺失的时间点值
                completionMissingValues(date, dateMap, valueMap);
                WeatherCityFcDO weatherCityHisDO = new WeatherCityFcDO();
                weatherCityHisDO.setCityId(cityId);
                weatherCityHisDO.setDate(new java.sql.Date(date.getTime()));
                weatherCityHisDO.setType(type);
                BasePeriodUtils.setAllFiled(weatherCityHisDO, valueMap);
                PeriodDataUtil.do24To96VO(weatherCityHisDO);
                weatherCityFcService.doInsertOrUpdate(weatherCityHisDO);
            }
        }
    }

    private static void makeMapValue(List<String> list, Map<String, Map<Date, Map<String, BigDecimal>>> weatherMap,
                                     String cityId, Date date, String field, int type) {
        BigDecimal weatherValue = new BigDecimal(list.get(type));
        weatherMap.computeIfAbsent(cityId, k -> new HashMap<>())
                .computeIfAbsent(date, k -> new HashMap<>())
                .put(field, weatherValue);
    }

    /**
     * 补全缺失的时间点值，包括 t0000-t2400 的所有缺失值
     * 补值逻辑：
     * 1. 数据中只有 t0200-t2300 的值
     * 2. 需要补全 t0000-t0200 和 t2300-t2400 的值
     * 3. 跨天情况用等差平均的方式补全
     * 4. 保证今日t0000与昨天的t2400值相同
     */
    private static void completionMissingValues(Date date, Map<Date, Map<String, BigDecimal>> dateMap, Map<String, BigDecimal> valueMap) {
        // 1. 补全 t0000 到 t0200 之间的值（基于t0200向前推算）
        completeTimeRangeBackward(valueMap, "t0200", "t0000");

        // 2. 补全 t2300 到 t2400 之间的值（基于t2300向后推算）
        completeTimeRangeForward(valueMap, "t2300", "t2400");

        // 3. 处理跨天逻辑：确保今日t0000与昨天的t2400值相同
        handleCrossDayConsistency(date, dateMap, valueMap);
    }

    /**
     * 向前补全时间范围内的值（从已知点向前推算）
     * 例如：从t0200向前推算到t0000
     */
    private static void completeTimeRangeBackward(Map<String, BigDecimal> valueMap, String knownTime, String targetTime) {
        BigDecimal knownValue = valueMap.get(knownTime);
        if (knownValue == null) {
            return;
        }

        // 获取时间点列表
        List<String> timePoints = getTimePointsBetween(targetTime, knownTime);
        if (timePoints.size() <= 1) {
            return;
        }

        // 查找最近的已知值作为参考点
        BigDecimal referenceValue = findNearestKnownValue(valueMap, timePoints, knownTime);
        if (referenceValue == null) {
            // 如果没有其他参考值，使用已知值作为基准，假设变化很小
            referenceValue = knownValue;
        }

        // 计算增量（假设从参考值到已知值是线性变化）
        BigDecimal totalDiff = knownValue.subtract(referenceValue);
        int intervals = timePoints.size() - 1;
        BigDecimal increment = totalDiff.divide(new BigDecimal(intervals), 4, BigDecimal.ROUND_HALF_UP);

        // 从已知点向前补全
        for (int i = timePoints.size() - 2; i >= 0; i--) {
            String timePoint = timePoints.get(i);
            if (valueMap.get(timePoint) == null) {
                int stepsFromKnown = timePoints.size() - 1 - i;
                BigDecimal interpolatedValue = knownValue.subtract(increment.multiply(new BigDecimal(stepsFromKnown)));
                valueMap.put(timePoint, interpolatedValue);
            }
        }
    }

    /**
     * 向后补全时间范围内的值（从已知点向后推算）
     * 例如：从t2300向后推算到t2400
     */
    private static void completeTimeRangeForward(Map<String, BigDecimal> valueMap, String knownTime, String targetTime) {
        BigDecimal knownValue = valueMap.get(knownTime);
        if (knownValue == null) {
            return;
        }

        // 获取时间点列表
        List<String> timePoints = getTimePointsBetween(knownTime, targetTime);
        if (timePoints.size() <= 1) {
            return;
        }

        // 查找最近的已知值作为参考点
        BigDecimal referenceValue = findNearestKnownValue(valueMap, timePoints, knownTime);
        if (referenceValue == null) {
            // 如果没有其他参考值，使用已知值作为基准，假设变化很小
            referenceValue = knownValue;
        }

        // 计算增量（假设从已知值到参考值是线性变化）
        BigDecimal totalDiff = referenceValue.subtract(knownValue);
        int intervals = timePoints.size() - 1;
        BigDecimal increment = totalDiff.divide(new BigDecimal(intervals), 4, BigDecimal.ROUND_HALF_UP);

        // 从已知点向后补全
        for (int i = 1; i < timePoints.size(); i++) {
            String timePoint = timePoints.get(i);
            if (valueMap.get(timePoint) == null) {
                BigDecimal interpolatedValue = knownValue.add(increment.multiply(new BigDecimal(i)));
                valueMap.put(timePoint, interpolatedValue);
            }
        }
    }

    /**
     * 查找最近的已知值作为参考
     */
    private static BigDecimal findNearestKnownValue(Map<String, BigDecimal> valueMap, List<String> timePoints, String excludeTime) {
        // 查找时间点列表附近的已知值
        String[] nearbyTimes = {"t0215", "t0230", "t0245", "t2245", "t2230", "t2215"};
        for (String time : nearbyTimes) {
            if (!time.equals(excludeTime) && valueMap.get(time) != null) {
                return valueMap.get(time);
            }
        }
        return null;
    }

    /**
     * 处理跨天一致性：确保今日t0000与昨天的t2400值相同
     */
    private static void handleCrossDayConsistency(Date date, Map<Date, Map<String, BigDecimal>> dateMap, Map<String, BigDecimal> valueMap) {
        BigDecimal currentT0000 = valueMap.get("t0000");
        if (currentT0000 != null) {
            // 更新前一天的t2400，使其与今天的t0000相同
            Date previousDay = DateUtil.getMoveDay(date, -1);
            Map<String, BigDecimal> previousMap = dateMap.get(previousDay);
            if (Objects.nonNull(previousMap)) {
                previousMap.put("t2400", currentT0000);
            }
        }

        BigDecimal currentT2400 = valueMap.get("t2400");
        if (currentT2400 != null) {
            // 更新明天的t0000，使其与今天的t2400相同
            Date nextDay = DateUtil.getMoveDay(date, 1);
            Map<String, BigDecimal> nextMap = dateMap.get(nextDay);
            if (Objects.nonNull(nextMap)) {
                nextMap.put("t0000", currentT2400);
            }
        }
    }

    /**
     * 补全指定时间范围内的值，使用等差平均的方式
     */
    private static void completeTimeRange(Map<String, BigDecimal> valueMap, String startTime, String endTime) {
        BigDecimal startValue = valueMap.get(startTime);
        BigDecimal endValue = valueMap.get(endTime);

        // 如果起始或结束值为空，则无法进行补全
        if (startValue == null || endValue == null) {
            return;
        }

        // 获取时间范围内的所有时间点
        List<String> timePoints = getTimePointsBetween(startTime, endTime);

        if (timePoints.size() <= 2) {
            // 如果只有起始和结束点，则不需要补全
            return;
        }

        // 计算每个时间间隔的增量
        BigDecimal totalDiff = endValue.subtract(startValue);
        int intervals = timePoints.size() - 1; // 间隔数 = 点数 - 1
        BigDecimal increment = totalDiff.divide(new BigDecimal(intervals), 4, RoundingMode.HALF_UP);

        // 补全中间的值
        for (int i = 1; i < timePoints.size() - 1; i++) {
            String timePoint = timePoints.get(i);
            if (valueMap.get(timePoint) == null) {
                BigDecimal interpolatedValue = startValue.add(increment.multiply(new BigDecimal(i)));
                valueMap.put(timePoint, interpolatedValue);
            }
        }
    }

    /**
     * 获取两个时间点之间的所有时间点（包括起始和结束点）
     */
    private static List<String> getTimePointsBetween(String startTime, String endTime) {
        List<String> timePoints = new ArrayList<>();

        // 解析起始时间
        int startHour = Integer.parseInt(startTime.substring(1, 3));
        int startMinute = Integer.parseInt(startTime.substring(3, 5));

        // 解析结束时间
        int endHour = Integer.parseInt(endTime.substring(1, 3));
        int endMinute = Integer.parseInt(endTime.substring(3, 5));

        // 生成时间点列表（每15分钟一个点）
        int currentHour = startHour;
        int currentMinute = startMinute;

        while (currentHour < endHour || (currentHour == endHour && currentMinute <= endMinute)) {
            String timePoint = String.format("t%02d%02d", currentHour, currentMinute);
            timePoints.add(timePoint);

            // 增加15分钟
            currentMinute += 15;
            if (currentMinute >= 60) {
                currentMinute = 0;
                currentHour++;
            }

            // 处理跨天情况（例如从23:45到第二天的00:00）
            if (currentHour >= 24) {
                if (endHour == 24 && endMinute == 0) {
                    // 特殊处理 t2400
                    timePoints.add("t2400");
                }
                break;
            }
        }

        return timePoints;
    }

    private static void completion2400(Date date, Map<Date, Map<String, BigDecimal>> dateMap, Map<String, BigDecimal> valueMap) {
        Date moveDay = DateUtil.getMoveDay(date, 1);
        Map<String, BigDecimal> nextMap = dateMap.get(moveDay);
        if (Objects.nonNull(nextMap)) {
            valueMap.put("t2400", nextMap.get("t0000"));
        }
    }
}