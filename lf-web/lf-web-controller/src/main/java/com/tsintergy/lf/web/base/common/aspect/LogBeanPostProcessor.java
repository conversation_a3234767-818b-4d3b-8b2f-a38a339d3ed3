/**
 * Copyright(C),2015‐2021,北京清能互联科技有限公司
 */
package com.tsintergy.lf.web.base.common.aspect;

import com.tsieframework.cloud.security.serviceapi.system.api.SecurityService;
import com.tsieframework.cloud.security.web.businesslog.cuts.LoginLogAspect;
import com.tsieframework.cloud.security.web.businesslog.cuts.OperateLogAspect;
import com.tsieframework.cloud.security.web.common.security.TokenManager;
import com.tsintergy.lf.web.base.common.handler.AddUpdateUserProceedingJoinPointLogHandler;
import com.tsintergy.lf.web.base.common.handler.DeleteUserProceedingJoinPointLogHandler;
import com.tsintergy.lf.web.base.common.handler.GroupPermissionProceedingJoinPointLogHandler;
import com.tsintergy.lf.web.base.common.handler.RolePermissionProceedingJoinPointLogHandler;
import com.tsintergy.lf.web.base.common.handler.ProceedingJoinPointLogHandlerChain;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

/**
 *Description:  替换tsie框架中的日志记录<br>
 *
 *@Author: <EMAIL>
 *@Date: 2021/12/28 19:03
 *@Version: 1.0.0
 */
public class LogBeanPostProcessor implements ApplicationContextAware,BeanPostProcessor {

    ApplicationContext applicationContext;

    @Override
    public Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {
        if (beanName.equals("loginLogAspect")) {
            LoginLogAspect loginLogAspect = (LoginLogAspect) applicationContext.getBean("loginLogAspect");
            CustomLoginLogAspect customLoginLogAspect = new CustomLoginLogAspect();
            customLoginLogAspect.setBusinessLogService(loginLogAspect.getBusinessLogService());
            customLoginLogAspect.setThreadPoolTaskExecutor(loginLogAspect.getThreadPoolTaskExecutor());
            return customLoginLogAspect;
        }
        if (beanName.equals("operateLogAspect")) {
            OperateLogAspect operateLogAspect = (OperateLogAspect) applicationContext.getBean("operateLogAspect");
            CustomOperateLogAspect customOperateLogAspect = new CustomOperateLogAspect();
            customOperateLogAspect.setBusinessLogService(operateLogAspect.getBusinessLogService());
            customOperateLogAspect.setThreadPoolTaskExecutor(operateLogAspect.getThreadPoolTaskExecutor());

            TokenManager tokenManager = (TokenManager) applicationContext.getBean("tokenManager");
            customOperateLogAspect.setTokenManager(tokenManager);

            //添加详细信息记录执行器
            ProceedingJoinPointLogHandlerChain handlerChain = new ProceedingJoinPointLogHandlerChain();
            handlerChain.addJoinPointLogHandler(new AddUpdateUserProceedingJoinPointLogHandler());

            SecurityService securityService = (SecurityService) applicationContext.getBean("securityService");
            handlerChain.addJoinPointLogHandler(new DeleteUserProceedingJoinPointLogHandler(securityService));
            handlerChain.addJoinPointLogHandler(new RolePermissionProceedingJoinPointLogHandler(securityService));
            handlerChain.addJoinPointLogHandler(new GroupPermissionProceedingJoinPointLogHandler(securityService));
            customOperateLogAspect.setProceedingJoinPointLogHandlerChain(handlerChain);

            return customOperateLogAspect;
        }

        return bean;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}