/**
 *   
 *  Copyright (C), 2015‐2020, 北京清能互联科技有限公司  
 *  Author:  EDZ  
 *  Date:  2020/10/28 13:34  
 *  History:  
 *  <author> <time> <version> <desc> 
 */
package com.tsintergy.lf.web.base.report.response;

import com.tsintergy.lf.serviceapi.base.report.dto.ReportUserAccuracyDTO;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.List;

/**  
 * Description: 用户上报准确率 <br> 
 * 
 * <AUTHOR>  
 * @create 2020/10/28  
 * @since 1.0.0  
 */
public class ReportAccuracyResp {

    /**
     * 上报天数
     */
    @ApiModelProperty(value = "上报天数",example = "5")
    private int reportDays = 0;

    @ApiModelProperty(value = "最高准确率",example = "100")
    private BigDecimal maxAccuracy;

    @ApiModelProperty(value = "最低准确率",example = "80")
    private BigDecimal minAccuracy;

    @ApiModelProperty(value = "平均准确率",example = "90")
    private BigDecimal avgAccuracy;

    /**
     * 用户上报准确率信息
     */
    List<ReportUserAccuracyDTO> userReportAccuracy;

    public Integer getReportDays() {
        return reportDays;
    }

    public void setReportDays(Integer reportDays) {
        this.reportDays = reportDays;
    }

    public BigDecimal getMaxAccuracy() {
        return maxAccuracy;
    }

    public void setMaxAccuracy(BigDecimal maxAccuracy) {
        this.maxAccuracy = maxAccuracy;
    }

    public BigDecimal getMinAccuracy() {
        return minAccuracy;
    }

    public void setMinAccuracy(BigDecimal minAccuracy) {
        this.minAccuracy = minAccuracy;
    }

    public BigDecimal getAvgAccuracy() {
        return avgAccuracy;
    }

    public void setAvgAccuracy(BigDecimal avgAccuracy) {
        this.avgAccuracy = avgAccuracy;
    }

    public List<ReportUserAccuracyDTO> getUserReportAccuracy() {
        return userReportAccuracy;
    }

    public void setUserReportAccuracy(List<ReportUserAccuracyDTO> userReportAccuracy) {
        this.userReportAccuracy = userReportAccuracy;
    }
}