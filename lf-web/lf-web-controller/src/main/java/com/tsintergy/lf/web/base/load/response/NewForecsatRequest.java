package com.tsintergy.lf.web.base.load.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2023-05-06
 * @since 1.0.0
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class NewForecsatRequest implements Serializable {
    @ApiModelProperty(value = "开始时间")
    Date startDate;
    @ApiModelProperty(value = "结束日期")
    Date endDate;
    @ApiModelProperty(value = "城市ID")
    private List<String> cityIds;
    @ApiModelProperty(value = "口径ID")
    private List<String> caliberIds;
    @ApiModelProperty(value = "算法ID")
    private List<String> algorithmIds;
    @ApiModelProperty(value = "季度ID")
    private List<String> seasonIds;

    public List<String> getSeasonIds() {
        return seasonIds;
    }

    public void setSeasonIds(List<String> seasonIds) {
        this.seasonIds = seasonIds;
    }

    public List<String> getAlgorithmIds() {
        return algorithmIds;
    }

    public void setAlgorithmIds(List<String> algorithmIds) {
        this.algorithmIds = algorithmIds;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public List<String> getCityIds() {
        return cityIds;
    }

    public void setCityIds(List<String> cityIds) {
        this.cityIds = cityIds;
    }

    public List<String> getCaliberIds() {
        return caliberIds;
    }

    public void setCaliberIds(List<String> caliberIds) {
        this.caliberIds = caliberIds;
    }
}
