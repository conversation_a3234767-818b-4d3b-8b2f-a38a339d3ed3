/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.web.base.analyze.controller;

import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.serviceapi.base.analyze.api.WinterAndSummerLoadAnalysisService;
import com.tsintergy.lf.serviceapi.base.analyze.dto.RequireResponseDTO;
import com.tsintergy.lf.serviceapi.base.analyze.dto.LoadComparisonCurveDTO;
import com.tsintergy.lf.serviceapi.base.analyze.dto.LoadComparisonDTO;
import com.tsintergy.lf.serviceapi.base.analyze.dto.LoadStatisticsDTO;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadHisDTO;
import com.tsintergy.lf.web.base.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description: 冬夏负荷分析 <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/8/22 15:06
 * @Version: 1.0.0
 */

@Api(tags = "冬夏负荷分析")
@RequestMapping("/winterAndSummerAnalyze")
@RestController
public class WinterAndSummerLoadAnalysisController extends BaseController {



    @Autowired
    WinterAndSummerLoadAnalysisService winterAndSummerLoadAnalysisService;


    @RequestMapping(value = "/getLoadComparison", method = RequestMethod.GET)
    @ApiOperation(value = "负荷对比")
    public BaseResp<List<LoadComparisonDTO>> getLoadComparison(String analyzeType,String year,String caliberId,String cityId) {
        if (cityId == null){
            cityId = this.getLoginCityId();
        }
        if (caliberId == null){
            caliberId = this.getCaliberId();
        }
        List<LoadComparisonDTO> loadComparisonDTOList = winterAndSummerLoadAnalysisService.getLoadComparisonDTO(analyzeType,year,caliberId,cityId);
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(loadComparisonDTOList);
        return baseResp;
    }


    @RequestMapping(value = "/getLoadComparisonCurve", method = RequestMethod.GET)
    @ApiOperation(value = "负荷对比曲线")
    public BaseResp<List<LoadComparisonCurveDTO>> getLoadComparisonCurve(String analyzeType,String year,String caliberId,String cityId) {
        if (cityId == null){
            cityId = this.getLoginCityId();
        }
        if (caliberId == null){
            caliberId = this.getCaliberId();
        }
        List<LoadComparisonCurveDTO> loadComparisonCurveDTOS = winterAndSummerLoadAnalysisService.getLoadComparisonCurve(analyzeType,year,caliberId,cityId);
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(loadComparisonCurveDTOS);
        return baseResp;
    }


    @RequestMapping(value = "/getLoadStatistics", method = RequestMethod.GET)
    @ApiOperation(value = "负荷统计")
    public BaseResp<LoadStatisticsDTO> getLoadStatistics(String analyzeType,String year,String caliberId,String cityId) {
        if (cityId == null){
            cityId = this.getLoginCityId();
        }
        if (caliberId == null){
            caliberId = this.getCaliberId();
        }
        LoadStatisticsDTO loadStatistics = winterAndSummerLoadAnalysisService.getLoadStatistics(analyzeType,year,caliberId,cityId);
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(loadStatistics);
        return baseResp;
    }


    @RequestMapping(value = "/getLoadStatisticsCurve", method = RequestMethod.GET)
    @ApiOperation(value = "负荷统计曲线")
    public BaseResp<List<LoadHisDTO>> getLoadStatisticsCurve(String analyzeType,String year,String caliberId,String cityId) {
        if (cityId == null){
            cityId = this.getLoginCityId();
        }
        if (caliberId == null){
            caliberId = this.getCaliberId();
        }
        List<LoadHisDTO> loadHisDTOS = winterAndSummerLoadAnalysisService.getLoadStatisticsCurve(analyzeType,year,caliberId,cityId);
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(loadHisDTOS);
        return baseResp;
    }


    @RequestMapping(value = "/getDemandSideResponse", method = RequestMethod.GET)
    @ApiOperation(value = "需求侧相应信息")
    public BaseResp<List<RequireResponseDTO>> getRequireResponse(String analyzeType,String year,String caliberId,String cityId) {
        if (cityId == null){
            cityId = this.getLoginCityId();
        }
        if (caliberId == null){
            caliberId = this.getCaliberId();
        }
        List<RequireResponseDTO> requireResponseDTOList = winterAndSummerLoadAnalysisService.getRequireResponse(analyzeType,year,caliberId,cityId);
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(requireResponseDTOList);
        return baseResp;
    }
}