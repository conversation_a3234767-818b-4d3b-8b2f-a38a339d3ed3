package com.tsintergy.lf.web.base.datamanage.controller;


import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.serviceapi.base.datamanage.api.PrecisionWeatherService;
import com.tsintergy.lf.serviceapi.base.datamanage.dto.PrecisionResp;
import com.tsintergy.lf.web.base.controller.CommonBaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * 预测准确率查询
 *
 * <AUTHOR>
 * @date 2019-08-27
 */
@Api(tags = "预测准确率查询")
@RestController
@RequestMapping("/precision")
public class PrecisionController extends CommonBaseController {

    @Autowired
    private PrecisionWeatherService precisionWeatherService;

    /**
     * 气象预测准确率---年/月/日查询
     * @param weatherType 气象类型 1=湿度,2=温度,3=降雨量,4=风速 5=实感温度、6=寒湿指数
     * @param dateType 时间类型 1=日 2=月 3=年
     */
    @ApiOperation("气象预测准确率")
    @RequestMapping(value = "/weather", method = RequestMethod.GET)
    public BaseResp<PrecisionResp> getPowerLoadFc(@ApiParam(value = "开始时间")String startDate,
                                    @ApiParam(value = "结束时间")String endDate,
                                    @ApiParam(value = "气象类型")Integer weatherType,
                                    @ApiParam(value = "时间类型")Integer dateType,
                                     @ApiParam(value = "城市id")String cityId) throws Exception {
        PrecisionResp resp = precisionWeatherService.findData(startDate, endDate, weatherType, dateType, cityId);
        if (resp.getDataList().size() == 0) {
            return new BaseResp("T706","查询结果为空");
        }
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(resp);
        return baseResp;
    }


}
