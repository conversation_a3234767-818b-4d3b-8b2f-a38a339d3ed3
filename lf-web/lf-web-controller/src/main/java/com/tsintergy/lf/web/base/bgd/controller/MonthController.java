package com.tsintergy.lf.web.base.bgd.controller;

import com.alibaba.excel.util.CollectionUtils;
import com.alibaba.excel.util.StringUtils;
import com.github.liaochong.myexcel.utils.StringUtil;
import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.serviceapi.base.bgd.api.MonthPageService;
import com.tsintergy.lf.serviceapi.base.bgd.dto.AlgorithmDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.TempCompareDTO;
import com.tsintergy.lf.serviceapi.base.system.api.SettingSystemService;
import com.tsintergy.lf.web.base.controller.CommonBaseController;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RequestMapping("/month")
@RestController
public class MonthController extends CommonBaseController {

    @Autowired
    private MonthPageService monthPageService;

    @Autowired
    private SettingSystemService settingSystemService;


    @ApiOperation("获取气象概况")
    @GetMapping(value = "/weather/info")
    public BaseResp<String> getWeatherInfo(String cityId, Date date) {
        BaseResp baseResp = BaseResp.succResp();
        if (StringUtil.isBlank(cityId)){
            cityId = String.valueOf(Constants.PROVINCE_TYPE);
        }
        String info = null;
        try {
            info = monthPageService.getWeatherInfo(cityId,date);
            baseResp.setData(info);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (StringUtils.isEmpty(info)) {
            return new BaseResp("T200");
        }
        return baseResp;
    }


    @ApiOperation("获取算法")
    @GetMapping(value = "getAlgorithm")
    public BaseResp<List<AlgorithmDTO>> getAlgorithm() {
        BaseResp baseResp = BaseResp.succResp();
        List<AlgorithmDTO> algorithmDTOList = new ArrayList<>(6);
        try {
            algorithmDTOList = settingSystemService.getAlgorithmDTOList();
            baseResp.setData(algorithmDTOList);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (CollectionUtils.isEmpty(algorithmDTOList)) {
            return new BaseResp("T706");
        }
        return baseResp;
    }

    @ApiOperation("气象预测对比")
    @GetMapping(value = "/getWeatherCompare")
    public BaseResp<List<TempCompareDTO>> getWeatherCompare(String cityId,Date date,Integer type) {
        BaseResp baseResp = BaseResp.succResp();
        List<TempCompareDTO> tempCompareDTOList = new ArrayList<>(6);
        try {
            tempCompareDTOList = monthPageService.getTempCompareDTOList(cityId, date, type);
            baseResp.setData(tempCompareDTOList);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (CollectionUtils.isEmpty(tempCompareDTOList)) {
            return new BaseResp("T706");
        }
        return baseResp;
    }
}
