/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司
 * Author:  ThinkPad
 * Date:  2019/3/28 19:02
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.ultra.request;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2023/1/10
 * @since 1.0.0
 */
@Data
public class UltraForecastRequest implements Serializable {

    Date startDate;

    Date endDate;

    private String cityId;

    private String caliberId;

    private String algorithmId;


    /**
     * 超短期实施页面专用  5分钟间隔  15分钟间隔
     */
    private Integer timeSpan;

    @ApiModelProperty(value = "目标日")
    private Date targetDay;
}