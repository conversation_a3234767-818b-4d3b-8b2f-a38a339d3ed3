package com.tsintergy.lf.web.base.util.ultra;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.fasterxml.jackson.databind.ser.Serializers.Base;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.ColumnUtil;
import com.tsintergy.lf.core.util.VslfDateUtil;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityFcService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityHisService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisDO;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

/**
 * 数据导入监听类
 *
 * <AUTHOR>
 */
public class ExcelMoreDayWeather96Listener extends AnalysisEventListener<ExcelMoreDayWeatherDataHis96> {

    private List<ExcelMoreDayWeatherDataHis96> list = new ArrayList();

    private WeatherCityHisService weatherCityHisService;
    private WeatherCityFcService weatherCityFcService;

    private String cityId;

    private String userId;

    private String caliberId;


    public ExcelMoreDayWeather96Listener(WeatherCityHisService weatherCityHisService, WeatherCityFcService weatherCityFcService, String cityId, String userId,
        String caliberId) {
        this.weatherCityHisService = weatherCityHisService;
        this.weatherCityFcService = weatherCityFcService;
        this.cityId = cityId;
        this.userId = userId;
        this.caliberId = caliberId;
    }

    /**
     * 逐行解析数据
     */
    @Override
    public void invoke(ExcelMoreDayWeatherDataHis96 data, AnalysisContext context) {
        list.add(data);
    }

    /**
     * 逐行的返回头信息
     */
    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {

    }


    /**
     * 所有数据解析完成后的回调函数
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context){
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<WeatherCityFcDO> re = new ArrayList<>();
        list.forEach(e -> {
            WeatherCityFcDO weatherCityHisDO = new WeatherCityFcDO();
            BeanUtils.copyProperties(e, weatherCityHisDO);
            weatherCityHisDO.setCityId(cityId);
            weatherCityHisDO.setType(Integer.valueOf(e.getType()));
            Date date = VslfDateUtil.getDate(e.getDate(), "yyyy-MM-dd");
            weatherCityHisDO.setDate(new java.sql.Date(date.getTime()));
            weatherCityHisDO.setId(e.getId());
            List<BigDecimal> bigDecimals = BasePeriodUtils.toList(e, Constants.LOAD_CURVE_POINT_NUM,
                true);
            try {
                Map<String, BigDecimal> stringBigDecimalMap = ColumnUtil.listToMap(bigDecimals,
                    Constants.WEATHER_CURVE_START_WITH_ZERO);
                BasePeriodUtils.setAllFiled(weatherCityHisDO,stringBigDecimalMap);
                weatherCityHisDO.setCreatetime(new Timestamp(System.currentTimeMillis()));
                weatherCityHisDO.setUpdatetime(new Timestamp(System.currentTimeMillis()));
                re.add(weatherCityHisDO);
            }catch (Exception ex){
                ex.printStackTrace();

            }
        });
        weatherCityFcService.doSaveOrUpdateList(re);
    }

}
