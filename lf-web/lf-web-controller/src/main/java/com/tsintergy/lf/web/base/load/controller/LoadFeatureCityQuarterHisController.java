package com.tsintergy.lf.web.base.load.controller;

import com.tsieframework.core.base.exception.TsieExceptionUtils;
import com.tsieframework.core.base.format.datetime.DateUtils;
import com.tsieframework.core.base.web.BaseResp;
import com.tsieframework.util.compatible.BigDecimalUtils;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.base.api.CityService;
import com.tsintergy.lf.serviceapi.base.base.api.LoadMonthYearFeatureService;
import com.tsintergy.lf.serviceapi.base.base.dto.LoadMonthFeatureDTO;
import com.tsintergy.lf.serviceapi.base.load.api.LoadCityHisService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityDayHisService;
import com.tsintergy.lf.serviceapi.base.load.api.LoadFeatureCityQuarterHisService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityDayHisDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCityQuarterHisDO;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherCityHisService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityDayHisService;
import com.tsintergy.lf.serviceapi.base.weather.api.WeatherFeatureCityQuarterHisService;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayHisDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityQuarterHisDO;
import com.tsintergy.lf.web.base.load.request.LoadAnalyseRequest;
import com.tsintergy.lf.web.base.load.response.FeatureAnalyseResp;
import com.tsintergy.lf.web.base.load.response.LoadWeatherHisYearMonthResp;
import com.tsintergy.lf.web.base.load.response.YearMonthFeatureAnalyseResp;
import com.tsintergy.lf.web.base.load.response.YearMonthFeatureStatisticsResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 季度负荷特性
 *
 * @author: wangh
 **/
@RequestMapping("/character/quarter")
@RestController
@Api(tags = "城市特征季度历史控制器")
public class LoadFeatureCityQuarterHisController extends BaseLoadFeatureController {

    @Autowired
    private LoadCityHisService loadCityHisService;

    @Autowired
    private LoadFeatureCityQuarterHisService loadFeatureCityQuarterHisService;

    @Autowired
    private WeatherCityHisService weatherCityHisService;

    @Autowired
    private WeatherFeatureCityQuarterHisService weatherFeatureCityQuarterHisService;

    @Autowired
    private CityService cityService;

    @Autowired
    private LoadFeatureCityDayHisService loadFeatureCityDayHisService;

    @Autowired
    private WeatherFeatureCityDayHisService weatherFeatureCityDayHisService;

    @Autowired
    private LoadMonthYearFeatureService loadMonthYearFeatureService;

    @RequestMapping(value = "/power_K", method = RequestMethod.POST)
    @ApiOperation("获取季度负荷图表")
    public BaseResp<List<FeatureAnalyseResp>> getQuarterLoadKChart(@RequestBody LoadAnalyseRequest monthRequest) throws Exception {
        //参数检查 如果时间为空，结束时间在系统时间当前时间，开始时间往前推2个月
        this.checkParam(monthRequest);
        String endYear = DateUtil.getYearByDate(this.getSystemDate());
        String endMonth = DateUtil.getMonthByDate(this.getSystemDate()).substring(5);
        String startYear = DateUtil.getYearByDate(DateUtils.addMonths(this.getSystemDate(), -2));
        String startMonth = DateUtil.getMonthByDate(DateUtils.addMonths(this.getSystemDate(), -2)).substring(5);
        String startYQ = startYear + "-" + startMonth;
        String endYQ = endYear + "-" + endMonth;
        if (monthRequest.getStartDate() != null) {
            startYQ = monthRequest.getStartDate();
        }
        if (monthRequest.getEndDate() != null) {
            endYQ = monthRequest.getEndDate();
        }

        String weatherCityId = cityService.findWeatherCityId(monthRequest.getCityId());
        String caliberId = getCaliberId();
        //季度特性
        List<LoadFeatureCityQuarterHisDO> quarterHisLoadDOS =
                loadFeatureCityQuarterHisService.getLoadFeatureCityQuarterHisVOS(monthRequest.getCityId(), startYQ, endYQ, caliberId);
        List<WeatherFeatureCityQuarterHisDO> weatherFeatureCityQuarterHisDOS =
                weatherFeatureCityQuarterHisService.getListWeatherFeatureCityQuarterHisStatDOS(weatherCityId, startYear, endYear);
        if (CollectionUtils.isEmpty(quarterHisLoadDOS)) {
            throw TsieExceptionUtils.newBusinessException("T706");
        }
        //将气象的日期和降雨量放到map中
        Map<String, BigDecimal> rainfallMap = new HashMap<>();
        //将气象的日期和最高温放到map中
        Map<String, BigDecimal> highestTemperatureMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(weatherFeatureCityQuarterHisDOS)) {
            Map<String, List<WeatherFeatureCityQuarterHisDO>> weatherFeatureYearMap = weatherFeatureCityQuarterHisDOS
                    .stream().collect(Collectors.groupingBy(WeatherFeatureCityQuarterHisDO::getYear));

            weatherFeatureYearMap.forEach((year, weatherFeatureCityQuarterHisStatDOList) -> {
                Map<String, BigDecimal> tempRainfallMap = weatherFeatureCityQuarterHisStatDOList.stream().collect(
                        Collectors.toMap(x -> x.getYear() + "-" + x.getQuarter(),
                                weather -> weather.getRainfall() == null ? BigDecimal.ZERO : weather.getRainfall()));
                rainfallMap.putAll(tempRainfallMap);

                Map<String, BigDecimal> highestTemperatureDateMap = weatherFeatureCityQuarterHisStatDOList.stream().collect(
                        Collectors.toMap(x -> x.getYear() + "-" + x.getQuarter(),
                                WeatherFeatureCityQuarterHisDO::getHighestTemperature));
                highestTemperatureMap.putAll(highestTemperatureDateMap);
            });
        }
        Map<String, List<LoadFeatureCityQuarterHisDO>> mapByDate = quarterHisLoadDOS.stream()
                .collect(Collectors.groupingBy(dp -> dp.getYear() + "-" + dp.getQuarter()));

        //构建vo
        List<YearMonthFeatureAnalyseResp> resultList = new ArrayList<>();
        quarterHisLoadDOS.forEach(powerFeatureCityDayHisStatDO -> {
            YearMonthFeatureAnalyseResp resp = new YearMonthFeatureAnalyseResp();
            BeanUtils.copyProperties(powerFeatureCityDayHisStatDO, resp);
            resp.setDate(powerFeatureCityDayHisStatDO.getYear() + "年" + powerFeatureCityDayHisStatDO.getQuarter() + "季度");
            //获取前三季度的出力数据
            List<LoadFeatureCityQuarterHisDO> firstData = mapByDate.get(powerFeatureCityDayHisStatDO.getYear() + "-" + powerFeatureCityDayHisStatDO.getQuarter());
            List<LoadFeatureCityQuarterHisDO> secondData = mapByDate.get(powerFeatureCityDayHisStatDO.getYear() + "-" + powerFeatureCityDayHisStatDO.getQuarter());
            List<LoadFeatureCityQuarterHisDO> thirdData = mapByDate.get(powerFeatureCityDayHisStatDO.getYear() + "-" + powerFeatureCityDayHisStatDO.getQuarter());
            if (firstData != null && secondData != null && thirdData != null) {
                BigDecimal add = BigDecimalUtils.add(firstData.get(0).getAveLoad(), secondData.get(0).getAveLoad());
                BigDecimal add1 = BigDecimalUtils.add(add, thirdData.get(0).getAveLoad());
                BigDecimal divide = BigDecimalUtils.divide(add1, new BigDecimal(3), 4);
                resp.setAvgPeriod(divide);
            }
            resp.setAvgLoad(powerFeatureCityDayHisStatDO.getAveLoad());
            resp.setRain(rainfallMap.get(
                    powerFeatureCityDayHisStatDO.getYear() + "-" + powerFeatureCityDayHisStatDO.getQuarter()));
            resp.setHighestTemperature(highestTemperatureMap.get(
                    powerFeatureCityDayHisStatDO.getYear() + "-" + powerFeatureCityDayHisStatDO.getQuarter()));

            resultList.add(resp);
        });

        if (resultList.size() < 1) {
            throw TsieExceptionUtils.newBusinessException("T706");
        }
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(resultList);
        return baseResp;
    }

    /**
     * 特性分析-季度特性统计查询
     */
    @ApiOperation("季度特性统计查询")
    @RequestMapping(value = "/statisticsFeatureCity", method = RequestMethod.POST)
    public BaseResp<YearMonthFeatureStatisticsResp> statisticsFeatureCity(@RequestBody LoadAnalyseRequest loadAnalyseRequest) throws Exception {
        checkParam(loadAnalyseRequest);
        Date date = this.getSystemDate();
        String dateYQ = loadAnalyseRequest.getDate() != null ? loadAnalyseRequest.getDate() : DateUtil.getQuarterByDate(date);
        String caliberId = getCaliberId();
        String year = dateYQ.substring(0, 4);
        String lastYear = String.valueOf(Integer.parseInt(year) - 1);
        String quarter = dateYQ.substring(5);

        LoadFeatureCityQuarterHisDO loadQuarter = loadFeatureCityQuarterHisService
                .getLoadFeatureCityQuarterHisVO(loadAnalyseRequest.getCityId(), year, quarter, caliberId);
        YearMonthFeatureStatisticsResp yearMonthFeatureStatisticsResp = new YearMonthFeatureStatisticsResp();
        if (loadQuarter != null) {
            yearMonthFeatureStatisticsResp.setAvgloadGradient(loadQuarter.getLoadGradient());
            yearMonthFeatureStatisticsResp.setAvgLoad(loadQuarter.getAveLoad());
            yearMonthFeatureStatisticsResp.setMaxLoad(loadQuarter.getMaxLoad());
            yearMonthFeatureStatisticsResp.setMinLoad(loadQuarter.getMinLoad());
            yearMonthFeatureStatisticsResp.setTotalEnergy(loadQuarter.getEnergy());
            yearMonthFeatureStatisticsResp.setDifferent(loadQuarter.getDifferent());
            yearMonthFeatureStatisticsResp.setGradient(loadQuarter.getGradient());
            yearMonthFeatureStatisticsResp.setDate(year + "年" + quarter + "季度");
        }
        LoadFeatureCityQuarterHisDO lastQuarter = loadFeatureCityQuarterHisService
                .getLoadFeatureCityQuarterHisVO(loadAnalyseRequest.getCityId(), lastYear, quarter, caliberId);
        if (lastQuarter != null) {
            yearMonthFeatureStatisticsResp.setLastLoadGradient(lastQuarter.getLoadGradient());
            yearMonthFeatureStatisticsResp.setLastAvgLoad(lastQuarter.getAveLoad());
            yearMonthFeatureStatisticsResp.setLastMaxLoad(lastQuarter.getMaxLoad());
            yearMonthFeatureStatisticsResp.setLastMinLoad(lastQuarter.getMinLoad());
            yearMonthFeatureStatisticsResp.setLastDifferent(lastQuarter.getDifferent());
            yearMonthFeatureStatisticsResp.setLastGradient(lastQuarter.getGradient());
            yearMonthFeatureStatisticsResp.setLastTotalEnergy(lastQuarter.getEnergy());
        }
        Date date2 = DateUtil.getDate(loadAnalyseRequest.getDate(), "yyyy-MM");
        Date first = DateUtil.getFirstDayOfMonth(date2);
        Date last1 = DateUtil.getLastDayOfMonth(date2);
        Date date1 = DateUtils.addYears(first, -1);
        Date date3 = DateUtils.addYears(last1, -1);
        List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisVOS = loadFeatureCityDayHisService.findLoadFeatureCityDayHisVOS(
                loadAnalyseRequest.getCityId(), first, last1, getCaliberId(loadAnalyseRequest.getCityId()));
        List<LoadFeatureCityDayHisDO> loadFeatureCityDayHisVOS1 = loadFeatureCityDayHisService.findLoadFeatureCityDayHisVOS(
                loadAnalyseRequest.getCityId(), date1, date3, getCaliberId(loadAnalyseRequest.getCityId()));
        List<BigDecimal> bigDecimals = new ArrayList<>();
        List<BigDecimal> bigDecimals1 = new ArrayList<>();
        List<BigDecimal> bigDecimals2 = new ArrayList<>();
        List<BigDecimal> bigDecimals3 = new ArrayList<>();
        List<BigDecimal> bigDecimals4 = new ArrayList<>();
        List<BigDecimal> bigDecimals5 = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(loadFeatureCityDayHisVOS)) {
            for (LoadFeatureCityDayHisDO loadFeatureCityDayHisVO : loadFeatureCityDayHisVOS) {
                BigDecimal loadGradient = loadFeatureCityDayHisVO.getLoadGradient();
                bigDecimals.add(loadGradient);
                BigDecimal different = loadFeatureCityDayHisVO.getDifferent();
                bigDecimals2.add(different);
                BigDecimal gradient = loadFeatureCityDayHisVO.getGradient();
                bigDecimals4.add(gradient);
            }
        }
        if (CollectionUtils.isNotEmpty(bigDecimals)) {
            BigDecimal bigDecimal = com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(
                    bigDecimals);
            yearMonthFeatureStatisticsResp.setAvgloadGradient(bigDecimal);
        }
        if (CollectionUtils.isNotEmpty(loadFeatureCityDayHisVOS1)) {
            for (LoadFeatureCityDayHisDO loadFeatureCityDayHisVO : loadFeatureCityDayHisVOS1) {
                BigDecimal loadGradient = loadFeatureCityDayHisVO.getLoadGradient();
                bigDecimals1.add(loadGradient);
                BigDecimal different = loadFeatureCityDayHisVO.getDifferent();
                bigDecimals3.add(different);
                BigDecimal gradient = loadFeatureCityDayHisVO.getGradient();
                bigDecimals5.add(gradient);
            }
        }
        if (CollectionUtils.isNotEmpty(bigDecimals1)) {
            BigDecimal bigDecimal = com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(
                    bigDecimals1);
            yearMonthFeatureStatisticsResp.setLastLoadGradient(bigDecimal);
        }
        if (CollectionUtils.isNotEmpty(bigDecimals2)) {
            BigDecimal bigDecimal = com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(
                    bigDecimals2);
            yearMonthFeatureStatisticsResp.setDifferent(bigDecimal);
        }
        if (CollectionUtils.isNotEmpty(bigDecimals3)) {
            BigDecimal bigDecimal = com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(
                    bigDecimals3);
            yearMonthFeatureStatisticsResp.setLastDifferent(bigDecimal);
        }
        if (CollectionUtils.isNotEmpty(bigDecimals4)) {
            BigDecimal bigDecimal = com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(
                    bigDecimals4);
            yearMonthFeatureStatisticsResp.setGradient(bigDecimal);
        }
        if (CollectionUtils.isNotEmpty(bigDecimals5)) {
            BigDecimal bigDecimal = com.tsintergy.lf.serviceapi.base.common.enumeration.BigDecimalUtils.listAvg(
                    bigDecimals5);
            yearMonthFeatureStatisticsResp.setLastGradient(bigDecimal);
        }
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(yearMonthFeatureStatisticsResp);
        return baseResp;
    }

    /**
     * 特性分析-季度历史曲线
     */
    @ApiOperation("季度历史曲线")
    @RequestMapping(value = "/hisPowerAndRain", method = RequestMethod.POST)
    public BaseResp<LoadWeatherHisYearMonthResp> getQuarterHisPowerAndRain(@RequestBody LoadAnalyseRequest loadAnalyseRequest) throws Exception {
        checkParam(loadAnalyseRequest);
        Date date = this.getSystemDate();
        String year = DateUtil.getYearByDate(date);
        String quarter = DateUtil.getQuarterByDate(date).substring(5);
        if (loadAnalyseRequest.getDate() != null) {
            year = loadAnalyseRequest.getDate().substring(0, 4);
            quarter = loadAnalyseRequest.getDate().substring(5);
        }
        Date startDate = DateUtil.getFirstDayDateOfOfQuarter(Integer.parseInt(year), Integer.parseInt(quarter));
        Date endDate = DateUtil.getLastDayOfQuarter(Integer.parseInt(year), Integer.parseInt(quarter));

        //获取季度负荷特性
        List<LoadFeatureCityDayHisDO> loadList = loadFeatureCityDayHisService
                .findLoadFeatureCityDayHisDOS(loadAnalyseRequest.getCityId(), startDate, endDate,
                        getCaliberId(loadAnalyseRequest.getCityId()));
        if (CollectionUtils.isEmpty(loadList)) {
            throw TsieExceptionUtils.newBusinessException("T706");
        }
        List<Date> dateList = DateUtil.getListBetweenDay(startDate, endDate);
        Map<java.sql.Date, LoadFeatureCityDayHisDO> collect = loadList.stream()
                .collect(Collectors.toMap(LoadFeatureCityDayHisDO::getDate, Function.identity(), (key1, key2) -> key2));
        List<LoadFeatureCityDayHisDO> resultList = new ArrayList<>();
        for (Date oneDay : dateList) {
            LoadFeatureCityDayHisDO hisDO = collect.get(new java.sql.Date(oneDay.getTime()));
            resultList.add(hisDO);
        }
        List<BigDecimal> minPower = resultList.stream()
                .map(load -> load == null ? null : load.getMinLoad()).collect(Collectors.toList());
        List<BigDecimal> avePower = resultList.stream()
                .map(load -> load == null ? null : load.getAveLoad()).collect(Collectors.toList());
        List<BigDecimal> loadGradient = resultList.stream()
                .map(load -> load == null ? null : load.getLoadGradient()).collect(Collectors.toList());
        List<BigDecimal> maxPower = resultList.stream()
                .map(load -> load == null ? null : load.getMaxLoad()).collect(Collectors.toList());
        LoadWeatherHisYearMonthResp powerWeatherHisYearMonthVO = new LoadWeatherHisYearMonthResp();
        powerWeatherHisYearMonthVO.setAveLoad(avePower);
        powerWeatherHisYearMonthVO.setLoadGradient(loadGradient);
        powerWeatherHisYearMonthVO.setMaxLoad(maxPower);
        powerWeatherHisYearMonthVO.setMinLoad(minPower);

        //查询气象特性数据
        List<WeatherFeatureCityDayHisDO> weatherFeatureCityDayHisStatDOS = weatherFeatureCityDayHisService
                .listWeatherFeatureCityDayHisDO(loadAnalyseRequest.getCityId(), startDate, endDate);
        if (!CollectionUtils.isEmpty(weatherFeatureCityDayHisStatDOS)) {
            Map<java.sql.Date, WeatherFeatureCityDayHisDO> weatherCollect = weatherFeatureCityDayHisStatDOS.stream()
                    .collect(Collectors.toMap(WeatherFeatureCityDayHisDO::getDate, Function.identity(), (key1, key2) -> key2));
            List<WeatherFeatureCityDayHisDO> weatherResultList = new ArrayList<>();
            for (Date oneDay : dateList) {
                WeatherFeatureCityDayHisDO weatherDO = weatherCollect.get(new java.sql.Date(oneDay.getTime()));
                weatherResultList.add(weatherDO);
            }
            //获取月降雨量
            List<BigDecimal> rain = weatherResultList.stream()
                    .map(weather -> weather == null ? null : weather.getRainfall()).collect(Collectors.toList());

            //获取月温度
            List<BigDecimal> highestTemperature = weatherResultList.stream()
                    .map(weather -> weather == null ? null : weather.getHighestTemperature()).collect(Collectors.toList());
            powerWeatherHisYearMonthVO.setRain(rain);
            powerWeatherHisYearMonthVO.setHighestTemperature(highestTemperature);
        }
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(powerWeatherHisYearMonthVO);
        return baseResp;
    }

    /**
     * 特性分析-季度历史曲线
     */
    @ApiOperation("季度历史负荷表格")
    @RequestMapping(value = "/hisQuartersFeature", method = RequestMethod.GET)
    public BaseResp<List<LoadMonthFeatureDTO>> getHisQuartersFeature(String startDate, String endDate,
                                                                     String cityId, String caliberId) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        if (caliberId == null) {
            caliberId = getCaliberId(cityId);
        }
        List<LoadMonthFeatureDTO> hisMonthFeature = loadMonthYearFeatureService.getHisQuartersFeature(startDate, endDate,
                cityId, caliberId);
        baseResp.setData(hisMonthFeature);
        return baseResp;
    }

    private String getCaliberId(String cityId) {
        if (Constants.PROVINCE_TYPE == Integer.parseInt(cityId)) {
            return Constants.CALIBER_ID_BG_QW;
        } else {
            return Constants.CALIBER_ID_BG_DS;
        }
    }
}
