/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.web.base.trade.controller;

import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.serviceapi.base.trade.api.TradeService;
import com.tsintergy.lf.serviceapi.base.trade.dto.*;
import com.tsintergy.lf.web.base.controller.BaseController;
import com.tsintergy.lf.web.base.trade.request.TradeRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * Description:  <br>
 *行业
 * @Author: <EMAIL>
 * @Date: 2022/9/2 15:20
 * @Version: 1.0.0
 */

@RequestMapping("/trade")
@RestController
@Api(tags = "行业负荷查询")
public class TradeController extends BaseController {

    @Autowired
    TradeService tradeService;


    @GetMapping("/getTradeInfo")
    @ApiOperation("查询行业信息")
    public BaseResp<String> getTradeInfo (String cityId,String name) throws Exception{
        TradeInfoDTO tradeInfoDTO = tradeService.getTradeInfo(cityId,name);
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(tradeInfoDTO);
        return baseResp;
    }


    @PostMapping("/getTradeCurve")
    @ApiOperation("查询行业负荷曲线")
    public BaseResp<List<TradeCurveDTO>> getTradeCurve (@RequestBody TradeRequest tradeRequest) throws Exception{
        List<TradeCurveDTO> values = tradeService.getTradeCurve(tradeRequest.getCityId(),tradeRequest.getStartDate(),tradeRequest.getEndDate(),tradeRequest.getCodeIds());
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(values);
        return baseResp;
    }


    @GetMapping("/getTradeElectricityProportion")
    @ApiOperation("查询行业用电量占比")
    public BaseResp<List<TradeElectricityProportionDTO>> getTradeElectricityProportion(String cityId,Date startDate, Date endDate) throws Exception{

        List<TradeElectricityProportionDTO> values = tradeService.getTradeElectricityProportion(cityId,startDate,endDate);
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(values);
        return baseResp;
    }

    @PostMapping("/getTradeFeature")
    @ApiOperation("查询行业特性")
    public BaseResp<List<TradeFeatureDTO>> getTradeFeature(@RequestBody TradeRequest tradeRequest) throws Exception {
        List<TradeFeatureDTO> values = tradeService.getTradeFeature(tradeRequest.getCityId(), tradeRequest.getStartDate(), tradeRequest.getEndDate(), tradeRequest.getCodeIds());
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(values);
        return baseResp;
    }

    @GetMapping("/getDayIndustryFeature")
    @ApiOperation("查询全行业日负荷特性")
    public BaseResp<List<IndustryLoadMaxFeatureDTO>> getDayIndustryFeature(String cityId, Date startDate, Date endDate,
                                                                           @ApiParam(name = "0-产业 1-行业") String dataType,
                                                                           @ApiParam(name = "0-行业最大负荷时刻数据 1-调度最大负荷时刻数据") String periodType) {
        List<IndustryLoadMaxFeatureDTO> industryLoadFeatureDTOS = tradeService.getDayIndustryFeature(cityId, startDate, endDate, dataType, periodType);
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(industryLoadFeatureDTOS);
        return baseResp;
    }

    @GetMapping("/getMonthIndustryFeature")
    @ApiOperation("查询全行业月负荷特性")
    public BaseResp<List<IndustryLoadMaxFeatureDTO>> getMonthIndustryFeature(String cityId, Date startDate, Date endDate,
                                                                             @ApiParam(name = "0-分产业 1-分行业") String dataType,
                                                                             @ApiParam(name = "0-行业最大负荷时刻数据 1-调度最大负荷时刻数据") String periodType) {
        List<IndustryLoadMaxFeatureDTO> industryLoadFeatureDTOS = tradeService.getMonthIndustryFeature(cityId, startDate, endDate, dataType, periodType);
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(industryLoadFeatureDTOS);
        return baseResp;
    }

    @GetMapping("/getMaxDayIndustryFeature")
    @ApiOperation("获取日期范围内的行业最大日负荷特性数据")
    public BaseResp<List<IndustryLoadMaxFeatureDTO>> getMaxDayIndustryFeature(String tradeCode, String cityId, Date startDate, Date endDate,
                                                                              @ApiParam(name = "0-产业 1-行业") String dataType,
                                                                              @ApiParam(name = "0-行业最大负荷时刻数据 1-调度最大负荷时刻数据") String periodType) {
        List<IndustryLoadMaxFeatureDTO> industryLoadFeatureDTOS = tradeService.getMaxDayIndustryFeature(tradeCode, cityId, startDate, endDate, dataType, periodType);
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(industryLoadFeatureDTOS);
        return baseResp;
    }

    @GetMapping("/getMaxMonthIndustryFeature")
    @ApiOperation("获取日期范围内的行业最大月负荷特性数据")
    public BaseResp<List<IndustryLoadMaxFeatureDTO>> getMaxMonthIndustryFeature(String tradeCode, String cityId, Date startDate, Date endDate,
                                                                                @ApiParam(name = "0-分产业 1-分行业") String dataType,
                                                                                @ApiParam(name = "0-行业最大负荷时刻数据 1-调度最大负荷时刻数据") String periodType) {
        List<IndustryLoadMaxFeatureDTO> industryLoadFeatureDTOS = tradeService.getMaxMonthIndustryFeature(tradeCode, cityId, startDate, endDate, dataType, periodType);
        BaseResp baseResp = BaseResp.succResp();
        baseResp.setData(industryLoadFeatureDTOS);
        return baseResp;
    }
}