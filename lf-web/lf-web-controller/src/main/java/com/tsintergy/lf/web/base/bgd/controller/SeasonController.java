package com.tsintergy.lf.web.base.bgd.controller;

import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.serviceapi.base.bgd.api.SeasonForecastModelService;
import com.tsintergy.lf.serviceapi.base.bgd.dto.TenDaysOfMonthFeatureDTO;
import io.swagger.annotations.ApiOperation;
import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description: TODO <br>
 *
 * <AUTHOR>
 * @create 2023-02-17
 * @since 1.0.0
 */
@RequestMapping("/season")
@RestController
public class SeasonController {

    @Autowired
    SeasonForecastModelService seasonForecastModelService;

    @ApiOperation("获取季负荷预测对比")
    @GetMapping(value = "/season/getMonthLoadFcCompare")
    public BaseResp<List<TenDaysOfMonthFeatureDTO>> getMonthLoadFcCompare(String cityId, String caliberId, Date date,
        String season, String algorithmId, String startStr, String endStr) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<TenDaysOfMonthFeatureDTO> monthFeatureDTOS = seasonForecastModelService.getMonthLoadFcCompare(cityId,
            getCaliberId(cityId), date, season, algorithmId, startStr, endStr);
        baseResp.setData(monthFeatureDTOS);
        return baseResp;
    }

    private String getCaliberId(String cityId){
        if (Constants.PROVINCE_TYPE == Integer.parseInt(cityId)){
            return Constants.CALIBER_ID_BG_QW;
        }else{
            return Constants.CALIBER_ID_BG_DS;
        }
    }

}
