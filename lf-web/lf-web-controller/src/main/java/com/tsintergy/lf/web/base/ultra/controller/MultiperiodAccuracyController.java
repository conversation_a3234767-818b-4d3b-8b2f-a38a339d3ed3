package com.tsintergy.lf.web.base.ultra.controller;

import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.serviceapi.base.ultra.api.UltraLoadAccuracyMonthService;
import com.tsintergy.lf.serviceapi.base.ultra.dto.AccuracyMonthDTO;
import com.tsintergy.lf.serviceapi.base.ultra.dto.AccuracyUltraData;
import com.tsintergy.lf.serviceapi.base.ultra.dto.MultiPeriodAccuracyDTO;
import com.tsintergy.lf.serviceapi.base.ultra.api.UltraLoadAccuracyDayService;
import com.tsintergy.lf.web.base.ultra.request.CommonDayRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.annotation.Resource;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description
 * @Date 2023/8/415 18:40
 * <AUTHOR>
 **/
@Api(value = "数据管理-负荷数据", tags = "[超短期模块][多时段准确率]")
@RestController
@RequestMapping("/ultra/multiperiod/")
public class MultiperiodAccuracyController extends UltraBaseLoadFeatureController {

    @Resource
    private UltraLoadAccuracyDayService loadAccuracyDayService;

    private final Logger logger = LogManager.getLogger(
        MultiperiodAccuracyController.class);

    @Resource
    private UltraLoadAccuracyMonthService ultraLoadAccuracyMonthService;

    private final Integer MONTH_TYPE = 1;

    /**
     * 查询年准确率列表
     */
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    @ApiOperation(value = "查询年准确率列表")
    public BaseResp<List<AccuracyMonthDTO>> getLoadHistory(@Validated CommonDayRequest commonRequest, String algorithmId, String startTime, String endTime) throws Exception {
        AccuracyUltraData data = new AccuracyUltraData();

        BeanUtils.copyProperties(commonRequest, data);
        data.setAlgorithmId(algorithmId);
        data.setStartTime(checkTime(startTime));
        data.setEndTime(checkTime(endTime));
        data.setCaliberId(getCaliberId());
        MultiPeriodAccuracyDTO periodAccuracy = loadAccuracyDayService.getMultiPeriodAccuracy(data);
        if (periodAccuracy == null) {
            return new BaseResp("T706");
        }
        //月度时间范围给我参数固定为当月第一天 格式 yyyy-MM-dd
        BaseResp resp = BaseResp.succResp("查询成功");
        resp.setData(periodAccuracy);
        return resp;
    }


}
