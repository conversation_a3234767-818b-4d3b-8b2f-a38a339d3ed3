package com.tsintergy.lf.web.base.bgd.controller;

import com.tsieframework.core.base.web.BaseResp;
import com.tsintergy.lf.core.util.DateUtil;
import com.tsintergy.lf.serviceapi.base.bgd.api.CityAllReportService;
import com.tsintergy.lf.serviceapi.base.bgd.dto.ShortReportResultDTO;
import io.swagger.annotations.ApiOperation;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description: 地市上报汇总
 *
 * <AUTHOR>
 * @create 2023-06-29
 * @since 1.0.0
 */
@RestController
@RequestMapping("/city")
public class CityReportController {

    @Autowired
    private CityAllReportService cityAllReportService;

    @ApiOperation("短期上报汇总")
    @GetMapping(value = "/shortReport")
    public BaseResp<List<ShortReportResultDTO>> getShortReport(String startStr, String endStr, String typeName,
        String caliberName) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<ShortReportResultDTO> result = new ArrayList<>(16);
        List<Integer> list = Arrays.asList(1, 2);
        List<String> strings = Arrays.asList("最大负荷", "最小负荷", "最小腰荷", "日电量");
        for (Integer integer : list) {
            for (String string : strings) {
                List<ShortReportResultDTO> shortReport = cityAllReportService.getShortReport(startStr, endStr, typeName, caliberName,
                    integer, string);
                result.addAll(shortReport);
            }
        }
        baseResp.setData(result);
        return baseResp;
    }

    @ApiOperation("短期上报气象汇总")
    @GetMapping(value = "/weather")
    public BaseResp<List<ShortReportResultDTO>> getShortReport(String startStr, String endStr) throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<ShortReportResultDTO> shortReport = cityAllReportService.getShortWeather(startStr, endStr);
        baseResp.setData(shortReport);
        return baseResp;
    }

    @ApiOperation("月度上报汇总")
    @GetMapping(value = "/monthReport")
    public BaseResp<List<ShortReportResultDTO>> getMonthReport(Date startStr, String type)
        throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<ShortReportResultDTO> result = new ArrayList<>(16);
        String dateStr = DateUtil.getDateToStrFORMAT(startStr, "yyyy-MM");
        List<Integer> list = Arrays.asList(1, 2);
        List<String> strings = Arrays.asList("最大负荷", "最小负荷", "最小腰荷", "总电量");
        for (Integer integer : list) {
            for (String string : strings) {
                List<ShortReportResultDTO> monthReport = cityAllReportService.getMonthReport(dateStr, type, integer, string);
                result.addAll(monthReport);
            }
        }
        baseResp.setData(result);
        return baseResp;
    }

    @ApiOperation("冬夏上报汇总")
    @GetMapping(value = "/seasonReport")
    public BaseResp<List<ShortReportResultDTO>> getSeasonReport(Date startStr, Integer type, Date endStr)
        throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<ShortReportResultDTO> result = new ArrayList<>(16);
        String dateStr = DateUtil.getDateToStrFORMAT(startStr, "yyyy-MM");
        List<Integer> list = Arrays.asList(1, 2);
        List<String> strings = Arrays.asList("最大负荷", "最小负荷", "最小腰荷", "总电量");
        for (Integer integer : list) {
            for (String string : strings) {
                List<ShortReportResultDTO> monthReport = cityAllReportService.getSeasonReport(startStr, type, endStr,
                    integer , string);
                result.addAll(monthReport);
            }
        }
        baseResp.setData(result);
        return baseResp;
    }

    @ApiOperation("年度上报汇总")
    @GetMapping(value = "/yearReport")
    public BaseResp<List<ShortReportResultDTO>> getYearReport(String startStr)
        throws Exception {
        BaseResp baseResp = BaseResp.succResp();
        List<ShortReportResultDTO> result = new ArrayList<>(16);
        List<Integer> list = Arrays.asList(1, 2);
        List<String> strings = Arrays.asList("最大负荷", "最小负荷", "最小腰荷", "总电量");
        for (Integer integer : list) {
            for (String string : strings) {
                List<ShortReportResultDTO> yearReport = cityAllReportService.getYearReport(startStr, string,
                    integer, "正常情况");
                List<ShortReportResultDTO> yearReport1 = cityAllReportService.getYearReport(startStr, string,
                    integer, "极端情况");
                result.addAll(yearReport);
                result.addAll(yearReport1);
            }
        }
        baseResp.setData(result);
        return baseResp;
    }

}
