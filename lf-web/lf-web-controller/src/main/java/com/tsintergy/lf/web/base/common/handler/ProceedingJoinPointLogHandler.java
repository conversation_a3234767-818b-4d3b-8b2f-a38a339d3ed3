/**
 * Copyright(C),2015‐2021,北京清能互联科技有限公司
 */
package com.tsintergy.lf.web.base.common.handler;

import com.tsieframework.cloud.security.serviceapi.businesslog.pojo.TsieOperateLogVO;
import org.aspectj.lang.ProceedingJoinPoint;

/**
 *Description: 解析拦截的参数，记录相关的内容到日志中 <br>
 *
 *@Author: <EMAIL>
 *@Date: 2021/12/28 20:12
 *@Version: 1.0.0
 */
public interface ProceedingJoinPointLogHandler {

    /**
     * 功能描述:是否匹配执行器<br>
     *
     * @Return: {@link boolean}
     * @Version: 1.0.0
     * @Author:<EMAIL>
     * @Date: 2021/12/29 8:35
     */
    boolean isMaching(ProceedingJoinPoint proceedingJoinPoint, TsieOperateLogVO operateLog);

    /**
     * 功能描述:执行日志记录<br>
     *
     * @param proceedingJoinPoint
     * @param operateLog
     * @Return:
     * @Version: 1.0.0
     * @Author:<EMAIL>
     * @Date: 2021/12/29 8:36
     */
    void proceedingJoinPointLog(ProceedingJoinPoint proceedingJoinPoint, TsieOperateLogVO operateLog);

}