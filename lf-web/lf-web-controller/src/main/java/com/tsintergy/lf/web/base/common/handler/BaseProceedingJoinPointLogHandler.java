/**
 * Copyright(C),2015‐2021,北京清能互联科技有限公司
 */
package com.tsintergy.lf.web.base.common.handler;

import com.tsieframework.cloud.security.serviceapi.businesslog.pojo.TsieOperateLogVO;
import java.util.List;
import org.aspectj.lang.ProceedingJoinPoint;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2021/12/29 10:26
 * @Version: 1.0.0
 */
public abstract class BaseProceedingJoinPointLogHandler implements ProceedingJoinPointLogHandler {

    protected List<String> operateTitle = null;

    {
        operateTitle = initOperateTitle();
    }

    /**
     * 初始化operateTitle
     */
    abstract List<String> initOperateTitle();

    @Override
    public boolean isMaching(ProceedingJoinPoint proceedingJoinPoint, TsieOperateLogVO operateLog) {
        if (operateTitle.contains(operateLog.getTitle())) {
            return true;
        }
        return false;
    }
}