/**
 * Copyright (C), 2015‐2021, 北京清能互联科技有限公司 Author:  wangfeng Date:  2021/4/13 11:19 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.web.base.common.interceptor;

import com.tsieframework.cloud.security.serviceapi.system.api.SecurityService;
import com.tsieframework.cloud.security.serviceapi.system.bean.TokenBean;
import com.tsieframework.cloud.security.serviceapi.system.pojo.TsieMenuVO;
import com.tsieframework.cloud.security.serviceapi.system.pojo.TsieUserVO;
import com.tsieframework.cloud.security.web.common.security.TokenManager;
import com.tsieframework.core.base.dao.hibernate.DBQueryParam;
import com.tsieframework.core.base.dao.hibernate.DBQueryParamBuilder;
import com.tsieframework.core.base.dao.hibernate.QueryOp;
import java.io.IOException;
import java.util.List;
import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.jasig.cas.client.validation.Assertion;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2021/4/13 
 * @since 2.0.0
 */
public class LoginInitFilter  implements Filter {
    private static final Logger logger = LoggerFactory.getLogger(LoginInitFilter.class);

    protected SecurityService securityService;
    protected TokenManager tokenManager;



    public LoginInitFilter(SecurityService securityService, TokenManager tokenManager) {
        this.securityService = securityService;
        this.tokenManager = tokenManager;
    }

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {

    }

    @Override
    public void doFilter(
        ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        HttpServletRequest req = (HttpServletRequest) servletRequest;
        HttpServletResponse res = (HttpServletResponse) servletResponse;
        logger.info("Cas request URL:" + req.getRequestURI());
        String username = getUserName(req);

        //用户名在前置的过滤器中有校验，不存在为空的可能
        logger.info("request username:" + username);
        if(StringUtils.isBlank(username)) {
            filterChain.doFilter(req, res);
        }else {
            initUserInfo(filterChain, req, res, username);
        }
    }

    private void initUserInfo(FilterChain filterChain, HttpServletRequest req, HttpServletResponse res, String username) throws IOException, ServletException {
        //获取请求携带的token
        TokenBean bean = tokenManager.getTokenBeanFromCache(username);

        //token不存在，或者token过期了，走后台登录方法
        if (bean == null) {
            DBQueryParam param = DBQueryParamBuilder.create().where(QueryOp.StringEqualTo, "username", username).queryDataOnly().build();
            try {
                //用户权限初始化
                TsieUserVO user = securityService.queryTsieUserVo(param);
                String token = tokenManager.setCookieToken(user);
                List<TsieMenuVO> menuList = securityService.queryTsieMenuAllByUserId(user.getId(), null);
                TokenBean tokenBean = TokenManager.createTokenBean(token, user, menuList);
                tokenManager.addTokenBeanToCache(user.getUsername(), tokenBean);
            } catch (Exception e) {
                logger.error("单点登录，初始化用户权限及组织架构信息异常...");
                e.printStackTrace();
            }
            filterChain.doFilter(req, res);
        } else {
            //设置session缓存
            req.getSession().setAttribute(tokenManager.getLoginTokenKey(), bean.getToken());
            //兼容可多用户登录情况下，返回浏览器cookies
//            tokenManager.setCookieToken(bean.getTsieUserVO());
            filterChain.doFilter(req, res);
        }
    }

    public String getUserName(HttpServletRequest request){
        //获取cas传递过来的username
        Object object = request.getSession().getAttribute("_const_cas_assertion_");
        String username = null;
        if (object != null) {
            Assertion assertion = (Assertion) object;
            username = assertion.getPrincipal().getName();
        } else {
            username = (String) request.getSession().getAttribute("edu.yale.its.tp.cas.client.filter.user");
        }
        return username;
    }

    @Override
    public void destroy() {

    }

}