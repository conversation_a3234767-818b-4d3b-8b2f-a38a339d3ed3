package com.tsintergy.lf.web.base.load.controller;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2023-05-07
 * @since 1.0.0
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class LongReportConfigRequest implements Serializable {
    @ApiModelProperty(value = "月度上报设置时间")
    private String monthDate;
    @ApiModelProperty(value = "夏季上报设置时间")
    private String summerDate;
    @ApiModelProperty(value = "冬季上报设置时间")
    private String winterDate;
    @ApiModelProperty(value = "年度上报设置时间")
    private String yearDate;
    @ApiModelProperty(value = "月度上报城市ID")
    private List<String> monthCityIds;
    @ApiModelProperty(value = "夏季上报城市ID")
    private List<String> summerCityIds;
    @ApiModelProperty(value = "冬季上报城市ID")
    private List<String> winterCityIds;
    @ApiModelProperty(value = "年度上报城市ID")
    private List<String> yearCityIds;
    @ApiModelProperty(value = "月度上报地市或者全网")
    private String monthType;
    @ApiModelProperty(value = "月度上报地市或者全网")
    private String summerType;
    @ApiModelProperty(value = "月度上报地市或者全网")
    private String winterType;
    @ApiModelProperty(value = "月度上报地市或者全网")
    private String yearType;

    public String getMonthType() {
        return monthType;
    }

    public void setMonthType(String monthType) {
        this.monthType = monthType;
    }

    public String getSummerType() {
        return summerType;
    }

    public void setSummerType(String summerType) {
        this.summerType = summerType;
    }

    public String getWinterType() {
        return winterType;
    }

    public void setWinterType(String winterType) {
        this.winterType = winterType;
    }

    public String getYearType() {
        return yearType;
    }

    public void setYearType(String yearType) {
        this.yearType = yearType;
    }

    public String getMonthDate() {
        return monthDate;
    }

    public void setMonthDate(String monthDate) {
        this.monthDate = monthDate;
    }

    public String getSummerDate() {
        return summerDate;
    }

    public void setSummerDate(String summerDate) {
        this.summerDate = summerDate;
    }

    public String getWinterDate() {
        return winterDate;
    }

    public void setWinterDate(String winterDate) {
        this.winterDate = winterDate;
    }

    public String getYearDate() {
        return yearDate;
    }

    public void setYearDate(String yearDate) {
        this.yearDate = yearDate;
    }

    public List<String> getMonthCityIds() {
        return monthCityIds;
    }

    public void setMonthCityIds(List<String> monthCityIds) {
        this.monthCityIds = monthCityIds;
    }

    public List<String> getSummerCityIds() {
        return summerCityIds;
    }

    public void setSummerCityIds(List<String> summerCityIds) {
        this.summerCityIds = summerCityIds;
    }

    public List<String> getWinterCityIds() {
        return winterCityIds;
    }

    public void setWinterCityIds(List<String> winterCityIds) {
        this.winterCityIds = winterCityIds;
    }

    public List<String> getYearCityIds() {
        return yearCityIds;
    }

    public void setYearCityIds(List<String> yearCityIds) {
        this.yearCityIds = yearCityIds;
    }
}
