tsie:
  multiple-datasource:
    configs:
      - name: dsC<PERSON>mon
        driver-class-name: com.tsieframework.jdbc.wrapper.dm.DmDriverWrapper
        #        mysql-host为主机名，非docker环境需要配置hosts的主机映射
        url: ${DM_URL:jdbc:dm://************:5240/LF_HENAN?useUnicode=true&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai&nullCatalogMeansCurrent=true}
        username: ${DM_USERNAME:LF_HENAN}
        password: ${DM_PASSWORD:Tsintergy@123}
        #password: qinghua123@
        primary: true
        properties:
          connectionTestQuery: select 1 from dual
          validationTimeout: 5000
          maxLifetime: 1800000
          idleTimeout: 600000
      - name: ultra
        driver-class-name: com.tsieframework.jdbc.wrapper.dm.DmDriverWrapper
        #        mysql-host为主机名，非docker环境需要配置hosts的主机映射
        url: ${DM_URL:jdbc:dm://************:5240/LF_HENAN?useUnicode=true&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai&nullCatalogMeansCurrent=true}
        username: ${DM_USERNAME:LF_HENAN}
        password: ${DM_PASSWORD:Tsintergy@123}
