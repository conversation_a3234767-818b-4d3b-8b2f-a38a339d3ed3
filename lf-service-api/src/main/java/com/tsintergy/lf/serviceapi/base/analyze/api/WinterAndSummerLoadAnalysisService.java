/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.analyze.api;

import com.tsintergy.lf.serviceapi.base.analyze.dto.RequireResponseDTO;
import com.tsintergy.lf.serviceapi.base.analyze.dto.LoadComparisonCurveDTO;
import com.tsintergy.lf.serviceapi.base.analyze.dto.LoadComparisonDTO;
import com.tsintergy.lf.serviceapi.base.analyze.dto.LoadStatisticsDTO;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadHisDTO;
import java.util.List;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/8/22 15:47
 * @Version: 1.0.0
 */
public interface WinterAndSummerLoadAnalysisService {

    /***
     * 功能描述:
     * <br>
     * @param year
     * @param caliberId
     * @param cityId
     * @Return: {@link java.util.List<com.tsintergy.lf.serviceapi.base.analyze.dto.LoadComparisonDTO>}
     * @Version: 1.0.0
     * @Author:<EMAIL>
     * @Date: 2022/8/22 15:49
     */
    List<LoadComparisonDTO> getLoadComparisonDTO(String analyzeType,String year, String caliberId, String cityId);

    
    /***
     * 功能描述:<br>
     * @param analyzeType 分析类型 0 度夏 1 度冬
     * @param year
     * @param caliberId
     * @param cityId
     * @Return: {@link java.util.List<com.tsintergy.lf.serviceapi.base.analyze.dto.LoadComparisonCurveDTO>}
     * @Version: 1.0.0
     * @Author:<EMAIL>
     * @Date: 2022/8/22 16:06
     */
    List<LoadComparisonCurveDTO> getLoadComparisonCurve(String analyzeType, String year, String caliberId, String cityId);

    /***
     * 功能描述:<br>
     * @param analyzeType
     * @param year
     * @param caliberId
     * @param cityId
     * @Return: {@link com.tsintergy.lf.serviceapi.base.analyze.dto.LoadStatisticsDTO}
     * @Version: 1.0.0
     * @Author:<EMAIL>
     * @Date: 2022/8/22 16:15
     */
    LoadStatisticsDTO getLoadStatistics(String analyzeType, String year, String caliberId, String cityId);

    /***
     * 功能描述:
     * 获取最大负荷当天曲线<br>
     * @param analyzeType
     * @param year
     * @param caliberId
     * @param cityId
     * @Return: {@link java.util.List<com.tsintergy.lf.serviceapi.base.load.dto.LoadHisDTO>}
     * @Version: 1.0.0
     * @Author:<EMAIL>
     * @Date: 2022/8/22 16:20
     */
    List<LoadHisDTO> getLoadStatisticsCurve(String analyzeType, String year, String caliberId, String cityId);

    /***
     * 功能描述:<br>
     * @param analyzeType
     * @param year
     * @param caliberId
     * @param cityId
     * @Return: {@link java.util.List< RequireResponseDTO >}
     * @Version: 1.0.0
     * @Author:<EMAIL>
     * @Date: 2022/8/22 16:26
     */
    List<RequireResponseDTO> getRequireResponse(String analyzeType, String year, String caliberId, String cityId);
}