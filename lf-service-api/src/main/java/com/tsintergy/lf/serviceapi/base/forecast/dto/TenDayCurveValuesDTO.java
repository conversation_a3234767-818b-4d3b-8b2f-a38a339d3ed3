package com.tsintergy.lf.serviceapi.base.forecast.dto;

import com.tsieframework.core.base.domain.dto.DTO;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * Description: 十日预测曲线
 *
 * <AUTHOR>
 * @create 2023-03-06
 * @since 1.0.0
 */
@Data
public class TenDayCurveValuesDTO implements DTO {

    /**
     * 预测算法
     */
    private List<TenDayCurveDateValuesDTO> dateFcLoadList;

    /**
     * 最终上报
     */
    private List<TenDayCurveDateValuesDTO> dateReportLoadList;

    /**
     * 实际负荷
     */
    private List<TenDayCurveDateValuesDTO> dateHisLoadList;

}
