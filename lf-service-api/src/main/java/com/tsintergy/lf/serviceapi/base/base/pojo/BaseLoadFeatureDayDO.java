/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.base.pojo;

import com.tsieframework.core.base.dao.BaseVO;
import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;
import javax.persistence.Column;
import javax.persistence.MappedSuperclass;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

/**
 * Description:
 * 顶层负荷特性<br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/8/8 17:48
 * @Version: 1.0.0
 */

@MappedSuperclass
public class BaseLoadFeatureDayDO extends BaseVO {



    /**
     * 日期
     */
    @Column(name = "date")
    protected Date date;


    /**
     * 最大负荷
     */
    @Column(name = "max_load")
    @BigdecimalJsonFormat(scale = 0)
    protected BigDecimal maxLoad;

    /**
     * 最小负荷
     */
    @Column(name = "min_load")
    @BigdecimalJsonFormat(scale = 0)
    protected BigDecimal minLoad;

    /**
     * 平均负荷
     */
    @Column(name = "ave_load")
    @BigdecimalJsonFormat(scale = 0)
    protected BigDecimal aveLoad;

    /**
     * 最大负荷发生时刻
     */
    @Column(name = "max_time")
    protected String maxTime;

    /**
     * 最小负荷发生时刻
     */
    @Column(name = "min_time")
    protected String minTime;

    /**
     * 尖峰平均负荷
     */
    @Column(name = "peak")
    protected BigDecimal peak;

    /**
     * 低谷平均负荷
     */
    @Column(name = "trough")
    protected BigDecimal trough;

    /**
     * 峰谷差
     */
    @Column(name = "different")
    @BigdecimalJsonFormat(scale = 2)
    protected BigDecimal different;

    /**
     * 峰谷差率
     */
    @Column(name = "gradient")
    @BigdecimalJsonFormat(scale = 2)
    protected BigDecimal gradient;

    /**
     * 负荷率
     */
    @Column(name = "load_gradient")
    protected BigDecimal loadGradient;

    /**
     * 日电量
     */
    @Column(name = "energy")
    protected BigDecimal energy;

    /**
     * 创建时间
     */
    @Column(name = "createtime")
    @CreationTimestamp
    protected Timestamp createtime;

    /**
     * 更新时间
     */
    @Column(name = "updatetime")
    @UpdateTimestamp
    protected Timestamp updatetime;

//    public String getId() {
//        return id;
//    }
//
//    public void setId(String id) {
//        this.id = id;
//    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }


    public BigDecimal getMaxLoad() {
        return maxLoad;
    }

    public void setMaxLoad(BigDecimal maxLoad) {
        this.maxLoad = maxLoad;
    }

    public BigDecimal getMinLoad() {
        return minLoad;
    }

    public void setMinLoad(BigDecimal minLoad) {
        this.minLoad = minLoad;
    }

    public BigDecimal getAveLoad() {
        return aveLoad;
    }

    public void setAveLoad(BigDecimal aveLoad) {
        this.aveLoad = aveLoad;
    }

    public String getMaxTime() {
        return maxTime;
    }

    public void setMaxTime(String maxTime) {
        this.maxTime = maxTime;
    }

    public String getMinTime() {
        return minTime;
    }

    public void setMinTime(String minTime) {
        this.minTime = minTime;
    }

    public BigDecimal getPeak() {
        return peak;
    }

    public void setPeak(BigDecimal peak) {
        this.peak = peak;
    }

    public BigDecimal getTrough() {
        return trough;
    }

    public void setTrough(BigDecimal trough) {
        this.trough = trough;
    }

    public BigDecimal getDifferent() {
        return different;
    }

    public void setDifferent(BigDecimal different) {
        this.different = different;
    }

    public BigDecimal getGradient() {
        return gradient;
    }

    public void setGradient(BigDecimal gradient) {
        this.gradient = gradient;
    }

    public BigDecimal getLoadGradient() {
        return loadGradient;
    }

    public void setLoadGradient(BigDecimal loadGradient) {
        this.loadGradient = loadGradient;
    }

    public BigDecimal getEnergy() {
        return energy;
    }

    public void setEnergy(BigDecimal energy) {
        this.energy = energy;
    }

    public Timestamp getCreatetime() {
        return createtime;
    }

    public void setCreatetime(Timestamp createtime) {
        this.createtime = createtime;
    }

    public Timestamp getUpdatetime() {
        return updatetime;
    }

    public void setUpdatetime(Timestamp updatetime) {
        this.updatetime = updatetime;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        BaseLoadFeatureDayDO that = (BaseLoadFeatureDayDO) o;

        //if (id != null ? !id.equals(that.id) : that.id != null) return false;
        if (date != null ? !date.equals(that.date) : that.date != null) return false;
        if (maxLoad != null ? !maxLoad.equals(that.maxLoad) : that.maxLoad != null) return false;
        if (minLoad != null ? !minLoad.equals(that.minLoad) : that.minLoad != null) return false;
        if (aveLoad != null ? !aveLoad.equals(that.aveLoad) : that.aveLoad != null) return false;
        if (maxTime != null ? !maxTime.equals(that.maxTime) : that.maxTime != null) return false;
        if (minTime != null ? !minTime.equals(that.minTime) : that.minTime != null) return false;
        if (peak != null ? !peak.equals(that.peak) : that.peak != null) return false;
        if (trough != null ? !trough.equals(that.trough) : that.trough != null) return false;
        if (different != null ? !different.equals(that.different) : that.different != null) return false;
        if (gradient != null ? !gradient.equals(that.gradient) : that.gradient != null) return false;
        if (loadGradient != null ? !loadGradient.equals(that.loadGradient) : that.loadGradient != null) return false;
        if (energy != null ? !energy.equals(that.energy) : that.energy != null) return false;
        if (createtime != null ? !createtime.equals(that.createtime) : that.createtime != null) return false;
        return updatetime != null ? updatetime.equals(that.updatetime) : that.updatetime == null;
    }

}
