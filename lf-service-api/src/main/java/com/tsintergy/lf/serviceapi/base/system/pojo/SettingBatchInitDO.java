package com.tsintergy.lf.serviceapi.base.system.pojo;

import com.tsieframework.core.base.dao.BaseVO;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;

/**
 * @Description 预测批次设置表
 * <AUTHOR>
 * @Date 2023/6/5 13:50
 **/

@Data
@Entity
@Table(name = "SETTING_BATCH_INIT")
public class SettingBatchInitDO extends BaseVO {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;


    /**
     * 批次名称
     */
    @Column(name = "name")
    private String name;

    /**
     * 开始时段
     */
    @Column(name = "start_time")
    private String startTime;

    /**
     * 结束时段
     */
    @Column(name = "end_time")
    private String endTime;

    /**
     * 城市id
     */
    @Column(name = "city_id")
    private String cityId;
}
