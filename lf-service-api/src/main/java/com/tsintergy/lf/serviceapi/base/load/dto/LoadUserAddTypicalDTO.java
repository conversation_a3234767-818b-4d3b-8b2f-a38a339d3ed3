package com.tsintergy.lf.serviceapi.base.load.dto;

import com.tsieframework.core.base.domain.dto.DTO;
import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class LoadUserAddTypicalDTO implements DTO {

    @ApiModelProperty(value = "工作日")
    @BigdecimalJsonFormat(scale = 0)
    private List<BigDecimal> workLoad;

    @ApiModelProperty(value = "休息日")
    @BigdecimalJsonFormat(scale = 0)
    private List<BigDecimal> restLoad;

    @ApiModelProperty(value = "节假日")
    @BigdecimalJsonFormat(scale = 0)
    private List<BigDecimal> holidayLoad;
}
