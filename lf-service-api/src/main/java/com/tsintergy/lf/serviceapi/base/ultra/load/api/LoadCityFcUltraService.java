
package com.tsintergy.lf.serviceapi.base.ultra.load.api;

import com.tsintergy.lf.core.enums.IfEnum;
import com.tsintergy.lf.serviceapi.base.ultra.load.pojo.LoadCityFcUltraDO;
import java.util.Date;
import java.util.List;

/**
 * 超短期预测数据service
 * <AUTHOR>
 */
public interface LoadCityFcUltraService {


    /**
     * create entity
     *
     * @param vo
     * @return
     * @throws Exception
     */
    LoadCityFcUltraDO doCreate(LoadCityFcUltraDO vo) throws Exception;


    /**
     * @param delta 5 or 15分钟
     * @param multipointType
     * @return
     * @throws Exception
     */
    LoadCityFcUltraDO getLoadCityFcDO(Date date, String cityId, String caliberId, String algorithmId,
        Integer delta, Integer multipointType);


    /**
     * 保存预测结果;注意：更新时带逻辑；用于保存算法结果保存最新一点预测数据
     *
     * @param LoadCityFcDO
     * @throws Exception
     */
    LoadCityFcUltraDO doSaveOrUpdateLatest(LoadCityFcUltraDO LoadCityFcDO) throws Exception;


    LoadCityFcUltraDO getReportLoadCityFcDO(Date date, String cityId, String caliberId, IfEnum report,
                                       Integer delta, Integer multipointType)
            throws Exception;

    List<LoadCityFcUltraDO> getLoadCityFcDO(String cityId, String caliberId,
        String algorithmId, Date startDate, Date endDate, Integer delta, Integer multipointType);


    /***
     * @Description 查询某一城市数据
     * @Date 2023/1/12 16:07
     * <AUTHOR>
     **/
    List<LoadCityFcUltraDO> listLoadCityFcDOS(String cityId, String caliberId, String algoId, Date startDate, Date endDate,
        Integer delta, Integer multipointType);

    /***
     * @Description 查询多个城市数据
     * @Date 2023/1/12 16:09
     * <AUTHOR>
     **/
    List<LoadCityFcUltraDO> listLoadCityFcDOSByCitys(List<String> cityIds, Date startDate, Date endDate, String caliberId, String algoId, Integer delta, Integer multipointType);

    /***
     * @Description 批量插入
     * @Date 2023/1/17 8:50
     * <AUTHOR>
     **/
    void doSaveOrUpdateList(List<LoadCityFcUltraDO> result);

    /***
     * @Description 根据城市口径和算法进行批量删除，用于生成随机数据防止唯一索引冲突问题
     * @return void
     * @Date 2023/1/17 9:12
     * <AUTHOR>
     **/
    void deleteByCityCaliberAlgoList(String cityId, String caliberId, String algoId, Date startDate, Date endDate);

    LoadCityFcUltraDO findLoadFc(String cityId, String caliberId, String algorithmId, Date date, Integer delta,
        IfEnum report, Integer multipointType);

    List<LoadCityFcUltraDO> findLoadCityFcD0(String cityId, String caliberId, String algorithmId, Date startDate, Date endDate, Boolean report, Integer delta);

    void deleteDO(java.sql.Date dateTime, String cityId, String caliberId, String algorithmId, IfEnum report, Integer timeSpan, Integer multipointType);

    /**
     * 超短期上报预测结果
     *
     * @param LoadCityFcDO
     * @return
     */
    LoadCityFcUltraDO report(LoadCityFcUltraDO LoadCityFcDO) throws Exception;

    /**
     * 根据索引进行更新
     * @param fcDO
     * @return
     */
    LoadCityFcUltraDO doSaveOrUpdateByTemplate(LoadCityFcUltraDO fcDO);
}