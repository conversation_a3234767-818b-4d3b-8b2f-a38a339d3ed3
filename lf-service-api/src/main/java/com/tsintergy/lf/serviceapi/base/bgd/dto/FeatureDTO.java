/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.bgd.dto;

import com.tsieframework.core.base.domain.dto.DTO;
import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import lombok.Data;

import java.math.BigDecimal;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/11/19 18:23
 * @Version: 1.0.0
 */
@Data
public class FeatureDTO implements DTO {

    /**
     * 最大负荷
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal maxLoad;

    /**
     *最小负荷
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal minLoad;

    /**
     *平均腰荷
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal noonTimeLoad;

    /**
     * 月电量
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal monthEnergy;

    /**
     * 去年同期最大负荷
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal lastYearMaxLoad;

    /**
     *去年同期最小负荷
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal lastYearMinLoad;

    /**
     *去年同期平均腰荷
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal lastYearNoonTimeLoad;

    /**
     * 去年同期月电量
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal lastYearMonthEnergy;

    /**
     * 日期类型上旬中旬下旬
     */
    private String type;

    /**
     * 旬电量
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal tenDayEnergy;
}
