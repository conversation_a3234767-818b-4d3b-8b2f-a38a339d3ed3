package com.tsintergy.lf.serviceapi.base.bgd.pojo;

import com.tsieframework.core.base.dao.BaseVO;
import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import java.math.BigDecimal;
import java.sql.Timestamp;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.UpdateTimestamp;

/**
 * Description: TODO <br>
 *
 * <AUTHOR>
 * @create 2023-02-16
 * @since 1.0.0
 */
@Data
@Entity
@Table(name = "LOAD_FEATURE_CITY_TEN_DAYS_FC_SERVICE")
public class LoadFeatureCityTenDaysFcServiceDO extends BaseVO {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;

    @Column(name = "year")
    private String year;

    @Column(name = "month")
    private String month;

    /**
     * 城市
     */
    @Column(name = "city_id")
    protected String cityId;

    /**
     * 口径ID
     */
    @Column(name = "caliber_id")
    protected String caliberId;

    /**
     * 算法id
     */
    @Column(name = "algorithm_id")
    private String algorithmId;

    /**
     * 上旬 中旬 下旬
     */
    @Column(name = "type")
    private String type;

    /**
     * 最大负荷
     */
    @Column(name = "max_load")
    @BigdecimalJsonFormat(scale = 0)
    protected BigDecimal maxLoad;

    /**
     * 最小负荷
     */
    @Column(name = "min_load")
    @BigdecimalJsonFormat(scale = 0)
    protected BigDecimal minLoad;

    /**
     * 平均负荷
     */
    @Column(name = "ave_load")
    @BigdecimalJsonFormat(scale = 0)
    protected BigDecimal aveLoad;

    @Column(name = "max_date")
    private Timestamp maxDate;

    /**
     * 最大负荷发生时刻
     */
    @Column(name = "max_time")
    protected String maxTime;

    /**
     * 最小负荷发生时刻
     */
    @Column(name = "min_time")
    protected String minTime;

    /**
     * 尖峰平均负荷
     */
    @Column(name = "peak")
    protected BigDecimal peak;

    /**
     * 低谷平均负荷
     */
    @Column(name = "trough")
    protected BigDecimal trough;

    /**
     * 峰谷差
     */
    @Column(name = "different")
    @BigdecimalJsonFormat(scale = 2)
    protected BigDecimal different;

    /**
     * 峰谷差率
     */
    @Column(name = "gradient")
    @BigdecimalJsonFormat(scale = 2)
    protected BigDecimal gradient;

    /**
     * 负荷率
     */
    @Column(name = "load_gradient")
    protected BigDecimal loadGradient;

    @Column(name = "day_unbalance")
    private BigDecimal dayUnbalance;

    /**
     * 电量
     */
    @Column(name = "energy")
    private BigDecimal energy;

    @Column(name = "upper_stability")
    private BigDecimal upperStability;

    @Column(name = "lower_stability")
    private BigDecimal lowerStability;

    @Column(name = "report")
    private Boolean report;

    @Column(name = "report_time")
    private Timestamp reportTime;
    /**
     * 创建时间
     */
    @Column(name = "createtime")
    @CreationTimestamp
    protected Timestamp createtime;

    /**
     * 更新时间
     */
    @Column(name = "updatetime")
    @UpdateTimestamp
    protected Timestamp updatetime;

    @Column(name = "extreme_max_load")
    @BigdecimalJsonFormat(scale = 0)
    protected BigDecimal extremeMaxLoad;

    @Column(name = "extreme_min_load")
    @BigdecimalJsonFormat(scale = 0)
    protected BigDecimal extremeMinLoad;

    @Column(name = "extreme_day_unbalance")
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal extremeDayUnbalance;

    @Column(name = "extreme_energy")
    private BigDecimal extremeEnergy;

}
