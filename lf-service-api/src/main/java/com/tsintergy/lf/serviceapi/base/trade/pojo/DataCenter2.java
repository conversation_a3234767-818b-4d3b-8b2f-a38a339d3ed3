/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.trade.pojo;

import com.tsieframework.core.base.dao.BaseDO;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/8/29 15:12
 * @Version: 1.0.0
 */

@Data
@Entity
@Table(name = "data_center_2")
public class DataCenter2 implements BaseDO {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;

    @Column(name = "p_code")
    private String p_code;

    @Column(name = "code_id")
    private String code_id;

    @Column(name = "code_sort_id")
    private String code_sort_id;

    @Column(name = "ds")
    private String ds;

    @Column(name = "name")
    private String name;

    @Column(name = "code_type")
    private String code_type;

    @Column(name = "value")
    private String value;

    @Column(name = "disp_sn")
    private String disp_sn;

    @Column(name = "content1")
    private String content1;

    @Column(name = "content2")
    private String content2;

    @Column(name = "content3")
    private String content3;

    @Column(name = "content4")
    private String content4;

    @Column(name = "content5")
    private String content5;

    @Column(name = "org_no")
    private String org_no;


}