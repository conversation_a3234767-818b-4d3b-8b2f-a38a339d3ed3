package com.tsintergy.lf.serviceapi.base.bgd.api;

import com.tsintergy.lf.serviceapi.base.bgd.pojo.LoadFeatureIndustryDayHisServiceDO;

import java.util.Date;
import java.util.List;

/**
 * Description: TODO <br>
 *
 * <AUTHOR>
 * @create 2023-02-16
 * @since 1.0.0
 */
public interface LoadFeatureIndustryDayHisService {

    List<LoadFeatureIndustryDayHisServiceDO> getLoadFeatureIndustryDayHisServiceList(Date startDate, Date endDate, String cityId, String tradeCode);

}
