/**
 *   
 *  Copyright (C), 2015‐2020, 北京清能互联科技有限公司  
 *  Author:  EDZ  
 *  Date:  2020/9/23 14:32  
 *  History:  
 *  <author> <time> <version> <desc> 
 */
package com.tsintergy.lf.serviceapi.base.evalucation.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**  
 * Description: 多日/多月/多年准确率信息 <br> 
 * 
 * <AUTHOR>  
 * @create 2020/9/23  
 * @since 1.0.0  
 */
@Data
@ApiModel("多日/多月/多年准确率信息")
public class MultipleAccuracyDTO implements Serializable {

    /**
     * 地市id
     */
    @ApiModelProperty(value = "准确率数据")
    private String cityId;

    /**
     * 地市
     */
    @ApiModelProperty(value = "准确率数据")
    private String name;

    /**
     * 平均准确率
     */
    @ApiModelProperty(value = "准确率数据")
    private BigDecimal aveAccuracy;


    /**
     * 合格率
     */
    @ApiModelProperty(value = "准确率数据")
    private BigDecimal passRate;

}