package com.tsintergy.lf.serviceapi.base.evalucation.dto;

import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import lombok.Data;

import java.math.BigDecimal;


/**
 * <AUTHOR>
 */
@Data

public class PeakLoadDeviationMonitorDTO {

    private String cityId;
    private String cityName;
    /**
     * 高峰时刻实际负荷
     */
    private BigDecimal peakActualLoad;
    /**
     * 高峰时刻预测负荷
     */
    private BigDecimal peakForecastLoad;
    /**
     * 预测偏差
     */
    private BigDecimal forecastDeviation;
    /**
     * 高峰准确率
     */
    @BigdecimalJsonFormat(percentConvert = 100, scale = 2)
    private BigDecimal peakAccuracy;
    /**
     * 偏差占比
     */
    @BigdecimalJsonFormat(percentConvert = 100, scale = 2)
    private BigDecimal deviation;
}
