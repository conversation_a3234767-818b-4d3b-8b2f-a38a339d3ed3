package com.tsintergy.lf.serviceapi.base.bgd.enums;

/**
 * Description: 重点行业下第一子行业
 *
 * <AUTHOR>
 * @create 2023-02-28
 * @since 1.0.0
 */
public enum IndustrialImportFirstTypeEnum {

    FIRST("2600","化学原料制品",1),
    SECOND("3100","非金属矿物",2),
    THIRD("3200","黑色金属冶炼",3),
    FOURTH("3300","有色金属冶炼",4);

    /**
     * 产业类型
     */
    private String industryId;

    /**
     * 产业名称
     */
    private String IndustryName;

    /**
     * 排序用
     */
    private Integer sort;

    IndustrialImportFirstTypeEnum(String industryId, String industryName, Integer sort) {
        this.industryId = industryId;
        IndustryName = industryName;
        this.sort = sort;
    }

    public String getIndustryId() {
        return industryId;
    }

    public void setIndustryId(String industryId) {
        this.industryId = industryId;
    }

    public String getIndustryName() {
        return IndustryName;
    }

    public void setIndustryName(String industryName) {
        IndustryName = industryName;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }
}

