/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.trade.api;

import com.tsintergy.lf.serviceapi.base.trade.pojo.TradeLoadHisStatDO;
import java.util.Date;
import java.util.List;

/**
 * Description:  <br>
 * 行业统计负荷
 * @Author: <EMAIL>
 * @Date: 2022/9/2 19:37
 * @Version: 1.0.0
 */
public interface TradeLoadHisStatService {


    void save(List<TradeLoadHisStatDO> list);


    List<TradeLoadHisStatDO> findTradeLoadHisBasicDO(String tradeCode, Date startDate, Date endDate);
}
