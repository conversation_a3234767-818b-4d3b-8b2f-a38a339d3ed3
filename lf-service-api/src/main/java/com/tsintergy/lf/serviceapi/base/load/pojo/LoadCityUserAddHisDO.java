package com.tsintergy.lf.serviceapi.base.load.pojo;


import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.aif.tool.core.feature.load.Load;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.serviceapi.base.base.pojo.Base96DO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;
import java.util.List;

/**
 * 重点用户总加负荷
 */
@Data
@Entity
@NoArgsConstructor
@Table(name = "load_city_user_add_his")
public class LoadCityUserAddHisDO extends Base96DO implements Load {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;

    /**
     * 日期
     */
    @Column(name = "date")
    private Date date;

    /**
     * 城市ID
     */
    @Column(name = "city_id")
    private String cityId;

    /**
     * 口径ID
     */
    @Column(name = "caliber_id")
    private String caliberId;


    /**
     * 创建时间
     */
    @Column(name = "createtime")
    @CreationTimestamp
    private Timestamp createtime;

    /**
     * 更新时间
     */
    @Column(name = "updatetime")
    @UpdateTimestamp
    private Timestamp updatetime;







    @Override
    public List<BigDecimal> getLoadList() {
        return BasePeriodUtils.toList(this, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
    }

    @Override
    public boolean isStartWithZero() {
        return Constants.LOAD_CURVE_START_WITH_ZERO;
    }

    @Override
    public Integer getScale() {
        return Constants.SCALE;
    }

    @Override
    public BigDecimal getMultiplyNumber() {
        return BigDecimal.valueOf(100);
    }

    @Override
    public boolean verifyData() {
        return true;
    }
}
