package com.tsintergy.lf.serviceapi.base.bgd.util;

/**
 * Description: TODO <br>
 *
 * <AUTHOR>
 * @create 2023-02-17
 * @since 1.0.0
 */

import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.core.enums.SeasonEnum;
import com.tsintergy.lf.core.util.DateUtil;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import org.springframework.util.StringUtils;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/11/19 22:34
 * @Version: 1.0.0
 */
public class QuarterUtil {


    private final static SimpleDateFormat shortSdf = new SimpleDateFormat("yyyy-MM-dd");

    /**
     * 获取指定日期所在季度
     */
    public static String getQuarterByDate(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int m = cal.get(Calendar.MONTH) + 1;
        int q = 0;
        if (m >= 3 && m<=5) {
            q = 1;
        } else if (m>=6 && m <= 8) {
            q = 2;
        } else if (m>=9 && m<= 10) {
            q = 3;
        } else if (m>=1 ||m >= 11) {
            q = 4;
        }
        return cal.get(Calendar.YEAR) + "-" + q;
    }


    public static String getQuarterByDate(String year,String month) {
        int m = Integer.parseInt(month);
        int q = 0;
        if (m >= 3 && m<=5) {
            q = 1;
        } else if (m>=6 && m <= 8) {
            q = 2;
        } else if (m>=9 && m<= 10) {
            q = 3;
        } else if (m>=1 ||m >= 11) {
            q = 4;
        }
        return year + "-" + q;
    }

    public static int getMonthDayByDate(Date date){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        //本月多少天
        return calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
    }

    /**
     * 获取月旬 三旬: 上旬1-10日 中旬11-20日 下旬21-31日
     *
     * @param date
     * @return
     */
    public static int getTenDay(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        int i = c.get(Calendar.DAY_OF_MONTH);
        if (i < 11){
            return 1;
        }
        else if (i < 21){
            return 2;
        } else{
            return 3;

        }
    }

    /**
     * 获取所属旬开始时间
     *
     * @param date
     * @return
     */
    public static Date getTenDayStartTime(Date date) {
        int ten = getTenDay(date);
        try {
            if (ten == 1) {
                return DateUtil.getFirstDayOfMonth(date);
            } else if (ten == 2) {
                SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-11");
                return shortSdf.parse(df.format(date));
            } else {
                SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-21");
                return shortSdf.parse(df.format(date));
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 默认为冬季，若参数不为null 则为夏季
     * @param season
     * @return
     */
    public static String getQuarterBySeason(String season){
        String quarter = String.valueOf(SeasonEnum.Winter.getSeason());
        if (!StringUtils.isEmpty(season)){
            quarter = String.valueOf(SeasonEnum.Summer.getSeason());
        }
        return quarter;
    }

    /**
     * 默认为冬季，若参数不为null 则为夏季
     * 获取季节对应月份
     * @param season
     * @return
     */
    public static List<String> getMonthsBySeason(String season){
        List<String> months = Arrays.asList(Constants.WINTER_MONTHS);
        if (!StringUtils.isEmpty(season)){
            months = Arrays.asList(Constants.SUMMER_MONTHS);
        }
        return months;
    }


}
