/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.analyze.dto;

import com.tsieframework.core.base.domain.dto.DTO;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/8/22 16:21
 * @Version: 1.0.0
 */
public class RequireResponseDTO implements DTO {


    /**
     * 日期
     */
    private Date date;

    /**
     * 相应时段
     */
    private String responseTime;

    /**
     *最大相应电力
     */
    private BigDecimal maxResponseElectricity;

    /**
     * 最大还原负荷
     */
    private BigDecimal maxRestoreLoad;

    /**
     *响应电量
     */
    private BigDecimal responsePower;


    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public String getResponseTime() {
        return responseTime;
    }

    public void setResponseTime(String responseTime) {
        this.responseTime = responseTime;
    }

    public BigDecimal getMaxResponseElectricity() {
        return maxResponseElectricity;
    }

    public void setMaxResponseElectricity(BigDecimal maxResponseElectricity) {
        this.maxResponseElectricity = maxResponseElectricity;
    }

    public BigDecimal getMaxRestoreLoad() {
        return maxRestoreLoad;
    }

    public void setMaxRestoreLoad(BigDecimal maxRestoreLoad) {
        this.maxRestoreLoad = maxRestoreLoad;
    }

    public BigDecimal getResponsePower() {
        return responsePower;
    }

    public void setResponsePower(BigDecimal responsePower) {
        this.responsePower = responsePower;
    }
}