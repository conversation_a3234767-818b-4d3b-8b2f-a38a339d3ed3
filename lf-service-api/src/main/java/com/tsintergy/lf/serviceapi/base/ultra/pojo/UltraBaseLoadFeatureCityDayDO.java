package com.tsintergy.lf.serviceapi.base.ultra.pojo;


import com.tsieframework.core.base.dao.BaseVO;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;
import javax.persistence.Column;
import javax.persistence.MappedSuperclass;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

/**
 * 日负荷特性
 */
@MappedSuperclass
public class UltraBaseLoadFeatureCityDayDO extends BaseVO {


    /**
     * 日期
     */
    @Column(name = "date_time")
    protected Date dateTime;

    /**
     * 城市
     */
    @Column(name = "city_id")
    protected String cityId;

    /**
     * 口径ID
     */
    @Column(name = "caliber_id")
    protected String caliberId;

    /***
     * @Description 数据类型 5 五分钟时间间隔 15 十五分钟时间间隔
     * @Date 2023/1/12 16:02
     * <AUTHOR>
     **/
    @Column(name = "type")
    private Integer type;

    /**
     * 最大负荷
     */
    @Column(name = "max_load")
    protected BigDecimal maxLoad;

    /**
     * 最小负荷
     */
    @Column(name = "min_load")
    protected BigDecimal minLoad;

    /**
     * 平均负荷
     */
    @Column(name = "ave_load")
    protected BigDecimal aveLoad;

    /**
     * 最大负荷发生时刻
     */
    @Column(name = "max_time")
    protected String maxTime;

    /**
     * 最小负荷发生时刻
     */
    @Column(name = "min_time")
    protected String minTime;

    /**
     * 尖峰平均负荷
     */
    @Column(name = "peak")
    protected BigDecimal peak;

    /**
     * 低谷平均负荷
     */
    @Column(name = "trough")
    protected BigDecimal trough;

    /**
     * 峰谷差
     */
    @Column(name = "different")
    protected BigDecimal different;

    /**
     * 峰谷差率
     */
    @Column(name = "gradient")
    protected BigDecimal gradient;

    /**
     * 负荷率
     */
    @Column(name = "load_gradient")
    protected BigDecimal loadGradient;

    /**
     * 日电量
     */
    @Column(name = "energy")
    protected BigDecimal energy;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @CreationTimestamp
    protected Timestamp createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    @UpdateTimestamp
    protected Timestamp updateTime;

//    public String getId() {
//        return id;
//    }
//
//    public void setId(String id) {
//        this.id = id;
//    }


    public Date getDateTime() {
        return dateTime;
    }

    public void setDateTime(Date dateTime) {
        this.dateTime = dateTime;
    }

    public Timestamp getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    public Timestamp getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Timestamp updateTime) {
        this.updateTime = updateTime;
    }

    public String getCityId() {
        return cityId;
    }

    public void setCityId(String cityId) {
        this.cityId = cityId;
    }

    public BigDecimal getMaxLoad() {
        return maxLoad;
    }

    public void setMaxLoad(BigDecimal maxLoad) {
        this.maxLoad = maxLoad;
    }

    public BigDecimal getMinLoad() {
        return minLoad;
    }

    public void setMinLoad(BigDecimal minLoad) {
        this.minLoad = minLoad;
    }

    public BigDecimal getAveLoad() {
        return aveLoad;
    }

    public void setAveLoad(BigDecimal aveLoad) {
        this.aveLoad = aveLoad;
    }

    public String getMaxTime() {
        return maxTime;
    }

    public void setMaxTime(String maxTime) {
        this.maxTime = maxTime;
    }

    public String getMinTime() {
        return minTime;
    }

    public void setMinTime(String minTime) {
        this.minTime = minTime;
    }

    public BigDecimal getPeak() {
        return peak;
    }

    public void setPeak(BigDecimal peak) {
        this.peak = peak;
    }

    public BigDecimal getTrough() {
        return trough;
    }

    public void setTrough(BigDecimal trough) {
        this.trough = trough;
    }

    public BigDecimal getDifferent() {
        return different;
    }

    public void setDifferent(BigDecimal different) {
        this.different = different;
    }

    public BigDecimal getGradient() {
        return gradient;
    }

    public void setGradient(BigDecimal gradient) {
        this.gradient = gradient;
    }

    public BigDecimal getLoadGradient() {
        return loadGradient;
    }

    public void setLoadGradient(BigDecimal loadGradient) {
        this.loadGradient = loadGradient;
    }

    public BigDecimal getEnergy() {
        return energy;
    }

    public void setEnergy(BigDecimal energy) {
        this.energy = energy;
    }


    public String getCaliberId() {
        return caliberId;
    }

    public void setCaliberId(String caliberId) {
        this.caliberId = caliberId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        UltraBaseLoadFeatureCityDayDO that = (UltraBaseLoadFeatureCityDayDO) o;

        //if (id != null ? !id.equals(that.id) : that.id != null) return false;
        if (dateTime != null ? !dateTime.equals(that.dateTime) : that.dateTime != null) return false;
        if (cityId != null ? !cityId.equals(that.cityId) : that.cityId != null) return false;
        if (caliberId != null ? !caliberId.equals(that.caliberId) : that.caliberId != null) return false;
        if (type != null ? !type.equals(that.type) : that.type != null) return false;
        if (maxLoad != null ? !maxLoad.equals(that.maxLoad) : that.maxLoad != null) return false;
        if (minLoad != null ? !minLoad.equals(that.minLoad) : that.minLoad != null) return false;
        if (aveLoad != null ? !aveLoad.equals(that.aveLoad) : that.aveLoad != null) return false;
        if (maxTime != null ? !maxTime.equals(that.maxTime) : that.maxTime != null) return false;
        if (minTime != null ? !minTime.equals(that.minTime) : that.minTime != null) return false;
        if (peak != null ? !peak.equals(that.peak) : that.peak != null) return false;
        if (trough != null ? !trough.equals(that.trough) : that.trough != null) return false;
        if (different != null ? !different.equals(that.different) : that.different != null) return false;
        if (gradient != null ? !gradient.equals(that.gradient) : that.gradient != null) return false;
        if (loadGradient != null ? !loadGradient.equals(that.loadGradient) : that.loadGradient != null) return false;
        if (energy != null ? !energy.equals(that.energy) : that.energy != null) return false;
        if (createTime != null ? !createTime.equals(that.createTime) : that.createTime != null) return false;
        return updateTime != null ? updateTime.equals(that.updateTime) : that.updateTime == null;
    }

//    @Override
//    public int hashCode() {
//       // int result = id != null ? id.hashCode() : 0;
//        int  result = 31 * result + (date != null ? date.hashCode() : 0);
//        result = 31 * result + (cityId != null ? cityId.hashCode() : 0);
//        result = 31 * result + (caliberId != null ? caliberId.hashCode() : 0);
//        result = 31 * result + (maxLoad != null ? maxLoad.hashCode() : 0);
//        result = 31 * result + (minLoad != null ? minLoad.hashCode() : 0);
//        result = 31 * result + (aveLoad != null ? aveLoad.hashCode() : 0);
//        result = 31 * result + (maxTime != null ? maxTime.hashCode() : 0);
//        result = 31 * result + (minTime != null ? minTime.hashCode() : 0);
//        result = 31 * result + (peak != null ? peak.hashCode() : 0);
//        result = 31 * result + (trough != null ? trough.hashCode() : 0);
//        result = 31 * result + (different != null ? different.hashCode() : 0);
//        result = 31 * result + (gradient != null ? gradient.hashCode() : 0);
//        result = 31 * result + (loadGradient != null ? loadGradient.hashCode() : 0);
//        result = 31 * result + (energy != null ? energy.hashCode() : 0);
//        result = 31 * result + (createtime != null ? createtime.hashCode() : 0);
//        result = 31 * result + (updatetime != null ? updatetime.hashCode() : 0);
//        return result;
//    }


}
