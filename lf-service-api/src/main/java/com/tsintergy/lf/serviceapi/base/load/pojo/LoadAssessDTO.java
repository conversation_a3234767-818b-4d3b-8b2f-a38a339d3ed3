package com.tsintergy.lf.serviceapi.base.load.pojo;

import com.tsieframework.core.base.domain.dto.DTO;
import java.math.BigDecimal;
import lombok.Data;

/**
 * Description: 地市考核结果
 *
 * <AUTHOR>
 * @create 2023-03-21
 * @since 1.0.0
 */
@Data
public class LoadAssessDTO implements DTO {

    private String cityName;

    private BigDecimal dayAccuracy;

    private BigDecimal maxLoadAccuracy;

    private BigDecimal minLoadAccuracy;

    private BigDecimal monthMaxLoadAccuracy;

    private BigDecimal monthEnergyAccuracy;

    private BigDecimal weightAverageAccuracy;

}
