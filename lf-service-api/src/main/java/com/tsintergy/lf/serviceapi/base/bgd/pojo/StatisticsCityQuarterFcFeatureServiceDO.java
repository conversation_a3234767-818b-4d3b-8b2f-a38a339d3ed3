/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.bgd.pojo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.tsieframework.core.base.dao.BaseDO;
import com.tsieframework.core.base.vo.annotation.EntityUniqueIndex;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;

/**
 * Description:  <br>
 * 月度负荷特性准确率表
 * @Author: <EMAIL>
 * @Date: 2022/11/17 19:28
 * @Version: 1.0.0
 */

@Data
@Entity
@Table(name = "STATISTICS_CITY_QUARTER_FC_FEATURE_SERVICE")
@TableName("STATISTICS_CITY_QUARTER_FC_FEATURE_SERVICE")
@EntityUniqueIndex({"cityId", "caliberId", "year", "quarter", "algorithmId"})
public class StatisticsCityQuarterFcFeatureServiceDO extends BaseFeatureAccuryDO implements BaseDO {


    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;

    @Column(name = "year")
    private String year;

    @Column(name = "quarter")
    private String quarter;



}