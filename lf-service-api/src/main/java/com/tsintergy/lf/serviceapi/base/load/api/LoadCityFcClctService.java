/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司 Author:  wangchen Date:  2019/3/11 14:56 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.load.api;


import com.tsieframework.core.base.service.BaseService;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityFcClctDO;
import java.util.Date;
import java.util.List;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2019/3/11
 * @since 1.0.0
 */
public interface LoadCityFcClctService extends BaseService {


    void saveOrUpdateBatch(List<LoadCityFcClctDO> loadCityFcClctDOS);

    LoadCityFcClctDO findLoadCityFcClctDO(Date date, String caliberId, String cityId);

    void updateById(LoadCityFcClctDO loadCityFcClctDO);
}
