/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.area.api;

import com.tsieframework.core.base.service.BaseService;
import com.tsieframework.core.base.service.FacadeService;
import com.tsintergy.lf.serviceapi.base.area.pojo.WeatherFeatureAreaDayHisDO;
import java.util.Date;
import java.util.List;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/8/9 11:43
 * @Version: 1.0.0
 */
public interface WeatherFeatureAreaHisService extends BaseService {


    void doSaveOrUpdate(WeatherFeatureAreaDayHisDO weatherFeatureAreaDayHisDO);

    List<WeatherFeatureAreaDayHisDO> findAreaWeatherFeature(String areaId, Date startDate, Date endDate);


    /***
     * 功能描述:
     * 统计区域历史气象特性
     * <br>
     * @param startDate
     * @param endDate
     * @param areaIds 区域id 集合
     * @Return:
     * @Version: 1.0.0
     * @Author:<EMAIL>
     * @Date: 2022/8/9 13:53
     */
    void statAreaWeatherHisFeature(Date startDate,Date endDate,String areaId) throws Exception;
}
