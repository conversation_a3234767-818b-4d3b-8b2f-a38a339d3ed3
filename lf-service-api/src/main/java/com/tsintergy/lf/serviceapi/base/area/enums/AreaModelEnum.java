/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.area.enums;

/**
 * Description:  <br>
 *
 * @Author: liu<PERSON><EMAIL>
 * @Date: 2022/8/4 13:46
 * @Version: 1.0.0
 */
public enum AreaModelEnum {
    //湿度
    SYSTEM_FORECAST(1, "系统预测"),
    CITY_REPORT(2, "地市上报");
    private Integer key;
    private String value;

    AreaModelEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }

    public Integer getKey() {
        return key;
    }

    public void setKey(Integer key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}