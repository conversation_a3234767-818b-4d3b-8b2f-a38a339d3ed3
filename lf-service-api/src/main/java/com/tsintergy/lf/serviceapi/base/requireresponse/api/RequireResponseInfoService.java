/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.requireresponse.api;

import com.tsieframework.core.base.service.BaseService;
import com.tsintergy.lf.serviceapi.base.requireresponse.pojo.RequireResponseInfoDO;
import java.util.List;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/8/23 14:53
 * @Version: 1.0.0
 */
public interface RequireResponseInfoService extends BaseService {


    List<RequireResponseInfoDO> getRequireResponseInfoByYear(String year);
}
