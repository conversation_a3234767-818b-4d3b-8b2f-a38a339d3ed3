package com.tsintergy.lf.serviceapi.base.forecast.dto;

import com.tsieframework.core.base.domain.dto.DTO;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * Description: 十日气象预测曲线
 *
 * <AUTHOR>
 * @create 2023-03-06
 * @since 1.0.0
 */
@Data
public class TenDayWeatherCurveValuesDTO implements DTO {

    /**
     * 气象类型
     */
    private String name;

    /**
     * 预测气象
     */
    private List<TenDayCurveWeatherDateDTO> weather;

    /**
     * 实际气象
     */
    private List<TenDayCurveWeatherDateDTO> hisWeather;

}
