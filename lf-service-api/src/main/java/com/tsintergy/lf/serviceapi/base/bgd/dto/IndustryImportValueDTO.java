package com.tsintergy.lf.serviceapi.base.bgd.dto;

import com.tsieframework.core.base.domain.dto.DTO;
import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * Description: 重点行业子行业
 *
 * <AUTHOR>
 * @create 2023-02-28
 * @since 1.0.0
 */
@Data
public class IndustryImportValueDTO implements DTO {

    private Date date;

    @BigdecimalJsonFormat(divideConvert = 1000, scale = 0)
    private BigDecimal morningLoad;

    @BigdecimalJsonFormat(divideConvert = 1000, scale = 0)
    private BigDecimal eveningLoad;

    @BigdecimalJsonFormat(divideConvert = 1000, scale = 0)
    private BigDecimal thoughLoad;

    @BigdecimalJsonFormat(divideConvert = 1000, scale = 0)
    private BigDecimal noonTimeLoad;

    @BigdecimalJsonFormat(divideConvert = 10000, scale = 0)
    private BigDecimal energy;

}
