package com.tsintergy.lf.serviceapi.base.forecast.dto;

import com.tsieframework.core.base.domain.dto.DTO;
import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * Description: 预测特征值
 *
 * <AUTHOR>
 * @create 2023-03-06
 * @since 1.0.0
 */
@Data
public class TenDayFeatureValuesDTO implements DTO {

    /**
     * 日期
     */
    private Date date;

    /**
     * 最大负荷
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal maxLoad;

    /**
     * 最小负荷
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal minLoad;

    /**
     * 最大负荷发生时刻
     */
    private String maxTime;

    /**
     * 最小负荷发生时刻
     */
    private String minTime;

    /**
     * 平均负荷
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal aveLoad;

    /**
     * 早峰负荷
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal morningLoad;

    /**
     * 晚峰负荷
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal eveningLoad;

    /**
     * 最小腰荷
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal noontimeLoad;

    /**
     * 最大腰荷
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal maxNoontimeLoad;

    /**
     * 积分电量
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal integralLoad;

    /**
     * 峰谷差
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal different;

    /**
     * 峰谷差率
     */
    @BigdecimalJsonFormat(scale = 4,percentConvert = 100)
    private BigDecimal gradient;

    /**
     * 早峰负荷发生时刻
     */
    private String morningLoadTime;

    /**
     * 晚峰负荷发生时刻
     */
    private String eveningLoadTime;

    /**
     * 最大腰荷发生时刻
     */
    private String maxNoontimeLoadTime;

    /**
     * 最小腰荷发生时刻
     */
    private String noontimeLoadTime;
}
