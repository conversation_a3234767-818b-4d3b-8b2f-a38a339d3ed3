package com.tsintergy.lf.serviceapi.base.weather.api;

import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityMonthMdHisDO;
import java.util.List;

/**
 * Description: TODO <br>
 *
 * <AUTHOR>
 * @create 2023-06-18
 * @since 1.0.0
 */
public interface WeatherFeatureCityMonthMdHisService {

    List<WeatherFeatureCityMonthMdHisDO> findWeatherFeatureCityTenDaysDTO(String cityId, String year, String month);

    void doSaveOrUpdate(WeatherFeatureCityMonthMdHisDO weatherFeatureCityMonthMdHisDO);

}
