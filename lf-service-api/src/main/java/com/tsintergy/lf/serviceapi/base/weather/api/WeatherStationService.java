/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.weather.api;

import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherStationInfoDO;
import java.util.List;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/5/31 10:46
 * @Version: 1.0.0
 */
public interface WeatherStationService {

    void doSaveOrUpdate(WeatherStationInfoDO weatherStationInfoDO);


    List<WeatherStationInfoDO>  getWeatherStationInfo(String cityId,String stationId);
}
