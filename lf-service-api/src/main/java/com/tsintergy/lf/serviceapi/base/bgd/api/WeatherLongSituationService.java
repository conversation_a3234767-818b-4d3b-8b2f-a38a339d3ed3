/**
 * Copyright (C), 2015‐2022, 北京清能互联科技有限公司 Author:  <EMAIL> Date:  2022/5/24 10:13 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.bgd.api;

import com.tsieframework.core.base.service.FacadeService;
import com.tsintergy.lf.serviceapi.base.bgd.pojo.WeatherLongSituationDO;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 */
public interface WeatherLongSituationService extends FacadeService {

    WeatherLongSituationDO getWeatherLongSituationDO(String cityId, String year, String month);

}
