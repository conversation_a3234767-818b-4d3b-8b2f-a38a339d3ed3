package com.tsintergy.lf.serviceapi.base.forecast.pojo;

import com.tsieframework.core.base.dao.BaseDO;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;

@Data
@Entity
@Table(name = "CITY_EXTRA_ALGORITHM_RUN_CONFIG")
public class CityExtraAlgorithmRunConfigDO implements BaseDO {

    @Id
    @Column(name = "algorithm_id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String algorithmId;

    @Column(name = "city_ids")
    private String cityIds;

    @Column(name = "algorithm_type")
    private String algorithmType;

}
