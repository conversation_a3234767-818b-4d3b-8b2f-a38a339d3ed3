/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司 Author:  ThinkPad Date:  2019/5/15 7:26 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.ultra.dto;



import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 超短期日准确率对象 用于月准确率页面的日均准确率对比 -和季度的通用
 *
 * <AUTHOR>
 * @create 2023/1/11
 * @since 1.0.0
 */
@Data
public class AccuracyDayUnitDTO {


    /**
     * 日期、月度展示yyyy-mm 季度展示同样 yyyy-mm 日维度 yyyy-mm-dd
     */
    private String date;

    /**
     * 准确率
     */
    @BigdecimalJsonFormat(percentConvert = 100)
    private BigDecimal accuracy;



}