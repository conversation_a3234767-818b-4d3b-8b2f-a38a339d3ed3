package com.tsintergy.lf.serviceapi.base.weather.pojo;

import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.aif.algorithm.serviceapi.base.pojo.Weather;
import com.tsintergy.lf.core.constants.Constants;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;
import java.util.List;
import java.util.UUID;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.UpdateTimestamp;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2024-12-03
 * @since 1.0.0
 */
@Data
@Entity
@Table(name = "weather_city_his_rpa_basic")
public class WeatherCityHisRpaDO extends BaseWeatherDO implements Weather {

    public WeatherCityHisRpaDO(){
        super();
    }
    public WeatherCityHisRpaDO(String cityId, Integer type, java.util.Date date,String stationId){
        this.cityId = cityId;
        this.stationId = stationId;
        this.type=type;
        this.date=new Date(date.getTime());
        this.id= UUID.randomUUID().toString().replace("-","");
    }

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;


    /**
     * 日期
     */
    @Column(name = "date")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date date;

    /**
     * 城市ID
     */
    @Column(name = "city_id")
    private String cityId;

    /**
     * 站点ID
     */
    @Column(name = "station_id")
    private String stationId;

    /**
     * 气象类型
     */
    @Column(name = "type")
    private Integer type;

    /**
     * 创建时间
     */
    @Column(name = "createtime")
    @CreationTimestamp
    private Timestamp createtime;

    /**
     * 更新时间
     */
    @Column(name = "updatetime")
    @UpdateTimestamp
    private Timestamp updatetime;

    @Override
    public List<BigDecimal> getWeatherList() {
        return BasePeriodUtils.toList(this, 96, Constants.LOAD_CURVE_START_WITH_ZERO);
    }
}
