package com.tsintergy.lf.serviceapi.base.ultra.forecast.dto;

import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2023-10-18
 * @since 1.0.0
 */
@Data
public class ForecastAlarmConfigDTO implements Serializable {

    @BigdecimalJsonFormat(scale = 2)
    private BigDecimal fiveAlarmLoad;

    private Integer fiveType;

    @BigdecimalJsonFormat(scale = 2)
    private BigDecimal fifteenAlarmLoad;

    private Integer fifteenType;

    private String cityId;
}
