package com.tsintergy.lf.serviceapi.base.bgd.api;

import com.tsintergy.lf.serviceapi.base.bgd.pojo.LoadCityMonthCurveFcBasicDO;
import java.util.List;


/**
 * Description: TODO <br>
 *
 * <AUTHOR>
 * @create 2023-03-09
 * @since 1.0.0
 */
public interface LoadMonthTypicalCurveService {

    /**
     * 查询月典型曲线
     *
     * @param cityId 城市标识
     * @param yearByDate 年按日期
     * @param monthByDate 月按日期
     * @return {@code List<LoadCityMonthCurveFcBasicDO>}
     */
    List<LoadCityMonthCurveFcBasicDO> queryMonthTypicalCurve(String cityId, String yearByDate, String monthByDate);

}
