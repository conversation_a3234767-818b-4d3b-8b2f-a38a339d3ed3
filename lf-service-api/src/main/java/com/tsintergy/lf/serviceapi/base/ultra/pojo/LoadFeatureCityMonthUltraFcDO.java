package com.tsintergy.lf.serviceapi.base.ultra.pojo;


import com.tsieframework.core.base.dao.BaseVO;
import com.tsieframework.core.base.vo.annotation.EntityUniqueIndex;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.UpdateTimestamp;

/**
 * 超短期准确率表；其中包含准确率特性
 */
@Data
@Entity
@Table(name = "load_feature_city_month_ultra_fc_service")
@EntityUniqueIndex({"dateTime", "cityId", "caliberId", "algorithmId", "type", "multipointType"})
public class LoadFeatureCityMonthUltraFcDO extends BaseVO {
    private static final long serialVersionUID = 7452399293533031099L;

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    protected String id;

    /**
     * 日期
     */
    @Column(name = "date_time")
    protected Date dateTime;

    /**
     * 城市
     */
    @Column(name = "city_id")
    protected String cityId;

    /**
     * 口径ID
     */
    @Column(name = "caliber_id")
    protected String caliberId;


    @Column(name = "algorithm_id")
    private String algorithmId;

    /**
     * 时间间隔 5五分钟 15 十五分钟
     */
    @Column(name = "type")
    private Integer type;

    @ApiModelProperty(value = "入库点数类型；示例5分钟：1；3；9对应时刻点:5;15;45")
    @Column(name = "multipoint_type")
    private Integer multipointType;

    /**
     * 最大实际负荷
     */
    @Column(name = "max_load")
    protected BigDecimal maxLoad;

    /**
     * 最小实际负荷
     */
    @Column(name = "min_load")
    protected BigDecimal minLoad;

    /**
     * 平均实际负荷
     */
    @Column(name = "avg_load")
    protected BigDecimal avgLoad;

    /**
     * 最大负荷发生时刻
     */
    @Column(name = "max_time")
    protected String maxTime;

    /**
     * 最小负荷发生时刻
     */
    @Column(name = "min_time")
    protected String minTime;

    /**
     * 最大负荷时刻准确率
     */
    @Column(name = "max_load_accuracy")
    protected BigDecimal maxLoadAccuracy;

    /**
     * 最小负荷时刻准确率
     */
    @Column(name = "min_load_accuracy")
    protected BigDecimal minLoadAccuracy;


    /**
     * 最高准确率
     */
    @Column(name = "max_accuracy")
    protected BigDecimal maxAccuracy;

    /**
     * 最低准确率
     */
    @Column(name = "min_accuracy")
    protected BigDecimal minAccuracy;

    /**
     * 最低准确率发生时间
     */
    @Column(name = "min_accuracy_time")
    protected String minAccuracyTime;

    /**
     * 平均准确率
     */
    @Column(name = "avg_accuracy")
    protected BigDecimal avgAccuracy;



    /**
     * 尖峰平均负荷
     */
    @Column(name = "peak")
    protected BigDecimal peak;

    /**
     * 低谷平均负荷
     */
    @Column(name = "trough")
    protected BigDecimal trough;

    /**
     * 峰谷差
     */
    @Column(name = "different")
    protected BigDecimal different;

    /**
     * 峰谷差率
     */
    @Column(name = "gradient")
    protected BigDecimal gradient;

    /**
     * 负荷率
     */
    @Column(name = "load_gradient")
    protected BigDecimal loadGradient;

    /**
     * 日电量
     */
    @Column(name = "energy")
    protected BigDecimal energy;


    @Column(name = "report")
    private Boolean report;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @CreationTimestamp
    protected Timestamp createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    @UpdateTimestamp
    protected Timestamp updateTime;
}
