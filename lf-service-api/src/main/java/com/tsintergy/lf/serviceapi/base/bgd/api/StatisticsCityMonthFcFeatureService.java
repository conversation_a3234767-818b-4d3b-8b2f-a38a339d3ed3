/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.bgd.api;

import com.tsieframework.core.base.service.FacadeService;
import com.tsintergy.lf.serviceapi.base.bgd.pojo.StatisticsCityMonthFcFeatureServiceDO;

import java.util.List;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/11/17 21:23
 * @Version: 1.0.0
 */
public interface StatisticsCityMonthFcFeatureService extends FacadeService {

    List<StatisticsCityMonthFcFeatureServiceDO> findStatisticsCityMonthFcFeature(String cityId, String caliberId, String algorithmId, String year, String month);

    List<StatisticsCityMonthFcFeatureServiceDO> findStatisticsCityMonthFcSeasonFeature(String cityId, String caliberId, String algorithmId, String year, List<String> months);

    List<StatisticsCityMonthFcFeatureServiceDO> findStatisticsCityMonthFcFeatures(String cityId,String caliberId,String algorithmId,String startYm, String endYm);

    List<StatisticsCityMonthFcFeatureServiceDO> findReportStatisticsMonthFcFeatureYM(String cityId,String caliberId,String startYm, String endYm);

    List<StatisticsCityMonthFcFeatureServiceDO> queryByYearAndMonths(String year, List<String> monthList);

    void saveOrUpdateList(List<StatisticsCityMonthFcFeatureServiceDO> result);

    List<StatisticsCityMonthFcFeatureServiceDO> queryAllCityByAlgoIdAndYmAndCaliberId(String caliberId, List<String> algoIdList, String year, String month);

    List<StatisticsCityMonthFcFeatureServiceDO> findReportStatisticsMonthFcFeature(String cityId,String caliberId,String year,String month);
}
