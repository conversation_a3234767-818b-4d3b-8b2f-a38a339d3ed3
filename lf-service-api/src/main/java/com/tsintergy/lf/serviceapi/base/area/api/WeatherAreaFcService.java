/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.area.api;

import com.tsintergy.lf.serviceapi.base.area.pojo.WeatherAreaFcDO;
import com.tsintergy.lf.serviceapi.base.area.pojo.WeatherAreaHisDO;
import java.util.Date;
import java.util.List;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/8/3 17:25
 * @Version: 1.0.0
 */
public interface WeatherAreaFcService {

    /**
     * 查询气象预测数据
     * @param areaId 气象城市ID
     * @param type 气象类型
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return
     */
    List<WeatherAreaFcDO> findWeatherCityFcDOs(String areaId, Integer type, Date startDate, Date endDate) throws Exception;


    void doInsertOrUpdate(WeatherAreaFcDO weatherAreaFcDO) throws Exception;


    void doInsertOrUpdateList(List<WeatherAreaFcDO> weatherAreaFcDOS) throws Exception;


    public void deleteByAreasIds(List<String> areaIds) throws Exception ;

    /**
     * 统计区域实际气象
     * @param areaIds
     * @param startDate
     * @param endDate
     * @return
     */
    void statAreaFcWeather(String areaId, Date startDate, Date endDate) throws Exception;

}

