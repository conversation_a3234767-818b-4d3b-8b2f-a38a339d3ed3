package com.tsintergy.lf.serviceapi.base.ultra.load.pojo;


import com.tsieframework.core.base.vo.annotation.EntityUniqueIndex;
import com.tsintergy.lf.core.enums.IfEnum;
import com.tsintergy.lf.serviceapi.base.ultra.load.pojo.NesdBasicPeriodDO;
import io.swagger.annotations.ApiModelProperty;
import java.sql.Date;
import java.sql.Timestamp;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 地区预测负荷
 */
@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "load_city_fc_ultra")
@EntityUniqueIndex({"dateTime", "cityId", "caliberId", "algorithmId", "multipointType"})
public class LoadCityFcUltraDO extends NesdBasicPeriodDO {


    /**
     * 日期
     */
    @Column(name = "date_time")
    private Date dateTime;

    /**
     * 城市ID
     */
    @Column(name = "city_id")
    private String cityId;

    /**
     * 口径ID
     */
    @Column(name = "caliber_id")
    private String caliberId;

    /**
     * 算法ID
     */
    @Column(name = "algorithm_id")
    private String algorithmId;

    @ApiModelProperty(value = "入库点数类型；示例5分钟：1；3；9对应时刻点:5;15;45")
    @Column(name = "multipoint_type")
    private Integer multipointType;

    /**
     * 是否为系统推荐的算法
     */
    @Column(name = "recommend")
    private Boolean recommend;

    /**
     * 是否上报结果
     */
    @Column(name = "report")
    private IfEnum report;


    /**
     * 是否上报成功
     */
    @Column(name = "succeed")
    private Boolean succeed;

    /***
     * @Description 上报时间
     * @Date 2023/1/10 16:47
     * <AUTHOR>
     **/
    @Column(name = "report_time")
    private Timestamp reportTime;

}
