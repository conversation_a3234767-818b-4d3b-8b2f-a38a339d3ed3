/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.bgd.api;


import com.tsintergy.lf.serviceapi.base.bgd.pojo.LoadFeatureIndustryMonthHisServiceDO;

import java.util.List;

/**
 * Description:  <br>
 *
 * @Author: Liujp
 */
public interface LoadFeatureIndustryMonthHisService {
    /**
     * 获取产业月特性
     *
     * @param tradeCode
     * @param startYM
     * @param endYM
     * @return
     */
    List<LoadFeatureIndustryMonthHisServiceDO> getLoadFeatureIndustryMonthHisServiceList(String cityId, String tradeCode, String startYM, String endYM);


    void saveOrUpdate(LoadFeatureIndustryMonthHisServiceDO loadFeatureIndustryMonthHisServiceDO);
}