/**
 *   
 *  Copyright (C), 2015‐2020, 北京清能互联科技有限公司  
 *  Author:  EDZ  
 *  Date:  2020/10/28 15:53  
 *  History:  
 *  <author> <time> <version> <desc> 
 */
package com.tsintergy.lf.serviceapi.base.load.dto;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**  
 * Description: 用户上报详细信息 <br> 
 * 
 * <AUTHOR>  
 * @create 2020/10/28  
 * @since 1.0.0  
 */
public class UserLoadReportDTO implements Serializable {

    @ApiModelProperty(value = "用户id",example = "1234567")
    private String userId;

    /**
     * 用户姓名
     */
    @ApiModelProperty(value = "用户姓名",example = "test")
    private String name;

    /**
     * 上报日期
     */
    @ApiModelProperty(value = "上报日期",example = "2022-01-01")
    private Date date;

    /**
     * 最大预测负荷
     */
    @ApiModelProperty(value = "最大预测负荷",example = "65535")
    private BigDecimal maxLoadFc;

    /**
     * 最大历史负荷
     */
    @ApiModelProperty(value = "最大历史负荷",example = "65535")
    private BigDecimal maxLoadHis;

    /**
     * 最小预测负荷
     */
    @ApiModelProperty(value = "最小预测负荷",example = "12345")
    private BigDecimal minLoadFc;

    /**
     * 最小历史负荷
     */
    @ApiModelProperty(value = "最小历史负荷",example = "12345")
    private BigDecimal minLoadHis;

    /**
     * 准确率
     */
    @ApiModelProperty(value = "准确率",example = "95")
    private BigDecimal accuracy;

    /**
     * 上报时间
     */
    @ApiModelProperty(value = "上报时间",example = "2022-01-01")
    private String reportTime;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public BigDecimal getMaxLoadFc() {
        return maxLoadFc;
    }

    public void setMaxLoadFc(BigDecimal maxLoadFc) {
        this.maxLoadFc = maxLoadFc;
    }

    public BigDecimal getMaxLoadHis() {
        return maxLoadHis;
    }

    public void setMaxLoadHis(BigDecimal maxLoadHis) {
        this.maxLoadHis = maxLoadHis;
    }

    public BigDecimal getMinLoadFc() {
        return minLoadFc;
    }

    public void setMinLoadFc(BigDecimal minLoadFc) {
        this.minLoadFc = minLoadFc;
    }

    public BigDecimal getMinLoadHis() {
        return minLoadHis;
    }

    public void setMinLoadHis(BigDecimal minLoadHis) {
        this.minLoadHis = minLoadHis;
    }

    public String getReportTime() {
        return reportTime;
    }

    public void setReportTime(String reportTime) {
        this.reportTime = reportTime;
    }

    public BigDecimal getAccuracy() {
        return accuracy;
    }

    public void setAccuracy(BigDecimal accuracy) {
        this.accuracy = accuracy;
    }
}