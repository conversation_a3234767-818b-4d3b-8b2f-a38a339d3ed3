package com.tsintergy.lf.serviceapi.base.ultra.load.pojo;

import com.tsieframework.core.base.dao.BaseDO;
import io.swagger.annotations.ApiModelProperty;
import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2021/11/18
 * @since 1.0.0
 */
@Data
@MappedSuperclass
public class NesdBaseDO implements BaseDO {

    /**
     * id;主键
     */
    @ApiModelProperty(value = "id")
    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    protected String id;
}
