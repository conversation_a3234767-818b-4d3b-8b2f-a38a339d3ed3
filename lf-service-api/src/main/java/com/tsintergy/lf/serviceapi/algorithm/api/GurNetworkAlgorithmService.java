package com.tsintergy.lf.serviceapi.algorithm.api;

import com.tsintergy.aif.algorithm.serviceapi.base.dto.shortforecast.ShortForecastResult;

import java.util.Date;

/**
 * Description: TODO <br>
 *
 * <AUTHOR>
 * @create 2023-06-20
 * @since 1.0.0
 */
public interface GurNetworkAlgorithmService {

    /**
     * gur网络预测
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param caliberId 口径id
     * @param cityId    城市id
     */
    void doGurNetworkForecast(Date startDate, Date endDate, String caliberId, String cityId, Integer pointNum, Integer weatherType,
                              Integer type, Integer apparentType);


    /**
     * 保存短期算法预测结果
     *
     * @param forecastResult
     * @param cityId
     * @param caliberId
     * @param gurId
     * @param startDate
     */
    void saveResult(ShortForecastResult forecastResult, String cityId, String caliberId, String gurId, Date startDate);

}
