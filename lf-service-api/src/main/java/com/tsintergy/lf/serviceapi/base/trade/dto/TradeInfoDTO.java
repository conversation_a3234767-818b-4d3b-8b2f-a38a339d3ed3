/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.trade.dto;

import com.tsieframework.core.base.domain.dto.DTO;
import java.util.List;

/**
 * Description:  <br>
 *行业信息
 * @Author: <EMAIL>
 * @Date: 2022/9/2 15:28
 * @Version: 1.0.0
 */
public class TradeInfoDTO implements DTO {


    private String key;

    private String title;

    List<TradeInfoDTO> children;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public List<TradeInfoDTO> getChildren() {
        return children;
    }

    public void setChildren(List<TradeInfoDTO> children) {
        this.children = children;
    }
}