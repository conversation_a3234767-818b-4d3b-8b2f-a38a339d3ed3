package com.tsintergy.lf.serviceapi.base.bgd.dto;

import com.tsieframework.core.base.domain.dto.DTO;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * Description: 月份气象特性设置
 *
 * <AUTHOR>
 * @create 2023-06-13
 * @since 1.0.0
 */
@Data
public class MonthWeatherSettingDTO implements DTO {

    private String cityId;

    private Integer type;

    private List<String> hisYearDate;

    private Date date;

    private String startDate;

    private String endDate;

    private Integer status;
}
