package com.tsintergy.lf.serviceapi.base.bgd.pojo;

import com.tsieframework.core.base.dao.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import java.sql.Date;
import java.sql.Timestamp;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.UpdateTimestamp;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2023-03-23
 * @since 1.0.0
 */
@Data
@Entity
@Table(name = "LOAD_CITY_DAY_REPORT_SERVICE")
public class LoadCityDayReportServiceDO extends BaseVO {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_GENERATOR)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    @ApiModelProperty(value = "id")
    private String id;

    /**
     * 日期
     */
    @ApiModelProperty(value = "日期")
    @Column(name = "date")
    private Date date;

    /**
     * 城市ID
     */
    @Column(name = "city_id")
    private String cityId;

    /**
     * 口径ID
     */
    @Column(name = "caliber_id")
    private String caliberId;

    /**
     * 算法ID
     */
    @ApiModelProperty(value = "算法ID")
    @Column(name = "algorithm_id")
    private String algorithmId;

    /**
     * 首次上报时间
     */
    @Column(name = "first_report_time")
    private Timestamp firstReportTime;

    /**
     * 首次上报截止时间
     */
    @Column(name = "first_report_end_time")
    private Timestamp firstReportEndTime;

    /**
     * 最终上报时间
     */
    @Column(name = "last_report_time")
    private Timestamp lastReportTime;

    /**
     * 最终上报截止时间
     */
    @Column(name = "last_report_end_time")
    private Timestamp lastReportEndTime;

    /**
     * 创建时间
     */
    @Column(name = "createtime")
    @CreationTimestamp
    private Timestamp createtime;

    /**
     * 更新时间
     */
    @Column(name = "updatetime")
    @UpdateTimestamp
    private Timestamp updatetime;

}
