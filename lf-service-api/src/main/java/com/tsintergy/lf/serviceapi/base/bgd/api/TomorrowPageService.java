/**
 * Copyright (C), 2015‐2022, 北京清能互联科技有限公司 Author:  <EMAIL> Date:  2022/5/24 10:13 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.bgd.api;

import com.tsieframework.core.base.service.FacadeService;
import com.tsintergy.lf.serviceapi.base.bgd.dto.LongInfoDTO;

import java.util.Date;

/**
 * Description: 次日页面所有接口 <br>
 *
 * <AUTHOR>
 */
public interface TomorrowPageService extends FacadeService {

    /**
     * 获取中长期气象概要
     * @param cityId
     * @param date
     * @param dateType
     * @return
     */
    LongInfoDTO getLongInfoDTO(String cityId, Date date, Integer dateType, String season);
}
