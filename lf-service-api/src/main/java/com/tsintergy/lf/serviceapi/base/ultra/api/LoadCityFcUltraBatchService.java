package com.tsintergy.lf.serviceapi.base.ultra.api;


import com.tsintergy.lf.serviceapi.base.ultra.pojo.LoadCityFcUltraBatchDO;
import java.util.Date;
import java.util.List;

/**
 * @Description
 * @Date 2023/1/10 10:35
 * <AUTHOR>
 **/
public interface LoadCityFcUltraBatchService {

    /***
     * @Description 获取条件内所有批次预测的数据
     * @Date 2023/1/10 10:40
     * <AUTHOR>
     **/
    List<LoadCityFcUltraBatchDO> listLoadCityFcUltraDOS(String cityId, String caliberId, String algorithmId, Integer tDelta, Integer periodType, String startPeriodTime, String endPeriodTime, Date startDate, Date endDate);

    /***
     * @Description 根据id获取对象实体
     *
     * @Param id
     * @return com.tsintergy.aif.ultra.serviceapi.data.load.pojo.LoadCityFcUltraBatchDO
     * @Date 2023/1/10 10:52
     * <AUTHOR>
     **/
    LoadCityFcUltraBatchDO getLoadCityFcUltraDOById(String id);


    LoadCityFcUltraBatchDO findUltraData(String startTime, Date date, String cityId, String caliberId, String algorithmId, Integer delta);

    void doSaveOrUpdate(LoadCityFcUltraBatchDO LoadCityFcUltraBatchDO);

    List<LoadCityFcUltraBatchDO> findLoadCityFcUltraDOS(String startTime, Date date, String cityId, String caliberId, String algorithmId, Integer type);


}
