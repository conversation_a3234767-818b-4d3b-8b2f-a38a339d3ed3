package com.tsintergy.lf.serviceapi.base.bgd.dto;

import com.tsieframework.core.base.domain.dto.DTO;
import java.math.BigDecimal;
import lombok.Data;

/**
 * Description: TODO <br>
 *
 * <AUTHOR>
 * @create 2023-02-16
 * @since 1.0.0
 */
@Data
public class MonthReportDTO implements DTO {

    /**
     * 上中下旬
     */
    private String type;

    private String cityId;

    private String caliberId;
    /**
     * 当前日期
     */
    private String date;

    private BigDecimal maxLoad;

    private BigDecimal minLoad;

    private BigDecimal noonTimeLoad;

    /**
     * 旬电量
     */
    private BigDecimal energy;

    private BigDecimal extremeMaxLoad;

    private BigDecimal extremeMinLoad;

    private BigDecimal extremeNoonTimeLoad;

    private BigDecimal extremeEnergy;
}
