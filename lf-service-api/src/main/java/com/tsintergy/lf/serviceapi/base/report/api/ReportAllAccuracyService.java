/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.report.api;

import com.tsintergy.lf.serviceapi.base.common.enumeration.ParamDate;
import com.tsintergy.lf.serviceapi.base.report.pojo.ReportAccuracyMonthDO;
import java.util.Date;
import java.util.List;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/4/1 13:27
 * @Version: 1.0.0
 */
public interface ReportAllAccuracyService {
    /**
     * 调用所有统计准确率
     * @param start
     * @param end
     * @throws Exception
     */
    void doReportAccuracy(Date start,Date end,String cityId) throws Exception;


    void doStatAomprehensiveAccuracy(Date endDate, String cityId) throws Exception;
    /**
     * 日准确率
     */
    public  void  statDayAccuracy(ParamDate paramDate,String cityId) throws Exception;

    /**
     * 周准确率

     */
    public  void  statWeekAccuracy(ParamDate paramDate,String cityId) throws Exception;
    /**
     * 月准确率
     * @return

     */
    public List<ReportAccuracyMonthDO> statMonthAccuracy(ParamDate paramDate,String cityId) throws Exception;

    /**
     * 月综合准确率
     */
    public  void statSynthesizeMonthAccuracy(ParamDate paramDate,String cityId) throws Exception;
}
