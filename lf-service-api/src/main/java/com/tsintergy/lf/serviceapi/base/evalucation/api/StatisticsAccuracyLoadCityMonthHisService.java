/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.evalucation.api;

import com.tsintergy.lf.serviceapi.base.base.pojo.CityDO;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.MultipleAccuracyDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.dto.SingleAccuraryDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.StatisticsAccuracyLoadCityMonthHisDO;
import java.util.List;
import java.util.Map;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/3/31 15:22
 * @Version: 1.0.0
 */
public interface StatisticsAccuracyLoadCityMonthHisService {

    List<MultipleAccuracyDTO> getAvgAccuracyLoadCityMonthHisDOs(Map<String, String> map, String caliberId,
        String startYM, String endYM) throws Exception;

    List<StatisticsAccuracyLoadCityMonthHisDO> getReportMonthAccuracy(List<CityDO> cityVOS, String caliberId,
        String startYM, String endYM) throws Exception;

    List<SingleAccuraryDTO> getStatisticsAccuracyLoadCityMonthHisDOs(String cityName, String cityId, String caliberId,
        String startYM, String endYM) throws Exception;

    void doSaveOrUpdate(List<StatisticsAccuracyLoadCityMonthHisDO> reportAccuracyMonthDOS) throws Exception;
}
