/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.bgd.api;

import com.tsieframework.core.base.service.FacadeService;
import com.tsintergy.lf.serviceapi.base.bgd.dto.FeaturesDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.MonthWeatherDefaultDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.MonthWeatherResultDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.MonthWeatherSettingDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.TempCompareDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.SeasonReportDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.TenDaysOfMonthAccuracyDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.TenDaysOfMonthFeatureDTO;
import java.util.Date;
import java.util.List;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/11/19 13:49
 * @Version: 1.0.0
 */
public interface SeasonForecastModelService extends FacadeService {

    List<TenDaysOfMonthAccuracyDTO> getHistoryAccuracy(String cityId, String caliberId, Date date, String year, String season)
        throws Exception;

    List<TempCompareDTO> getTempCompareDTOList(String cityId,String year,String season,Integer type,String startStr, String endStr) throws Exception;

    List<FeaturesDTO> getFeatureList(String cityId,String year,String season,String algorithmId) throws Exception;

    List<TenDaysOfMonthFeatureDTO> getMonthLoadFc(String cityId, String caliberId, Date date, String season,
                                                  String algorithmId);

    List<TenDaysOfMonthFeatureDTO> getMonthLoadFcCompare(String cityId, String caliberId, Date date, String season,
                                                         String algorithmId, String startStr, String endStr)
        throws Exception;

    List<TenDaysOfMonthFeatureDTO> getMonthReportList(String cityId, String caliberId, Date date, String algorithmName,
                                                      String season,String startStr, String endStr) throws Exception;

    void report(List<SeasonReportDTO> monthReportDTOS) throws Exception;

    String getReportTime(String cityId, String caliberId, Date date, String season) throws Exception;

    List<MonthWeatherResultDTO> getWeatherSetting(MonthWeatherSettingDTO monthWeatherSettingDTO) throws Exception;

    void saveWeatherSetting(List<MonthWeatherResultDTO> monthWeatherResultDTOS) throws Exception;

    List<MonthWeatherResultDTO> getWeatherInfo(String startStr, String endStr, String cityId) throws Exception;

}
