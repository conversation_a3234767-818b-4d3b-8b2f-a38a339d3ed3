package com.tsintergy.lf.serviceapi.base.report.dto;


import java.io.Serializable;
import java.math.BigDecimal;

/**
 *  Description: 用户上报准确率 <br> 
 *  
 *  <AUTHOR>
 *  @create 2021/1/18
 *  @since 1.0.0 
 */

public class ReportUserAccuracyDTO implements Serializable {
    private String userId;

    private String name;

    private int days;

    private BigDecimal maxAccuracy;

    private BigDecimal minAccuracy;

    private BigDecimal avgAccuracy;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getDays() {
        return days;
    }

    public void setDays(int days) {
        this.days = days;
    }

    public BigDecimal getMaxAccuracy() {
        return maxAccuracy;
    }

    public void setMaxAccuracy(BigDecimal maxAccuracy) {
        this.maxAccuracy = maxAccuracy;
    }

    public BigDecimal getMinAccuracy() {
        return minAccuracy;
    }

    public void setMinAccuracy(BigDecimal minAccuracy) {
        this.minAccuracy = minAccuracy;
    }

    public BigDecimal getAvgAccuracy() {
        return avgAccuracy;
    }

    public void setAvgAccuracy(BigDecimal avgAccuracy) {
        this.avgAccuracy = avgAccuracy;
    }
}
