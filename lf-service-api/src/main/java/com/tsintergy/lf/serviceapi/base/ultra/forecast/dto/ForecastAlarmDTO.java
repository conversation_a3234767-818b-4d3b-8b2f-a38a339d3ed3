package com.tsintergy.lf.serviceapi.base.ultra.forecast.dto;

import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2023-10-18
 * @since 1.0.0
 */
@Data
public class ForecastAlarmDTO implements Serializable {

    private String alarmTime;

    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal fcLoad;

    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal hisLoad;

    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal fcCompare;

    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal alarmLoad;
}
