package com.tsintergy.lf.serviceapi.base.forecast.dto;

import com.tsieframework.core.base.domain.dto.DTO;
import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * Description: 气象预测特征值
 *
 * <AUTHOR>
 * @create 2023-03-06
 * @since 1.0.0
 */
@Data
public class TenDayWeatherFeatureDTO implements DTO {

    /**
     * 日期
     */
    private Date date;

    /**
     * 最高温度
     */
    @BigdecimalJsonFormat(scale = 2)
    private BigDecimal highestTemperature;

    /**
     * 最低温度
     */
    @BigdecimalJsonFormat(scale = 2)
    private BigDecimal lowestTemperature;

    /**
     * 平均温度
     */
    @BigdecimalJsonFormat(scale = 2)
    private BigDecimal aveTemperature;

    /**
     * 最高体感温度
     */
    @BigdecimalJsonFormat(scale = 2)
    private BigDecimal highestEffectiveTemperature;

    /**
     * 最低体感温度
     */
    @BigdecimalJsonFormat(scale = 2)
    private BigDecimal lowestEffectiveTemperature;

    /**
     * 平均体感温度
     */
    @BigdecimalJsonFormat(scale = 2)
    private BigDecimal aveEffectiveTemperature;

    /**
     * 最大日降雨量
     */
    @BigdecimalJsonFormat(scale = 2)
    private BigDecimal rainfall;

    /**
     * 最大风速
     */
    @BigdecimalJsonFormat(scale = 2)
    private BigDecimal maxWinds;

    /**
     * 相对湿度
     */
    @BigdecimalJsonFormat(scale = 2)
    private BigDecimal aveHumidity;

}
