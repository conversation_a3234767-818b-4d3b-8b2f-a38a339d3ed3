package com.tsintergy.lf.serviceapi.base.bgd.dto;

import com.tsieframework.core.base.domain.dto.DTO;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * Description: 月度典型曲线
 *
 * <AUTHOR>
 * @create 2023-03-09
 * @since 1.0.0
 */
@Data
public class MonthTypicalCurveDTO implements DTO {

    /**
     * 工作日96点负荷
     */
    private List<BigDecimal> workdayList;

    /**
     * 休息日96点负荷
     */
    private List<BigDecimal> weekendList;

}
