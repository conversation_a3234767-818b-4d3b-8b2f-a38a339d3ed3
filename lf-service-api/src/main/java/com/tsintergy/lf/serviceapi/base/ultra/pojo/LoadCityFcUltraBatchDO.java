package com.tsintergy.lf.serviceapi.base.ultra.pojo;


import com.tsieframework.core.base.dao.BaseVO;
import com.tsieframework.core.base.dao.type.DataPointListMeta;
import com.tsieframework.core.base.dao.type.TsieCustomType;
import com.tsieframework.core.base.vo.annotation.EntityUniqueIndex;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;
import java.util.List;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.Columns;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.UpdateTimestamp;

/**
 * 超短期预测实体
 *
 * <AUTHOR>
 */
@Data
@Entity
@EntityUniqueIndex({"cityId", "dateTime", "startTime", "type", "tvMeta", "algorithmId"})
@Table(name = "load_city_fc_ultra_batch")
public class LoadCityFcUltraBatchDO extends BaseVO {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;

    /**
     * 日期
     */
    @Column(name = "date_time")
    private Date dateTime;

    /**
     * 城市ID
     */
    @Column(name = "city_id")
    private String cityId;

    /**
     * 口径ID
     */
    @Column(name = "caliber_id")
    private String caliberId;

    /**
     * 算法ID
     */
    @Column(name = "algorithm_id")
    private String algorithmId;

    /**
     * 开始时间 例 0015
     */
    @Column(name = "start_time")
    private String startTime;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @CreationTimestamp
    private Timestamp createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    @UpdateTimestamp
    private Timestamp updateTime;

    /**
     * 数据点配置
     */
    @ApiModelProperty
    @Type(type = TsieCustomType.CustomCollectionType.DataPointListMetaType)
    @Columns(columns = {
            @Column(name = "t_start_time"),
            @Column(name = "t_size"),
            @Column(name = "t_delta"),
            @Column(name = "t_delta_unit")
    })
    protected DataPointListMeta tvMeta;

    @ApiModelProperty(value = "数据")
    @Column(name = "TV_DATA")
    @Type(type = TsieCustomType.CustomCollectionType.BigDecimalListType)
    protected List<BigDecimal> tvData;

}
