/**
 * Copyright (C), 2015‐2022, 北京清能互联科技有限公司 Author:  <EMAIL> Date:  2022/5/26 15:09 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.load.api;

import java.util.Date;

/**
 * Description: 空调预测负荷特性 <br>
 *
 * <AUTHOR>
 * @create 2022/5/26
 * @since 1.0.0
 */
public interface LoadFeatureAcFcService {

    void doFcLoadFeatureCityDay(Date startDate, Date endDate) throws Exception;
}
