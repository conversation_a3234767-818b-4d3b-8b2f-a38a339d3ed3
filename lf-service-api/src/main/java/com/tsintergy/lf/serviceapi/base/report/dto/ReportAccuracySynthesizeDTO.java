package com.tsintergy.lf.serviceapi.base.report.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;

@ApiModel("上报准确率DTO")
public class ReportAccuracySynthesizeDTO implements Serializable {
    @ApiModelProperty("编号")
    private String id;
    @ApiModelProperty("城市名称")
    private String cityName;
    @ApiModelProperty("城市id")
    private String cityId;
    @ApiModelProperty("综合准确率")
    private BigDecimal comprehensiveAccuracy;
    @ApiModelProperty("综合准确率（修正）")
    private BigDecimal comprehensiveAccuracyRight;
    @ApiModelProperty("日期")
    private String date;

    public String getCityId() {
        return cityId;
    }

    public void setCityId(String cityId) {
        this.cityId = cityId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public BigDecimal getComprehensiveAccuracy() {
        return comprehensiveAccuracy;
    }

    public void setComprehensiveAccuracy(BigDecimal comprehensiveAccuracy) {
        this.comprehensiveAccuracy = comprehensiveAccuracy;
    }

    public BigDecimal getComprehensiveAccuracyRight() {
        return comprehensiveAccuracyRight;
    }

    public void setComprehensiveAccuracyRight(BigDecimal comprehensiveAccuracyRight) {
        this.comprehensiveAccuracyRight = comprehensiveAccuracyRight;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }
}
