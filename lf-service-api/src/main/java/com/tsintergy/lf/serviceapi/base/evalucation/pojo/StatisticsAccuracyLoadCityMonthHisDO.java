package com.tsintergy.lf.serviceapi.base.evalucation.pojo;

import com.tsieframework.core.base.dao.BaseVO;
import java.math.BigDecimal;
import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;
import com.tsieframework.core.base.dao.BaseVO;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.math.BigDecimal;
import java.sql.Timestamp;
@Data
@Entity
@Table(name = "statistics_accuracy_load_city_month_his")
public class StatisticsAccuracyLoadCityMonthHisDO extends BaseVO {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;

    @Column(name = "city_id")
    private String cityId;

    /**
     * 口径ID
     */
    @Column(name = "caliber_id")
    private String caliberId;

    @Column(name = "year")
    private String year;

    @Column(name = "month")
    private String month;

    /**
     * 准确率
     */
    @Column(name = "accuracy")
    private BigDecimal accuracy;

    @Column(name = "createtime")
    private Timestamp createtime;

    @Column(name = "updatetime")
    private Timestamp updatetime;
}