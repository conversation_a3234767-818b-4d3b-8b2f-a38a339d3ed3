package com.tsintergy.lf.serviceapi.base.bgd.pojo;

import com.tsieframework.core.base.dao.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.sql.Timestamp;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.UpdateTimestamp;

/**
 * Description: 月度地市上报
 *
 * <AUTHOR>
 * @create 2023-03-22
 * @since 1.0.0
 */
@Data
@Entity
@Table(name = "LOAD_CITY_MONTH_ACCURACY_REPORT")
public class LoadCityMonthAccuracyReportDO extends BaseVO {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_GENERATOR)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    @ApiModelProperty(value = "id")
    private String id;

    /**
     * 城市ID
     */
    @Column(name = "city_id")
    private String cityId;

    /**
     * 口径ID
     */
    @Column(name = "caliber_id")
    private String caliberId;

    /**
     * 年
     */
    @Column(name = "year")
    private String year;

    /**
     * 月
     */
    @Column(name = "month")
    private String month;

    /**
     * 月度日均负荷率
     */
    @Column(name = "day_load_accuracy")
    private BigDecimal dayLoadAccuracy;

    /**
     * 月度最大
     */
    @Column(name = "max_load_accuracy")
    private BigDecimal maxLoadAccuracy;

    /**
     * 月度最小
     */
    @Column(name = "min_load_accuracy")
    private BigDecimal minLoadAccuracy;

    /**
     * 月电量
     */
    @Column(name = "energy_accuracy")
    private BigDecimal energyAccuracy;

    /**
     * 创建时间
     */
    @Column(name = "createtime")
    @CreationTimestamp
    private Timestamp createtime;

    /**
     * 更新时间
     */
    @Column(name = "updatetime")
    @UpdateTimestamp
    private Timestamp updatetime;

}
