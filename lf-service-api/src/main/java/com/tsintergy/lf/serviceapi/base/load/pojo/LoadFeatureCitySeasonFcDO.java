package com.tsintergy.lf.serviceapi.base.load.pojo;

import com.tsieframework.core.base.dao.BaseVO;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.UpdateTimestamp;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2023-03-29
 * @since 1.0.0
 */

@Data
@Entity
@Table(name = "load_feature_city_season_fc_service")
public class LoadFeatureCitySeasonFcDO extends BaseVO {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;

    /**
     * 年
     */
    @Column(name = "year")
    private String year;

    /**
     * 月
     */
    @Column(name = "month")
    private String month;

    /**
     * 季度
     */
    @Column(name = "season")
    private String season;

    /**
     * 城市ID
     */
    @Column(name = "city_id")
    private String cityId;

    /**
     * 口径ID
     */
    @Column(name = "caliber_id")
    private String caliberId;

    @Column(name = "algorithm_id")
    private String algorithmId;

    /**
     * 最大负荷
     */
    @Column(name = "max_load")
    private BigDecimal maxLoad;

    /**
     * 最小负荷
     */
    @Column(name = "min_load")
    private BigDecimal minLoad;

    /**
     * 平均负荷
     */
    @Column(name = "ave_load")
    private BigDecimal aveLoad;

    /**
     * 最大负荷日
     */
    @Column(name = "max_month")
    private String maxMonth;

    @Column(name = "min_month")
    private String minMonth;

    @Column(name = "energy")
    private BigDecimal energy;
    /**
     * 创建时间
     */
    @Column(name = "createtime")
    @CreationTimestamp
    private Date createtime;

    /**
     * 更新时间
     */
    @Column(name = "updatetime")
    @UpdateTimestamp
    private Date updatetime;

    @Column(name = "report")
    private Boolean report;

    @Column(name = "report_time")
    private Timestamp reportTime;

    @Column(name = "extreme_max_load")
    private BigDecimal extremeMaxLoad;

    @Column(name = "extreme_min_load")
    private BigDecimal extremeMinLoad;

    @Column(name = "extreme_energy")
    private BigDecimal extremeEnergy;

    @Column(name = "extreme_noontime_load")
    private BigDecimal extremeNoontimeLoad;

    @Column(name = "noontime_load")
    private BigDecimal noontimeLoad;
}
