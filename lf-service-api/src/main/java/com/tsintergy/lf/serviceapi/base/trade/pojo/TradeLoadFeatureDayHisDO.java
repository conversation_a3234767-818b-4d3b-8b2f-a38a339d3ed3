package com.tsintergy.lf.serviceapi.base.trade.pojo;


import com.tsintergy.lf.serviceapi.base.base.pojo.BaseLoadFeatureDayDO;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

/**
 * 日负荷特性
 */
@Data
@Entity
@Table(name = "TRADE_LOAD_FEATURE_DAY_HIS_SERVICE")
public class TradeLoadFeatureDayHisDO extends BaseLoadFeatureDayDO {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;


    @Column(name = "trade_code")
    private String tradeCode;
}
