package com.tsintergy.lf.serviceapi.base.bgd.dto;

import com.tsieframework.core.base.domain.dto.DTO;
import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import java.math.BigDecimal;
import javax.persistence.Column;
import lombok.Data;

/**
 * Description: 月维度 旬特性 <br>
 *
 * <AUTHOR>
 * @create 2023-02-16
 * @since 1.0.0
 */
@Data
public class TenDaysOfMonthFeatureDTO implements DTO {

    /**
     * 日期月份
     */
    private String month;

    /**
     * 日期类型上旬中旬下旬
     */
    private String type;

    /**
     * 算法
     */
    private String algorithmName;

    /**
     * 最大负荷
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal maxLoad;

    /**
     * 最大负荷极端
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal extremeMaxLoad;

    /**
     * 最小负荷
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal minLoad;

    /**
     * 最小负荷极端
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal extremeMinLoad;

    /**
     * 月电量
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal monthEnergy;

    /**
     * 月电量极端
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal extremeMonthEnergy;

    /**
     * 最小腰荷
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal noonTimeLoad;

    /**
     * 最小腰荷极端
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal extremeNoonTimeLoad;

    /**
     * 去年同期最大负荷
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal lastYearMaxLoad;
    /**
     * 去年同期最小负荷
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal lastYearMinLoad;

    /**
     * 去年同期电量
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal lastYearEnergy;

    /**
     * 去年同期最小腰荷
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal lastYearNoonTimeLoad;

    /**
     * 最大负荷准确率
     */
    private BigDecimal maxLoadAccuracy;

    /**
     * 最小负荷准确率
     */
    private BigDecimal minLoadAccuracy;

    /**
     * 电量准确率
     */
    private BigDecimal energyAccuracy;

    private BigDecimal noonTimeLoadAccuracy;
}
