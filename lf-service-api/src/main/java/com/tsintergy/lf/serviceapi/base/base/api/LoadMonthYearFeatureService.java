package com.tsintergy.lf.serviceapi.base.base.api;

import com.tsintergy.lf.serviceapi.base.base.dto.LoadMonthFeatureDTO;

import java.util.List;

/**
 * Description: TODO <br>
 *
 * <AUTHOR>
 * @create 2023-10-23
 * @since 1.0.0
 */
public interface LoadMonthYearFeatureService {

    List<LoadMonthFeatureDTO> getHisMonthFeature(String startDate, String endDate, String cityId,
                                                 String caliberId) throws Exception;


    List<LoadMonthFeatureDTO> getHisTenDaysFeature(String startDate, String endDate, String cityId,
                                                   String caliberId) throws Exception;

    List<LoadMonthFeatureDTO> getHisQuartersFeature(String startYQ, String endYQ, String cityId,
                                                    String caliberId) throws Exception;

}
