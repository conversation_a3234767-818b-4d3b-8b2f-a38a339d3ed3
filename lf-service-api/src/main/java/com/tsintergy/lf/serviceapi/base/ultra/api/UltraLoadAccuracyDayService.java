
package com.tsintergy.lf.serviceapi.base.ultra.api;


import com.tsintergy.lf.serviceapi.base.ultra.dto.AccuracyLowDetailsDayDTO;
import com.tsintergy.lf.serviceapi.base.ultra.dto.AccuracyUltraData;
import com.tsintergy.lf.serviceapi.base.ultra.dto.AccuracyUltraDayDTO;
import com.tsintergy.lf.serviceapi.base.ultra.dto.MultiPeriodAccuracyDTO;
import com.tsintergy.lf.serviceapi.base.ultra.dto.TimeAccuracyDTO;
import java.util.List;

/**
 * 日准确率评估
 *
 * <AUTHOR>
 */
public interface UltraLoadAccuracyDayService {

    /**
     * 日准确查询列表
     *
     * @param data 查询参数
     * @return 结果对象
     */
    List<AccuracyUltraDayDTO> getLoadHistory(AccuracyUltraData data) throws Exception;

    /**
     * 查询时刻点准确率
     * @param data 查询参数
     * @return 结果对象
     */
    TimeAccuracyDTO getTimeAccuracy(AccuracyUltraData data);

    /**
     * 查询最低准确率详情
     * @param data 查询参数
     * @return 结果对象
     */
    AccuracyLowDetailsDayDTO getAccuracyLowDetails(AccuracyUltraData data) throws Exception;



    /**
     * 多时段预测对比
     *
     * @param data 查询参数
     * @return 结果对象
     */
    MultiPeriodAccuracyDTO getMultiPeriodAccuracy(AccuracyUltraData data) throws Exception;


}