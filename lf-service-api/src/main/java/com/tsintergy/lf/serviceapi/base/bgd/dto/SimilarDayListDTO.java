package com.tsintergy.lf.serviceapi.base.bgd.dto;

import com.tsieframework.core.base.domain.dto.DTO;
import com.tsintergy.aif.algorithm.serviceapi.base.dto.DateValuesDTO;
import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import java.math.BigDecimal;
import lombok.Data;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2023-05-05
 * @since 1.0.0
 */
@Data
public class SimilarDayListDTO  extends DateValuesDTO implements DTO {

    /**
     * 日期
     */
    private String date;

    /**
     * 曲线相似度
     */
    private BigDecimal degree;

    /**
     * 最大负荷偏差
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal deviation;

}
