package com.tsintergy.lf.serviceapi.base.weather.pojo;

import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.aif.algorithm.serviceapi.base.pojo.Weather;
import com.tsintergy.lf.core.constants.Constants;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.math.BigDecimal;
import java.sql.Date;
import java.util.List;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2024-12-03
 * @since 1.0.0
 */
@Data
@Entity
@Table(name = "weather_city_fc_meteo_basic_batch")
public class WeatherCityFcMeteoBatchDO extends BaseWeatherDO implements Weather {


    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;

    /**
     * 日期
     */
    @Column(name = "date")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date date;

    /**
     * 城市ID
     */
    @Column(name = "city_id")
    private String cityId;

    /**
     * 气象类型
     */
    @Column(name = "type")
    private Integer type;

    /**
     * 批次号
     */
    @Column(name = "batch_id")
    private String batchId;

    @Override
    public List<BigDecimal> getWeatherList() {
        return BasePeriodUtils.toList(this, 96, Constants.LOAD_CURVE_START_WITH_ZERO);
    }
}
