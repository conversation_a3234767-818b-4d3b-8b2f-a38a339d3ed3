package com.tsintergy.lf.serviceapi.base.system.pojo;

import com.tsieframework.core.base.vo.CacheVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2023-05-08
 * @since 1.0.0
 */
@Data
@Entity
@Table(name = "report_system_init")
@ApiModel
public class ReportSystemInitDO extends CacheVO {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    @ApiModelProperty(value = "id",example = "1")
    private String id;

    /**
     * 城市id
     */
    @Column(name = "cityId")
    @ApiModelProperty(value = "城市id",example = "1")
    private String cityId;

    /**
     * 参数键
     */
    @Column(name = "field")
    @ApiModelProperty(value = "参数键",example = "short_report_time")
    private String field;

    /**
     * 参数值
     */
    @Column(name = "value")
    @ApiModelProperty(value = "参数值",example = "09:00-15:30")
    private String value;

    /**
     * 参数名
     */
    @Column(name = "name")
    @ApiModelProperty(value = "参数名",example = "短期上报设置时间")
    private String name;

    /**
     * 描述
     */
    @Column(name = "description")
    @ApiModelProperty(value = "描述",example = "无")
    private String description;

    /**
     * 限制状态
     */
    @Column(name = "status")
    @ApiModelProperty(value = "限制状态",example = "1")
    private Boolean status;

    /**
     * 限制状态2
     */
    @Column(name = "type")
    @ApiModelProperty(value = "限制状态2",example = "1")
    private Boolean type;

    @Override
    public String getKey() {
        return this.field;
    }

    @Override
    public String getLabel() {
        return this.name;
    }

}
