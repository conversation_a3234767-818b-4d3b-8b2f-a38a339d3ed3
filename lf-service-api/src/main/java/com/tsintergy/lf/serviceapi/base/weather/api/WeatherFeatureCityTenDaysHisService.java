package com.tsintergy.lf.serviceapi.base.weather.api;

import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityTenDaysHisDO;

import java.util.List;

/**
 * Description: TODO <br>
 *
 * <AUTHOR>
 * @create 2023-06-13
 * @since 1.0.0
 */
public interface WeatherFeatureCityTenDaysHisService {

    /**
     * 查询10日天气特征
     *
     * @param cityId 城市标识
     * @param year 一年
     * @param month 月
     * @param type 类型
     * @return {@code WeatherFeatureCityTenDaysHisDO}
     */
    List<WeatherFeatureCityTenDaysHisDO> findWeatherFeatureCityTenDaysDTO(String cityId, String year, String month,
                                                                          String type);

    List<WeatherFeatureCityTenDaysHisDO> findByRangeCondition(String cityId, String startYM, String endYM);

    /**
     * 做保存或更新
     *
     * @param weatherFeatureCityTenDaysHisDO 城市十天做天气特征
     */
    void doSaveOrUpdate(WeatherFeatureCityTenDaysHisDO weatherFeatureCityTenDaysHisDO);
}
