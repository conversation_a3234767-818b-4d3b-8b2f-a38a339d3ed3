/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.bgd.dto;

import com.tsieframework.core.base.domain.dto.DTO;
import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import lombok.Data;

import java.math.BigDecimal;

/**
 * Description:  <br>
 * 月维度 旬特性
 * @Author: <EMAIL>
 * @Date: 2022/11/17 15:41
 * @Version: 1.0.0
 */
@Data
public class TenDaysOfMonthAccuracyDTO implements DTO {

    private String algorithmName;

    /**
     * 日期类型上旬中旬下旬
     */
    private String type;
    /**
     * 最大负荷
     */
    @BigdecimalJsonFormat(percentConvert = 100, scale = 2)
    private BigDecimal maxLoad;
    /**
     *最小负荷
     */
    @BigdecimalJsonFormat(percentConvert = 100, scale = 2)
    private BigDecimal minLoad;
    /**
     * 月电量
     */
    @BigdecimalJsonFormat(percentConvert = 100, scale = 2)
    private BigDecimal monthEnergy;

    /**
     * 最小腰荷
     */
    @BigdecimalJsonFormat(percentConvert = 100, scale = 2)
    private BigDecimal noontimeLoad;
}
