/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.recall.pojo;

import com.tsintergy.lf.serviceapi.base.base.pojo.BaseStatisticsCityDayFcDO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.StatisticsCityDayFcDO;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

/**
 * Description: 预测回溯每日准确率 <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/8/1 12:06
 * @Version: 1.0.0
 */
@Data
@Entity
@Table(name = "statistics_city_day_fc_service_recall")
public class StatisticsCityDayFcRecallDO extends BaseStatisticsCityDayFcDO {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;

}