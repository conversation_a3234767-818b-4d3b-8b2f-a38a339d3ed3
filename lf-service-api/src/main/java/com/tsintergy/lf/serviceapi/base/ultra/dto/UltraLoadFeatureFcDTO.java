package com.tsintergy.lf.serviceapi.base.ultra.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.tsintergy.lf.serviceapi.base.common.jsonserialize.PercentSerialize2Digits;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @date: 3/21/18 4:41 PM
 * @author: angel
 **/
@ApiModel
public class UltraLoadFeatureFcDTO implements Serializable {

    /**
     * 最大负荷
     */
    @ApiModelProperty(value = "最大负荷")
    private BigDecimal maxLoad;

    /**
     * 最小负荷
     */
    @ApiModelProperty(value = "最小负荷")
    private BigDecimal minLoad;

    /**
     * 平均负荷
     */
    @ApiModelProperty(value = "平均负荷")
    private BigDecimal aveLoad;

    /**
     * 最大负荷发生时刻
     */
    @ApiModelProperty(value = "最大负荷发生时刻")
    private String maxTime;

    /**
     * 最小负荷发生时刻
     */
    @ApiModelProperty(value = "最小负荷发生时刻")
    private String minTime;

    /**
     * 峰谷差
     */
    @ApiModelProperty(value = "峰谷差")
    private BigDecimal different;

    /**
     * 峰谷差率
     */
    @JsonSerialize(using = PercentSerialize2Digits.class)
    @ApiModelProperty(value = "峰谷差率")
    private BigDecimal gradient;


    /**
     * 积分电量
     */
    @ApiModelProperty(value = "积分电量",example = "2312")
    private BigDecimal integralLoad;

    public BigDecimal getMaxLoad() {
        return maxLoad;
    }

    public void setMaxLoad(BigDecimal maxLoad) {
        this.maxLoad = maxLoad;
    }

    public BigDecimal getMinLoad() {
        return minLoad;
    }

    public void setMinLoad(BigDecimal minLoad) {
        this.minLoad = minLoad;
    }

    public BigDecimal getAveLoad() {
        return aveLoad;
    }

    public void setAveLoad(BigDecimal aveLoad) {
        this.aveLoad = aveLoad;
    }

    public String getMaxTime() {
        return maxTime;
    }

    public void setMaxTime(String maxTime) {
        this.maxTime = maxTime;
    }

    public String getMinTime() {
        return minTime;
    }

    public void setMinTime(String minTime) {
        this.minTime = minTime;
    }

    public BigDecimal getDifferent() {
        return different;
    }

    public void setDifferent(BigDecimal different) {
        this.different = different;
    }

    public BigDecimal getGradient() {
        return gradient;
    }

    public void setGradient(BigDecimal gradient) {
        this.gradient = gradient;
    }

    public BigDecimal getIntegralLoad() {
        return integralLoad;
    }

    public void setIntegralLoad(BigDecimal integralLoad) {
        this.integralLoad = integralLoad;
    }

}
