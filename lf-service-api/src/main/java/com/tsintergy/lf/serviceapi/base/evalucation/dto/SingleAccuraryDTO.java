package com.tsintergy.lf.serviceapi.base.evalucation.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

@Data
@ApiModel("日月年准确率参数")
public class SingleAccuraryDTO implements Serializable {

    /**
     * 城市名
     */
    @ApiModelProperty("城市名称")
    private String cityName;

    /**
     * 日期
     */
    @ApiModelProperty("日期")
    private String datetime;

    /**
     * 准确率
     */
    @ApiModelProperty("准确率")
    private BigDecimal accuracy;


    /**
     * 是否合格
     */
    @ApiModelProperty("是否合格")
    private Boolean isPass;


    /**
     * 是否考核
     */
    @ApiModelProperty("是否考核")
    private Boolean isCheck;
}