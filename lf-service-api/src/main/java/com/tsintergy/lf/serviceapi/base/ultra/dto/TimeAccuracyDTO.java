/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司 Author:  ThinkPad Date:  2019/5/15 7:26 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.ultra.dto;


import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * 超短期日准确率结果对象
 *
 * <AUTHOR>
 * @create 2023/1/11
 * @since 1.0.0
 */
@Data
public class TimeAccuracyDTO implements Serializable {


    /**
     * 超短期预测
     */
    @BigdecimalJsonFormat(scale = 0)
    private List<BigDecimal> fc;

    @BigdecimalJsonFormat(scale = 0)
    private List<BigDecimal> his;

    /**
     * 偏差
     */
    @BigdecimalJsonFormat(scale = 0)
    private List<BigDecimal> deviation;

    /**
     * 准确率
     */
    @BigdecimalJsonFormat(percentConvert = 100)
    private List<BigDecimal> accuracy;

}