package com.tsintergy.lf.serviceapi.base.evalucation.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import lombok.Data;

@Data
@ApiModel("假日准确率")
public class HolidayAccuracyDTO {
    @ApiModelProperty("城市")
    private String city;
    /**
     * 节假日
     */
    @ApiModelProperty("节假日")
    private String holiday;
    @ApiModelProperty("code")
    private Integer code;
    @ApiModelProperty("开始时间")
    private Date startDate;
    @ApiModelProperty("结束时间")
    private Date endDate;

    /**
     * 调休日期，多日以，隔开
     */
    @ApiModelProperty("调休日期")
    private List<Date> offDates;

    @ApiModelProperty("假日准确率")
    private List<SingleHolidayAccuracyDTO> holidayAccuracy;

    /**
     * 人工决策算法准确率
     */
    @ApiModelProperty("人工决策算法准确率")
    private String avgManualAccuracy;

    /**
     * 节假日算法预测准确率
     */
    @ApiModelProperty("节假日算法预测准确率")
    private String avgholidayAlgorithmAccuracy;
}