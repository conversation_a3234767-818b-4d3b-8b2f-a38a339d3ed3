/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.analyze.dto;

import com.tsieframework.core.base.domain.dto.DTO;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Description:  <br>
 *冬夏分析页面 负荷分析
 * @Author: <EMAIL>
 * @Date: 2022/8/22 15:38
 * @Version: 1.0.0
 */
public class LoadComparisonDTO implements DTO {

    /**
     * 年份
     */
    private String year;

    /**
     * 日期
     */
    private Date date;

    /**
     * 最大负荷
     */
    private BigDecimal maxLoad;

    /**
     * 负荷增长率
     */
    private BigDecimal loadGrowthRate;


    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public BigDecimal getMaxLoad() {
        return maxLoad;
    }

    public void setMaxLoad(BigDecimal maxLoad) {
        this.maxLoad = maxLoad;
    }

    public BigDecimal getLoadGrowthRate() {
        return loadGrowthRate;
    }

    public void setLoadGrowthRate(BigDecimal loadGrowthRate) {
        this.loadGrowthRate = loadGrowthRate;
    }
}