/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.area.pojo;

import com.tsieframework.core.base.dao.BaseVO;
import java.sql.Timestamp;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.UpdateTimestamp;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/8/3 17:37
 * @Version: 1.0.0
 */
@Data
@Entity
@Table(name = "base_area_info")
public class BaseAreaDO extends BaseVO {
    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;
    @Column(name = "name")
    private String name;
    @Column(name = "city_ids")
    private String cityIds;
    @Column(name = "description")
    private String description;
    @Column(name = "create_time")
    @CreationTimestamp
    private Timestamp createTime;
    @UpdateTimestamp
    @Column(name = "update_time")
    private Timestamp updateTime;

}