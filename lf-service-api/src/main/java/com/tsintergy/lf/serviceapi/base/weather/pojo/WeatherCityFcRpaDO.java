package com.tsintergy.lf.serviceapi.base.weather.pojo;

import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.aif.algorithm.serviceapi.base.pojo.Weather;
import com.tsintergy.lf.core.constants.Constants;
import java.math.BigDecimal;
import java.sql.Date;
import java.util.List;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2024-12-03
 * @since 1.0.0
 */
@Data
@Entity
@Table(name = "weather_city_fc_rpa_basic")
public class WeatherCityFcRpaDO extends BaseWeatherDO implements Weather {

    public WeatherCityFcRpaDO() {
        super();
    }

    public WeatherCityFcRpaDO(String cityId, Integer type, java.util.Date date) {
        super(cityId, type, date);
    }

    /**
     * 站点ID
     */
    @Column(name = "station_id")
    private String stationId;

    /**
     * 上传日期
     */
    @Column(name = "upload_date")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date uploadDate;

    @Override
    public List<BigDecimal> getWeatherList() {
        return BasePeriodUtils.toList(this, 96, Constants.LOAD_CURVE_START_WITH_ZERO);
    }
}
