package com.tsintergy.lf.serviceapi.base.weather.api;

import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcRpaDO;
import java.util.List;

/**
 * Description: TODO <br>
 *
 * <AUTHOR>
 * @create 2024-12-04
 * @since 1.0.0
 */
public interface WeatherCityFcRpaService {

    void doSaveOrUpdate(List<WeatherCityFcRpaDO> weatherCityFcRpaDOList);

    void doSave(List<WeatherCityFcRpaDO> weatherCityFcRpaDOList);
}
