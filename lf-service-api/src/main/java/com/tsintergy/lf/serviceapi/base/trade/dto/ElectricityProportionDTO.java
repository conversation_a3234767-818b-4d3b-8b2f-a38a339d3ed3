/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.trade.dto;

import com.tsieframework.core.base.domain.dto.DTO;
import java.math.BigDecimal;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/9/2 18:05
 * @Version: 1.0.0
 */


public class ElectricityProportionDTO implements DTO {

    /**
     * 行业名称
     */
    private String name;

    /**
     * 电量占比
     */
    private BigDecimal value;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public BigDecimal getValue() {
        return value;
    }

    public void setValue(BigDecimal value) {
        this.value = value;
    }
}