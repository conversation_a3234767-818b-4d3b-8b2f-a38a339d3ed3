package com.tsintergy.lf.serviceapi.base.load.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
@ApiModel("准确率评估")
public class AccuracyEstimationsDTO {

    @ApiModelProperty("日期")
    private List<AccuracyEstimationDTO> accuracyEstimation;
    //日准确率平均值
    @ApiModelProperty("日准确率平均值")
    private BigDecimal dailyAccuracyAvg;
    //日最大负荷准确率平均值
    @ApiModelProperty("日最大负荷准确率平均值")
    private BigDecimal dailyMaximumLoadAccuracyAvg;
    //最小负荷准确率平均值
    @ApiModelProperty("最小负荷准确率平均值")
    private BigDecimal dailyMinimumLoadAccuracyAvg;

    //最大最小准确率平均值
    @ApiModelProperty("最大最小准确率平均值")
    private BigDecimal dailyMaximumAndMinimumAccuracyAvg;

}
