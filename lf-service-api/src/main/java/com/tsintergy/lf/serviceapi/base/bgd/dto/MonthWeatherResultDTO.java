package com.tsintergy.lf.serviceapi.base.bgd.dto;

import com.tsieframework.core.base.domain.dto.DTO;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * Description: 月度气象特性结果
 *
 * <AUTHOR>
 * @create 2023-06-13
 * @since 1.0.0
 */
@Data
public class MonthWeatherResultDTO implements DTO {

    private String dateTime;

    private BigDecimal maxTem;

    private BigDecimal minTem;

    private BigDecimal aveTem;

    private BigDecimal extremeMaxTem;

    private BigDecimal extremeMinTem;

    private BigDecimal extremeAveTem;

    private BigDecimal hisMaxTem;

    private BigDecimal hisMinTem;

    private BigDecimal hisAveTem;

    private Integer type;

    private String cityId;

    private List<String> hisYearDate;

    private String season;

}
