package com.tsintergy.lf.serviceapi.base.report.pojo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.tsieframework.core.base.dao.BaseVO;
import com.tsieframework.core.base.vo.annotation.EntityUniqueIndex;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.UpdateTimestamp;

@Data
@Entity
@TableName("report_accuracy_synthesize_cumulative")
@EntityUniqueIndex({"cityId,date,type"})
public class ReportAccuracySynthesizeCumulativeDO extends BaseVO {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;


    @Column(name = "city_id")
    private String cityId;

    /**
     * 累计综合准确率
     */
    @Column(name = "accuracy")
    private BigDecimal accuracy;

    /**
     * 日期
     */
    @Column(name = "date")
    private java.sql.Date date;

    /**
     * 日期
     */
    @Column(name = "type")
    private Integer type;

    /**
     * 累计综合准确率（修正）
     */
    @Column(name = "accuracy_correct")
    private BigDecimal accuracyCorrect;

    /**
     * 96时刻点准确率
     */
    @Column(name = "point_accuracy")
    private BigDecimal pointAccuracy;

    /**
     * 最大负荷综合预测准确率
     */
    @Column(name = "max_synthesize_accuracy")
    private BigDecimal maxSynthesizeAccuracy;

    /**
     * 最小负荷综合准确率
     */
    @Column(name = "min_synthesize_accuracy")
    private BigDecimal minSynthesizeAccuracy;

    /**
     * 电量综合准确率
     */
    @Column(name = "energy_synthesize_accuracy")
    private BigDecimal energySynthesizeAccuracy;

    /**
     * 提交时间
     */
    @Column(name = "report_time")
    private Timestamp reportTime;

    @Column(name = "create_time")
    @CreationTimestamp
    private Date createTime;


    @Column(name = "update_time")
    @UpdateTimestamp
    private Date updateTime;
}