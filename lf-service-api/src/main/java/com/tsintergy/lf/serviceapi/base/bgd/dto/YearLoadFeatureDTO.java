package com.tsintergy.lf.serviceapi.base.bgd.dto;

import com.tsieframework.core.base.domain.dto.DTO;
import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class YearLoadFeatureDTO implements DTO {

    private String month;

    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal maxLoad;

    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal lastMaxLoad;

    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal minLoad;

    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal lastMinLoad;

    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal energy;

    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal lastEnergy;

    private BigDecimal maxLoadAccuracy;

    private BigDecimal minLoadAccuracy;

    private BigDecimal energyAccuracy;

    private BigDecimal noonTimeLoadAccuracy;

    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal noonTimeLoad;

    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal lastYearNoonTimeLoad;

    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal extremeMaxLoad;

    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal extremeMinLoad;

    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal extremeMonthEnergy;

    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal extremeNoonTimeLoad;
}
