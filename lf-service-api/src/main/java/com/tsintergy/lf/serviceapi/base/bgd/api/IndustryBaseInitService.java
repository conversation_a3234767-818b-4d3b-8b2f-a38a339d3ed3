package com.tsintergy.lf.serviceapi.base.bgd.api;

import com.tsintergy.lf.serviceapi.base.bgd.pojo.IndustryBaseInitDO;

import java.util.List;

/**
 * Description: TODO <br>
 *
 * <AUTHOR>
 * @create 2023-02-16
 * @since 1.0.0
 */
public interface IndustryBaseInitService {

    List<IndustryBaseInitDO> getIndustryBaseInitServiceListByTypes(List<String> types);

    List<IndustryBaseInitDO> getIndustryBaseInitServiceListById(String parentId);

    List<IndustryBaseInitDO> getIndustryBaseInitServiceListByName(String name);

    List<IndustryBaseInitDO> getIndustryBaseInitServiceListByLevel(String level);

    List<IndustryBaseInitDO> getIndustryBaseInitServiceListByTypes();

}
