package com.tsintergy.lf.serviceapi.base.typical.api;

import com.tsintergy.lf.serviceapi.base.typical.dto.Curve96Load;
import java.util.List;

/**
 * Description: TODO <br>
 *
 * <AUTHOR>
 * @create 2023-09-18
 * @since 1.0.0
 */
public interface TypicalLoadCurveService {

    List<Curve96Load> findWeekCurveCompare(String cityId, String dateStr) throws Exception;

    List<Curve96Load> findYearCurveCompare(String cityId, String dateStr, Integer type) throws Exception;

    List<Curve96Load> findTenDaysCurveCompare(String cityId, String dateStr, Integer type) throws Exception;

    List<Curve96Load> findTenDaysYearCurveCompare(String cityId, String dateStr, Integer type, Integer tenDaysType)
        throws Exception;

    List<Curve96Load> findMonthCurveCompare(String cityId, String dateStart, String dateEnd, Integer type)
        throws Exception;

    List<Curve96Load> findMonthYearCurveCompare(String cityId, String dateStr, Integer type) throws Exception;

    List<Curve96Load> findSeasoCurveCompare(String cityId, String dateStr, Integer type) throws Exception;

    List<Curve96Load> findSeasonYearCurveCompare(String cityId, String dateStr, Integer type, Integer seasonType)
        throws Exception;

    List<Curve96Load> findAllYearCurveCompare(String cityId, String dateStr, Integer type) throws Exception;
}
