package com.tsintergy.lf.serviceapi.base.load.dto;

import com.tsieframework.core.base.domain.dto.DTO;
import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.List;


@Data
public class LoadUserAddAndFeatureDTO implements DTO {

    @ApiModelProperty(value = "日期")
    private String date;

    /**
     * 最大负荷
     */
    @BigdecimalJsonFormat(scale = 0)
    @ApiModelProperty(value = "最大负荷")
    private BigDecimal maxLoad;

    /**
     * 最小负荷
     */
    @BigdecimalJsonFormat(scale = 0)
    @ApiModelProperty(value = "最小负荷")
    private BigDecimal minLoad;

    /**
     * 最大负荷发生时刻
     */
    @ApiModelProperty(value = "最大负荷发生时刻")
    private String maxTime;

    /**
     * 最小负荷发生时刻
     */
    @ApiModelProperty(value = "最小负荷发生时刻")
    private String minTime;



    /**
     * 峰谷差
     */
    @BigdecimalJsonFormat(scale = 0)
    @ApiModelProperty(value = "峰谷差")
    private BigDecimal different;

    /**
     * 峰谷差率
     */
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "峰谷差率")
    private BigDecimal gradient;

    /**
     * 负荷率
     */
    @ApiModelProperty(value = "负荷率")
    @BigdecimalJsonFormat(scale = 2)
    private BigDecimal loadGradient;

    /**
     * 日电量
     */
    @ApiModelProperty(value = "日电量")
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal energy;


    /**
     * 负荷数据
     */
    @ApiModelProperty(value = "负荷数据")
    @BigdecimalJsonFormat(scale = 0)
    private List<BigDecimal> load;
}
