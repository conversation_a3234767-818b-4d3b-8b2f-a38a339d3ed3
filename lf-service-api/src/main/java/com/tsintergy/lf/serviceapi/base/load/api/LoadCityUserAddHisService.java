package com.tsintergy.lf.serviceapi.base.load.api;

import com.tsintergy.lf.serviceapi.base.load.dto.LoadUserAddAndFeatureDTO;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadUserAddDTO;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadUserAddTypicalDTO;
import com.tsintergy.lf.serviceapi.base.load.dto.LoadUserFeatureDTO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityUserAddHisDO;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public interface LoadCityUserAddHisService {

    List<LoadCityUserAddHisDO> findByDate(String cityId, String caliberId, Date startDate, Date endDate);

    List<BigDecimal> getLoad(String cityId, String caliberId, Date startDate, Date endDate);

    LoadUserAddDTO getUserLoadContrast(String cityId, String caliberId, Date date);

    List<LoadUserAddAndFeatureDTO> getUserLoadAndFeature(String cityId, String caliberId, Date startDate, Date endDate);

    LoadUserAddTypicalDTO getUserTypicalLoad(String cityId, String caliberId, Date startDate, Date endDate);

    List<LoadUserAddAndFeatureDTO> getTypicalLoadAndFeature(String cityId, String caliberId, Date startDate, Date endDate);
}
