package com.tsintergy.lf.serviceapi.base.bgd.dto;

import com.tsieframework.core.base.domain.dto.DTO;
import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2023-05-05
 * @since 1.0.0
 */
@Data
public class DateValuesDTO implements DTO {

    /**
     * 日期
     */
    private String date;

    /**
     * 数据集合
     */
    @BigdecimalJsonFormat(scale = 0)
    private List<BigDecimal> values;

}
