/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.area.api;

import com.tsintergy.lf.serviceapi.base.area.pojo.LoadAreaHisDO;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/8/3 17:10
 * @Version: 1.0.0
 */
public interface LoadAreaHisService {


    List<LoadAreaHisDO> findLoadCityHisDOS(String areaId, Date startDate, Date endDate, String caliberId)
        throws Exception;


    void doInsertOrUpdate(LoadAreaHisDO loadAreaHisDO) throws Exception;
    void deleteByAreasIds(List<String> areaIds) throws Exception;

    void doInsert(LoadAreaHisDO loadAreaHisDO);
    void doInsertOrUpdateBatch(List<LoadAreaHisDO> loadAreaHisDOS) throws Exception;


    /***
     * 功能描述:
     * 统计区域负荷
     * <br>
     * @param
     * @param
     * @param startDate
     * @param
     * @Return:
     * @Version: 1.0.0
     * @Author:<EMAIL>
     * @Date: 2022/8/6 15:57
     */
    void statAreaHisLoad(String caliberId, String areaId, Date startDate, Date end) throws Exception;
}
