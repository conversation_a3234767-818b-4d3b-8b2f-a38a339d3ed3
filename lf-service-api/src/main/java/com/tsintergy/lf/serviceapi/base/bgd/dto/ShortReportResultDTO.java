package com.tsintergy.lf.serviceapi.base.bgd.dto;

import com.tsieframework.core.base.domain.dto.DTO;
import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2023-06-29
 * @since 1.0.0
 */
@Data
public class ShortReportResultDTO implements DTO {

    private String regionType;

    private String dateStr;

    private String type;

    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal normalData;

    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal extremeData;

    private List<ShortDateValuesDTO> shortDateValuesDTOS;

    private String featureType;

    private String dateType;

    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal yearData;
}
