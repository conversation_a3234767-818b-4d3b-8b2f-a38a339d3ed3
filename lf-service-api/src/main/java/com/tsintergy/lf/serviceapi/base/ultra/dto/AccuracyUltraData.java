package com.tsintergy.lf.serviceapi.base.ultra.dto;

import java.util.Date;
import lombok.Data;

/**
 * @author: wangh
 * 准确率评估 查询参数对象；
 **/
@Data
public class AccuracyUltraData {

    private String cityId;

    private String caliberId;

    private Date startDate;

    private Date endDate;

    /**
     * 获取单天数据时使用
     */
    private Date date;

    private Integer timeSpan;

    private String algorithmId;

    private String startTime;

    private String endTime;

    /**
     * 最低准确率详情时使用
     */
    private String time;

}
