package com.tsintergy.lf.serviceapi.base.airconditioner.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@ApiModel
public class StatisticsAcDTO {
    @ApiModelProperty(value = "年份")
    String year;

    @ApiModelProperty(value = "最大空调负荷")
    BigDecimal maxAirLoad;

    @ApiModelProperty(value = "最大空调负荷出现时间")
    String maxAirLoadTime;

    @ApiModelProperty(value = "最高温度")
    BigDecimal highestTemperature;

    @ApiModelProperty(value = "最高温度出现时间")
    String highestTemperatureTime;
}
