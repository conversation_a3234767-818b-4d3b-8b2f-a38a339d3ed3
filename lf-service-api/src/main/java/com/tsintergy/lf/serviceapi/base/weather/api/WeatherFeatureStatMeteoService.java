package com.tsintergy.lf.serviceapi.base.weather.api;


import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayHisMeteoDO;

import java.util.Date;
import java.util.List;

/**
 * 负荷特性统计接口
 * User:taojingui
 * Date:18-2-23
 * Time:上午11:53
 */
public interface WeatherFeatureStatMeteoService {

    /**
     * 统计气象特性并入库（日）
     *
     * @param cityIds   城市ID列表 代码内部匹配对应气象城市
     * @param startDate 开始日期
     * @param endDate   结束日期
     */
    List<WeatherFeatureCityDayHisMeteoDO> doStatWeatherFeatureCityDay(List<String> cityIds, Date startDate, Date endDate) throws Exception;

}
