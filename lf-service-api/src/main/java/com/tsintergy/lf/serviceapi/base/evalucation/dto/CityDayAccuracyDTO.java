package com.tsintergy.lf.serviceapi.base.evalucation.dto;

import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Comparator;

/**
 * <AUTHOR>
 */
@Data
@ApiModel
public class CityDayAccuracyDTO implements Serializable {
    private String cityName;
    @BigdecimalJsonFormat(percentConvert = 100, scale = 2)
    private BigDecimal accuracy;
    @BigdecimalJsonFormat(percentConvert = 100, scale = 2)
    private BigDecimal standard;
}
