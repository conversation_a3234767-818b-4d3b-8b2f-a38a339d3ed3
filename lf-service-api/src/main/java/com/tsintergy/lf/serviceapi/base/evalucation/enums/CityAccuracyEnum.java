package com.tsintergy.lf.serviceapi.base.evalucation.enums;

/**
 * Description: TODO <br>
 *
 * <AUTHOR>
 * @create 2024-03-06
 * @since 1.0.0
 */
public enum CityAccuracyEnum {

    DAY_ACCURACY("1", "日准确率", 1),
    MAX_LOAD_ACCURACY("2", "最大负荷准确率", 2),
    MIN_LOAD_ACCURACY("3", "最小负荷准确率", 3);

    private String typeId;

    private String typeName;

    private Integer sort;

    CityAccuracyEnum(String typeId, String typeName, Integer sort) {
        this.typeId = typeId;
        this.typeName = typeName;
        this.sort = sort;
    }

    public String getTypeId() {
        return typeId;
    }

    public void setTypeId(String typeId) {
        this.typeId = typeId;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }
}
