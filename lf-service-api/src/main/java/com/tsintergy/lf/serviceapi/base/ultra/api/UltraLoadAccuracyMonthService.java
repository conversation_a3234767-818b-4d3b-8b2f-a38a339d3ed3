
package com.tsintergy.lf.serviceapi.base.ultra.api;


import com.tsintergy.lf.serviceapi.base.ultra.dto.AccuracyDayUnitDTO;
import com.tsintergy.lf.serviceapi.base.ultra.dto.AccuracyLowDetailsMonthDTO;
import com.tsintergy.lf.serviceapi.base.ultra.dto.AccuracyLowDetailsQuarterDTO;
import com.tsintergy.lf.serviceapi.base.ultra.dto.AccuracyMonthDTO;
import com.tsintergy.lf.serviceapi.base.ultra.dto.AccuracyMonthUltraData;
import java.util.List;

/**
 * 日月准确率评估
 *
 * <AUTHOR>
 */
public interface UltraLoadAccuracyMonthService {

    /**
     * 月准确查询列表
     *
     * @param data 查询参数
     * @param type 1 月度 2季度
     * @return 结果对象
     */
    List<AccuracyMonthDTO> getLoadHistory(AccuracyMonthUltraData data, Integer type) throws Exception;

    /**
     * 查询日均准确率列表
     * @param data 查询参数
     * @param type 1 月度 2季度
     * @return 完整的一个月的数据
     */
    List<AccuracyDayUnitDTO> getDayAccuracy(AccuracyMonthUltraData data, Integer type);

    /**
     * 查询最低准确率详情-月度
     * @param data 查询参数
     * @return 结果对象
     */
    AccuracyLowDetailsMonthDTO getAccuracyLowMonthDetails(AccuracyMonthUltraData data) throws Exception;

    /**
     * 查询最低准确率详情-季度
     * @param data 查询参数
     * @return 结果对象
     */
    AccuracyLowDetailsQuarterDTO getAccuracyLowQuarterDetails(AccuracyMonthUltraData data) throws Exception;


}