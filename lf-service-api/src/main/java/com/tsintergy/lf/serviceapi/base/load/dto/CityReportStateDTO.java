package com.tsintergy.lf.serviceapi.base.load.dto;

import com.tsieframework.core.base.domain.dto.DTO;
import java.util.Date;
import lombok.Data;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2023-03-23
 * @since 1.0.0
 */
@Data
public class CityReportStateDTO implements DTO {

    private String cityName;

    private String firstReportTime;

    private String firstReportEndTime;

    private String lastReportTime;

    private String lastReportEndTime;

    private String firstReportStatus;

    private String lastReportStatus;

}
