package com.tsintergy.lf.serviceapi.base.system.api;

import com.tsintergy.lf.serviceapi.base.system.pojo.ReportSystemInitDO;
import java.util.List;

/**
 * Description: TODO <br>
 *
 * <AUTHOR>
 * @create 2023-05-08
 * @since 1.0.0
 */
public interface ReportSystemService {

    /**
     * 找到上报系统配置
     *
     * @param cityId 城市标识
     * @param field 场
     * @param name 名字
     * @return {@code List<ReportSystemInitDO>}
     */
    List<ReportSystemInitDO> findReportSystemConfig(String cityId, String field, String name);

    /**
     * 找到报告系统配置
     *
     * @param cityIds 城市标识
     * @param field 场
     * @param name 名字
     * @return {@code List<ReportSystemInitDO>}
     */
    List<ReportSystemInitDO> findReportSystemConfig(List<String> cityIds, String field, String name);

    /**
     * 做保存或更新
     *
     * @param reportSystemInitDO 报告系统初始化
     */
    void doSaveOrUpdate(ReportSystemInitDO reportSystemInitDO);
}
