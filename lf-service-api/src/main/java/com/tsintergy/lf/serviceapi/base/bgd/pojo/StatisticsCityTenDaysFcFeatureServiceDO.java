/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.bgd.pojo;

import com.tsieframework.core.base.dao.BaseDO;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;

/**
 * Description:  <br>
 * 月度负荷特性准确率表
 * @Author: <EMAIL>
 * @Date: 2022/11/17 19:28
 * @Version: 1.0.0
 */

@Data
@Entity
@Table(name = "statistics_city_ten_days_fc_feature_service")
public class StatisticsCityTenDaysFcFeatureServiceDO extends BaseFeatureAccuryDO implements BaseDO{


    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;

    @Column(name = "year")
    private String year;

    @Column(name = "month")
    private String month;

    @Column(name = "type")
    private String type;


}