package com.tsintergy.lf.serviceapi.algorithm.dto;

import com.tsintergy.aif.algorithm.serviceapi.base.dto.gurNetwork.GurNetWorkParam;

import java.util.Date;
import java.util.List;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2023-06-20
 * @since 1.0.0
 */
public class GurNetworkAlgorithmParam extends GurNetWorkParam {


    private String cityId;

    private String caliberId;


    private List<String> userCityList;

    private Boolean recall;

    private String fcWeatherSource;

    public GurNetworkAlgorithmParam(String cityName, Date beginDate, Date endDate, String[] distinguishParams) {
        super(cityName, beginDate, endDate, distinguishParams);
    }


    public String getCityId() {
        return cityId;
    }

    public void setCityId(String cityId) {
        this.cityId = cityId;
    }

    public String getCaliberId() {
        return caliberId;
    }

    public void setCaliberId(String caliberId) {
        this.caliberId = caliberId;
    }


    public List<String> getUserCityList() {
        return userCityList;
    }

    public void setUserCityList(List<String> userCityList) {
        this.userCityList = userCityList;
    }

    public Boolean getRecall() {
        return recall;
    }

    public void setRecall(Boolean recall) {
        this.recall = recall;
    }

    public String getFcWeatherSource() {
        return fcWeatherSource;
    }

    public void setFcWeatherSource(String fcWeatherSource) {
        this.fcWeatherSource = fcWeatherSource;
    }
}

