package com.tsintergy.lf.serviceapi.base.load.dto;

import com.tsieframework.core.base.domain.dto.DTO;
import java.math.BigDecimal;
import lombok.Data;

/**
 * Description: 冬夏中长期
 *
 * <AUTHOR>
 * @create 2023-03-17
 * @since 1.0.0
 */
@Data
public class SeasonLongTermDTO implements DTO {

    /***
     * @Description 季度月最大准确率
     **/
    private BigDecimal seasonMaxLoadAccuracy;

    /***
     * @Description 季度月最小准确率
     **/
    private BigDecimal seasonMinLoadAccuracy;

    /***
     * @Description 季度月电量准确率
     **/
    private BigDecimal seasonEnergyAccuracy;

    /***
     * @Description 季度平均准确率
     **/
    private BigDecimal seasonAvgAccuracy;

}
