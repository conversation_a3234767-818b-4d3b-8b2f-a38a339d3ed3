package com.tsintergy.lf.serviceapi.base.ultra.forecast.dto;

import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2023-10-18
 * @since 1.0.0
 */
@Data
public class ForecastAlarmAllDTO implements Serializable {

    private List<ForecastAlarmDTO> forecastAlarmDTOList;

    @BigdecimalJsonFormat(scale = 0)
    private List<BigDecimal> fcCompare;
}
