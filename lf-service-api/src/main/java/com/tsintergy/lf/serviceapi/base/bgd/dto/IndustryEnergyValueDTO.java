package com.tsintergy.lf.serviceapi.base.bgd.dto;

import com.tsieframework.core.base.domain.dto.DTO;
import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import java.math.BigDecimal;
import lombok.Data;

/**
 * Description: 行业电量值 <br>
 *
 * <AUTHOR>
 * @create 2023-02-16
 * @since 1.0.0
 */
@Data
public class IndustryEnergyValueDTO implements DTO {

    private String name;

    @BigdecimalJsonFormat(divideConvert = 10000, scale = 0)
    private BigDecimal value;

}
