/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.trade.dto;

import com.tsieframework.core.base.domain.dto.DTO;
import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import java.math.BigDecimal;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/9/2 18:26
 * @Version: 1.0.0
 */
public class TradeFeatureDTO implements DTO {

    /**
     * 行业名称
     */
    private String name;

    /**
     * 最大负荷
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal maxLoad;

    /**
     * 最大负荷出现时间
     */
    private String maxLoadTime;

    /**
     * 最小负荷
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal minLoad;

    /**
     * 最小负荷出现时间
     */
    private String minLoadTime;

    /**
     * 平均负荷
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal aveLoad;

    /**
     * 日平均电量
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal aveElectricity;

    /**
     * 总电量
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal totalPower;


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public BigDecimal getMaxLoad() {
        return maxLoad;
    }

    public void setMaxLoad(BigDecimal maxLoad) {
        this.maxLoad = maxLoad;
    }

    public String getMaxLoadTime() {
        return maxLoadTime;
    }

    public void setMaxLoadTime(String maxLoadTime) {
        this.maxLoadTime = maxLoadTime;
    }

    public BigDecimal getMinLoad() {
        return minLoad;
    }

    public void setMinLoad(BigDecimal minLoad) {
        this.minLoad = minLoad;
    }

    public String getMinLoadTime() {
        return minLoadTime;
    }

    public void setMinLoadTime(String minLoadTime) {
        this.minLoadTime = minLoadTime;
    }

    public BigDecimal getAveLoad() {
        return aveLoad;
    }

    public void setAveLoad(BigDecimal aveLoad) {
        this.aveLoad = aveLoad;
    }

    public BigDecimal getAveElectricity() {
        return aveElectricity;
    }

    public void setAveElectricity(BigDecimal aveElectricity) {
        this.aveElectricity = aveElectricity;
    }

    public BigDecimal getTotalPower() {
        return totalPower;
    }

    public void setTotalPower(BigDecimal totalPower) {
        this.totalPower = totalPower;
    }
}
