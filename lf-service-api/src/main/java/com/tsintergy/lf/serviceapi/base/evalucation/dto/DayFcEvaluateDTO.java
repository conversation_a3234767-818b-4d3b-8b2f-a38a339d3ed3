package com.tsintergy.lf.serviceapi.base.evalucation.dto;


import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class DayFcEvaluateDTO {
    private String cityName;
    /**
     * 预测精度
     */
    @BigdecimalJsonFormat(percentConvert = 100, scale = 2)
    private BigDecimal precisionFc;
    /**
     * 预测负荷
     */
    private List<BigDecimal> loadFcList;
    /**
     * 实际负荷
     */
    private List<BigDecimal> loadHisList;
    /**
     * 预测气温
     */
    private List<BigDecimal> temperateFcList;
    /**
     * 实际气温
     */
    private List<BigDecimal> temperateHisList;
    /**
     * 考核标准
     */
    @BigdecimalJsonFormat(percentConvert = 100, scale = 2)
    private BigDecimal standard;

}
