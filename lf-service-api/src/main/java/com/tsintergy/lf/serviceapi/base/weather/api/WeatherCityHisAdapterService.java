package com.tsintergy.lf.serviceapi.base.weather.api;

import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityHisDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayHisDO;

import java.util.Date;
import java.util.List;

public interface WeatherCityHisAdapterService {

    List<WeatherCityHisDO> findHisWeather(String cityId, String algorithmId, Integer type, Date start, Date end) throws Exception;

    List<WeatherCityHisDO> findWeatherCityHisDOS(String sourceType, String cityId, Integer type, Date start, Date end) throws Exception;

    List<WeatherCityHisDO> findHisWeather(String sourceType, String cityId, Date start, Date endDate) throws Exception;

    List<WeatherFeatureCityDayHisDO> findWeatherFeatureCityDayHisDO(String sourceType, String cityId, Date startDate, Date endDate) throws Exception;

    List<WeatherFeatureCityDayHisDO> findWeatherFeatureByAlgorithmId(String algorithmId, String cityId, Date startDate, Date endDate) throws Exception;
}
