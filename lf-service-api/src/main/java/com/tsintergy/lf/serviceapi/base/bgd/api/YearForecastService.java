package com.tsintergy.lf.serviceapi.base.bgd.api;

import com.tsieframework.core.base.service.FacadeService;
import com.tsintergy.lf.serviceapi.base.bgd.dto.MonthWeatherResultDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.MonthWeatherSettingDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.SeasonReportDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.YearLoadFeatureDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.YearWeatherFeatureDTO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface YearForecastService extends FacadeService {

    List<YearLoadFeatureDTO> getYearLoadFc(String cityId, String caliberId, String year, String algorithmId, String startStr, String endStr)
        throws Exception;

    /**
     * 修正结果暂存
     * @param monthReportDTOS
     * @return
     */
    void save(List<SeasonReportDTO> monthReportDTOS) throws Exception;

    List<YearLoadFeatureDTO> getYearLoadComparison(String cityId, String caliberId, String year, String algorithmId, String startStr, String endStr)
        throws Exception;

    List<YearWeatherFeatureDTO> getYearWeatherComparison(String cityId, String year, String startStr,
        String endStr);

    List<MonthWeatherResultDTO> getWeatherSetting(MonthWeatherSettingDTO monthWeatherSettingDTO) throws Exception;

    void saveWeatherSetting(List<MonthWeatherResultDTO> monthWeatherResultDTOS) throws Exception;
}
