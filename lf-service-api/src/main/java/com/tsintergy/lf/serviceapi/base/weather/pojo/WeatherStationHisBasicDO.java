/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.weather.pojo;

import java.sql.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * Description: 场站气象历史负荷表 <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/5/20 15:37
 * @Version: 1.0.0
 */
@Data
@Entity
@Table(name = "weather_station_his_basic")
public class WeatherStationHisBasicDO extends  BaseWeatherDO{

    @Column(name = "station_id")
    private String stationId;

    /**
     * 日期
     */
    @Column(name = "date")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date date;

}