/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.bgd.api;

import com.tsieframework.core.base.service.FacadeService;
import com.tsintergy.lf.serviceapi.base.bgd.pojo.StatisticsCityQuarterFcFeatureServiceDO;

import java.util.List;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/11/17 21:23
 * @Version: 1.0.0
 */
public interface StatisticsCityQuarterFcFeatureService extends FacadeService {

    List<StatisticsCityQuarterFcFeatureServiceDO> findStatisticsCityQuarterFcFeature(String cityId, String caliberId, String algorithmId, String year, String quarter);


    void saveOrUpdateList(List<StatisticsCityQuarterFcFeatureServiceDO> quarterDOList);
}
