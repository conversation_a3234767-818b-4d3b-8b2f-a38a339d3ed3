package com.tsintergy.lf.serviceapi.base.weather.api;

import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcMeteoDO;

import java.util.Date;
import java.util.List;

/**
 * Description: TODO <br>
 *
 * <AUTHOR>
 * @create 2024-12-04
 * @since 1.0.0
 */
public interface WeatherCityFcMeteoService {

    List<WeatherCityFcMeteoDO> getListByCondition(String cityId, Integer type, Date startDate, Date endDate);

    void doSaveOrUpdate(WeatherCityFcMeteoDO weatherCityFcMeteoDO);

    void insertYesterday24HourData(WeatherCityFcMeteoDO weatherCityFcMeteoDO);

    void statMeteoProvinceFcWeather(Date startDate, Date endDate);
}
