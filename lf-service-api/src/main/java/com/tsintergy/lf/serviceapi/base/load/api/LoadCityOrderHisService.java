package com.tsintergy.lf.serviceapi.base.load.api;

import com.tsintergy.lf.serviceapi.base.load.dto.LoadHisDataDTO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadCityOrderHisDO;

import java.util.Date;
import java.util.List;

public interface LoadCityOrderHisService {

    List<LoadCityOrderHisDO> findByDate(String cityId, String caliberId, Date startDate,Date endDate);

    List<LoadHisDataDTO> findLoadOrderByCityId(Date startDate, Date endDate, String cityId, String caliberId);
}
