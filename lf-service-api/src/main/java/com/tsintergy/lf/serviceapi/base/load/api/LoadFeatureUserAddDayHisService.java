package com.tsintergy.lf.serviceapi.base.load.api;

import com.tsintergy.lf.serviceapi.base.load.dto.LoadUserFeatureDTO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureUserAddDayHisDO;

import java.util.Date;
import java.util.List;

public interface LoadFeatureUserAddDayHisService {

    List<LoadFeatureUserAddDayHisDO> findByDate(String cityId, String caliberId, Date startDate, Date endDate);

    List<LoadUserFeatureDTO> getUserWarn(String cityId, String caliberId, Date startDate, Date endDate);

}
