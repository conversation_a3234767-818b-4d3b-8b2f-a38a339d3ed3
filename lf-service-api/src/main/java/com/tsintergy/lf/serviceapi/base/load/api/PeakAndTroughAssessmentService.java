package com.tsintergy.lf.serviceapi.base.load.api;

import com.tsintergy.lf.serviceapi.base.load.dto.AccuracyEstimationsDTO;
import com.tsintergy.lf.serviceapi.base.load.dto.AccuracyDistributionDTO;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021-09-06
 * 负荷预测-高峰低谷评估
 */
public interface PeakAndTroughAssessmentService {


    /**
     * 准确率统计
     * @param cityId
     * @param caliberId
     * @param startDate
     * @param endDate
     * @param dateType
     * @return
     * @throws Exception
     */
    AccuracyEstimationsDTO getAccuracyStatistics(String cityId, String caliberId, String startDate, String endDate, String dateType ) throws Exception;


    /**
     * 准确率分布饼状图 查询
     * @param accuracyType
     * @param caliberId
     * @param cityId
     * @param dateType
     * @param startDate
     * @param endDate
     * @return
     */
    List<AccuracyDistributionDTO> getAccuracyDistribution(String accuracyType, String caliberId, String cityId, String dateType, String startDate, String endDate) throws Exception;


     Map<String, Date> getDate(String dateType, String startDate, String endDate);
    }
