package com.tsintergy.lf.serviceapi.base.weather.pojo;

import javax.persistence.Entity;
import javax.persistence.Table;
import lombok.Data;

/**
 * 气象预测数据
 */
@Data
@Entity
@Table(name = "weather_city_fc_basic_copy2")
public class WeatherCityFcCopy2DO extends BaseWeatherDO {

    public WeatherCityFcCopy2DO() {
        super();
    }

    public WeatherCityFcCopy2DO(String cityId, Integer type, java.util.Date date) {
        super(cityId, type, date);
    }
}
