package com.tsintergy.lf.serviceapi.base.bgd.dto;

/**
 * Description: TODO <br>
 *
 * <AUTHOR>
 * @create 2023-02-17
 * @since 1.0.0
 */

import com.tsieframework.core.base.domain.dto.DTO;
import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import java.math.BigDecimal;
import lombok.Data;

/**
 * Description:  <br>
 * 季度上报
 * @Author: <EMAIL>
 * @Date: 2022/11/25 21:44
 * @Version: 1.0.0
 */
@Data
public class SeasonReportDTO implements DTO {


    private String cityId;

    private String caliberId;

    private String date;

    private String algorithmId;

    private BigDecimal maxLoad;

    private BigDecimal minLoad;

    private BigDecimal energy;

    private String season;

    private BigDecimal noonTimeLoad;

    private BigDecimal extremeMaxLoad;

    private BigDecimal extremeMinLoad;

    private BigDecimal extremeNoonTimeLoad;

    private BigDecimal extremeEnergy;
}
