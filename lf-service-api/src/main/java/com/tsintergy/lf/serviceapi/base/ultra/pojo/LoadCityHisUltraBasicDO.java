package com.tsintergy.lf.serviceapi.base.ultra.pojo;


import com.tsieframework.core.base.vo.annotation.EntityUniqueIndex;
import com.tsintergy.lf.serviceapi.base.ultra.load.pojo.NesdBasicPeriodDO;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import lombok.Data;

/**
 * 地区历史负荷；从clct处理后的数据；页面查询数据来自这里；
 */
@Data
@Entity
@Table(name = "load_city_his_ultra_basic")
@EntityUniqueIndex({"cityId", "caliberId", "dateTime", "tvMeta"})
public class LoadCityHisUltraBasicDO extends NesdBasicPeriodDO {

    /***
     * @Description 城市id
     * @Date 2023/1/10 16:50
     * <AUTHOR>
     **/
    @ApiModelProperty("城市id")
    @Column(name = "CITY_ID")
    private String cityId;

    /***
     * @Description 口径id
     * @Date 2023/1/10 16:50
     * <AUTHOR>
     **/
    @ApiModelProperty("口径ID")
    @Column(name = "CALIBER_ID")
    private String caliberId;

    /***
     * @Description 日期
     * @Date 2023/1/10 16:50
     * <AUTHOR>
     **/
    @ApiModelProperty("日期")
    @Column(name = "DATE_TIME")
    private Date dateTime;

}
