package com.tsintergy.lf.serviceapi.base.forecast.api;

import com.tsintergy.lf.serviceapi.base.forecast.pojo.AlgorithmParamDO;

import java.util.List;

/**
 * Description: TODO <br>
 *
 * <AUTHOR>
 * @create 2023-06-20
 * @since 1.0.0
 */
public interface AlgorithmParamService {

    /**
     * 获取算法的参数
     *
     * @param algorithmId
     * @return
     * @throws Exception
     */
    List<AlgorithmParamDO> getAlgorithmParamVOsByAlgorithmId(String algorithmId) throws Exception;

    /**
     * 根据ID获取算法参数
     *
     * @return
     * @throws Exception
     */
    AlgorithmParamDO getAlgorithmParamVOById(String id) throws Exception;

    /**
     * 查询所有算法参数
     */

    List<AlgorithmParamDO> getAllAlgorithmParam() throws Exception;

    public List<AlgorithmParamDO> getAlgorithmParamByAlgorithmIdNoCache(String algorithmId) throws Exception;
}
