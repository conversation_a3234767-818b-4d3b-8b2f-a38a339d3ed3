package com.tsintergy.lf.serviceapi.base.airconditioner.dto;

import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel
public class MaxAcLoadDTO {
    @BigdecimalJsonFormat(scale = 0)
    @ApiModelProperty(value = "最大空调负荷曲线")
    List<BigDecimal> acLoad;

    @ApiModelProperty(value = "年份")
    String year;

    @ApiModelProperty(value = "最大空调负荷日期")
    List<String> date;
}
