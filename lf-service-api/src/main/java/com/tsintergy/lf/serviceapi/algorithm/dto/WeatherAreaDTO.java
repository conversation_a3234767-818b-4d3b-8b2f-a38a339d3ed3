/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.algorithm.dto;

import com.tsintergy.aif.algorithm.serviceapi.base.pojo.Load;
import com.tsintergy.aif.algorithm.serviceapi.base.pojo.Weather;
import java.util.List;

/**
 *Description:  <br>
 *
 *@Author: <EMAIL>
 *@Date: 2022/5/5 18:34
 *@Version: 1.0.0
 */
public class WeatherAreaDTO{


    /**
     * 历史湿度
     */
    private List<? extends Weather> hisHumiditys;

    /**
     * 预测湿度
     */
    private List<? extends Weather> fcHumiditys;

    /**
     * 历史温度
     */
    private List<? extends Weather> hisTemperatures;

    /**
     * 预测温度
     */
    private List<? extends Weather> fcTemperatures;

    /**
     * 历史降水
     */
    private List<? extends Weather> hisPrecipitations;

    /**
     * 预测降水
     */
    private List<? extends Weather> fcPrecipitations;

    /**
     * 历史风速
     */
    private List<? extends Weather> hisWinds;

    /**
     * 预测风速
     */
    private List<? extends Weather> fcWinds;


    public List<? extends Weather> getHisHumiditys() {
        return hisHumiditys;
    }

    public void setHisHumiditys(List<? extends Weather> hisHumiditys) {
        this.hisHumiditys = hisHumiditys;
    }

    public List<? extends Weather> getFcHumiditys() {
        return fcHumiditys;
    }

    public void setFcHumiditys(List<? extends Weather> fcHumiditys) {
        this.fcHumiditys = fcHumiditys;
    }

    public List<? extends Weather> getHisTemperatures() {
        return hisTemperatures;
    }

    public void setHisTemperatures(
        List<? extends Weather> hisTemperatures) {
        this.hisTemperatures = hisTemperatures;
    }

    public List<? extends Weather> getFcTemperatures() {
        return fcTemperatures;
    }

    public void setFcTemperatures(
        List<? extends Weather> fcTemperatures) {
        this.fcTemperatures = fcTemperatures;
    }

    public List<? extends Weather> getHisPrecipitations() {
        return hisPrecipitations;
    }

    public void setHisPrecipitations(
        List<? extends Weather> hisPrecipitations) {
        this.hisPrecipitations = hisPrecipitations;
    }

    public List<? extends Weather> getFcPrecipitations() {
        return fcPrecipitations;
    }

    public void setFcPrecipitations(
        List<? extends Weather> fcPrecipitations) {
        this.fcPrecipitations = fcPrecipitations;
    }

    public List<? extends Weather> getHisWinds() {
        return hisWinds;
    }

    public void setHisWinds(List<? extends Weather> hisWinds) {
        this.hisWinds = hisWinds;
    }

    public List<? extends Weather> getFcWinds() {
        return fcWinds;
    }

    public void setFcWinds(List<? extends Weather> fcWinds) {
        this.fcWinds = fcWinds;
    }
}