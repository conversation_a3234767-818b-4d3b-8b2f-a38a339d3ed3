/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.area.api;

import com.tsintergy.lf.serviceapi.base.area.pojo.LoadAreaFcDO;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Description:  <br>
 * 
 * @Author: <EMAIL>
 * @Date: 2022/8/3 17:10
 * @Version: 1.0.0
 */
public interface LoadAreaFcService {



    List<LoadAreaFcDO> findLoadCityFcDOS(String areaId, Date startDate, Date endDate, String caliberId)
        throws Exception;


    void doInsertOrUpdate(LoadAreaFcDO loadAreaHisDO) throws Exception;


    void doInsertOrUpdateBatch(List<LoadAreaFcDO> loadAreaHisDOS) throws Exception;


    List<BigDecimal> findLoadCityFcDO(String areaId, Date startDate, Date endDate, String caliberId)
        throws Exception;


    /***
     * 功能描述:
     * 统计区域总预测负荷<br>
     * @param startDate
     * @param end
     * @param defaultAreaIds
     * @param caliberId
     * @param netLossCoefficient 网损系数
     * @Return:
     * @Version: 1.0.0
     * @Author:<EMAIL>
     * @Date: 2022/8/7 11:42
     */
    void doStatAllAreaFcLoad(Date startDate, Date end, List<String> defaultAreaIds, String caliberId,String netLossCoefficient) throws Exception;


    /***
     * 功能描述:
     * 区域预测
     * <br>
     * @param startDate
     * @param end
     * @param id
     * @param defaultAreaIds
     * @Return:
     * @Version: 1.0.0
     * @Author:<EMAIL>
     * @Date: 2022/8/7 13:21
     */
    void doAreaForecast(Date startDate, Date end, String caliberId) throws Exception;

}
