/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.trade.api;

import com.tsintergy.lf.serviceapi.base.trade.pojo.TradeLoadFeatureDayHisDO;

import java.util.Date;
import java.util.List;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/9/2 15:03
 * @Version: 1.0.0
 */
public interface TradeLoadFeatureDayHisService {


    void saveOrUpdate(TradeLoadFeatureDayHisDO tradeLoadFeatureDayHisDO);

    List<TradeLoadFeatureDayHisDO> findTradeLoadFeatureDayHisDO(Date startDate, Date endDate, String codeId);

    List<TradeLoadFeatureDayHisDO> findTradeLoadFeatureDayHisDO(String cityId, Date startDate, Date endDate, List<String> codeIds);

}
