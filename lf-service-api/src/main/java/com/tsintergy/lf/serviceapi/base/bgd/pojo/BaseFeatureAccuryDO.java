/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.bgd.pojo;

import com.tsieframework.core.base.dao.BaseDO;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.Column;
import javax.persistence.MappedSuperclass;
import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * Description:  <br>
 * 特性准确率父类
 * @Author: <EMAIL>
 * @Date: 2022/11/22 17:37
 * @Version: 1.0.0
 */
@MappedSuperclass
public class BaseFeatureAccuryDO implements BaseDO {


    /**
     * 城市
     */
    @Column(name = "city_id")
    private String cityId;

    /**
     * 口径ID
     */
    @Column(name = "caliber_id")
    private String caliberId;

    @Column(name = "algorithm_id")
    private String algorithmId;


    @Column(name = "max_load_accuracy")
    private BigDecimal maxLoadAccuracy;

    @Column(name = "min_load_accuracy")
    private BigDecimal minLoadAccuracy;

    @Column(name = "energy_accuracy")
    private BigDecimal energyAccuracy;

    @Column(name = "report")
    private Boolean report;

    @Column(name = "createtime")
    @CreationTimestamp
    protected Timestamp createtime;

    /**
     * 更新时间
     */
    @Column(name = "updatetime")
    @UpdateTimestamp
    protected Timestamp updatetime;


    public String getCityId() {
        return cityId;
    }

    public void setCityId(String cityId) {
        this.cityId = cityId;
    }

    public String getCaliberId() {
        return caliberId;
    }

    public void setCaliberId(String caliberId) {
        this.caliberId = caliberId;
    }

    public String getAlgorithmId() {
        return algorithmId;
    }

    public void setAlgorithmId(String algorithmId) {
        this.algorithmId = algorithmId;
    }

    public BigDecimal getMaxLoadAccuracy() {
        return maxLoadAccuracy;
    }

    public void setMaxLoadAccuracy(BigDecimal maxLoadAccuracy) {
        this.maxLoadAccuracy = maxLoadAccuracy;
    }

    public BigDecimal getMinLoadAccuracy() {
        return minLoadAccuracy;
    }

    public void setMinLoadAccuracy(BigDecimal minLoadAccuracy) {
        this.minLoadAccuracy = minLoadAccuracy;
    }

    public BigDecimal getEnergyAccuracy() {
        return energyAccuracy;
    }

    public void setEnergyAccuracy(BigDecimal energyAccuracy) {
        this.energyAccuracy = energyAccuracy;
    }

    public Boolean getReport() {
        return report;
    }

    public void setReport(Boolean report) {
        this.report = report;
    }

    public Timestamp getCreatetime() {
        return createtime;
    }

    public void setCreatetime(Timestamp createtime) {
        this.createtime = createtime;
    }

    public Timestamp getUpdatetime() {
        return updatetime;
    }

    public void setUpdatetime(Timestamp updatetime) {
        this.updatetime = updatetime;
    }
}