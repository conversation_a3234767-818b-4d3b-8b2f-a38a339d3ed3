package com.tsintergy.lf.serviceapi.base.ultra.load.pojo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.MappedSuperclass;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2023/01/02
 * @since 1.0.0
 */
@Data
@MappedSuperclass
public class NesdBasicDO extends NesdBaseDO {

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @Column(name = "create_time", updatable = false)
    @CreationTimestamp
    private Date createTime;

    /**
     * 最后一次修改时间
     */
    @ApiModelProperty(value = "最后一次修改时间")
    @Column(name = "update_time")
    @UpdateTimestamp
    private Date updateTime;

}
