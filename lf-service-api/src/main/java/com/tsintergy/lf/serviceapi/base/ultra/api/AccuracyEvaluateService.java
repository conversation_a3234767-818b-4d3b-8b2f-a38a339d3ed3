package com.tsintergy.lf.serviceapi.base.ultra.api;


import com.tsintergy.lf.serviceapi.base.ultra.dto.LoadFeatureUltraFcDayDTO;
import com.tsintergy.lf.serviceapi.base.ultra.dto.LoadUltraFcAccuracyDTO;
import java.util.Date;
import java.util.List;

/**
 * @Description
 * @Date 2023/1/9 16:36
 * <AUTHOR>
 **/
public interface AccuracyEvaluateService {

    /***
     * @Description
     * @Date 2023/1/9 16:36
     * <AUTHOR>
     * tDelta 时间间隔 5五分钟 15 十五分钟
     *
     */
    List<LoadFeatureUltraFcDayDTO> findFeatureStatisDTOS(Date startDate, Date endDate, String caliberId, String cityId, Integer tDelta, Integer sort, String algorithmId) throws Exception;

    /***
     * @Description 时刻准确率
     * @Date 2023/1/9 16:37
     * <AUTHOR>
     **/
    List<LoadUltraFcAccuracyDTO> findDayTimeAccuracy(Date date, String cityId, String caliberId, Integer tDelta, Integer sort, String algorithmId) throws Exception;

}
