package com.tsintergy.lf.serviceapi.base.trade.pojo;

import com.tsieframework.core.base.dao.BaseDO;
import com.tsieframework.core.base.vo.annotation.EntityUniqueIndex;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.aif.algorithm.serviceapi.base.pojo.Load;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.serviceapi.base.base.pojo.Base96DO;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.math.BigDecimal;
import java.sql.Date;
import java.util.List;

@Data
@Entity
@Table(name = "industry_city_load_day_his_clct")
@EntityUniqueIndex({"city_id", "date", "tradeCode"})
public class IndustryCityLoadDayHisClctDO extends Base96DO implements BaseDO, Load {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;

    /**
     * 日期
     */
    @Column(name = "date")
    private Date date;

    /**
     * 城市ID
     */
    @Column(name = "city_id")
    private String cityId;

    /**
     * 行业ID
     */
    @Column(name = "trade_code")
    private String tradeCode;


    @Column(name = "trade_code_dsc")
    private String tradeCodeDsc;

    @Override
    public List<BigDecimal> getloadList() {
        return BasePeriodUtils.toList(this, Constants.LOAD_CURVE_POINT_NUM, Constants.LOAD_CURVE_START_WITH_ZERO);
    }

    @Override
    public String getDeviceId() {
        return this.tradeCode;
    }
}
