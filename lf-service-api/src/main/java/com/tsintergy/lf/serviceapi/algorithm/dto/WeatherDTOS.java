package com.tsintergy.lf.serviceapi.algorithm.dto;

import com.tsintergy.aif.algorithm.serviceapi.base.pojo.Load;
import com.tsintergy.aif.algorithm.serviceapi.base.pojo.Weather;
import lombok.Data;

import java.util.List;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2023-06-20
 * @since 1.0.0
 */
@Data
public class WeatherDTOS {

    /**
     * 历史负荷
     */
    private List<? extends Load> hisLoads;

    /**
     * 历史湿度
     */
    private List<? extends Weather> hisHumiditys;

    /**
     * 预测湿度
     */
    private List<? extends Weather> fcHumiditys;

    /**
     * 历史温度
     */
    private List<? extends Load> hisTemperatures;

    /**
     * 预测温度
     */
    private List<? extends Load> fcTemperatures;

    /**
     * 历史降水
     */
    private List<? extends Load> hisPrecipitations;

    /**
     * 预测降水
     */
    private List<? extends Load> fcPrecipitations;

    /**
     * 历史风速
     */
    private List<? extends Weather> hisWinds;

    /**
     * 预测风速
     */
    private List<? extends Weather> fcWinds;


}
