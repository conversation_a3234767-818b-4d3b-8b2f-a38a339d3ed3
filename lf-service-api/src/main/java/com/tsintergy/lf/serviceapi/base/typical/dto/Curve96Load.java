package com.tsintergy.lf.serviceapi.base.typical.dto;

import com.tsieframework.core.base.domain.dto.DTO;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2023-09-18
 * @since 1.0.0
 */
@Data
public class Curve96Load implements DTO {

    /**
     * 时间
     */
    private String dateStr;

    /**
     * 96点负荷
     */
    private List<BigDecimal> bigDecimals;
}
