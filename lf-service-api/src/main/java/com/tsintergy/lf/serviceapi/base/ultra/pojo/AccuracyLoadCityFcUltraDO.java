package com.tsintergy.lf.serviceapi.base.ultra.pojo;


import com.tsieframework.core.base.vo.annotation.EntityUniqueIndex;

import com.tsintergy.lf.serviceapi.base.ultra.load.pojo.NesdBasicPeriodDO;
import io.swagger.annotations.ApiModelProperty;
import java.sql.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

/**
 * 逐点预测准确率 96 or 288 点
 */
@Data
@Entity
@Table(name = "accuracy_load_city_fc_ultra_service")
@EntityUniqueIndex({"dateTime", "cityId", "algorithmId", "caliberId", "tvMeta", "multipointType"})
public class AccuracyLoadCityFcUltraDO extends NesdBasicPeriodDO {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;

    /**
     * 日期
     */
    @Column(name = "date_time")
    private Date dateTime;

    /**
     * 城市ID
     */
    @Column(name = "city_id")
    private String cityId;

    /**
     * 算法ID
     */
    @Column(name = "algorithm_id")
    private String algorithmId;

    /**
     * 口径ID
     */
    @Column(name = "caliber_id")
    private String caliberId;

    @ApiModelProperty(value = "入库点数类型；示例5分钟：1；3；9对应时刻点:5;15;45")
    @Column(name = "multipoint_type")
    private Integer multipointType;

    /**
     * 是否上报
     */
    @Column(name = "report")
    private Boolean report;

}
