package com.tsintergy.lf.serviceapi.base.recall.dto;

import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@ApiModel
public class LoadFcCompareDTO implements Serializable {
    @ApiModelProperty(value = "日期")
    private String date;

    @ApiModelProperty(value = "时刻")
    private String time;

    @ApiModelProperty(value = "预测负荷")
    private BigDecimal LoadFc;

    @ApiModelProperty(value = "回溯负荷")
    private BigDecimal recallLoad;

    @ApiModelProperty(value = "实际负荷")
    private BigDecimal loadHis;

    @BigdecimalJsonFormat(percentConvert = 100, scale = 2)
    @ApiModelProperty(value = "预测准确率")
    private BigDecimal accuracyFc;

    @BigdecimalJsonFormat(percentConvert = 100, scale = 2)
    @ApiModelProperty(value = "回溯准确率")
    private BigDecimal recallAccuracy;
}
