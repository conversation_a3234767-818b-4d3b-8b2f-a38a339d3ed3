/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.trade.api;

import com.tsieframework.core.base.service.BaseService;
import com.tsintergy.lf.serviceapi.base.trade.dto.*;

import java.util.Date;
import java.util.List;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/9/2 15:25
 * @Version: 1.0.0
 */
public interface TradeService extends BaseService {


    /***
     * 功能描述:
     * 获取行业信息<br>
    ` * @param cityId
     * @param name`
     * @Return: {@link com.tsintergy.lf.serviceapi.base.trade.dto.TradeInfoDTO}
     * @Version: 1.0.0
     * @Author:<EMAIL>
     * @Date: 2022/9/2 18:13
     */
    TradeInfoDTO getTradeInfo(String cityId, String name);

    /***
     * 功能描述:
     * 获取行业负荷曲线<br>
     * @param cityId
     * @param startDate
     * @param endDate
     * @param codeIds
     * @Return: {@link List< LoadHisDTO>}
     * @Version: 1.0.0
     * @Author:<EMAIL>
     * @Date: 2022/9/2 18:13
     */
    List<TradeCurveDTO> getTradeCurve(String cityId, Date startDate, Date endDate, List<String> codeIds);

    /***
     * 功能描述:
     * 获取行业电量占比<br>
     * @param cityId
     * @param startDate
     * @param endDate
     * @Return: {@link List< TradeElectricityProportionDTO>}
     * @Version: 1.0.0
     * @Author:<EMAIL>
     * @Date: 2022/9/2 18:13
     */
    List<TradeElectricityProportionDTO> getTradeElectricityProportion(String cityId, Date startDate, Date endDate);

    List<TradeFeatureDTO> getTradeFeature(String cityId, Date startDate, Date endDate, List<String> codeIds);

    List<IndustryLoadMaxFeatureDTO> getDayIndustryFeature(String cityId, Date startDate, Date endDate, String dataType, String periodType);

    List<IndustryLoadMaxFeatureDTO> getMonthIndustryFeature(String cityId, Date startDate, Date endDate, String dataType, String periodType);

    List<IndustryLoadMaxFeatureDTO> getMaxDayIndustryFeature(String tradeCode, String cityId, Date startDate, Date endDate, String dataType, String periodType);

    List<IndustryLoadMaxFeatureDTO> getMaxMonthIndustryFeature(String tradeCode, String cityId, Date startDate, Date endDate, String dataType, String periodType);
}
