package com.tsintergy.lf.serviceapi.base.evalucation.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class DayAssessDTO {

    private String cityName;

    /**
     * 日平均准确率
     */
    private BigDecimal dailyAvgAccuracy;

    /**
     * 日综合准确率
     */
    private BigDecimal dailyOverallAccuracy;

    /**
     * 日最大准确率
     */
    private BigDecimal dailyMaxAccuracy;

    /**
     * 日保供准确率
     */
    private BigDecimal dailySupplyAccuracy;

    /**
     * 午间最小负荷准确率
     */
    private BigDecimal middayMinLoadAccuracy;

    /**
     * 夜间最小负荷准确率
     */
    private BigDecimal nighttimeMinLoadAccuracy;

}
