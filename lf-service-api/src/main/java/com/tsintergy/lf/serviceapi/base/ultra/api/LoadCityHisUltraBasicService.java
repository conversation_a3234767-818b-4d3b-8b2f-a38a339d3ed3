
package com.tsintergy.lf.serviceapi.base.ultra.api;

import com.tsintergy.lf.serviceapi.base.ultra.pojo.LoadCityHisUltraBasicDO;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 包含查询 96 or 288 数据；其中 lambdaQuery无法命中子属性；暂时使用query
 * pointSize 数据点数； 使用常量 DataPointListMeta.MINUTE_5 or DataPointListMeta.MINUTE_15
 *
 * <AUTHOR>
 */
public interface LoadCityHisUltraBasicService {

    /**
     * 时间范围查询历史出力；
     *
     * @param delta 5 or 15
     * @return
     * @throws Exception
     */
    List<LoadCityHisUltraBasicDO> getLoadCityHisDOS(String cityId, String caliberId, Date startDate, Date endDate,
                                          Integer delta);

    LoadCityHisUltraBasicDO getLoadCityHisDO(String cityId, String caliberId, Date date, Integer delta) throws Exception;


    void doSaveOrUpdateList(List<LoadCityHisUltraBasicDO> loadCityHisDOS);

    List<BigDecimal> findLoadCityHisDO(String cityId, Date startDate, Date endDate, String caliberId, Integer delta);

    /***
     * @Description 查找多个城市的数据
     * @Date 2023/1/12 14:09
     * <AUTHOR>
     **/
    List<LoadCityHisUltraBasicDO> listLoadCityHisDOS(List<String> cityIds, String caliberId, Integer delta, Date startDate, Date endDate);

    /***
     * @Description 根据城市和口径以及时间范围删除数据，生成随机数据防止唯一索引冲突使用
     * @Date 2023/1/17 9:06
     * <AUTHOR>
     **/
    void deleteByCityIdAndCaliberIdList(String cityId, String caliberId, Date startDate, Date endDate);
}