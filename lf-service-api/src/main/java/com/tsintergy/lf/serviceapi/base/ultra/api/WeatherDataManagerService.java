
package com.tsintergy.lf.serviceapi.base.ultra.api;


import com.tsintergy.lf.serviceapi.base.ultra.dto.DataManagerDTO;
import com.tsintergy.lf.serviceapi.base.ultra.dto.WeatherNameDTO;
import com.tsintergy.lf.serviceapi.base.weather.api.BaseFcWeatherService;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version $Id: WeatherCityFcService.java, v 0.1 2018-01-31 10:59:49 tao Exp $$
 */

public interface WeatherDataManagerService extends BaseFcWeatherService {


    /***
     * @Description 查询数据管理模块预测气象数据
     * @Date 2023/1/9 13:17
     * <AUTHOR>
     **/
    List<DataManagerDTO> findWeatherCityFcDTOs(String cityId, Integer type, Date startDate, Date endDate) throws Exception;

    /**
     * 查询数据管理模块历史气象历史数据
     *
     * @param cityId    城市ID
     * @param type      气象类型
     * @param startDate 开始日期
     * @return endDate 结束日期
     */
    List<DataManagerDTO> findWeatherCityHisDTOs(String cityId, Integer type, Date startDate, Date endDate) throws Exception;

    /**
     * 查询該城市目标日的所有基础气象数据
     *
     * @param cityId
     * @param targetDate
     * @return
     * @throws Exception
     */
    List<WeatherNameDTO> findHisAllByDateAndCityId(String cityId, Date targetDate) throws Exception;

    List<WeatherNameDTO> findFcAllByDateAndCityId(String cityId, Date targetDate) throws Exception;

}