/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司 Author:  ThinkPad Date:  2019/5/15 7:26 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.ultra.dto;


import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 超短期日最低准确率详情对象
 *
 * <AUTHOR>
 * @create 2023/1/11
 * @since 1.0.0
 */
@Data
public class AccuracyLowDetailsDayDTO implements Serializable {


    /**
     * 日期
     */
    protected Date dateTime;

    /**
     * 对应时刻点预测负荷
     */
    protected BigDecimal fcLoad;

    /**
     * 对应时刻点历史负荷
     */
    protected BigDecimal hisLoad;

    /**
     * 负荷偏差
     */
    protected BigDecimal deviationLoad;

    /**
     * 预测温度
     */
    protected BigDecimal fcTemp;

    /**
     * 历史温度
     */
    protected BigDecimal hisTemp;

    /**
     * 温度偏差
     */
    protected BigDecimal deviationTemp;





}