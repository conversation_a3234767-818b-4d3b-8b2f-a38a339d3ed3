package com.tsintergy.lf.serviceapi.base.load.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

@Data
@ApiModel("准确率评估")
public class AccuracyEstimationDTO {

    @ApiModelProperty("日期")
    private String date;

    //日准确率
    @ApiModelProperty("每日精度")
    private BigDecimal dailyAccuracy;

    //日最大负荷预测值
    @ApiModelProperty("日最大预测负荷")
    private BigDecimal dailyMaximumLoadFc;


    //日最大负荷实际值
    @ApiModelProperty("日最大负荷实际值")
    private BigDecimal actualValueOfDailyMaximumLad;

    @ApiModelProperty("日最大负荷准确率")
    //日最大负荷准确率
    private BigDecimal dailyMaximumLoadAccuracy;

    @ApiModelProperty("日最小负荷预测值")
    //日最小负荷预测值
    private BigDecimal dailyMinimumLoadFc;

    @ApiModelProperty("日最小负荷实际值")
    //日最小负荷实际值
    private BigDecimal actualValueOfDailyMinimumLad;

    @ApiModelProperty("日最小负荷准确率")
    //日最小负荷准确率
    private BigDecimal dailyMinimumLoadAccuracy;

    @ApiModelProperty("日最大最小准确率")
    //日最大最小准确率
    private BigDecimal dailyMaximumAndMinimumAccuracy;

}