/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.requireresponse.pojo;

import com.tsieframework.core.base.dao.BaseDO;
import com.tsieframework.core.base.vo.annotation.EntityUniqueIndex;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

/**
 * Description:  <br>
 * 需求相应实体
 * @Author: <EMAIL>
 * @Date: 2022/8/23 14:47
 * @Version: 1.0.0
 */

@Data
@Entity
@Table(name = "REQUIRE_RESPONSE_INFO")
public class RequireResponseInfoDO implements BaseDO {


    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;



    /**
     * 日期
     */
    @Column(name = "date")
    private Date date;

    /**
     * 相应时段
     */
    @Column(name = "response_time")
    private String responseTime;

    /**
     *最大相应电力
     */
    @Column(name = "max_response_electricity")
    private BigDecimal maxResponseElectricity;

    /**
     * 最大还原负荷
     */
    @Column(name = "max_restore_load")
    private BigDecimal maxRestoreLoad;

    /**
     *响应电量
     */
    @Column(name = "response_power")
    private BigDecimal responsePower;


    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public String getResponseTime() {
        return responseTime;
    }

    public void setResponseTime(String responseTime) {
        this.responseTime = responseTime;
    }

    public BigDecimal getMaxResponseElectricity() {
        return maxResponseElectricity;
    }

    public void setMaxResponseElectricity(BigDecimal maxResponseElectricity) {
        this.maxResponseElectricity = maxResponseElectricity;
    }

    public BigDecimal getMaxRestoreLoad() {
        return maxRestoreLoad;
    }

    public void setMaxRestoreLoad(BigDecimal maxRestoreLoad) {
        this.maxRestoreLoad = maxRestoreLoad;
    }

    public BigDecimal getResponsePower() {
        return responsePower;
    }

    public void setResponsePower(BigDecimal responsePower) {
        this.responsePower = responsePower;
    }
}