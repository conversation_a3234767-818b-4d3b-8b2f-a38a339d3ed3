/**
 * Copyright (C), 2015‐2020, 北京清能互联科技有限公司 Author:  wangchen Date:  2020/4/19 16:12 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.ultra.api;



import com.tsintergy.lf.serviceapi.base.ultra.pojo.LoadFeatureCityDayUltraFcDO;
import com.tsintergy.lf.serviceapi.base.ultra.pojo.LoadFeatureCityMonthUltraFcDO;
import com.tsintergy.lf.serviceapi.base.ultra.pojo.LoadFeatureCityYearUltraFcDO;
import java.util.Date;
import java.util.List;

/**
 * 准确率特性数据查询；包含日&月&季&年的特性查询
 */
public interface LoadFeatureCityFcUltraService {

    void doSaveOrUpdateLoadFeatureCityDayFcDOS(List<LoadFeatureCityDayUltraFcDO> loadFeatureCityDayUltraFcDOS);

    List<LoadFeatureCityDayUltraFcDO> findLoadFeatureCityDayUltraFcDOS(String cityId, Date startDate, Date endDate,
        String caliberId, String algorithmId, Integer tDelta, Integer multipointType);


    LoadFeatureCityDayUltraFcDO findLoadFeatureCityDayUltraFcDO(String cityId, Date date, String caliberId,
        String algorithmId, Integer type);

    void doSaveOrUpdateLoadFeatureCityMonthFcDOS(List<LoadFeatureCityMonthUltraFcDO> loadFeatureCityDayUltraFcDOS);

    List<LoadFeatureCityMonthUltraFcDO> findLoadFeatureCityMonthUltraFcDOS(String cityId, Date startDate, Date endDate,
        String caliberId, String algorithmId, Integer tDelta, Integer multipointType);


    LoadFeatureCityMonthUltraFcDO findLoadFeatureCityMonthUltraFcDO(String cityId, Date date, String caliberId,
        String algorithmId, Integer type);

    void doSaveOrUpdateLoadFeatureCityYearFcDOS(List<LoadFeatureCityYearUltraFcDO> loadFeatureCityDayUltraFcDOS);

    List<LoadFeatureCityYearUltraFcDO> findLoadFeatureCityYearUltraFcDOS(String cityId, String startYear, String endYear,
        String caliberId, String algorithmId, Integer tDelta, Integer multipointType);


    LoadFeatureCityYearUltraFcDO findLoadFeatureCityYearUltraFcDO(String cityId, String year, String caliberId,
        String algorithmId, Integer type);

}
