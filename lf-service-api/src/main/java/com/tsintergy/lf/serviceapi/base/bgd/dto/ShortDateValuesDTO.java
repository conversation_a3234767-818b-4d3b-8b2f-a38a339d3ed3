package com.tsintergy.lf.serviceapi.base.bgd.dto;

import com.tsieframework.core.base.domain.dto.DTO;
import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import java.math.BigDecimal;
import lombok.Data;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2023-06-30
 * @since 1.0.0
 */
@Data
public class ShortDateValuesDTO implements DTO {

    private String date;

    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal decimal;
}
