package com.tsintergy.lf.serviceapi.base.weather.pojo;

import com.tsieframework.core.base.dao.BaseVO;
import java.math.BigDecimal;
import java.sql.Timestamp;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.UpdateTimestamp;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2023-06-13
 * @since 1.0.0
 */
@Data
@Entity
@Table(name = "weather_feature_city_ten_days_his_service")
public class WeatherFeatureCityTenDaysHisDO extends BaseVO {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;

    /**
     * 年
     */
    @Column(name = "year")
    private String year;

    /**
     * 月
     */
    @Column(name = "month")
    private String month;

    /**
     * 城市ID
     */
    @Column(name = "city_id")
    private String cityId;

    /**
     * 分旬
     */
    @Column(name = "type")
    private String type;

    /**
     * 气象数据类型
     */
    @Column(name = "status")
    private Boolean status;

    /**
     * 最高温度
     */
    @Column(name = "highest_temperature")
    private BigDecimal highestTemperature;

    /**
     * 最低温度
     */
    @Column(name = "lowest_temperature")
    private BigDecimal lowestTemperature;

    /**
     * 平均温度
     */
    @Column(name = "ave_temperature")
    private BigDecimal aveTemperature;

    /**
     * 极端最高温度
     */
    @Column(name = "extreme_highest_temperature")
    private BigDecimal extremeHighestTemperature;

    /**
     * 极端最低温度
     */
    @Column(name = "extreme_lowest_temperature")
    private BigDecimal extremeLowestTemperature;

    /**
     * 极端平均温度
     */
    @Column(name = "extreme_ave_temperature")
    private BigDecimal extremeAveTemperature;

    /**
     * 最高相对湿度
     */
    @Column(name = "highest_humidity")
    private BigDecimal highestHumidity;

    /**
     * 最低相对湿度
     */
    @Column(name = "lowest_humidity")
    private BigDecimal lowestHumidity;

    /**
     * 平均相对湿度
     */
    @Column(name = "ave_humidity")
    private BigDecimal aveHumidity;

    /**
     * 最大风速
     */
    @Column(name = "max_winds")
    private BigDecimal maxWinds;

    /**
     * 最小风速
     */
    @Column(name = "min_winds")
    private BigDecimal minWinds;

    /**
     * 平均风速
     */
    @Column(name = "ave_winds")
    private BigDecimal aveWinds;

    /**
     * 最大人体舒适度
     */
    @Column(name = "highest_comfort")
    private BigDecimal highestComfort;

    /**
     * 最低人体舒适度
     */
    @Column(name = "lowest_comfort")
    private BigDecimal lowestComfort;

    /**
     * 平均人体舒适度
     */
    @Column(name = "ave_comfort")
    private BigDecimal aveComfort;

    /**
     * 最高实感温度
     */
    @Column(name = "highest_effective_temperature")
    private BigDecimal highestEffectiveTemperature;

    /**
     * 最低实感温度
     */
    @Column(name = "lowest_effective_temperature")
    private BigDecimal lowestEffectiveTemperature;

    /**
     * 平均实感温度
     */
    @Column(name = "ave_effective_temperature")
    private BigDecimal aveEffectiveTemperature;

    /**
     * 最大寒冷指数
     */
    @Column(name = "max_coldness")
    private BigDecimal maxColdness;

    /**
     * 最小寒冷指数
     */
    @Column(name = "min_coldness")
    private BigDecimal minColdness;

    /**平均寒冷指数
     *
     */
    @Column(name = "ave_coldness")
    private BigDecimal aveColdness;

    /**
     * 最大温湿指数
     */
    @Column(name = "max_temperature_humidity")
    private BigDecimal maxTemperatureHumidity;

    /**
     * 最小温湿指数
     */
    @Column(name = "min_temperature_humidity")
    private BigDecimal minTemperatureHumidity;

    /**
     * 平均温湿指数
     */
    @Column(name = "ave_temperature_humidity")
    private BigDecimal aveTemperatureHumidity;

    /**
     * 降雨量
     */
    @Column(name = "rainfall")
    private BigDecimal rainfall;

    /**
     * 创建时间
     */
    @Column(name = "createtime")
    @CreationTimestamp
    private Timestamp createtime;

    /**
     * 更新时间
     */
    @Column(name = "updatetime")
    @UpdateTimestamp
    private Timestamp updatetime;

    @Column(name = "flag")
    private Boolean flag;
}
