package com.tsintergy.lf.serviceapi.base.ultra.forecast.api;


import com.tsintergy.lf.serviceapi.base.ultra.forecast.pojo.SettingAlarmInitDO;
import com.tsintergy.lf.serviceapi.base.ultra.forecast.dto.ForecastAlarmConfigDTO;

/**
 * Description: TODO <br>
 *
 * <AUTHOR>
 * @create 2023-10-19
 * @since 1.0.0
 */
public interface SettingAlarmInitService {

    SettingAlarmInitDO findByCityId(String cityId) throws Exception;

    void doSaveOrUpdate(ForecastAlarmConfigDTO forecastAlarmConfigDTO) throws Exception;
}
