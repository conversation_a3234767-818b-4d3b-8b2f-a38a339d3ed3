/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.trade.pojo;


import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

/**
 * Description:  <br>
 * 数据中台采集1  SER_HYRYDFH
 * @Author: <EMAIL>
 * @Date: 2022/8/29 15:06
 * @Version: 1.0.0
 */
@Data
@Entity
@Table(name = "TRADE_LOAD_HIS_STAT")
public class TradeLoadHisStatDO  extends BaseTradeDO{

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;


    @Column(name ="date_time")
    private Date dateTime;


    @Override
    public Date getDate() {
        return dateTime;
    }
}