/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.algorithm.dto;

import com.tsintergy.aif.algorithm.serviceapi.base.dto.shortforecast.ShortForecastParam;
import java.util.Date;

/**
 * Description: 区域预测算法参数 <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/8/8 12:15
 * @Version: 1.0.0
 */
public class AreaForecastAlgorithmParam extends ShortForecastParam {


    /**
     * 区域id
     */
    private String areaId;

    /**
     * 口径id
     */
    private String caliberId;

    public AreaForecastAlgorithmParam(String cityName, Date beginDate, Date endDate, String[] distinguishParams,
        Integer fcType, Integer point, Integer fcWay, Integer fcDayWeatherType) {
        super(cityName, beginDate, endDate, distinguishParams, fcType, point, fcWay, fcDayWeatherType);
    }


    public String getAreaId() {
        return areaId;
    }

    public void setAreaId(String areaId) {
        this.areaId = areaId;
    }

    public String getCaliberId() {
        return caliberId;
    }

    public void setCaliberId(String caliberId) {
        this.caliberId = caliberId;
    }


}