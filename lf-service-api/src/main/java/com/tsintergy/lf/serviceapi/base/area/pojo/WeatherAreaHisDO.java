package com.tsintergy.lf.serviceapi.base.area.pojo;


import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.aif.algorithm.serviceapi.base.pojo.Weather;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.serviceapi.base.base.pojo.Base96DO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.BaseWeatherDO;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;
import java.util.List;
import java.util.UUID;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.UpdateTimestamp;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 区域气象数据
 */
@Entity
@Table(name = "weather_area_his_basic")
public class WeatherAreaHisDO extends Base96DO implements Weather {

    public WeatherAreaHisDO(){

    }
    public WeatherAreaHisDO( Integer type, java.util.Date date){
        this.type=type;
        this.date=new Date(date.getTime());
        this.id= UUID.randomUUID().toString().replace("-","");
    }

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;


    /**
     * 日期
     */
    @Column(name = "date")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date date;


    /**
     * 区域id
     */
    @Column(name = "area_id")
    private String areaId;
    /**
     * 气象类型
     */
    @Column(name = "type")
    private Integer type;

    /**
     * 创建时间
     */
    @Column(name = "createtime")
    @CreationTimestamp
    private Timestamp createtime;

    /**
     * 更新时间
     */
    @Column(name = "updatetime")
    @UpdateTimestamp
    private Timestamp updatetime;


     
    public String getId() {
        return id;
    }

     
    public void setId(String id) {
        this.id = id;
    }

     
    public Date getDate() {
        return date;
    }

     
    public void setDate(Date date) {
        this.date = date;
    }
    

     
    public Integer getType() {
        return type;
    }

    public String getAreaId() {
        return areaId;
    }

    public void setAreaId(String areaId) {
        this.areaId = areaId;
    }

     
    public void setType(Integer type) {
        this.type = type;
    }


     
    public Timestamp getCreatetime() {
        return createtime;
    }

     
    public void setCreatetime(Timestamp createtime) {
        this.createtime = createtime;
    }

     
    public Timestamp getUpdatetime() {
        return updatetime;
    }

     
    public void setUpdatetime(Timestamp updatetime) {
        this.updatetime = updatetime;
    }

    @Override
    public List<BigDecimal> getWeatherList() {
        return BasePeriodUtils.toList(this, 96, Constants.LOAD_CURVE_START_WITH_ZERO);
    }
}
