package com.tsintergy.lf.serviceapi.base.bgd.dto;

import com.tsieframework.core.base.domain.dto.DTO;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class YearWeatherFeatureDTO implements DTO {

    private String month;

    private BigDecimal hisHighTem;

    private BigDecimal fcHighTem;

    private BigDecimal hisLowestTem;

    private BigDecimal fcLowestTem;

    private BigDecimal hisAveTem;

    private BigDecimal fcAveTem;




}
