package com.tsintergy.lf.serviceapi.base.bgd.enums;

/**
 * Description: TODO <br>
 *
 * <AUTHOR>
 * @create 2023-05-05
 * @since 1.0.0
 */
public enum DefaultAlgorithmIdTypeEnum {

    DAY("1", "日类型"),
    TEN_DAY("2", "旬类型"),
    MONTH("3", "月类型");

    /**
     * 默认算法类型
     */
    private String type;

    /**
     * 类型名称
     */
    private String name;


    DefaultAlgorithmIdTypeEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
