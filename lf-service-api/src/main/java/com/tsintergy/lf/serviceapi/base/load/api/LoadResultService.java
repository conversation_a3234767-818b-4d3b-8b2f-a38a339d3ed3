/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.load.api;

import com.tsintergy.lf.serviceapi.base.evalucation.dto.DayAssessDTO;
import com.tsintergy.lf.serviceapi.base.load.dto.*;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadAssessDTO;

import java.util.Date;
import java.util.List;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/3/31 14:16
 * @Version: 1.0.0
 */
public interface LoadResultService {
    /**
     * 时刻准确率
     * @param cityId
     * @param caliberId
     * @param date
     * @return
     * @throws Exception
     */
    LoadAccuracyDTO findAccuracy(String cityId, String caliberId, Date date) throws Exception;

    /**
     * 多天准确性
     *
     * @param cityId 城市标识
     * @param caliberId 口径id
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return {@code List<LoadDaysAccuracyDTO>}
     * @throws Exception 异常
     */
    List<LoadDaysAccuracyDTO> findDaysAccuracy(String cityId, String caliberId, Date startDate, Date endDate) throws Exception;

    /**
     * 多月准确性
     *
     * @param cityId 城市标识
     * @param caliberId 口径id
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return {@code List<LoadDaysAccuracyDTO>}
     * @throws Exception 异常
     */
    List<LoadDaysAccuracyDTO> findMonthAccuracy(String cityId, String caliberId, Date startDate, Date endDate) throws Exception;

    /**
     * 多年准确性
     *
     * @param cityId 城市标识
     * @param caliberId 口径id
     * @param startYear 开始日期
     * @param endYear 结束日期
     * @return {@code List<LoadDaysAccuracyDTO>}
     * @throws Exception 异常
     */
    List<LoadDaysAccuracyDTO> findYearAccuracy(String cityId, String caliberId, String startYear, String endYear) throws Exception;

    /**
     * 多月准确性
     *
     * @param cityId 城市标识
     * @param caliberId 口径id
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return {@code List<LoadDaysAccuracyDTO>}
     * @throws Exception 异常
     */
    List<LoadLongTermAccuracyDTO> findLongMonthAccuracy(String cityId, String caliberId, Date startDate, Date endDate) throws Exception;

    /**
     * 找到季节准确性
     *
     * @param cityId 城市标识
     * @param year 一年
     * @param season 季节
     * @param caliberId 口径
     * @return {@code List<LoadLongTermAccuracyDTO>}
     * @throws Exception 异常
     */
    SeasonDTO findSeasonAccuracy(String cityId, String caliberId, String year, String season) throws Exception;

    /**
     * 发现长年准确性
     *
     * @param cityId 城市标识
     * @param caliberId 口径id
     * @param year 一年
     * @param season 季节
     * @return {@code SeasonDTO}
     * @throws Exception 异常
     */
    SeasonDTO findLongYearAccuracy(String cityId, String caliberId, String year, String season) throws Exception;

    /**
     * 日评估
     *
     * @param startDate 开始日期
     * @param startDate 结束日期
     * @param caliberId 口径id
     * @return {@code List<LoadAssessDTO>}
     * @throws Exception 异常
     */
    List<DayAssessDTO> findDayAssess(Date startDate, Date endDate, String caliberId, String batchId) throws Exception;

    /**
     * 找到月评估
     *
     * @param startDate 开始日期
     * @param caliberId 口径id
     * @return {@code List<LoadAssessDTO>}
     * @throws Exception 异常
     */
    List<LoadAssessDTO> findMonthAssess(Date startDate, String caliberId) throws Exception;

    /**
     * 发现年评估
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param caliberId 口径id
     * @return {@code List<LoadAssessDTO>}
     * @throws Exception 异常
     */
    List<LoadAssessDTO> findYearAssess(Date startDate, Date endDate, String caliberId) throws Exception;

    /**
     * 发现城市报告状态
     *
     * @param startDate 开始日期
     * @param caliberId 口径id
     * @return {@code List<CityReportStateDTO>}
     * @throws Exception 异常
     */
    List<CityReportStateDTO> findCityReportState(Date startDate, String caliberId) throws Exception;

    /**
     * 城市报告时间配置吗
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param firstCityIds 第一个城市id
     * @param lastCityIds 去年城市id
     * @param type1 类型1
     * @param type2 类型2
     * @throws Exception 异常
     */
    void doCityReportTimeConfig(String startDate, String endDate, List<String> firstCityIds, List<String> lastCityIds,
        String type1, String type2) throws Exception;

    /**
     * 得到城市报告时间配置
     *
     * @param firstCityIds 第一个城市id
     * @param lastCityIds 去年城市id
     * @return {@code CityReportResultDTO}
     * @throws Exception 异常
     */
    CityReportResultDTO getCityReportTimeConfig(List<String> firstCityIds, List<String> lastCityIds) throws Exception;

    /**
     * 城市变长时间配置报告
     *
     * @param monthCityIds 月城市id
     * @param summerCityIds 夏天城市id
     * @param winterCityIds 冬季城市id
     * @param yearCityIds 年城市id
     * @return {@code LongCityReportResultDTO}
     * @throws Exception 异常
     */
    LongCityReportResultDTO getLongCityReportTimeConfig(List<String> monthCityIds, List<String> summerCityIds,
        List<String> winterCityIds, List<String> yearCityIds) throws Exception;

    /**
     * 城市长时间报告配置吗
     *
     * @param monthDate 月日期
     * @param summerDate 夏天日期
     * @param winterDate 冬天日期
     * @param yearDate 年日期
     * @param monthCityIds 月城市id
     * @param summerCityIds 夏天城市id
     * @param winterCityIds 冬季城市id
     * @param yearCityIds 年城市id
     * @param monthType 月类型
     * @param summerType 夏天类型
     * @param winterType 冬天类型
     * @param yearType 一年类型
     * @throws Exception 异常
     */
    void doCityLongReportTimeConfig(String monthDate, String summerDate, String winterDate, String yearDate,
        List<String> monthCityIds, List<String> summerCityIds, List<String> winterCityIds, List<String> yearCityIds,
        String monthType, String summerType, String winterType, String yearType)
        throws Exception;
}
