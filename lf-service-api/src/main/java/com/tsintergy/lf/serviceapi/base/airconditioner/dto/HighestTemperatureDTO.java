package com.tsintergy.lf.serviceapi.base.airconditioner.dto;

import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel
public class HighestTemperatureDTO {
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "最高温度曲线")
    List<BigDecimal> temperature;

    @ApiModelProperty(value = "年份")
    String year;

    @ApiModelProperty(value = "最高温度日期")
    List<String> date;

    @ApiModelProperty(value = "最高温度时刻")
    List<String> time;
}
