/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.area.api;

import com.tsintergy.lf.serviceapi.base.area.dto.BasePlanDTO;
import com.tsintergy.lf.serviceapi.base.area.pojo.BasePlanDO;
import java.util.List;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/8/4 9:37
 * @Version: 1.0.0
 */
public interface BasePlanService {

    BasePlanDO getDefaultPlan();


    List<String> getDefaultAreaIds();

     List<BasePlanDTO> findAllPlans();
    void findPlanByName(String name,String id) throws Exception;
     List<BasePlanDO> findAllDO();
     List<String> findAreaIdsByPlanId(String planId);
    void doUpdate(String planId, String name, Integer valid, List<String> areaIds,Integer fcMode);
    void doDelete(String planId);
    void doInsert(String planName,List<String> areaIds,Integer fcMode);
}
