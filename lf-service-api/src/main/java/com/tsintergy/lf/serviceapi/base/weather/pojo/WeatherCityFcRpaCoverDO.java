package com.tsintergy.lf.serviceapi.base.weather.pojo;

import com.tsieframework.core.base.vo.annotation.EntityUniqueIndex;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.aif.algorithm.serviceapi.base.pojo.Weather;
import com.tsintergy.lf.core.constants.Constants;
import java.math.BigDecimal;
import java.util.List;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import lombok.Data;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2024-12-03
 * @since 1.0.0
 */
@Data
@Entity
@EntityUniqueIndex(value = {"cityId", "date", "stationId", "type"})
@Table(name = "weather_city_fc_rpa_cover_basic")
public class WeatherCityFcRpaCoverDO extends BaseWeatherDO implements Weather {

    public WeatherCityFcRpaCoverDO() {
        super();
    }

    public WeatherCityFcRpaCoverDO(String cityId, Integer type, java.util.Date date) {
        super(cityId, type, date);
    }

    /**
     * 站点ID
     */
    @Column(name = "station_id")
    private String stationId;

    @Override
    public List<BigDecimal> getWeatherList() {
        return BasePeriodUtils.toList(this, 96, Constants.LOAD_CURVE_START_WITH_ZERO);
    }
}

