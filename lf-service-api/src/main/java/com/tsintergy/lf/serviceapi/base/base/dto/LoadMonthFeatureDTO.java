package com.tsintergy.lf.serviceapi.base.base.dto;

import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2023-10-23
 * @since 1.0.0
 */
@Data
public class LoadMonthFeatureDTO implements Serializable {

    /**
     * 日期
     */
    private String dateStr;

    /**
     * 最大负荷
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal maxLoad;

    /**
     * 最大负荷出现时间
     */
    private String maxLoadTime;

    /**
     * 最小负荷
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal minLoad;

    /**
     * 最小负荷出现时间
     */
    private String minLoadTime;

    /**
     * 早峰负荷
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal morningMaxLoad;

    /**
     * 早峰负荷出现时间
     */
    private String morningLoadTime;

    /**
     * 最小腰荷
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal noontimeMinLoad;

    /**
     * 最小腰荷出现时间
     */
    private String noontimeMinLoadTime;

    /**
     * 最大腰荷
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal noontimeMaxLoad;

    /**
     * 最大腰荷出现时间
     */
    private String noontimeMaxLoadTime;

    /**
     * 晚峰负荷
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal eveningMaxLoad;

    /**
     * 晚峰负荷出现时间
     */
    private String eveningMaxLoadTime;

    /**
     * 月平均负荷
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal aveLoad;

    /**
     * 月电量
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal energy;

    /**
     * 最大峰谷差
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal maxDifference;

    /**
     * 最大峰谷差日
     */
    private String maxDifferenceDay;

    /**
     * 最小峰谷差
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal minDifference;

    /**
     * 最小峰谷差日
     */
    private String minDifferenceDay;

    /**
     * 平均峰谷差
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal aveDifference;

    /**
     * 平均峰谷差率
     */
    @BigdecimalJsonFormat(percentConvert = 100, scale = 2)
    private BigDecimal aveDifferenceRate;

    /**
     * 平均最大负荷
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal aveMaxLoad;

    /**
     * 平均最小负荷
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal aveMinLoad;

    /**
     * 平均早峰负荷
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal aveMorningMaxLoad;

    /**
     * 平均最小腰荷
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal aveMinNoontimeLoad;

    /**
     * 平均最大腰荷
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal aveMaxNoontimeLoad;

    /**
     * 平均晚峰负荷
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal aveMaxEveningLoad;

    /**
     * 负荷率最大值
     */
    @BigdecimalJsonFormat(percentConvert = 100, scale = 2)
    private BigDecimal maxLoadRate;

    /**
     * 负荷率最大值出现日
     */
    private String maxLoadRateDay;

    /**
     * 负荷率最小值
     */
    @BigdecimalJsonFormat(percentConvert = 100, scale = 2)
    private BigDecimal minLoadRate;

    /**
     * 负荷率最小值出现日
     */
    private String minLoadRateDay;

    /**
     * 负荷率平均值
     */
    @BigdecimalJsonFormat(percentConvert = 100, scale = 2)
    private BigDecimal aveLoadRate;
}
