package com.tsintergy.lf.serviceapi.base.bgd.pojo;

import com.tsieframework.core.base.dao.BaseVO;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

/**
 * Description: TODO <br>
 *
 * <AUTHOR>
 * @create 2023-02-16
 * @since 1.0.0
 */
@Data
@Entity
@Table(name = "INDUSTRY_BASE_INIT")
public class IndustryBaseInitDO extends BaseVO {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;

    @Column(name = "name")
    private String name;

    @Column(name = "type")
    private String type;

    @Column(name = "level")
    private String level;

    @Column(name = "important")
    private Boolean important;

    @Column(name = "order_no")
    private Boolean orderNo;

    @Column(name = "parent_id")
    private String parentId;

}
