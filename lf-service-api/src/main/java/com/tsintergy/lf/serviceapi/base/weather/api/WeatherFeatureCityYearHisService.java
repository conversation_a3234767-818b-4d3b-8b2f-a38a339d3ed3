package com.tsintergy.lf.serviceapi.base.weather.api;

import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityYearHisDO;
import java.util.List;

/**
 * Description: TODO <br>
 *
 * <AUTHOR>
 * @create 2023-06-16
 * @since 1.0.0
 */
public interface WeatherFeatureCityYearHisService {

    /**
     * 查询十天天气特征
     *
     * @param cityId 城市标识
     * @param year 一年
     * @param month 月
     * @return {@code List<WeatherFeatureCityYearHisDO>}
     */
    List<WeatherFeatureCityYearHisDO> findWeatherFeatureCityTenDaysDTO(String cityId, String year, List<String> month);

    List<WeatherFeatureCityYearHisDO> findWeatherFeatureCityTenDaysDTO(String cityId, String year, String month);

    void doSaveOrUpdate(WeatherFeatureCityYearHisDO weatherFeatureCityYearHisDO);

}
