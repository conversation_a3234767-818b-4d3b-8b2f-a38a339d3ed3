package com.tsintergy.lf.serviceapi.base.bgd.pojo;

import com.tsieframework.core.base.dao.BaseVO;
import java.sql.Timestamp;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.UpdateTimestamp;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2023-05-05
 * @since 1.0.0
 */
@Data
@Entity
@Table(name = "DEFAULT_ALGORITHM_ID_CONF")
public class DefaultAlgorithmIdConfDO extends BaseVO {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;

    @Column(name = "city_id")
    private String cityId;

    /**
     * 算法类型 1-日类型 2-旬类型 3-月类型
     */
    @Column(name = "type")
    private String type;

    @Column(name = "default_algorithm_id")
    private String defaultAlgorithmId;

    /**
     * 创建时间
     */
    @Column(name = "createtime")
    @CreationTimestamp
    protected Timestamp createtime;

    /**
     * 更新时间
     */
    @Column(name = "updatetime")
    @UpdateTimestamp
    protected Timestamp updatetime;
}
