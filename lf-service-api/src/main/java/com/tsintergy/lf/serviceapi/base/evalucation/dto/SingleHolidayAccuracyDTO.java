package com.tsintergy.lf.serviceapi.base.evalucation.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

@Data
@ApiModel("单假日准确率实体")
public class SingleHolidayAccuracyDTO {
    @ApiModelProperty("假日")
    private String holiday;
    @ApiModelProperty("日期")
    private Date date;
    @ApiModelProperty("手动精度")
    private String manualAccuracy;
    @ApiModelProperty("假期算法准确率")
    private String holidayAlgorithmAccuracy;
}