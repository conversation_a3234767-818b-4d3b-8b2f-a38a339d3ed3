package com.tsintergy.lf.serviceapi.base.load.pojo;

import com.tsieframework.core.base.dao.BaseVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;

@Data
@Entity
@NoArgsConstructor
@Table(name = "setting_warn_init")
public class SettingWarnInitDO extends BaseVO {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;

    /**
     * 城市id
     */
    @Column(name = "city_id")
    private String cityId;

    /**
     * 预警类型
     */
    @ApiModelProperty(value = "预警类型：1：日最大负荷预警阈值；2：日电量预警阈值；3：最大负荷发生时间预警阈值")
    @Column(name = "warn_type")
    private Integer warnType;

    /**
     * 预警指标描述
     */
    @Column(name = "describe")
    private String describe;

    /**
     * 预警指标值
     */
    @Column(name = "value")
    private String value;
}
