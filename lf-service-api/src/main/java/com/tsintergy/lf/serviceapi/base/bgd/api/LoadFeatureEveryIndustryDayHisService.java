package com.tsintergy.lf.serviceapi.base.bgd.api;

import com.tsintergy.aif.algorithm.serviceapi.base.pojo.Load;
import com.tsintergy.lf.serviceapi.base.bgd.pojo.LoadFeatureEveryIndustryDayHisServiceDO;
import com.tsintergy.lf.serviceapi.base.trade.pojo.TradeLoadFeatureDayHisDO;
import java.util.Date;
import java.util.List;

/**
 * Description: TODO <br>
 *
 * <AUTHOR>
 * @create 2023-02-16
 * @since 1.0.0
 */
public interface LoadFeatureEveryIndustryDayHisService {

    List<LoadFeatureEveryIndustryDayHisServiceDO> getLoadFeatureEveryIndustryDayHisServiceList(String cityId, String id, Date startDate, Date endDate);

    List<LoadFeatureEveryIndustryDayHisServiceDO> getLoadFeatureEveryIndustryDayHisServiceList(String cityId, Date startDate, Date endDate);

    List<LoadFeatureEveryIndustryDayHisServiceDO> getLoadFeatureEveryIndustryDayHisServiceLists(String cityId, Date startDate, Date endDate, List<String> industryList);

    List<LoadFeatureEveryIndustryDayHisServiceDO> getLoadFeatureEveryIndustryDayHisServiceTypeList(String cityId, Date startDate, Date endDate, List<String> type);

    LoadFeatureEveryIndustryDayHisServiceDO doStatLoadFeatureCityDayTests(boolean startWithZero, Load load,Date date) throws Exception;

    void saveOrUpdate(TradeLoadFeatureDayHisDO tradeLoadFeatureDayHisDO);

}
