/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司 Author:  ThinkPad Date:  2019/5/15 7:26 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.ultra.dto;


import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * 超短期日准确率结果对象
 *
 * <AUTHOR>
 * @create 2023/1/11
 * @since 1.0.0
 */
@Data
public class MultiPeriodAccuracyDTO implements Serializable {


    /**
     * 预测对比
     */
    private List<MultiPeriodLoadUnitDTO> fcList;

    /**
     * 偏差对比
     */
    private List<MultiPeriodLoadUnitDTO> deviationList;

    /**
     * 准确率对比
     */

    private List<MultiPeriodAccuracyUnitDTO> accuracyList;
}