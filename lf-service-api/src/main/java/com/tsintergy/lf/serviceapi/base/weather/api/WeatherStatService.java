/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.weather.api;

import java.util.Date;

/**
 *Description: 气象统计 <br>
 *
 *@Author: <EMAIL>
 *@Date: 2022/6/29 17:29
 *@Version: 1.0.0
 */
public interface WeatherStatService {

    /**
     * 根据权重计算省调气象
     * @param start
     * @param end
     */
    void statPorviceWeather(Date start, Date end) throws Exception;

    void statPorviceHisWeather(Date start, Date end) throws Exception;

    void statPorviceFcWeather(Date start, Date end) throws Exception;

}