package com.tsintergy.lf.serviceapi.base.bgd.api;

import com.tsieframework.core.base.service.FacadeService;
import com.tsintergy.lf.serviceapi.base.bgd.pojo.LoadFeatureCityTenDaysHisServiceDO;

import java.util.List;

/**
 * Description: TODO <br>
 *
 * <AUTHOR>
 * @create 2023-02-16
 * @since 1.0.0
 */
public interface LoadFeatureCityTenDaysHisService extends FacadeService {

    List<LoadFeatureCityTenDaysHisServiceDO> findLoadFeatureCityTenDaysHisServiceDOs(String cityId, String caliberId,
                                                                                     String year, String month, String type);

    List<LoadFeatureCityTenDaysHisServiceDO> findLoadFeatureCityTenDaysHisServiceDOs(String cityId, String caliberId,
                                                                                     String year, List<String> monthList);

    List<LoadFeatureCityTenDaysHisServiceDO> findLoadFeatureCityTenDaysHisServiceDOs(String cityId, String caliberId,
                                                                                     List<String> yearList, String month);

    List<LoadFeatureCityTenDaysHisServiceDO> findLoadFeatureCityTenDaysHisServiceDOs(List<String> cityIds,
                                                                                     String caliberId, String year, String month);

    List<LoadFeatureCityTenDaysHisServiceDO> findByRangeCondition(String cityId, String caliberId, String startYM, String endYM);

}
