/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司 Author:  ThinkPad Date:  2019/5/15 7:26 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.ultra.dto;


import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 超短期月准确率结果对象 -和月度通用
 *
 * <AUTHOR>
 * @create 2023/1/11
 * @since 1.0.0
 */
@Data
public class AccuracyMonthDTO extends AccuracyUltraDayDTO {


    /**
     * 日期 yyyy-mm 季度展示时返回 第一季度；第二季度...
     */
    protected String yearMonth;

    /**
     * 继承关系时 父类中的BigdecimalJsonFormat会失效；因此在这里重写
     *
     * 最大负荷时刻准确率
     */
    @BigdecimalJsonFormat(percentConvert = 100)
    protected BigDecimal maxLoadAccuracy;

    /**
     * 最小负荷时刻准确率
     */
    @BigdecimalJsonFormat(percentConvert = 100)
    protected BigDecimal minLoadAccuracy;


    /**
     * 最高准确率
     */
    @BigdecimalJsonFormat(percentConvert = 100)
    protected BigDecimal maxAccuracy;

    /**
     * 最低准确率
     */
    @BigdecimalJsonFormat(percentConvert = 100)
    protected BigDecimal minAccuracy;

    /**
     * 平均准确率
     */
    @BigdecimalJsonFormat(percentConvert = 100)
    protected BigDecimal avgAccuracy;

}