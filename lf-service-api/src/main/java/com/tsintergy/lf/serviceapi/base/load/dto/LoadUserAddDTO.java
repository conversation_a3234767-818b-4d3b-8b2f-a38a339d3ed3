package com.tsintergy.lf.serviceapi.base.load.dto;

import com.tsieframework.core.base.domain.dto.DTO;
import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class LoadUserAddDTO implements DTO {

    @ApiModelProperty(value = "选择日期")
    private Date date;

    @ApiModelProperty(value = "选择日负荷数据")
    @BigdecimalJsonFormat(scale = 0)
    private List<BigDecimal> load;


    @ApiModelProperty(value = "上周同类日日期")
    private Date lastDate;

    @ApiModelProperty(value = "上周同类日日期负荷数据")
    @BigdecimalJsonFormat(scale = 0)
    private List<BigDecimal> lastLoad;
}
