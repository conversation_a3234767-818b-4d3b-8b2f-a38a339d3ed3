package com.tsintergy.lf.serviceapi.base.forecast.dto;

import com.tsieframework.core.base.domain.dto.DTO;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * Description: 十日预测日期数据
 *
 * <AUTHOR>
 * @create 2023-03-06
 * @since 1.0.0
 */
@Data
public class TenDayCurveDateValuesDTO implements DTO {

    /**
     * 日期
     */
    private Date date;

    /**
     * 96点负荷
     */
    private List<BigDecimal> bigDecimals;

}
