package com.tsintergy.lf.serviceapi.base.bgd.dto;

import com.tsieframework.core.base.domain.dto.DTO;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * Description: 分旬手动预测
 *
 * <AUTHOR>
 * @create 2023-03-24
 * @since 1.0.0
 */
@Data
public class MonthManualForecastDTO implements DTO {

    private String cityId;

    private List<String> algorithmIds;

    private Date forecastDay;

    private String season;

    private String status;

    private Date endDay;

}
