/**
 * Copyright (C), 2015‐2020, 北京清能互联科技有限公司 Author:  wangchen Date:  2020/4/19 16:12 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.ultra.api;

import com.tsintergy.lf.serviceapi.base.ultra.pojo.LoadFeatureCityDayUltraFcDO;
import com.tsintergy.lf.serviceapi.base.ultra.pojo.UltraLoadFeatureCityMonthFcDO;
import java.util.List;

/**
 * 统计 周预测特性
 *
 * <AUTHOR>
 * @create 2020/7/6
 * @since 1.0.0
 */
public interface UltraLoadFeatureCityMonthFcService {

    void doSaveOrUpdateLoadFeatureCityMonthFcDOS(List<UltraLoadFeatureCityMonthFcDO> monthFcVOS);


     List<UltraLoadFeatureCityMonthFcDO> findLoadFeatureMonth(String cityId, String algorithmId,
         String startYm,
         String endYm, String caliberId) throws Exception;


    /**
     * 月 负荷特性   预测  计算逻辑
     */
    List<UltraLoadFeatureCityMonthFcDO> statisticsMonthFeatures(List<LoadFeatureCityDayUltraFcDO> monthFcVOS)
        throws Exception;
}
