/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.bgd.pojo;

import com.tsieframework.core.base.dao.BaseVO;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.sql.Timestamp;

/**
 * Description:<br>
 *
 * @Author:Liujp
 */
@Data
@Entity
@Table(name = "WEATHER_LONG_SITUATION")
public class WeatherLongSituationDO extends BaseVO {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;

    /**
     * 城市
     */
    @Column(name = "city_id")
    protected String cityId;

    @Column(name = "year")
    private String year;

    @Column(name = "month")
    private String month;

    @Column(name = "situation")
    private String situation;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @CreationTimestamp
    protected Timestamp createtime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    @UpdateTimestamp
    protected Timestamp updatetime;
}