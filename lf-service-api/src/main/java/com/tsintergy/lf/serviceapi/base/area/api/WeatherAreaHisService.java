/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.area.api;

import com.tsintergy.lf.serviceapi.base.area.pojo.WeatherAreaHisDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.BaseWeatherDO;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/8/3 17:25
 * @Version: 1.0.0
 */
public interface WeatherAreaHisService {

    /**
     * 查询气象预测数据
     * @param areaId 气象城市ID
     * @param type 气象类型
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return
     */
    List<WeatherAreaHisDO> findWeatherCityHisDOs(String areaId, Integer type, Date startDate, Date endDate);


    void doInsertOrUpdate(WeatherAreaHisDO weatherAreaHisDO) ;


    void doInsertOrUpdateList(List<WeatherAreaHisDO> weatherAreaHisDOS);

    void doInsert(WeatherAreaHisDO weatherAreaHisDO);

    /**
     * 统计区域实际气象
     * @param areaIds
     * @param startDate
     * @param endDate
     * @return
     */
    void statAreaHisWeather(String areaId, Date startDate, Date endDate) throws Exception;

    public void deleteByAreasIds(List<String> areaIds) throws Exception ;

    /**
     * 获取区域城市的气象平均
     * @param cityIds
     * @param map
     * @return
     * @throws Exception
     */
    Map<String, BigDecimal> getAveAreaWeatherValueMap(List<String> cityIds,Map<String, BaseWeatherDO> map,Date date,Integer type) throws Exception;
}

