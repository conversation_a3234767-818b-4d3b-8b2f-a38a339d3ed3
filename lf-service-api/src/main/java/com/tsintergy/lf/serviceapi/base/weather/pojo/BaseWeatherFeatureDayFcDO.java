package com.tsintergy.lf.serviceapi.base.weather.pojo;

import com.tsieframework.core.base.dao.BaseVO;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;
import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.UpdateTimestamp;

/**
 * 气象特性（预测）
 */
@MappedSuperclass
public  class BaseWeatherFeatureDayFcDO extends BaseVO {


    /**
     * 日期
     */
    @Column(name = "date")
    private Date date;


    /**
     * 最高温度
     */
    @Column(name = "highest_temperature")
    private BigDecimal highestTemperature;

    /**
     * 最低温度
     */
    @Column(name = "lowest_temperature")
    private BigDecimal lowestTemperature;

    /**
     * 平均温度
     */
    @Column(name = "ave_temperature")
    private BigDecimal aveTemperature;

    /**
     * 最高相对湿度
     */
    @Column(name = "highest_humidity")
    private BigDecimal highestHumidity;

    /**
     * 最低相对湿度
     */
    @Column(name = "lowest_humidity")
    private BigDecimal lowestHumidity;

    /**
     * 平均相对湿度
     */
    @Column(name = "ave_humidity")
    private BigDecimal aveHumidity;

    /**
     * 最大风速
     */
    @Column(name = "max_winds")
    private BigDecimal maxWinds;

    /**
     * 最小风速
     */
    @Column(name = "min_winds")
    private BigDecimal minWinds;

    /**
     * 平均风速
     */
    @Column(name = "ave_winds")
    private BigDecimal aveWinds;

    /**
     * 最大人体舒适度
     */
    @Column(name = "highest_comfort")
    private BigDecimal highestComfort;

    /**
     * 最低人体舒适度
     */
    @Column(name = "lowest_comfort")
    private BigDecimal lowestComfort;

    /**
     * 平均人体舒适度
     */
    @Column(name = "ave_comfort")
    private BigDecimal aveComfort;

    /**
     * 最高实感温度
     */
    @Column(name = "highest_effective_temperature")
    private BigDecimal highestEffectiveTemperature;

    /**
     * 最低实感温度
     */
    @Column(name = "lowest_effective_temperature")
    private BigDecimal lowestEffectiveTemperature;

    /**
     * 平均实感温度
     */
    @Column(name = "ave_effective_temperature")
    private BigDecimal aveEffectiveTemperature;

    /**
     * 最大寒冷指数
     */
    @Column(name = "max_coldness")
    private BigDecimal maxColdness;

    /**
     * 最小寒冷指数
     */
    @Column(name = "min_coldness")
    private BigDecimal minColdness;

    /**平均寒冷指数
     *
     */
    @Column(name = "ave_coldness")
    private BigDecimal aveColdness;

    /**
     * 最大温湿指数
     */
    @Column(name = "max_temperature_humidity")
    private BigDecimal maxTemperatureHumidity;

    /**
     * 最小温湿指数
     */
    @Column(name = "min_temperature_humidity")
    private BigDecimal minTemperatureHumidity;

    /**
     * 平均温湿指数
     */
    @Column(name = "ave_temperature_humidity")
    private BigDecimal aveTemperatureHumidity;

    /**
     * 降雨量
     */
    @Column(name = "rainfall")
    private BigDecimal rainfall;

    /**
     * 创建时间
     */
    @Column(name = "createtime")
    @CreationTimestamp
    private Timestamp createtime;

    /**
     * 更新时间
     */
    @Column(name = "updatetime")
    @UpdateTimestamp
    private Timestamp updatetime;


    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public BigDecimal getHighestTemperature() {
        return highestTemperature;
    }

    public void setHighestTemperature(BigDecimal highestTemperature) {
        this.highestTemperature = highestTemperature;
    }

    public BigDecimal getLowestTemperature() {
        return lowestTemperature;
    }

    public void setLowestTemperature(BigDecimal lowestTemperature) {
        this.lowestTemperature = lowestTemperature;
    }

    public BigDecimal getAveTemperature() {
        return aveTemperature;
    }

    public void setAveTemperature(BigDecimal aveTemperature) {
        this.aveTemperature = aveTemperature;
    }

    public BigDecimal getHighestHumidity() {
        return highestHumidity;
    }

    public void setHighestHumidity(BigDecimal highestHumidity) {
        this.highestHumidity = highestHumidity;
    }

    public BigDecimal getLowestHumidity() {
        return lowestHumidity;
    }

    public void setLowestHumidity(BigDecimal lowestHumidity) {
        this.lowestHumidity = lowestHumidity;
    }

    public BigDecimal getAveHumidity() {
        return aveHumidity;
    }

    public void setAveHumidity(BigDecimal aveHumidity) {
        this.aveHumidity = aveHumidity;
    }

    public BigDecimal getMaxWinds() {
        return maxWinds;
    }

    public void setMaxWinds(BigDecimal maxWinds) {
        this.maxWinds = maxWinds;
    }

    public BigDecimal getMinWinds() {
        return minWinds;
    }

    public void setMinWinds(BigDecimal minWinds) {
        this.minWinds = minWinds;
    }

    public BigDecimal getAveWinds() {
        return aveWinds;
    }

    public void setAveWinds(BigDecimal aveWinds) {
        this.aveWinds = aveWinds;
    }

    public BigDecimal getHighestComfort() {
        return highestComfort;
    }

    public void setHighestComfort(BigDecimal highestComfort) {
        this.highestComfort = highestComfort;
    }

    public BigDecimal getLowestComfort() {
        return lowestComfort;
    }

    public void setLowestComfort(BigDecimal lowestComfort) {
        this.lowestComfort = lowestComfort;
    }

    public BigDecimal getAveComfort() {
        return aveComfort;
    }

    public void setAveComfort(BigDecimal aveComfort) {
        this.aveComfort = aveComfort;
    }

    public BigDecimal getHighestEffectiveTemperature() {
        return highestEffectiveTemperature;
    }

    public void setHighestEffectiveTemperature(BigDecimal highestEffectiveTemperature) {
        this.highestEffectiveTemperature = highestEffectiveTemperature;
    }

    public BigDecimal getLowestEffectiveTemperature() {
        return lowestEffectiveTemperature;
    }

    public void setLowestEffectiveTemperature(BigDecimal lowestEffectiveTemperature) {
        this.lowestEffectiveTemperature = lowestEffectiveTemperature;
    }

    public BigDecimal getAveEffectiveTemperature() {
        return aveEffectiveTemperature;
    }

    public void setAveEffectiveTemperature(BigDecimal aveEffectiveTemperature) {
        this.aveEffectiveTemperature = aveEffectiveTemperature;
    }

    public BigDecimal getMaxColdness() {
        return maxColdness;
    }

    public void setMaxColdness(BigDecimal maxColdness) {
        this.maxColdness = maxColdness;
    }

    public BigDecimal getMinColdness() {
        return minColdness;
    }

    public void setMinColdness(BigDecimal minColdness) {
        this.minColdness = minColdness;
    }

    public BigDecimal getAveColdness() {
        return aveColdness;
    }

    public void setAveColdness(BigDecimal aveColdness) {
        this.aveColdness = aveColdness;
    }

    public BigDecimal getMaxTemperatureHumidity() {
        return maxTemperatureHumidity;
    }

    public void setMaxTemperatureHumidity(BigDecimal maxTemperatureHumidity) {
        this.maxTemperatureHumidity = maxTemperatureHumidity;
    }

    public BigDecimal getMinTemperatureHumidity() {
        return minTemperatureHumidity;
    }

    public void setMinTemperatureHumidity(BigDecimal minTemperatureHumidity) {
        this.minTemperatureHumidity = minTemperatureHumidity;
    }

    public BigDecimal getAveTemperatureHumidity() {
        return aveTemperatureHumidity;
    }

    public void setAveTemperatureHumidity(BigDecimal aveTemperatureHumidity) {
        this.aveTemperatureHumidity = aveTemperatureHumidity;
    }

    public BigDecimal getRainfall() {
        return rainfall;
    }

    public void setRainfall(BigDecimal rainfall) {
        this.rainfall = rainfall;
    }

    public Timestamp getCreatetime() {
        return createtime;
    }

    public void setCreatetime(Timestamp createtime) {
        this.createtime = createtime;
    }

    public Timestamp getUpdatetime() {
        return updatetime;
    }

    public void setUpdatetime(Timestamp updatetime) {
        this.updatetime = updatetime;
    }
}
