package com.tsintergy.lf.serviceapi.base.forecast.dto;

import com.tsieframework.core.base.domain.dto.DTO;
import java.util.List;

import com.tsintergy.lf.serviceapi.base.evalucation.dto.AccuracyAssessDTO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.AccuracyAssessDO;
import lombok.Data;

/**
 * Description: 十日负荷预测
 *
 * <AUTHOR>
 * @create 2023-03-06
 * @since 1.0.0
 */
@Data
public class TenDayForecastDTO implements DTO {

    /**
     * 预测类型
     */
    private String name;

    /**
     * 负荷预测特征值
     */
    private List<TenDayFeatureValuesDTO> loadDataList;

    /**
     * 气象预测特征值
     */
    private List<TenDayWeatherFeatureDTO> weatherDataList;

    private List<AccuracyAssessDTO> accuracyAssessList;

}
