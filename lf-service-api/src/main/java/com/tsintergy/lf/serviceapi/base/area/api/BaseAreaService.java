/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.area.api;

import com.tsintergy.lf.serviceapi.base.area.dto.AreaDTO;
import com.tsintergy.lf.serviceapi.base.area.dto.CityAndAreaDTO;
import com.tsintergy.lf.serviceapi.base.area.pojo.BaseAreaDO;
import java.util.List;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/8/4 9:36
 * @Version: 1.0.0
 */
public interface BaseAreaService {
    public List<BaseAreaDO> findDOBYPlan(String planId) throws Exception;
    public CityAndAreaDTO findCityAndAreaDTO(String planId) throws Exception;

    List<String> getCityIdsByAreaId(String areaId);

    List<String> doSaveOrUpdate(List<AreaDTO> areaDTOS);
    void doDelete(List<String> areaList);
}
