package com.tsintergy.lf.serviceapi.base.weather.pojo;

import static com.tsieframework.core.base.dao.Entity.DEFAULT_GENERATOR;
import static com.tsieframework.core.base.dao.Entity.DEFAULT_STRATEGY;

import com.tsieframework.core.base.dao.BaseVO;
import java.math.BigDecimal;
import java.sql.Timestamp;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.UpdateTimestamp;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2023-06-16
 * @since 1.0.0
 */
@Data
@Entity
@Table(name = "weather_feature_city_year_his_service")
public class WeatherFeatureCityYearHisDO extends BaseVO {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;

    /**
     * 年
     */
    @Column(name = "year")
    private String year;

    /**
     * 月
     */
    @Column(name = "month")
    private String month;

    /**
     * 城市ID
     */
    @Column(name = "city_id")
    private String cityId;

    /**
     * 最高温度
     */
    @Column(name = "highest_temperature")
    private BigDecimal highestTemperature;

    @Column(name = "extreme_highest_temperature")
    private BigDecimal extremeHighestTemperature;

    /**
     * 最低温度
     */
    @Column(name = "lowest_temperature")
    private BigDecimal lowestTemperature;

    @Column(name = "extreme_lowest_temperature")
    private BigDecimal extremeLowestTemperature;

    /**
     * 平均温度
     */
    @Column(name = "ave_temperature")
    private BigDecimal aveTemperature;

    @Column(name = "extreme_ave_temperature")
    private BigDecimal extremeAveTemperature;

    @Column(name = "max_tem")
    private BigDecimal maxTem;

    @Column(name = "min_tem")
    private BigDecimal minTem;

    @Column(name = "ave_tem")
    private BigDecimal aveTem;

    @Column(name = "extreme_max_tem")
    private BigDecimal extremeMaxTem;

    @Column(name = "extreme_min_tem")
    private BigDecimal extremeMinTem;

    @Column(name = "extreme_ave_tem")
    private BigDecimal extremeAveTem;

    /**
     * 创建时间
     */
    @Column(name = "createtime")
    @CreationTimestamp
    private Timestamp createtime;

    /**
     * 更新时间
     */
    @Column(name = "updatetime")
    @UpdateTimestamp
    private Timestamp updatetime;

    @Column(name = "flag")
    private Boolean flag;
}
