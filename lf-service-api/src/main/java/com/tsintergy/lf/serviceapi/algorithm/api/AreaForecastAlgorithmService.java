/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.algorithm.api;

import java.util.Date;
import java.util.List;

/**
 * Description:
 * 调用分区预测<br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/8/9 14:22
 * @Version: 1.0.0
 */
public interface AreaForecastAlgorithmService {


    /***
     * 功能描述:
     * 分区预测<br>
     * @param pointNum 新息点
     * @param algorithmIds 算法集
     * @param type 预测方式 1 T+1 2 T+2 实施页面传参
     * @param fcDayWeatherType 实施页面调用传参 待预测日气象参数 0使用历史气象 1 使用预测气象
     * @param startDate 预测开始日期
     * @param endDate 预测结束日期
     * @param forecastType 1 算法内部滚动预测 预测多天，只调用一次算法 2 程序循环一天一天调用 默认为 1
     * @Return:
     * @Version: 1.0.0
     * @Author:<EMAIL>
     * @Date: 2022/8/9 14:24
     */
    void doStatAreaForecast(String areaId, String caliberId,Integer pointNum, List<String> algorithmIds,
        Integer type, Integer fcDayWeatherType, Date startDate, Date endDate, Integer forecastType);
}