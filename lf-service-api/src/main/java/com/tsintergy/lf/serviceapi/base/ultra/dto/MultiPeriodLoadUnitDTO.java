/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司 Author:  ThinkPad Date:  2019/5/15 7:26 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.ultra.dto;


import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * 超短期日准确率对象 用于月准确率页面的日均准确率对比 -和季度的通用
 *
 * <AUTHOR>
 * @create 2023/1/11
 * @since 1.0.0
 */
@Data
public class MultiPeriodLoadUnitDTO {


    /**
     * 15分钟时： 1 2 4 8 16 20 对应 15分钟 30分钟 1小时 2 小时 4小时 5小时  0为实际负荷
     * 5分钟时：1 3 6 12 24 48 60 对应 5分钟 15分钟 30分钟 1小时 2 小时 4小时 5小时  0为实际负荷
     */
    private Integer type;

    /**
     * 负荷
     */
    @BigdecimalJsonFormat(scale = 0)
    private List<BigDecimal> value;



}