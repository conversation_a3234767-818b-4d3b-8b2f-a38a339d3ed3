/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.weather.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import org.springframework.util.CollectionUtils;

/**
 *Description: 省气象特性 <br>
 *
 *@Author: <EMAIL>
 *@Date: 2022/6/28 18:43
 *@Version: 1.0.0
 */
public class ProvinceWeatherFeatureDTO implements Serializable {

    private Date date;

    private List<BigDecimal> temperatureList;

    public ProvinceWeatherFeatureDTO(Date date, List<BigDecimal> temperatureList) {
        this.date = date;
        this.temperatureList = temperatureList;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public List<BigDecimal> getTemperatureList() {
        return temperatureList;
    }

    public void setTemperatureList(List<BigDecimal> temperatureList) {
        this.temperatureList = temperatureList;
    }

    public String getTemperatureValues() {
        return valueToString(this.temperatureList);
    }
    public String valueToString(List<BigDecimal> values) {
        StringBuilder stringBuilder = new StringBuilder();
        if (!CollectionUtils.isEmpty(values)) {
            for (BigDecimal bigDecimal : values) {
                stringBuilder.append(bigDecimal == null ? "null" : String.valueOf(bigDecimal)).append("  ");
            }
        }
        return stringBuilder.toString();
    }

}