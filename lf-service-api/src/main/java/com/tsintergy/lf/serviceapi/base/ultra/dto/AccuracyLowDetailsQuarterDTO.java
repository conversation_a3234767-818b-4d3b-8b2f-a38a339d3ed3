/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司 Author:  ThinkPad Date:  2019/5/15 7:26 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.ultra.dto;



import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 超短期月最低准确率详情对象 -用于季度页面展示数据
 *
 * <AUTHOR>
 * @create 2023/1/11
 * @since 1.0.0
 */
@Data
public class AccuracyLowDetailsQuarterDTO implements Serializable {


    /**
     * yyyy-mm（用于季度展示）
     */
    protected String dateTime;

    /**
     * 最大负荷（月）
     */
    protected BigDecimal maxLoad;

    /**
     * 最小负荷（月）
     */
    protected BigDecimal minLoad;

    /**
     * 最大负荷发生日期
     */
    protected String maxDate;

    /**
     * 最小负荷发生日期
     */
    protected String minDate;

    /**
     * 最大负荷时刻准确率
     */
    @BigdecimalJsonFormat(percentConvert = 100)
    protected BigDecimal maxLoadAccuracy;

    /**
     * 最小负荷时刻准确率
     */
    @BigdecimalJsonFormat(percentConvert = 100)
    protected BigDecimal minLoadAccuracy;


    /**
     * 最高准确率
     */
    @BigdecimalJsonFormat(percentConvert = 100)
    protected BigDecimal maxAccuracy;

    /**
     * 最低准确率
     */
    @BigdecimalJsonFormat(percentConvert = 100)
    protected BigDecimal minAccuracy;

    /**
     * 最低准确率发生日期
     */
    protected String minAccuracyDate;

    /**
     * 平均准确率
     */
    @BigdecimalJsonFormat(percentConvert = 100)
    protected BigDecimal avgAccuracy;

}