package com.tsintergy.lf.serviceapi.base.evalucation.pojo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.tsieframework.core.base.dao.BaseVO;
import com.tsieframework.core.base.vo.annotation.EntityUniqueIndex;
import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.UpdateTimestamp;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2023-11-30
 * @since 1.0.0
 */
@Data
@Entity
@Table(name = "STATISTICS_CITY_DAY_FC_FEATURE_SERVICE")
@TableName("STATISTICS_CITY_DAY_FC_FEATURE_SERVICE")
@EntityUniqueIndex({"date" , "cityId","caliberId" , "algorithmId"})
public class StatisticsCityDayFcFeatureDO extends BaseVO {


    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;

    @Column(name = "date")
    private Date date;

    @Column(name = "algorithm_id")
    private String algorithmId;

    /**
     * 城市
     */
    @Column(name = "city_id")
    protected String cityId;

    /**
     * 口径ID
     */
    @Column(name = "caliber_id")
    protected String caliberId;


    /**
     * 上旬 中旬 下旬
     */
//    @Column(name = "type")
//    private String type;

    /**
     * 最大负荷
     */
    @Column(name = "max_load_accuracy")
    @BigdecimalJsonFormat(scale = 2)
    protected BigDecimal maxLoadAccuracy;

    /**
     * 最小负荷
     */
    @Column(name = "min_load_accuracy")
    @BigdecimalJsonFormat(scale = 2)
    protected BigDecimal minLoadAccuracy;

    /**
     * 早峰负荷
     */
    @Column(name = "morning_peak_accuracy")
    @BigdecimalJsonFormat(scale = 2)
    protected BigDecimal morningPeakAccuracy;

    /**
     * 午间负荷
     */
    @Column(name = "noon_time_accuracy")
    @BigdecimalJsonFormat(scale = 2)
    protected BigDecimal noonTimeAccuracy;

    /**
     * 晚峰负荷
     */
    @Column(name = "evening_peak_accuracy")
    @BigdecimalJsonFormat(scale = 2)
    protected BigDecimal eveningPeakAccuracy;

    /**
     * 低谷负荷
     */
    @Column(name = "trough_max_accuracy")
    @BigdecimalJsonFormat(scale = 2)
    protected BigDecimal troughMaxAccuracy;

    /**
     * 日电量
     */
    @Column(name = "energy_accuracy")
    @BigdecimalJsonFormat(scale = 2)
    protected BigDecimal energyAccuracy;

    /**
     * 上报
     */
    @Column(name = "report")
    protected Boolean report;

    /**
     * 绑定算法开发参数设置方案id
     */
    @Column(name = "plan_id")
    private String planId;

    /**
     * 创建时间
     */
    @Column(name = "createtime")
    @CreationTimestamp
    protected Timestamp createtime;

    /**
     * 更新时间
     */
    @Column(name = "updatetime")
    @UpdateTimestamp
    protected Timestamp updatetime;

}
