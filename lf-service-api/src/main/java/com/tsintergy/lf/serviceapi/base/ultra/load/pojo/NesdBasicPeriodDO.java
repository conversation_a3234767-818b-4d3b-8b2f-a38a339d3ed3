package com.tsintergy.lf.serviceapi.base.ultra.load.pojo;

import com.tsieframework.core.base.dao.type.DataPointListMeta;
import com.tsieframework.core.base.dao.type.TsieCustomType;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import javax.persistence.Column;
import javax.persistence.MappedSuperclass;
import lombok.Data;
import org.hibernate.annotations.Columns;
import org.hibernate.annotations.Type;

/**
 * Description:  <br>
 *
 * <AUTHOR>
 * @create 2021/11/18
 * @since 1.0.0
 */
@Data
@MappedSuperclass
public class NesdBasicPeriodDO extends NesdBasicDO {

    /**
     * 数据点配置
     */
    @ApiModelProperty
    @Type(type = TsieCustomType.CustomCollectionType.DataPointListMetaType)
    @Columns(columns = {
            @Column(name = "t_start_time"),
            @Column(name = "t_size"),
            @Column(name = "t_delta"),
            @Column(name = "t_delta_unit")
    })
    protected DataPointListMeta tvMeta;

    /***
     * @Description 数据点数据
     * @Date 2023/1/10 16:44
     * <AUTHOR>
     **/
    @ApiModelProperty(value = "数据")
    @Column(name = "TV_DATA")
    @Type(type = TsieCustomType.CustomCollectionType.BigDecimalListType)
    protected List<BigDecimal> tvData;
}
