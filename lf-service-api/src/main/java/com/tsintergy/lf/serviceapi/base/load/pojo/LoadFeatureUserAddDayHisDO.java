package com.tsintergy.lf.serviceapi.base.load.pojo;


import com.tsintergy.lf.serviceapi.base.base.pojo.BaseLoadFeatureCityDayDO;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;

/**
 * 重点用户日负荷特性
 */
@Data
@Entity
@Table(name = "load_feature_user_add_day_his_service")
public class LoadFeatureUserAddDayHisDO extends BaseLoadFeatureCityDayDO {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;
}
