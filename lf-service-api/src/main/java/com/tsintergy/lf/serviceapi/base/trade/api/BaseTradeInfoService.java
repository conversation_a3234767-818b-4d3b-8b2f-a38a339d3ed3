/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.trade.api;

import com.tsintergy.lf.serviceapi.base.trade.pojo.BaseTradeInfoDO;
import java.util.Date;
import java.util.List;

/**
 * Description:  <br>
 * 行业统计负荷
 * @Author: <EMAIL>
 * @Date: 2022/9/2 19:37
 * @Version: 1.0.0
 */
public interface BaseTradeInfoService {


    void save(List<BaseTradeInfoDO> list);


    List<BaseTradeInfoDO> findBaseTradeInfo(String name ,String code, String pCode,Integer level);
}
