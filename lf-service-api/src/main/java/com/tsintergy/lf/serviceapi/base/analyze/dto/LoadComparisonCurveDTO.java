/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.analyze.dto;

import com.tsieframework.core.base.domain.dto.DTO;
import java.util.List;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/8/22 15:56
 * @Version: 1.0.0
 */
public class LoadComparisonCurveDTO implements DTO {

    private String year;

    /**
     * 曲线类型 0 负荷 1 气象
     */
    private String type;

    private List<LoadDateValueDTO> list;


    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public List<LoadDateValueDTO> getList() {
        return list;
    }

    public void setList(List<LoadDateValueDTO> list) {
        this.list = list;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}