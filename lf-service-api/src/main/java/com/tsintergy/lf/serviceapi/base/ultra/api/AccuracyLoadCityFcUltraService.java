package com.tsintergy.lf.serviceapi.base.ultra.api;


import com.tsintergy.lf.serviceapi.base.ultra.pojo.AccuracyLoadCityFcUltraDO;
import java.util.Date;
import java.util.List;

/**
 * @Description
 * @Date 2023/1/31 16:10
 * <AUTHOR>
 **/
public interface AccuracyLoadCityFcUltraService {
    List<AccuracyLoadCityFcUltraDO> findShortMultipointFc(Date startDate, Date endDate, String cityId, String caliberId,
        Integer timeSpan, String algorithmId, Integer multipointType);

    void doSaveOrUpdateList(List<AccuracyLoadCityFcUltraDO> accuracyLoadCityFcDOS);

}
