/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.base.pojo;

import com.tsieframework.core.base.dao.BaseVO;
import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;
import javax.persistence.Column;
import javax.persistence.MappedSuperclass;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/8/2 17:49
 * @Version: 1.0.0
 */
@MappedSuperclass
public class BaseStatisticsCityDayFcDO extends BaseVO {



    /**
     * 城市ID
     */
    @Column(name = "city_id")
    private String cityId;

    /**
     * 日期
     */
    @Column(name = "date")
    private Date date;

    /**
     * 算法ID
     */
    @Column(name = "algorithm_id")
    private String algorithmId;

    /**
     * 口径ID
     */
    @Column(name = "caliber_id")
    private String caliberId;

    /**
     * 准确率
     */
    @Column(name = "accuracy")
    @BigdecimalJsonFormat(percentConvert = 100)
    private BigDecimal accuracy;

    /**
     * 偏差率
     */
    @Column(name = "deviation")
    private BigDecimal deviation;

    /**
     * 合格率
     */
    @Column(name = "pass")
    private BigDecimal pass;

    /**
     * 离散度
     */
    @Column(name = "dispersion")
    private BigDecimal dispersion;

    /**
     * 考核标准准确率
     */
    @Column(name = "standard_accuracy")
    private BigDecimal standardAccuracy;

    /**
     * 创建时间
     */
    @Column(name = "createtime")
    @CreationTimestamp
    private Timestamp createtime;

    /**
     * 更新时间
     */
    @Column(name = "updatetime")
    @UpdateTimestamp
    private Timestamp updatetime;

    /**
     * 是否上报
     */
    @Column(name = "report")
    private Boolean report;



    public String getCityId() {
        return cityId;
    }

    public void setCityId(String cityId) {
        this.cityId = cityId;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public String getAlgorithmId() {
        return algorithmId;
    }

    public void setAlgorithmId(String algorithmId) {
        this.algorithmId = algorithmId;
    }

    public String getCaliberId() {
        return caliberId;
    }

    public void setCaliberId(String caliberId) {
        this.caliberId = caliberId;
    }

    public BigDecimal getAccuracy() {
        return accuracy;
    }

    public void setAccuracy(BigDecimal accuracy) {
        this.accuracy = accuracy;
    }

    public BigDecimal getDeviation() {
        return deviation;
    }

    public void setDeviation(BigDecimal deviation) {
        this.deviation = deviation;
    }

    public BigDecimal getPass() {
        return pass;
    }

    public void setPass(BigDecimal pass) {
        this.pass = pass;
    }

    public BigDecimal getDispersion() {
        return dispersion;
    }

    public void setDispersion(BigDecimal dispersion) {
        this.dispersion = dispersion;
    }

    public BigDecimal getStandardAccuracy() {
        return standardAccuracy;
    }

    public void setStandardAccuracy(BigDecimal standardAccuracy) {
        this.standardAccuracy = standardAccuracy;
    }

    public Timestamp getCreatetime() {
        return createtime;
    }

    public void setCreatetime(Timestamp createtime) {
        this.createtime = createtime;
    }

    public Timestamp getUpdatetime() {
        return updatetime;
    }

    public void setUpdatetime(Timestamp updatetime) {
        this.updatetime = updatetime;
    }

    public Boolean getReport() {
        return report;
    }

    public void setReport(Boolean report) {
        this.report = report;
    }
}