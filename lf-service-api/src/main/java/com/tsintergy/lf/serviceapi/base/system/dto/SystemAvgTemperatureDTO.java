/**
 * Copyright (C), 2015‐2022, 北京清能互联科技有限公司 Author:  <EMAIL> Date:  2022/6/9 14:52 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.system.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Description: 累计平均温度设置 <br>
 * <AUTHOR>
 */
@Data
@ApiModel
public class SystemAvgTemperatureDTO {

    /**
     * 0代表D日  1代表D-1日  2代表D-2日 ...
     */
    @ApiModelProperty(value = "日期")
    private String[] date;

    @ApiModelProperty(value = "权重")
    private String[] value;
}  
