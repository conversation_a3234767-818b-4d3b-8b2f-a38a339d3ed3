package com.tsintergy.lf.serviceapi.base.forecast.dto;

import com.tsieframework.core.base.domain.dto.DTO;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * Description: 十日负荷预测曲线
 *
 * <AUTHOR>
 * @create 2023-03-06
 * @since 1.0.0
 */
@Data
public class TenDayForecastCurveDTO implements DTO {

    /**
     * 负荷预测曲线
     */
    private TenDayCurveValuesDTO loadDataList;

    /**
     * 气象预测曲线
     */
    private List<TenDayWeatherCurveValuesDTO> weatherDataList;

}
