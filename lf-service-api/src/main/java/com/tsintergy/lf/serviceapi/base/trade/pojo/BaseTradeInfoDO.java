/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.trade.pojo;

import com.tsieframework.core.base.dao.BaseDO;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

/**
 * Description: 行业信息 <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/9/2 19:16
 * @Version: 1.0.0
 */
@Data
@Entity
@Table(name = "base_trade_info")
public class BaseTradeInfoDO implements BaseDO {


    @Id
    @Column(name = "code")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String code;

    @Column(name = "name")
    private String name;

    @Column(name = "parent_code")
    private String parent_code;

    @Column(name = "level")
    private Integer level;

    @Column(name = "order_no")
    private Integer orderNo;

    @Column(name = "valide")
    private Integer valide;
}