/**
 *   
 *  Copyright (C), 2015‐2020, 北京清能互联科技有限公司  
 *  Author:  EDZ  
 *  Date:  2020/10/12 13:37  
 *  History:  
 *  <author> <time> <version> <desc> 
 */
package com.tsintergy.lf.serviceapi.base.weather.pojo;

import com.tsieframework.core.base.vo.annotation.EntityUniqueIndex;
import com.tsieframework.core.base.vo.util.BasePeriodUtils;

import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;
import java.util.List;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

/**
 *   
 *  Description: 负荷预测使用的气象数据信息 <br> 
 *  
 *  <AUTHOR> 
 *  @create 2020/10/12  
 *  @since 1.0.0  
 */
@Data
@Entity
@Table(name = "weather_city_fc_load_forecast")
@EntityUniqueIndex({"date", "cityId", "caliberId", "algorithmId", "batchId", "type"})
public class WeatherCityFcLoadForecastDO extends BaseWeatherDO {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;
    /**
     * 日期
     */
    @Column(name = "date")
    private Date date;
    /**
     * 城市ID
     */
    @Column(name = "city_id")
    private String cityId;
    /**
     * 算法id
     */
    @Column(name = "algorithm_id")
    private String algorithmId;
    /**
     * 口径id
     */
    @Column(name = "caliber_id")
    private String caliberId;

    /**
     * 城市ID
     */
    /*@Column(name = "load_id")
    private String loadId;*/
    /**
     * 气象类型
     */
    @Column(name = "type")
    private Integer type;
    /**
     * 气象类型
     */
    @Column(name = "batch_id")
    private Integer batchId;
    /**
     * 创建时间
     */
    @Column(name = "createtime")
    private Timestamp createtime;
    /**
     * 更新时间
     */
    @Column(name = "updatetime")
    private Timestamp updatetime;

    public WeatherCityFcLoadForecastDO() {
        super();
    }

    public WeatherCityFcLoadForecastDO(String cityId, String algorithmId, Integer type, java.util.Date date, String caliberId) {
        this.cityId = cityId;
        this.caliberId = caliberId;
        this.algorithmId = algorithmId;
        this.type = type;
        this.date = new Date(date.getTime());
    }

    public List<BigDecimal> getWeatherList() {
        return BasePeriodUtils.toList(this, 96, false);
    }
}
