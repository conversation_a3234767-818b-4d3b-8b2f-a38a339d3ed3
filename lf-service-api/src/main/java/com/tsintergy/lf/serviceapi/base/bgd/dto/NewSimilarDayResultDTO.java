package com.tsintergy.lf.serviceapi.base.bgd.dto;

import com.tsieframework.core.base.domain.dto.DTO;
import java.util.List;
import lombok.Data;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2023-05-06
 * @since 1.0.0
 */
@Data
public class NewSimilarDayResultDTO implements DTO {

    /**
     * 相似日负荷曲线对比 预测
     */
    private List<DateValuesDTO> loadList;

    /**
     * 气温曲线对比
     */
    private List<NewDateValuesDTO> weatherList;

    /**
     * 湿度曲线对比
     */
    private List<NewDateValuesDTO> humidityList;

    /**
     * 降雨量曲线对比
     */
    private List<NewDateValuesDTO> rainfallList;

    /**
     * 风速曲线对比
     */
    private List<NewDateValuesDTO> windList;

    /**
     * 体感温度曲线对比
     */
    private List<NewDateValuesDTO> temperatureList;

    /**
     * 特性统计
     */
    private List<SimilarDayFeatureDTO> featureList;

    /**
     * 特性统计
     */
    private List<SimilarDayFeatureDTO> featureFcList;

    /**
     * 相似度列表 历史
     */
    private List<SimilarDayListDTO> similarDayList;

    /**
     * 相似度列表 预测
     */
    private List<SimilarDayListDTO> similarDayFcList;

    /**
     * 相似日负荷曲线对比 预测
     */
    private List<DateValuesDTO> loadListFc;

    private List<NewDateValuesDTO> weatherListFc;

}
