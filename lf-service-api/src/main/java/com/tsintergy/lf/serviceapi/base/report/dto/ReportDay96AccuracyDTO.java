package com.tsintergy.lf.serviceapi.base.report.dto;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;

@Data
public class ReportDay96AccuracyDTO {
    @ApiModelProperty("城市名称")
    String cityName;
    @ApiModelProperty("平均准确率")
    BigDecimal avgAccuracy;
    @ApiModelProperty("准确率数据列表")
    List<BigDecimal> accuracyList=new ArrayList<>();
    @ApiModelProperty("准确率下标")
    List<Integer> index=new ArrayList<>();
}