package com.tsintergy.lf.serviceapi.base.evalucation.api;

import com.tsintergy.lf.serviceapi.base.evalucation.pojo.AccuracyAssessDO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.AccuracyCompositeDO;
import com.tsintergy.lf.serviceapi.base.evalucation.pojo.StatisticsCityDayFcBatchDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcLoadForecastDO;

import java.util.List;

public interface BatchDataFilterService {

    /**
     * 根据批次+日前n天过滤考核点准确率
     *
     * @param assessDOS 考核点准确率列表
     * @param cityId 城市id
     * @param batchId 批次id
     * @param day       日前n天
     */
    List<AccuracyAssessDO> filterAssessByBatchId(List<AccuracyAssessDO> assessDOS, String cityId, String batchId, Integer day);

    /**
     * 根据批次+日前n天过滤综合准确率
     * @param compositeDOS 综合准确率列表
     * @param cityId 城市id
     * @param batchId 批次id
     * @param day       日前n天
     */
    List<AccuracyCompositeDO> filterCompositeByBatchId(List<AccuracyCompositeDO> compositeDOS, String cityId, String batchId, Integer day);


    /**
     * 根据批次+日前n天过滤城市批次日分析数据
     * @param statisticsDOS 城市批次日分析列表
     * @param cityId 城市id
     * @param batchId 批次id
     * @param day       日前n天
     */
    List<StatisticsCityDayFcBatchDO> filterStatisticsByBatchId(List<StatisticsCityDayFcBatchDO> statisticsDOS, String cityId, String batchId, Integer day);


    /**
     * 根据批次+日前n天过滤城市预测气象批次列表
     * @param weatherForecastDOS 预测气象批次列表
     * @param cityId 城市id
     * @param batchId 批次id
     * @param day       日前n天
     */
    List<WeatherCityFcLoadForecastDO> filterWeatherForecastDOByBatchId(List<WeatherCityFcLoadForecastDO> weatherForecastDOS, String cityId, String batchId, Integer day);
}
