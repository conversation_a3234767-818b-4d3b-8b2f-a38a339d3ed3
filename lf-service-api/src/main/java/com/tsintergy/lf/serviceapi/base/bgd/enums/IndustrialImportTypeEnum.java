package com.tsintergy.lf.serviceapi.base.bgd.enums;

/**
 * Description: 重点行业 <br>
 *
 * <AUTHOR>
 * @create 2023-02-28
 * @since 1.0.0
 */
public enum IndustrialImportTypeEnum {

    FIRST("0","高耗能",1),
    SECOND("1","电动汽车",2),
    THIRD("2","商业楼宇",3),
    FOURTH("3","景观照明",4);

    /**
     * 产业类型
     */
    private String industryId;

    /**
     * 产业名称
     */
    private String IndustryName;

    /**
     * 排序用
     */
    private Integer sort;

    IndustrialImportTypeEnum(String industryId, String industryName, Integer sort) {
        this.industryId = industryId;
        IndustryName = industryName;
        this.sort = sort;
    }

    public String getIndustryId() {
        return industryId;
    }

    public void setIndustryId(String industryId) {
        this.industryId = industryId;
    }

    public String getIndustryName() {
        return IndustryName;
    }

    public void setIndustryName(String industryName) {
        IndustryName = industryName;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }
}
