package com.tsintergy.lf.serviceapi.base.forecast.api;

import com.tsintergy.lf.serviceapi.base.forecast.dto.TenDayForecastCurveDTO;
import com.tsintergy.lf.serviceapi.base.forecast.dto.TenDayForecastDTO;
import com.tsintergy.lf.serviceapi.base.forecast.pojo.LoadCityFcDO;
import java.util.Date;
import java.util.List;

/**
 * Description: TODO <br>
 *
 * <AUTHOR>
 * @create 2023-03-06
 * @since 1.0.0
 */
public interface TenDaysLoadForecastService {

    List<TenDayForecastDTO> getListLoadCityFc(String cityId, String caliberId, Date startDate, Date endDate, String algorithmId) throws Exception;

    TenDayForecastCurveDTO getListLoadCityFcCurve(String cityId, String caliberId, Date startDate, Date endDate, String algorithmId) throws Exception;

}
