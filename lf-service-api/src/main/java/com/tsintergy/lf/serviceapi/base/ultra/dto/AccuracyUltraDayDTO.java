/**
 * Copyright (C), 2015‐2019, 北京清能互联科技有限公司 Author:  ThinkPad Date:  2019/5/15 7:26 History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.ultra.dto;

import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Date;
import lombok.Data;

/**
 * 超短期日准确率结果对象
 *
 * <AUTHOR>
 * @create 2023/1/11
 * @since 1.0.0
 */
@Data
public class AccuracyUltraDayDTO implements Serializable {


    /**
     * 日期
     */
    protected Date dateTime;

    /**
     * 城市
     */
    protected String cityId;

    /**
     * 口径ID
     */
    protected String caliberId;


    private String algorithmId;

    /**
     * 时间间隔 5五分钟 15 十五分钟
     */
    private Integer type;

    /**
     * 最大负荷
     */
    @BigdecimalJsonFormat(scale = 0)
    protected BigDecimal maxLoad;

    /**
     * 最小负荷
     */
    @BigdecimalJsonFormat(scale = 0)
    protected BigDecimal minLoad;

    /**
     * 平均负荷
     */
    @BigdecimalJsonFormat(scale = 0)
    protected BigDecimal avgLoad;

    /**
     * 最大负荷发生时刻
     */
    protected String maxTime;

    /**
     * 最小负荷发生时刻
     */
    protected String minTime;

    /**
     * 最大负荷时刻准确率
     */
    @BigdecimalJsonFormat(percentConvert = 100)
    protected BigDecimal maxLoadAccuracy;

    /**
     * 最小负荷时刻准确率
     */
    @BigdecimalJsonFormat(percentConvert = 100)
    protected BigDecimal minLoadAccuracy;


    /**
     * 最高准确率
     */
    @BigdecimalJsonFormat(percentConvert = 100)
    protected BigDecimal maxAccuracy;

    /**
     * 最低准确率
     */
    @BigdecimalJsonFormat(percentConvert = 100)
    protected BigDecimal minAccuracy;

    /**
     * 最低准确率发生时间
     */
    protected String minAccuracyTime;

    /**
     * 平均准确率
     */
    @BigdecimalJsonFormat(percentConvert = 100)
    protected BigDecimal avgAccuracy;
}