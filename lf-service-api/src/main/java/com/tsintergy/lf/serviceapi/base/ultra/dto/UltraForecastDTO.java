package com.tsintergy.lf.serviceapi.base.ultra.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @date: 2/27/18 5:33 PM
 * @author: angel
 **/
public class UltraForecastDTO implements Serializable {

    private String algorithmId;

    public String id;
    /**
     * 口径id
     */
    public String caliberId;

    /**
     * 96点预测数据
     */
    List<BigDecimal> list;

    public String getCaliberId() {
        return caliberId;
    }

    public void setCaliberId(String caliberId) {
        this.caliberId = caliberId;
    }

    public String getAlgorithmId() {
        return algorithmId;
    }

    public void setAlgorithmId(String algorithmId) {
        this.algorithmId = algorithmId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public List<BigDecimal> getList() {
        return list;
    }

    public void setList(List<BigDecimal> list) {
        this.list = list;
    }
}
