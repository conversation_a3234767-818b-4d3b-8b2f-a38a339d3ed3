/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.trade.api;

import com.tsintergy.lf.serviceapi.base.trade.pojo.TradeLoadHisClctDO;
import java.util.Date;
import java.util.List;

/**
 * Description:  <br>
 * 基础负荷
 * @Author: <EMAIL>
 * @Date: 2022/8/29 15:18
 * @Version: 1.0.0
 */
public interface TradeLoadHisBasicService {

   void save(List<TradeLoadHisClctDO> list);


   List<TradeLoadHisClctDO> findTradeLoadHisBasicDO(String tradeCode,Date startDate, Date endDate);
}
