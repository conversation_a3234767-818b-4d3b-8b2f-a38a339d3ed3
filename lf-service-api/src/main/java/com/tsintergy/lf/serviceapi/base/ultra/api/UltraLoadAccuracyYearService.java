
package com.tsintergy.lf.serviceapi.base.ultra.api;

import com.tsintergy.lf.serviceapi.base.ultra.dto.AccuracyDayUnitDTO;
import com.tsintergy.lf.serviceapi.base.ultra.dto.AccuracyLowDetailsMonthDTO;
import com.tsintergy.lf.serviceapi.base.ultra.dto.AccuracyMonthDTO;
import com.tsintergy.lf.serviceapi.base.ultra.dto.AccuracyMonthUltraData;
import com.tsintergy.lf.serviceapi.base.ultra.dto.AccuracyYearUltraData;
import java.util.List;

/**
 * 年度月准确率评估
 *
 * <AUTHOR>
 */
public interface UltraLoadAccuracyYearService {

    /**
     * 月准确查询列表
     *
     * @param data 查询参数
     * @return 结果对象
     */
    List<AccuracyMonthDTO> getLoadHistory(AccuracyYearUltraData data) throws Exception;

    /**
     * 查询日均准确率列表
     * @param data 查询参数
     * @return 完整的一个月的数据
     */
    List<AccuracyDayUnitDTO> getTimeAccuracy(AccuracyMonthUltraData data);

    /**
     * 查询最低准确率详情
     * @param data 查询参数
     * @return 结果对象
     */
    AccuracyLowDetailsMonthDTO getAccuracyLowDetails(AccuracyMonthUltraData data) throws Exception;


}