/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.analyze.dto;

import com.tsieframework.core.base.domain.dto.DTO;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/8/22 16:04
 * @Version: 1.0.0
 */
public class LoadDateValueDTO implements DTO {

    private Date date;

    private BigDecimal value;


    public LoadDateValueDTO(Date date, BigDecimal value) {
        this.date = date;
        this.value = value;
    }

    public LoadDateValueDTO() {
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public BigDecimal getValue() {
        return value;
    }

    public void setValue(BigDecimal value) {
        this.value = value;
    }
}