package com.tsintergy.lf.serviceapi.base.weather.pojo;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import lombok.Data;

/**
 * 气象预测数据
 */
@Data
@Entity
@Table(name = "weather_city_fc_basic_copy1")
public class WeatherCityFcCopy1DO extends BaseWeatherDO {


    /**
     * 气象发布时间
     */
    @Column(name = "order_id")
    private String orderId;

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public WeatherCityFcCopy1DO() {
        super();
    }

    public WeatherCityFcCopy1DO(String cityId, Integer type, java.util.Date date) {
        super(cityId, type, date);
    }
}
