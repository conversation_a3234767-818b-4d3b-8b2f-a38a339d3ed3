package com.tsintergy.lf.serviceapi.base.trade.dto;

import com.tsieframework.core.base.domain.dto.DTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class IndustryLoadMaxFeatureDTO implements DTO {

    @ApiModelProperty(value = "最大负荷发生时刻")
    private String date;

    @ApiModelProperty(value = "行业代码")
    private String tradeCode;

    @ApiModelProperty(value = "行业名称")
    private String tradeName;

    @ApiModelProperty(value = "行业编号")
    private Integer orderNo;

    @ApiModelProperty(value = "行业负荷")
    private BigDecimal load;

    @ApiModelProperty(value = "负荷占比")
    private BigDecimal loadRate;

    @ApiModelProperty(value = "同比负荷")
    private BigDecimal tongbiLoad;

    @ApiModelProperty(value = "同比负荷增长率")
    private BigDecimal tongbiGrowthRate;

    @ApiModelProperty(value = "全行业同比负荷增长率")
    private BigDecimal allTradeTongbiGrowthRate;

    @ApiModelProperty(value = "同比日期")
    private Date tongbiDate;

    @ApiModelProperty(value = "同比时间")
    private String tongbiMaxTime;

    @ApiModelProperty(value = "环比负荷")
    private BigDecimal huanbiLoad;

    @ApiModelProperty(value = "环比负荷增长率")
    private BigDecimal huanbiGrowthRate;

    @ApiModelProperty(value = "全行业环比负荷增长率")
    private BigDecimal allTradeHuanbiGrowthRate;

    @ApiModelProperty(value = "环比负荷变化量")
    private BigDecimal huanbiChangeValue;

    @ApiModelProperty(value = "环比负荷变化贡献率")
    private BigDecimal huanbiChangeContributionRate;

    @ApiModelProperty(value = "环比日期")
    private Date huanbiDate;

    @ApiModelProperty(value = "环比时间")
    private String huanbiMaxTime;

    @ApiModelProperty(value = "子行业负荷特性")
    private List<IndustryLoadMaxFeatureDTO> subIndustryFeatureList;

}
