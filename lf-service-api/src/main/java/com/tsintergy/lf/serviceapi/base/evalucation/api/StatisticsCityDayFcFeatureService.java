package com.tsintergy.lf.serviceapi.base.evalucation.api;

import com.tsintergy.lf.serviceapi.base.evalucation.pojo.StatisticsCityDayFcFeatureDO;
import java.util.Date;
import java.util.List;

/**
 * Description: TODO <br>
 *
 * <AUTHOR>
 * @create 2023-11-30
 * @since 1.0.0
 */
public interface StatisticsCityDayFcFeatureService {

    List<StatisticsCityDayFcFeatureDO> getStatisticsCityDayFcFeatureDOReportList(String cityId, String caliberId,
        Date startDate, Date endDate) throws Exception;

    List<StatisticsCityDayFcFeatureDO> getStatisticsCityDayFcFeatureDOReportList(String cityId, String caliberId,
        Date startDate, Date endDate, String algorithmId) throws Exception;
}
