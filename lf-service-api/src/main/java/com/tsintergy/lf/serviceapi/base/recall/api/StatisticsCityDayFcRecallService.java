/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.recall.api;

import com.tsintergy.lf.serviceapi.base.recall.dto.StatisticsAccuracyDTO;
import com.tsintergy.lf.serviceapi.base.recall.pojo.StatisticsCityDayFcRecallDO;

import java.util.Date;
import java.util.List;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/8/1 14:22
 * @Version: 1.0.0
 */
public interface StatisticsCityDayFcRecallService {

    StatisticsAccuracyDTO getStatisticsAccuracyDTO(String cityId, String caliberId, Date startDate, Date endDate, String algorithmId);

    List<StatisticsCityDayFcRecallDO> queryStatisticsCity(String cityId, String algorithmId, String caliberId, Date date,
                                                          Boolean report) throws Exception;

    List<StatisticsCityDayFcRecallDO> doSaveOrUpdateBatch(
            List<StatisticsCityDayFcRecallDO> statisticsCityDayFcVOS) throws Exception;

    List<StatisticsCityDayFcRecallDO> getStatisticsCityDayFcRecallDOList(String cityId, String caliberId, Date startDate, Date endDate, String algorithmId) throws Exception;

}

