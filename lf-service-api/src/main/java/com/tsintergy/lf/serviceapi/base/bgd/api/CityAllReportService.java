package com.tsintergy.lf.serviceapi.base.bgd.api;

import com.tsieframework.core.base.service.FacadeService;
import com.tsintergy.lf.serviceapi.base.bgd.dto.ShortReportResultDTO;
import java.util.Date;
import java.util.List;

/**
 * Description: TODO <br>
 *
 * <AUTHOR>
 * @create 2023-06-30
 * @since 1.0.0
 */
public interface CityAllReportService extends FacadeService {

    /**
     * 短期上报汇总
     *
     * @param startStr 开始str
     * @param endStr str结束
     * @param typeName 类型名称
     * @param caliberName 口径名字
     * @param featureType 功能类型
     * @return {@code ShortReportResultDTO}
     * @throws Exception 异常
     */
    List<ShortReportResultDTO> getShortReport(String startStr, String endStr, String typeName,
        String caliberName, Integer featureType, String loadType) throws Exception;

    /**
     * @param startStr 开始str
     * @param type 类型
     * @param featureType 功能类型
     * @return {@code List<ShortReportResultDTO>}
     * @throws Exception 异常
     */
    List<ShortReportResultDTO> getMonthReport(String startStr, String type, Integer featureType, String loadType) throws Exception;

    List<ShortReportResultDTO> getShortWeather(String startStr, String endStr) throws Exception;

    List<ShortReportResultDTO> getSeasonReport(Date startStr, Integer type, Date endStr, Integer featureType,
        String loadType) throws Exception;

    List<ShortReportResultDTO> getYearReport(String startStr, String loadType, Integer featureType, String dataType)
        throws Exception;
}
