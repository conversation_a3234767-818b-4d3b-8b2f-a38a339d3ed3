/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.bgd.api;

import com.tsieframework.core.base.service.FacadeService;
import com.tsintergy.lf.serviceapi.base.bgd.dto.FeatureDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.MonthModelForecastDTO;

import com.tsintergy.lf.serviceapi.base.bgd.dto.MonthReportDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.MonthTypicalCurveDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.MonthWeatherDefaultDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.MonthWeatherResultDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.MonthWeatherSettingDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.TenDaysOfMonthAccuracyDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.TenDaysOfMonthFeatureDTO;
import java.util.Date;
import java.util.List;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/11/17 15:40
 * @Version: 1.0.0
 */
public interface MonthForecastModelService extends FacadeService {

    /***
     * 功能描述:
     * 获取月度预测
     * <br>
     * @param date
     * @param algorithmId  预测算法
     * @Author:<EMAIL>
     * @Date: 2022/11/17 16:03
     */
    List<TenDaysOfMonthFeatureDTO> getMonthLoadFc(String cityId, String caliberId, Date date, String algorithmId);

    /**
     * 月度典型曲线
     *
     * @param cityId 城市标识
     * @param caliberId 口径id
     * @param date 日期
     * @return {@code MonthTypicalCurveDTO}
     */
    MonthTypicalCurveDTO getMonthTypicalCurve(String cityId, String caliberId, Date date);

    /***
     * 功能描述:
     * 获取月度预测对比
     * <br>
     * @param date
     * @param algorithmId  预测算法
     * @Author:<EMAIL>
     * @Date: 2022/11/17 16:03
     */
    List<TenDaysOfMonthFeatureDTO> getMonthLoadFcCompare(String cityId, String caliberId, Date date, String algorithmId)
        throws Exception;

    /***
     * 功能描述:
     * 获取历史准确率
     * <br>
     * @param date
     * @param type  1 近一个月 2 近两个月
     * @Author:<EMAIL>
     * @Date: 2022/11/17 16:03
     */
    List<TenDaysOfMonthAccuracyDTO> getHistoryAccuracy(String cityId, String caliberId, Date date, Integer type) throws Exception;

    /**
     * 获取指定算法历史准确率-旬
     * @param cityId
     * @param date
     * @param type
     * @param algorithmId
     * @return
     */
    List<FeatureDTO> getTenDayAccuracy(String cityId,Date date, Integer type,String algorithmId,String caliberId);

    /**
     * 获取校核上报
     * @param cityId
     * @param date
     * @param caliberId
     * @param algorithmName
     * @return
     */
    List<FeatureDTO> getReportList(String cityId,String caliberId,String algorithmName,Date date);

    /**
     * 校核上报
     * @param monthReportDTOS
     * @return
     */
    void report(List<MonthReportDTO> monthReportDTOS);

    /***
     * 功能描述:获取最新上报时间<br>
     * @param cityId
     * @param caliberId
     * @param date
     * @Return: {@link java.lang.String}
     * @Version: 1.0.0
     * @Author:<EMAIL>
     * @Date: 2022/11/25 17:01
     */
    String getReportTime(String cityId, String caliberId, Date date);

    /***
     * 功能描述:获取最新预测时间<br>
     * @param cityId
     * @param caliberId
     * @param date
     * @Return: {@link java.lang.String}
     * @Version: 1.0.0
     * @Author:<EMAIL>
     * @Date: 2022/11/25 17:01
     */
    String getForecastTime(String cityId, String caliberId, Date date, String algorithmId);

    String getSeasonForecastTime(String cityId, String caliberId, Date date, String algorithmId, String season);

    /**
     * 获取天气设置
     *
     * @param monthWeatherSettingDTO 月天气设定dto
     * @return {@code List<MonthWeatherResultDTO>}
     * @throws Exception 异常
     */
    List<MonthWeatherResultDTO> getWeatherSetting(MonthWeatherSettingDTO monthWeatherSettingDTO) throws Exception;

    /**
     * 保存天气设置
     *
     * @param monthWeatherResultDTOS 月天气结果dto
     * @throws Exception 异常
     */
    void saveWeatherSetting(List<MonthWeatherResultDTO> monthWeatherResultDTOS) throws Exception;

    /**
     * 获取天气信息
     *
     * @param dateStr str日期
     * @param cityId 城市标识
     * @return {@code List<MonthWeatherResultDTO>}
     * @throws Exception 异常
     */
    List<MonthWeatherResultDTO> getWeatherInfo(String dateStr, String cityId) throws Exception;

    MonthWeatherDefaultDTO getMonthWeather(String cityId, String dateStr, String season) throws Exception;
}
