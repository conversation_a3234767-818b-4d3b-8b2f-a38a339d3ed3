/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.weather.api;

import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherStationHisBasicDO;
import java.util.Date;
import java.util.List;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/5/20 15:44
 * @Version: 1.0.0
 */
public interface WeatherStationHisBasicService {

    List<WeatherStationHisBasicDO> getWeatherStationHisBasicDO(String cityId,String stationId, Date startDate,Date endDate,Integer type);


    void doSaveOrUpdate(WeatherStationHisBasicDO weatherStationHisBasicDO);

    void doSaveOrUpdateList(List<WeatherStationHisBasicDO> result) throws Exception;

    void doSaveOrUpdateBatch(List<WeatherStationHisBasicDO> weatherStationHisBasicDOS);
}
