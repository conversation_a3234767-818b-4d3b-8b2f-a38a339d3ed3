package com.tsintergy.lf.serviceapi.algorithm.api;

import com.tsintergy.lf.serviceapi.algorithm.dto.Param;
import com.tsintergy.lf.serviceapi.algorithm.dto.Result;
import java.util.Map;

/**
 * Description: TODO <br>
 *
 * <AUTHOR>
 * @create 2023-05-05
 * @since 1.0.0
 */
public interface SimilarDayQueryService <E extends Param, T extends Result> {

    /**
     * 预测顶层接口 默认实现中规范了整体的执行流程；自定义流程需重写方法
     */
    void forecast(E param) throws Exception;

    /**
     * 该接口用于 查询并merge所需要的数据到map中，以供算法的Executor写入数据
     * @param param 数据参数
     * @param srcMap 数据集合map
     */
    void mergeData(E param, Map<String, Object> srcMap) throws Exception;
}
