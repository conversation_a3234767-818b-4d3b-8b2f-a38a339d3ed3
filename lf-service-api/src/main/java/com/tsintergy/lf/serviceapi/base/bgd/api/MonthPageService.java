package com.tsintergy.lf.serviceapi.base.bgd.api;

import com.tsieframework.core.base.service.FacadeService;
import com.tsintergy.lf.serviceapi.base.bgd.dto.TempCompareDTO;

import javax.xml.crypto.Data;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * 月维度页面接口
 */
public interface MonthPageService extends FacadeService {
    String getWeatherInfo(String cityId, Date date);

    List<TempCompareDTO> getTempCompareDTOList(String cityId, Date date,Integer type) throws Exception;
}
