package com.tsintergy.lf.serviceapi.base.weather.api;

import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcRpaCoverDO;
import java.util.List;

/**
 * Description: TODO <br>
 *
 * <AUTHOR>
 * @create 2024-12-04
 * @since 1.0.0
 */
public interface WeatherCityFcCoverRpaService {

    void doSaveOrUpdate(List<WeatherCityFcRpaCoverDO> weatherCityFcRpaCoverDOList);

    void doSave(List<WeatherCityFcRpaCoverDO> weatherCityFcRpaCoverDOList);
}
