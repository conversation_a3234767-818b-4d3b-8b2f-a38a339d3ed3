/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.weather.pojo;

import com.tsieframework.core.base.dao.BaseVO;
import java.sql.Timestamp;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.UpdateTimestamp;

/**
 * Description: 气象站 <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/5/31 10:36
 * @Version: 1.0.0
 */

@Data
@Entity
@Table(name = "weather_station_info")
public class WeatherStationInfoDO extends BaseVO {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;


    @Column(name = "city_id")
    private String cityId;


    @Column(name = "station_id")
    private String stationId;

    @Column(name = "area")
    private String area;
    /**
     * 创建时间
     */
    @Column(name = "createtime")
    @CreationTimestamp
    private Timestamp createtime;

    /**
     * 更新时间
     */
    @Column(name = "updatetime")
    @UpdateTimestamp
    private Timestamp updatetime;


}