/**
 * Copyright (C), 2015‐2018, 北京清能互联科技有限公司
 * Author: wang<PERSON>
 * Date: 2018/8/28 14:36
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.algorithm.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Description: 旬度气象特征 <br>
 *
 * <AUTHOR>
 * @create 2024/6/2
 * @since 1.0.0
 */
@Data
public class TendayWeatherFeatureDTO {
    /**
     * 日期
     */
    private Date date;
    /**
     * 最高温度
     */
    private BigDecimal highestTemperature;
    /**
     * 最低温度
     */
    private BigDecimal lowestTemperature;
    /**
     * 平均温度
     */
    private BigDecimal aveTemperature;
    /**
     * 最大风速
     */
    private BigDecimal highestWindSpe;
    /**
     * 最低风速
     */
    private BigDecimal lowestWindSpe;
    /**
     * 平均风速
     */
    private BigDecimal aveWindSpe;
    /**
     * 降雨
     */
    private BigDecimal rainfall;
    /**
     * 最大湿度
     */
    private BigDecimal highestHumidity;
    /**
     * 最低湿度
     */
    private BigDecimal lowestHumidity;
    /**
     * 平均湿度
     */
    private BigDecimal aveHumidity;

}