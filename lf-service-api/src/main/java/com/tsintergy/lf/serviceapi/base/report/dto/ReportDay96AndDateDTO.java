package com.tsintergy.lf.serviceapi.base.report.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import lombok.Data;

@Data
@ApiModel("日上报96点数据")
public class ReportDay96AndDateDTO {
    @ApiModelProperty("日期列表")
    List<Date> dateList;
    @ApiModelProperty("96点数据列表")
    List<ReportDay96AccuracyDTO> rowList;
}