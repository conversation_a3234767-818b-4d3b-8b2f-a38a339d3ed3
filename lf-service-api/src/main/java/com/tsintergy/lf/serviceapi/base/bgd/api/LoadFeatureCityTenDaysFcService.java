package com.tsintergy.lf.serviceapi.base.bgd.api;

import com.tsintergy.lf.serviceapi.base.bgd.pojo.LoadFeatureCityTenDaysFcServiceDO;
import com.tsintergy.lf.serviceapi.base.load.pojo.LoadFeatureCitySeasonFcDO;
import java.util.List;

/**
 * Description: TODO <br>
 *
 * <AUTHOR>
 * @create 2023-02-16
 * @since 1.0.0
 */
public interface LoadFeatureCityTenDaysFcService {

    List<LoadFeatureCityTenDaysFcServiceDO> findLoadFeatureCityTenDaysFcServiceDOs(String cityId,String caliberId,String algorithmId,String year,String month,String type);

    List<LoadFeatureCityTenDaysFcServiceDO> findLoadFeatureCityTenDaysFcServiceDOs(String cityId,String caliberId,String algorithmId,String year,List<String> monthList);

    List<LoadFeatureCityTenDaysFcServiceDO> getMonthReportVO(String cityId,String caliberId,String year,String month);

    List<LoadFeatureCityTenDaysFcServiceDO> getMonthReportVO(List<String> cityIds,String caliberId,String year,String month);

    List<LoadFeatureCityTenDaysFcServiceDO> getMonthForecastVO(String cityId,String caliberId,String year,String month,String algorithmId);

    List<LoadFeatureCitySeasonFcDO> getSeasonForecastVO(String cityId,String caliberId,String year,String month,String algorithmId,String season);

    void doSaveOrUpdate(LoadFeatureCityTenDaysFcServiceDO loadFeatureCityTenDaysFcServiceDO);

}
