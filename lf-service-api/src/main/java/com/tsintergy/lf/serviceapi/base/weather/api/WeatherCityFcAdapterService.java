package com.tsintergy.lf.serviceapi.base.weather.api;

import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherCityFcDO;
import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherFeatureCityDayFcDO;

import java.util.Date;
import java.util.List;

public interface WeatherCityFcAdapterService {

    List<WeatherCityFcDO> findFcWeather(String cityId, String algorithmId, Integer type, Date start, Date end) throws Exception;

    List<WeatherCityFcDO> findWeatherCityFcDOS(String sourceType, String cityId, Integer type, Date start, Date end) throws Exception;

    List<WeatherCityFcDO> findFcWeather(String sourceType, String cityId, Date start, Date endDate) throws Exception;

    List<WeatherFeatureCityDayFcDO> findWeatherFeatureCityDayFcDO(String sourceType, String cityId, Date startDate, Date endDate) throws Exception;

    List<WeatherFeatureCityDayFcDO> findWeatherFeatureByAlgorithmId(String algorithmId, String cityId, Date startDate, Date endDate) throws Exception;
}
