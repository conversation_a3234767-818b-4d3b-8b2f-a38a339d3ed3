package com.tsintergy.lf.serviceapi.base.load.dto;

import com.tsieframework.core.base.domain.dto.DTO;
import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;


@Data
public class LoadUserFeatureDTO implements DTO {

    @ApiModelProperty(value = "日期")
    private String date;


    @ApiModelProperty(value = "最大负荷")
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal maxLoad;

    @ApiModelProperty(value = "上周同类型日最大负荷")
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal lastMaxLoad;

    @ApiModelProperty(value = "最大负荷变化量")
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal maxLoadChange;

    @ApiModelProperty(value = "前7天均值")
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal lastAvgLoad;

    @ApiModelProperty(value = "前7天均值变化量")
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal avgLoadChange;

    @ApiModelProperty(value = "日电量")
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal energy;

    @ApiModelProperty(value = "上周同类型日电量")
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal lastEnergy;

    @ApiModelProperty(value = "日电量变化量")
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal energyChange;

    @ApiModelProperty(value = "日电量前7天均值")
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal lastAvgEnergy;

    @ApiModelProperty(value = "日电量前7天均值变化量")
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal avgEnergyChange;


    @ApiModelProperty(value = "最大负荷发生时刻")
    private String maxLoadTime;

    @ApiModelProperty(value = "上周同类型日最大负荷发生时刻")
    private String lastMaxLoadTime;

    @ApiModelProperty(value = "偏移点数")
    private Integer deviation;
}
