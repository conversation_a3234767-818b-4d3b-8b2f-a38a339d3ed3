package com.tsintergy.lf.serviceapi.base.typical.enums;

/**
 * Description: TODO <br>
 *
 * <AUTHOR>
 * @create 2023-09-18
 * @since 1.0.0
 */
public enum CurveLoadCompareEnum {

    WORKING_DAY("工作日", 1),
    SATURDAY("周六", 2),
    SUNDAY("周日", 3),
    REST_DAY("休息日", 4),
    MAX_LOAD_DAY("最大负荷日", 5),
    MIN_LOAD_DAY("最小负荷日", 6),
    MAX_ENERGY_DAY("最大电量日", 7),
    MAX_PEAK_DAY("最大峰谷差日", 8)
    ;

    /**
     * 典型曲线类型
     */
    private String typeName;

    /**
     * 排序用
     */
    private Integer sort;

    CurveLoadCompareEnum(String typeName, Integer sort) {
        this.typeName = typeName;
        this.sort = sort;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }
}
