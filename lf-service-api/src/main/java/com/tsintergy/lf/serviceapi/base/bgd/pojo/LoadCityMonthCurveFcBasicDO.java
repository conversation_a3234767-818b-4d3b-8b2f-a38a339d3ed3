package com.tsintergy.lf.serviceapi.base.bgd.pojo;

import com.tsintergy.lf.serviceapi.base.base.pojo.Base96DO;
import java.sql.Timestamp;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.UpdateTimestamp;

/**
 * Description: 月度典型曲线
 *
 * <AUTHOR>
 * @create 2023-03-09
 * @since 1.0.0
 */
@Data
@Entity
@Table(name = "load_city_month_curve_fc_basic")
public class LoadCityMonthCurveFcBasicDO extends Base96DO {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;

    @Column(name = "year")
    private String year;

    @Column(name = "month")
    private String month;

    /**
     * 城市
     */
    @Column(name = "city_id")
    protected String cityId;

    /**
     * 口径ID
     */
    @Column(name = "caliber_id")
    protected String caliberId;

    /**
     * 算法id
     */
    @Column(name = "algorithm_id")
    private String algorithmId;

    /**
     * 曲线类型
     */
    @Column(name = "type")
    private String type;

    @Column(name = "createtime")
    @CreationTimestamp
    private Timestamp createtime;

    @Column(name = "updatetime")
    @UpdateTimestamp
    private Timestamp updatetime;

}
