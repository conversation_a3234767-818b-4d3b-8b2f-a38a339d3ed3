package com.tsintergy.lf.serviceapi.base.recall.dto;

import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel
public class LoadFcQueryDTO implements Serializable {
    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "算法预测值", example = "[32.3,32.3]")
    private List<BigDecimal> algorithmFc;

    @BigdecimalJsonFormat(scale = 2)
    @ApiModelProperty(value = "预测回溯值(实际气象值)", example = "[32.3,32.3]")
    private List<BigDecimal> recallFc;

    @BigdecimalJsonFormat(scale = 0)
    @ApiModelProperty(value = "实际负荷值", example = "[32.3,32.3]")
    private List<BigDecimal> loadHis;

    @BigdecimalJsonFormat(percentConvert = 100, scale = 2)
    @ApiModelProperty(value = "预测准确率")
    private List<BigDecimal> accuracyFc;

    @BigdecimalJsonFormat(percentConvert = 100, scale = 2)
    @ApiModelProperty(value = "回溯准确率")
    private List<BigDecimal> accuracyRecall;


}
