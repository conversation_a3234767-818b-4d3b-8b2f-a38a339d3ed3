package com.tsintergy.lf.serviceapi.base.area.pojo;


import com.tsintergy.aif.algorithm.serviceapi.base.pojo.WeatherFeature;
import com.tsintergy.lf.serviceapi.base.weather.pojo.BaseWeatherFeatureDayFcDO;
import java.math.BigDecimal;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import org.hibernate.annotations.GenericGenerator;

/**
 * 气象特性（预测）
 */
@Entity
@Table(name = "weather_feature_area_day_fc_service")
public class WeatherFeatureAreaDayFcDO extends BaseWeatherFeatureDayFcDO implements WeatherFeature {


    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;


    @Column(name = "area_id")
    private String areaId;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getAreaId() {
        return areaId;
    }

    public void setAreaId(String areaId) {
        this.areaId = areaId;
    }

}
