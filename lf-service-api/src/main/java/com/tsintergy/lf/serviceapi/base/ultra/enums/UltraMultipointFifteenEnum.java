/**
 * Copyright (C), 2015‐2018, 北京清能互联科技有限公司
 * Author: wang<PERSON>
 * Date: 2018/7/31 13:22
 * History:
 * <author> <time> <version> <desc>
 */
package com.tsintergy.lf.serviceapi.base.ultra.enums;

/**
 * Description: 超短期多点枚举类-15分钟间隔；
 *
 * <AUTHOR>
 * @create 2023/2/2
 * @since 1.0.0
 */
public enum UltraMultipointFifteenEnum {

    ULTRA_FORECAST_2(2, "前30分钟"),
    ULTRA_FORECAST_3(3, "前45分钟"),
    ULTRA_FORECAST_4(4, "前1小时"),
    ULTRA_FORECAST_8(8, "前2小时"),
    ULTRA_FORECAST_16(16, "前4小时");

    UltraMultipointFifteenEnum(Integer type, String description) {
        this.type = type;
        this.description = description;
    }

    private Integer type;

    private String description;

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
