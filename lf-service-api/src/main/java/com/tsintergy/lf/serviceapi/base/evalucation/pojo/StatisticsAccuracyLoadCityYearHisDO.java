package com.tsintergy.lf.serviceapi.base.evalucation.pojo;

import com.tsieframework.core.base.dao.BaseVO;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.math.BigDecimal;
import java.sql.Timestamp;

/**  
 * Description: 年准确率 <br> 
 * 
 * <AUTHOR>  
 * @create 2020/9/23  
 * @since 1.0.0  
 */
@Data
@Entity
@Table(name = "statistics_accuracy_load_city_year_his")
public class StatisticsAccuracyLoadCityYearHisDO extends BaseVO {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;

    @Column(name = "city_id")
    private String cityId;

    /**
     * 口径ID
     */
    @Column(name = "caliber_id")
    private String caliberId;

    @Column(name = "year")
    private String year;

    /**
     * 准确率
     */
    @Column(name = "accuracy")
    private BigDecimal accuracy;

    @Column(name = "createtime")
    private Timestamp createtime;

    @Column(name = "updatetime")
    private Timestamp updatetime;
}