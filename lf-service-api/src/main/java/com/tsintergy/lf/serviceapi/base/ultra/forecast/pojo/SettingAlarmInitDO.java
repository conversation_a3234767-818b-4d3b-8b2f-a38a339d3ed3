package com.tsintergy.lf.serviceapi.base.ultra.forecast.pojo;

import com.tsieframework.core.base.dao.BaseDO;
import java.math.BigDecimal;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2023-10-19
 * @since 1.0.0
 */
@Data
@Entity
@Table(name = "setting_alarm_init")
public class SettingAlarmInitDO implements BaseDO {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;

    @Column(name = "city_id")
    private String cityId;

    @Column(name = "five_type")
    private Integer fiveType;

    @Column(name = "five_alarm")
    private BigDecimal fiveAlarm;

    @Column(name = "fifteen_type")
    private Integer fifteenType;

    @Column(name = "fifteen_alarm")
    private BigDecimal fifteenAlarm;
}
