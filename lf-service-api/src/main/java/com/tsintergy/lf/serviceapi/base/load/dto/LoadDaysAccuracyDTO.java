package com.tsintergy.lf.serviceapi.base.load.dto;

import com.tsieframework.core.base.domain.dto.DTO;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2023-03-16
 * @since 1.0.0
 */
@Data
public class LoadDaysAccuracyDTO implements DTO {

    /***
     * @Description 日期
     **/
    private Date date;

    /***
     * @Description 年月
     **/
    private String yearMonth;

    /***
     * @Description 年
     **/
    private String year;

    /***
     * @Description 96点准确率
     **/
    private BigDecimal dayAccuracy;

    /***
     * @Description 日最大负荷准确率
     **/
    private BigDecimal maxLoadAccuracy;

    /***
     * @Description 日最小负荷准确率
     **/
    private BigDecimal minLoadAccuracy;

    /***
     * @Description 平均准确率
     **/
    private BigDecimal avgAccuracy;

}
