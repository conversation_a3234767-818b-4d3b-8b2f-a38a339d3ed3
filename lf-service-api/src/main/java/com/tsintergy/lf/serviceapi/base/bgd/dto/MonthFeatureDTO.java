/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.bgd.dto;

import com.tsieframework.core.base.domain.dto.DTO;
import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import lombok.Data;

import java.math.BigDecimal;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/11/19 13:50
 * @Version: 1.0.0
 */
@Data
public class MonthFeatureDTO implements DTO {

    @BigdecimalJsonFormat(divideConvert = 10,scale = 2)
    private BigDecimal maxLoad;

    private String maxLoadMonth;

    @BigdecimalJsonFormat(divideConvert = 10,scale = 2)
    private BigDecimal minLoad;

    private String minLoadMonth;

    @BigdecimalJsonFormat(divideConvert = 100000,scale = 0)
    private BigDecimal maxEnergy;

    @BigdecimalJsonFormat(divideConvert = 100000,scale = 0)
    private BigDecimal minEnergy;

    @BigdecimalJsonFormat(divideConvert = 10,scale = 0)
    private BigDecimal basicLoad;

    @BigdecimalJsonFormat(divideConvert = 10,scale = 0)
    private BigDecimal weatherLoad;


}
