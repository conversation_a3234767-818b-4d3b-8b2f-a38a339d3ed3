/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.analyze.dto;

import com.tsieframework.core.base.domain.dto.DTO;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/8/22 16:10
 * @Version: 1.0.0
 */
public class LoadStatisticsDTO implements DTO {

    /**
     * 最大负荷
     */
    private BigDecimal maxLoad;

    /**
     * 负荷同比增长
     */
    private BigDecimal loadGrowth;

    /**
     * 创新高次数
     */
    private Integer InnovationsHighCount;


    /**
     * 最后一次创新高日期
     */
    private Date lastDate ;


    public BigDecimal getMaxLoad() {
        return maxLoad;
    }

    public void setMaxLoad(BigDecimal maxLoad) {
        this.maxLoad = maxLoad;
    }

    public BigDecimal getLoadGrowth() {
        return loadGrowth;
    }

    public void setLoadGrowth(BigDecimal loadGrowth) {
        this.loadGrowth = loadGrowth;
    }

    public Integer getInnovationsHighCount() {
        return InnovationsHighCount;
    }

    public void setInnovationsHighCount(Integer innovationsHighCount) {
        InnovationsHighCount = innovationsHighCount;
    }

    public Date getLastDate() {
        return lastDate;
    }

    public void setLastDate(Date lastDate) {
        this.lastDate = lastDate;
    }
}