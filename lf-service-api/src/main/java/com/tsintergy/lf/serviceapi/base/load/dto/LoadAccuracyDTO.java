package com.tsintergy.lf.serviceapi.base.load.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
@ApiModel
public class LoadAccuracyDTO implements Serializable {

    @ApiModelProperty(value = "上报曲线")
    private List<BigDecimal> reportCurve;

    @ApiModelProperty(value = "历史数据")
    private  List<BigDecimal>  hisLoad;

    @ApiModelProperty(value = "偏差数据")
    private  List<BigDecimal> deviation;
    @ApiModelProperty(value = "准确率数据")
    private List<BigDecimal> accuracy;


    /**
     * 日准确率
     */
    @ApiModelProperty(value = "日准确率")
    private  BigDecimal  dayAccuracy;

}