package com.tsintergy.lf.serviceapi.base.bgd.dto;

import com.tsieframework.core.base.domain.dto.DTO;
import com.tsintergy.aif.algorithm.serviceapi.base.dto.DateValuesDTO;
import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import java.math.BigDecimal;
import lombok.Data;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2023-05-05
 * @since 1.0.0
 */
@Data
public class SimilarDayFeatureDTO  extends DateValuesDTO implements DTO {

    /**
     * 日期
     */
    private String date;

    /**
     * 最大负荷
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal maxLoad;

    /**
     * 最小负荷
     */
    @BigdecimalJsonFormat(scale = 0)
    private BigDecimal minLoad;

    /**
     * 最高温度
     */
    private BigDecimal maxTemp;

    /**
     * 最低温度
     */
    private BigDecimal minTemp;

    /**
     * 累计平均温度
     */
    private BigDecimal sumAvgTemp;

    /**
     * 日类型
     */
    private String dateType;

}
