package com.tsintergy.lf.serviceapi.base.bgd.dto;

import com.tsieframework.core.base.domain.dto.DTO;
import java.util.List;
import lombok.Data;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2023-05-05
 * @since 1.0.0
 */
@Data
public class SimilarDayResultDTO implements DTO {

    /**
     * 相似日负荷曲线对比 预测
     */
    private List<DateValuesDTO> loadList;

    /**
     * 气温曲线对比
     */
    private List<DateValuesDTO> weatherList;

    /**
     * 特性统计
     */
    private List<SimilarDayFeatureDTO> featureList;

    /**
     * 相似度列表 历史
     */
    private List<SimilarDayListDTO> similarDayList;

    /**
     * 相似度列表 预测
     */
    private List<SimilarDayListDTO> similarDayFcList;

    /**
     * 相似日负荷曲线对比 预测
     */
    private List<DateValuesDTO> loadListFc;

}
