package com.tsintergy.lf.serviceapi.base.ultra.forecast.api;


import com.tsintergy.lf.serviceapi.base.ultra.forecast.dto.ForecastAlarmAllDTO;
import com.tsintergy.lf.serviceapi.base.ultra.forecast.dto.ForecastAlarmConfigDTO;
import com.tsintergy.lf.serviceapi.base.ultra.forecast.dto.ForecastAlarmCurveDTO;
import java.util.Date;

/**
 * Description: TODO <br>
 *
 * <AUTHOR>
 * @create 2023-10-18
 * @since 1.0.0
 */
public interface ForecastAlarmService {

    ForecastAlarmAllDTO findForecastAlarm(Date date, String cityId, Integer timeSpan, String caliberId,
        String algorithmId) throws Exception;

    ForecastAlarmConfigDTO findForecastAlarmConfig(String cityId) throws Exception;

    void saveForecastAlarmConfig(ForecastAlarmConfigDTO forecastAlarmConfigDTO) throws Exception;

    ForecastAlarmCurveDTO findForecastAlarmCurve(Date date, String cityId, Integer timeSpan, String caliberId,
        String algorithmId) throws Exception;
}
