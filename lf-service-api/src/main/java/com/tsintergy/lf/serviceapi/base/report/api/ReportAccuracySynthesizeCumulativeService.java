package com.tsintergy.lf.serviceapi.base.report.api;

import com.tsintergy.lf.serviceapi.base.report.dto.ReportAccuracySynthesizeDTO;
import com.tsintergy.lf.serviceapi.base.report.dto.ReportAccuracySynthesizeInfoDTO;
import com.tsintergy.lf.serviceapi.base.report.pojo.ReportAccuracySynthesizeCumulativeDO;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public interface ReportAccuracySynthesizeCumulativeService {

     /**
      * 通过时间段和type查找累计综合准确率以及修正后的累计综合准确率
      * @param start
      * @param end
      * @param type
      * @return
      * @throws Exception
      */
    List<ReportAccuracySynthesizeDTO> findByDate(Date start, Date end, Integer type) throws Exception;
    /**
     * 功能描述:<br>查询数据表里所有信息
     * @param date
     * @param type
     * @param cityId
     * @Return: {@link List< ReportAccuracySynthesizeInfoDTO>}
     * @Version: 1.0.0
     * @Author:<EMAIL>
     * @Date: 2021/11/16 10:23
     */
    List<ReportAccuracySynthesizeInfoDTO> findSynthesizeCumulative(java.util.Date date, Integer type, String cityId) throws Exception;
    ReportAccuracySynthesizeCumulativeDO findByDateAndCityId(Date date, String cityId, Integer type) throws Exception;

    void updateAccuracyCorrect(String cityId, BigDecimal decimal, java.util.Date date,Integer type) throws Exception;

    void update(ReportAccuracySynthesizeCumulativeDO reportAccuracySynthesizeCumulativeDO)
        throws Exception;
     void insert(ReportAccuracySynthesizeCumulativeDO reportAccuracySynthesizeCumulativeDO)
         throws Exception;

}