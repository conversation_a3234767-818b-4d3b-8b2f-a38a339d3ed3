package com.tsintergy.lf.serviceapi.base.ultra.forecast.dto;

import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2023-10-18
 * @since 1.0.0
 */
@Data
public class ForecastAlarmCurveDTO implements Serializable {

    @BigdecimalJsonFormat(scale = 0)
    private List<BigDecimal> fcLoad;

    @BigdecimalJsonFormat(scale = 0)
    private List<BigDecimal> hisLoad;

    private List<BigDecimal> fcTem;

    private List<BigDecimal> hisTem;

    private List<BigDecimal> fcRain;

    private List<BigDecimal> hisRain;

    private List<BigDecimal> fcWind;

    private List<BigDecimal> hisWind;

    private List<BigDecimal> fcHum;

    private List<BigDecimal> hisHum;
}
