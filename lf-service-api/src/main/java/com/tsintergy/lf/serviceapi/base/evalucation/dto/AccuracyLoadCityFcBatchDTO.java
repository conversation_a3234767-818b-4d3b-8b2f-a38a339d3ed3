package com.tsintergy.lf.serviceapi.base.evalucation.dto;


import com.tsintergy.lf.serviceapi.base.base.pojo.BaseLoadFcCityDO;
import java.sql.Date;
import java.sql.Timestamp;
import lombok.Data;

/**
 * 预测准确率
 */
@Data
public class AccuracyLoadCityFcBatchDTO extends BaseLoadFcCityDO {


    private String id;

    /**
     * 日期
     */
    private Date date;

    /**
     * 城市ID
     */
    private String cityId;

    /**
     * 算法ID
     */
    private String algorithmId;

    /**
     * 口径ID
     */
    private String caliberId;

    /**
     * 批次id
     */
    private Integer batchId;

    /**
     * 是否上报
     */
    private Boolean report;

    /**
     * 创建时间
     */
    private Timestamp createtime;

    /**
     * 更新时间
     */
    private Timestamp updatetime;


}
