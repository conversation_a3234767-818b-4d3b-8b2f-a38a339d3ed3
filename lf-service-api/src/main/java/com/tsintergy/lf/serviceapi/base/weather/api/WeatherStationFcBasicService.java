/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.weather.api;

import com.tsintergy.lf.serviceapi.base.weather.pojo.WeatherStationFcBasicDO;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/5/20 15:44
 * @Version: 1.0.0
 */
public interface WeatherStationFcBasicService {

    List<WeatherStationFcBasicDO> getWeatherStationHisBasicDO(String cityId,String stationId, Date startDate,Date endDate,Integer type);


    void doSaveOrUpdate(WeatherStationFcBasicDO weatherStationFcBasicDO);

    void doSaveOrUpdateList(Map<String,WeatherStationFcBasicDO> result) throws Exception;
}
