package com.tsintergy.lf.serviceapi.base.bgd.enums;

/**
 * Description:  <br>
 * 行业类型
 * @Author: <EMAIL>
 * @Date: 2022/11/16 22:01
 * @Version: 1.0.0
 */
public enum IndustrialTypeEnum {

    RESIDENTS("0","城乡居民",4),
    FIRST("1","第一产业",1),
    SECOND("2","第二产业",2),
    THIRD("3","第三产业",3),;

    /**
     * 产业类型
     */
    private String type;

    /**
     * 产业名称
     */
    private String  IndustrialName;

    /**
     * 排序用
     */
    private Integer sort;

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    IndustrialTypeEnum(String type, String industrialName, Integer sort) {
        this.type = type;
        IndustrialName = industrialName;
        this.sort = sort;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getIndustrialName() {
        return IndustrialName;
    }

    public void setIndustrialName(String industrialName) {
        IndustrialName = industrialName;
    }
}
