/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.trade.pojo;

import com.tsieframework.core.base.dao.BaseDO;
import com.tsintergy.aif.algorithm.serviceapi.base.pojo.Load;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.Column;
import javax.persistence.MappedSuperclass;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/9/2 19:18
 * @Version: 1.0.0
 */
@MappedSuperclass
public class BaseTradeDO implements BaseDO, Load {



    @Column(name = "trade_code")
    private String tradeCode;

    @Column(name = "h00")
    private BigDecimal h00;

    @Column(name = "h01")
    private BigDecimal h01;

    @Column(name = "h02")
    private BigDecimal h02;

    @Column(name = "h03")
    private BigDecimal h03;
    @Column(name = "h04")
    private BigDecimal h04;
    @Column(name = "h05")
    private BigDecimal h05;
    @Column(name = "h06")
    private BigDecimal h06;

    @Column(name = "h07")
    private BigDecimal h07;

    @Column(name = "h08")
    private BigDecimal h08;

    @Column(name = "h09")
    private BigDecimal h09;

    @Column(name = "h10")
    private BigDecimal h10;
    @Column(name = "h11")
    private BigDecimal h11;
    @Column(name = "h12")
    private BigDecimal h12;
    @Column(name = "h13")
    private BigDecimal h13;
    @Column(name = "h14")
    private BigDecimal h14;
    @Column(name = "h15")
    private BigDecimal h15;
    @Column(name = "h16")
    private BigDecimal h16;
    @Column(name = "h17")
    private BigDecimal h17;
    @Column(name = "h18")
    private BigDecimal h18;
    @Column(name = "h19")
    private BigDecimal h19;
    @Column(name = "h20")
    private BigDecimal h20;
    @Column(name = "h21")
    private BigDecimal h21;
    @Column(name = "h22")
    private BigDecimal h22;
    @Column(name = "h23")
    private BigDecimal h23;


    public String getTradeCode() {
        return tradeCode;
    }

    public void setTradeCode(String tradeCode) {
        this.tradeCode = tradeCode;
    }

    public BigDecimal getH00() {
        return h00;
    }

    public void setH00(BigDecimal h00) {
        this.h00 = h00;
    }

    public BigDecimal getH01() {
        return h01;
    }

    public void setH01(BigDecimal h01) {
        this.h01 = h01;
    }

    public BigDecimal getH02() {
        return h02;
    }

    public void setH02(BigDecimal h02) {
        this.h02 = h02;
    }

    public BigDecimal getH03() {
        return h03;
    }

    public void setH03(BigDecimal h03) {
        this.h03 = h03;
    }

    public BigDecimal getH04() {
        return h04;
    }

    public void setH04(BigDecimal h04) {
        this.h04 = h04;
    }

    public BigDecimal getH05() {
        return h05;
    }

    public void setH05(BigDecimal h05) {
        this.h05 = h05;
    }

    public BigDecimal getH06() {
        return h06;
    }

    public void setH06(BigDecimal h06) {
        this.h06 = h06;
    }

    public BigDecimal getH07() {
        return h07;
    }

    public void setH07(BigDecimal h07) {
        this.h07 = h07;
    }

    public BigDecimal getH08() {
        return h08;
    }

    public void setH08(BigDecimal h08) {
        this.h08 = h08;
    }

    public BigDecimal getH09() {
        return h09;
    }

    public void setH09(BigDecimal h09) {
        this.h09 = h09;
    }

    public BigDecimal getH10() {
        return h10;
    }

    public void setH10(BigDecimal h10) {
        this.h10 = h10;
    }

    public BigDecimal getH11() {
        return h11;
    }

    public void setH11(BigDecimal h11) {
        this.h11 = h11;
    }

    public BigDecimal getH12() {
        return h12;
    }

    public void setH12(BigDecimal h12) {
        this.h12 = h12;
    }

    public BigDecimal getH13() {
        return h13;
    }

    public void setH13(BigDecimal h13) {
        this.h13 = h13;
    }

    public BigDecimal getH14() {
        return h14;
    }

    public void setH14(BigDecimal h14) {
        this.h14 = h14;
    }

    public BigDecimal getH15() {
        return h15;
    }

    public void setH15(BigDecimal h15) {
        this.h15 = h15;
    }

    public BigDecimal getH16() {
        return h16;
    }

    public void setH16(BigDecimal h16) {
        this.h16 = h16;
    }

    public BigDecimal getH17() {
        return h17;
    }

    public void setH17(BigDecimal h17) {
        this.h17 = h17;
    }

    public BigDecimal getH18() {
        return h18;
    }

    public void setH18(BigDecimal h18) {
        this.h18 = h18;
    }

    public BigDecimal getH19() {
        return h19;
    }

    public void setH19(BigDecimal h19) {
        this.h19 = h19;
    }

    public BigDecimal getH20() {
        return h20;
    }

    public void setH20(BigDecimal h20) {
        this.h20 = h20;
    }

    public BigDecimal getH21() {
        return h21;
    }

    public void setH21(BigDecimal h21) {
        this.h21 = h21;
    }

    public BigDecimal getH22() {
        return h22;
    }

    public void setH22(BigDecimal h22) {
        this.h22 = h22;
    }

    public BigDecimal getH23() {
        return h23;
    }

    public void setH23(BigDecimal h23) {
        this.h23 = h23;
    }

    @Override
    public Date getDate() {
        return null;
    }

    @Override
    public List<BigDecimal> getloadList() {
        List<BigDecimal> loadList = new ArrayList<>();
        Class<? extends BaseTradeDO> cla = this.getClass();
        Class<?> superclass = cla.getSuperclass();
        Field[] declaredFields = superclass.getDeclaredFields();
        for (Field field : declaredFields) {
            if (!field.getName().contains("h")){
                continue;
            }
            try {
                PropertyDescriptor descriptor = new PropertyDescriptor(field.getName(), cla);
                Method m = descriptor.getReadMethod();

                if (null == m) {
                    throw new RuntimeException(field.getName() + "不存在set方法");
                }
                BigDecimal column = (BigDecimal) m.invoke(this);
                loadList.add(column);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return loadList;
    }

}