package com.tsintergy.lf.serviceapi.base.bgd.dto;

import com.tsieframework.core.base.domain.dto.DTO;
import com.tsintergy.lf.serviceapi.base.common.jsonserialize.BigdecimalJsonFormat;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * Description: TODO <br>
 *
 * <AUTHOR>
 * @create 2023-02-16
 * @since 1.0.0
 */
@Data
public class IndustryValueDTO implements DTO {

    private Date date;

    @BigdecimalJsonFormat(divideConvert = 1000, scale = 0)
    private BigDecimal value;

}
