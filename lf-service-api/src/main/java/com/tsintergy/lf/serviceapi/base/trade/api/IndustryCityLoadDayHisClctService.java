package com.tsintergy.lf.serviceapi.base.trade.api;

import com.tsintergy.lf.serviceapi.base.trade.pojo.IndustryCityLoadDayHisClctDO;

import java.util.Date;
import java.util.List;

public interface IndustryCityLoadDayHisClctService {

    List<IndustryCityLoadDayHisClctDO> findIndustryCityLoadDayHisClctDOS(String cityId, List<String> tradeCodes, Date startDate, Date endDate);


    List<IndustryCityLoadDayHisClctDO> findIndustryCityLoadDayHisClctDOS(String cityId, List<String> tradeCodes, List<Date> dates);

}
