/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.bgd.dto;

import com.tsieframework.core.base.domain.dto.DTO;
import lombok.Data;

/**
 * Description:  <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/11/19 17:31
 * @Version: 1.0.0
 */
@Data
public class MonthModelForecastDTO implements DTO {

    private String type;

    FeatureDTO xgb;

    FeatureDTO svm;

    FeatureDTO lgb;

    FeatureDTO random;

    FeatureDTO custom;

    FeatureDTO report;

}