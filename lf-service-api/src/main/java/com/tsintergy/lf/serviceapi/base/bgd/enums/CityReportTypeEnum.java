package com.tsintergy.lf.serviceapi.base.bgd.enums;

/**
 * Description:
 *
 * <AUTHOR>
 * @create 2023-07-01
 * @since 1.0.0
 */
public enum CityReportTypeEnum {

    CITY_LOAD("10", "地区负荷", 1),
    CITY_ELECTRICITY("3", "地区发电", 2),
    CITY_DIAODU("2", "地区调度", 3),
    CITY_WANGGONG("1", "地区网供", 4),
    CITY_FENGDIAN("9" ,"风电", 5),
    CITY_GUANGFU("8", "光伏", 6),
    CITY_SHUIDIAN("6", "水电", 7),
    CITY_HUODIAN("7", "火电", 8),;

    CityReportTypeEnum(String type, String name, Integer sort) {
        this.type = type;
        this.name = name;
        this.sort = sort;
    }

    /**
     * 口径类型
     */
    private String type;

    /**
     * 口径名称
     */
    private String  name;

    /**
     * 排序用
     */
    private Integer sort;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }
}
