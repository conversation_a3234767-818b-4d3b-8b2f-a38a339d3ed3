/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.recall.pojo;

import com.tsintergy.lf.serviceapi.base.base.pojo.BaseLoadFcCityDO;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

/**
 * Description: 回溯时刻点准确率 <br>
 *
 * @Author: <EMAIL>
 * @Date: 2022/8/1 12:00
 * @Version: 1.0.0
 */

@Data
@Entity
@Table(name = "ACCURACY_LOAD_CITY_FC_SERVICE_RECALL")
public class AccuracyLoadCityFcServiceRecallDO extends BaseLoadFcCityDO {



    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;
}