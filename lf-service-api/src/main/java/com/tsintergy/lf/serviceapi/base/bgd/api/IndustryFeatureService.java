package com.tsintergy.lf.serviceapi.base.bgd.api;

import com.tsintergy.lf.serviceapi.base.bgd.dto.IndustrialImportTypeDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.IndustryAllTypeDateLoadDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.IndustryAllTypeLoadDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.IndustryEnergyHisDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.IndustryEnergyValueDTO;
import com.tsintergy.lf.serviceapi.base.bgd.dto.IndustryLoadHisDTO;
import java.util.Date;
import java.util.List;

/**
 * Description: TODO <br>
 *
 * <AUTHOR>
 * @create 2023-02-16
 * @since 1.0.0
 */
public interface IndustryFeatureService {

    /**
     * 行业电量
     * @param cityId
     * @param startDate
     * @param endDate
     * @return
     */
    List<IndustryEnergyValueDTO> queryTotalEnergy(String cityId, Date startDate, Date endDate);

    /**
     * 负荷曲线
     * @param cityId
     * @param startDate
     * @param endDate
     * @return
     */
    List<IndustryLoadHisDTO> queryLoadIndustry(String cityId, Date startDate, Date endDate);

    /**
     * 电量曲线
     * @param cityId
     * @param startDate
     * @param endDate
     * @return
     */
    List<IndustryLoadHisDTO> queryEnergyIndustry(String cityId, Date startDate, Date endDate);

    /**
     * 行业负荷
     * @param cityId
     * @param startDate
     * @param endDate
     * @return
     */
    List<IndustryAllTypeLoadDTO> queryAllLoadIndustry(String cityId, Date startDate, Date endDate);

    /**
     * 行业电量累计
     * @param cityId
     * @param startDate
     * @param endDate
     * @return
     */
    List<IndustryAllTypeLoadDTO> queryAllEnergyIndustry(String cityId, Date startDate, Date endDate);

    /**
     * 行业负荷详情
     * @param cityId
     * @param startDate
     * @param endDate
     * @param industryType
     * @return
     */
    List<IndustryAllTypeLoadDTO> queryAllLoadIndustryDetails(String cityId, Date startDate, Date endDate, String industryType);

    /**
     * 重点行业负荷详情
     * @param cityId
     * @param startDate
     * @param endDate
     * @param industryType
     * @return
     */
    List<IndustrialImportTypeDTO> queryAllLoadDateIndustryDetails(String cityId, Date startDate, Date endDate, String industryType);

}
