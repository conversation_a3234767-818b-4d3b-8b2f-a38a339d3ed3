package com.tsintergy.lf.serviceapi.base.area.pojo;


import com.tsieframework.core.base.vo.util.BasePeriodUtils;
import com.tsintergy.aif.algorithm.serviceapi.base.pojo.Load;
import com.tsintergy.lf.core.constants.Constants;
import com.tsintergy.lf.serviceapi.base.base.pojo.Base96DO;
import com.tsintergy.lf.serviceapi.base.base.pojo.BaseLoadCityDO;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;
import java.util.List;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.UpdateTimestamp;

/**
 * 地区历史负荷
 */
@Entity
@Table(name = "load_area_his_basic")
public class LoadAreaHisDO extends Base96DO implements Load {

    @Id
    @Column(name = "id")
    @GenericGenerator(name = DEFAULT_GENERATOR, strategy = DEFAULT_STRATEGY)
    @GeneratedValue(generator = DEFAULT_GENERATOR)
    private String id;


    @Column(name = "area_id")
    private String areaId;

    /**
     * 日期
     */
    @Column(name = "date")
    private Date date;

    /**
     * 口径ID
     */
    @Column(name = "caliber_id")
    private String caliberId;

    /**
     * 创建时间
     */
    @Column(name = "createtime")
    @CreationTimestamp
    private Timestamp createtime;

    /**
     * 更新时间
     */
    @Column(name = "updatetime")
    @UpdateTimestamp
    private Timestamp updatetime;


     
    public List<BigDecimal> getloadList() {
        return BasePeriodUtils.toList(this, Constants.LOAD_CURVE_POINT_NUM,Constants.LOAD_CURVE_START_WITH_ZERO);
    }


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getAreaId() {
        return areaId;
    }

    public void setAreaId(String areaId) {
        this.areaId = areaId;
    }
    

     
    public Date getDate() {
        return date;
    }

     
    public void setDate(Date date) {
        this.date = date;
    }

     
    public String getCaliberId() {
        return caliberId;
    }

     
    public void setCaliberId(String caliberId) {
        this.caliberId = caliberId;
    }

     
    public Timestamp getCreatetime() {
        return createtime;
    }

     
    public void setCreatetime(Timestamp createtime) {
        this.createtime = createtime;
    }

     
    public Timestamp getUpdatetime() {
        return updatetime;
    }

     
    public void setUpdatetime(Timestamp updatetime) {
        this.updatetime = updatetime;
    }
}
