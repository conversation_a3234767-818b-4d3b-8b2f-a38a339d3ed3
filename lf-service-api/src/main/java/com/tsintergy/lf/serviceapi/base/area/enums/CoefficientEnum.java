/**
 * Copyright(C),2015‐2022,北京清能互联科技有限公司
 */
package com.tsintergy.lf.serviceapi.base.area.enums;

/**
 * Description:  <br>
 *
 * @Author: liu<PERSON><EMAIL>
 * @Date: 2022/8/5 17:23
 * @Version: 1.0.0
 */
public enum  CoefficientEnum {
    COEFFICIENT("1", "网损系数"),
    COEFFICIENT_NUM("2", "自定义网损系数");
    private String key;
    private String value;

    CoefficientEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}