package com.tsintergy.lf.serviceapi.base.common.enumeration;

public enum WarnTypeEnum {
    MAX_LOAD(1,"日最大负荷预警阈值"),
    ENERGY(2,"日电量预警阈值"),
    MAX_TIME(3,"最大负荷发生时间预警阈值");

    private Integer type;

    private String typeName;

    WarnTypeEnum(Integer type, String typeName) {
        this.type = type;
        this.typeName = typeName;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }
}

