package com.tsintergy.lf.core;

import com.tsieframework.core.base.annotation.BelongTo;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import org.springframework.core.annotation.AliasFor;

/**
 * <p>
 *     添加该注解后，对应bean的名称会自动添加模块名前缀，避免不同应用模块整合时名称冲突的问题 <br/>
 *     具体参考：{@link com.tsieframework.core.base.annotation.BelongToAnnotationBeanNameGenerator}
 * </p>
 *
 * <AUTHOR>
 * @date 2020-02-18 11:00:43
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface BelongToLf {

    /**
     * 模块名
     * @return
     */
    @AliasFor(annotation = BelongTo.class)
    String value() default LfConfigConstants.PROJECT_NAME;

}