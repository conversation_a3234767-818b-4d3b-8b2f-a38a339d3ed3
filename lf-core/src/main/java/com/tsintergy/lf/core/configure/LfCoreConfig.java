package com.tsintergy.lf.core.configure;

import com.tsintergy.lf.core.logging.CustomLogAspectj;
import com.tsieframework.core.base.logging.LogAspectj;
import com.tsintergy.lf.core.properties.LfProperties;
import com.tsintergy.lf.core.util.UltraSystemUtils;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * <p>
 *     Core包配置
 * </p>
 *
 * <AUTHOR>
 * @date 2020-02-18 11:00:43
 */
@EnableConfigurationProperties(LfProperties.class)
@Configuration
public class LfCoreConfig {

    @Primary
    @Bean("logAspectJ")
    LogAspectj logAspectJ(){
        return new CustomLogAspectj();
    }


    @Bean
    public UltraSystemUtils ultraSystemUtils() {
        return new UltraSystemUtils();
    }

}
