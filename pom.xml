<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>tsie-boot-parent</artifactId>
        <groupId>com.tsieframework.boot</groupId>
        <version>3.1.0</version>
    </parent>

    <groupId>com.tsintergy.lf</groupId>
    <artifactId>lf</artifactId>
    <version>${lf.version}</version>
    <packaging>pom</packaging>

    <name>lf_backend</name>

    <properties>
        <lf.version>henan-1.0.0-SNAPSHOT</lf.version>
    </properties>

    <modules>
        <module>lf-parent</module>
        <module>lf-config</module>
        <module>lf-core</module>
        <module>lf-i18n</module>
        <module>lf-service-api</module>
        <module>lf-service-impl</module>
        <module>lf-starters</module>
        <module>lf-web</module>
        <module>lf-jobhandler</module>
    </modules>
</project>
